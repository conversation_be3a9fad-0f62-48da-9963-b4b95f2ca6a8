#!/usr/bin/env python3
"""
Script to convert JSON results from Paychex API into table format.
Each row represents a worker, with CustomerID as the first column.
"""

import json
import pandas as pd
import os
from pathlib import Path
from typing import List, Dict, Any


def load_json_results(results_dir: str = "results") -> List[Dict[str, Any]]:
    """Load all JSON result files from the results directory."""
    results = []
    results_path = Path(results_dir)
    
    if not results_path.exists():
        print(f"Results directory '{results_dir}' not found")
        return results
    
    for json_file in results_path.glob("company_*_workers.json"):
        try:
            with open(json_file, 'r') as f:
                data = json.load(f)
                if data.get("success") and "data" in data:
                    results.append(data)
                    print(f"Loaded {json_file.name}")
        except Exception as e:
            print(f"Error loading {json_file.name}: {e}")
    
    return results


def flatten_worker_data(worker: Dict[str, Any], customer_id: str) -> Dict[str, Any]:
    """Flatten worker data into a single row for the table."""
    flattened = {"CustomerID": customer_id}
    
    # Basic worker info
    flattened.update({
        "WorkerID": worker.get("workerId", ""),
        "EmployeeID": worker.get("employeeId", ""),
        "WorkerType": worker.get("workerType", ""),
        "ExemptionType": worker.get("exemptionType", ""),
        "WorkState": worker.get("workState", ""),
        "BirthDate": worker.get("birthDate", ""),
        "Sex": worker.get("sex", ""),
        "HireDate": worker.get("hireDate", "")
    })
    
    # Name information
    name = worker.get("name", {})
    flattened.update({
        "FamilyName": name.get("familyName", ""),
        "GivenName": name.get("givenName", "")
    })
    
    # Legal ID information
    legal_id = worker.get("legalId", {})
    flattened.update({
        "LegalIDType": legal_id.get("legalIdType", ""),
        "LegalIDValue": legal_id.get("legalIdValue", "")
    })
    
    # Organization information
    organization = worker.get("organization", {})
    flattened.update({
        "OrganizationID": organization.get("organizationId", ""),
        "OrganizationName": organization.get("name", ""),
        "OrganizationNumber": organization.get("number", "")
    })
    
    # Current status information
    current_status = worker.get("currentStatus", {})
    flattened.update({
        "WorkerStatusID": current_status.get("workerStatusId", ""),
        "StatusType": current_status.get("statusType", ""),
        "StatusReason": current_status.get("statusReason", ""),
        "EffectiveDate": current_status.get("effectiveDate", "")
    })
    
    # Additional fields
    flattened.update({
        "ID": worker.get("id", ""),
        "CompanyID": worker.get("companyId", ""),
        "Type": worker.get("type", "")
    })
    
    return flattened


def convert_to_table(results: List[Dict[str, Any]]) -> pd.DataFrame:
    """Convert JSON results to a pandas DataFrame (table format)."""
    all_workers = []
    
    for result in results:
        customer_id = result.get("company_id", "")
        workers = result.get("data", {}).get("content", [])
        
        for worker in workers:
            flattened_worker = flatten_worker_data(worker, customer_id)
            all_workers.append(flattened_worker)
    
    df = pd.DataFrame(all_workers)
    return df


def save_to_excel(df: pd.DataFrame, filename: str = "workers_table.xlsx"):
    """Save DataFrame to Excel file."""
    try:
        df.to_excel(filename, index=False)
        print(f"Table saved to {filename}")
        print(f"Total workers: {len(df)}")
        print(f"Total companies: {df['CustomerID'].nunique()}")
    except Exception as e:
        print(f"Error saving to Excel: {e}")


def save_to_csv(df: pd.DataFrame, filename: str = "workers_table.csv"):
    """Save DataFrame to CSV file."""
    try:
        df.to_csv(filename, index=False)
        print(f"Table saved to {filename}")
        print(f"Total workers: {len(df)}")
        print(f"Total companies: {df['CustomerID'].nunique()}")
    except Exception as e:
        print(f"Error saving to CSV: {e}")


def main():
    print("Converting JSON results to table format...")
    
    # Load JSON results
    results = load_json_results()
    
    if not results:
        print("No valid results found to process.")
        return
    
    print(f"Processing {len(results)} result files...")
    
    # Convert to table
    df = convert_to_table(results)
    
    if df.empty:
        print("No worker data found to convert.")
        return
    
    # Save to both Excel and CSV
    save_to_excel(df)
    save_to_csv(df)
    
    print("\nColumn names in the table:")
    for i, col in enumerate(df.columns, 1):
        print(f"{i:2d}. {col}")


if __name__ == "__main__":
    main()