# Authentication Configuration
# Choose one method:

# Method 1: Manual Bearer <PERSON> (if you already have one)
BEARER_TOKEN = "your-bearer-token-here"  # Set this if you have a token
USE_MANUAL_TOKEN = True  # Set to True to use BEARER_TOKEN above

# Method 2: Interactive Login (device code flow)
CLIENT_ID = "your-client-id"  # Usually a well-known client ID for the organization
TENANT_ID = "your-tenant-id-or-domain"  # e.g., "paychex.onmicrosoft.com" or tenant ID
SCOPE = "https://graph.microsoft.com/.default"  # Or the specific API scope

# API Configuration
BASE_URL = "https://ca-payroll-ai-mock-sb-001-secure.nicebeach-8a7a52ab.eastus.azurecontainerapps.io"
EXCEL_FILE = "data/250630_golden_set_customerid.xlsx"
COLUMN_NAME = "CustomerID"
