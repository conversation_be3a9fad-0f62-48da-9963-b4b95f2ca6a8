#!/usr/bin/env python3
"""
<PERSON>rip<PERSON> to read company IDs from Excel file and call Paychex API payperiods endpoint for each company.
Results are saved to individual files. Uses Microsoft AD authentication.
"""

import pandas as pd
import requests
import json
import os
from pathlib import Path
import time
from dotenv import load_dotenv
from typing import Optional


def read_ids_from_excel(file_path: str, column_name: str = "companyId") -> list:
    """Read company IDs from Excel file."""
    try:
        df = pd.read_excel(file_path)
        if column_name not in df.columns:
            raise ValueError(f"Column '{column_name}' not found in Excel file")
        return df[column_name].tolist()
    except Exception as e:
        print(f"Error reading Excel file: {e}")
        return []


def call_payperiods_api_endpoint(base_url: str, company_id: str, headers: dict = None) -> dict:
    """Call Paychex API payperiods endpoint for given company ID."""
    try:
        url = f"{base_url}/ca/companies/{company_id}/payperiods"
        
        response = requests.get(url, headers=headers, timeout=30)
        response.raise_for_status()
        
        return {
            "success": True,
            "company_id": company_id,
            "data": response.json() if response.content else {},
            "status_code": response.status_code
        }
    except requests.exceptions.RequestException as e:
        return {
            "success": False,
            "company_id": company_id,
            "error": str(e),
            "status_code": getattr(e.response, 'status_code', None) if hasattr(e, 'response') else None
        }


def save_result_to_file(result: dict, company_id: str, output_dir: str = "payperiods_results"):
    """Save API result to JSON file."""
    Path(output_dir).mkdir(exist_ok=True)
    
    filename = f"{output_dir}/company_{company_id}_payperiods.json"
    try:
        with open(filename, 'w') as f:
            json.dump(result, f, indent=2)
        print(f"Saved payperiods result for company {company_id} to {filename}")
    except Exception as e:
        print(f"Error saving payperiods result for company {company_id}: {e}")


def main():
    # Load environment variables
    load_dotenv()
    
    # Get configuration from environment
    ACCESS_TOKEN = os.getenv("ACCESS_TOKEN")
    EXCEL_FILE = os.getenv("EXCEL_FILE", "data/CompanyIDs for API data pull.xlsx")
    COLUMN_NAME = os.getenv("COLUMN_NAME", "CustomerID")
    BASE_URL = "https://ca-payroll-ai-mock-sb-001-secure.nicebeach-8a7a52ab.eastus.azurecontainerapps.io"
    
    print("Starting Paychex API payperiods data extraction...")
    
    # Check access token
    if not ACCESS_TOKEN or ACCESS_TOKEN == "your-access-token-here":
        print("Please set ACCESS_TOKEN in .env file")
        return
    
    print("Using access token from .env file...")
    access_token = ACCESS_TOKEN
    
    # Set up headers with Bearer token
    HEADERS = {
        "Authorization": f"Bearer {access_token}",
        "Content-Type": "application/json"
    }
    
    # Read company IDs from Excel file
    print(f"Reading company IDs from {EXCEL_FILE}...")
    company_ids = read_ids_from_excel(EXCEL_FILE, COLUMN_NAME)
    
    if not company_ids:
        print("No company IDs found or error reading file.")
        return
    
    print(f"Found {len(company_ids)} company IDs to process")
    
    # Process each company ID
    for i, company_id in enumerate(company_ids, 1):
        print(f"Processing payperiods for company {company_id} ({i}/{len(company_ids)})...")
        
        # Call API endpoint
        result = call_payperiods_api_endpoint(BASE_URL, str(company_id), HEADERS)
        
        # Save result to file
        save_result_to_file(result, str(company_id))
        
        # Add small delay between requests to be respectful to the API
        time.sleep(0.5)
    
    print("Payperiods processing complete!")


if __name__ == "__main__":
    main()