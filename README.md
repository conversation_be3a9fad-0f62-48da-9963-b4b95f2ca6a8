# Paychex API Worker Data Extractor

This script reads company IDs from an Excel file and calls the Paychex CA API endpoint to get worker data for each company. Uses Microsoft Azure AD authentication.

## Setup

1. Install dependencies using uv:
   ```bash
   uv sync
   ```

2. Configure Azure AD credentials in `config.py`:
   ```python
   CLIENT_ID = "your-azure-ad-client-id"
   CLIENT_SECRET = "your-azure-ad-client-secret" 
   TENANT_ID = "your-azure-ad-tenant-id"
   SCOPE = "api://your-api-scope/.default"
   ```

3. Add your company IDs to `data/company_ids.xlsx`

## Azure AD Setup

You'll need:
- **Client ID**: From your Azure AD App Registration
- **Client Secret**: Generated in Azure AD App Registration  
- **Tenant ID**: Your Azure AD tenant identifier
- **Scope**: The API scope (usually `api://your-api-id/.default`)

## Usage

```bash
uv run api_caller.py
```

## API Endpoint

Calls: `/ca/companies/{companyId}/workers`
Base URL: `https://ca-payroll-ai-mock-sb-001-secure.nicebeach-8a7a52ab.eastus.azurecontainerapps.io`

## File Structure

- `data/company_ids.xlsx`: Excel file containing company IDs to process
- `config.py`: Azure AD and API configuration  
- `api_caller.py`: Main script
- `results/`: Directory where API results are saved (created automatically)

## Excel File Format

The Excel file should have a column with company IDs:

| companyId |
|-----------|
| COMP001   |
| COMP002   |
| COMP003   |

## Output

Results are saved as JSON files in the `results/` directory:
- `company_COMP001_workers.json`
- `company_COMP002_workers.json`
- etc.

Each file contains the worker data for that company or error information if the call failed.
