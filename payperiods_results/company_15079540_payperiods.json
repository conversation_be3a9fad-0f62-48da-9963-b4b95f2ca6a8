{"success": true, "company_id": "15079540", "data": {"metadata": {"contentItemCount": 42}, "content": [{"payPeriodId": "1060038969826025", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2024-12-30T00:00:00Z", "endDate": "2025-01-12T00:00:00Z", "submitByDate": "2025-01-15T00:00:00Z", "checkDate": "2025-01-17T00:00:00Z", "checkCount": 5, "id": "7c99d429-adc4-4b2b-a6fb-4c1e10f77a46", "companyId": "15079540", "type": "payperiod", "_rid": "NmJkAKiCbEdyEwQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdyEwQAAAAAAA==/", "_etag": "\"a700fcd5-0000-0100-0000-687038a90000\"", "_attachments": "attachments/", "_ts": 1752185001}, {"payPeriodId": "1060039525765308", "status": "COMPLETED", "description": "Additional bonus payroll", "startDate": "2024-12-30T00:00:00Z", "endDate": "2025-01-12T00:00:00Z", "submitByDate": "2025-01-16T00:00:00Z", "checkDate": "2025-01-17T00:00:00Z", "checkCount": 1, "id": "d5d71bc2-40fb-41da-87ab-df304510ae39", "companyId": "15079540", "type": "payperiod", "_rid": "NmJkAKiCbEdzEwQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdzEwQAAAAAAA==/", "_etag": "\"a70001d6-0000-0100-0000-687038a90000\"", "_attachments": "attachments/", "_ts": 1752185001}, {"payPeriodId": "1060039042261835", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-01-13T00:00:00Z", "endDate": "2025-01-26T00:00:00Z", "submitByDate": "2025-01-29T00:00:00Z", "checkDate": "2025-01-31T00:00:00Z", "checkCount": 5, "id": "290516bf-73b1-4993-abfd-594229aa90b1", "companyId": "15079540", "type": "payperiod", "_rid": "NmJkAKiCbEd0EwQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEd0EwQAAAAAAA==/", "_etag": "\"a70004d6-0000-0100-0000-687038a90000\"", "_attachments": "attachments/", "_ts": 1752185001}, {"payPeriodId": "1060039122401920", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-01-27T00:00:00Z", "endDate": "2025-02-09T00:00:00Z", "submitByDate": "2025-02-12T00:00:00Z", "checkDate": "2025-02-14T00:00:00Z", "checkCount": 5, "id": "bc6fc9f7-2bc9-4f2b-bda3-a9f49fcbfa19", "companyId": "15079540", "type": "payperiod", "_rid": "NmJkAKiCbEd1EwQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEd1EwQAAAAAAA==/", "_etag": "\"a70007d6-0000-0100-0000-687038a90000\"", "_attachments": "attachments/", "_ts": 1752185001}, {"payPeriodId": "1060039192000208", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-02-10T00:00:00Z", "endDate": "2025-02-23T00:00:00Z", "submitByDate": "2025-02-26T00:00:00Z", "checkDate": "2025-02-28T00:00:00Z", "checkCount": 6, "id": "3721384f-4378-40a5-b92d-37c90d37fe20", "companyId": "15079540", "type": "payperiod", "_rid": "NmJkAKiCbEd2EwQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEd2EwQAAAAAAA==/", "_etag": "\"a70009d6-0000-0100-0000-687038a90000\"", "_attachments": "attachments/", "_ts": 1752185001}, {"payPeriodId": "1060039264427457", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-02-24T00:00:00Z", "endDate": "2025-03-09T00:00:00Z", "submitByDate": "2025-03-12T00:00:00Z", "checkDate": "2025-03-14T00:00:00Z", "checkCount": 4, "id": "b599420a-d7b5-4d8b-a124-50fc01f2afe9", "companyId": "15079540", "type": "payperiod", "_rid": "NmJkAKiCbEd3EwQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEd3EwQAAAAAAA==/", "_etag": "\"a7000bd6-0000-0100-0000-687038a90000\"", "_attachments": "attachments/", "_ts": 1752185001}, {"payPeriodId": "1060039339030294", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-03-10T00:00:00Z", "endDate": "2025-03-23T00:00:00Z", "submitByDate": "2025-03-26T00:00:00Z", "checkDate": "2025-03-28T00:00:00Z", "checkCount": 4, "id": "c09c16f6-aead-4598-9bf1-473cdbe839a4", "companyId": "15079540", "type": "payperiod", "_rid": "NmJkAKiCbEd4EwQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEd4EwQAAAAAAA==/", "_etag": "\"a70010d6-0000-0100-0000-687038a90000\"", "_attachments": "attachments/", "_ts": 1752185001}, {"payPeriodId": "1060039416724162", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-03-24T00:00:00Z", "endDate": "2025-04-06T00:00:00Z", "submitByDate": "2025-04-09T00:00:00Z", "checkDate": "2025-04-11T00:00:00Z", "checkCount": 0, "id": "e0019d7b-1277-4a95-8c01-92c12e7a76ad", "companyId": "15079540", "type": "payperiod", "_rid": "NmJkAKiCbEd5EwQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEd5EwQAAAAAAA==/", "_etag": "\"a70013d6-0000-0100-0000-687038a90000\"", "_attachments": "attachments/", "_ts": 1752185001}, {"payPeriodId": "1060039486832238", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-04-07T00:00:00Z", "endDate": "2025-04-20T00:00:00Z", "submitByDate": "2025-04-23T00:00:00Z", "checkDate": "2025-04-25T00:00:00Z", "checkCount": 0, "id": "ef70bab2-9627-4cc9-a070-ab7a3a38b98a", "companyId": "15079540", "type": "payperiod", "_rid": "NmJkAKiCbEd6EwQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEd6EwQAAAAAAA==/", "_etag": "\"a70016d6-0000-0100-0000-687038a90000\"", "_attachments": "attachments/", "_ts": 1752185001}, {"payPeriodId": "1060039553576206", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-04-21T00:00:00Z", "endDate": "2025-05-04T00:00:00Z", "submitByDate": "2025-05-07T00:00:00Z", "checkDate": "2025-05-09T00:00:00Z", "checkCount": 0, "id": "ecf016ae-6c48-4e56-9edb-fad982936ba3", "companyId": "15079540", "type": "payperiod", "_rid": "NmJkAKiCbEd7EwQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEd7EwQAAAAAAA==/", "_etag": "\"a70019d6-0000-0100-0000-687038a90000\"", "_attachments": "attachments/", "_ts": 1752185001}, {"payPeriodId": "1060039623027462", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-05-05T00:00:00Z", "endDate": "2025-05-18T00:00:00Z", "submitByDate": "2025-05-21T00:00:00Z", "checkDate": "2025-05-23T00:00:00Z", "checkCount": 0, "id": "340adfe6-fb84-48bd-a93a-575c3f83fc4e", "companyId": "15079540", "type": "payperiod", "_rid": "NmJkAKiCbEd8EwQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEd8EwQAAAAAAA==/", "_etag": "\"a7001cd6-0000-0100-0000-687038a90000\"", "_attachments": "attachments/", "_ts": 1752185001}, {"payPeriodId": "1060039689384737", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-05-19T00:00:00Z", "endDate": "2025-06-01T00:00:00Z", "submitByDate": "2025-06-04T00:00:00Z", "checkDate": "2025-06-06T00:00:00Z", "checkCount": 0, "id": "45ead3d8-7398-4d6d-81fb-4efa6c4a9239", "companyId": "15079540", "type": "payperiod", "_rid": "NmJkAKiCbEd9EwQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEd9EwQAAAAAAA==/", "_etag": "\"a70023d6-0000-0100-0000-687038a90000\"", "_attachments": "attachments/", "_ts": 1752185001}, {"payPeriodId": "1060039760200655", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-06-02T00:00:00Z", "endDate": "2025-06-15T00:00:00Z", "submitByDate": "2025-06-18T00:00:00Z", "checkDate": "2025-06-20T00:00:00Z", "checkCount": 0, "id": "93cef874-66a3-4600-973f-7b5935c70b93", "companyId": "15079540", "type": "payperiod", "_rid": "NmJkAKiCbEd+EwQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEd+EwQAAAAAAA==/", "_etag": "\"a70025d6-0000-0100-0000-687038aa0000\"", "_attachments": "attachments/", "_ts": 1752185002}, {"payPeriodId": "1060039827780030", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-06-16T00:00:00Z", "endDate": "2025-06-29T00:00:00Z", "submitByDate": "2025-07-01T00:00:00Z", "checkDate": "2025-07-03T00:00:00Z", "checkCount": 0, "id": "580bcb0b-083f-46f1-8b01-ed965c242a69", "companyId": "15079540", "type": "payperiod", "_rid": "NmJkAKiCbEd-EwQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEd-EwQAAAAAAA==/", "_etag": "\"a70027d6-0000-0100-0000-687038aa0000\"", "_attachments": "attachments/", "_ts": 1752185002}, {"payPeriodId": "1060039905011089", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-06-30T00:00:00Z", "endDate": "2025-07-13T00:00:00Z", "submitByDate": "2025-07-16T00:00:00Z", "checkDate": "2025-07-18T00:00:00Z", "checkCount": 0, "id": "3590d1c0-fae1-408f-bc94-e0631f68ce98", "companyId": "15079540", "type": "payperiod", "_rid": "NmJkAKiCbEeAEwQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeAEwQAAAAAAA==/", "_etag": "\"a70029d6-0000-0100-0000-687038aa0000\"", "_attachments": "attachments/", "_ts": 1752185002}, {"payPeriodId": "1060039979223218", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-07-14T00:00:00Z", "endDate": "2025-07-27T00:00:00Z", "submitByDate": "2025-07-30T00:00:00Z", "checkDate": "2025-08-01T00:00:00Z", "checkCount": 0, "id": "278cdc63-530a-4f70-a775-2ad27c9b08a8", "companyId": "15079540", "type": "payperiod", "_rid": "NmJkAKiCbEeBEwQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeBEwQAAAAAAA==/", "_etag": "\"a7002cd6-0000-0100-0000-687038aa0000\"", "_attachments": "attachments/", "_ts": 1752185002}, {"payPeriodId": "1060040055974348", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-07-28T00:00:00Z", "endDate": "2025-08-10T00:00:00Z", "submitByDate": "2025-08-13T00:00:00Z", "checkDate": "2025-08-15T00:00:00Z", "checkCount": 0, "id": "db38777a-ce52-4d84-9c9f-f2979177952b", "companyId": "15079540", "type": "payperiod", "_rid": "NmJkAKiCbEeCEwQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeCEwQAAAAAAA==/", "_etag": "\"a7002ed6-0000-0100-0000-687038aa0000\"", "_attachments": "attachments/", "_ts": 1752185002}, {"payPeriodId": "1060040127449310", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-08-11T00:00:00Z", "endDate": "2025-08-24T00:00:00Z", "submitByDate": "2025-08-27T00:00:00Z", "checkDate": "2025-08-29T00:00:00Z", "checkCount": 0, "id": "2be26d6d-0e10-49ed-bbf4-317c1523e4a0", "companyId": "15079540", "type": "payperiod", "_rid": "NmJkAKiCbEeDEwQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeDEwQAAAAAAA==/", "_etag": "\"a70031d6-0000-0100-0000-687038aa0000\"", "_attachments": "attachments/", "_ts": 1752185002}, {"payPeriodId": "1060040196169362", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-08-25T00:00:00Z", "endDate": "2025-09-07T00:00:00Z", "submitByDate": "2025-09-10T00:00:00Z", "checkDate": "2025-09-12T00:00:00Z", "checkCount": 0, "id": "70d384d0-2593-4b83-a86e-e92c16444f14", "companyId": "15079540", "type": "payperiod", "_rid": "NmJkAKiCbEeEEwQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeEEwQAAAAAAA==/", "_etag": "\"a70033d6-0000-0100-0000-687038aa0000\"", "_attachments": "attachments/", "_ts": 1752185002}, {"payPeriodId": "1060040264207717", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-09-08T00:00:00Z", "endDate": "2025-09-21T00:00:00Z", "submitByDate": "2025-09-24T00:00:00Z", "checkDate": "2025-09-26T00:00:00Z", "checkCount": 0, "id": "33aa1367-d8aa-4477-b2df-38b29da814ae", "companyId": "15079540", "type": "payperiod", "_rid": "NmJkAKiCbEeFEwQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeFEwQAAAAAAA==/", "_etag": "\"a70039d6-0000-0100-0000-687038aa0000\"", "_attachments": "attachments/", "_ts": 1752185002}, {"payPeriodId": "1060040335335119", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-09-22T00:00:00Z", "endDate": "2025-10-05T00:00:00Z", "submitByDate": "2025-10-08T00:00:00Z", "checkDate": "2025-10-10T00:00:00Z", "checkCount": 0, "id": "680d0d51-60c1-4912-9db3-ac2574fb9322", "companyId": "15079540", "type": "payperiod", "_rid": "NmJkAKiCbEeGEwQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeGEwQAAAAAAA==/", "_etag": "\"a7003dd6-0000-0100-0000-687038aa0000\"", "_attachments": "attachments/", "_ts": 1752185002}, {"payPeriodId": "1060038969826025", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2024-12-30T00:00:00Z", "endDate": "2025-01-12T00:00:00Z", "submitByDate": "2025-01-15T00:00:00Z", "checkDate": "2025-01-17T00:00:00Z", "checkCount": 5, "id": "b4c74818-b1b5-49d2-9324-e8c0c311f7cb", "companyId": "15079540", "type": "payperiod", "_rid": "NmJkAKiCbEeREwQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeREwQAAAAAAA==/", "_etag": "\"a70060d6-0000-0100-0000-687038ab0000\"", "_attachments": "attachments/", "_ts": 1752185003}, {"payPeriodId": "1060039525765308", "status": "COMPLETED", "description": "Additional bonus payroll", "startDate": "2024-12-30T00:00:00Z", "endDate": "2025-01-12T00:00:00Z", "submitByDate": "2025-01-16T00:00:00Z", "checkDate": "2025-01-17T00:00:00Z", "checkCount": 1, "id": "f2cc0cc2-a863-4db3-aa4e-7a54600bfad5", "companyId": "15079540", "type": "payperiod", "_rid": "NmJkAKiCbEeSEwQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeSEwQAAAAAAA==/", "_etag": "\"a70064d6-0000-0100-0000-687038ab0000\"", "_attachments": "attachments/", "_ts": 1752185003}, {"payPeriodId": "1060039042261835", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-01-13T00:00:00Z", "endDate": "2025-01-26T00:00:00Z", "submitByDate": "2025-01-29T00:00:00Z", "checkDate": "2025-01-31T00:00:00Z", "checkCount": 5, "id": "fc1441af-88b9-4be8-8819-5d8e37dc1b5f", "companyId": "15079540", "type": "payperiod", "_rid": "NmJkAKiCbEeTEwQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeTEwQAAAAAAA==/", "_etag": "\"a7006ad6-0000-0100-0000-687038ab0000\"", "_attachments": "attachments/", "_ts": 1752185003}, {"payPeriodId": "1060039122401920", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-01-27T00:00:00Z", "endDate": "2025-02-09T00:00:00Z", "submitByDate": "2025-02-12T00:00:00Z", "checkDate": "2025-02-14T00:00:00Z", "checkCount": 5, "id": "fe88e303-4dbc-4c7d-b088-7f31d692f297", "companyId": "15079540", "type": "payperiod", "_rid": "NmJkAKiCbEeUEwQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeUEwQAAAAAAA==/", "_etag": "\"a7006cd6-0000-0100-0000-687038ab0000\"", "_attachments": "attachments/", "_ts": 1752185003}, {"payPeriodId": "1060039192000208", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-02-10T00:00:00Z", "endDate": "2025-02-23T00:00:00Z", "submitByDate": "2025-02-26T00:00:00Z", "checkDate": "2025-02-28T00:00:00Z", "checkCount": 6, "id": "b765e760-f3bb-4d54-9029-f244175ea310", "companyId": "15079540", "type": "payperiod", "_rid": "NmJkAKiCbEeVEwQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeVEwQAAAAAAA==/", "_etag": "\"a7006dd6-0000-0100-0000-687038ab0000\"", "_attachments": "attachments/", "_ts": 1752185003}, {"payPeriodId": "1060039264427457", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-02-24T00:00:00Z", "endDate": "2025-03-09T00:00:00Z", "submitByDate": "2025-03-12T00:00:00Z", "checkDate": "2025-03-14T00:00:00Z", "checkCount": 4, "id": "8b53cc7c-aa18-4d19-9b25-c2a708b36715", "companyId": "15079540", "type": "payperiod", "_rid": "NmJkAKiCbEeWEwQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeWEwQAAAAAAA==/", "_etag": "\"a7006ed6-0000-0100-0000-687038ab0000\"", "_attachments": "attachments/", "_ts": 1752185003}, {"payPeriodId": "1060039339030294", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-03-10T00:00:00Z", "endDate": "2025-03-23T00:00:00Z", "submitByDate": "2025-03-26T00:00:00Z", "checkDate": "2025-03-28T00:00:00Z", "checkCount": 4, "id": "3399deca-b51d-42ca-b655-d3aaeb139f47", "companyId": "15079540", "type": "payperiod", "_rid": "NmJkAKiCbEeXEwQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeXEwQAAAAAAA==/", "_etag": "\"a7006fd6-0000-0100-0000-687038ac0000\"", "_attachments": "attachments/", "_ts": 1752185004}, {"payPeriodId": "1060039416724162", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-03-24T00:00:00Z", "endDate": "2025-04-06T00:00:00Z", "submitByDate": "2025-04-09T00:00:00Z", "checkDate": "2025-04-11T00:00:00Z", "checkCount": 6, "id": "01a2274f-fba7-4005-bf0b-f6ef183111a1", "companyId": "15079540", "type": "payperiod", "_rid": "NmJkAKiCbEeYEwQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeYEwQAAAAAAA==/", "_etag": "\"a70071d6-0000-0100-0000-687038ac0000\"", "_attachments": "attachments/", "_ts": 1752185004}, {"payPeriodId": "1060039486832238", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-04-07T00:00:00Z", "endDate": "2025-04-20T00:00:00Z", "submitByDate": "2025-04-23T00:00:00Z", "checkDate": "2025-04-25T00:00:00Z", "checkCount": 4, "id": "b8c19a29-8f4c-4183-97e0-4513914df08e", "companyId": "15079540", "type": "payperiod", "_rid": "NmJkAKiCbEeZEwQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeZEwQAAAAAAA==/", "_etag": "\"a70072d6-0000-0100-0000-687038ac0000\"", "_attachments": "attachments/", "_ts": 1752185004}, {"payPeriodId": "1060039553576206", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-04-21T00:00:00Z", "endDate": "2025-05-04T00:00:00Z", "submitByDate": "2025-05-07T00:00:00Z", "checkDate": "2025-05-09T00:00:00Z", "checkCount": 4, "id": "08852ef2-9474-4149-aac4-184c48a9b6fe", "companyId": "15079540", "type": "payperiod", "_rid": "NmJkAKiCbEeaEwQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeaEwQAAAAAAA==/", "_etag": "\"a70073d6-0000-0100-0000-687038ac0000\"", "_attachments": "attachments/", "_ts": 1752185004}, {"payPeriodId": "1060039623027462", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-05-05T00:00:00Z", "endDate": "2025-05-18T00:00:00Z", "submitByDate": "2025-05-21T00:00:00Z", "checkDate": "2025-05-23T00:00:00Z", "checkCount": 4, "id": "0f426428-ac60-4498-b1ec-4748eb31c4b3", "companyId": "15079540", "type": "payperiod", "_rid": "NmJkAKiCbEebEwQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEebEwQAAAAAAA==/", "_etag": "\"a70076d6-0000-0100-0000-687038ac0000\"", "_attachments": "attachments/", "_ts": 1752185004}, {"payPeriodId": "1060039689384737", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-05-19T00:00:00Z", "endDate": "2025-06-01T00:00:00Z", "submitByDate": "2025-06-04T00:00:00Z", "checkDate": "2025-06-06T00:00:00Z", "checkCount": 4, "id": "fc6f415b-fde5-4697-ba37-efeee10dd5f5", "companyId": "15079540", "type": "payperiod", "_rid": "NmJkAKiCbEecEwQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEecEwQAAAAAAA==/", "_etag": "\"a70077d6-0000-0100-0000-687038ac0000\"", "_attachments": "attachments/", "_ts": 1752185004}, {"payPeriodId": "1060039760200655", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-06-02T00:00:00Z", "endDate": "2025-06-15T00:00:00Z", "submitByDate": "2025-06-18T00:00:00Z", "checkDate": "2025-06-20T00:00:00Z", "checkCount": 4, "id": "27d02e88-1824-44de-893e-cdfbea6165f7", "companyId": "15079540", "type": "payperiod", "_rid": "NmJkAKiCbEedEwQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEedEwQAAAAAAA==/", "_etag": "\"a7007ad6-0000-0100-0000-687038ac0000\"", "_attachments": "attachments/", "_ts": 1752185004}, {"payPeriodId": "1060039827780030", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-06-16T00:00:00Z", "endDate": "2025-06-29T00:00:00Z", "submitByDate": "2025-07-01T00:00:00Z", "checkDate": "2025-07-03T00:00:00Z", "checkCount": 4, "id": "7fdc77b5-e738-4bfe-9d3f-16bddf9122e8", "companyId": "15079540", "type": "payperiod", "_rid": "NmJkAKiCbEeeEwQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeeEwQAAAAAAA==/", "_etag": "\"a7007cd6-0000-0100-0000-687038ac0000\"", "_attachments": "attachments/", "_ts": 1752185004}, {"payPeriodId": "1060039905011089", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-06-30T00:00:00Z", "endDate": "2025-07-13T00:00:00Z", "submitByDate": "2025-07-16T00:00:00Z", "checkDate": "2025-07-18T00:00:00Z", "checkCount": 0, "id": "fd2c88f4-882f-465c-98f9-dda81e441127", "companyId": "15079540", "type": "payperiod", "_rid": "NmJkAKiCbEefEwQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEefEwQAAAAAAA==/", "_etag": "\"a70081d6-0000-0100-0000-687038ac0000\"", "_attachments": "attachments/", "_ts": 1752185004}, {"payPeriodId": "1060039979223218", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-07-14T00:00:00Z", "endDate": "2025-07-27T00:00:00Z", "submitByDate": "2025-07-30T00:00:00Z", "checkDate": "2025-08-01T00:00:00Z", "checkCount": 0, "id": "66e603ee-ab24-4f8d-a97b-4a95a19f0139", "companyId": "15079540", "type": "payperiod", "_rid": "NmJkAKiCbEegEwQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEegEwQAAAAAAA==/", "_etag": "\"a70087d6-0000-0100-0000-687038ac0000\"", "_attachments": "attachments/", "_ts": 1752185004}, {"payPeriodId": "1060040055974348", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-07-28T00:00:00Z", "endDate": "2025-08-10T00:00:00Z", "submitByDate": "2025-08-13T00:00:00Z", "checkDate": "2025-08-15T00:00:00Z", "checkCount": 0, "id": "dbab2952-9b40-4ddb-845d-a4e757f75129", "companyId": "15079540", "type": "payperiod", "_rid": "NmJkAKiCbEehEwQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEehEwQAAAAAAA==/", "_etag": "\"a70089d6-0000-0100-0000-687038ac0000\"", "_attachments": "attachments/", "_ts": 1752185004}, {"payPeriodId": "1060040127449310", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-08-11T00:00:00Z", "endDate": "2025-08-24T00:00:00Z", "submitByDate": "2025-08-27T00:00:00Z", "checkDate": "2025-08-29T00:00:00Z", "checkCount": 0, "id": "ef66bf88-f9da-4748-921d-17017d520a2d", "companyId": "15079540", "type": "payperiod", "_rid": "NmJkAKiCbEeiEwQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeiEwQAAAAAAA==/", "_etag": "\"a7008cd6-0000-0100-0000-687038ac0000\"", "_attachments": "attachments/", "_ts": 1752185004}, {"payPeriodId": "1060040196169362", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-08-25T00:00:00Z", "endDate": "2025-09-07T00:00:00Z", "submitByDate": "2025-09-10T00:00:00Z", "checkDate": "2025-09-12T00:00:00Z", "checkCount": 0, "id": "87efb38a-619a-4da9-9463-accfdd48cef9", "companyId": "15079540", "type": "payperiod", "_rid": "NmJkAKiCbEejEwQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEejEwQAAAAAAA==/", "_etag": "\"a7008fd6-0000-0100-0000-687038ac0000\"", "_attachments": "attachments/", "_ts": 1752185004}, {"payPeriodId": "1060040264207717", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-09-08T00:00:00Z", "endDate": "2025-09-21T00:00:00Z", "submitByDate": "2025-09-24T00:00:00Z", "checkDate": "2025-09-26T00:00:00Z", "checkCount": 0, "id": "c15d3b88-49e2-4bc3-b22d-2df8fd30587e", "companyId": "15079540", "type": "payperiod", "_rid": "NmJkAKiCbEekEwQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEekEwQAAAAAAA==/", "_etag": "\"a70092d6-0000-0100-0000-687038ad0000\"", "_attachments": "attachments/", "_ts": 1752185005}, {"payPeriodId": "1060040335335119", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-09-22T00:00:00Z", "endDate": "2025-10-05T00:00:00Z", "submitByDate": "2025-10-08T00:00:00Z", "checkDate": "2025-10-10T00:00:00Z", "checkCount": 0, "id": "9ffaa8b4-f0b6-4181-9cff-c16bc32f9e00", "companyId": "15079540", "type": "payperiod", "_rid": "NmJkAKiCbEelEwQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEelEwQAAAAAAA==/", "_etag": "\"a70093d6-0000-0100-0000-687038ad0000\"", "_attachments": "attachments/", "_ts": 1752185005}], "links": [{"rel": "self", "href": "https://ca-payroll-ai-mock-sb-001-secure.nicebeach-8a7a52ab.eastus.azurecontainerapps.io/ca/companies/15079540/payperiods"}]}, "status_code": 200}