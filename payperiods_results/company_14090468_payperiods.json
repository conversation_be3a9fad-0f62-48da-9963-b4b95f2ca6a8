{"success": true, "company_id": "14090468", "data": {"metadata": {"contentItemCount": 40}, "content": [{"payPeriodId": "1050103134919581", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (2)", "startDate": "2024-12-30T00:00:00Z", "endDate": "2025-01-12T00:00:00Z", "submitByDate": "2025-01-15T00:00:00Z", "checkDate": "2025-01-17T00:00:00Z", "checkCount": 1, "id": "d99badf6-787c-4e58-9dfe-a5bf22478ba0", "companyId": "14090468", "type": "payperiod", "_rid": "NmJkAKiCbEe9FgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEe9FgMAAAAAAA==/", "_etag": "\"a5002706-0000-0100-0000-687024390000\"", "_attachments": "attachments/", "_ts": 1752179769}, {"payPeriodId": "1050103609474187", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (2)", "startDate": "2025-01-13T00:00:00Z", "endDate": "2025-01-26T00:00:00Z", "submitByDate": "2025-01-29T00:00:00Z", "checkDate": "2025-01-31T00:00:00Z", "checkCount": 1, "id": "7375426b-7a25-4c4b-954b-efd1a6d2f0de", "companyId": "14090468", "type": "payperiod", "_rid": "NmJkAKiCbEe+FgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEe+FgMAAAAAAA==/", "_etag": "\"a5002906-0000-0100-0000-687024390000\"", "_attachments": "attachments/", "_ts": 1752179769}, {"payPeriodId": "1050104092858493", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (2)", "startDate": "2025-01-27T00:00:00Z", "endDate": "2025-02-09T00:00:00Z", "submitByDate": "2025-02-12T00:00:00Z", "checkDate": "2025-02-14T00:00:00Z", "checkCount": 1, "id": "95514a02-b404-4c89-a74a-cb8088560aac", "companyId": "14090468", "type": "payperiod", "_rid": "NmJkAKiCbEe-FgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEe-FgMAAAAAAA==/", "_etag": "\"a5002e06-0000-0100-0000-687024390000\"", "_attachments": "attachments/", "_ts": 1752179769}, {"payPeriodId": "1050104568648766", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (2)", "startDate": "2025-02-10T00:00:00Z", "endDate": "2025-02-23T00:00:00Z", "submitByDate": "2025-02-26T00:00:00Z", "checkDate": "2025-02-28T00:00:00Z", "checkCount": 1, "id": "448b9cfe-2618-4eb1-a284-ea9220c72e06", "companyId": "14090468", "type": "payperiod", "_rid": "NmJkAKiCbEfAFgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfAFgMAAAAAAA==/", "_etag": "\"a5003006-0000-0100-0000-687024390000\"", "_attachments": "attachments/", "_ts": 1752179769}, {"payPeriodId": "1050105037003132", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (2)", "startDate": "2025-02-24T00:00:00Z", "endDate": "2025-03-09T00:00:00Z", "submitByDate": "2025-03-12T00:00:00Z", "checkDate": "2025-03-14T00:00:00Z", "checkCount": 1, "id": "1c721a14-e5dd-4fdd-b72d-f5eaf33cc09e", "companyId": "14090468", "type": "payperiod", "_rid": "NmJkAKiCbEfBFgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfBFgMAAAAAAA==/", "_etag": "\"a5003406-0000-0100-0000-687024390000\"", "_attachments": "attachments/", "_ts": 1752179769}, {"payPeriodId": "1050105547361203", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (2)", "startDate": "2025-03-10T00:00:00Z", "endDate": "2025-03-23T00:00:00Z", "submitByDate": "2025-03-26T00:00:00Z", "checkDate": "2025-03-28T00:00:00Z", "checkCount": 1, "id": "ad0a500e-32bb-4a4d-9995-0b6a6c4033b9", "companyId": "14090468", "type": "payperiod", "_rid": "NmJkAKiCbEfCFgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfCFgMAAAAAAA==/", "_etag": "\"a5003606-0000-0100-0000-687024390000\"", "_attachments": "attachments/", "_ts": 1752179769}, {"payPeriodId": "1050106053777219", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (2)", "startDate": "2025-03-24T00:00:00Z", "endDate": "2025-04-06T00:00:00Z", "submitByDate": "2025-04-09T00:00:00Z", "checkDate": "2025-04-11T00:00:00Z", "checkCount": 0, "id": "0a11db36-ce0f-4d8f-89d0-9da629e1e8c0", "companyId": "14090468", "type": "payperiod", "_rid": "NmJkAKiCbEfDFgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfDFgMAAAAAAA==/", "_etag": "\"a5003806-0000-0100-0000-687024390000\"", "_attachments": "attachments/", "_ts": 1752179769}, {"payPeriodId": "1050106539836956", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (2)", "startDate": "2025-04-07T00:00:00Z", "endDate": "2025-04-20T00:00:00Z", "submitByDate": "2025-04-23T00:00:00Z", "checkDate": "2025-04-25T00:00:00Z", "checkCount": 0, "id": "5a282866-a6df-4f93-a722-b0e2ba447399", "companyId": "14090468", "type": "payperiod", "_rid": "NmJkAKiCbEfEFgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfEFgMAAAAAAA==/", "_etag": "\"a5003c06-0000-0100-0000-687024390000\"", "_attachments": "attachments/", "_ts": 1752179769}, {"payPeriodId": "1050107011006808", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (2)", "startDate": "2025-04-21T00:00:00Z", "endDate": "2025-05-04T00:00:00Z", "submitByDate": "2025-05-07T00:00:00Z", "checkDate": "2025-05-09T00:00:00Z", "checkCount": 0, "id": "27aba3b1-2399-4a1e-82e4-ce2b1d6a58f8", "companyId": "14090468", "type": "payperiod", "_rid": "NmJkAKiCbEfFFgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfFFgMAAAAAAA==/", "_etag": "\"a5004006-0000-0100-0000-687024390000\"", "_attachments": "attachments/", "_ts": 1752179769}, {"payPeriodId": "1050107499619079", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (2)", "startDate": "2025-05-05T00:00:00Z", "endDate": "2025-05-18T00:00:00Z", "submitByDate": "2025-05-21T00:00:00Z", "checkDate": "2025-05-23T00:00:00Z", "checkCount": 0, "id": "f48886be-378d-4730-8b7a-977f9911b8fd", "companyId": "14090468", "type": "payperiod", "_rid": "NmJkAKiCbEfGFgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfGFgMAAAAAAA==/", "_etag": "\"a5004206-0000-0100-0000-687024390000\"", "_attachments": "attachments/", "_ts": 1752179769}, {"payPeriodId": "1050107973657472", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (2)", "startDate": "2025-05-19T00:00:00Z", "endDate": "2025-06-01T00:00:00Z", "submitByDate": "2025-06-04T00:00:00Z", "checkDate": "2025-06-06T00:00:00Z", "checkCount": 0, "id": "96b0ceb8-ed17-4b62-96ef-ffca9067e175", "companyId": "14090468", "type": "payperiod", "_rid": "NmJkAKiCbEfHFgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfHFgMAAAAAAA==/", "_etag": "\"a5004506-0000-0100-0000-687024390000\"", "_attachments": "attachments/", "_ts": 1752179769}, {"payPeriodId": "1050108448842842", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (2)", "startDate": "2025-06-02T00:00:00Z", "endDate": "2025-06-15T00:00:00Z", "submitByDate": "2025-06-18T00:00:00Z", "checkDate": "2025-06-20T00:00:00Z", "checkCount": 0, "id": "f255b8f3-98f0-48dc-a33a-4af9d689a0a0", "companyId": "14090468", "type": "payperiod", "_rid": "NmJkAKiCbEfIFgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfIFgMAAAAAAA==/", "_etag": "\"a5004a06-0000-0100-0000-6870243a0000\"", "_attachments": "attachments/", "_ts": 1752179770}, {"payPeriodId": "1050108918802777", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (2)", "startDate": "2025-06-16T00:00:00Z", "endDate": "2025-06-29T00:00:00Z", "submitByDate": "2025-07-01T00:00:00Z", "checkDate": "2025-07-03T00:00:00Z", "checkCount": 0, "id": "e512e5fd-d033-41a6-abc2-ef95f337a9c4", "companyId": "14090468", "type": "payperiod", "_rid": "NmJkAKiCbEfJFgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfJFgMAAAAAAA==/", "_etag": "\"a5004d06-0000-0100-0000-6870243a0000\"", "_attachments": "attachments/", "_ts": 1752179770}, {"payPeriodId": "1050109420361368", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (2)", "startDate": "2025-06-30T00:00:00Z", "endDate": "2025-07-13T00:00:00Z", "submitByDate": "2025-07-16T00:00:00Z", "checkDate": "2025-07-18T00:00:00Z", "checkCount": 0, "id": "39ee46f3-fb2c-4a85-8bf4-e370a5d28996", "companyId": "14090468", "type": "payperiod", "_rid": "NmJkAKiCbEfKFgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfKFgMAAAAAAA==/", "_etag": "\"a5004e06-0000-0100-0000-6870243a0000\"", "_attachments": "attachments/", "_ts": 1752179770}, {"payPeriodId": "1050109909549618", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (2)", "startDate": "2025-07-14T00:00:00Z", "endDate": "2025-07-27T00:00:00Z", "submitByDate": "2025-07-30T00:00:00Z", "checkDate": "2025-08-01T00:00:00Z", "checkCount": 0, "id": "c0b2f994-8d9e-4d94-95e6-5d6cf1987bbd", "companyId": "14090468", "type": "payperiod", "_rid": "NmJkAKiCbEfLFgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfLFgMAAAAAAA==/", "_etag": "\"a5005206-0000-0100-0000-6870243a0000\"", "_attachments": "attachments/", "_ts": 1752179770}, {"payPeriodId": "1050110397869848", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (2)", "startDate": "2025-07-28T00:00:00Z", "endDate": "2025-08-10T00:00:00Z", "submitByDate": "2025-08-13T00:00:00Z", "checkDate": "2025-08-15T00:00:00Z", "checkCount": 0, "id": "a5d4ca68-bf47-4816-b9d6-9a5397a062c0", "companyId": "14090468", "type": "payperiod", "_rid": "NmJkAKiCbEfMFgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfMFgMAAAAAAA==/", "_etag": "\"a5005406-0000-0100-0000-6870243a0000\"", "_attachments": "attachments/", "_ts": 1752179770}, {"payPeriodId": "1050110890124716", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (2)", "startDate": "2025-08-11T00:00:00Z", "endDate": "2025-08-24T00:00:00Z", "submitByDate": "2025-08-27T00:00:00Z", "checkDate": "2025-08-29T00:00:00Z", "checkCount": 0, "id": "0f5bf99b-1049-4dfe-a806-b86de8475c90", "companyId": "14090468", "type": "payperiod", "_rid": "NmJkAKiCbEfNFgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfNFgMAAAAAAA==/", "_etag": "\"a5005506-0000-0100-0000-6870243a0000\"", "_attachments": "attachments/", "_ts": 1752179770}, {"payPeriodId": "1050111388939310", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (2)", "startDate": "2025-08-25T00:00:00Z", "endDate": "2025-09-07T00:00:00Z", "submitByDate": "2025-09-10T00:00:00Z", "checkDate": "2025-09-12T00:00:00Z", "checkCount": 0, "id": "8478cc0a-6f8c-4bb0-9037-e73f896e902a", "companyId": "14090468", "type": "payperiod", "_rid": "NmJkAKiCbEfOFgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfOFgMAAAAAAA==/", "_etag": "\"a5005706-0000-0100-0000-6870243a0000\"", "_attachments": "attachments/", "_ts": 1752179770}, {"payPeriodId": "1050111895024220", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (2)", "startDate": "2025-09-08T00:00:00Z", "endDate": "2025-09-21T00:00:00Z", "submitByDate": "2025-09-24T00:00:00Z", "checkDate": "2025-09-26T00:00:00Z", "checkCount": 0, "id": "94daee6c-3817-4c1f-a49c-6426bf236bd2", "companyId": "14090468", "type": "payperiod", "_rid": "NmJkAKiCbEfPFgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfPFgMAAAAAAA==/", "_etag": "\"a5005a06-0000-0100-0000-6870243a0000\"", "_attachments": "attachments/", "_ts": 1752179770}, {"payPeriodId": "1050112397577387", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (2)", "startDate": "2025-09-22T00:00:00Z", "endDate": "2025-10-05T00:00:00Z", "submitByDate": "2025-10-08T00:00:00Z", "checkDate": "2025-10-10T00:00:00Z", "checkCount": 0, "id": "9fdd6b56-3158-4e69-8a14-b5c493efc0f5", "companyId": "14090468", "type": "payperiod", "_rid": "NmJkAKiCbEfQFgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfQFgMAAAAAAA==/", "_etag": "\"a5005d06-0000-0100-0000-6870243a0000\"", "_attachments": "attachments/", "_ts": 1752179770}, {"payPeriodId": "1050103134919581", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (2)", "startDate": "2024-12-30T00:00:00Z", "endDate": "2025-01-12T00:00:00Z", "submitByDate": "2025-01-15T00:00:00Z", "checkDate": "2025-01-17T00:00:00Z", "checkCount": 1, "id": "a61039f5-bab8-4d47-afd3-bed0885f6e8e", "companyId": "14090468", "type": "payperiod", "_rid": "NmJkAKiCbEfSFgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfSFgMAAAAAAA==/", "_etag": "\"a5006306-0000-0100-0000-6870243a0000\"", "_attachments": "attachments/", "_ts": 1752179770}, {"payPeriodId": "1050103609474187", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (2)", "startDate": "2025-01-13T00:00:00Z", "endDate": "2025-01-26T00:00:00Z", "submitByDate": "2025-01-29T00:00:00Z", "checkDate": "2025-01-31T00:00:00Z", "checkCount": 1, "id": "e2895ef4-0c2d-4ddb-b52d-db074c69a115", "companyId": "14090468", "type": "payperiod", "_rid": "NmJkAKiCbEfTFgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfTFgMAAAAAAA==/", "_etag": "\"a5006506-0000-0100-0000-6870243a0000\"", "_attachments": "attachments/", "_ts": 1752179770}, {"payPeriodId": "1050104092858493", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (2)", "startDate": "2025-01-27T00:00:00Z", "endDate": "2025-02-09T00:00:00Z", "submitByDate": "2025-02-12T00:00:00Z", "checkDate": "2025-02-14T00:00:00Z", "checkCount": 1, "id": "eec2bd3c-9c8c-4b72-aacd-97d18abebddb", "companyId": "14090468", "type": "payperiod", "_rid": "NmJkAKiCbEfUFgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfUFgMAAAAAAA==/", "_etag": "\"a5006b06-0000-0100-0000-6870243a0000\"", "_attachments": "attachments/", "_ts": 1752179770}, {"payPeriodId": "1050104568648766", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (2)", "startDate": "2025-02-10T00:00:00Z", "endDate": "2025-02-23T00:00:00Z", "submitByDate": "2025-02-26T00:00:00Z", "checkDate": "2025-02-28T00:00:00Z", "checkCount": 1, "id": "77b010da-1f6c-46dc-bbba-b942708692ff", "companyId": "14090468", "type": "payperiod", "_rid": "NmJkAKiCbEfVFgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfVFgMAAAAAAA==/", "_etag": "\"a5007006-0000-0100-0000-6870243b0000\"", "_attachments": "attachments/", "_ts": 1752179771}, {"payPeriodId": "1050105037003132", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (2)", "startDate": "2025-02-24T00:00:00Z", "endDate": "2025-03-09T00:00:00Z", "submitByDate": "2025-03-12T00:00:00Z", "checkDate": "2025-03-14T00:00:00Z", "checkCount": 1, "id": "64a9c461-5570-44ff-81bf-8e1a90b5da2d", "companyId": "14090468", "type": "payperiod", "_rid": "NmJkAKiCbEfWFgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfWFgMAAAAAAA==/", "_etag": "\"a5007206-0000-0100-0000-6870243b0000\"", "_attachments": "attachments/", "_ts": 1752179771}, {"payPeriodId": "1050105547361203", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (2)", "startDate": "2025-03-10T00:00:00Z", "endDate": "2025-03-23T00:00:00Z", "submitByDate": "2025-03-26T00:00:00Z", "checkDate": "2025-03-28T00:00:00Z", "checkCount": 1, "id": "9402e2ad-e776-49d0-8827-c0193130e412", "companyId": "14090468", "type": "payperiod", "_rid": "NmJkAKiCbEfXFgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfXFgMAAAAAAA==/", "_etag": "\"a5007406-0000-0100-0000-6870243b0000\"", "_attachments": "attachments/", "_ts": 1752179771}, {"payPeriodId": "1050106053777219", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (2)", "startDate": "2025-03-24T00:00:00Z", "endDate": "2025-04-06T00:00:00Z", "submitByDate": "2025-04-09T00:00:00Z", "checkDate": "2025-04-11T00:00:00Z", "checkCount": 1, "id": "c086bf7c-431a-4340-999a-934f5a7e09fb", "companyId": "14090468", "type": "payperiod", "_rid": "NmJkAKiCbEfYFgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfYFgMAAAAAAA==/", "_etag": "\"a5007606-0000-0100-0000-6870243b0000\"", "_attachments": "attachments/", "_ts": 1752179771}, {"payPeriodId": "1050106539836956", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (2)", "startDate": "2025-04-07T00:00:00Z", "endDate": "2025-04-20T00:00:00Z", "submitByDate": "2025-04-23T00:00:00Z", "checkDate": "2025-04-25T00:00:00Z", "checkCount": 1, "id": "843e2198-61f3-4693-a5e3-d53f1b0ca093", "companyId": "14090468", "type": "payperiod", "_rid": "NmJkAKiCbEfZFgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfZFgMAAAAAAA==/", "_etag": "\"a5007806-0000-0100-0000-6870243b0000\"", "_attachments": "attachments/", "_ts": 1752179771}, {"payPeriodId": "1050107011006808", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (2)", "startDate": "2025-04-21T00:00:00Z", "endDate": "2025-05-04T00:00:00Z", "submitByDate": "2025-05-07T00:00:00Z", "checkDate": "2025-05-09T00:00:00Z", "checkCount": 1, "id": "5e43817a-1ec9-4a1e-b90d-3df28fe4487c", "companyId": "14090468", "type": "payperiod", "_rid": "NmJkAKiCbEfaFgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfaFgMAAAAAAA==/", "_etag": "\"a5007906-0000-0100-0000-6870243b0000\"", "_attachments": "attachments/", "_ts": 1752179771}, {"payPeriodId": "1050107499619079", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (2)", "startDate": "2025-05-05T00:00:00Z", "endDate": "2025-05-18T00:00:00Z", "submitByDate": "2025-05-21T00:00:00Z", "checkDate": "2025-05-23T00:00:00Z", "checkCount": 1, "id": "56809eed-7f1b-48a9-8c05-0001c91b275e", "companyId": "14090468", "type": "payperiod", "_rid": "NmJkAKiCbEfbFgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfbFgMAAAAAAA==/", "_etag": "\"a5007b06-0000-0100-0000-6870243b0000\"", "_attachments": "attachments/", "_ts": 1752179771}, {"payPeriodId": "1050107973657472", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (2)", "startDate": "2025-05-19T00:00:00Z", "endDate": "2025-06-01T00:00:00Z", "submitByDate": "2025-06-04T00:00:00Z", "checkDate": "2025-06-06T00:00:00Z", "checkCount": 1, "id": "83b899f4-5343-48b2-9479-00eca75bb976", "companyId": "14090468", "type": "payperiod", "_rid": "NmJkAKiCbEfcFgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfcFgMAAAAAAA==/", "_etag": "\"a5007e06-0000-0100-0000-6870243b0000\"", "_attachments": "attachments/", "_ts": 1752179771}, {"payPeriodId": "1050108448842842", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (2)", "startDate": "2025-06-02T00:00:00Z", "endDate": "2025-06-15T00:00:00Z", "submitByDate": "2025-06-18T00:00:00Z", "checkDate": "2025-06-20T00:00:00Z", "checkCount": 1, "id": "aaa12495-2480-4a1c-8cf3-4e291733bca3", "companyId": "14090468", "type": "payperiod", "_rid": "NmJkAKiCbEfdFgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfdFgMAAAAAAA==/", "_etag": "\"a5008206-0000-0100-0000-6870243b0000\"", "_attachments": "attachments/", "_ts": 1752179771}, {"payPeriodId": "1050108918802777", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (2)", "startDate": "2025-06-16T00:00:00Z", "endDate": "2025-06-29T00:00:00Z", "submitByDate": "2025-07-01T00:00:00Z", "checkDate": "2025-07-03T00:00:00Z", "checkCount": 1, "id": "8275a18f-8c0d-4100-b738-6a78a6266b6b", "companyId": "14090468", "type": "payperiod", "_rid": "NmJkAKiCbEfeFgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfeFgMAAAAAAA==/", "_etag": "\"a5008306-0000-0100-0000-6870243b0000\"", "_attachments": "attachments/", "_ts": 1752179771}, {"payPeriodId": "1050109420361368", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (2)", "startDate": "2025-06-30T00:00:00Z", "endDate": "2025-07-13T00:00:00Z", "submitByDate": "2025-07-16T00:00:00Z", "checkDate": "2025-07-18T00:00:00Z", "checkCount": 0, "id": "97758734-f225-4c55-bee2-de0aadaeb28f", "companyId": "14090468", "type": "payperiod", "_rid": "NmJkAKiCbEffFgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEffFgMAAAAAAA==/", "_etag": "\"a5008606-0000-0100-0000-6870243b0000\"", "_attachments": "attachments/", "_ts": 1752179771}, {"payPeriodId": "1050109909549618", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (2)", "startDate": "2025-07-14T00:00:00Z", "endDate": "2025-07-27T00:00:00Z", "submitByDate": "2025-07-30T00:00:00Z", "checkDate": "2025-08-01T00:00:00Z", "checkCount": 0, "id": "20bf1789-24df-4354-bc82-9ec05a62800e", "companyId": "14090468", "type": "payperiod", "_rid": "NmJkAKiCbEfgFgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfgFgMAAAAAAA==/", "_etag": "\"a5008906-0000-0100-0000-6870243b0000\"", "_attachments": "attachments/", "_ts": 1752179771}, {"payPeriodId": "1050110397869848", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (2)", "startDate": "2025-07-28T00:00:00Z", "endDate": "2025-08-10T00:00:00Z", "submitByDate": "2025-08-13T00:00:00Z", "checkDate": "2025-08-15T00:00:00Z", "checkCount": 0, "id": "b7d31422-3de8-4e9a-89d9-8bbcd200412b", "companyId": "14090468", "type": "payperiod", "_rid": "NmJkAKiCbEfhFgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfhFgMAAAAAAA==/", "_etag": "\"a5008a06-0000-0100-0000-6870243c0000\"", "_attachments": "attachments/", "_ts": 1752179772}, {"payPeriodId": "1050110890124716", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (2)", "startDate": "2025-08-11T00:00:00Z", "endDate": "2025-08-24T00:00:00Z", "submitByDate": "2025-08-27T00:00:00Z", "checkDate": "2025-08-29T00:00:00Z", "checkCount": 0, "id": "24ba7fb5-3341-40e1-bb41-933e3045088e", "companyId": "14090468", "type": "payperiod", "_rid": "NmJkAKiCbEfiFgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfiFgMAAAAAAA==/", "_etag": "\"a5008d06-0000-0100-0000-6870243c0000\"", "_attachments": "attachments/", "_ts": 1752179772}, {"payPeriodId": "1050111388939310", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (2)", "startDate": "2025-08-25T00:00:00Z", "endDate": "2025-09-07T00:00:00Z", "submitByDate": "2025-09-10T00:00:00Z", "checkDate": "2025-09-12T00:00:00Z", "checkCount": 0, "id": "93059103-18f1-494b-8d16-4efc92ebe3f2", "companyId": "14090468", "type": "payperiod", "_rid": "NmJkAKiCbEfjFgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfjFgMAAAAAAA==/", "_etag": "\"a5009106-0000-0100-0000-6870243c0000\"", "_attachments": "attachments/", "_ts": 1752179772}, {"payPeriodId": "1050111895024220", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (2)", "startDate": "2025-09-08T00:00:00Z", "endDate": "2025-09-21T00:00:00Z", "submitByDate": "2025-09-24T00:00:00Z", "checkDate": "2025-09-26T00:00:00Z", "checkCount": 0, "id": "3f9c6bd3-4f06-439e-b7b1-bc6ea00f8a12", "companyId": "14090468", "type": "payperiod", "_rid": "NmJkAKiCbEfkFgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfkFgMAAAAAAA==/", "_etag": "\"a5009206-0000-0100-0000-6870243c0000\"", "_attachments": "attachments/", "_ts": 1752179772}, {"payPeriodId": "1050112397577387", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (2)", "startDate": "2025-09-22T00:00:00Z", "endDate": "2025-10-05T00:00:00Z", "submitByDate": "2025-10-08T00:00:00Z", "checkDate": "2025-10-10T00:00:00Z", "checkCount": 0, "id": "76ef00de-c3a7-4d55-a537-1c33fd511300", "companyId": "14090468", "type": "payperiod", "_rid": "NmJkAKiCbEflFgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEflFgMAAAAAAA==/", "_etag": "\"a5009406-0000-0100-0000-6870243c0000\"", "_attachments": "attachments/", "_ts": 1752179772}], "links": [{"rel": "self", "href": "https://ca-payroll-ai-mock-sb-001-secure.nicebeach-8a7a52ab.eastus.azurecontainerapps.io/ca/companies/14090468/payperiods"}]}, "status_code": 200}