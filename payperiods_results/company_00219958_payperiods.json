{"success": true, "company_id": "00219958", "data": {"metadata": {"contentItemCount": 42}, "content": [{"payPeriodId": "1070074739662782", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2024-12-22T00:00:00Z", "endDate": "2025-01-04T00:00:00Z", "submitByDate": "2025-01-06T00:00:00Z", "checkDate": "2025-01-08T00:00:00Z", "checkCount": 2, "id": "0c1ea1e8-463d-45a2-9a67-595d0f7c6f19", "companyId": "00219958", "type": "payperiod", "_rid": "NmJkAKiCbEejeAQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEejeAQAAAAAAA==/", "_etag": "\"a800b7c5-0000-0100-0000-687040f00000\"", "_attachments": "attachments/", "_ts": 1752187120}, {"payPeriodId": "1070075014419225", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-01-05T00:00:00Z", "endDate": "2025-01-18T00:00:00Z", "submitByDate": "2025-01-20T00:00:00Z", "checkDate": "2025-01-22T00:00:00Z", "checkCount": 2, "id": "aab1cbfb-ebb4-4815-a1ca-8874b859aea0", "companyId": "00219958", "type": "payperiod", "_rid": "NmJkAKiCbEekeAQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEekeAQAAAAAAA==/", "_etag": "\"a800b9c5-0000-0100-0000-687040f00000\"", "_attachments": "attachments/", "_ts": 1752187120}, {"payPeriodId": "1070075275999389", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-01-19T00:00:00Z", "endDate": "2025-02-01T00:00:00Z", "submitByDate": "2025-02-03T00:00:00Z", "checkDate": "2025-02-05T00:00:00Z", "checkCount": 2, "id": "ee384544-34f3-4f4a-bdc2-69b099826fbb", "companyId": "00219958", "type": "payperiod", "_rid": "NmJkAKiCbEeleAQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeleAQAAAAAAA==/", "_etag": "\"a800bac5-0000-0100-0000-687040f10000\"", "_attachments": "attachments/", "_ts": 1752187121}, {"payPeriodId": "1070075543580044", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-02-02T00:00:00Z", "endDate": "2025-02-15T00:00:00Z", "submitByDate": "2025-02-17T00:00:00Z", "checkDate": "2025-02-19T00:00:00Z", "checkCount": 2, "id": "9427800b-5131-4c18-a11c-17eb72bd2dcd", "companyId": "00219958", "type": "payperiod", "_rid": "NmJkAKiCbEemeAQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEemeAQAAAAAAA==/", "_etag": "\"a800bbc5-0000-0100-0000-687040f10000\"", "_attachments": "attachments/", "_ts": 1752187121}, {"payPeriodId": "1070075808130152", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-02-16T00:00:00Z", "endDate": "2025-03-01T00:00:00Z", "submitByDate": "2025-03-03T00:00:00Z", "checkDate": "2025-03-05T00:00:00Z", "checkCount": 2, "id": "ec1b06c9-e7e4-4525-b6fd-38782bbbe26c", "companyId": "00219958", "type": "payperiod", "_rid": "NmJkAKiCbEeneAQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeneAQAAAAAAA==/", "_etag": "\"a800bcc5-0000-0100-0000-687040f10000\"", "_attachments": "attachments/", "_ts": 1752187121}, {"payPeriodId": "1070076067945114", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-03-02T00:00:00Z", "endDate": "2025-03-15T00:00:00Z", "submitByDate": "2025-03-17T00:00:00Z", "checkDate": "2025-03-19T00:00:00Z", "checkCount": 2, "id": "d21ad1b5-5fae-44b4-a4f6-a785f4759b75", "companyId": "00219958", "type": "payperiod", "_rid": "NmJkAKiCbEeoeAQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeoeAQAAAAAAA==/", "_etag": "\"a800bec5-0000-0100-0000-687040f10000\"", "_attachments": "attachments/", "_ts": 1752187121}, {"payPeriodId": "1070076345588476", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-03-16T00:00:00Z", "endDate": "2025-03-29T00:00:00Z", "submitByDate": "2025-03-31T00:00:00Z", "checkDate": "2025-04-02T00:00:00Z", "checkCount": 0, "id": "fb1c8220-85fb-45ab-a1bb-f0d1b7fd7a9a", "companyId": "00219958", "type": "payperiod", "_rid": "NmJkAKiCbEepeAQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEepeAQAAAAAAA==/", "_etag": "\"a800bfc5-0000-0100-0000-687040f10000\"", "_attachments": "attachments/", "_ts": 1752187121}, {"payPeriodId": "1070076590024336", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-03-30T00:00:00Z", "endDate": "2025-04-12T00:00:00Z", "submitByDate": "2025-04-14T00:00:00Z", "checkDate": "2025-04-16T00:00:00Z", "checkCount": 0, "id": "13f4e2c8-9074-40d3-862c-9a6750662f62", "companyId": "00219958", "type": "payperiod", "_rid": "NmJkAKiCbEeqeAQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeqeAQAAAAAAA==/", "_etag": "\"a800c1c5-0000-0100-0000-687040f10000\"", "_attachments": "attachments/", "_ts": 1752187121}, {"payPeriodId": "1070076872641109", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-04-13T00:00:00Z", "endDate": "2025-04-26T00:00:00Z", "submitByDate": "2025-04-28T00:00:00Z", "checkDate": "2025-04-30T00:00:00Z", "checkCount": 0, "id": "33983ebd-a0c2-4e31-97a0-0e0e5d383036", "companyId": "00219958", "type": "payperiod", "_rid": "NmJkAKiCbEereAQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEereAQAAAAAAA==/", "_etag": "\"a800c3c5-0000-0100-0000-687040f10000\"", "_attachments": "attachments/", "_ts": 1752187121}, {"payPeriodId": "1070077119676473", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-04-27T00:00:00Z", "endDate": "2025-05-10T00:00:00Z", "submitByDate": "2025-05-12T00:00:00Z", "checkDate": "2025-05-14T00:00:00Z", "checkCount": 0, "id": "48a534e5-30ac-448e-936e-451d66b1bfcd", "companyId": "00219958", "type": "payperiod", "_rid": "NmJkAKiCbEeseAQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeseAQAAAAAAA==/", "_etag": "\"a800c6c5-0000-0100-0000-687040f10000\"", "_attachments": "attachments/", "_ts": 1752187121}, {"payPeriodId": "1070077386271512", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-05-11T00:00:00Z", "endDate": "2025-05-24T00:00:00Z", "submitByDate": "2025-05-23T00:00:00Z", "checkDate": "2025-05-28T00:00:00Z", "checkCount": 0, "id": "64fe826c-91a8-46cc-912a-8ab971af74b4", "companyId": "00219958", "type": "payperiod", "_rid": "NmJkAKiCbEeteAQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeteAQAAAAAAA==/", "_etag": "\"a800c7c5-0000-0100-0000-687040f10000\"", "_attachments": "attachments/", "_ts": 1752187121}, {"payPeriodId": "1070077650657477", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-05-25T00:00:00Z", "endDate": "2025-06-07T00:00:00Z", "submitByDate": "2025-06-09T00:00:00Z", "checkDate": "2025-06-11T00:00:00Z", "checkCount": 0, "id": "569e9e0d-27a5-481e-b83f-4f6935d94fb4", "companyId": "00219958", "type": "payperiod", "_rid": "NmJkAKiCbEeueAQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeueAQAAAAAAA==/", "_etag": "\"a800c9c5-0000-0100-0000-687040f10000\"", "_attachments": "attachments/", "_ts": 1752187121}, {"payPeriodId": "1070077916709777", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-06-08T00:00:00Z", "endDate": "2025-06-21T00:00:00Z", "submitByDate": "2025-06-23T00:00:00Z", "checkDate": "2025-06-25T00:00:00Z", "checkCount": 0, "id": "bb23bc00-f06c-4dd3-80ba-1306e05d4f0a", "companyId": "00219958", "type": "payperiod", "_rid": "NmJkAKiCbEeveAQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeveAQAAAAAAA==/", "_etag": "\"a800ccc5-0000-0100-0000-687040f20000\"", "_attachments": "attachments/", "_ts": 1752187122}, {"payPeriodId": "1070078180887030", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-06-22T00:00:00Z", "endDate": "2025-07-05T00:00:00Z", "submitByDate": "2025-07-07T00:00:00Z", "checkDate": "2025-07-09T00:00:00Z", "checkCount": 0, "id": "797577fa-7571-4e47-9a41-606a57b69a65", "companyId": "00219958", "type": "payperiod", "_rid": "NmJkAKiCbEeweAQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeweAQAAAAAAA==/", "_etag": "\"a800cec5-0000-0100-0000-687040f20000\"", "_attachments": "attachments/", "_ts": 1752187122}, {"payPeriodId": "1070078450977244", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-07-06T00:00:00Z", "endDate": "2025-07-19T00:00:00Z", "submitByDate": "2025-07-21T00:00:00Z", "checkDate": "2025-07-23T00:00:00Z", "checkCount": 0, "id": "98b620b9-e718-4cf0-a241-299766f658f5", "companyId": "00219958", "type": "payperiod", "_rid": "NmJkAKiCbEexeAQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEexeAQAAAAAAA==/", "_etag": "\"a800cfc5-0000-0100-0000-687040f20000\"", "_attachments": "attachments/", "_ts": 1752187122}, {"payPeriodId": "1070078706753425", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-07-20T00:00:00Z", "endDate": "2025-08-02T00:00:00Z", "submitByDate": "2025-08-04T00:00:00Z", "checkDate": "2025-08-06T00:00:00Z", "checkCount": 0, "id": "1ab35ddd-e2a8-4132-aa36-08bb1b6919b5", "companyId": "00219958", "type": "payperiod", "_rid": "NmJkAKiCbEeyeAQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeyeAQAAAAAAA==/", "_etag": "\"a800d2c5-0000-0100-0000-687040f20000\"", "_attachments": "attachments/", "_ts": 1752187122}, {"payPeriodId": "1070078978663312", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-08-03T00:00:00Z", "endDate": "2025-08-16T00:00:00Z", "submitByDate": "2025-08-18T00:00:00Z", "checkDate": "2025-08-20T00:00:00Z", "checkCount": 0, "id": "7510e479-9341-48a7-85ad-a6e93139abd3", "companyId": "00219958", "type": "payperiod", "_rid": "NmJkAKiCbEezeAQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEezeAQAAAAAAA==/", "_etag": "\"a800d3c5-0000-0100-0000-687040f20000\"", "_attachments": "attachments/", "_ts": 1752187122}, {"payPeriodId": "1070079240995118", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-08-17T00:00:00Z", "endDate": "2025-08-30T00:00:00Z", "submitByDate": "2025-08-29T00:00:00Z", "checkDate": "2025-09-03T00:00:00Z", "checkCount": 0, "id": "33746290-0faa-4775-89cc-d0f24a97cd5b", "companyId": "00219958", "type": "payperiod", "_rid": "NmJkAKiCbEe0eAQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEe0eAQAAAAAAA==/", "_etag": "\"a800d5c5-0000-0100-0000-687040f20000\"", "_attachments": "attachments/", "_ts": 1752187122}, {"payPeriodId": "1070079504947176", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-08-31T00:00:00Z", "endDate": "2025-09-13T00:00:00Z", "submitByDate": "2025-09-15T00:00:00Z", "checkDate": "2025-09-17T00:00:00Z", "checkCount": 0, "id": "7d20e44c-cf1a-4a53-bca3-726e3c24d86a", "companyId": "00219958", "type": "payperiod", "_rid": "NmJkAKiCbEe1eAQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEe1eAQAAAAAAA==/", "_etag": "\"a800d6c5-0000-0100-0000-687040f20000\"", "_attachments": "attachments/", "_ts": 1752187122}, {"payPeriodId": "1070079760162079", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-09-14T00:00:00Z", "endDate": "2025-09-27T00:00:00Z", "submitByDate": "2025-09-29T00:00:00Z", "checkDate": "2025-10-01T00:00:00Z", "checkCount": 0, "id": "1a146c41-a1d8-43bd-a209-59a9e9cbfed2", "companyId": "00219958", "type": "payperiod", "_rid": "NmJkAKiCbEe2eAQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEe2eAQAAAAAAA==/", "_etag": "\"a800d7c5-0000-0100-0000-687040f20000\"", "_attachments": "attachments/", "_ts": 1752187122}, {"payPeriodId": "1070079920245023", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-09-28T00:00:00Z", "endDate": "2025-10-11T00:00:00Z", "submitByDate": "2025-10-13T00:00:00Z", "checkDate": "2025-10-15T00:00:00Z", "checkCount": 0, "id": "45133bb9-5ea9-44e4-a073-1fb5875047e5", "companyId": "00219958", "type": "payperiod", "_rid": "NmJkAKiCbEe3eAQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEe3eAQAAAAAAA==/", "_etag": "\"a800dbc5-0000-0100-0000-687040f20000\"", "_attachments": "attachments/", "_ts": 1752187122}, {"payPeriodId": "1070074739662782", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2024-12-22T00:00:00Z", "endDate": "2025-01-04T00:00:00Z", "submitByDate": "2025-01-06T00:00:00Z", "checkDate": "2025-01-08T00:00:00Z", "checkCount": 2, "id": "a7ab56fb-34d5-459d-a383-286b84d4c396", "companyId": "00219958", "type": "payperiod", "_rid": "NmJkAKiCbEe9eAQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEe9eAQAAAAAAA==/", "_etag": "\"a800e7c5-0000-0100-0000-687040f30000\"", "_attachments": "attachments/", "_ts": 1752187123}, {"payPeriodId": "1070075014419225", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-01-05T00:00:00Z", "endDate": "2025-01-18T00:00:00Z", "submitByDate": "2025-01-20T00:00:00Z", "checkDate": "2025-01-22T00:00:00Z", "checkCount": 2, "id": "1bfd485d-110c-4d61-abc8-fc2fd83f825f", "companyId": "00219958", "type": "payperiod", "_rid": "NmJkAKiCbEe+eAQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEe+eAQAAAAAAA==/", "_etag": "\"a800e8c5-0000-0100-0000-687040f30000\"", "_attachments": "attachments/", "_ts": 1752187123}, {"payPeriodId": "1070075275999389", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-01-19T00:00:00Z", "endDate": "2025-02-01T00:00:00Z", "submitByDate": "2025-02-03T00:00:00Z", "checkDate": "2025-02-05T00:00:00Z", "checkCount": 2, "id": "542cd8df-1391-4f93-a2d1-88c0f7960a54", "companyId": "00219958", "type": "payperiod", "_rid": "NmJkAKiCbEe-eAQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEe-eAQAAAAAAA==/", "_etag": "\"a800e9c5-0000-0100-0000-687040f30000\"", "_attachments": "attachments/", "_ts": 1752187123}, {"payPeriodId": "1070075543580044", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-02-02T00:00:00Z", "endDate": "2025-02-15T00:00:00Z", "submitByDate": "2025-02-17T00:00:00Z", "checkDate": "2025-02-19T00:00:00Z", "checkCount": 2, "id": "f056d9f5-6fa6-4ceb-a4e0-a8f50d6aaf20", "companyId": "00219958", "type": "payperiod", "_rid": "NmJkAKiCbEfAeAQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfAeAQAAAAAAA==/", "_etag": "\"a800ebc5-0000-0100-0000-687040f30000\"", "_attachments": "attachments/", "_ts": 1752187123}, {"payPeriodId": "1070075808130152", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-02-16T00:00:00Z", "endDate": "2025-03-01T00:00:00Z", "submitByDate": "2025-03-03T00:00:00Z", "checkDate": "2025-03-05T00:00:00Z", "checkCount": 2, "id": "7d8f7333-8836-4e06-9d41-15ca5435189a", "companyId": "00219958", "type": "payperiod", "_rid": "NmJkAKiCbEfBeAQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfBeAQAAAAAAA==/", "_etag": "\"a800edc5-0000-0100-0000-687040f30000\"", "_attachments": "attachments/", "_ts": 1752187123}, {"payPeriodId": "1070076067945114", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-03-02T00:00:00Z", "endDate": "2025-03-15T00:00:00Z", "submitByDate": "2025-03-17T00:00:00Z", "checkDate": "2025-03-19T00:00:00Z", "checkCount": 2, "id": "668644f8-b1fe-4365-9d97-a5b3170d8ea3", "companyId": "00219958", "type": "payperiod", "_rid": "NmJkAKiCbEfCeAQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfCeAQAAAAAAA==/", "_etag": "\"a800eec5-0000-0100-0000-687040f30000\"", "_attachments": "attachments/", "_ts": 1752187123}, {"payPeriodId": "1070076345588476", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-03-16T00:00:00Z", "endDate": "2025-03-29T00:00:00Z", "submitByDate": "2025-03-31T00:00:00Z", "checkDate": "2025-04-02T00:00:00Z", "checkCount": 2, "id": "a7665987-0a07-48dd-a823-cd76bf2b5ec4", "companyId": "00219958", "type": "payperiod", "_rid": "NmJkAKiCbEfDeAQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfDeAQAAAAAAA==/", "_etag": "\"a800efc5-0000-0100-0000-687040f30000\"", "_attachments": "attachments/", "_ts": 1752187123}, {"payPeriodId": "1070076590024336", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-03-30T00:00:00Z", "endDate": "2025-04-12T00:00:00Z", "submitByDate": "2025-04-14T00:00:00Z", "checkDate": "2025-04-16T00:00:00Z", "checkCount": 2, "id": "c2353782-4d64-4403-866b-0030df80a1ed", "companyId": "00219958", "type": "payperiod", "_rid": "NmJkAKiCbEfEeAQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfEeAQAAAAAAA==/", "_etag": "\"a800f1c5-0000-0100-0000-687040f40000\"", "_attachments": "attachments/", "_ts": 1752187124}, {"payPeriodId": "1070076872641109", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-04-13T00:00:00Z", "endDate": "2025-04-26T00:00:00Z", "submitByDate": "2025-04-28T00:00:00Z", "checkDate": "2025-04-30T00:00:00Z", "checkCount": 2, "id": "8b26f497-2518-4490-a3f0-12520d01a563", "companyId": "00219958", "type": "payperiod", "_rid": "NmJkAKiCbEfFeAQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfFeAQAAAAAAA==/", "_etag": "\"a800f2c5-0000-0100-0000-687040f40000\"", "_attachments": "attachments/", "_ts": 1752187124}, {"payPeriodId": "1070077119676473", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-04-27T00:00:00Z", "endDate": "2025-05-10T00:00:00Z", "submitByDate": "2025-05-12T00:00:00Z", "checkDate": "2025-05-14T00:00:00Z", "checkCount": 2, "id": "8bf0e5e5-3b85-42ec-bb31-3946b50c679d", "companyId": "00219958", "type": "payperiod", "_rid": "NmJkAKiCbEfGeAQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfGeAQAAAAAAA==/", "_etag": "\"a800f3c5-0000-0100-0000-687040f40000\"", "_attachments": "attachments/", "_ts": 1752187124}, {"payPeriodId": "1070077386271512", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-05-11T00:00:00Z", "endDate": "2025-05-24T00:00:00Z", "submitByDate": "2025-05-23T00:00:00Z", "checkDate": "2025-05-28T00:00:00Z", "checkCount": 2, "id": "45033017-dee0-46b3-8065-86083b780894", "companyId": "00219958", "type": "payperiod", "_rid": "NmJkAKiCbEfHeAQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfHeAQAAAAAAA==/", "_etag": "\"a800f4c5-0000-0100-0000-687040f40000\"", "_attachments": "attachments/", "_ts": 1752187124}, {"payPeriodId": "1070077650657477", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-05-25T00:00:00Z", "endDate": "2025-06-07T00:00:00Z", "submitByDate": "2025-06-09T00:00:00Z", "checkDate": "2025-06-11T00:00:00Z", "checkCount": 2, "id": "f3f05047-c533-4945-bed7-8d7c24f22536", "companyId": "00219958", "type": "payperiod", "_rid": "NmJkAKiCbEfIeAQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfIeAQAAAAAAA==/", "_etag": "\"a800f7c5-0000-0100-0000-687040f40000\"", "_attachments": "attachments/", "_ts": 1752187124}, {"payPeriodId": "1070077916709777", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-06-08T00:00:00Z", "endDate": "2025-06-21T00:00:00Z", "submitByDate": "2025-06-23T00:00:00Z", "checkDate": "2025-06-25T00:00:00Z", "checkCount": 2, "id": "c1070fe2-0735-45d7-80bd-eafbee67f7d6", "companyId": "00219958", "type": "payperiod", "_rid": "NmJkAKiCbEfJeAQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfJeAQAAAAAAA==/", "_etag": "\"a800f8c5-0000-0100-0000-687040f40000\"", "_attachments": "attachments/", "_ts": 1752187124}, {"payPeriodId": "1070078180887030", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-06-22T00:00:00Z", "endDate": "2025-07-05T00:00:00Z", "submitByDate": "2025-07-07T00:00:00Z", "checkDate": "2025-07-09T00:00:00Z", "checkCount": 0, "id": "bdbfd0a1-42fa-4c30-89b6-12fd0594b250", "companyId": "00219958", "type": "payperiod", "_rid": "NmJkAKiCbEfKeAQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfKeAQAAAAAAA==/", "_etag": "\"a800fbc5-0000-0100-0000-687040f40000\"", "_attachments": "attachments/", "_ts": 1752187124}, {"payPeriodId": "1070078450977244", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-07-06T00:00:00Z", "endDate": "2025-07-19T00:00:00Z", "submitByDate": "2025-07-21T00:00:00Z", "checkDate": "2025-07-23T00:00:00Z", "checkCount": 0, "id": "ee80ab5b-2389-407b-a598-d8e9b0c71039", "companyId": "00219958", "type": "payperiod", "_rid": "NmJkAKiCbEfLeAQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfLeAQAAAAAAA==/", "_etag": "\"a800ffc5-0000-0100-0000-687040f40000\"", "_attachments": "attachments/", "_ts": 1752187124}, {"payPeriodId": "1070078706753425", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-07-20T00:00:00Z", "endDate": "2025-08-02T00:00:00Z", "submitByDate": "2025-08-04T00:00:00Z", "checkDate": "2025-08-06T00:00:00Z", "checkCount": 0, "id": "54fa795d-302e-4ef5-b805-deb9bc34ddbf", "companyId": "00219958", "type": "payperiod", "_rid": "NmJkAKiCbEfMeAQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfMeAQAAAAAAA==/", "_etag": "\"a80000c6-0000-0100-0000-687040f40000\"", "_attachments": "attachments/", "_ts": 1752187124}, {"payPeriodId": "1070078978663312", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-08-03T00:00:00Z", "endDate": "2025-08-16T00:00:00Z", "submitByDate": "2025-08-18T00:00:00Z", "checkDate": "2025-08-20T00:00:00Z", "checkCount": 0, "id": "f1920c8f-38df-4574-9e7f-8fd4b6c6d305", "companyId": "00219958", "type": "payperiod", "_rid": "NmJkAKiCbEfNeAQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfNeAQAAAAAAA==/", "_etag": "\"a80002c6-0000-0100-0000-687040f40000\"", "_attachments": "attachments/", "_ts": 1752187124}, {"payPeriodId": "1070079240995118", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-08-17T00:00:00Z", "endDate": "2025-08-30T00:00:00Z", "submitByDate": "2025-08-29T00:00:00Z", "checkDate": "2025-09-03T00:00:00Z", "checkCount": 0, "id": "61977cd0-e10e-4162-9c9d-56e8a4e21789", "companyId": "00219958", "type": "payperiod", "_rid": "NmJkAKiCbEfOeAQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfOeAQAAAAAAA==/", "_etag": "\"a80004c6-0000-0100-0000-687040f40000\"", "_attachments": "attachments/", "_ts": 1752187124}, {"payPeriodId": "1070079504947176", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-08-31T00:00:00Z", "endDate": "2025-09-13T00:00:00Z", "submitByDate": "2025-09-15T00:00:00Z", "checkDate": "2025-09-17T00:00:00Z", "checkCount": 0, "id": "31c7c7ae-de92-4792-b768-ee2b5d26a916", "companyId": "00219958", "type": "payperiod", "_rid": "NmJkAKiCbEfPeAQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfPeAQAAAAAAA==/", "_etag": "\"a80007c6-0000-0100-0000-687040f50000\"", "_attachments": "attachments/", "_ts": 1752187125}, {"payPeriodId": "1070079760162079", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-09-14T00:00:00Z", "endDate": "2025-09-27T00:00:00Z", "submitByDate": "2025-09-29T00:00:00Z", "checkDate": "2025-10-01T00:00:00Z", "checkCount": 0, "id": "00a7c981-41c8-46b1-bd7a-76aea714b126", "companyId": "00219958", "type": "payperiod", "_rid": "NmJkAKiCbEfQeAQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfQeAQAAAAAAA==/", "_etag": "\"a8000ac6-0000-0100-0000-687040f50000\"", "_attachments": "attachments/", "_ts": 1752187125}, {"payPeriodId": "1070079920245023", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-09-28T00:00:00Z", "endDate": "2025-10-11T00:00:00Z", "submitByDate": "2025-10-13T00:00:00Z", "checkDate": "2025-10-15T00:00:00Z", "checkCount": 0, "id": "728050e9-6c29-4924-ba11-a3950ce3c86d", "companyId": "00219958", "type": "payperiod", "_rid": "NmJkAKiCbEfReAQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfReAQAAAAAAA==/", "_etag": "\"a8000dc6-0000-0100-0000-687040f50000\"", "_attachments": "attachments/", "_ts": 1752187125}], "links": [{"rel": "self", "href": "https://ca-payroll-ai-mock-sb-001-secure.nicebeach-8a7a52ab.eastus.azurecontainerapps.io/ca/companies/00219958/payperiods"}]}, "status_code": 200}