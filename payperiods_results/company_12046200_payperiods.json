{"success": true, "company_id": "12046200", "data": {"metadata": {"contentItemCount": 24}, "content": [{"payPeriodId": "1030068558075697", "intervalCode": "MONTHLY", "status": "COMPLETED", "description": "Monthly Payroll (6)", "startDate": "2024-12-07T00:00:00Z", "endDate": "2025-01-02T00:00:00Z", "submitByDate": "2025-01-05T00:00:00Z", "checkDate": "2025-01-06T00:00:00Z", "checkCount": 2, "id": "11a4ee3c-729d-464d-b554-2fbbbbcb1bf9", "companyId": "12046200", "type": "payperiod", "_rid": "NmJkAKiCbEdI6QQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdI6QQAAAAAAA==/", "_etag": "\"a90013c8-0000-0100-0000-68704ad70000\"", "_attachments": "attachments/", "_ts": 1752189655}, {"payPeriodId": "1030069117085596", "intervalCode": "MONTHLY", "status": "COMPLETED", "description": "Monthly Payroll (6)", "startDate": "2025-01-01T00:00:00Z", "endDate": "2025-01-31T00:00:00Z", "submitByDate": "2025-02-03T00:00:00Z", "checkDate": "2025-02-04T00:00:00Z", "checkCount": 2, "id": "03c628ce-f29e-4bce-854e-fc3a87604cb5", "companyId": "12046200", "type": "payperiod", "_rid": "NmJkAKiCbEdJ6QQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdJ6QQAAAAAAA==/", "_etag": "\"a90016c8-0000-0100-0000-68704ad80000\"", "_attachments": "attachments/", "_ts": 1752189656}, {"payPeriodId": "1030069790058273", "intervalCode": "MONTHLY", "status": "COMPLETED", "description": "Monthly Payroll (6)", "startDate": "2025-02-01T00:00:00Z", "endDate": "2025-02-28T00:00:00Z", "submitByDate": "2025-03-04T00:00:00Z", "checkDate": "2025-03-06T00:00:00Z", "checkCount": 2, "id": "8c32e3cd-8136-4e49-8f2d-924e234a577c", "companyId": "12046200", "type": "payperiod", "_rid": "NmJkAKiCbEdK6QQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdK6QQAAAAAAA==/", "_etag": "\"a90017c8-0000-0100-0000-68704ad80000\"", "_attachments": "attachments/", "_ts": 1752189656}, {"payPeriodId": "1030070336785410", "intervalCode": "MONTHLY", "status": "COMPLETED", "description": "Monthly Payroll (6)", "startDate": "2025-03-01T00:00:00Z", "endDate": "2025-03-27T00:00:00Z", "submitByDate": "2025-03-30T00:00:00Z", "checkDate": "2025-03-31T00:00:00Z", "checkCount": 2, "id": "e19d3f7a-134b-44f6-99ab-75f77f4169ce", "companyId": "12046200", "type": "payperiod", "_rid": "NmJkAKiCbEdL6QQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdL6QQAAAAAAA==/", "_etag": "\"a90019c8-0000-0100-0000-68704ad80000\"", "_attachments": "attachments/", "_ts": 1752189656}, {"payPeriodId": "1030070904175472", "intervalCode": "MONTHLY", "status": "INITIAL", "description": "Monthly Payroll (6)", "startDate": "2025-04-01T00:00:00Z", "endDate": "2025-04-30T00:00:00Z", "submitByDate": "2025-04-27T00:00:00Z", "checkDate": "2025-04-28T00:00:00Z", "checkCount": 0, "id": "2d98cbbc-d4c2-49ad-adce-47d47713a798", "companyId": "12046200", "type": "payperiod", "_rid": "NmJkAKiCbEdM6QQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdM6QQAAAAAAA==/", "_etag": "\"a9001dc8-0000-0100-0000-68704ad80000\"", "_attachments": "attachments/", "_ts": 1752189656}, {"payPeriodId": "1030073649647832", "status": "INITIAL", "description": "Monthly Payroll (6)", "startDate": "2025-05-01T00:00:00Z", "endDate": "2025-05-31T00:00:00Z", "submitByDate": "2025-05-25T00:00:00Z", "checkDate": "2025-05-27T00:00:00Z", "checkCount": 0, "id": "6b438c03-cee6-4ec2-92b9-93945be7f626", "companyId": "12046200", "type": "payperiod", "_rid": "NmJkAKiCbEdN6QQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdN6QQAAAAAAA==/", "_etag": "\"a90022c8-0000-0100-0000-68704ad80000\"", "_attachments": "attachments/", "_ts": 1752189656}, {"payPeriodId": "1030074205960917", "status": "INITIAL", "description": "Payroll", "startDate": "2025-05-24T00:00:00Z", "endDate": "2025-06-19T00:00:00Z", "submitByDate": "2025-06-22T00:00:00Z", "checkDate": "2025-06-23T00:00:00Z", "checkCount": 0, "id": "7194b72d-4147-424a-a10c-6721b7f3a0d5", "companyId": "12046200", "type": "payperiod", "_rid": "NmJkAKiCbEdO6QQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdO6QQAAAAAAA==/", "_etag": "\"a90024c8-0000-0100-0000-68704ad80000\"", "_attachments": "attachments/", "_ts": 1752189656}, {"payPeriodId": "1030072003806038", "intervalCode": "MONTHLY", "status": "INITIAL", "description": "Monthly Payroll (6)", "startDate": "2025-06-01T00:00:00Z", "endDate": "2025-06-30T00:00:00Z", "submitByDate": "2025-07-01T00:00:00Z", "checkDate": "2025-07-03T00:00:00Z", "checkCount": 0, "id": "82b06772-5ceb-4d46-a509-b6ee15d66bba", "companyId": "12046200", "type": "payperiod", "_rid": "NmJkAKiCbEdP6QQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdP6QQAAAAAAA==/", "_etag": "\"a90025c8-0000-0100-0000-68704ad80000\"", "_attachments": "attachments/", "_ts": 1752189656}, {"payPeriodId": "1030072695125301", "intervalCode": "MONTHLY", "status": "INITIAL", "description": "Monthly Payroll (6)", "startDate": "2025-07-01T00:00:00Z", "endDate": "2025-07-31T00:00:00Z", "submitByDate": "2025-08-05T00:00:00Z", "checkDate": "2025-08-07T00:00:00Z", "checkCount": 0, "id": "537d90a6-8b38-4d47-a4bc-e043a3e93d12", "companyId": "12046200", "type": "payperiod", "_rid": "NmJkAKiCbEdQ6QQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdQ6QQAAAAAAA==/", "_etag": "\"a90027c8-0000-0100-0000-68704ad80000\"", "_attachments": "attachments/", "_ts": 1752189656}, {"payPeriodId": "1030073380643493", "intervalCode": "MONTHLY", "status": "INITIAL", "description": "Monthly Payroll (6)", "startDate": "2025-08-01T00:00:00Z", "endDate": "2025-08-31T00:00:00Z", "submitByDate": "2025-09-02T00:00:00Z", "checkDate": "2025-09-04T00:00:00Z", "checkCount": 0, "id": "c39c6120-d17b-42d2-be9d-38d774d5856e", "companyId": "12046200", "type": "payperiod", "_rid": "NmJkAKiCbEdR6QQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdR6QQAAAAAAA==/", "_etag": "\"a90029c8-0000-0100-0000-68704ad80000\"", "_attachments": "attachments/", "_ts": 1752189656}, {"payPeriodId": "1030073940430428", "intervalCode": "MONTHLY", "status": "INITIAL", "description": "Monthly Payroll (6)", "startDate": "2025-09-01T00:00:00Z", "endDate": "2025-09-30T00:00:00Z", "submitByDate": "2025-09-30T00:00:00Z", "checkDate": "2025-10-02T00:00:00Z", "checkCount": 0, "id": "00b14982-10c7-4d78-9937-e1b1d9dd1668", "companyId": "12046200", "type": "payperiod", "_rid": "NmJkAKiCbEdS6QQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdS6QQAAAAAAA==/", "_etag": "\"a9002cc8-0000-0100-0000-68704ad80000\"", "_attachments": "attachments/", "_ts": 1752189656}, {"payPeriodId": "1030074500283031", "intervalCode": "MONTHLY", "status": "INITIAL", "description": "Monthly Payroll (6)", "startDate": "2025-10-01T00:00:00Z", "endDate": "2025-10-31T00:00:00Z", "submitByDate": "2025-11-04T00:00:00Z", "checkDate": "2025-11-06T00:00:00Z", "checkCount": 0, "id": "76917260-31ce-4937-badc-c7664eb3189c", "companyId": "12046200", "type": "payperiod", "_rid": "NmJkAKiCbEdT6QQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdT6QQAAAAAAA==/", "_etag": "\"a9002dc8-0000-0100-0000-68704ad80000\"", "_attachments": "attachments/", "_ts": 1752189656}, {"payPeriodId": "1030068558075697", "intervalCode": "MONTHLY", "status": "COMPLETED", "description": "Monthly Payroll (6)", "startDate": "2024-12-07T00:00:00Z", "endDate": "2025-01-02T00:00:00Z", "submitByDate": "2025-01-05T00:00:00Z", "checkDate": "2025-01-06T00:00:00Z", "checkCount": 2, "id": "bc36c480-7b0b-413c-87ca-d29724451f29", "companyId": "12046200", "type": "payperiod", "_rid": "NmJkAKiCbEdX6QQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdX6QQAAAAAAA==/", "_etag": "\"a90033c8-0000-0100-0000-68704ad90000\"", "_attachments": "attachments/", "_ts": 1752189657}, {"payPeriodId": "1030069117085596", "intervalCode": "MONTHLY", "status": "COMPLETED", "description": "Monthly Payroll (6)", "startDate": "2025-01-01T00:00:00Z", "endDate": "2025-01-31T00:00:00Z", "submitByDate": "2025-02-03T00:00:00Z", "checkDate": "2025-02-04T00:00:00Z", "checkCount": 2, "id": "f8d3d1c9-e236-4628-bad6-e0c9baa7b3ba", "companyId": "12046200", "type": "payperiod", "_rid": "NmJkAKiCbEdY6QQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdY6QQAAAAAAA==/", "_etag": "\"a90035c8-0000-0100-0000-68704ad90000\"", "_attachments": "attachments/", "_ts": 1752189657}, {"payPeriodId": "1030069790058273", "intervalCode": "MONTHLY", "status": "COMPLETED", "description": "Monthly Payroll (6)", "startDate": "2025-02-01T00:00:00Z", "endDate": "2025-02-28T00:00:00Z", "submitByDate": "2025-03-04T00:00:00Z", "checkDate": "2025-03-06T00:00:00Z", "checkCount": 2, "id": "28be93c1-477d-41f2-836f-65913d020bd9", "companyId": "12046200", "type": "payperiod", "_rid": "NmJkAKiCbEdZ6QQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdZ6QQAAAAAAA==/", "_etag": "\"a90036c8-0000-0100-0000-68704ad90000\"", "_attachments": "attachments/", "_ts": 1752189657}, {"payPeriodId": "1030070336785410", "intervalCode": "MONTHLY", "status": "COMPLETED", "description": "Monthly Payroll (6)", "startDate": "2025-03-01T00:00:00Z", "endDate": "2025-03-27T00:00:00Z", "submitByDate": "2025-03-30T00:00:00Z", "checkDate": "2025-03-31T00:00:00Z", "checkCount": 2, "id": "3ba48813-2205-474c-ac79-ea3da7b229a5", "companyId": "12046200", "type": "payperiod", "_rid": "NmJkAKiCbEda6QQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEda6QQAAAAAAA==/", "_etag": "\"a9003ac8-0000-0100-0000-68704ad90000\"", "_attachments": "attachments/", "_ts": 1752189657}, {"payPeriodId": "1030070904175472", "intervalCode": "MONTHLY", "status": "COMPLETED", "description": "Monthly Payroll (6)", "startDate": "2025-04-01T00:00:00Z", "endDate": "2025-04-30T00:00:00Z", "submitByDate": "2025-04-27T00:00:00Z", "checkDate": "2025-04-28T00:00:00Z", "checkCount": 2, "id": "fb5cb82b-1370-47f0-8ed5-919865aaa242", "companyId": "12046200", "type": "payperiod", "_rid": "NmJkAKiCbEdb6QQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdb6QQAAAAAAA==/", "_etag": "\"a9003bc8-0000-0100-0000-68704ad90000\"", "_attachments": "attachments/", "_ts": 1752189657}, {"payPeriodId": "1030073649647832", "status": "COMPLETED", "description": "Monthly Payroll (6)", "startDate": "2025-05-01T00:00:00Z", "endDate": "2025-05-31T00:00:00Z", "submitByDate": "2025-05-25T00:00:00Z", "checkDate": "2025-05-27T00:00:00Z", "checkCount": 2, "id": "9db9b508-8afa-4df1-86ce-c17dca5bb678", "companyId": "12046200", "type": "payperiod", "_rid": "NmJkAKiCbEdc6QQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdc6QQAAAAAAA==/", "_etag": "\"a9003dc8-0000-0100-0000-68704ad90000\"", "_attachments": "attachments/", "_ts": 1752189657}, {"payPeriodId": "1030074205960917", "status": "COMPLETED", "description": "Payroll", "startDate": "2025-05-24T00:00:00Z", "endDate": "2025-06-19T00:00:00Z", "submitByDate": "2025-06-22T00:00:00Z", "checkDate": "2025-06-23T00:00:00Z", "checkCount": 2, "id": "74508248-a413-4a30-9081-538302b820cc", "companyId": "12046200", "type": "payperiod", "_rid": "NmJkAKiCbEdd6QQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdd6QQAAAAAAA==/", "_etag": "\"a90041c8-0000-0100-0000-68704ad90000\"", "_attachments": "attachments/", "_ts": 1752189657}, {"payPeriodId": "1030072003806038", "intervalCode": "MONTHLY", "status": "INITIAL", "description": "Monthly Payroll (6)", "startDate": "2025-06-01T00:00:00Z", "endDate": "2025-06-30T00:00:00Z", "submitByDate": "2025-07-01T00:00:00Z", "checkDate": "2025-07-03T00:00:00Z", "checkCount": 0, "id": "869bbbc2-7fd7-4585-8259-73a911a217e7", "companyId": "12046200", "type": "payperiod", "_rid": "NmJkAKiCbEde6QQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEde6QQAAAAAAA==/", "_etag": "\"a90042c8-0000-0100-0000-68704ad90000\"", "_attachments": "attachments/", "_ts": 1752189657}, {"payPeriodId": "1030072695125301", "intervalCode": "MONTHLY", "status": "INITIAL", "description": "Monthly Payroll (6)", "startDate": "2025-07-01T00:00:00Z", "endDate": "2025-07-31T00:00:00Z", "submitByDate": "2025-08-05T00:00:00Z", "checkDate": "2025-08-07T00:00:00Z", "checkCount": 0, "id": "88f10c98-3ec5-48c4-ac50-bdb8984afb57", "companyId": "12046200", "type": "payperiod", "_rid": "NmJkAKiCbEdf6QQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdf6QQAAAAAAA==/", "_etag": "\"a90045c8-0000-0100-0000-68704ad90000\"", "_attachments": "attachments/", "_ts": 1752189657}, {"payPeriodId": "1030073380643493", "intervalCode": "MONTHLY", "status": "INITIAL", "description": "Monthly Payroll (6)", "startDate": "2025-08-01T00:00:00Z", "endDate": "2025-08-31T00:00:00Z", "submitByDate": "2025-09-02T00:00:00Z", "checkDate": "2025-09-04T00:00:00Z", "checkCount": 0, "id": "d5d57825-4c04-48cc-a7b1-028113d79bde", "companyId": "12046200", "type": "payperiod", "_rid": "NmJkAKiCbEdg6QQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdg6QQAAAAAAA==/", "_etag": "\"a90048c8-0000-0100-0000-68704ad90000\"", "_attachments": "attachments/", "_ts": 1752189657}, {"payPeriodId": "1030073940430428", "intervalCode": "MONTHLY", "status": "INITIAL", "description": "Monthly Payroll (6)", "startDate": "2025-09-01T00:00:00Z", "endDate": "2025-09-30T00:00:00Z", "submitByDate": "2025-09-30T00:00:00Z", "checkDate": "2025-10-02T00:00:00Z", "checkCount": 0, "id": "6c471487-397a-4865-9916-760b1077aa9e", "companyId": "12046200", "type": "payperiod", "_rid": "NmJkAKiCbEdh6QQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdh6QQAAAAAAA==/", "_etag": "\"a9004bc8-0000-0100-0000-68704ad90000\"", "_attachments": "attachments/", "_ts": 1752189657}, {"payPeriodId": "1030074500283031", "intervalCode": "MONTHLY", "status": "INITIAL", "description": "Monthly Payroll (6)", "startDate": "2025-10-01T00:00:00Z", "endDate": "2025-10-31T00:00:00Z", "submitByDate": "2025-11-04T00:00:00Z", "checkDate": "2025-11-06T00:00:00Z", "checkCount": 0, "id": "ee6f06cf-4d6a-4bc4-a3d1-f7c303c6bd42", "companyId": "12046200", "type": "payperiod", "_rid": "NmJkAKiCbEdi6QQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdi6QQAAAAAAA==/", "_etag": "\"a9004dc8-0000-0100-0000-68704ad90000\"", "_attachments": "attachments/", "_ts": 1752189657}], "links": [{"rel": "self", "href": "https://ca-payroll-ai-mock-sb-001-secure.nicebeach-8a7a52ab.eastus.azurecontainerapps.io/ca/companies/12046200/payperiods"}]}, "status_code": 200}