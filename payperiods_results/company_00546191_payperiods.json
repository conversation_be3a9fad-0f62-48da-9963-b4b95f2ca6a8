{"success": true, "company_id": "00546191", "data": {"metadata": {"contentItemCount": 44}, "content": [{"payPeriodId": "1030068542205147", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2024-12-19T00:00:00Z", "endDate": "2025-01-03T00:00:00Z", "submitByDate": "2025-01-08T00:00:00Z", "checkDate": "2025-01-10T00:00:00Z", "checkCount": 11, "id": "ec748a3a-fbcd-44d6-b590-a88e2ee34174", "companyId": "00546191", "type": "payperiod", "_rid": "NmJkAKiCbEeKoQMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeKoQMAAAAAAA==/", "_etag": "\"a60014b3-0000-0100-0000-68702f820000\"", "_attachments": "attachments/", "_ts": 1752182658}, {"payPeriodId": "1030069139637784", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-01-04T00:00:00Z", "endDate": "2025-01-18T00:00:00Z", "submitByDate": "2025-01-22T00:00:00Z", "checkDate": "2025-01-24T00:00:00Z", "checkCount": 12, "id": "0a92795b-c6e0-4c7c-ae54-f1c960691879", "companyId": "00546191", "type": "payperiod", "_rid": "NmJkAKiCbEeLoQMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeLoQMAAAAAAA==/", "_etag": "\"a60019b3-0000-0100-0000-68702f820000\"", "_attachments": "attachments/", "_ts": 1752182658}, {"payPeriodId": "1030071511942626", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-01-19T00:00:00Z", "endDate": "2025-02-03T00:00:00Z", "submitByDate": "2025-02-06T00:00:00Z", "checkDate": "2025-02-07T00:00:00Z", "checkCount": 12, "id": "2b68de26-d5e6-4c71-bb8d-79dd21cfa815", "companyId": "00546191", "type": "payperiod", "_rid": "NmJkAKiCbEeMoQMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeMoQMAAAAAAA==/", "_etag": "\"a6001bb3-0000-0100-0000-68702f820000\"", "_attachments": "attachments/", "_ts": 1752182658}, {"payPeriodId": "1030069944868504", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-02-04T00:00:00Z", "endDate": "2025-02-18T00:00:00Z", "submitByDate": "2025-02-21T00:00:00Z", "checkDate": "2025-02-25T00:00:00Z", "checkCount": 11, "id": "42a9a77f-fc83-4ba0-ba8d-a798c67a0334", "companyId": "00546191", "type": "payperiod", "_rid": "NmJkAKiCbEeNoQMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeNoQMAAAAAAA==/", "_etag": "\"a6001cb3-0000-0100-0000-68702f820000\"", "_attachments": "attachments/", "_ts": 1752182658}, {"payPeriodId": "1030069944868505", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-02-19T00:00:00Z", "endDate": "2025-03-03T00:00:00Z", "submitByDate": "2025-03-06T00:00:00Z", "checkDate": "2025-03-10T00:00:00Z", "checkCount": 11, "id": "57285b86-a610-4fa7-ae13-620b204d59d5", "companyId": "00546191", "type": "payperiod", "_rid": "NmJkAKiCbEeOoQMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeOoQMAAAAAAA==/", "_etag": "\"a6001eb3-0000-0100-0000-68702f820000\"", "_attachments": "attachments/", "_ts": 1752182658}, {"payPeriodId": "1030070275932509", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-03-04T00:00:00Z", "endDate": "2025-03-18T00:00:00Z", "submitByDate": "2025-03-21T00:00:00Z", "checkDate": "2025-03-25T00:00:00Z", "checkCount": 11, "id": "b1c44c0d-f961-438c-939c-a4375f02307b", "companyId": "00546191", "type": "payperiod", "_rid": "NmJkAKiCbEePoQMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEePoQMAAAAAAA==/", "_etag": "\"a60021b3-0000-0100-0000-68702f820000\"", "_attachments": "attachments/", "_ts": 1752182658}, {"payPeriodId": "1030070275932510", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-03-19T00:00:00Z", "endDate": "2025-04-03T00:00:00Z", "submitByDate": "2025-04-08T00:00:00Z", "checkDate": "2025-04-10T00:00:00Z", "checkCount": 0, "id": "f9dd15cb-d2d0-4929-9536-f76425785eab", "companyId": "00546191", "type": "payperiod", "_rid": "NmJkAKiCbEeQoQMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeQoQMAAAAAAA==/", "_etag": "\"a60022b3-0000-0100-0000-68702f820000\"", "_attachments": "attachments/", "_ts": 1752182658}, {"payPeriodId": "1030070940569657", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-04-04T00:00:00Z", "endDate": "2025-04-18T00:00:00Z", "submitByDate": "2025-04-23T00:00:00Z", "checkDate": "2025-04-25T00:00:00Z", "checkCount": 0, "id": "e97daa75-54fe-4703-891c-e7475b5d1561", "companyId": "00546191", "type": "payperiod", "_rid": "NmJkAKiCbEeRoQMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeRoQMAAAAAAA==/", "_etag": "\"a60024b3-0000-0100-0000-68702f820000\"", "_attachments": "attachments/", "_ts": 1752182658}, {"payPeriodId": "1030070940569658", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-04-19T00:00:00Z", "endDate": "2025-05-03T00:00:00Z", "submitByDate": "2025-05-07T00:00:00Z", "checkDate": "2025-05-09T00:00:00Z", "checkCount": 0, "id": "0f8946ac-7524-49b9-a9a2-2b6caaf893b3", "companyId": "00546191", "type": "payperiod", "_rid": "NmJkAKiCbEeSoQMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeSoQMAAAAAAA==/", "_etag": "\"a60026b3-0000-0100-0000-68702f820000\"", "_attachments": "attachments/", "_ts": 1752182658}, {"payPeriodId": "1030073269464095", "status": "INITIAL", "description": "GS error", "startDate": "2025-04-19T00:00:00Z", "endDate": "2025-05-03T00:00:00Z", "submitByDate": "2025-05-08T00:00:00Z", "checkDate": "2025-05-09T00:00:00Z", "checkCount": 0, "id": "0fae1259-0bc6-46c0-9367-c52dff6ae35b", "companyId": "00546191", "type": "payperiod", "_rid": "NmJkAKiCbEeToQMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeToQMAAAAAAA==/", "_etag": "\"a60028b3-0000-0100-0000-68702f830000\"", "_attachments": "attachments/", "_ts": 1752182659}, {"payPeriodId": "1030071752244329", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-05-04T00:00:00Z", "endDate": "2025-05-18T00:00:00Z", "submitByDate": "2025-05-21T00:00:00Z", "checkDate": "2025-05-23T00:00:00Z", "checkCount": 0, "id": "f0c459bf-9a86-45af-86d1-09f46136da7f", "companyId": "00546191", "type": "payperiod", "_rid": "NmJkAKiCbEeUoQMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeUoQMAAAAAAA==/", "_etag": "\"a6002bb3-0000-0100-0000-68702f830000\"", "_attachments": "attachments/", "_ts": 1752182659}, {"payPeriodId": "1030071752244330", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-05-19T00:00:00Z", "endDate": "2025-06-03T00:00:00Z", "submitByDate": "2025-06-06T00:00:00Z", "checkDate": "2025-06-10T00:00:00Z", "checkCount": 0, "id": "acdf4a7d-5385-4ce1-a2a5-ce9534791930", "companyId": "00546191", "type": "payperiod", "_rid": "NmJkAKiCbEeVoQMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeVoQMAAAAAAA==/", "_etag": "\"a6002db3-0000-0100-0000-68702f830000\"", "_attachments": "attachments/", "_ts": 1752182659}, {"payPeriodId": "1030072057931394", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-06-04T00:00:00Z", "endDate": "2025-06-18T00:00:00Z", "submitByDate": "2025-06-23T00:00:00Z", "checkDate": "2025-06-25T00:00:00Z", "checkCount": 0, "id": "d624a8e9-14d9-4d2f-b1d3-54938ecfdf8e", "companyId": "00546191", "type": "payperiod", "_rid": "NmJkAKiCbEeWoQMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeWoQMAAAAAAA==/", "_etag": "\"a60030b3-0000-0100-0000-68702f830000\"", "_attachments": "attachments/", "_ts": 1752182659}, {"payPeriodId": "1030072057931395", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-06-19T00:00:00Z", "endDate": "2025-07-03T00:00:00Z", "submitByDate": "2025-07-08T00:00:00Z", "checkDate": "2025-07-10T00:00:00Z", "checkCount": 0, "id": "b094931b-1223-48fc-828d-ad03d2b3fb75", "companyId": "00546191", "type": "payperiod", "_rid": "NmJkAKiCbEeXoQMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeXoQMAAAAAAA==/", "_etag": "\"a60033b3-0000-0100-0000-68702f830000\"", "_attachments": "attachments/", "_ts": 1752182659}, {"payPeriodId": "1030072679606825", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-07-04T00:00:00Z", "endDate": "2025-07-18T00:00:00Z", "submitByDate": "2025-07-23T00:00:00Z", "checkDate": "2025-07-25T00:00:00Z", "checkCount": 0, "id": "2b36fd36-1191-4f67-8b1a-c1d1cca37781", "companyId": "00546191", "type": "payperiod", "_rid": "NmJkAKiCbEeYoQMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeYoQMAAAAAAA==/", "_etag": "\"a60034b3-0000-0100-0000-68702f830000\"", "_attachments": "attachments/", "_ts": 1752182659}, {"payPeriodId": "1030072679606826", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-07-19T00:00:00Z", "endDate": "2025-08-03T00:00:00Z", "submitByDate": "2025-08-06T00:00:00Z", "checkDate": "2025-08-08T00:00:00Z", "checkCount": 0, "id": "ae2f4d3a-cb68-4062-b5d5-4c966706bc8e", "companyId": "00546191", "type": "payperiod", "_rid": "NmJkAKiCbEeZoQMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeZoQMAAAAAAA==/", "_etag": "\"a60037b3-0000-0100-0000-68702f830000\"", "_attachments": "attachments/", "_ts": 1752182659}, {"payPeriodId": "1030073263681067", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-08-04T00:00:00Z", "endDate": "2025-08-18T00:00:00Z", "submitByDate": "2025-08-21T00:00:00Z", "checkDate": "2025-08-25T00:00:00Z", "checkCount": 0, "id": "c786e457-ba84-4cf0-8350-85166473fa39", "companyId": "00546191", "type": "payperiod", "_rid": "NmJkAKiCbEeaoQMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeaoQMAAAAAAA==/", "_etag": "\"a6003cb3-0000-0100-0000-68702f830000\"", "_attachments": "attachments/", "_ts": 1752182659}, {"payPeriodId": "1030073263681068", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-08-19T00:00:00Z", "endDate": "2025-09-03T00:00:00Z", "submitByDate": "2025-09-08T00:00:00Z", "checkDate": "2025-09-10T00:00:00Z", "checkCount": 0, "id": "c85d0b7d-52fa-4eea-8215-98356bfab718", "companyId": "00546191", "type": "payperiod", "_rid": "NmJkAKiCbEeboQMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeboQMAAAAAAA==/", "_etag": "\"a60043b3-0000-0100-0000-68702f830000\"", "_attachments": "attachments/", "_ts": 1752182659}, {"payPeriodId": "1030073843577758", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-09-04T00:00:00Z", "endDate": "2025-09-18T00:00:00Z", "submitByDate": "2025-09-23T00:00:00Z", "checkDate": "2025-09-25T00:00:00Z", "checkCount": 0, "id": "3d3d5c35-2f8c-4e47-a1c6-c2e21466704e", "companyId": "00546191", "type": "payperiod", "_rid": "NmJkAKiCbEecoQMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEecoQMAAAAAAA==/", "_etag": "\"a60044b3-0000-0100-0000-68702f830000\"", "_attachments": "attachments/", "_ts": 1752182659}, {"payPeriodId": "1030073843577759", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-09-19T00:00:00Z", "endDate": "2025-10-03T00:00:00Z", "submitByDate": "2025-10-08T00:00:00Z", "checkDate": "2025-10-10T00:00:00Z", "checkCount": 0, "id": "1eb3150a-4bfd-480d-8224-7eb5acfcb5cb", "companyId": "00546191", "type": "payperiod", "_rid": "NmJkAKiCbEedoQMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEedoQMAAAAAAA==/", "_etag": "\"a60048b3-0000-0100-0000-68702f830000\"", "_attachments": "attachments/", "_ts": 1752182659}, {"payPeriodId": "1030074505009993", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-10-04T00:00:00Z", "endDate": "2025-10-18T00:00:00Z", "submitByDate": "2025-10-22T00:00:00Z", "checkDate": "2025-10-24T00:00:00Z", "checkCount": 0, "id": "2eb6bfb7-8c4a-4857-a88c-dee7b2fd2b44", "companyId": "00546191", "type": "payperiod", "_rid": "NmJkAKiCbEeeoQMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeeoQMAAAAAAA==/", "_etag": "\"a6004cb3-0000-0100-0000-68702f830000\"", "_attachments": "attachments/", "_ts": 1752182659}, {"payPeriodId": "1030074505009994", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-10-19T00:00:00Z", "endDate": "2025-11-03T00:00:00Z", "submitByDate": "2025-11-06T00:00:00Z", "checkDate": "2025-11-10T00:00:00Z", "checkCount": 0, "id": "f5c13275-0a4d-43b1-bf5a-495af4996bbb", "companyId": "00546191", "type": "payperiod", "_rid": "NmJkAKiCbEefoQMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEefoQMAAAAAAA==/", "_etag": "\"a6004db3-0000-0100-0000-68702f830000\"", "_attachments": "attachments/", "_ts": 1752182659}, {"payPeriodId": "1030068542205147", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2024-12-19T00:00:00Z", "endDate": "2025-01-03T00:00:00Z", "submitByDate": "2025-01-08T00:00:00Z", "checkDate": "2025-01-10T00:00:00Z", "checkCount": 11, "id": "459322a4-5c3b-44ac-8fe9-bffdea493a58", "companyId": "00546191", "type": "payperiod", "_rid": "NmJkAKiCbEfPoQMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfPoQMAAAAAAA==/", "_etag": "\"a600b9b3-0000-0100-0000-68702f870000\"", "_attachments": "attachments/", "_ts": 1752182663}, {"payPeriodId": "1030069139637784", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-01-04T00:00:00Z", "endDate": "2025-01-18T00:00:00Z", "submitByDate": "2025-01-22T00:00:00Z", "checkDate": "2025-01-24T00:00:00Z", "checkCount": 12, "id": "4b260654-caf3-474c-9ed7-253d6a0ef030", "companyId": "00546191", "type": "payperiod", "_rid": "NmJkAKiCbEfQoQMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfQoQMAAAAAAA==/", "_etag": "\"a600bbb3-0000-0100-0000-68702f870000\"", "_attachments": "attachments/", "_ts": 1752182663}, {"payPeriodId": "1030071511942626", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-01-19T00:00:00Z", "endDate": "2025-02-03T00:00:00Z", "submitByDate": "2025-02-06T00:00:00Z", "checkDate": "2025-02-07T00:00:00Z", "checkCount": 12, "id": "66f47a66-314c-4fc6-a195-123589aa34ab", "companyId": "00546191", "type": "payperiod", "_rid": "NmJkAKiCbEfRoQMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfRoQMAAAAAAA==/", "_etag": "\"a600bcb3-0000-0100-0000-68702f880000\"", "_attachments": "attachments/", "_ts": 1752182664}, {"payPeriodId": "1030069944868504", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-02-04T00:00:00Z", "endDate": "2025-02-18T00:00:00Z", "submitByDate": "2025-02-21T00:00:00Z", "checkDate": "2025-02-25T00:00:00Z", "checkCount": 11, "id": "fbb038d5-352f-49f7-a058-535e69a3cac6", "companyId": "00546191", "type": "payperiod", "_rid": "NmJkAKiCbEfSoQMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfSoQMAAAAAAA==/", "_etag": "\"a600beb3-0000-0100-0000-68702f880000\"", "_attachments": "attachments/", "_ts": 1752182664}, {"payPeriodId": "1030069944868505", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-02-19T00:00:00Z", "endDate": "2025-03-03T00:00:00Z", "submitByDate": "2025-03-06T00:00:00Z", "checkDate": "2025-03-10T00:00:00Z", "checkCount": 11, "id": "9daf66ad-ad9d-46d1-b946-e7d380b506cd", "companyId": "00546191", "type": "payperiod", "_rid": "NmJkAKiCbEfToQMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfToQMAAAAAAA==/", "_etag": "\"a600c0b3-0000-0100-0000-68702f880000\"", "_attachments": "attachments/", "_ts": 1752182664}, {"payPeriodId": "1030070275932509", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-03-04T00:00:00Z", "endDate": "2025-03-18T00:00:00Z", "submitByDate": "2025-03-21T00:00:00Z", "checkDate": "2025-03-25T00:00:00Z", "checkCount": 11, "id": "3563fef5-6be7-4435-877c-a3a764f161bf", "companyId": "00546191", "type": "payperiod", "_rid": "NmJkAKiCbEfUoQMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfUoQMAAAAAAA==/", "_etag": "\"a600c1b3-0000-0100-0000-68702f880000\"", "_attachments": "attachments/", "_ts": 1752182664}, {"payPeriodId": "1030070275932510", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-03-19T00:00:00Z", "endDate": "2025-04-03T00:00:00Z", "submitByDate": "2025-04-08T00:00:00Z", "checkDate": "2025-04-10T00:00:00Z", "checkCount": 11, "id": "e4b52f08-525b-4a86-a07a-841049b40a25", "companyId": "00546191", "type": "payperiod", "_rid": "NmJkAKiCbEfVoQMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfVoQMAAAAAAA==/", "_etag": "\"a600c3b3-0000-0100-0000-68702f880000\"", "_attachments": "attachments/", "_ts": 1752182664}, {"payPeriodId": "1030070940569657", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-04-04T00:00:00Z", "endDate": "2025-04-18T00:00:00Z", "submitByDate": "2025-04-23T00:00:00Z", "checkDate": "2025-04-25T00:00:00Z", "checkCount": 11, "id": "68a6bfc8-da70-4dbc-905b-3721d28fcc0d", "companyId": "00546191", "type": "payperiod", "_rid": "NmJkAKiCbEfWoQMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfWoQMAAAAAAA==/", "_etag": "\"a600c4b3-0000-0100-0000-68702f880000\"", "_attachments": "attachments/", "_ts": 1752182664}, {"payPeriodId": "1030070940569658", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-04-19T00:00:00Z", "endDate": "2025-05-03T00:00:00Z", "submitByDate": "2025-05-07T00:00:00Z", "checkDate": "2025-05-09T00:00:00Z", "checkCount": 11, "id": "1b2e6507-f54f-447e-9145-e7b53085a3f8", "companyId": "00546191", "type": "payperiod", "_rid": "NmJkAKiCbEfXoQMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfXoQMAAAAAAA==/", "_etag": "\"a600c5b3-0000-0100-0000-68702f880000\"", "_attachments": "attachments/", "_ts": 1752182664}, {"payPeriodId": "1030073269464095", "status": "COMPLETED", "description": "GS error", "startDate": "2025-04-19T00:00:00Z", "endDate": "2025-05-03T00:00:00Z", "submitByDate": "2025-05-08T00:00:00Z", "checkDate": "2025-05-09T00:00:00Z", "checkCount": 1, "id": "14b398f0-b062-4810-91b1-4b0bf698c839", "companyId": "00546191", "type": "payperiod", "_rid": "NmJkAKiCbEfYoQMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfYoQMAAAAAAA==/", "_etag": "\"a600c7b3-0000-0100-0000-68702f880000\"", "_attachments": "attachments/", "_ts": 1752182664}, {"payPeriodId": "1030071752244329", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-05-04T00:00:00Z", "endDate": "2025-05-18T00:00:00Z", "submitByDate": "2025-05-21T00:00:00Z", "checkDate": "2025-05-23T00:00:00Z", "checkCount": 12, "id": "45e4b7a1-c283-4e2d-9e65-4ef103f9f538", "companyId": "00546191", "type": "payperiod", "_rid": "NmJkAKiCbEfZoQMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfZoQMAAAAAAA==/", "_etag": "\"a600c9b3-0000-0100-0000-68702f880000\"", "_attachments": "attachments/", "_ts": 1752182664}, {"payPeriodId": "1030071752244330", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-05-19T00:00:00Z", "endDate": "2025-06-03T00:00:00Z", "submitByDate": "2025-06-06T00:00:00Z", "checkDate": "2025-06-10T00:00:00Z", "checkCount": 13, "id": "5941d478-e886-4a36-a212-a74eab366038", "companyId": "00546191", "type": "payperiod", "_rid": "NmJkAKiCbEfaoQMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfaoQMAAAAAAA==/", "_etag": "\"a600ccb3-0000-0100-0000-68702f880000\"", "_attachments": "attachments/", "_ts": 1752182664}, {"payPeriodId": "1030072057931394", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-06-04T00:00:00Z", "endDate": "2025-06-18T00:00:00Z", "submitByDate": "2025-06-23T00:00:00Z", "checkDate": "2025-06-25T00:00:00Z", "checkCount": 12, "id": "7c016a28-d510-4342-8db4-41a8db854fdd", "companyId": "00546191", "type": "payperiod", "_rid": "NmJkAKiCbEfboQMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfboQMAAAAAAA==/", "_etag": "\"a600d0b3-0000-0100-0000-68702f880000\"", "_attachments": "attachments/", "_ts": 1752182664}, {"payPeriodId": "1030072057931395", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-06-19T00:00:00Z", "endDate": "2025-07-03T00:00:00Z", "submitByDate": "2025-07-08T00:00:00Z", "checkDate": "2025-07-10T00:00:00Z", "checkCount": 12, "id": "411351d9-7131-4b21-b829-17fc24ce8707", "companyId": "00546191", "type": "payperiod", "_rid": "NmJkAKiCbEfcoQMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfcoQMAAAAAAA==/", "_etag": "\"a600d3b3-0000-0100-0000-68702f880000\"", "_attachments": "attachments/", "_ts": 1752182664}, {"payPeriodId": "1030072679606825", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-07-04T00:00:00Z", "endDate": "2025-07-18T00:00:00Z", "submitByDate": "2025-07-23T00:00:00Z", "checkDate": "2025-07-25T00:00:00Z", "checkCount": 0, "id": "ec0ad523-eb00-4d96-b1d2-8acf1fbedf7d", "companyId": "00546191", "type": "payperiod", "_rid": "NmJkAKiCbEfdoQMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfdoQMAAAAAAA==/", "_etag": "\"a600d5b3-0000-0100-0000-68702f880000\"", "_attachments": "attachments/", "_ts": 1752182664}, {"payPeriodId": "1030072679606826", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-07-19T00:00:00Z", "endDate": "2025-08-03T00:00:00Z", "submitByDate": "2025-08-06T00:00:00Z", "checkDate": "2025-08-08T00:00:00Z", "checkCount": 0, "id": "23c9d81c-6203-4717-afca-c80ea340c8be", "companyId": "00546191", "type": "payperiod", "_rid": "NmJkAKiCbEfeoQMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfeoQMAAAAAAA==/", "_etag": "\"a600d8b3-0000-0100-0000-68702f890000\"", "_attachments": "attachments/", "_ts": 1752182665}, {"payPeriodId": "1030073263681067", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-08-04T00:00:00Z", "endDate": "2025-08-18T00:00:00Z", "submitByDate": "2025-08-21T00:00:00Z", "checkDate": "2025-08-25T00:00:00Z", "checkCount": 0, "id": "e92275c9-94fe-4761-99f1-be0f77d4b25b", "companyId": "00546191", "type": "payperiod", "_rid": "NmJkAKiCbEffoQMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEffoQMAAAAAAA==/", "_etag": "\"a600dbb3-0000-0100-0000-68702f890000\"", "_attachments": "attachments/", "_ts": 1752182665}, {"payPeriodId": "1030073263681068", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-08-19T00:00:00Z", "endDate": "2025-09-03T00:00:00Z", "submitByDate": "2025-09-08T00:00:00Z", "checkDate": "2025-09-10T00:00:00Z", "checkCount": 0, "id": "20517d09-577b-4f40-9dc2-0af14474b447", "companyId": "00546191", "type": "payperiod", "_rid": "NmJkAKiCbEfgoQMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfgoQMAAAAAAA==/", "_etag": "\"a600ddb3-0000-0100-0000-68702f890000\"", "_attachments": "attachments/", "_ts": 1752182665}, {"payPeriodId": "1030073843577758", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-09-04T00:00:00Z", "endDate": "2025-09-18T00:00:00Z", "submitByDate": "2025-09-23T00:00:00Z", "checkDate": "2025-09-25T00:00:00Z", "checkCount": 0, "id": "0f698da0-8a95-4429-8691-0ade39626863", "companyId": "00546191", "type": "payperiod", "_rid": "NmJkAKiCbEfhoQMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfhoQMAAAAAAA==/", "_etag": "\"a600e0b3-0000-0100-0000-68702f890000\"", "_attachments": "attachments/", "_ts": 1752182665}, {"payPeriodId": "1030073843577759", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-09-19T00:00:00Z", "endDate": "2025-10-03T00:00:00Z", "submitByDate": "2025-10-08T00:00:00Z", "checkDate": "2025-10-10T00:00:00Z", "checkCount": 0, "id": "9ebd12e4-591d-477f-81ce-9a3bc0032d7d", "companyId": "00546191", "type": "payperiod", "_rid": "NmJkAKiCbEfioQMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfioQMAAAAAAA==/", "_etag": "\"a600e3b3-0000-0100-0000-68702f890000\"", "_attachments": "attachments/", "_ts": 1752182665}, {"payPeriodId": "1030074505009993", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-10-04T00:00:00Z", "endDate": "2025-10-18T00:00:00Z", "submitByDate": "2025-10-22T00:00:00Z", "checkDate": "2025-10-24T00:00:00Z", "checkCount": 0, "id": "54cab0e0-eb11-48ec-9648-d6c4c7d35df4", "companyId": "00546191", "type": "payperiod", "_rid": "NmJkAKiCbEfjoQMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfjoQMAAAAAAA==/", "_etag": "\"a600e6b3-0000-0100-0000-68702f890000\"", "_attachments": "attachments/", "_ts": 1752182665}, {"payPeriodId": "1030074505009994", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-10-19T00:00:00Z", "endDate": "2025-11-03T00:00:00Z", "submitByDate": "2025-11-06T00:00:00Z", "checkDate": "2025-11-10T00:00:00Z", "checkCount": 0, "id": "a5a54f8b-8339-40bc-aee6-1917068474d8", "companyId": "00546191", "type": "payperiod", "_rid": "NmJkAKiCbEfkoQMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfkoQMAAAAAAA==/", "_etag": "\"a600eab3-0000-0100-0000-68702f890000\"", "_attachments": "attachments/", "_ts": 1752182665}], "links": [{"rel": "self", "href": "https://ca-payroll-ai-mock-sb-001-secure.nicebeach-8a7a52ab.eastus.azurecontainerapps.io/ca/companies/00546191/payperiods"}]}, "status_code": 200}