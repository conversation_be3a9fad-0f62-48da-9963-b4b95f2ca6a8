{"success": true, "company_id": "0042D788", "data": {"metadata": {"contentItemCount": 42}, "content": [{"payPeriodId": "1090065943524862", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2024-12-26T00:00:00Z", "endDate": "2025-01-10T00:00:00Z", "submitByDate": "2025-01-13T00:00:00Z", "checkDate": "2025-01-15T00:00:00Z", "checkCount": 6, "id": "080c8fe4-adb0-475e-8f7c-a40214079e2f", "companyId": "0042D788", "type": "payperiod", "_rid": "NmJkAKiCbEfK6AQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfK6AQAAAAAAA==/", "_etag": "\"a90000c7-0000-0100-0000-68704acd0000\"", "_attachments": "attachments/", "_ts": 1752189645}, {"payPeriodId": "1090065943524863", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-01-11T00:00:00Z", "endDate": "2025-01-25T00:00:00Z", "submitByDate": "2025-01-29T00:00:00Z", "checkDate": "2025-01-31T00:00:00Z", "checkCount": 4, "id": "a89ee975-18c1-4b19-95c2-398fe2df1943", "companyId": "0042D788", "type": "payperiod", "_rid": "NmJkAKiCbEfL6AQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfL6AQAAAAAAA==/", "_etag": "\"a90001c7-0000-0100-0000-68704acd0000\"", "_attachments": "attachments/", "_ts": 1752189645}, {"payPeriodId": "1090066623996583", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-01-26T00:00:00Z", "endDate": "2025-02-10T00:00:00Z", "submitByDate": "2025-02-12T00:00:00Z", "checkDate": "2025-02-14T00:00:00Z", "checkCount": 5, "id": "a242df30-ade3-4949-af65-bac76d14318b", "companyId": "0042D788", "type": "payperiod", "_rid": "NmJkAKiCbEfM6AQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfM6AQAAAAAAA==/", "_etag": "\"a90002c7-0000-0100-0000-68704ace0000\"", "_attachments": "attachments/", "_ts": 1752189646}, {"payPeriodId": "1090066623996584", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-02-11T00:00:00Z", "endDate": "2025-02-25T00:00:00Z", "submitByDate": "2025-02-26T00:00:00Z", "checkDate": "2025-02-28T00:00:00Z", "checkCount": 5, "id": "3cf50b76-ca3a-49ca-82b3-31b90b21b19a", "companyId": "0042D788", "type": "payperiod", "_rid": "NmJkAKiCbEfN6AQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfN6AQAAAAAAA==/", "_etag": "\"a90004c7-0000-0100-0000-68704ace0000\"", "_attachments": "attachments/", "_ts": 1752189646}, {"payPeriodId": "1090067225161412", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-02-26T00:00:00Z", "endDate": "2025-03-10T00:00:00Z", "submitByDate": "2025-03-12T00:00:00Z", "checkDate": "2025-03-14T00:00:00Z", "checkCount": 4, "id": "bca1b2cb-5d9d-4482-887b-884750c7651a", "companyId": "0042D788", "type": "payperiod", "_rid": "NmJkAKiCbEfO6AQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfO6AQAAAAAAA==/", "_etag": "\"a90007c7-0000-0100-0000-68704ace0000\"", "_attachments": "attachments/", "_ts": 1752189646}, {"payPeriodId": "1090067225161413", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-03-11T00:00:00Z", "endDate": "2025-03-25T00:00:00Z", "submitByDate": "2025-03-27T00:00:00Z", "checkDate": "2025-03-31T00:00:00Z", "checkCount": 5, "id": "7ba548a2-7f31-4d76-af66-4bbdd3c2b5c1", "companyId": "0042D788", "type": "payperiod", "_rid": "NmJkAKiCbEfP6AQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfP6AQAAAAAAA==/", "_etag": "\"a90008c7-0000-0100-0000-68704ace0000\"", "_attachments": "attachments/", "_ts": 1752189646}, {"payPeriodId": "1090067964559193", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-03-26T00:00:00Z", "endDate": "2025-04-10T00:00:00Z", "submitByDate": "2025-04-11T00:00:00Z", "checkDate": "2025-04-15T00:00:00Z", "checkCount": 0, "id": "e3a5287b-0c70-46b8-9429-a4e1ba457199", "companyId": "0042D788", "type": "payperiod", "_rid": "NmJkAKiCbEfQ6AQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfQ6AQAAAAAAA==/", "_etag": "\"a9000cc7-0000-0100-0000-68704ace0000\"", "_attachments": "attachments/", "_ts": 1752189646}, {"payPeriodId": "1090067964559194", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-04-11T00:00:00Z", "endDate": "2025-04-25T00:00:00Z", "submitByDate": "2025-04-28T00:00:00Z", "checkDate": "2025-04-30T00:00:00Z", "checkCount": 0, "id": "8beeb3c3-44ff-4a7d-87d8-efcfaae66f23", "companyId": "0042D788", "type": "payperiod", "_rid": "NmJkAKiCbEfR6AQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfR6AQAAAAAAA==/", "_etag": "\"a9000ec7-0000-0100-0000-68704ace0000\"", "_attachments": "attachments/", "_ts": 1752189646}, {"payPeriodId": "1090068642479323", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-04-26T00:00:00Z", "endDate": "2025-05-10T00:00:00Z", "submitByDate": "2025-05-13T00:00:00Z", "checkDate": "2025-05-15T00:00:00Z", "checkCount": 0, "id": "3b6a22c9-5ff8-48d4-8cd9-94cfa834c030", "companyId": "0042D788", "type": "payperiod", "_rid": "NmJkAKiCbEfS6AQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfS6AQAAAAAAA==/", "_etag": "\"a90010c7-0000-0100-0000-68704ace0000\"", "_attachments": "attachments/", "_ts": 1752189646}, {"payPeriodId": "1090068642479324", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-05-11T00:00:00Z", "endDate": "2025-05-25T00:00:00Z", "submitByDate": "2025-05-28T00:00:00Z", "checkDate": "2025-05-30T00:00:00Z", "checkCount": 0, "id": "fa96d535-7abd-49fb-8f2e-d2b9a299f130", "companyId": "0042D788", "type": "payperiod", "_rid": "NmJkAKiCbEfT6AQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfT6AQAAAAAAA==/", "_etag": "\"a90013c7-0000-0100-0000-68704ace0000\"", "_attachments": "attachments/", "_ts": 1752189646}, {"payPeriodId": "1090069244501760", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-05-26T00:00:00Z", "endDate": "2025-06-10T00:00:00Z", "submitByDate": "2025-06-11T00:00:00Z", "checkDate": "2025-06-13T00:00:00Z", "checkCount": 0, "id": "8649528b-7fc0-409a-8745-b539d2c7e75c", "companyId": "0042D788", "type": "payperiod", "_rid": "NmJkAKiCbEfU6AQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfU6AQAAAAAAA==/", "_etag": "\"a90014c7-0000-0100-0000-68704ace0000\"", "_attachments": "attachments/", "_ts": 1752189646}, {"payPeriodId": "1090069244501761", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-06-11T00:00:00Z", "endDate": "2025-06-25T00:00:00Z", "submitByDate": "2025-06-26T00:00:00Z", "checkDate": "2025-06-30T00:00:00Z", "checkCount": 0, "id": "a504c0b1-9739-4fd7-9aad-440c1ef0e293", "companyId": "0042D788", "type": "payperiod", "_rid": "NmJkAKiCbEfV6AQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfV6AQAAAAAAA==/", "_etag": "\"a90017c7-0000-0100-0000-68704ace0000\"", "_attachments": "attachments/", "_ts": 1752189646}, {"payPeriodId": "1090069890471704", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-06-26T00:00:00Z", "endDate": "2025-07-10T00:00:00Z", "submitByDate": "2025-07-11T00:00:00Z", "checkDate": "2025-07-15T00:00:00Z", "checkCount": 0, "id": "c85f348a-c864-4e8d-8b25-7826f1f74e52", "companyId": "0042D788", "type": "payperiod", "_rid": "NmJkAKiCbEfW6AQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfW6AQAAAAAAA==/", "_etag": "\"a90019c7-0000-0100-0000-68704ace0000\"", "_attachments": "attachments/", "_ts": 1752189646}, {"payPeriodId": "1090069890471705", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-07-11T00:00:00Z", "endDate": "2025-07-25T00:00:00Z", "submitByDate": "2025-07-29T00:00:00Z", "checkDate": "2025-07-31T00:00:00Z", "checkCount": 0, "id": "818e48b0-01a4-4c95-bc83-accabec8dc6a", "companyId": "0042D788", "type": "payperiod", "_rid": "NmJkAKiCbEfX6AQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfX6AQAAAAAAA==/", "_etag": "\"a9001bc7-0000-0100-0000-68704ace0000\"", "_attachments": "attachments/", "_ts": 1752189646}, {"payPeriodId": "1090070578744087", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-07-26T00:00:00Z", "endDate": "2025-08-10T00:00:00Z", "submitByDate": "2025-08-13T00:00:00Z", "checkDate": "2025-08-15T00:00:00Z", "checkCount": 0, "id": "10f0e1d2-7c1a-4191-8289-321a5a5cce97", "companyId": "0042D788", "type": "payperiod", "_rid": "NmJkAKiCbEfY6AQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfY6AQAAAAAAA==/", "_etag": "\"a9001cc7-0000-0100-0000-68704ace0000\"", "_attachments": "attachments/", "_ts": 1752189646}, {"payPeriodId": "1090070578744088", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-08-11T00:00:00Z", "endDate": "2025-08-25T00:00:00Z", "submitByDate": "2025-08-27T00:00:00Z", "checkDate": "2025-08-29T00:00:00Z", "checkCount": 0, "id": "2581f5f7-186d-4efa-9511-32b0d5517ced", "companyId": "0042D788", "type": "payperiod", "_rid": "NmJkAKiCbEfZ6AQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfZ6AQAAAAAAA==/", "_etag": "\"a9001fc7-0000-0100-0000-68704acf0000\"", "_attachments": "attachments/", "_ts": 1752189647}, {"payPeriodId": "1090071218585305", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-08-26T00:00:00Z", "endDate": "2025-09-10T00:00:00Z", "submitByDate": "2025-09-11T00:00:00Z", "checkDate": "2025-09-15T00:00:00Z", "checkCount": 0, "id": "bd77b951-5a33-4cd6-90ad-2b8581f515e7", "companyId": "0042D788", "type": "payperiod", "_rid": "NmJkAKiCbEfa6AQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfa6AQAAAAAAA==/", "_etag": "\"a90020c7-0000-0100-0000-68704acf0000\"", "_attachments": "attachments/", "_ts": 1752189647}, {"payPeriodId": "1090071218585306", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-09-11T00:00:00Z", "endDate": "2025-09-25T00:00:00Z", "submitByDate": "2025-09-26T00:00:00Z", "checkDate": "2025-09-30T00:00:00Z", "checkCount": 0, "id": "c64274c1-44eb-49d9-8580-ef5e4107b279", "companyId": "0042D788", "type": "payperiod", "_rid": "NmJkAKiCbEfb6AQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfb6AQAAAAAAA==/", "_etag": "\"a90021c7-0000-0100-0000-68704acf0000\"", "_attachments": "attachments/", "_ts": 1752189647}, {"payPeriodId": "1090071914396512", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-09-26T00:00:00Z", "endDate": "2025-10-10T00:00:00Z", "submitByDate": "2025-10-13T00:00:00Z", "checkDate": "2025-10-15T00:00:00Z", "checkCount": 0, "id": "a1bc56fb-f8aa-44a7-b0b5-2f56a9548fae", "companyId": "0042D788", "type": "payperiod", "_rid": "NmJkAKiCbEfc6AQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfc6AQAAAAAAA==/", "_etag": "\"a90023c7-0000-0100-0000-68704acf0000\"", "_attachments": "attachments/", "_ts": 1752189647}, {"payPeriodId": "1090071914396513", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-10-11T00:00:00Z", "endDate": "2025-10-25T00:00:00Z", "submitByDate": "2025-10-29T00:00:00Z", "checkDate": "2025-10-31T00:00:00Z", "checkCount": 0, "id": "d38a96b4-87c1-408e-928e-0968c3757a9b", "companyId": "0042D788", "type": "payperiod", "_rid": "NmJkAKiCbEfd6AQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfd6AQAAAAAAA==/", "_etag": "\"a90025c7-0000-0100-0000-68704acf0000\"", "_attachments": "attachments/", "_ts": 1752189647}, {"payPeriodId": "1090071217599644", "status": "INITIAL", "description": "Void", "startDate": "2025-04-11T00:00:00Z", "endDate": "2025-04-25T00:00:00Z", "submitByDate": "2025-04-29T00:00:00Z", "checkDate": "2025-04-30T00:00:00Z", "checkCount": 0, "id": "e02d6fde-1007-41ab-ad85-d595b764cc26", "companyId": "0042D788", "type": "payperiod", "_rid": "NmJkAKiCbEfe6AQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfe6AQAAAAAAA==/", "_etag": "\"a90026c7-0000-0100-0000-68704acf0000\"", "_attachments": "attachments/", "_ts": 1752189647}, {"payPeriodId": "1090065943524862", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2024-12-26T00:00:00Z", "endDate": "2025-01-10T00:00:00Z", "submitByDate": "2025-01-13T00:00:00Z", "checkDate": "2025-01-15T00:00:00Z", "checkCount": 6, "id": "77d1b485-ee42-489c-a613-27637d76ef95", "companyId": "0042D788", "type": "payperiod", "_rid": "NmJkAKiCbEf56AQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf56AQAAAAAAA==/", "_etag": "\"a90056c7-0000-0100-0000-68704ad10000\"", "_attachments": "attachments/", "_ts": 1752189649}, {"payPeriodId": "1090065943524863", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-01-11T00:00:00Z", "endDate": "2025-01-25T00:00:00Z", "submitByDate": "2025-01-29T00:00:00Z", "checkDate": "2025-01-31T00:00:00Z", "checkCount": 4, "id": "da864175-93e0-4764-b951-dc92d72a7b5c", "companyId": "0042D788", "type": "payperiod", "_rid": "NmJkAKiCbEf66AQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf66AQAAAAAAA==/", "_etag": "\"a90059c7-0000-0100-0000-68704ad10000\"", "_attachments": "attachments/", "_ts": 1752189649}, {"payPeriodId": "1090066623996583", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-01-26T00:00:00Z", "endDate": "2025-02-10T00:00:00Z", "submitByDate": "2025-02-12T00:00:00Z", "checkDate": "2025-02-14T00:00:00Z", "checkCount": 5, "id": "795579b9-3347-43bb-94a3-79cf30e51453", "companyId": "0042D788", "type": "payperiod", "_rid": "NmJkAKiCbEf76AQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf76AQAAAAAAA==/", "_etag": "\"a9005ac7-0000-0100-0000-68704ad10000\"", "_attachments": "attachments/", "_ts": 1752189649}, {"payPeriodId": "1090066623996584", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-02-11T00:00:00Z", "endDate": "2025-02-25T00:00:00Z", "submitByDate": "2025-02-26T00:00:00Z", "checkDate": "2025-02-28T00:00:00Z", "checkCount": 5, "id": "ed9b58a2-4036-4391-a90f-d1d55ab6c48c", "companyId": "0042D788", "type": "payperiod", "_rid": "NmJkAKiCbEf86AQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf86AQAAAAAAA==/", "_etag": "\"a9005cc7-0000-0100-0000-68704ad10000\"", "_attachments": "attachments/", "_ts": 1752189649}, {"payPeriodId": "1090067225161412", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-02-26T00:00:00Z", "endDate": "2025-03-10T00:00:00Z", "submitByDate": "2025-03-12T00:00:00Z", "checkDate": "2025-03-14T00:00:00Z", "checkCount": 4, "id": "08269f99-90ae-4c3b-9a9d-b79bbf8c54fc", "companyId": "0042D788", "type": "payperiod", "_rid": "NmJkAKiCbEf96AQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf96AQAAAAAAA==/", "_etag": "\"a90060c7-0000-0100-0000-68704ad10000\"", "_attachments": "attachments/", "_ts": 1752189649}, {"payPeriodId": "1090067225161413", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-03-11T00:00:00Z", "endDate": "2025-03-25T00:00:00Z", "submitByDate": "2025-03-27T00:00:00Z", "checkDate": "2025-03-31T00:00:00Z", "checkCount": 5, "id": "b4b3a2d0-a0fb-49c4-821d-fe84d78fb574", "companyId": "0042D788", "type": "payperiod", "_rid": "NmJkAKiCbEf+6AQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf+6AQAAAAAAA==/", "_etag": "\"a90062c7-0000-0100-0000-68704ad20000\"", "_attachments": "attachments/", "_ts": 1752189650}, {"payPeriodId": "1090067964559193", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-03-26T00:00:00Z", "endDate": "2025-04-10T00:00:00Z", "submitByDate": "2025-04-11T00:00:00Z", "checkDate": "2025-04-15T00:00:00Z", "checkCount": 6, "id": "7af65319-5a1b-4abc-97ce-3dd507cc618c", "companyId": "0042D788", "type": "payperiod", "_rid": "NmJkAKiCbEf-6AQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf-6AQAAAAAAA==/", "_etag": "\"a90064c7-0000-0100-0000-68704ad20000\"", "_attachments": "attachments/", "_ts": 1752189650}, {"payPeriodId": "1090067964559194", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-04-11T00:00:00Z", "endDate": "2025-04-25T00:00:00Z", "submitByDate": "2025-04-28T00:00:00Z", "checkDate": "2025-04-30T00:00:00Z", "checkCount": 4, "id": "469e3e5d-1632-4cf8-aa1c-2a5c439e8a3c", "companyId": "0042D788", "type": "payperiod", "_rid": "NmJkAKiCbEcA6QQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcA6QQAAAAAAA==/", "_etag": "\"a90065c7-0000-0100-0000-68704ad20000\"", "_attachments": "attachments/", "_ts": 1752189650}, {"payPeriodId": "1090068642479323", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-04-26T00:00:00Z", "endDate": "2025-05-10T00:00:00Z", "submitByDate": "2025-05-13T00:00:00Z", "checkDate": "2025-05-15T00:00:00Z", "checkCount": 6, "id": "31487eb2-75ee-42eb-bd23-3973c2259611", "companyId": "0042D788", "type": "payperiod", "_rid": "NmJkAKiCbEcB6QQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcB6QQAAAAAAA==/", "_etag": "\"a90066c7-0000-0100-0000-68704ad20000\"", "_attachments": "attachments/", "_ts": 1752189650}, {"payPeriodId": "1090068642479324", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-05-11T00:00:00Z", "endDate": "2025-05-25T00:00:00Z", "submitByDate": "2025-05-28T00:00:00Z", "checkDate": "2025-05-30T00:00:00Z", "checkCount": 6, "id": "ae88e971-11fb-43db-95a9-4c2025f32491", "companyId": "0042D788", "type": "payperiod", "_rid": "NmJkAKiCbEcC6QQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcC6QQAAAAAAA==/", "_etag": "\"a90068c7-0000-0100-0000-68704ad20000\"", "_attachments": "attachments/", "_ts": 1752189650}, {"payPeriodId": "1090069244501760", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-05-26T00:00:00Z", "endDate": "2025-06-10T00:00:00Z", "submitByDate": "2025-06-11T00:00:00Z", "checkDate": "2025-06-13T00:00:00Z", "checkCount": 4, "id": "d345452a-9b15-4f9c-8134-4bb2860f9273", "companyId": "0042D788", "type": "payperiod", "_rid": "NmJkAKiCbEcD6QQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcD6QQAAAAAAA==/", "_etag": "\"a9006cc7-0000-0100-0000-68704ad20000\"", "_attachments": "attachments/", "_ts": 1752189650}, {"payPeriodId": "1090069244501761", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-06-11T00:00:00Z", "endDate": "2025-06-25T00:00:00Z", "submitByDate": "2025-06-26T00:00:00Z", "checkDate": "2025-06-30T00:00:00Z", "checkCount": 6, "id": "52f72bb8-d91f-4cb2-b994-e4522909da3b", "companyId": "0042D788", "type": "payperiod", "_rid": "NmJkAKiCbEcE6QQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcE6QQAAAAAAA==/", "_etag": "\"a9006dc7-0000-0100-0000-68704ad20000\"", "_attachments": "attachments/", "_ts": 1752189650}, {"payPeriodId": "1090069890471704", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-06-26T00:00:00Z", "endDate": "2025-07-10T00:00:00Z", "submitByDate": "2025-07-11T00:00:00Z", "checkDate": "2025-07-15T00:00:00Z", "checkCount": 0, "id": "7f407d0f-8259-43e9-8eb9-7a0724b66210", "companyId": "0042D788", "type": "payperiod", "_rid": "NmJkAKiCbEcF6QQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcF6QQAAAAAAA==/", "_etag": "\"a9006ec7-0000-0100-0000-68704ad20000\"", "_attachments": "attachments/", "_ts": 1752189650}, {"payPeriodId": "1090069890471705", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-07-11T00:00:00Z", "endDate": "2025-07-25T00:00:00Z", "submitByDate": "2025-07-29T00:00:00Z", "checkDate": "2025-07-31T00:00:00Z", "checkCount": 0, "id": "1991cffe-9386-4781-85ac-633fa1d4f9b0", "companyId": "0042D788", "type": "payperiod", "_rid": "NmJkAKiCbEcG6QQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcG6QQAAAAAAA==/", "_etag": "\"a90070c7-0000-0100-0000-68704ad20000\"", "_attachments": "attachments/", "_ts": 1752189650}, {"payPeriodId": "1090070578744087", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-07-26T00:00:00Z", "endDate": "2025-08-10T00:00:00Z", "submitByDate": "2025-08-13T00:00:00Z", "checkDate": "2025-08-15T00:00:00Z", "checkCount": 0, "id": "fae35f32-bb2a-4139-8c29-30984c206af6", "companyId": "0042D788", "type": "payperiod", "_rid": "NmJkAKiCbEcH6QQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcH6QQAAAAAAA==/", "_etag": "\"a90073c7-0000-0100-0000-68704ad20000\"", "_attachments": "attachments/", "_ts": 1752189650}, {"payPeriodId": "1090070578744088", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-08-11T00:00:00Z", "endDate": "2025-08-25T00:00:00Z", "submitByDate": "2025-08-27T00:00:00Z", "checkDate": "2025-08-29T00:00:00Z", "checkCount": 0, "id": "be98acf0-3b52-4853-bd1d-4d78136ad164", "companyId": "0042D788", "type": "payperiod", "_rid": "NmJkAKiCbEcI6QQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcI6QQAAAAAAA==/", "_etag": "\"a90075c7-0000-0100-0000-68704ad20000\"", "_attachments": "attachments/", "_ts": 1752189650}, {"payPeriodId": "1090071218585305", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-08-26T00:00:00Z", "endDate": "2025-09-10T00:00:00Z", "submitByDate": "2025-09-11T00:00:00Z", "checkDate": "2025-09-15T00:00:00Z", "checkCount": 0, "id": "070b5512-8b2e-4803-a3f8-adec99dc9044", "companyId": "0042D788", "type": "payperiod", "_rid": "NmJkAKiCbEcJ6QQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcJ6QQAAAAAAA==/", "_etag": "\"a90077c7-0000-0100-0000-68704ad20000\"", "_attachments": "attachments/", "_ts": 1752189650}, {"payPeriodId": "1090071218585306", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-09-11T00:00:00Z", "endDate": "2025-09-25T00:00:00Z", "submitByDate": "2025-09-26T00:00:00Z", "checkDate": "2025-09-30T00:00:00Z", "checkCount": 0, "id": "ddf4e669-0fd5-4186-bbc4-09a5a3ce2413", "companyId": "0042D788", "type": "payperiod", "_rid": "NmJkAKiCbEcK6QQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcK6QQAAAAAAA==/", "_etag": "\"a90078c7-0000-0100-0000-68704ad30000\"", "_attachments": "attachments/", "_ts": 1752189651}, {"payPeriodId": "1090071914396512", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-09-26T00:00:00Z", "endDate": "2025-10-10T00:00:00Z", "submitByDate": "2025-10-13T00:00:00Z", "checkDate": "2025-10-15T00:00:00Z", "checkCount": 0, "id": "65d77a2d-f823-4139-a7e9-9e89b9339ed1", "companyId": "0042D788", "type": "payperiod", "_rid": "NmJkAKiCbEcL6QQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcL6QQAAAAAAA==/", "_etag": "\"a9007dc7-0000-0100-0000-68704ad30000\"", "_attachments": "attachments/", "_ts": 1752189651}, {"payPeriodId": "1090071914396513", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-10-11T00:00:00Z", "endDate": "2025-10-25T00:00:00Z", "submitByDate": "2025-10-29T00:00:00Z", "checkDate": "2025-10-31T00:00:00Z", "checkCount": 0, "id": "a0541ae3-226c-4fc1-9d94-bb426107db76", "companyId": "0042D788", "type": "payperiod", "_rid": "NmJkAKiCbEcM6QQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcM6QQAAAAAAA==/", "_etag": "\"a90080c7-0000-0100-0000-68704ad30000\"", "_attachments": "attachments/", "_ts": 1752189651}, {"payPeriodId": "1090071217599644", "status": "ENTRY", "description": "Void", "startDate": "2025-04-11T00:00:00Z", "endDate": "2025-04-25T00:00:00Z", "submitByDate": "2025-04-29T00:00:00Z", "checkDate": "2025-04-30T00:00:00Z", "checkCount": 1, "id": "8865a530-3a68-4738-8aa1-36cb6ebf8327", "companyId": "0042D788", "type": "payperiod", "_rid": "NmJkAKiCbEcN6QQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcN6QQAAAAAAA==/", "_etag": "\"a90083c7-0000-0100-0000-68704ad30000\"", "_attachments": "attachments/", "_ts": 1752189651}], "links": [{"rel": "self", "href": "https://ca-payroll-ai-mock-sb-001-secure.nicebeach-8a7a52ab.eastus.azurecontainerapps.io/ca/companies/0042D788/payperiods"}]}, "status_code": 200}