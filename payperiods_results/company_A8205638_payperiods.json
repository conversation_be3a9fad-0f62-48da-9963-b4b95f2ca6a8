{"success": true, "company_id": "A8205638", "data": {"metadata": {"contentItemCount": 36}, "content": [{"payPeriodId": "1060038969917873", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2024-12-28T00:00:00Z", "endDate": "2025-01-10T00:00:00Z", "submitByDate": "2025-01-15T00:00:00Z", "checkDate": "2025-01-17T00:00:00Z", "checkCount": 3, "id": "8b747f54-5b3b-4e9e-ae4f-d5e61c93dbf4", "companyId": "A8205638", "type": "payperiod", "_rid": "NmJkAKiCbEf71gIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf71gIAAAAAAA==/", "_etag": "\"a400af28-0000-0100-0000-68701f0a0000\"", "_attachments": "attachments/", "_ts": 1752178442}, {"payPeriodId": "1060039042333677", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-01-11T00:00:00Z", "endDate": "2025-01-24T00:00:00Z", "submitByDate": "2025-01-29T00:00:00Z", "checkDate": "2025-01-31T00:00:00Z", "checkCount": 3, "id": "897a927c-08e2-434d-ba3e-0840cc78ea96", "companyId": "A8205638", "type": "payperiod", "_rid": "NmJkAKiCbEf81gIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf81gIAAAAAAA==/", "_etag": "\"a400b228-0000-0100-0000-68701f0a0000\"", "_attachments": "attachments/", "_ts": 1752178442}, {"payPeriodId": "1060039122482921", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-01-25T00:00:00Z", "endDate": "2025-02-07T00:00:00Z", "submitByDate": "2025-02-12T00:00:00Z", "checkDate": "2025-02-14T00:00:00Z", "checkCount": 3, "id": "713a3a85-f940-44d7-b598-6957120ac106", "companyId": "A8205638", "type": "payperiod", "_rid": "NmJkAKiCbEf91gIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf91gIAAAAAAA==/", "_etag": "\"a400b828-0000-0100-0000-68701f0a0000\"", "_attachments": "attachments/", "_ts": 1752178442}, {"payPeriodId": "1060039192081274", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-02-08T00:00:00Z", "endDate": "2025-02-21T00:00:00Z", "submitByDate": "2025-02-26T00:00:00Z", "checkDate": "2025-02-28T00:00:00Z", "checkCount": 3, "id": "6dce4a58-afad-403a-9bbb-19a65f53a9ba", "companyId": "A8205638", "type": "payperiod", "_rid": "NmJkAKiCbEf+1gIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf+1gIAAAAAAA==/", "_etag": "\"a400bd28-0000-0100-0000-68701f0a0000\"", "_attachments": "attachments/", "_ts": 1752178442}, {"payPeriodId": "1060039264539654", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-02-22T00:00:00Z", "endDate": "2025-03-07T00:00:00Z", "submitByDate": "2025-03-12T00:00:00Z", "checkDate": "2025-03-14T00:00:00Z", "checkCount": 3, "id": "90baf206-7034-4c9e-a62d-8b658d6272d9", "companyId": "A8205638", "type": "payperiod", "_rid": "NmJkAKiCbEf-1gIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf-1gIAAAAAAA==/", "_etag": "\"a400bf28-0000-0100-0000-68701f0a0000\"", "_attachments": "attachments/", "_ts": 1752178442}, {"payPeriodId": "1060039339134808", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-03-08T00:00:00Z", "endDate": "2025-03-21T00:00:00Z", "submitByDate": "2025-04-04T00:00:00Z", "checkDate": "2025-04-05T00:00:00Z", "checkCount": 0, "id": "059c9df0-25e9-4e3b-bbe6-4405be0a753f", "companyId": "A8205638", "type": "payperiod", "_rid": "NmJkAKiCbEcA1wIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcA1wIAAAAAAA==/", "_etag": "\"a400c328-0000-0100-0000-68701f0b0000\"", "_attachments": "attachments/", "_ts": 1752178443}, {"payPeriodId": "1060039623082006", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-05-03T00:00:00Z", "endDate": "2025-05-16T00:00:00Z", "submitByDate": "2025-05-21T00:00:00Z", "checkDate": "2025-05-23T00:00:00Z", "checkCount": 0, "id": "3557f556-e3e1-487c-8177-097ec1b4b28e", "companyId": "A8205638", "type": "payperiod", "_rid": "NmJkAKiCbEcB1wIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcB1wIAAAAAAA==/", "_etag": "\"a400c628-0000-0100-0000-68701f0b0000\"", "_attachments": "attachments/", "_ts": 1752178443}, {"payPeriodId": "1060039689469962", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-05-17T00:00:00Z", "endDate": "2025-05-30T00:00:00Z", "submitByDate": "2025-06-04T00:00:00Z", "checkDate": "2025-06-06T00:00:00Z", "checkCount": 0, "id": "61b175bc-c5c8-4e3e-8bb2-bd717d624818", "companyId": "A8205638", "type": "payperiod", "_rid": "NmJkAKiCbEcC1wIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcC1wIAAAAAAA==/", "_etag": "\"a400c828-0000-0100-0000-68701f0b0000\"", "_attachments": "attachments/", "_ts": 1752178443}, {"payPeriodId": "1060039760321974", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-05-31T00:00:00Z", "endDate": "2025-06-13T00:00:00Z", "submitByDate": "2025-06-18T00:00:00Z", "checkDate": "2025-06-20T00:00:00Z", "checkCount": 0, "id": "6028a2a1-e8f2-497e-b698-937babbb3d45", "companyId": "A8205638", "type": "payperiod", "_rid": "NmJkAKiCbEcD1wIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcD1wIAAAAAAA==/", "_etag": "\"a400ce28-0000-0100-0000-68701f0b0000\"", "_attachments": "attachments/", "_ts": 1752178443}, {"payPeriodId": "1060039856356007", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-06-28T00:00:00Z", "endDate": "2025-07-11T00:00:00Z", "submitByDate": "2025-07-16T00:00:00Z", "checkDate": "2025-07-18T00:00:00Z", "checkCount": 0, "id": "04d37021-d2f9-40a2-b200-1acd935506fd", "companyId": "A8205638", "type": "payperiod", "_rid": "NmJkAKiCbEcE1wIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcE1wIAAAAAAA==/", "_etag": "\"a400d128-0000-0100-0000-68701f0b0000\"", "_attachments": "attachments/", "_ts": 1752178443}, {"payPeriodId": "1060039979331104", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-07-12T00:00:00Z", "endDate": "2025-07-25T00:00:00Z", "submitByDate": "2025-07-30T00:00:00Z", "checkDate": "2025-08-01T00:00:00Z", "checkCount": 0, "id": "a531ba57-f24a-4b0c-9057-9de7f522cdf8", "companyId": "A8205638", "type": "payperiod", "_rid": "NmJkAKiCbEcF1wIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcF1wIAAAAAAA==/", "_etag": "\"a400d428-0000-0100-0000-68701f0b0000\"", "_attachments": "attachments/", "_ts": 1752178443}, {"payPeriodId": "1060040056112879", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-07-26T00:00:00Z", "endDate": "2025-08-08T00:00:00Z", "submitByDate": "2025-08-13T00:00:00Z", "checkDate": "2025-08-15T00:00:00Z", "checkCount": 0, "id": "d8420617-8643-4986-84c9-187aae0c51a6", "companyId": "A8205638", "type": "payperiod", "_rid": "NmJkAKiCbEcG1wIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcG1wIAAAAAAA==/", "_etag": "\"a400d628-0000-0100-0000-68701f0b0000\"", "_attachments": "attachments/", "_ts": 1752178443}, {"payPeriodId": "1060040127603922", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-08-09T00:00:00Z", "endDate": "2025-08-22T00:00:00Z", "submitByDate": "2025-08-27T00:00:00Z", "checkDate": "2025-08-29T00:00:00Z", "checkCount": 0, "id": "606011a2-a049-43fe-a846-bb2f7d238308", "companyId": "A8205638", "type": "payperiod", "_rid": "NmJkAKiCbEcH1wIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcH1wIAAAAAAA==/", "_etag": "\"a400d728-0000-0100-0000-68701f0b0000\"", "_attachments": "attachments/", "_ts": 1752178443}, {"payPeriodId": "1060040196230200", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-08-23T00:00:00Z", "endDate": "2025-09-05T00:00:00Z", "submitByDate": "2025-09-10T00:00:00Z", "checkDate": "2025-09-12T00:00:00Z", "checkCount": 0, "id": "0c65089e-740f-4beb-bf5b-0e0762fff411", "companyId": "A8205638", "type": "payperiod", "_rid": "NmJkAKiCbEcI1wIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcI1wIAAAAAAA==/", "_etag": "\"a400d928-0000-0100-0000-68701f0b0000\"", "_attachments": "attachments/", "_ts": 1752178443}, {"payPeriodId": "1060040264269291", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-09-06T00:00:00Z", "endDate": "2025-09-19T00:00:00Z", "submitByDate": "2025-09-24T00:00:00Z", "checkDate": "2025-09-26T00:00:00Z", "checkCount": 0, "id": "0f004e9b-3af4-42ee-a2db-8b20a8e8b8c9", "companyId": "A8205638", "type": "payperiod", "_rid": "NmJkAKiCbEcJ1wIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcJ1wIAAAAAAA==/", "_etag": "\"a400db28-0000-0100-0000-68701f0b0000\"", "_attachments": "attachments/", "_ts": 1752178443}, {"payPeriodId": "1060040335426446", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-09-20T00:00:00Z", "endDate": "2025-10-03T00:00:00Z", "submitByDate": "2025-10-08T00:00:00Z", "checkDate": "2025-10-10T00:00:00Z", "checkCount": 0, "id": "9704ce3d-f8be-4680-82fa-bdf13d39ca41", "companyId": "A8205638", "type": "payperiod", "_rid": "NmJkAKiCbEcK1wIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcK1wIAAAAAAA==/", "_etag": "\"a400dd28-0000-0100-0000-68701f0b0000\"", "_attachments": "attachments/", "_ts": 1752178443}, {"payPeriodId": "1060039553636597", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-04-19T00:00:00Z", "endDate": "2025-05-02T00:00:00Z", "submitByDate": "2025-05-07T00:00:00Z", "checkDate": "2025-05-09T00:00:00Z", "checkCount": 0, "id": "28314328-7af0-4cfa-9c2c-6a808c7fccab", "companyId": "A8205638", "type": "payperiod", "_rid": "NmJkAKiCbEcL1wIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcL1wIAAAAAAA==/", "_etag": "\"a400e028-0000-0100-0000-68701f0b0000\"", "_attachments": "attachments/", "_ts": 1752178443}, {"payPeriodId": "1060040355456419", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-06-14T00:00:00Z", "endDate": "2025-06-27T00:00:00Z", "submitByDate": "2025-07-01T00:00:00Z", "checkDate": "2025-07-10T00:00:00Z", "checkCount": 0, "id": "c0fba888-6bbd-49de-84aa-c94fbded47c9", "companyId": "A8205638", "type": "payperiod", "_rid": "NmJkAKiCbEcM1wIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcM1wIAAAAAAA==/", "_etag": "\"a400e128-0000-0100-0000-68701f0b0000\"", "_attachments": "attachments/", "_ts": 1752178443}, {"payPeriodId": "1060038969917873", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2024-12-28T00:00:00Z", "endDate": "2025-01-10T00:00:00Z", "submitByDate": "2025-01-15T00:00:00Z", "checkDate": "2025-01-17T00:00:00Z", "checkCount": 3, "id": "a4b03c05-add1-4f53-aefe-cc9c592f1107", "companyId": "A8205638", "type": "payperiod", "_rid": "NmJkAKiCbEcP1wIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcP1wIAAAAAAA==/", "_etag": "\"a400f328-0000-0100-0000-68701f0c0000\"", "_attachments": "attachments/", "_ts": 1752178444}, {"payPeriodId": "1060039042333677", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-01-11T00:00:00Z", "endDate": "2025-01-24T00:00:00Z", "submitByDate": "2025-01-29T00:00:00Z", "checkDate": "2025-01-31T00:00:00Z", "checkCount": 3, "id": "27c4436e-38c0-4139-9f2d-784e0ea84f60", "companyId": "A8205638", "type": "payperiod", "_rid": "NmJkAKiCbEcQ1wIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcQ1wIAAAAAAA==/", "_etag": "\"a400f428-0000-0100-0000-68701f0c0000\"", "_attachments": "attachments/", "_ts": 1752178444}, {"payPeriodId": "1060039122482921", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-01-25T00:00:00Z", "endDate": "2025-02-07T00:00:00Z", "submitByDate": "2025-02-12T00:00:00Z", "checkDate": "2025-02-14T00:00:00Z", "checkCount": 3, "id": "3e188eb8-7775-4e29-a06e-96778bebc2f2", "companyId": "A8205638", "type": "payperiod", "_rid": "NmJkAKiCbEcR1wIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcR1wIAAAAAAA==/", "_etag": "\"a400f628-0000-0100-0000-68701f0c0000\"", "_attachments": "attachments/", "_ts": 1752178444}, {"payPeriodId": "1060039192081274", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-02-08T00:00:00Z", "endDate": "2025-02-21T00:00:00Z", "submitByDate": "2025-02-26T00:00:00Z", "checkDate": "2025-02-28T00:00:00Z", "checkCount": 3, "id": "19c1929d-0de8-4956-b8a7-edd2485e5175", "companyId": "A8205638", "type": "payperiod", "_rid": "NmJkAKiCbEcS1wIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcS1wIAAAAAAA==/", "_etag": "\"a400fa28-0000-0100-0000-68701f0c0000\"", "_attachments": "attachments/", "_ts": 1752178444}, {"payPeriodId": "1060039264539654", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-02-22T00:00:00Z", "endDate": "2025-03-07T00:00:00Z", "submitByDate": "2025-03-12T00:00:00Z", "checkDate": "2025-03-14T00:00:00Z", "checkCount": 3, "id": "e0b7885e-cbdd-4554-8390-7f5a343c8187", "companyId": "A8205638", "type": "payperiod", "_rid": "NmJkAKiCbEcT1wIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcT1wIAAAAAAA==/", "_etag": "\"a400fd28-0000-0100-0000-68701f0c0000\"", "_attachments": "attachments/", "_ts": 1752178444}, {"payPeriodId": "1060039339134808", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-03-08T00:00:00Z", "endDate": "2025-03-21T00:00:00Z", "submitByDate": "2025-04-04T00:00:00Z", "checkDate": "2025-04-05T00:00:00Z", "checkCount": 3, "id": "38301b74-a356-4dd3-a677-5d1f3f05092b", "companyId": "A8205638", "type": "payperiod", "_rid": "NmJkAKiCbEcU1wIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcU1wIAAAAAAA==/", "_etag": "\"a400ff28-0000-0100-0000-68701f0c0000\"", "_attachments": "attachments/", "_ts": 1752178444}, {"payPeriodId": "1060039623082006", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-05-03T00:00:00Z", "endDate": "2025-05-16T00:00:00Z", "submitByDate": "2025-05-21T00:00:00Z", "checkDate": "2025-05-23T00:00:00Z", "checkCount": 2, "id": "a4bc332a-11db-4389-b1ad-186a2c29d90a", "companyId": "A8205638", "type": "payperiod", "_rid": "NmJkAKiCbEcV1wIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcV1wIAAAAAAA==/", "_etag": "\"a4000029-0000-0100-0000-68701f0c0000\"", "_attachments": "attachments/", "_ts": 1752178444}, {"payPeriodId": "1060039689469962", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-05-17T00:00:00Z", "endDate": "2025-05-30T00:00:00Z", "submitByDate": "2025-06-04T00:00:00Z", "checkDate": "2025-06-06T00:00:00Z", "checkCount": 2, "id": "3cb64066-d3c4-4951-bd7f-28317c6b9808", "companyId": "A8205638", "type": "payperiod", "_rid": "NmJkAKiCbEcW1wIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcW1wIAAAAAAA==/", "_etag": "\"a4000329-0000-0100-0000-68701f0c0000\"", "_attachments": "attachments/", "_ts": 1752178444}, {"payPeriodId": "1060039760321974", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-05-31T00:00:00Z", "endDate": "2025-06-13T00:00:00Z", "submitByDate": "2025-06-18T00:00:00Z", "checkDate": "2025-06-20T00:00:00Z", "checkCount": 2, "id": "07d1f9a3-ee4e-41c8-8c34-2d1a2e0cd607", "companyId": "A8205638", "type": "payperiod", "_rid": "NmJkAKiCbEcX1wIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcX1wIAAAAAAA==/", "_etag": "\"a4000629-0000-0100-0000-68701f0c0000\"", "_attachments": "attachments/", "_ts": 1752178444}, {"payPeriodId": "1060039856356007", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-06-28T00:00:00Z", "endDate": "2025-07-11T00:00:00Z", "submitByDate": "2025-07-16T00:00:00Z", "checkDate": "2025-07-18T00:00:00Z", "checkCount": 0, "id": "eee72100-7ebe-4cd7-ac5a-92036e12dd6f", "companyId": "A8205638", "type": "payperiod", "_rid": "NmJkAKiCbEcY1wIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcY1wIAAAAAAA==/", "_etag": "\"a4000829-0000-0100-0000-68701f0c0000\"", "_attachments": "attachments/", "_ts": 1752178444}, {"payPeriodId": "1060039979331104", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-07-12T00:00:00Z", "endDate": "2025-07-25T00:00:00Z", "submitByDate": "2025-07-30T00:00:00Z", "checkDate": "2025-08-01T00:00:00Z", "checkCount": 0, "id": "7c7874cd-007a-4a14-9e84-154a38ac43be", "companyId": "A8205638", "type": "payperiod", "_rid": "NmJkAKiCbEcZ1wIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcZ1wIAAAAAAA==/", "_etag": "\"a4000929-0000-0100-0000-68701f0d0000\"", "_attachments": "attachments/", "_ts": 1752178445}, {"payPeriodId": "1060040056112879", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-07-26T00:00:00Z", "endDate": "2025-08-08T00:00:00Z", "submitByDate": "2025-08-13T00:00:00Z", "checkDate": "2025-08-15T00:00:00Z", "checkCount": 0, "id": "b20a9667-2286-4737-8af8-50e4075ff45a", "companyId": "A8205638", "type": "payperiod", "_rid": "NmJkAKiCbEca1wIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEca1wIAAAAAAA==/", "_etag": "\"a4000c29-0000-0100-0000-68701f0d0000\"", "_attachments": "attachments/", "_ts": 1752178445}, {"payPeriodId": "1060040127603922", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-08-09T00:00:00Z", "endDate": "2025-08-22T00:00:00Z", "submitByDate": "2025-08-27T00:00:00Z", "checkDate": "2025-08-29T00:00:00Z", "checkCount": 0, "id": "86c38afa-2879-4fd8-840a-5802819be93f", "companyId": "A8205638", "type": "payperiod", "_rid": "NmJkAKiCbEcb1wIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcb1wIAAAAAAA==/", "_etag": "\"a4001029-0000-0100-0000-68701f0d0000\"", "_attachments": "attachments/", "_ts": 1752178445}, {"payPeriodId": "1060040196230200", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-08-23T00:00:00Z", "endDate": "2025-09-05T00:00:00Z", "submitByDate": "2025-09-10T00:00:00Z", "checkDate": "2025-09-12T00:00:00Z", "checkCount": 0, "id": "c8945230-4594-4f21-8f7e-e870522a6bcb", "companyId": "A8205638", "type": "payperiod", "_rid": "NmJkAKiCbEcc1wIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcc1wIAAAAAAA==/", "_etag": "\"a4001629-0000-0100-0000-68701f0d0000\"", "_attachments": "attachments/", "_ts": 1752178445}, {"payPeriodId": "1060040264269291", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-09-06T00:00:00Z", "endDate": "2025-09-19T00:00:00Z", "submitByDate": "2025-09-24T00:00:00Z", "checkDate": "2025-09-26T00:00:00Z", "checkCount": 0, "id": "d523c7c2-e2b2-4731-867a-f65fa44f1444", "companyId": "A8205638", "type": "payperiod", "_rid": "NmJkAKiCbEcd1wIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcd1wIAAAAAAA==/", "_etag": "\"a4001b29-0000-0100-0000-68701f0d0000\"", "_attachments": "attachments/", "_ts": 1752178445}, {"payPeriodId": "1060040335426446", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-09-20T00:00:00Z", "endDate": "2025-10-03T00:00:00Z", "submitByDate": "2025-10-08T00:00:00Z", "checkDate": "2025-10-10T00:00:00Z", "checkCount": 0, "id": "d6589bc1-5fb3-492c-b77f-e2c228ad4fc1", "companyId": "A8205638", "type": "payperiod", "_rid": "NmJkAKiCbEce1wIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEce1wIAAAAAAA==/", "_etag": "\"a4002029-0000-0100-0000-68701f0d0000\"", "_attachments": "attachments/", "_ts": 1752178445}, {"payPeriodId": "1060039553636597", "intervalCode": "BI_WEEKLY", "status": "ENTRY", "description": "Bi-weekly Payroll (1)", "startDate": "2025-04-19T00:00:00Z", "endDate": "2025-05-02T00:00:00Z", "submitByDate": "2025-05-07T00:00:00Z", "checkDate": "2025-05-09T00:00:00Z", "checkCount": 4, "id": "b1af472e-5dcf-4403-9a33-15bf1794f562", "companyId": "A8205638", "type": "payperiod", "_rid": "NmJkAKiCbEcf1wIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcf1wIAAAAAAA==/", "_etag": "\"a4002129-0000-0100-0000-68701f0d0000\"", "_attachments": "attachments/", "_ts": 1752178445}, {"payPeriodId": "1060040355456419", "status": "ENTRY", "description": "Bi-weekly Payroll (1)", "startDate": "2025-06-14T00:00:00Z", "endDate": "2025-06-27T00:00:00Z", "submitByDate": "2025-07-01T00:00:00Z", "checkDate": "2025-07-10T00:00:00Z", "checkCount": 2, "id": "0f42728f-8836-4fcc-9410-d68f86f6829f", "companyId": "A8205638", "type": "payperiod", "_rid": "NmJkAKiCbEcg1wIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcg1wIAAAAAAA==/", "_etag": "\"a4002329-0000-0100-0000-68701f0d0000\"", "_attachments": "attachments/", "_ts": 1752178445}], "links": [{"rel": "self", "href": "https://ca-payroll-ai-mock-sb-001-secure.nicebeach-8a7a52ab.eastus.azurecontainerapps.io/ca/companies/A8205638/payperiods"}]}, "status_code": 200}