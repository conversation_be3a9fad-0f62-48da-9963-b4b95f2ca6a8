{"success": true, "company_id": "18163405", "data": {"metadata": {"contentItemCount": 160}, "content": [{"payPeriodId": "1090066094508862", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2024-12-30T00:00:00Z", "endDate": "2025-01-05T00:00:00Z", "submitByDate": "2025-01-14T00:00:00Z", "checkDate": "2025-01-16T00:00:00Z", "checkCount": 8, "id": "7d4cd6cb-d34d-4702-aced-e42f129fb1ad", "companyId": "18163405", "type": "payperiod", "_rid": "NmJkAKiCbEf2PAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf2PAAAAAAAAA==/", "_etag": "\"970070cc-0000-0100-0000-686fd3ca0000\"", "_attachments": "attachments/", "_ts": 1752159178}, {"payPeriodId": "1090066270674400", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-01-06T00:00:00Z", "endDate": "2025-01-12T00:00:00Z", "submitByDate": "2025-01-21T00:00:00Z", "checkDate": "2025-01-23T00:00:00Z", "checkCount": 8, "id": "a3b89f5b-2a34-43aa-b7ca-6e8b823e36ff", "companyId": "18163405", "type": "payperiod", "_rid": "NmJkAKiCbEf3PAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf3PAAAAAAAAA==/", "_etag": "\"970073cc-0000-0100-0000-686fd3ca0000\"", "_attachments": "attachments/", "_ts": 1752159178}, {"payPeriodId": "1090066350207932", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-01-13T00:00:00Z", "endDate": "2025-01-19T00:00:00Z", "submitByDate": "2025-01-28T00:00:00Z", "checkDate": "2025-01-30T00:00:00Z", "checkCount": 7, "id": "1c1fbcc9-ff3d-4e17-9cf2-2b955b665b21", "companyId": "18163405", "type": "payperiod", "_rid": "NmJkAKiCbEf4PAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf4PAAAAAAAAA==/", "_etag": "\"970076cc-0000-0100-0000-686fd3ca0000\"", "_attachments": "attachments/", "_ts": 1752159178}, {"payPeriodId": "1090066543956486", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-01-20T00:00:00Z", "endDate": "2025-01-26T00:00:00Z", "submitByDate": "2025-02-04T00:00:00Z", "checkDate": "2025-02-06T00:00:00Z", "checkCount": 7, "id": "0e95730c-5ca9-4c0b-aa76-a3bb6e180cb0", "companyId": "18163405", "type": "payperiod", "_rid": "NmJkAKiCbEf5PAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf5PAAAAAAAAA==/", "_etag": "\"970079cc-0000-0100-0000-686fd3ca0000\"", "_attachments": "attachments/", "_ts": 1752159178}, {"payPeriodId": "1090066639799565", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-01-27T00:00:00Z", "endDate": "2025-02-02T00:00:00Z", "submitByDate": "2025-02-11T00:00:00Z", "checkDate": "2025-02-13T00:00:00Z", "checkCount": 8, "id": "89c2b930-b2a4-4d70-b4bd-0eddf41ebc0d", "companyId": "18163405", "type": "payperiod", "_rid": "NmJkAKiCbEf6PAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf6PAAAAAAAAA==/", "_etag": "\"97007dcc-0000-0100-0000-686fd3ca0000\"", "_attachments": "attachments/", "_ts": 1752159178}, {"payPeriodId": "1090066806511086", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-02-03T00:00:00Z", "endDate": "2025-02-09T00:00:00Z", "submitByDate": "2025-02-18T00:00:00Z", "checkDate": "2025-02-20T00:00:00Z", "checkCount": 6, "id": "9544e357-2ccf-4c8f-8dcc-cdfba3b25931", "companyId": "18163405", "type": "payperiod", "_rid": "NmJkAKiCbEf7PAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf7PAAAAAAAAA==/", "_etag": "\"970082cc-0000-0100-0000-686fd3ca0000\"", "_attachments": "attachments/", "_ts": 1752159178}, {"payPeriodId": "1090066966094480", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-02-10T00:00:00Z", "endDate": "2025-02-16T00:00:00Z", "submitByDate": "2025-02-25T00:00:00Z", "checkDate": "2025-02-27T00:00:00Z", "checkCount": 6, "id": "a9f89487-f4f0-4f23-aa07-62b476aaff10", "companyId": "18163405", "type": "payperiod", "_rid": "NmJkAKiCbEf8PAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf8PAAAAAAAAA==/", "_etag": "\"970084cc-0000-0100-0000-686fd3ca0000\"", "_attachments": "attachments/", "_ts": 1752159178}, {"payPeriodId": "1090067102973566", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-02-17T00:00:00Z", "endDate": "2025-02-23T00:00:00Z", "submitByDate": "2025-03-04T00:00:00Z", "checkDate": "2025-03-06T00:00:00Z", "checkCount": 7, "id": "0339be17-57b4-4508-b74e-25f77c81de52", "companyId": "18163405", "type": "payperiod", "_rid": "NmJkAKiCbEf9PAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf9PAAAAAAAAA==/", "_etag": "\"970086cc-0000-0100-0000-686fd3ca0000\"", "_attachments": "attachments/", "_ts": 1752159178}, {"payPeriodId": "1090067314621089", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-02-24T00:00:00Z", "endDate": "2025-03-02T00:00:00Z", "submitByDate": "2025-03-11T00:00:00Z", "checkDate": "2025-03-13T00:00:00Z", "checkCount": 6, "id": "a8584897-d115-4461-b265-026967758189", "companyId": "18163405", "type": "payperiod", "_rid": "NmJkAKiCbEf+PAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf+PAAAAAAAAA==/", "_etag": "\"97008acc-0000-0100-0000-686fd3ca0000\"", "_attachments": "attachments/", "_ts": 1752159178}, {"payPeriodId": "1090067400500661", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-03-03T00:00:00Z", "endDate": "2025-03-09T00:00:00Z", "submitByDate": "2025-03-18T00:00:00Z", "checkDate": "2025-03-20T00:00:00Z", "checkCount": 7, "id": "b41b2ba5-6c37-4674-878e-5006c2e179d6", "companyId": "18163405", "type": "payperiod", "_rid": "NmJkAKiCbEf-PAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf-PAAAAAAAAA==/", "_etag": "\"97008bcc-0000-0100-0000-686fd3ca0000\"", "_attachments": "attachments/", "_ts": 1752159178}, {"payPeriodId": "1090067538931552", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-03-10T00:00:00Z", "endDate": "2025-03-16T00:00:00Z", "submitByDate": "2025-03-25T00:00:00Z", "checkDate": "2025-03-27T00:00:00Z", "checkCount": 7, "id": "a8151b9c-2eab-4fbf-ae11-ff1f469fa4f0", "companyId": "18163405", "type": "payperiod", "_rid": "NmJkAKiCbEcAPQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcAPQAAAAAAAA==/", "_etag": "\"97008ccc-0000-0100-0000-686fd3ca0000\"", "_attachments": "attachments/", "_ts": 1752159178}, {"payPeriodId": "1090067725095628", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-03-17T00:00:00Z", "endDate": "2025-03-23T00:00:00Z", "submitByDate": "2025-04-01T00:00:00Z", "checkDate": "2025-04-03T00:00:00Z", "checkCount": 0, "id": "bb2ede08-3e33-47d9-bd47-c2197742285d", "companyId": "18163405", "type": "payperiod", "_rid": "NmJkAKiCbEcBPQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcBPQAAAAAAAA==/", "_etag": "\"970091cc-0000-0100-0000-686fd3ca0000\"", "_attachments": "attachments/", "_ts": 1752159178}, {"payPeriodId": "1090067934859555", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-03-24T00:00:00Z", "endDate": "2025-03-30T00:00:00Z", "submitByDate": "2025-04-08T00:00:00Z", "checkDate": "2025-04-10T00:00:00Z", "checkCount": 0, "id": "0ec5d72c-0f77-44ce-816c-550e3025e8c7", "companyId": "18163405", "type": "payperiod", "_rid": "NmJkAKiCbEcCPQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcCPQAAAAAAAA==/", "_etag": "\"970094cc-0000-0100-0000-686fd3ca0000\"", "_attachments": "attachments/", "_ts": 1752159178}, {"payPeriodId": "1090068060281591", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-03-31T00:00:00Z", "endDate": "2025-04-06T00:00:00Z", "submitByDate": "2025-04-15T00:00:00Z", "checkDate": "2025-04-17T00:00:00Z", "checkCount": 0, "id": "d0361954-0034-4f60-8196-dab48fc5a343", "companyId": "18163405", "type": "payperiod", "_rid": "NmJkAKiCbEcDPQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcDPQAAAAAAAA==/", "_etag": "\"970097cc-0000-0100-0000-686fd3ca0000\"", "_attachments": "attachments/", "_ts": 1752159179}, {"payPeriodId": "1090068204343531", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-04-07T00:00:00Z", "endDate": "2025-04-13T00:00:00Z", "submitByDate": "2025-04-22T00:00:00Z", "checkDate": "2025-04-24T00:00:00Z", "checkCount": 0, "id": "471d4744-8496-432a-9658-b17474415674", "companyId": "18163405", "type": "payperiod", "_rid": "NmJkAKiCbEcEPQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcEPQAAAAAAAA==/", "_etag": "\"970099cc-0000-0100-0000-686fd3cb0000\"", "_attachments": "attachments/", "_ts": 1752159179}, {"payPeriodId": "1090068387796454", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-04-14T00:00:00Z", "endDate": "2025-04-20T00:00:00Z", "submitByDate": "2025-04-29T00:00:00Z", "checkDate": "2025-05-01T00:00:00Z", "checkCount": 0, "id": "fc5aa0cb-cae1-4164-b91d-eabab07572e9", "companyId": "18163405", "type": "payperiod", "_rid": "NmJkAKiCbEcFPQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcFPQAAAAAAAA==/", "_etag": "\"97009bcc-0000-0100-0000-686fd3cb0000\"", "_attachments": "attachments/", "_ts": 1752159179}, {"payPeriodId": "1090068504515338", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-04-21T00:00:00Z", "endDate": "2025-04-27T00:00:00Z", "submitByDate": "2025-05-06T00:00:00Z", "checkDate": "2025-05-08T00:00:00Z", "checkCount": 0, "id": "67779fc9-cbb4-4729-8a1f-a1b3f45f11cb", "companyId": "18163405", "type": "payperiod", "_rid": "NmJkAKiCbEcGPQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcGPQAAAAAAAA==/", "_etag": "\"97009fcc-0000-0100-0000-686fd3cb0000\"", "_attachments": "attachments/", "_ts": 1752159179}, {"payPeriodId": "1090068652955920", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-04-28T00:00:00Z", "endDate": "2025-05-04T00:00:00Z", "submitByDate": "2025-05-13T00:00:00Z", "checkDate": "2025-05-15T00:00:00Z", "checkCount": 0, "id": "8ecb52c6-1f61-4eb1-9f4d-3ee3c7fd2526", "companyId": "18163405", "type": "payperiod", "_rid": "NmJkAKiCbEcHPQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcHPQAAAAAAAA==/", "_etag": "\"9700a3cc-0000-0100-0000-686fd3cb0000\"", "_attachments": "attachments/", "_ts": 1752159179}, {"payPeriodId": "1090068799232322", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-05-05T00:00:00Z", "endDate": "2025-05-11T00:00:00Z", "submitByDate": "2025-05-20T00:00:00Z", "checkDate": "2025-05-22T00:00:00Z", "checkCount": 0, "id": "c893b3d1-555f-4791-b754-e237b3370426", "companyId": "18163405", "type": "payperiod", "_rid": "NmJkAKiCbEcIPQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcIPQAAAAAAAA==/", "_etag": "\"9700a7cc-0000-0100-0000-686fd3cb0000\"", "_attachments": "attachments/", "_ts": 1752159179}, {"payPeriodId": "1090068967452091", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-05-12T00:00:00Z", "endDate": "2025-05-18T00:00:00Z", "submitByDate": "2025-05-27T00:00:00Z", "checkDate": "2025-05-29T00:00:00Z", "checkCount": 0, "id": "e6736acc-b36c-4cce-8899-7074b9918f47", "companyId": "18163405", "type": "payperiod", "_rid": "NmJkAKiCbEcJPQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcJPQAAAAAAAA==/", "_etag": "\"9700a8cc-0000-0100-0000-686fd3cb0000\"", "_attachments": "attachments/", "_ts": 1752159179}, {"payPeriodId": "1090069136626585", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-05-19T00:00:00Z", "endDate": "2025-05-25T00:00:00Z", "submitByDate": "2025-06-03T00:00:00Z", "checkDate": "2025-06-05T00:00:00Z", "checkCount": 0, "id": "dac9b31c-e94e-4d04-b8a4-64e0f195fbe1", "companyId": "18163405", "type": "payperiod", "_rid": "NmJkAKiCbEcKPQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcKPQAAAAAAAA==/", "_etag": "\"9700aacc-0000-0100-0000-686fd3cb0000\"", "_attachments": "attachments/", "_ts": 1752159179}, {"payPeriodId": "1090069236974040", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-05-26T00:00:00Z", "endDate": "2025-06-01T00:00:00Z", "submitByDate": "2025-06-10T00:00:00Z", "checkDate": "2025-06-12T00:00:00Z", "checkCount": 0, "id": "a6a0c1b3-b2c0-4d13-9a84-088a4ef2fa14", "companyId": "18163405", "type": "payperiod", "_rid": "NmJkAKiCbEcLPQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcLPQAAAAAAAA==/", "_etag": "\"9700afcc-0000-0100-0000-686fd3cb0000\"", "_attachments": "attachments/", "_ts": 1752159179}, {"payPeriodId": "1090069412709288", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-06-02T00:00:00Z", "endDate": "2025-06-08T00:00:00Z", "submitByDate": "2025-06-16T00:00:00Z", "checkDate": "2025-06-18T00:00:00Z", "checkCount": 0, "id": "2a8a04d0-8b5b-4659-86c8-76e27fb33e34", "companyId": "18163405", "type": "payperiod", "_rid": "NmJkAKiCbEcMPQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcMPQAAAAAAAA==/", "_etag": "\"9700b4cc-0000-0100-0000-686fd3cb0000\"", "_attachments": "attachments/", "_ts": 1752159179}, {"payPeriodId": "1090069550373257", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-06-09T00:00:00Z", "endDate": "2025-06-15T00:00:00Z", "submitByDate": "2025-06-24T00:00:00Z", "checkDate": "2025-06-26T00:00:00Z", "checkCount": 0, "id": "e409a1c9-7a45-4082-aaa7-5ca66c0072e0", "companyId": "18163405", "type": "payperiod", "_rid": "NmJkAKiCbEcNPQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcNPQAAAAAAAA==/", "_etag": "\"9700b9cc-0000-0100-0000-686fd3cb0000\"", "_attachments": "attachments/", "_ts": 1752159179}, {"payPeriodId": "1090069724338471", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-06-16T00:00:00Z", "endDate": "2025-06-22T00:00:00Z", "submitByDate": "2025-07-01T00:00:00Z", "checkDate": "2025-07-03T00:00:00Z", "checkCount": 0, "id": "4332471e-38b7-49cb-88d8-4956efa8bd80", "companyId": "18163405", "type": "payperiod", "_rid": "NmJkAKiCbEcOPQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcOPQAAAAAAAA==/", "_etag": "\"9700bdcc-0000-0100-0000-686fd3cb0000\"", "_attachments": "attachments/", "_ts": 1752159179}, {"payPeriodId": "1090069895278376", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-06-23T00:00:00Z", "endDate": "2025-06-29T00:00:00Z", "submitByDate": "2025-07-08T00:00:00Z", "checkDate": "2025-07-10T00:00:00Z", "checkCount": 0, "id": "9899060f-57c2-43e8-adb7-2ab44a921314", "companyId": "18163405", "type": "payperiod", "_rid": "NmJkAKiCbEcPPQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcPPQAAAAAAAA==/", "_etag": "\"9700bfcc-0000-0100-0000-686fd3cb0000\"", "_attachments": "attachments/", "_ts": 1752159179}, {"payPeriodId": "1090070042475209", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-06-30T00:00:00Z", "endDate": "2025-07-06T00:00:00Z", "submitByDate": "2025-07-15T00:00:00Z", "checkDate": "2025-07-17T00:00:00Z", "checkCount": 0, "id": "37ca4a47-2562-4dea-85dd-56005ae1bc7c", "companyId": "18163405", "type": "payperiod", "_rid": "NmJkAKiCbEcQPQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcQPQAAAAAAAA==/", "_etag": "\"9700c4cc-0000-0100-0000-686fd3cb0000\"", "_attachments": "attachments/", "_ts": 1752159179}, {"payPeriodId": "1090070177690660", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-07-07T00:00:00Z", "endDate": "2025-07-13T00:00:00Z", "submitByDate": "2025-07-22T00:00:00Z", "checkDate": "2025-07-24T00:00:00Z", "checkCount": 0, "id": "12e35f24-dd79-4682-87dd-59c92b33b1ac", "companyId": "18163405", "type": "payperiod", "_rid": "NmJkAKiCbEcRPQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcRPQAAAAAAAA==/", "_etag": "\"9700c6cc-0000-0100-0000-686fd3cb0000\"", "_attachments": "attachments/", "_ts": 1752159179}, {"payPeriodId": "1090070338020687", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-07-14T00:00:00Z", "endDate": "2025-07-20T00:00:00Z", "submitByDate": "2025-07-29T00:00:00Z", "checkDate": "2025-07-31T00:00:00Z", "checkCount": 0, "id": "759ce3ac-eddf-4f24-b66d-5b929a58196f", "companyId": "18163405", "type": "payperiod", "_rid": "NmJkAKiCbEcSPQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcSPQAAAAAAAA==/", "_etag": "\"9700c9cc-0000-0100-0000-686fd3cc0000\"", "_attachments": "attachments/", "_ts": 1752159180}, {"payPeriodId": "1090070482796443", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-07-21T00:00:00Z", "endDate": "2025-07-27T00:00:00Z", "submitByDate": "2025-08-05T00:00:00Z", "checkDate": "2025-08-07T00:00:00Z", "checkCount": 0, "id": "86f53739-dda8-4e8f-8b29-e813366c1dcc", "companyId": "18163405", "type": "payperiod", "_rid": "NmJkAKiCbEcTPQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcTPQAAAAAAAA==/", "_etag": "\"9700cacc-0000-0100-0000-686fd3cc0000\"", "_attachments": "attachments/", "_ts": 1752159180}, {"payPeriodId": "1090070664859800", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-07-28T00:00:00Z", "endDate": "2025-08-03T00:00:00Z", "submitByDate": "2025-08-12T00:00:00Z", "checkDate": "2025-08-14T00:00:00Z", "checkCount": 0, "id": "b3adba0a-3723-4af9-88f4-778e3069d7c0", "companyId": "18163405", "type": "payperiod", "_rid": "NmJkAKiCbEcUPQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcUPQAAAAAAAA==/", "_etag": "\"9700cccc-0000-0100-0000-686fd3cc0000\"", "_attachments": "attachments/", "_ts": 1752159180}, {"payPeriodId": "1090070819936475", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-08-04T00:00:00Z", "endDate": "2025-08-10T00:00:00Z", "submitByDate": "2025-08-19T00:00:00Z", "checkDate": "2025-08-21T00:00:00Z", "checkCount": 0, "id": "97e13958-18f8-431d-b4df-a56bee63edd4", "companyId": "18163405", "type": "payperiod", "_rid": "NmJkAKiCbEcVPQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcVPQAAAAAAAA==/", "_etag": "\"9700cecc-0000-0100-0000-686fd3cc0000\"", "_attachments": "attachments/", "_ts": 1752159180}, {"payPeriodId": "1090070938909295", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-08-11T00:00:00Z", "endDate": "2025-08-17T00:00:00Z", "submitByDate": "2025-08-26T00:00:00Z", "checkDate": "2025-08-28T00:00:00Z", "checkCount": 0, "id": "d1f4ec11-1cea-4643-bb9d-97e1c845ac27", "companyId": "18163405", "type": "payperiod", "_rid": "NmJkAKiCbEcWPQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcWPQAAAAAAAA==/", "_etag": "\"9700d4cc-0000-0100-0000-686fd3cc0000\"", "_attachments": "attachments/", "_ts": 1752159180}, {"payPeriodId": "1090071086245119", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-08-18T00:00:00Z", "endDate": "2025-08-24T00:00:00Z", "submitByDate": "2025-09-02T00:00:00Z", "checkDate": "2025-09-04T00:00:00Z", "checkCount": 0, "id": "8732649c-a269-41d7-adf8-8b03b4853493", "companyId": "18163405", "type": "payperiod", "_rid": "NmJkAKiCbEcXPQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcXPQAAAAAAAA==/", "_etag": "\"9700d8cc-0000-0100-0000-686fd3cc0000\"", "_attachments": "attachments/", "_ts": 1752159180}, {"payPeriodId": "1090071268789349", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-08-25T00:00:00Z", "endDate": "2025-08-31T00:00:00Z", "submitByDate": "2025-09-09T00:00:00Z", "checkDate": "2025-09-11T00:00:00Z", "checkCount": 0, "id": "10064dc8-21a2-40e0-b403-3b6c3b1e3b83", "companyId": "18163405", "type": "payperiod", "_rid": "NmJkAKiCbEcYPQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcYPQAAAAAAAA==/", "_etag": "\"9700dacc-0000-0100-0000-686fd3cc0000\"", "_attachments": "attachments/", "_ts": 1752159180}, {"payPeriodId": "1090071438135725", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-09-01T00:00:00Z", "endDate": "2025-09-07T00:00:00Z", "submitByDate": "2025-09-16T00:00:00Z", "checkDate": "2025-09-18T00:00:00Z", "checkCount": 0, "id": "8d91c8ed-051e-4377-9a09-fc69afd0b6b4", "companyId": "18163405", "type": "payperiod", "_rid": "NmJkAKiCbEcZPQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcZPQAAAAAAAA==/", "_etag": "\"9700decc-0000-0100-0000-686fd3cc0000\"", "_attachments": "attachments/", "_ts": 1752159180}, {"payPeriodId": "1090071572020613", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-09-08T00:00:00Z", "endDate": "2025-09-14T00:00:00Z", "submitByDate": "2025-09-23T00:00:00Z", "checkDate": "2025-09-25T00:00:00Z", "checkCount": 0, "id": "e2521dc0-e3ef-47a9-a733-26a12137daf4", "companyId": "18163405", "type": "payperiod", "_rid": "NmJkAKiCbEcaPQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcaPQAAAAAAAA==/", "_etag": "\"9700e1cc-0000-0100-0000-686fd3cc0000\"", "_attachments": "attachments/", "_ts": 1752159180}, {"payPeriodId": "1090071712530223", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-09-15T00:00:00Z", "endDate": "2025-09-21T00:00:00Z", "submitByDate": "2025-09-30T00:00:00Z", "checkDate": "2025-10-02T00:00:00Z", "checkCount": 0, "id": "e4741fe9-2709-4d83-a7a1-c2e0e8868a89", "companyId": "18163405", "type": "payperiod", "_rid": "NmJkAKiCbEcbPQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcbPQAAAAAAAA==/", "_etag": "\"9700e6cc-0000-0100-0000-686fd3cc0000\"", "_attachments": "attachments/", "_ts": 1752159180}, {"payPeriodId": "1090071883375105", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-09-22T00:00:00Z", "endDate": "2025-09-28T00:00:00Z", "submitByDate": "2025-10-07T00:00:00Z", "checkDate": "2025-10-09T00:00:00Z", "checkCount": 0, "id": "3cb653e6-742b-43cd-9496-ea12e53dd5f8", "companyId": "18163405", "type": "payperiod", "_rid": "NmJkAKiCbEccPQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEccPQAAAAAAAA==/", "_etag": "\"9700e9cc-0000-0100-0000-686fd3cc0000\"", "_attachments": "attachments/", "_ts": 1752159180}, {"payPeriodId": "1090072062115427", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-09-29T00:00:00Z", "endDate": "2025-10-05T00:00:00Z", "submitByDate": "2025-10-14T00:00:00Z", "checkDate": "2025-10-16T00:00:00Z", "checkCount": 0, "id": "d624f047-1a08-4d4c-abf3-81e21180dad4", "companyId": "18163405", "type": "payperiod", "_rid": "NmJkAKiCbEcdPQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcdPQAAAAAAAA==/", "_etag": "\"9700eccc-0000-0100-0000-686fd3cc0000\"", "_attachments": "attachments/", "_ts": 1752159180}, {"payPeriodId": "1090066094508862", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2024-12-30T00:00:00Z", "endDate": "2025-01-05T00:00:00Z", "submitByDate": "2025-01-14T00:00:00Z", "checkDate": "2025-01-16T00:00:00Z", "checkCount": 8, "id": "107b4929-d1f3-4687-b170-3e194e00c37b", "companyId": "18163405", "type": "payperiod", "_rid": "NmJkAKiCbEcuPQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcuPQAAAAAAAA==/", "_etag": "\"970023cd-0000-0100-0000-686fd3ce0000\"", "_attachments": "attachments/", "_ts": 1752159182}, {"payPeriodId": "1090066270674400", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-01-06T00:00:00Z", "endDate": "2025-01-12T00:00:00Z", "submitByDate": "2025-01-21T00:00:00Z", "checkDate": "2025-01-23T00:00:00Z", "checkCount": 8, "id": "a2fc8b3f-a03b-474c-8cd4-9729b65aa5d9", "companyId": "18163405", "type": "payperiod", "_rid": "NmJkAKiCbEcvPQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcvPQAAAAAAAA==/", "_etag": "\"970029cd-0000-0100-0000-686fd3ce0000\"", "_attachments": "attachments/", "_ts": 1752159182}, {"payPeriodId": "1090066350207932", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-01-13T00:00:00Z", "endDate": "2025-01-19T00:00:00Z", "submitByDate": "2025-01-28T00:00:00Z", "checkDate": "2025-01-30T00:00:00Z", "checkCount": 7, "id": "7834248d-04e5-471f-baa7-36a7ad5ac2ec", "companyId": "18163405", "type": "payperiod", "_rid": "NmJkAKiCbEcwPQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcwPQAAAAAAAA==/", "_etag": "\"97002dcd-0000-0100-0000-686fd3ce0000\"", "_attachments": "attachments/", "_ts": 1752159182}, {"payPeriodId": "1090066543956486", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-01-20T00:00:00Z", "endDate": "2025-01-26T00:00:00Z", "submitByDate": "2025-02-04T00:00:00Z", "checkDate": "2025-02-06T00:00:00Z", "checkCount": 7, "id": "6d0812f3-5798-4e29-a471-7a5b6ac51edd", "companyId": "18163405", "type": "payperiod", "_rid": "NmJkAKiCbEcxPQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcxPQAAAAAAAA==/", "_etag": "\"970030cd-0000-0100-0000-686fd3ce0000\"", "_attachments": "attachments/", "_ts": 1752159182}, {"payPeriodId": "1090066639799565", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-01-27T00:00:00Z", "endDate": "2025-02-02T00:00:00Z", "submitByDate": "2025-02-11T00:00:00Z", "checkDate": "2025-02-13T00:00:00Z", "checkCount": 8, "id": "1fdba61e-dcd2-4b50-a69e-09fb2ad60ce7", "companyId": "18163405", "type": "payperiod", "_rid": "NmJkAKiCbEcyPQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcyPQAAAAAAAA==/", "_etag": "\"970035cd-0000-0100-0000-686fd3ce0000\"", "_attachments": "attachments/", "_ts": 1752159182}, {"payPeriodId": "1090066806511086", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-02-03T00:00:00Z", "endDate": "2025-02-09T00:00:00Z", "submitByDate": "2025-02-18T00:00:00Z", "checkDate": "2025-02-20T00:00:00Z", "checkCount": 6, "id": "80ee34df-127c-49c2-95c4-d419ba16f921", "companyId": "18163405", "type": "payperiod", "_rid": "NmJkAKiCbEczPQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEczPQAAAAAAAA==/", "_etag": "\"970038cd-0000-0100-0000-686fd3ce0000\"", "_attachments": "attachments/", "_ts": 1752159182}, {"payPeriodId": "1090066966094480", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-02-10T00:00:00Z", "endDate": "2025-02-16T00:00:00Z", "submitByDate": "2025-02-25T00:00:00Z", "checkDate": "2025-02-27T00:00:00Z", "checkCount": 6, "id": "90c1faf8-d37c-4b2a-8e9b-d418ca1489e9", "companyId": "18163405", "type": "payperiod", "_rid": "NmJkAKiCbEc0PQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc0PQAAAAAAAA==/", "_etag": "\"97003dcd-0000-0100-0000-686fd3ce0000\"", "_attachments": "attachments/", "_ts": 1752159182}, {"payPeriodId": "1090067102973566", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-02-17T00:00:00Z", "endDate": "2025-02-23T00:00:00Z", "submitByDate": "2025-03-04T00:00:00Z", "checkDate": "2025-03-06T00:00:00Z", "checkCount": 7, "id": "9aae49d9-dc8c-4139-9cb9-3c117a3b6c1f", "companyId": "18163405", "type": "payperiod", "_rid": "NmJkAKiCbEc1PQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc1PQAAAAAAAA==/", "_etag": "\"97003fcd-0000-0100-0000-686fd3ce0000\"", "_attachments": "attachments/", "_ts": 1752159182}, {"payPeriodId": "1090067314621089", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-02-24T00:00:00Z", "endDate": "2025-03-02T00:00:00Z", "submitByDate": "2025-03-11T00:00:00Z", "checkDate": "2025-03-13T00:00:00Z", "checkCount": 6, "id": "7a569791-df73-4bb1-93da-685ba207df23", "companyId": "18163405", "type": "payperiod", "_rid": "NmJkAKiCbEc2PQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc2PQAAAAAAAA==/", "_etag": "\"970044cd-0000-0100-0000-686fd3ce0000\"", "_attachments": "attachments/", "_ts": 1752159182}, {"payPeriodId": "1090067400500661", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-03-03T00:00:00Z", "endDate": "2025-03-09T00:00:00Z", "submitByDate": "2025-03-18T00:00:00Z", "checkDate": "2025-03-20T00:00:00Z", "checkCount": 7, "id": "f91ea3ab-af75-4582-81c3-f6edc86eb46e", "companyId": "18163405", "type": "payperiod", "_rid": "NmJkAKiCbEc3PQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc3PQAAAAAAAA==/", "_etag": "\"970046cd-0000-0100-0000-686fd3ce0000\"", "_attachments": "attachments/", "_ts": 1752159182}, {"payPeriodId": "1090067538931552", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-03-10T00:00:00Z", "endDate": "2025-03-16T00:00:00Z", "submitByDate": "2025-03-25T00:00:00Z", "checkDate": "2025-03-27T00:00:00Z", "checkCount": 7, "id": "8b66d679-729e-49ba-b388-ce6d1293503d", "companyId": "18163405", "type": "payperiod", "_rid": "NmJkAKiCbEc4PQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc4PQAAAAAAAA==/", "_etag": "\"97004acd-0000-0100-0000-686fd3ce0000\"", "_attachments": "attachments/", "_ts": 1752159182}, {"payPeriodId": "1090067725095628", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-03-17T00:00:00Z", "endDate": "2025-03-23T00:00:00Z", "submitByDate": "2025-04-01T00:00:00Z", "checkDate": "2025-04-03T00:00:00Z", "checkCount": 7, "id": "51b124b6-62f4-4ac0-9170-6fd8a447371b", "companyId": "18163405", "type": "payperiod", "_rid": "NmJkAKiCbEc5PQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc5PQAAAAAAAA==/", "_etag": "\"97004ecd-0000-0100-0000-686fd3ce0000\"", "_attachments": "attachments/", "_ts": 1752159182}, {"payPeriodId": "1090067934859555", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-03-24T00:00:00Z", "endDate": "2025-03-30T00:00:00Z", "submitByDate": "2025-04-08T00:00:00Z", "checkDate": "2025-04-10T00:00:00Z", "checkCount": 7, "id": "809dae4a-acc6-45e4-8154-9a2565b6d75b", "companyId": "18163405", "type": "payperiod", "_rid": "NmJkAKiCbEc6PQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc6PQAAAAAAAA==/", "_etag": "\"97004fcd-0000-0100-0000-686fd3cf0000\"", "_attachments": "attachments/", "_ts": 1752159183}, {"payPeriodId": "1090068060281591", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-03-31T00:00:00Z", "endDate": "2025-04-06T00:00:00Z", "submitByDate": "2025-04-15T00:00:00Z", "checkDate": "2025-04-17T00:00:00Z", "checkCount": 8, "id": "9ad967f6-2746-4ce7-b63b-91c4e452d56b", "companyId": "18163405", "type": "payperiod", "_rid": "NmJkAKiCbEc7PQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc7PQAAAAAAAA==/", "_etag": "\"970052cd-0000-0100-0000-686fd3cf0000\"", "_attachments": "attachments/", "_ts": 1752159183}, {"payPeriodId": "1090068204343531", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-04-07T00:00:00Z", "endDate": "2025-04-13T00:00:00Z", "submitByDate": "2025-04-22T00:00:00Z", "checkDate": "2025-04-24T00:00:00Z", "checkCount": 8, "id": "4d5a4771-0bbc-4ad2-8ae5-5a740403c851", "companyId": "18163405", "type": "payperiod", "_rid": "NmJkAKiCbEc8PQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc8PQAAAAAAAA==/", "_etag": "\"970056cd-0000-0100-0000-686fd3cf0000\"", "_attachments": "attachments/", "_ts": 1752159183}, {"payPeriodId": "1090068387796454", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-04-14T00:00:00Z", "endDate": "2025-04-20T00:00:00Z", "submitByDate": "2025-04-29T00:00:00Z", "checkDate": "2025-05-01T00:00:00Z", "checkCount": 9, "id": "dba28205-1e9a-432a-b140-8a01696490d2", "companyId": "18163405", "type": "payperiod", "_rid": "NmJkAKiCbEc9PQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc9PQAAAAAAAA==/", "_etag": "\"97005acd-0000-0100-0000-686fd3cf0000\"", "_attachments": "attachments/", "_ts": 1752159183}, {"payPeriodId": "1090068504515338", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-04-21T00:00:00Z", "endDate": "2025-04-27T00:00:00Z", "submitByDate": "2025-05-06T00:00:00Z", "checkDate": "2025-05-08T00:00:00Z", "checkCount": 9, "id": "c484b7af-685d-4340-94f8-00b1a726325b", "companyId": "18163405", "type": "payperiod", "_rid": "NmJkAKiCbEc+PQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc+PQAAAAAAAA==/", "_etag": "\"970060cd-0000-0100-0000-686fd3cf0000\"", "_attachments": "attachments/", "_ts": 1752159183}, {"payPeriodId": "1090068652955920", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-04-28T00:00:00Z", "endDate": "2025-05-04T00:00:00Z", "submitByDate": "2025-05-13T00:00:00Z", "checkDate": "2025-05-15T00:00:00Z", "checkCount": 9, "id": "dc59dc54-6a05-46a7-99ae-570a0ed5e5fb", "companyId": "18163405", "type": "payperiod", "_rid": "NmJkAKiCbEc-PQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc-PQAAAAAAAA==/", "_etag": "\"970066cd-0000-0100-0000-686fd3cf0000\"", "_attachments": "attachments/", "_ts": 1752159183}, {"payPeriodId": "1090068799232322", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-05-05T00:00:00Z", "endDate": "2025-05-11T00:00:00Z", "submitByDate": "2025-05-20T00:00:00Z", "checkDate": "2025-05-22T00:00:00Z", "checkCount": 9, "id": "42f961ce-6e4a-4e5e-a282-7ad9f578c90d", "companyId": "18163405", "type": "payperiod", "_rid": "NmJkAKiCbEdAPQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdAPQAAAAAAAA==/", "_etag": "\"970067cd-0000-0100-0000-686fd3cf0000\"", "_attachments": "attachments/", "_ts": 1752159183}, {"payPeriodId": "1090068967452091", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-05-12T00:00:00Z", "endDate": "2025-05-18T00:00:00Z", "submitByDate": "2025-05-27T00:00:00Z", "checkDate": "2025-05-29T00:00:00Z", "checkCount": 8, "id": "1c5d31fb-2513-45ef-ba93-81db12b70379", "companyId": "18163405", "type": "payperiod", "_rid": "NmJkAKiCbEdBPQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdBPQAAAAAAAA==/", "_etag": "\"970069cd-0000-0100-0000-686fd3cf0000\"", "_attachments": "attachments/", "_ts": 1752159183}, {"payPeriodId": "1090069136626585", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-05-19T00:00:00Z", "endDate": "2025-05-25T00:00:00Z", "submitByDate": "2025-06-03T00:00:00Z", "checkDate": "2025-06-05T00:00:00Z", "checkCount": 8, "id": "c2bf4f84-7997-47cb-8eed-b353aa2e482a", "companyId": "18163405", "type": "payperiod", "_rid": "NmJkAKiCbEdCPQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdCPQAAAAAAAA==/", "_etag": "\"97006dcd-0000-0100-0000-686fd3cf0000\"", "_attachments": "attachments/", "_ts": 1752159183}, {"payPeriodId": "1090069236974040", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-05-26T00:00:00Z", "endDate": "2025-06-01T00:00:00Z", "submitByDate": "2025-06-10T00:00:00Z", "checkDate": "2025-06-12T00:00:00Z", "checkCount": 8, "id": "b6471b12-c6a9-41b1-aabf-7bded44c52dd", "companyId": "18163405", "type": "payperiod", "_rid": "NmJkAKiCbEdDPQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdDPQAAAAAAAA==/", "_etag": "\"97006fcd-0000-0100-0000-686fd3cf0000\"", "_attachments": "attachments/", "_ts": 1752159183}, {"payPeriodId": "1090069412709288", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-06-02T00:00:00Z", "endDate": "2025-06-08T00:00:00Z", "submitByDate": "2025-06-16T00:00:00Z", "checkDate": "2025-06-18T00:00:00Z", "checkCount": 8, "id": "ccf3f059-b0ff-46e0-9952-9c8a9af2d4b0", "companyId": "18163405", "type": "payperiod", "_rid": "NmJkAKiCbEdEPQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdEPQAAAAAAAA==/", "_etag": "\"970071cd-0000-0100-0000-686fd3cf0000\"", "_attachments": "attachments/", "_ts": 1752159183}, {"payPeriodId": "1090069550373257", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-06-09T00:00:00Z", "endDate": "2025-06-15T00:00:00Z", "submitByDate": "2025-06-24T00:00:00Z", "checkDate": "2025-06-26T00:00:00Z", "checkCount": 8, "id": "75292494-3624-4215-8a3e-4e67b83c6180", "companyId": "18163405", "type": "payperiod", "_rid": "NmJkAKiCbEdFPQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdFPQAAAAAAAA==/", "_etag": "\"970077cd-0000-0100-0000-686fd3cf0000\"", "_attachments": "attachments/", "_ts": 1752159183}, {"payPeriodId": "1090069724338471", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-06-16T00:00:00Z", "endDate": "2025-06-22T00:00:00Z", "submitByDate": "2025-07-01T00:00:00Z", "checkDate": "2025-07-03T00:00:00Z", "checkCount": 8, "id": "8ad45e04-f55b-4218-8486-b33423b9cb81", "companyId": "18163405", "type": "payperiod", "_rid": "NmJkAKiCbEdGPQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdGPQAAAAAAAA==/", "_etag": "\"97007acd-0000-0100-0000-686fd3cf0000\"", "_attachments": "attachments/", "_ts": 1752159183}, {"payPeriodId": "1090069895278376", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-06-23T00:00:00Z", "endDate": "2025-06-29T00:00:00Z", "submitByDate": "2025-07-08T00:00:00Z", "checkDate": "2025-07-10T00:00:00Z", "checkCount": 0, "id": "f7d58127-861e-483d-994c-699c0a07f15b", "companyId": "18163405", "type": "payperiod", "_rid": "NmJkAKiCbEdHPQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdHPQAAAAAAAA==/", "_etag": "\"97007dcd-0000-0100-0000-686fd3cf0000\"", "_attachments": "attachments/", "_ts": 1752159183}, {"payPeriodId": "1090070042475209", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-06-30T00:00:00Z", "endDate": "2025-07-06T00:00:00Z", "submitByDate": "2025-07-15T00:00:00Z", "checkDate": "2025-07-17T00:00:00Z", "checkCount": 0, "id": "e27e40b0-8d33-4009-b4e1-7db5feb0d1c5", "companyId": "18163405", "type": "payperiod", "_rid": "NmJkAKiCbEdIPQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdIPQAAAAAAAA==/", "_etag": "\"970080cd-0000-0100-0000-686fd3d00000\"", "_attachments": "attachments/", "_ts": 1752159184}, {"payPeriodId": "1090070177690660", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-07-07T00:00:00Z", "endDate": "2025-07-13T00:00:00Z", "submitByDate": "2025-07-22T00:00:00Z", "checkDate": "2025-07-24T00:00:00Z", "checkCount": 0, "id": "1ed305f9-5b67-4514-b1a6-39515ab7ae2b", "companyId": "18163405", "type": "payperiod", "_rid": "NmJkAKiCbEdJPQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdJPQAAAAAAAA==/", "_etag": "\"970086cd-0000-0100-0000-686fd3d00000\"", "_attachments": "attachments/", "_ts": 1752159184}, {"payPeriodId": "1090070338020687", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-07-14T00:00:00Z", "endDate": "2025-07-20T00:00:00Z", "submitByDate": "2025-07-29T00:00:00Z", "checkDate": "2025-07-31T00:00:00Z", "checkCount": 0, "id": "9a2c6b99-e5cf-41d1-a513-3f25536351e0", "companyId": "18163405", "type": "payperiod", "_rid": "NmJkAKiCbEdKPQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdKPQAAAAAAAA==/", "_etag": "\"970089cd-0000-0100-0000-686fd3d00000\"", "_attachments": "attachments/", "_ts": 1752159184}, {"payPeriodId": "1090070482796443", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-07-21T00:00:00Z", "endDate": "2025-07-27T00:00:00Z", "submitByDate": "2025-08-05T00:00:00Z", "checkDate": "2025-08-07T00:00:00Z", "checkCount": 0, "id": "992ae12f-0569-45a2-bc98-902e51c42bd3", "companyId": "18163405", "type": "payperiod", "_rid": "NmJkAKiCbEdLPQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdLPQAAAAAAAA==/", "_etag": "\"97008bcd-0000-0100-0000-686fd3d00000\"", "_attachments": "attachments/", "_ts": 1752159184}, {"payPeriodId": "1090070664859800", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-07-28T00:00:00Z", "endDate": "2025-08-03T00:00:00Z", "submitByDate": "2025-08-12T00:00:00Z", "checkDate": "2025-08-14T00:00:00Z", "checkCount": 0, "id": "042fb33c-6e01-449a-b575-67c8d2f1b1ce", "companyId": "18163405", "type": "payperiod", "_rid": "NmJkAKiCbEdMPQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdMPQAAAAAAAA==/", "_etag": "\"97008ecd-0000-0100-0000-686fd3d00000\"", "_attachments": "attachments/", "_ts": 1752159184}, {"payPeriodId": "1090070819936475", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-08-04T00:00:00Z", "endDate": "2025-08-10T00:00:00Z", "submitByDate": "2025-08-19T00:00:00Z", "checkDate": "2025-08-21T00:00:00Z", "checkCount": 0, "id": "7ac89ea7-4f43-41a9-9bd4-276c18bb15f8", "companyId": "18163405", "type": "payperiod", "_rid": "NmJkAKiCbEdNPQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdNPQAAAAAAAA==/", "_etag": "\"970095cd-0000-0100-0000-686fd3d00000\"", "_attachments": "attachments/", "_ts": 1752159184}, {"payPeriodId": "1090070938909295", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-08-11T00:00:00Z", "endDate": "2025-08-17T00:00:00Z", "submitByDate": "2025-08-26T00:00:00Z", "checkDate": "2025-08-28T00:00:00Z", "checkCount": 0, "id": "9dd0c6a2-b6f4-4f25-87ee-faa2c9e9e92f", "companyId": "18163405", "type": "payperiod", "_rid": "NmJkAKiCbEdOPQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdOPQAAAAAAAA==/", "_etag": "\"970097cd-0000-0100-0000-686fd3d00000\"", "_attachments": "attachments/", "_ts": 1752159184}, {"payPeriodId": "1090071086245119", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-08-18T00:00:00Z", "endDate": "2025-08-24T00:00:00Z", "submitByDate": "2025-09-02T00:00:00Z", "checkDate": "2025-09-04T00:00:00Z", "checkCount": 0, "id": "2065d86e-7f3c-4622-8c88-c892237e88d2", "companyId": "18163405", "type": "payperiod", "_rid": "NmJkAKiCbEdPPQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdPPQAAAAAAAA==/", "_etag": "\"970098cd-0000-0100-0000-686fd3d00000\"", "_attachments": "attachments/", "_ts": 1752159184}, {"payPeriodId": "1090071268789349", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-08-25T00:00:00Z", "endDate": "2025-08-31T00:00:00Z", "submitByDate": "2025-09-09T00:00:00Z", "checkDate": "2025-09-11T00:00:00Z", "checkCount": 0, "id": "515d9ce9-bfb1-4730-b3ab-b809a92f7e24", "companyId": "18163405", "type": "payperiod", "_rid": "NmJkAKiCbEdQPQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdQPQAAAAAAAA==/", "_etag": "\"97009bcd-0000-0100-0000-686fd3d00000\"", "_attachments": "attachments/", "_ts": 1752159184}, {"payPeriodId": "1090071438135725", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-09-01T00:00:00Z", "endDate": "2025-09-07T00:00:00Z", "submitByDate": "2025-09-16T00:00:00Z", "checkDate": "2025-09-18T00:00:00Z", "checkCount": 0, "id": "3f28784d-7ce8-468d-8356-b7b059013506", "companyId": "18163405", "type": "payperiod", "_rid": "NmJkAKiCbEdRPQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdRPQAAAAAAAA==/", "_etag": "\"97009ecd-0000-0100-0000-686fd3d00000\"", "_attachments": "attachments/", "_ts": 1752159184}, {"payPeriodId": "1090071572020613", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-09-08T00:00:00Z", "endDate": "2025-09-14T00:00:00Z", "submitByDate": "2025-09-23T00:00:00Z", "checkDate": "2025-09-25T00:00:00Z", "checkCount": 0, "id": "bb09bc6d-2794-44e5-b74b-7ed3f0dace33", "companyId": "18163405", "type": "payperiod", "_rid": "NmJkAKiCbEdSPQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdSPQAAAAAAAA==/", "_etag": "\"9700a1cd-0000-0100-0000-686fd3d00000\"", "_attachments": "attachments/", "_ts": 1752159184}, {"payPeriodId": "1090071712530223", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-09-15T00:00:00Z", "endDate": "2025-09-21T00:00:00Z", "submitByDate": "2025-09-30T00:00:00Z", "checkDate": "2025-10-02T00:00:00Z", "checkCount": 0, "id": "c525495f-5c4b-479d-86b4-b7903874bb45", "companyId": "18163405", "type": "payperiod", "_rid": "NmJkAKiCbEdTPQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdTPQAAAAAAAA==/", "_etag": "\"9700a3cd-0000-0100-0000-686fd3d00000\"", "_attachments": "attachments/", "_ts": 1752159184}, {"payPeriodId": "1090071883375105", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-09-22T00:00:00Z", "endDate": "2025-09-28T00:00:00Z", "submitByDate": "2025-10-07T00:00:00Z", "checkDate": "2025-10-09T00:00:00Z", "checkCount": 0, "id": "35b249f5-7fc7-4d7f-a011-f881a0b32cd0", "companyId": "18163405", "type": "payperiod", "_rid": "NmJkAKiCbEdUPQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdUPQAAAAAAAA==/", "_etag": "\"9700a8cd-0000-0100-0000-686fd3d00000\"", "_attachments": "attachments/", "_ts": 1752159184}, {"payPeriodId": "1090072062115427", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-09-29T00:00:00Z", "endDate": "2025-10-05T00:00:00Z", "submitByDate": "2025-10-14T00:00:00Z", "checkDate": "2025-10-16T00:00:00Z", "checkCount": 0, "id": "09c78902-9a01-42a7-b79a-29c2bd6abbdb", "companyId": "18163405", "type": "payperiod", "_rid": "NmJkAKiCbEdVPQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdVPQAAAAAAAA==/", "_etag": "\"9700accd-0000-0100-0000-686fd3d00000\"", "_attachments": "attachments/", "_ts": 1752159184}, {"payPeriodId": "1090066094508862", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2024-12-30T00:00:00Z", "endDate": "2025-01-05T00:00:00Z", "submitByDate": "2025-01-14T00:00:00Z", "checkDate": "2025-01-16T00:00:00Z", "checkCount": 8, "id": "02408ae6-9136-4d08-a337-6a028bdf59fd", "companyId": "18163405", "type": "payperiod", "_rid": "NmJkAKiCbEcjNgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcjNgEAAAAAAA==/", "_etag": "\"9e00ce04-0000-0100-0000-686ffa570000\"", "_attachments": "attachments/", "_ts": 1752169047}, {"payPeriodId": "1090066270674400", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-01-06T00:00:00Z", "endDate": "2025-01-12T00:00:00Z", "submitByDate": "2025-01-21T00:00:00Z", "checkDate": "2025-01-23T00:00:00Z", "checkCount": 8, "id": "0c99d9c4-10e5-4d7d-b936-c686aadf8f03", "companyId": "18163405", "type": "payperiod", "_rid": "NmJkAKiCbEckNgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEckNgEAAAAAAA==/", "_etag": "\"9e00cf04-0000-0100-0000-686ffa570000\"", "_attachments": "attachments/", "_ts": 1752169047}, {"payPeriodId": "1090066350207932", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-01-13T00:00:00Z", "endDate": "2025-01-19T00:00:00Z", "submitByDate": "2025-01-28T00:00:00Z", "checkDate": "2025-01-30T00:00:00Z", "checkCount": 7, "id": "310ffa79-7c96-49fb-a638-c6e22aefb534", "companyId": "18163405", "type": "payperiod", "_rid": "NmJkAKiCbEclNgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEclNgEAAAAAAA==/", "_etag": "\"9e00d104-0000-0100-0000-686ffa570000\"", "_attachments": "attachments/", "_ts": 1752169047}, {"payPeriodId": "1090066543956486", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-01-20T00:00:00Z", "endDate": "2025-01-26T00:00:00Z", "submitByDate": "2025-02-04T00:00:00Z", "checkDate": "2025-02-06T00:00:00Z", "checkCount": 7, "id": "e24f6615-1a5b-4d83-875b-2986d78317f6", "companyId": "18163405", "type": "payperiod", "_rid": "NmJkAKiCbEcmNgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcmNgEAAAAAAA==/", "_etag": "\"9e00d404-0000-0100-0000-686ffa570000\"", "_attachments": "attachments/", "_ts": 1752169047}, {"payPeriodId": "1090066639799565", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-01-27T00:00:00Z", "endDate": "2025-02-02T00:00:00Z", "submitByDate": "2025-02-11T00:00:00Z", "checkDate": "2025-02-13T00:00:00Z", "checkCount": 8, "id": "25e4a52b-55b7-4ce1-aa09-a39e5ef38ed9", "companyId": "18163405", "type": "payperiod", "_rid": "NmJkAKiCbEcnNgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcnNgEAAAAAAA==/", "_etag": "\"9e00d504-0000-0100-0000-686ffa570000\"", "_attachments": "attachments/", "_ts": 1752169047}, {"payPeriodId": "1090066806511086", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-02-03T00:00:00Z", "endDate": "2025-02-09T00:00:00Z", "submitByDate": "2025-02-18T00:00:00Z", "checkDate": "2025-02-20T00:00:00Z", "checkCount": 6, "id": "fe39a35b-a638-4d3e-adca-3cecc04cb489", "companyId": "18163405", "type": "payperiod", "_rid": "NmJkAKiCbEcoNgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcoNgEAAAAAAA==/", "_etag": "\"9e00d704-0000-0100-0000-686ffa570000\"", "_attachments": "attachments/", "_ts": 1752169047}, {"payPeriodId": "1090066966094480", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-02-10T00:00:00Z", "endDate": "2025-02-16T00:00:00Z", "submitByDate": "2025-02-25T00:00:00Z", "checkDate": "2025-02-27T00:00:00Z", "checkCount": 6, "id": "cc7fe875-7e28-47c5-a0fd-748d5defc2ec", "companyId": "18163405", "type": "payperiod", "_rid": "NmJkAKiCbEcpNgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcpNgEAAAAAAA==/", "_etag": "\"9e00dc04-0000-0100-0000-686ffa570000\"", "_attachments": "attachments/", "_ts": 1752169047}, {"payPeriodId": "1090067102973566", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-02-17T00:00:00Z", "endDate": "2025-02-23T00:00:00Z", "submitByDate": "2025-03-04T00:00:00Z", "checkDate": "2025-03-06T00:00:00Z", "checkCount": 7, "id": "45f628ae-6ef4-475f-a2ef-b5acfe8f57e9", "companyId": "18163405", "type": "payperiod", "_rid": "NmJkAKiCbEcqNgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcqNgEAAAAAAA==/", "_etag": "\"9e00dd04-0000-0100-0000-686ffa570000\"", "_attachments": "attachments/", "_ts": 1752169047}, {"payPeriodId": "1090067314621089", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-02-24T00:00:00Z", "endDate": "2025-03-02T00:00:00Z", "submitByDate": "2025-03-11T00:00:00Z", "checkDate": "2025-03-13T00:00:00Z", "checkCount": 6, "id": "f9127710-57a6-4ff2-91bc-2764d2e6746d", "companyId": "18163405", "type": "payperiod", "_rid": "NmJkAKiCbEcrNgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcrNgEAAAAAAA==/", "_etag": "\"9e00df04-0000-0100-0000-686ffa570000\"", "_attachments": "attachments/", "_ts": 1752169047}, {"payPeriodId": "1090067400500661", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-03-03T00:00:00Z", "endDate": "2025-03-09T00:00:00Z", "submitByDate": "2025-03-18T00:00:00Z", "checkDate": "2025-03-20T00:00:00Z", "checkCount": 7, "id": "8c18909e-ae88-4727-9cac-c5a7aa014e03", "companyId": "18163405", "type": "payperiod", "_rid": "NmJkAKiCbEcsNgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcsNgEAAAAAAA==/", "_etag": "\"9e00e204-0000-0100-0000-686ffa570000\"", "_attachments": "attachments/", "_ts": 1752169047}, {"payPeriodId": "1090067538931552", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-03-10T00:00:00Z", "endDate": "2025-03-16T00:00:00Z", "submitByDate": "2025-03-25T00:00:00Z", "checkDate": "2025-03-27T00:00:00Z", "checkCount": 7, "id": "efde1fb4-e4dd-4bec-a99c-18cd43860205", "companyId": "18163405", "type": "payperiod", "_rid": "NmJkAKiCbEctNgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEctNgEAAAAAAA==/", "_etag": "\"9e00e504-0000-0100-0000-686ffa570000\"", "_attachments": "attachments/", "_ts": 1752169047}, {"payPeriodId": "1090067725095628", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-03-17T00:00:00Z", "endDate": "2025-03-23T00:00:00Z", "submitByDate": "2025-04-01T00:00:00Z", "checkDate": "2025-04-03T00:00:00Z", "checkCount": 0, "id": "a283ea1b-1eb5-4499-9d01-a0cef7ed67a5", "companyId": "18163405", "type": "payperiod", "_rid": "NmJkAKiCbEcuNgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcuNgEAAAAAAA==/", "_etag": "\"9e00e704-0000-0100-0000-686ffa570000\"", "_attachments": "attachments/", "_ts": 1752169047}, {"payPeriodId": "1090067934859555", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-03-24T00:00:00Z", "endDate": "2025-03-30T00:00:00Z", "submitByDate": "2025-04-08T00:00:00Z", "checkDate": "2025-04-10T00:00:00Z", "checkCount": 0, "id": "da2e590e-ed32-4574-8c9c-218d85fb5e3b", "companyId": "18163405", "type": "payperiod", "_rid": "NmJkAKiCbEcvNgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcvNgEAAAAAAA==/", "_etag": "\"9e00ea04-0000-0100-0000-686ffa580000\"", "_attachments": "attachments/", "_ts": 1752169048}, {"payPeriodId": "1090068060281591", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-03-31T00:00:00Z", "endDate": "2025-04-06T00:00:00Z", "submitByDate": "2025-04-15T00:00:00Z", "checkDate": "2025-04-17T00:00:00Z", "checkCount": 0, "id": "df85625d-5ae5-4e37-8a0a-cb16de34f022", "companyId": "18163405", "type": "payperiod", "_rid": "NmJkAKiCbEcwNgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcwNgEAAAAAAA==/", "_etag": "\"9e00ee04-0000-0100-0000-686ffa580000\"", "_attachments": "attachments/", "_ts": 1752169048}, {"payPeriodId": "1090068204343531", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-04-07T00:00:00Z", "endDate": "2025-04-13T00:00:00Z", "submitByDate": "2025-04-22T00:00:00Z", "checkDate": "2025-04-24T00:00:00Z", "checkCount": 0, "id": "b5f87525-513e-4e06-8091-7363b0c72a96", "companyId": "18163405", "type": "payperiod", "_rid": "NmJkAKiCbEcxNgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcxNgEAAAAAAA==/", "_etag": "\"9e00f304-0000-0100-0000-686ffa580000\"", "_attachments": "attachments/", "_ts": 1752169048}, {"payPeriodId": "1090068387796454", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-04-14T00:00:00Z", "endDate": "2025-04-20T00:00:00Z", "submitByDate": "2025-04-29T00:00:00Z", "checkDate": "2025-05-01T00:00:00Z", "checkCount": 0, "id": "1ecf00d5-f060-4546-9490-d8f71ec3bbe3", "companyId": "18163405", "type": "payperiod", "_rid": "NmJkAKiCbEcyNgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcyNgEAAAAAAA==/", "_etag": "\"9e00f704-0000-0100-0000-686ffa580000\"", "_attachments": "attachments/", "_ts": 1752169048}, {"payPeriodId": "1090068504515338", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-04-21T00:00:00Z", "endDate": "2025-04-27T00:00:00Z", "submitByDate": "2025-05-06T00:00:00Z", "checkDate": "2025-05-08T00:00:00Z", "checkCount": 0, "id": "0d14a4ea-58cc-4fc6-be74-2b456b795aa9", "companyId": "18163405", "type": "payperiod", "_rid": "NmJkAKiCbEczNgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEczNgEAAAAAAA==/", "_etag": "\"9e00fb04-0000-0100-0000-686ffa580000\"", "_attachments": "attachments/", "_ts": 1752169048}, {"payPeriodId": "1090068652955920", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-04-28T00:00:00Z", "endDate": "2025-05-04T00:00:00Z", "submitByDate": "2025-05-13T00:00:00Z", "checkDate": "2025-05-15T00:00:00Z", "checkCount": 0, "id": "03b24724-b6e1-4232-a49c-d31132df865c", "companyId": "18163405", "type": "payperiod", "_rid": "NmJkAKiCbEc0NgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc0NgEAAAAAAA==/", "_etag": "\"9e00fc04-0000-0100-0000-686ffa580000\"", "_attachments": "attachments/", "_ts": 1752169048}, {"payPeriodId": "1090068799232322", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-05-05T00:00:00Z", "endDate": "2025-05-11T00:00:00Z", "submitByDate": "2025-05-20T00:00:00Z", "checkDate": "2025-05-22T00:00:00Z", "checkCount": 0, "id": "1505e26b-d6df-486f-9b81-1b57ea08a495", "companyId": "18163405", "type": "payperiod", "_rid": "NmJkAKiCbEc1NgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc1NgEAAAAAAA==/", "_etag": "\"9e000005-0000-0100-0000-686ffa580000\"", "_attachments": "attachments/", "_ts": 1752169048}, {"payPeriodId": "1090068967452091", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-05-12T00:00:00Z", "endDate": "2025-05-18T00:00:00Z", "submitByDate": "2025-05-27T00:00:00Z", "checkDate": "2025-05-29T00:00:00Z", "checkCount": 0, "id": "aaabbce8-a16c-46bd-8fa1-c1f4562e2eec", "companyId": "18163405", "type": "payperiod", "_rid": "NmJkAKiCbEc2NgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc2NgEAAAAAAA==/", "_etag": "\"9e000405-0000-0100-0000-686ffa580000\"", "_attachments": "attachments/", "_ts": 1752169048}, {"payPeriodId": "1090069136626585", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-05-19T00:00:00Z", "endDate": "2025-05-25T00:00:00Z", "submitByDate": "2025-06-03T00:00:00Z", "checkDate": "2025-06-05T00:00:00Z", "checkCount": 0, "id": "cff95d0c-88fe-4955-ac76-86409d475cf0", "companyId": "18163405", "type": "payperiod", "_rid": "NmJkAKiCbEc3NgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc3NgEAAAAAAA==/", "_etag": "\"9e000905-0000-0100-0000-686ffa580000\"", "_attachments": "attachments/", "_ts": 1752169048}, {"payPeriodId": "1090069236974040", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-05-26T00:00:00Z", "endDate": "2025-06-01T00:00:00Z", "submitByDate": "2025-06-10T00:00:00Z", "checkDate": "2025-06-12T00:00:00Z", "checkCount": 0, "id": "72e6bbe5-90d5-4f02-aad1-3279afc83df6", "companyId": "18163405", "type": "payperiod", "_rid": "NmJkAKiCbEc4NgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc4NgEAAAAAAA==/", "_etag": "\"9e000c05-0000-0100-0000-686ffa580000\"", "_attachments": "attachments/", "_ts": 1752169048}, {"payPeriodId": "1090069412709288", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-06-02T00:00:00Z", "endDate": "2025-06-08T00:00:00Z", "submitByDate": "2025-06-16T00:00:00Z", "checkDate": "2025-06-18T00:00:00Z", "checkCount": 0, "id": "84e5039c-f7b9-47ed-ba21-c6ee1b9e0888", "companyId": "18163405", "type": "payperiod", "_rid": "NmJkAKiCbEc5NgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc5NgEAAAAAAA==/", "_etag": "\"9e001105-0000-0100-0000-686ffa580000\"", "_attachments": "attachments/", "_ts": 1752169048}, {"payPeriodId": "1090069550373257", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-06-09T00:00:00Z", "endDate": "2025-06-15T00:00:00Z", "submitByDate": "2025-06-24T00:00:00Z", "checkDate": "2025-06-26T00:00:00Z", "checkCount": 0, "id": "e09a2b06-feb6-43c9-92c3-962b27dabb9f", "companyId": "18163405", "type": "payperiod", "_rid": "NmJkAKiCbEc6NgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc6NgEAAAAAAA==/", "_etag": "\"9e001305-0000-0100-0000-686ffa580000\"", "_attachments": "attachments/", "_ts": 1752169048}, {"payPeriodId": "1090069724338471", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-06-16T00:00:00Z", "endDate": "2025-06-22T00:00:00Z", "submitByDate": "2025-07-01T00:00:00Z", "checkDate": "2025-07-03T00:00:00Z", "checkCount": 0, "id": "fece77bd-f3f2-4119-88a6-7daffd7ad035", "companyId": "18163405", "type": "payperiod", "_rid": "NmJkAKiCbEc7NgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc7NgEAAAAAAA==/", "_etag": "\"9e001505-0000-0100-0000-686ffa580000\"", "_attachments": "attachments/", "_ts": 1752169048}, {"payPeriodId": "1090069895278376", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-06-23T00:00:00Z", "endDate": "2025-06-29T00:00:00Z", "submitByDate": "2025-07-08T00:00:00Z", "checkDate": "2025-07-10T00:00:00Z", "checkCount": 0, "id": "67a02a34-681b-4710-935a-09a5f7ac23e7", "companyId": "18163405", "type": "payperiod", "_rid": "NmJkAKiCbEc8NgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc8NgEAAAAAAA==/", "_etag": "\"9e001605-0000-0100-0000-686ffa590000\"", "_attachments": "attachments/", "_ts": 1752169049}, {"payPeriodId": "1090070042475209", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-06-30T00:00:00Z", "endDate": "2025-07-06T00:00:00Z", "submitByDate": "2025-07-15T00:00:00Z", "checkDate": "2025-07-17T00:00:00Z", "checkCount": 0, "id": "e287433f-7c76-4462-bc6b-6890423272ca", "companyId": "18163405", "type": "payperiod", "_rid": "NmJkAKiCbEc9NgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc9NgEAAAAAAA==/", "_etag": "\"9e001805-0000-0100-0000-686ffa590000\"", "_attachments": "attachments/", "_ts": 1752169049}, {"payPeriodId": "1090070177690660", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-07-07T00:00:00Z", "endDate": "2025-07-13T00:00:00Z", "submitByDate": "2025-07-22T00:00:00Z", "checkDate": "2025-07-24T00:00:00Z", "checkCount": 0, "id": "47387d55-0bc7-4ed4-a995-b4abb92a5471", "companyId": "18163405", "type": "payperiod", "_rid": "NmJkAKiCbEc+NgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc+NgEAAAAAAA==/", "_etag": "\"9e001905-0000-0100-0000-686ffa590000\"", "_attachments": "attachments/", "_ts": 1752169049}, {"payPeriodId": "1090070338020687", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-07-14T00:00:00Z", "endDate": "2025-07-20T00:00:00Z", "submitByDate": "2025-07-29T00:00:00Z", "checkDate": "2025-07-31T00:00:00Z", "checkCount": 0, "id": "008904e2-c150-45a7-aebb-934550dc9386", "companyId": "18163405", "type": "payperiod", "_rid": "NmJkAKiCbEc-NgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc-NgEAAAAAAA==/", "_etag": "\"9e001b05-0000-0100-0000-686ffa590000\"", "_attachments": "attachments/", "_ts": 1752169049}, {"payPeriodId": "1090070482796443", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-07-21T00:00:00Z", "endDate": "2025-07-27T00:00:00Z", "submitByDate": "2025-08-05T00:00:00Z", "checkDate": "2025-08-07T00:00:00Z", "checkCount": 0, "id": "f226f18f-ca84-4d8f-884d-e84ad7d17289", "companyId": "18163405", "type": "payperiod", "_rid": "NmJkAKiCbEdANgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdANgEAAAAAAA==/", "_etag": "\"9e001e05-0000-0100-0000-686ffa590000\"", "_attachments": "attachments/", "_ts": 1752169049}, {"payPeriodId": "1090070664859800", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-07-28T00:00:00Z", "endDate": "2025-08-03T00:00:00Z", "submitByDate": "2025-08-12T00:00:00Z", "checkDate": "2025-08-14T00:00:00Z", "checkCount": 0, "id": "7800b12c-347b-45fe-a787-0628a8f8d99a", "companyId": "18163405", "type": "payperiod", "_rid": "NmJkAKiCbEdBNgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdBNgEAAAAAAA==/", "_etag": "\"9e002005-0000-0100-0000-686ffa590000\"", "_attachments": "attachments/", "_ts": 1752169049}, {"payPeriodId": "1090070819936475", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-08-04T00:00:00Z", "endDate": "2025-08-10T00:00:00Z", "submitByDate": "2025-08-19T00:00:00Z", "checkDate": "2025-08-21T00:00:00Z", "checkCount": 0, "id": "3d86c109-23fd-4e32-b7ef-78d1949cb5cb", "companyId": "18163405", "type": "payperiod", "_rid": "NmJkAKiCbEdCNgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdCNgEAAAAAAA==/", "_etag": "\"9e002305-0000-0100-0000-686ffa590000\"", "_attachments": "attachments/", "_ts": 1752169049}, {"payPeriodId": "1090070938909295", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-08-11T00:00:00Z", "endDate": "2025-08-17T00:00:00Z", "submitByDate": "2025-08-26T00:00:00Z", "checkDate": "2025-08-28T00:00:00Z", "checkCount": 0, "id": "0e4b80c5-86af-4c10-b335-f5f0cbca0bb7", "companyId": "18163405", "type": "payperiod", "_rid": "NmJkAKiCbEdDNgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdDNgEAAAAAAA==/", "_etag": "\"9e002505-0000-0100-0000-686ffa590000\"", "_attachments": "attachments/", "_ts": 1752169049}, {"payPeriodId": "1090071086245119", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-08-18T00:00:00Z", "endDate": "2025-08-24T00:00:00Z", "submitByDate": "2025-09-02T00:00:00Z", "checkDate": "2025-09-04T00:00:00Z", "checkCount": 0, "id": "4cc86dae-1ab0-4d80-8bbb-cfc199cdc4c0", "companyId": "18163405", "type": "payperiod", "_rid": "NmJkAKiCbEdENgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdENgEAAAAAAA==/", "_etag": "\"9e002705-0000-0100-0000-686ffa590000\"", "_attachments": "attachments/", "_ts": 1752169049}, {"payPeriodId": "1090071268789349", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-08-25T00:00:00Z", "endDate": "2025-08-31T00:00:00Z", "submitByDate": "2025-09-09T00:00:00Z", "checkDate": "2025-09-11T00:00:00Z", "checkCount": 0, "id": "1f022278-b928-480a-a46a-4504013a4baf", "companyId": "18163405", "type": "payperiod", "_rid": "NmJkAKiCbEdFNgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdFNgEAAAAAAA==/", "_etag": "\"9e002c05-0000-0100-0000-686ffa590000\"", "_attachments": "attachments/", "_ts": 1752169049}, {"payPeriodId": "1090071438135725", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-09-01T00:00:00Z", "endDate": "2025-09-07T00:00:00Z", "submitByDate": "2025-09-16T00:00:00Z", "checkDate": "2025-09-18T00:00:00Z", "checkCount": 0, "id": "43752f50-5588-4060-9bd5-5cc9ae16da55", "companyId": "18163405", "type": "payperiod", "_rid": "NmJkAKiCbEdGNgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdGNgEAAAAAAA==/", "_etag": "\"9e003005-0000-0100-0000-686ffa590000\"", "_attachments": "attachments/", "_ts": 1752169049}, {"payPeriodId": "1090071572020613", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-09-08T00:00:00Z", "endDate": "2025-09-14T00:00:00Z", "submitByDate": "2025-09-23T00:00:00Z", "checkDate": "2025-09-25T00:00:00Z", "checkCount": 0, "id": "757108b3-4749-4e57-89de-f357362b108f", "companyId": "18163405", "type": "payperiod", "_rid": "NmJkAKiCbEdHNgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdHNgEAAAAAAA==/", "_etag": "\"9e003305-0000-0100-0000-686ffa590000\"", "_attachments": "attachments/", "_ts": 1752169049}, {"payPeriodId": "1090071712530223", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-09-15T00:00:00Z", "endDate": "2025-09-21T00:00:00Z", "submitByDate": "2025-09-30T00:00:00Z", "checkDate": "2025-10-02T00:00:00Z", "checkCount": 0, "id": "1dc69e07-4a31-4905-b121-c278449f5fa1", "companyId": "18163405", "type": "payperiod", "_rid": "NmJkAKiCbEdINgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdINgEAAAAAAA==/", "_etag": "\"9e003705-0000-0100-0000-686ffa590000\"", "_attachments": "attachments/", "_ts": 1752169049}, {"payPeriodId": "1090071883375105", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-09-22T00:00:00Z", "endDate": "2025-09-28T00:00:00Z", "submitByDate": "2025-10-07T00:00:00Z", "checkDate": "2025-10-09T00:00:00Z", "checkCount": 0, "id": "5fc6968b-9f70-43d2-8834-d8646180f760", "companyId": "18163405", "type": "payperiod", "_rid": "NmJkAKiCbEdJNgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdJNgEAAAAAAA==/", "_etag": "\"9e003a05-0000-0100-0000-686ffa5a0000\"", "_attachments": "attachments/", "_ts": 1752169050}, {"payPeriodId": "1090072062115427", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-09-29T00:00:00Z", "endDate": "2025-10-05T00:00:00Z", "submitByDate": "2025-10-14T00:00:00Z", "checkDate": "2025-10-16T00:00:00Z", "checkCount": 0, "id": "a97b5667-b1bf-4eb5-8f9b-a74a9b69591e", "companyId": "18163405", "type": "payperiod", "_rid": "NmJkAKiCbEdKNgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdKNgEAAAAAAA==/", "_etag": "\"9e003b05-0000-0100-0000-686ffa5a0000\"", "_attachments": "attachments/", "_ts": 1752169050}, {"payPeriodId": "1090066094508862", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2024-12-30T00:00:00Z", "endDate": "2025-01-05T00:00:00Z", "submitByDate": "2025-01-14T00:00:00Z", "checkDate": "2025-01-16T00:00:00Z", "checkCount": 8, "id": "914a358c-8def-45af-a8c1-daec7b0ed080", "companyId": "18163405", "type": "payperiod", "_rid": "NmJkAKiCbEdbNgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdbNgEAAAAAAA==/", "_etag": "\"9e006205-0000-0100-0000-686ffa5b0000\"", "_attachments": "attachments/", "_ts": 1752169051}, {"payPeriodId": "1090066270674400", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-01-06T00:00:00Z", "endDate": "2025-01-12T00:00:00Z", "submitByDate": "2025-01-21T00:00:00Z", "checkDate": "2025-01-23T00:00:00Z", "checkCount": 8, "id": "715bc550-33cb-42ae-aa7b-b2e9c9091ba6", "companyId": "18163405", "type": "payperiod", "_rid": "NmJkAKiCbEdcNgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdcNgEAAAAAAA==/", "_etag": "\"9e006405-0000-0100-0000-686ffa5b0000\"", "_attachments": "attachments/", "_ts": 1752169051}, {"payPeriodId": "1090066350207932", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-01-13T00:00:00Z", "endDate": "2025-01-19T00:00:00Z", "submitByDate": "2025-01-28T00:00:00Z", "checkDate": "2025-01-30T00:00:00Z", "checkCount": 7, "id": "e9ce38e7-9a8f-49f9-9f7a-74f1d65377dd", "companyId": "18163405", "type": "payperiod", "_rid": "NmJkAKiCbEddNgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEddNgEAAAAAAA==/", "_etag": "\"9e006505-0000-0100-0000-686ffa5b0000\"", "_attachments": "attachments/", "_ts": 1752169051}, {"payPeriodId": "1090066543956486", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-01-20T00:00:00Z", "endDate": "2025-01-26T00:00:00Z", "submitByDate": "2025-02-04T00:00:00Z", "checkDate": "2025-02-06T00:00:00Z", "checkCount": 7, "id": "44765bd1-146d-4d61-9548-1fdbe60c7f53", "companyId": "18163405", "type": "payperiod", "_rid": "NmJkAKiCbEdeNgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdeNgEAAAAAAA==/", "_etag": "\"9e006905-0000-0100-0000-686ffa5b0000\"", "_attachments": "attachments/", "_ts": 1752169051}, {"payPeriodId": "1090066639799565", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-01-27T00:00:00Z", "endDate": "2025-02-02T00:00:00Z", "submitByDate": "2025-02-11T00:00:00Z", "checkDate": "2025-02-13T00:00:00Z", "checkCount": 8, "id": "397b43fc-0efd-4f97-83bf-4d8cfa01597b", "companyId": "18163405", "type": "payperiod", "_rid": "NmJkAKiCbEdfNgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdfNgEAAAAAAA==/", "_etag": "\"9e006e05-0000-0100-0000-686ffa5b0000\"", "_attachments": "attachments/", "_ts": 1752169051}, {"payPeriodId": "1090066806511086", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-02-03T00:00:00Z", "endDate": "2025-02-09T00:00:00Z", "submitByDate": "2025-02-18T00:00:00Z", "checkDate": "2025-02-20T00:00:00Z", "checkCount": 6, "id": "502f03ba-78ea-4646-8cc6-522546ea6e09", "companyId": "18163405", "type": "payperiod", "_rid": "NmJkAKiCbEdgNgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdgNgEAAAAAAA==/", "_etag": "\"9e007605-0000-0100-0000-686ffa5b0000\"", "_attachments": "attachments/", "_ts": 1752169051}, {"payPeriodId": "1090066966094480", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-02-10T00:00:00Z", "endDate": "2025-02-16T00:00:00Z", "submitByDate": "2025-02-25T00:00:00Z", "checkDate": "2025-02-27T00:00:00Z", "checkCount": 6, "id": "5724a3df-9ab8-4011-91cf-0bd83b0dd235", "companyId": "18163405", "type": "payperiod", "_rid": "NmJkAKiCbEdhNgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdhNgEAAAAAAA==/", "_etag": "\"9e007905-0000-0100-0000-686ffa5b0000\"", "_attachments": "attachments/", "_ts": 1752169051}, {"payPeriodId": "1090067102973566", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-02-17T00:00:00Z", "endDate": "2025-02-23T00:00:00Z", "submitByDate": "2025-03-04T00:00:00Z", "checkDate": "2025-03-06T00:00:00Z", "checkCount": 7, "id": "54b2fb75-0d82-4363-9b2b-0efe71ecbf50", "companyId": "18163405", "type": "payperiod", "_rid": "NmJkAKiCbEdiNgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdiNgEAAAAAAA==/", "_etag": "\"9e007a05-0000-0100-0000-686ffa5c0000\"", "_attachments": "attachments/", "_ts": 1752169052}, {"payPeriodId": "1090067314621089", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-02-24T00:00:00Z", "endDate": "2025-03-02T00:00:00Z", "submitByDate": "2025-03-11T00:00:00Z", "checkDate": "2025-03-13T00:00:00Z", "checkCount": 6, "id": "d829a52c-edc9-4c00-80e8-6756e3157bd1", "companyId": "18163405", "type": "payperiod", "_rid": "NmJkAKiCbEdjNgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdjNgEAAAAAAA==/", "_etag": "\"9e007d05-0000-0100-0000-686ffa5c0000\"", "_attachments": "attachments/", "_ts": 1752169052}, {"payPeriodId": "1090067400500661", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-03-03T00:00:00Z", "endDate": "2025-03-09T00:00:00Z", "submitByDate": "2025-03-18T00:00:00Z", "checkDate": "2025-03-20T00:00:00Z", "checkCount": 7, "id": "34a34bdd-6240-4fce-84b5-426b8160a4a2", "companyId": "18163405", "type": "payperiod", "_rid": "NmJkAKiCbEdkNgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdkNgEAAAAAAA==/", "_etag": "\"9e008105-0000-0100-0000-686ffa5c0000\"", "_attachments": "attachments/", "_ts": 1752169052}, {"payPeriodId": "1090067538931552", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-03-10T00:00:00Z", "endDate": "2025-03-16T00:00:00Z", "submitByDate": "2025-03-25T00:00:00Z", "checkDate": "2025-03-27T00:00:00Z", "checkCount": 7, "id": "2377df22-6826-4baf-a531-eca2a46c3e21", "companyId": "18163405", "type": "payperiod", "_rid": "NmJkAKiCbEdlNgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdlNgEAAAAAAA==/", "_etag": "\"9e008505-0000-0100-0000-686ffa5c0000\"", "_attachments": "attachments/", "_ts": 1752169052}, {"payPeriodId": "1090067725095628", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-03-17T00:00:00Z", "endDate": "2025-03-23T00:00:00Z", "submitByDate": "2025-04-01T00:00:00Z", "checkDate": "2025-04-03T00:00:00Z", "checkCount": 7, "id": "3b94f14b-0f53-4301-a550-36f0aa62bd82", "companyId": "18163405", "type": "payperiod", "_rid": "NmJkAKiCbEdmNgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdmNgEAAAAAAA==/", "_etag": "\"9e008a05-0000-0100-0000-686ffa5c0000\"", "_attachments": "attachments/", "_ts": 1752169052}, {"payPeriodId": "1090067934859555", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-03-24T00:00:00Z", "endDate": "2025-03-30T00:00:00Z", "submitByDate": "2025-04-08T00:00:00Z", "checkDate": "2025-04-10T00:00:00Z", "checkCount": 7, "id": "feb988e3-84c8-40de-ae56-1cb26abe995b", "companyId": "18163405", "type": "payperiod", "_rid": "NmJkAKiCbEdnNgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdnNgEAAAAAAA==/", "_etag": "\"9e008b05-0000-0100-0000-686ffa5c0000\"", "_attachments": "attachments/", "_ts": 1752169052}, {"payPeriodId": "1090068060281591", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-03-31T00:00:00Z", "endDate": "2025-04-06T00:00:00Z", "submitByDate": "2025-04-15T00:00:00Z", "checkDate": "2025-04-17T00:00:00Z", "checkCount": 8, "id": "07936a06-b0a1-4664-8a60-f327c436f9dc", "companyId": "18163405", "type": "payperiod", "_rid": "NmJkAKiCbEdoNgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdoNgEAAAAAAA==/", "_etag": "\"9e008e05-0000-0100-0000-686ffa5c0000\"", "_attachments": "attachments/", "_ts": 1752169052}, {"payPeriodId": "1090068204343531", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-04-07T00:00:00Z", "endDate": "2025-04-13T00:00:00Z", "submitByDate": "2025-04-22T00:00:00Z", "checkDate": "2025-04-24T00:00:00Z", "checkCount": 8, "id": "c4dc0f73-cbc5-466b-ba9e-81d2121dfaf9", "companyId": "18163405", "type": "payperiod", "_rid": "NmJkAKiCbEdpNgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdpNgEAAAAAAA==/", "_etag": "\"9e009105-0000-0100-0000-686ffa5c0000\"", "_attachments": "attachments/", "_ts": 1752169052}, {"payPeriodId": "1090068387796454", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-04-14T00:00:00Z", "endDate": "2025-04-20T00:00:00Z", "submitByDate": "2025-04-29T00:00:00Z", "checkDate": "2025-05-01T00:00:00Z", "checkCount": 9, "id": "26916241-6c7e-4f61-9d37-2f19b8336901", "companyId": "18163405", "type": "payperiod", "_rid": "NmJkAKiCbEdqNgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdqNgEAAAAAAA==/", "_etag": "\"9e009805-0000-0100-0000-686ffa5c0000\"", "_attachments": "attachments/", "_ts": 1752169052}, {"payPeriodId": "1090068504515338", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-04-21T00:00:00Z", "endDate": "2025-04-27T00:00:00Z", "submitByDate": "2025-05-06T00:00:00Z", "checkDate": "2025-05-08T00:00:00Z", "checkCount": 9, "id": "f921ac49-1b14-425e-8916-a1d62312c99f", "companyId": "18163405", "type": "payperiod", "_rid": "NmJkAKiCbEdrNgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdrNgEAAAAAAA==/", "_etag": "\"9e009a05-0000-0100-0000-686ffa5c0000\"", "_attachments": "attachments/", "_ts": 1752169052}, {"payPeriodId": "1090068652955920", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-04-28T00:00:00Z", "endDate": "2025-05-04T00:00:00Z", "submitByDate": "2025-05-13T00:00:00Z", "checkDate": "2025-05-15T00:00:00Z", "checkCount": 9, "id": "5ea6832c-40a8-48ba-8c1a-8b8d21855b23", "companyId": "18163405", "type": "payperiod", "_rid": "NmJkAKiCbEdsNgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdsNgEAAAAAAA==/", "_etag": "\"9e009d05-0000-0100-0000-686ffa5c0000\"", "_attachments": "attachments/", "_ts": 1752169052}, {"payPeriodId": "1090068799232322", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-05-05T00:00:00Z", "endDate": "2025-05-11T00:00:00Z", "submitByDate": "2025-05-20T00:00:00Z", "checkDate": "2025-05-22T00:00:00Z", "checkCount": 9, "id": "7aa5a1f9-3caf-4fc5-96fb-3294e658bb43", "companyId": "18163405", "type": "payperiod", "_rid": "NmJkAKiCbEdtNgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdtNgEAAAAAAA==/", "_etag": "\"9e00a005-0000-0100-0000-686ffa5c0000\"", "_attachments": "attachments/", "_ts": 1752169052}, {"payPeriodId": "1090068967452091", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-05-12T00:00:00Z", "endDate": "2025-05-18T00:00:00Z", "submitByDate": "2025-05-27T00:00:00Z", "checkDate": "2025-05-29T00:00:00Z", "checkCount": 8, "id": "b562c749-3fd6-4321-905e-1fc3b3362e5e", "companyId": "18163405", "type": "payperiod", "_rid": "NmJkAKiCbEduNgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEduNgEAAAAAAA==/", "_etag": "\"9e00a305-0000-0100-0000-686ffa5c0000\"", "_attachments": "attachments/", "_ts": 1752169052}, {"payPeriodId": "1090069136626585", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-05-19T00:00:00Z", "endDate": "2025-05-25T00:00:00Z", "submitByDate": "2025-06-03T00:00:00Z", "checkDate": "2025-06-05T00:00:00Z", "checkCount": 8, "id": "274572e9-2acd-438c-92a1-9b37ad325a6e", "companyId": "18163405", "type": "payperiod", "_rid": "NmJkAKiCbEdvNgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdvNgEAAAAAAA==/", "_etag": "\"9e00a405-0000-0100-0000-686ffa5d0000\"", "_attachments": "attachments/", "_ts": 1752169053}, {"payPeriodId": "1090069236974040", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-05-26T00:00:00Z", "endDate": "2025-06-01T00:00:00Z", "submitByDate": "2025-06-10T00:00:00Z", "checkDate": "2025-06-12T00:00:00Z", "checkCount": 8, "id": "dcb3e45c-5720-4102-b06e-c99480176dc6", "companyId": "18163405", "type": "payperiod", "_rid": "NmJkAKiCbEdwNgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdwNgEAAAAAAA==/", "_etag": "\"9e00a705-0000-0100-0000-686ffa5d0000\"", "_attachments": "attachments/", "_ts": 1752169053}, {"payPeriodId": "1090069412709288", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-06-02T00:00:00Z", "endDate": "2025-06-08T00:00:00Z", "submitByDate": "2025-06-16T00:00:00Z", "checkDate": "2025-06-18T00:00:00Z", "checkCount": 8, "id": "b36f305a-e71a-4920-9cdb-8f97bd65ca22", "companyId": "18163405", "type": "payperiod", "_rid": "NmJkAKiCbEdxNgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdxNgEAAAAAAA==/", "_etag": "\"9e00aa05-0000-0100-0000-686ffa5d0000\"", "_attachments": "attachments/", "_ts": 1752169053}, {"payPeriodId": "1090069550373257", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-06-09T00:00:00Z", "endDate": "2025-06-15T00:00:00Z", "submitByDate": "2025-06-24T00:00:00Z", "checkDate": "2025-06-26T00:00:00Z", "checkCount": 8, "id": "360b2a0f-0a9b-489b-b758-8d88ad54cea5", "companyId": "18163405", "type": "payperiod", "_rid": "NmJkAKiCbEdyNgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdyNgEAAAAAAA==/", "_etag": "\"9e00ad05-0000-0100-0000-686ffa5d0000\"", "_attachments": "attachments/", "_ts": 1752169053}, {"payPeriodId": "1090069724338471", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-06-16T00:00:00Z", "endDate": "2025-06-22T00:00:00Z", "submitByDate": "2025-07-01T00:00:00Z", "checkDate": "2025-07-03T00:00:00Z", "checkCount": 8, "id": "f83a00bc-ecf2-499a-9ca6-58c8eac7aebd", "companyId": "18163405", "type": "payperiod", "_rid": "NmJkAKiCbEdzNgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdzNgEAAAAAAA==/", "_etag": "\"9e00af05-0000-0100-0000-686ffa5d0000\"", "_attachments": "attachments/", "_ts": 1752169053}, {"payPeriodId": "1090069895278376", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-06-23T00:00:00Z", "endDate": "2025-06-29T00:00:00Z", "submitByDate": "2025-07-08T00:00:00Z", "checkDate": "2025-07-10T00:00:00Z", "checkCount": 0, "id": "21230c30-5bd3-4c50-b6fe-eef7cff81f2f", "companyId": "18163405", "type": "payperiod", "_rid": "NmJkAKiCbEd0NgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEd0NgEAAAAAAA==/", "_etag": "\"9e00b205-0000-0100-0000-686ffa5d0000\"", "_attachments": "attachments/", "_ts": 1752169053}, {"payPeriodId": "1090070042475209", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-06-30T00:00:00Z", "endDate": "2025-07-06T00:00:00Z", "submitByDate": "2025-07-15T00:00:00Z", "checkDate": "2025-07-17T00:00:00Z", "checkCount": 0, "id": "69321524-5dfc-49f1-819f-14bce82c1d73", "companyId": "18163405", "type": "payperiod", "_rid": "NmJkAKiCbEd1NgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEd1NgEAAAAAAA==/", "_etag": "\"9e00b605-0000-0100-0000-686ffa5d0000\"", "_attachments": "attachments/", "_ts": 1752169053}, {"payPeriodId": "1090070177690660", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-07-07T00:00:00Z", "endDate": "2025-07-13T00:00:00Z", "submitByDate": "2025-07-22T00:00:00Z", "checkDate": "2025-07-24T00:00:00Z", "checkCount": 0, "id": "d020b4db-560d-4eb8-b560-3f0f9571dd81", "companyId": "18163405", "type": "payperiod", "_rid": "NmJkAKiCbEd2NgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEd2NgEAAAAAAA==/", "_etag": "\"9e00bb05-0000-0100-0000-686ffa5d0000\"", "_attachments": "attachments/", "_ts": 1752169053}, {"payPeriodId": "1090070338020687", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-07-14T00:00:00Z", "endDate": "2025-07-20T00:00:00Z", "submitByDate": "2025-07-29T00:00:00Z", "checkDate": "2025-07-31T00:00:00Z", "checkCount": 0, "id": "31b0ca40-a3bb-4578-9985-a89b9fbf664e", "companyId": "18163405", "type": "payperiod", "_rid": "NmJkAKiCbEd3NgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEd3NgEAAAAAAA==/", "_etag": "\"9e00c105-0000-0100-0000-686ffa5d0000\"", "_attachments": "attachments/", "_ts": 1752169053}, {"payPeriodId": "1090070482796443", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-07-21T00:00:00Z", "endDate": "2025-07-27T00:00:00Z", "submitByDate": "2025-08-05T00:00:00Z", "checkDate": "2025-08-07T00:00:00Z", "checkCount": 0, "id": "327c8e68-f6b3-405c-b510-1323e11f3aaa", "companyId": "18163405", "type": "payperiod", "_rid": "NmJkAKiCbEd4NgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEd4NgEAAAAAAA==/", "_etag": "\"9e00c605-0000-0100-0000-686ffa5d0000\"", "_attachments": "attachments/", "_ts": 1752169053}, {"payPeriodId": "1090070664859800", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-07-28T00:00:00Z", "endDate": "2025-08-03T00:00:00Z", "submitByDate": "2025-08-12T00:00:00Z", "checkDate": "2025-08-14T00:00:00Z", "checkCount": 0, "id": "fc298ad9-014f-4661-b7d1-ec2ab7c67941", "companyId": "18163405", "type": "payperiod", "_rid": "NmJkAKiCbEd5NgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEd5NgEAAAAAAA==/", "_etag": "\"9e00c905-0000-0100-0000-686ffa5d0000\"", "_attachments": "attachments/", "_ts": 1752169053}, {"payPeriodId": "1090070819936475", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-08-04T00:00:00Z", "endDate": "2025-08-10T00:00:00Z", "submitByDate": "2025-08-19T00:00:00Z", "checkDate": "2025-08-21T00:00:00Z", "checkCount": 0, "id": "5501a965-f154-401e-b6fa-2eb4d7b72506", "companyId": "18163405", "type": "payperiod", "_rid": "NmJkAKiCbEd6NgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEd6NgEAAAAAAA==/", "_etag": "\"9e00cc05-0000-0100-0000-686ffa5d0000\"", "_attachments": "attachments/", "_ts": 1752169053}, {"payPeriodId": "1090070938909295", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-08-11T00:00:00Z", "endDate": "2025-08-17T00:00:00Z", "submitByDate": "2025-08-26T00:00:00Z", "checkDate": "2025-08-28T00:00:00Z", "checkCount": 0, "id": "b1b43cb9-4c07-4dc6-a84b-c3a02643df06", "companyId": "18163405", "type": "payperiod", "_rid": "NmJkAKiCbEd7NgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEd7NgEAAAAAAA==/", "_etag": "\"9e00d005-0000-0100-0000-686ffa5e0000\"", "_attachments": "attachments/", "_ts": 1752169054}, {"payPeriodId": "1090071086245119", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-08-18T00:00:00Z", "endDate": "2025-08-24T00:00:00Z", "submitByDate": "2025-09-02T00:00:00Z", "checkDate": "2025-09-04T00:00:00Z", "checkCount": 0, "id": "54b7ffd6-480f-45a9-bfe4-5a86321dfd02", "companyId": "18163405", "type": "payperiod", "_rid": "NmJkAKiCbEd8NgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEd8NgEAAAAAAA==/", "_etag": "\"9e00d805-0000-0100-0000-686ffa5e0000\"", "_attachments": "attachments/", "_ts": 1752169054}, {"payPeriodId": "1090071268789349", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-08-25T00:00:00Z", "endDate": "2025-08-31T00:00:00Z", "submitByDate": "2025-09-09T00:00:00Z", "checkDate": "2025-09-11T00:00:00Z", "checkCount": 0, "id": "9bffba77-8f93-4143-a054-65a927f7ed01", "companyId": "18163405", "type": "payperiod", "_rid": "NmJkAKiCbEd9NgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEd9NgEAAAAAAA==/", "_etag": "\"9e00dc05-0000-0100-0000-686ffa5e0000\"", "_attachments": "attachments/", "_ts": 1752169054}, {"payPeriodId": "1090071438135725", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-09-01T00:00:00Z", "endDate": "2025-09-07T00:00:00Z", "submitByDate": "2025-09-16T00:00:00Z", "checkDate": "2025-09-18T00:00:00Z", "checkCount": 0, "id": "012b8e28-a9a7-4a22-b540-69dc1145255a", "companyId": "18163405", "type": "payperiod", "_rid": "NmJkAKiCbEd+NgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEd+NgEAAAAAAA==/", "_etag": "\"9e00de05-0000-0100-0000-686ffa5e0000\"", "_attachments": "attachments/", "_ts": 1752169054}, {"payPeriodId": "1090071572020613", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-09-08T00:00:00Z", "endDate": "2025-09-14T00:00:00Z", "submitByDate": "2025-09-23T00:00:00Z", "checkDate": "2025-09-25T00:00:00Z", "checkCount": 0, "id": "e86e9ee3-fe7e-4b0f-bd99-a3b97e0109ad", "companyId": "18163405", "type": "payperiod", "_rid": "NmJkAKiCbEd-NgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEd-NgEAAAAAAA==/", "_etag": "\"9e00e605-0000-0100-0000-686ffa5e0000\"", "_attachments": "attachments/", "_ts": 1752169054}, {"payPeriodId": "1090071712530223", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-09-15T00:00:00Z", "endDate": "2025-09-21T00:00:00Z", "submitByDate": "2025-09-30T00:00:00Z", "checkDate": "2025-10-02T00:00:00Z", "checkCount": 0, "id": "5ed98598-350e-46d1-a89a-bd993705eb85", "companyId": "18163405", "type": "payperiod", "_rid": "NmJkAKiCbEeANgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeANgEAAAAAAA==/", "_etag": "\"9e00ed05-0000-0100-0000-686ffa5e0000\"", "_attachments": "attachments/", "_ts": 1752169054}, {"payPeriodId": "1090071883375105", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-09-22T00:00:00Z", "endDate": "2025-09-28T00:00:00Z", "submitByDate": "2025-10-07T00:00:00Z", "checkDate": "2025-10-09T00:00:00Z", "checkCount": 0, "id": "a8640693-6922-400a-a36c-48c0f8474b41", "companyId": "18163405", "type": "payperiod", "_rid": "NmJkAKiCbEeBNgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeBNgEAAAAAAA==/", "_etag": "\"9e00f305-0000-0100-0000-686ffa5e0000\"", "_attachments": "attachments/", "_ts": 1752169054}, {"payPeriodId": "1090072062115427", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-09-29T00:00:00Z", "endDate": "2025-10-05T00:00:00Z", "submitByDate": "2025-10-14T00:00:00Z", "checkDate": "2025-10-16T00:00:00Z", "checkCount": 0, "id": "0b03d889-8c0a-47da-9310-d33da4fac5fb", "companyId": "18163405", "type": "payperiod", "_rid": "NmJkAKiCbEeCNgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeCNgEAAAAAAA==/", "_etag": "\"9e00f605-0000-0100-0000-686ffa5e0000\"", "_attachments": "attachments/", "_ts": 1752169054}], "links": [{"rel": "self", "href": "https://ca-payroll-ai-mock-sb-001-secure.nicebeach-8a7a52ab.eastus.azurecontainerapps.io/ca/companies/18163405/payperiods"}]}, "status_code": 200}