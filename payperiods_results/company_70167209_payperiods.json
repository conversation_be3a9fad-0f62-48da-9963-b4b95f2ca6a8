{"success": true, "company_id": "70167209", "data": {"metadata": {"contentItemCount": 20}, "content": [{"payPeriodId": "1060039038968730", "intervalCode": "MONTHLY", "status": "COMPLETED", "description": "Monthly Payroll", "startDate": "2025-01-01T00:00:00Z", "endDate": "2025-01-31T00:00:00Z", "submitByDate": "2025-01-24T00:00:00Z", "checkDate": "2025-01-28T00:00:00Z", "checkCount": 3, "id": "508023fa-9e56-47ba-aabb-9ea3a3c20001", "companyId": "70167209", "type": "payperiod", "_rid": "NmJkAKiCbEeh-wIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeh-wIAAAAAAA==/", "_etag": "\"a40036bd-0000-0100-0000-687022590000\"", "_attachments": "attachments/", "_ts": 1752179289}, {"payPeriodId": "1050107948827192", "intervalCode": "MONTHLY", "status": "COMPLETED", "description": "Monthly Payroll (1)", "startDate": "2025-02-01T00:00:00Z", "endDate": "2025-02-28T00:00:00Z", "submitByDate": "2025-02-26T00:00:00Z", "checkDate": "2025-02-28T00:00:00Z", "checkCount": 2, "id": "5db483c4-d201-454e-87fb-b9088813915d", "companyId": "70167209", "type": "payperiod", "_rid": "NmJkAKiCbEei-wIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEei-wIAAAAAAA==/", "_etag": "\"a4003bbd-0000-0100-0000-687022590000\"", "_attachments": "attachments/", "_ts": 1752179289}, {"payPeriodId": "1050107948827195", "intervalCode": "MONTHLY", "status": "COMPLETED", "description": "Monthly Payroll (1)", "startDate": "2025-03-01T00:00:00Z", "endDate": "2025-03-31T00:00:00Z", "submitByDate": "2025-03-27T00:00:00Z", "checkDate": "2025-03-28T00:00:00Z", "checkCount": 3, "id": "bae873d9-ab94-4542-866b-3aeb546ec845", "companyId": "70167209", "type": "payperiod", "_rid": "NmJkAKiCbEej-wIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEej-wIAAAAAAA==/", "_etag": "\"a4003ebd-0000-0100-0000-6870225a0000\"", "_attachments": "attachments/", "_ts": 1752179290}, {"payPeriodId": "1050110287674426", "status": "INITIAL", "description": "Monthly Payroll (1)", "startDate": "2025-04-01T00:00:00Z", "endDate": "2025-04-30T00:00:00Z", "submitByDate": "2025-04-24T00:00:00Z", "checkDate": "2025-04-30T00:00:00Z", "checkCount": 0, "id": "eecf470c-8c5b-4da7-b565-503a1fee220e", "companyId": "70167209", "type": "payperiod", "_rid": "NmJkAKiCbEek-wIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEek-wIAAAAAAA==/", "_etag": "\"a40041bd-0000-0100-0000-6870225a0000\"", "_attachments": "attachments/", "_ts": 1752179290}, {"payPeriodId": "1050109872400521", "intervalCode": "MONTHLY", "status": "INITIAL", "description": "Monthly Payroll (1)", "startDate": "2025-05-01T00:00:00Z", "endDate": "2025-05-31T00:00:00Z", "submitByDate": "2025-05-23T00:00:00Z", "checkDate": "2025-05-28T00:00:00Z", "checkCount": 0, "id": "c8b2879b-9a72-4ba7-ad25-effa5e34d511", "companyId": "70167209", "type": "payperiod", "_rid": "NmJkAKiCbEel-wIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEel-wIAAAAAAA==/", "_etag": "\"a40044bd-0000-0100-0000-6870225a0000\"", "_attachments": "attachments/", "_ts": 1752179290}, {"payPeriodId": "1050109872400524", "intervalCode": "MONTHLY", "status": "INITIAL", "description": "Monthly Payroll (1)", "startDate": "2025-06-01T00:00:00Z", "endDate": "2025-06-30T00:00:00Z", "submitByDate": "2025-06-25T00:00:00Z", "checkDate": "2025-06-27T00:00:00Z", "checkCount": 0, "id": "2383746f-f719-4515-afa5-f68294b0e55c", "companyId": "70167209", "type": "payperiod", "_rid": "NmJkAKiCbEem-wIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEem-wIAAAAAAA==/", "_etag": "\"a40047bd-0000-0100-0000-6870225a0000\"", "_attachments": "attachments/", "_ts": 1752179290}, {"payPeriodId": "1060040056076225", "intervalCode": "MONTHLY", "status": "INITIAL", "description": "Monthly Payroll (1)", "startDate": "2025-07-01T00:00:00Z", "endDate": "2025-07-31T00:00:00Z", "submitByDate": "2025-07-24T00:00:00Z", "checkDate": "2025-07-28T00:00:00Z", "checkCount": 0, "id": "35b060e5-3257-4b4b-bcde-ebfb73ea0b25", "companyId": "70167209", "type": "payperiod", "_rid": "NmJkAKiCbEen-wIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEen-wIAAAAAAA==/", "_etag": "\"a40048bd-0000-0100-0000-6870225a0000\"", "_attachments": "attachments/", "_ts": 1752179290}, {"payPeriodId": "1060040056076226", "intervalCode": "MONTHLY", "status": "INITIAL", "description": "Monthly Payroll (1)", "startDate": "2025-08-01T00:00:00Z", "endDate": "2025-08-31T00:00:00Z", "submitByDate": "2025-08-26T00:00:00Z", "checkDate": "2025-08-28T00:00:00Z", "checkCount": 0, "id": "f43549d9-7288-47b1-80c3-d3c175473089", "companyId": "70167209", "type": "payperiod", "_rid": "NmJkAKiCbEeo-wIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeo-wIAAAAAAA==/", "_etag": "\"a4004cbd-0000-0100-0000-6870225a0000\"", "_attachments": "attachments/", "_ts": 1752179290}, {"payPeriodId": "1060040229158554", "intervalCode": "MONTHLY", "status": "INITIAL", "description": "Monthly Payroll (1)", "startDate": "2025-09-01T00:00:00Z", "endDate": "2025-09-30T00:00:00Z", "submitByDate": "2025-09-24T00:00:00Z", "checkDate": "2025-09-26T00:00:00Z", "checkCount": 0, "id": "6093604b-8647-42ba-b47d-e01ce5613b35", "companyId": "70167209", "type": "payperiod", "_rid": "NmJkAKiCbEep-wIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEep-wIAAAAAAA==/", "_etag": "\"a4004ebd-0000-0100-0000-6870225a0000\"", "_attachments": "attachments/", "_ts": 1752179290}, {"payPeriodId": "1060040369944168", "intervalCode": "MONTHLY", "status": "INITIAL", "description": "Monthly Payroll (1)", "startDate": "2025-10-01T00:00:00Z", "endDate": "2025-10-31T00:00:00Z", "submitByDate": "2025-10-24T00:00:00Z", "checkDate": "2025-10-28T00:00:00Z", "checkCount": 0, "id": "a38771cd-9b1e-4b50-a2a1-41a38e7d88b7", "companyId": "70167209", "type": "payperiod", "_rid": "NmJkAKiCbEeq-wIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeq-wIAAAAAAA==/", "_etag": "\"a40050bd-0000-0100-0000-6870225a0000\"", "_attachments": "attachments/", "_ts": 1752179290}, {"payPeriodId": "1060039038968730", "intervalCode": "MONTHLY", "status": "COMPLETED", "description": "Monthly Payroll", "startDate": "2025-01-01T00:00:00Z", "endDate": "2025-01-31T00:00:00Z", "submitByDate": "2025-01-24T00:00:00Z", "checkDate": "2025-01-28T00:00:00Z", "checkCount": 3, "id": "0732d93b-4da7-46b3-b6b1-70fc650d2726", "companyId": "70167209", "type": "payperiod", "_rid": "NmJkAKiCbEes-wIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEes-wIAAAAAAA==/", "_etag": "\"a40058bd-0000-0100-0000-6870225a0000\"", "_attachments": "attachments/", "_ts": 1752179290}, {"payPeriodId": "1050107948827192", "intervalCode": "MONTHLY", "status": "COMPLETED", "description": "Monthly Payroll (1)", "startDate": "2025-02-01T00:00:00Z", "endDate": "2025-02-28T00:00:00Z", "submitByDate": "2025-02-26T00:00:00Z", "checkDate": "2025-02-28T00:00:00Z", "checkCount": 2, "id": "09a2e22f-5d3c-4cc3-a61b-74cbd050f07b", "companyId": "70167209", "type": "payperiod", "_rid": "NmJkAKiCbEet-wIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEet-wIAAAAAAA==/", "_etag": "\"a4005bbd-0000-0100-0000-6870225a0000\"", "_attachments": "attachments/", "_ts": 1752179290}, {"payPeriodId": "1050107948827195", "intervalCode": "MONTHLY", "status": "COMPLETED", "description": "Monthly Payroll (1)", "startDate": "2025-03-01T00:00:00Z", "endDate": "2025-03-31T00:00:00Z", "submitByDate": "2025-03-27T00:00:00Z", "checkDate": "2025-03-28T00:00:00Z", "checkCount": 3, "id": "58e6263d-5994-45d6-98ec-e173a5cfd3a1", "companyId": "70167209", "type": "payperiod", "_rid": "NmJkAKiCbEeu-wIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeu-wIAAAAAAA==/", "_etag": "\"a4005fbd-0000-0100-0000-6870225a0000\"", "_attachments": "attachments/", "_ts": 1752179290}, {"payPeriodId": "1050110287674426", "status": "COMPLETED", "description": "Monthly Payroll (1)", "startDate": "2025-04-01T00:00:00Z", "endDate": "2025-04-30T00:00:00Z", "submitByDate": "2025-04-24T00:00:00Z", "checkDate": "2025-04-30T00:00:00Z", "checkCount": 3, "id": "f23371f9-b0c5-4230-affc-5ca65e86abbd", "companyId": "70167209", "type": "payperiod", "_rid": "NmJkAKiCbEev-wIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEev-wIAAAAAAA==/", "_etag": "\"a40061bd-0000-0100-0000-6870225a0000\"", "_attachments": "attachments/", "_ts": 1752179290}, {"payPeriodId": "1050109872400521", "intervalCode": "MONTHLY", "status": "INITIAL", "description": "Monthly Payroll (1)", "startDate": "2025-05-01T00:00:00Z", "endDate": "2025-05-31T00:00:00Z", "submitByDate": "2025-05-23T00:00:00Z", "checkDate": "2025-05-28T00:00:00Z", "checkCount": 0, "id": "d8b36f59-fd40-4cf5-a787-227db4c05b73", "companyId": "70167209", "type": "payperiod", "_rid": "NmJkAKiCbEew-wIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEew-wIAAAAAAA==/", "_etag": "\"a40065bd-0000-0100-0000-6870225b0000\"", "_attachments": "attachments/", "_ts": 1752179291}, {"payPeriodId": "1050109872400524", "intervalCode": "MONTHLY", "status": "INITIAL", "description": "Monthly Payroll (1)", "startDate": "2025-06-01T00:00:00Z", "endDate": "2025-06-30T00:00:00Z", "submitByDate": "2025-06-25T00:00:00Z", "checkDate": "2025-06-27T00:00:00Z", "checkCount": 0, "id": "ed251923-cd6e-4309-944d-55944d715aa1", "companyId": "70167209", "type": "payperiod", "_rid": "NmJkAKiCbEex-wIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEex-wIAAAAAAA==/", "_etag": "\"a4006cbd-0000-0100-0000-6870225b0000\"", "_attachments": "attachments/", "_ts": 1752179291}, {"payPeriodId": "1060040056076225", "intervalCode": "MONTHLY", "status": "INITIAL", "description": "Monthly Payroll (1)", "startDate": "2025-07-01T00:00:00Z", "endDate": "2025-07-31T00:00:00Z", "submitByDate": "2025-07-24T00:00:00Z", "checkDate": "2025-07-28T00:00:00Z", "checkCount": 0, "id": "873323c1-fc55-424b-a87e-0ff71edc4643", "companyId": "70167209", "type": "payperiod", "_rid": "NmJkAKiCbEey-wIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEey-wIAAAAAAA==/", "_etag": "\"a40070bd-0000-0100-0000-6870225b0000\"", "_attachments": "attachments/", "_ts": 1752179291}, {"payPeriodId": "1060040056076226", "intervalCode": "MONTHLY", "status": "INITIAL", "description": "Monthly Payroll (1)", "startDate": "2025-08-01T00:00:00Z", "endDate": "2025-08-31T00:00:00Z", "submitByDate": "2025-08-26T00:00:00Z", "checkDate": "2025-08-28T00:00:00Z", "checkCount": 0, "id": "2bbea8e7-7047-4335-97e7-7dc9f76692fc", "companyId": "70167209", "type": "payperiod", "_rid": "NmJkAKiCbEez-wIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEez-wIAAAAAAA==/", "_etag": "\"a40072bd-0000-0100-0000-6870225b0000\"", "_attachments": "attachments/", "_ts": 1752179291}, {"payPeriodId": "1060040229158554", "intervalCode": "MONTHLY", "status": "INITIAL", "description": "Monthly Payroll (1)", "startDate": "2025-09-01T00:00:00Z", "endDate": "2025-09-30T00:00:00Z", "submitByDate": "2025-09-24T00:00:00Z", "checkDate": "2025-09-26T00:00:00Z", "checkCount": 0, "id": "b19aef7a-602d-4f5b-91d6-4f84ef100b33", "companyId": "70167209", "type": "payperiod", "_rid": "NmJkAKiCbEe0-wIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEe0-wIAAAAAAA==/", "_etag": "\"a40074bd-0000-0100-0000-6870225b0000\"", "_attachments": "attachments/", "_ts": 1752179291}, {"payPeriodId": "1060040369944168", "intervalCode": "MONTHLY", "status": "INITIAL", "description": "Monthly Payroll (1)", "startDate": "2025-10-01T00:00:00Z", "endDate": "2025-10-31T00:00:00Z", "submitByDate": "2025-10-24T00:00:00Z", "checkDate": "2025-10-28T00:00:00Z", "checkCount": 0, "id": "390a2cfc-b988-4b44-bcc2-df070ff304b4", "companyId": "70167209", "type": "payperiod", "_rid": "NmJkAKiCbEe1-wIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEe1-wIAAAAAAA==/", "_etag": "\"a40078bd-0000-0100-0000-6870225b0000\"", "_attachments": "attachments/", "_ts": 1752179291}], "links": [{"rel": "self", "href": "https://ca-payroll-ai-mock-sb-001-secure.nicebeach-8a7a52ab.eastus.azurecontainerapps.io/ca/companies/70167209/payperiods"}]}, "status_code": 200}