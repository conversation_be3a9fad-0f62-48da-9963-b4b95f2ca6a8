{"success": true, "company_id": "17011209", "data": {"metadata": {"contentItemCount": 36}, "content": [{"payPeriodId": "1080039085373309", "intervalCode": "MONTHLY", "status": "COMPLETED", "description": "Monthly Payroll (2)", "startDate": "2025-01-01T00:00:00Z", "endDate": "2025-01-31T00:00:00Z", "submitByDate": "2025-01-17T00:00:00Z", "checkDate": "2025-01-21T00:00:00Z", "checkCount": 1, "id": "8b98de72-7bd8-450f-99e0-0868d96a424e", "companyId": "17011209", "type": "payperiod", "_rid": "NmJkAKiCbEcNCwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcNCwAAAAAAAA==/", "_etag": "\"97003f1f-0000-0100-0000-686fd0190000\"", "_attachments": "attachments/", "_ts": 1752158233}, {"payPeriodId": "1080039280953214", "intervalCode": "MONTHLY", "status": "COMPLETED", "description": "Monthly Payroll (2)", "startDate": "2025-02-01T00:00:00Z", "endDate": "2025-02-28T00:00:00Z", "submitByDate": "2025-02-18T00:00:00Z", "checkDate": "2025-02-20T00:00:00Z", "checkCount": 1, "id": "13d66d19-6824-4d8d-85f0-4c1dc77eeffa", "companyId": "17011209", "type": "payperiod", "_rid": "NmJkAKiCbEcOCwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcOCwAAAAAAAA==/", "_etag": "\"9700431f-0000-0100-0000-686fd0190000\"", "_attachments": "attachments/", "_ts": 1752158233}, {"payPeriodId": "1080039510161451", "intervalCode": "MONTHLY", "status": "COMPLETED", "description": "Monthly Payroll (2)", "startDate": "2025-03-01T00:00:00Z", "endDate": "2025-03-31T00:00:00Z", "submitByDate": "2025-03-18T00:00:00Z", "checkDate": "2025-03-20T00:00:00Z", "checkCount": 1, "id": "d8be43c8-3e52-465c-85af-27d69ac304f5", "companyId": "17011209", "type": "payperiod", "_rid": "NmJkAKiCbEcPCwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcPCwAAAAAAAA==/", "_etag": "\"9700471f-0000-0100-0000-686fd0190000\"", "_attachments": "attachments/", "_ts": 1752158233}, {"payPeriodId": "1080039711534466", "intervalCode": "MONTHLY", "status": "INITIAL", "description": "Monthly Payroll (2)", "startDate": "2025-04-01T00:00:00Z", "endDate": "2025-04-30T00:00:00Z", "submitByDate": "2025-04-17T00:00:00Z", "checkDate": "2025-04-21T00:00:00Z", "checkCount": 0, "id": "194fef75-1d29-4598-bdfd-09ab3fda7876", "companyId": "17011209", "type": "payperiod", "_rid": "NmJkAKiCbEcQCwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcQCwAAAAAAAA==/", "_etag": "\"9700491f-0000-0100-0000-686fd0190000\"", "_attachments": "attachments/", "_ts": 1752158233}, {"payPeriodId": "1080039899159546", "intervalCode": "MONTHLY", "status": "INITIAL", "description": "Monthly Payroll (2)", "startDate": "2025-05-01T00:00:00Z", "endDate": "2025-05-31T00:00:00Z", "submitByDate": "2025-05-16T00:00:00Z", "checkDate": "2025-05-20T00:00:00Z", "checkCount": 0, "id": "f2bc1009-d231-4ae7-afb9-522a6cce1c6e", "companyId": "17011209", "type": "payperiod", "_rid": "NmJkAKiCbEcRCwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcRCwAAAAAAAA==/", "_etag": "\"97004c1f-0000-0100-0000-686fd0190000\"", "_attachments": "attachments/", "_ts": 1752158233}, {"payPeriodId": "1080040085611872", "intervalCode": "MONTHLY", "status": "INITIAL", "description": "Monthly Payroll (2)", "startDate": "2025-06-01T00:00:00Z", "endDate": "2025-06-30T00:00:00Z", "submitByDate": "2025-06-18T00:00:00Z", "checkDate": "2025-06-20T00:00:00Z", "checkCount": 0, "id": "f12601e9-7a13-4e5f-9599-3b24c2a33cdf", "companyId": "17011209", "type": "payperiod", "_rid": "NmJkAKiCbEcSCwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcSCwAAAAAAAA==/", "_etag": "\"9700501f-0000-0100-0000-686fd0190000\"", "_attachments": "attachments/", "_ts": 1752158233}, {"payPeriodId": "1080040325223532", "intervalCode": "MONTHLY", "status": "INITIAL", "description": "Monthly Payroll (2)", "startDate": "2025-07-01T00:00:00Z", "endDate": "2025-07-31T00:00:00Z", "submitByDate": "2025-07-17T00:00:00Z", "checkDate": "2025-07-21T00:00:00Z", "checkCount": 0, "id": "4d64b2d9-c543-4cea-b875-16e4c51406e7", "companyId": "17011209", "type": "payperiod", "_rid": "NmJkAKiCbEcTCwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcTCwAAAAAAAA==/", "_etag": "\"9700531f-0000-0100-0000-686fd0190000\"", "_attachments": "attachments/", "_ts": 1752158233}, {"payPeriodId": "1080040539138355", "intervalCode": "MONTHLY", "status": "INITIAL", "description": "Monthly Payroll (2)", "startDate": "2025-08-01T00:00:00Z", "endDate": "2025-08-31T00:00:00Z", "submitByDate": "2025-08-18T00:00:00Z", "checkDate": "2025-08-20T00:00:00Z", "checkCount": 0, "id": "44f9db90-d4e7-4491-b9f3-aba4180ba96c", "companyId": "17011209", "type": "payperiod", "_rid": "NmJkAKiCbEcUCwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcUCwAAAAAAAA==/", "_etag": "\"9700571f-0000-0100-0000-686fd0190000\"", "_attachments": "attachments/", "_ts": 1752158233}, {"payPeriodId": "1080040773249287", "intervalCode": "MONTHLY", "status": "INITIAL", "description": "Monthly Payroll (2)", "startDate": "2025-09-01T00:00:00Z", "endDate": "2025-09-30T00:00:00Z", "submitByDate": "2025-09-18T00:00:00Z", "checkDate": "2025-09-22T00:00:00Z", "checkCount": 0, "id": "ea5e32b5-77d4-4752-b759-994766aad96c", "companyId": "17011209", "type": "payperiod", "_rid": "NmJkAKiCbEcVCwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcVCwAAAAAAAA==/", "_etag": "\"97005a1f-0000-0100-0000-686fd0190000\"", "_attachments": "attachments/", "_ts": 1752158233}, {"payPeriodId": "1080039085373309", "intervalCode": "MONTHLY", "status": "COMPLETED", "description": "Monthly Payroll (2)", "startDate": "2025-01-01T00:00:00Z", "endDate": "2025-01-31T00:00:00Z", "submitByDate": "2025-01-17T00:00:00Z", "checkDate": "2025-01-21T00:00:00Z", "checkCount": 1, "id": "2ff76322-4f6f-4787-9658-32136cf3d0a9", "companyId": "17011209", "type": "payperiod", "_rid": "NmJkAKiCbEcZCwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcZCwAAAAAAAA==/", "_etag": "\"9700681f-0000-0100-0000-686fd01a0000\"", "_attachments": "attachments/", "_ts": 1752158234}, {"payPeriodId": "1080039280953214", "intervalCode": "MONTHLY", "status": "COMPLETED", "description": "Monthly Payroll (2)", "startDate": "2025-02-01T00:00:00Z", "endDate": "2025-02-28T00:00:00Z", "submitByDate": "2025-02-18T00:00:00Z", "checkDate": "2025-02-20T00:00:00Z", "checkCount": 1, "id": "a0abe062-b630-41ca-acc5-62c523e47a09", "companyId": "17011209", "type": "payperiod", "_rid": "NmJkAKiCbEcaCwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcaCwAAAAAAAA==/", "_etag": "\"97006a1f-0000-0100-0000-686fd01a0000\"", "_attachments": "attachments/", "_ts": 1752158234}, {"payPeriodId": "1080039510161451", "intervalCode": "MONTHLY", "status": "COMPLETED", "description": "Monthly Payroll (2)", "startDate": "2025-03-01T00:00:00Z", "endDate": "2025-03-31T00:00:00Z", "submitByDate": "2025-03-18T00:00:00Z", "checkDate": "2025-03-20T00:00:00Z", "checkCount": 1, "id": "bdd0c4fd-63de-452a-8e1e-92b5d4b805c4", "companyId": "17011209", "type": "payperiod", "_rid": "NmJkAKiCbEcbCwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcbCwAAAAAAAA==/", "_etag": "\"97006f1f-0000-0100-0000-686fd01a0000\"", "_attachments": "attachments/", "_ts": 1752158234}, {"payPeriodId": "1080039711534466", "intervalCode": "MONTHLY", "status": "COMPLETED", "description": "Monthly Payroll (2)", "startDate": "2025-04-01T00:00:00Z", "endDate": "2025-04-30T00:00:00Z", "submitByDate": "2025-04-17T00:00:00Z", "checkDate": "2025-04-21T00:00:00Z", "checkCount": 1, "id": "aebd6692-669f-4c5f-bede-a1198c537eed", "companyId": "17011209", "type": "payperiod", "_rid": "NmJkAKiCbEccCwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEccCwAAAAAAAA==/", "_etag": "\"9700721f-0000-0100-0000-686fd01a0000\"", "_attachments": "attachments/", "_ts": 1752158234}, {"payPeriodId": "1080039899159546", "intervalCode": "MONTHLY", "status": "COMPLETED", "description": "Monthly Payroll (2)", "startDate": "2025-05-01T00:00:00Z", "endDate": "2025-05-31T00:00:00Z", "submitByDate": "2025-05-16T00:00:00Z", "checkDate": "2025-05-20T00:00:00Z", "checkCount": 1, "id": "543a7135-66e5-47a1-86c0-4523aea19d3e", "companyId": "17011209", "type": "payperiod", "_rid": "NmJkAKiCbEcdCwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcdCwAAAAAAAA==/", "_etag": "\"9700761f-0000-0100-0000-686fd01a0000\"", "_attachments": "attachments/", "_ts": 1752158234}, {"payPeriodId": "1080040085611872", "intervalCode": "MONTHLY", "status": "COMPLETED", "description": "Monthly Payroll (2)", "startDate": "2025-06-01T00:00:00Z", "endDate": "2025-06-30T00:00:00Z", "submitByDate": "2025-06-18T00:00:00Z", "checkDate": "2025-06-20T00:00:00Z", "checkCount": 1, "id": "c95c683e-c183-4098-b8d5-84e29fa1f023", "companyId": "17011209", "type": "payperiod", "_rid": "NmJkAKiCbEceCwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEceCwAAAAAAAA==/", "_etag": "\"97007c1f-0000-0100-0000-686fd01a0000\"", "_attachments": "attachments/", "_ts": 1752158234}, {"payPeriodId": "1080040325223532", "intervalCode": "MONTHLY", "status": "INITIAL", "description": "Monthly Payroll (2)", "startDate": "2025-07-01T00:00:00Z", "endDate": "2025-07-31T00:00:00Z", "submitByDate": "2025-07-17T00:00:00Z", "checkDate": "2025-07-21T00:00:00Z", "checkCount": 0, "id": "81ca7a9b-95c8-4575-92e3-a98eeb8af116", "companyId": "17011209", "type": "payperiod", "_rid": "NmJkAKiCbEcfCwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcfCwAAAAAAAA==/", "_etag": "\"9700821f-0000-0100-0000-686fd01a0000\"", "_attachments": "attachments/", "_ts": 1752158234}, {"payPeriodId": "1080040539138355", "intervalCode": "MONTHLY", "status": "INITIAL", "description": "Monthly Payroll (2)", "startDate": "2025-08-01T00:00:00Z", "endDate": "2025-08-31T00:00:00Z", "submitByDate": "2025-08-18T00:00:00Z", "checkDate": "2025-08-20T00:00:00Z", "checkCount": 0, "id": "614dfe08-d87f-4ce6-89e9-af1c63b48851", "companyId": "17011209", "type": "payperiod", "_rid": "NmJkAKiCbEcgCwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcgCwAAAAAAAA==/", "_etag": "\"9700851f-0000-0100-0000-686fd01a0000\"", "_attachments": "attachments/", "_ts": 1752158234}, {"payPeriodId": "1080040773249287", "intervalCode": "MONTHLY", "status": "INITIAL", "description": "Monthly Payroll (2)", "startDate": "2025-09-01T00:00:00Z", "endDate": "2025-09-30T00:00:00Z", "submitByDate": "2025-09-18T00:00:00Z", "checkDate": "2025-09-22T00:00:00Z", "checkCount": 0, "id": "5945b337-b77d-4c82-852c-a0bda3b8ff93", "companyId": "17011209", "type": "payperiod", "_rid": "NmJkAKiCbEchCwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEchCwAAAAAAAA==/", "_etag": "\"9700861f-0000-0100-0000-686fd01a0000\"", "_attachments": "attachments/", "_ts": 1752158234}, {"payPeriodId": "1080039085373309", "intervalCode": "MONTHLY", "status": "COMPLETED", "description": "Monthly Payroll (2)", "startDate": "2025-01-01T00:00:00Z", "endDate": "2025-01-31T00:00:00Z", "submitByDate": "2025-01-17T00:00:00Z", "checkDate": "2025-01-21T00:00:00Z", "checkCount": 1, "id": "39273e0b-4c0e-4174-be54-3c47a1f8812c", "companyId": "17011209", "type": "payperiod", "_rid": "NmJkAKiCbEc6BAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc6BAEAAAAAAA==/", "_etag": "\"9d008863-0000-0100-0000-686ff6580000\"", "_attachments": "attachments/", "_ts": 1752168024}, {"payPeriodId": "1080039280953214", "intervalCode": "MONTHLY", "status": "COMPLETED", "description": "Monthly Payroll (2)", "startDate": "2025-02-01T00:00:00Z", "endDate": "2025-02-28T00:00:00Z", "submitByDate": "2025-02-18T00:00:00Z", "checkDate": "2025-02-20T00:00:00Z", "checkCount": 1, "id": "a53d3593-0e8f-4760-b475-fbcc3894fad9", "companyId": "17011209", "type": "payperiod", "_rid": "NmJkAKiCbEc7BAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc7BAEAAAAAAA==/", "_etag": "\"9d008963-0000-0100-0000-686ff6580000\"", "_attachments": "attachments/", "_ts": 1752168024}, {"payPeriodId": "1080039510161451", "intervalCode": "MONTHLY", "status": "COMPLETED", "description": "Monthly Payroll (2)", "startDate": "2025-03-01T00:00:00Z", "endDate": "2025-03-31T00:00:00Z", "submitByDate": "2025-03-18T00:00:00Z", "checkDate": "2025-03-20T00:00:00Z", "checkCount": 1, "id": "898f9862-f4a6-4f05-bd12-208e61ea9678", "companyId": "17011209", "type": "payperiod", "_rid": "NmJkAKiCbEc8BAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc8BAEAAAAAAA==/", "_etag": "\"9d008a63-0000-0100-0000-686ff6580000\"", "_attachments": "attachments/", "_ts": 1752168024}, {"payPeriodId": "1080039711534466", "intervalCode": "MONTHLY", "status": "INITIAL", "description": "Monthly Payroll (2)", "startDate": "2025-04-01T00:00:00Z", "endDate": "2025-04-30T00:00:00Z", "submitByDate": "2025-04-17T00:00:00Z", "checkDate": "2025-04-21T00:00:00Z", "checkCount": 0, "id": "a746a478-9bd1-4eae-869e-d8e74ef4d622", "companyId": "17011209", "type": "payperiod", "_rid": "NmJkAKiCbEc9BAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc9BAEAAAAAAA==/", "_etag": "\"9d008d63-0000-0100-0000-686ff6590000\"", "_attachments": "attachments/", "_ts": 1752168025}, {"payPeriodId": "1080039899159546", "intervalCode": "MONTHLY", "status": "INITIAL", "description": "Monthly Payroll (2)", "startDate": "2025-05-01T00:00:00Z", "endDate": "2025-05-31T00:00:00Z", "submitByDate": "2025-05-16T00:00:00Z", "checkDate": "2025-05-20T00:00:00Z", "checkCount": 0, "id": "afe5eaaa-5056-4525-82ea-37f696133404", "companyId": "17011209", "type": "payperiod", "_rid": "NmJkAKiCbEc+BAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc+BAEAAAAAAA==/", "_etag": "\"9d008f63-0000-0100-0000-686ff6590000\"", "_attachments": "attachments/", "_ts": 1752168025}, {"payPeriodId": "1080040085611872", "intervalCode": "MONTHLY", "status": "INITIAL", "description": "Monthly Payroll (2)", "startDate": "2025-06-01T00:00:00Z", "endDate": "2025-06-30T00:00:00Z", "submitByDate": "2025-06-18T00:00:00Z", "checkDate": "2025-06-20T00:00:00Z", "checkCount": 0, "id": "3853f565-7cf9-40b9-bbab-0f430c9f85b5", "companyId": "17011209", "type": "payperiod", "_rid": "NmJkAKiCbEc-BAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc-BAEAAAAAAA==/", "_etag": "\"9d009263-0000-0100-0000-686ff6590000\"", "_attachments": "attachments/", "_ts": 1752168025}, {"payPeriodId": "1080040325223532", "intervalCode": "MONTHLY", "status": "INITIAL", "description": "Monthly Payroll (2)", "startDate": "2025-07-01T00:00:00Z", "endDate": "2025-07-31T00:00:00Z", "submitByDate": "2025-07-17T00:00:00Z", "checkDate": "2025-07-21T00:00:00Z", "checkCount": 0, "id": "8fd0fbb4-df7e-460d-b1ef-254d7cf7f940", "companyId": "17011209", "type": "payperiod", "_rid": "NmJkAKiCbEdABAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdABAEAAAAAAA==/", "_etag": "\"9d009863-0000-0100-0000-686ff6590000\"", "_attachments": "attachments/", "_ts": 1752168025}, {"payPeriodId": "1080040539138355", "intervalCode": "MONTHLY", "status": "INITIAL", "description": "Monthly Payroll (2)", "startDate": "2025-08-01T00:00:00Z", "endDate": "2025-08-31T00:00:00Z", "submitByDate": "2025-08-18T00:00:00Z", "checkDate": "2025-08-20T00:00:00Z", "checkCount": 0, "id": "b587351d-307b-4d25-9482-9a0bb3d09304", "companyId": "17011209", "type": "payperiod", "_rid": "NmJkAKiCbEdBBAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdBBAEAAAAAAA==/", "_etag": "\"9d009c63-0000-0100-0000-686ff6590000\"", "_attachments": "attachments/", "_ts": 1752168025}, {"payPeriodId": "1080040773249287", "intervalCode": "MONTHLY", "status": "INITIAL", "description": "Monthly Payroll (2)", "startDate": "2025-09-01T00:00:00Z", "endDate": "2025-09-30T00:00:00Z", "submitByDate": "2025-09-18T00:00:00Z", "checkDate": "2025-09-22T00:00:00Z", "checkCount": 0, "id": "7a23a4a7-9262-4e31-853f-f26ae79582d1", "companyId": "17011209", "type": "payperiod", "_rid": "NmJkAKiCbEdCBAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdCBAEAAAAAAA==/", "_etag": "\"9d009e63-0000-0100-0000-686ff6590000\"", "_attachments": "attachments/", "_ts": 1752168025}, {"payPeriodId": "1080039085373309", "intervalCode": "MONTHLY", "status": "COMPLETED", "description": "Monthly Payroll (2)", "startDate": "2025-01-01T00:00:00Z", "endDate": "2025-01-31T00:00:00Z", "submitByDate": "2025-01-17T00:00:00Z", "checkDate": "2025-01-21T00:00:00Z", "checkCount": 1, "id": "d87721aa-50bc-4a4d-af6d-fdd9db12c6af", "companyId": "17011209", "type": "payperiod", "_rid": "NmJkAKiCbEdGBAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdGBAEAAAAAAA==/", "_etag": "\"9d00a963-0000-0100-0000-686ff6590000\"", "_attachments": "attachments/", "_ts": 1752168025}, {"payPeriodId": "1080039280953214", "intervalCode": "MONTHLY", "status": "COMPLETED", "description": "Monthly Payroll (2)", "startDate": "2025-02-01T00:00:00Z", "endDate": "2025-02-28T00:00:00Z", "submitByDate": "2025-02-18T00:00:00Z", "checkDate": "2025-02-20T00:00:00Z", "checkCount": 1, "id": "03f7b018-420f-4448-a483-570f46ed66de", "companyId": "17011209", "type": "payperiod", "_rid": "NmJkAKiCbEdHBAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdHBAEAAAAAAA==/", "_etag": "\"9d00ab63-0000-0100-0000-686ff6590000\"", "_attachments": "attachments/", "_ts": 1752168025}, {"payPeriodId": "1080039510161451", "intervalCode": "MONTHLY", "status": "COMPLETED", "description": "Monthly Payroll (2)", "startDate": "2025-03-01T00:00:00Z", "endDate": "2025-03-31T00:00:00Z", "submitByDate": "2025-03-18T00:00:00Z", "checkDate": "2025-03-20T00:00:00Z", "checkCount": 1, "id": "23be4066-ee16-44bb-bf9e-57fc82841729", "companyId": "17011209", "type": "payperiod", "_rid": "NmJkAKiCbEdIBAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdIBAEAAAAAAA==/", "_etag": "\"9d00ad63-0000-0100-0000-686ff6590000\"", "_attachments": "attachments/", "_ts": 1752168025}, {"payPeriodId": "1080039711534466", "intervalCode": "MONTHLY", "status": "COMPLETED", "description": "Monthly Payroll (2)", "startDate": "2025-04-01T00:00:00Z", "endDate": "2025-04-30T00:00:00Z", "submitByDate": "2025-04-17T00:00:00Z", "checkDate": "2025-04-21T00:00:00Z", "checkCount": 1, "id": "3f28c460-3c0c-462a-b927-f0f909496f13", "companyId": "17011209", "type": "payperiod", "_rid": "NmJkAKiCbEdJBAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdJBAEAAAAAAA==/", "_etag": "\"9d00b163-0000-0100-0000-686ff6590000\"", "_attachments": "attachments/", "_ts": 1752168025}, {"payPeriodId": "1080039899159546", "intervalCode": "MONTHLY", "status": "COMPLETED", "description": "Monthly Payroll (2)", "startDate": "2025-05-01T00:00:00Z", "endDate": "2025-05-31T00:00:00Z", "submitByDate": "2025-05-16T00:00:00Z", "checkDate": "2025-05-20T00:00:00Z", "checkCount": 1, "id": "5e6b22bd-9ec0-4b14-9684-88753c59d0c5", "companyId": "17011209", "type": "payperiod", "_rid": "NmJkAKiCbEdKBAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdKBAEAAAAAAA==/", "_etag": "\"9d00b363-0000-0100-0000-686ff65a0000\"", "_attachments": "attachments/", "_ts": 1752168026}, {"payPeriodId": "1080040085611872", "intervalCode": "MONTHLY", "status": "COMPLETED", "description": "Monthly Payroll (2)", "startDate": "2025-06-01T00:00:00Z", "endDate": "2025-06-30T00:00:00Z", "submitByDate": "2025-06-18T00:00:00Z", "checkDate": "2025-06-20T00:00:00Z", "checkCount": 1, "id": "d42febd6-b421-45e7-9a01-a7e2d1ff15f5", "companyId": "17011209", "type": "payperiod", "_rid": "NmJkAKiCbEdLBAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdLBAEAAAAAAA==/", "_etag": "\"9d00b663-0000-0100-0000-686ff65a0000\"", "_attachments": "attachments/", "_ts": 1752168026}, {"payPeriodId": "1080040325223532", "intervalCode": "MONTHLY", "status": "INITIAL", "description": "Monthly Payroll (2)", "startDate": "2025-07-01T00:00:00Z", "endDate": "2025-07-31T00:00:00Z", "submitByDate": "2025-07-17T00:00:00Z", "checkDate": "2025-07-21T00:00:00Z", "checkCount": 0, "id": "e4c829a7-40a6-4c64-b8c5-ac5eba7ff982", "companyId": "17011209", "type": "payperiod", "_rid": "NmJkAKiCbEdMBAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdMBAEAAAAAAA==/", "_etag": "\"9d00ba63-0000-0100-0000-686ff65a0000\"", "_attachments": "attachments/", "_ts": 1752168026}, {"payPeriodId": "1080040539138355", "intervalCode": "MONTHLY", "status": "INITIAL", "description": "Monthly Payroll (2)", "startDate": "2025-08-01T00:00:00Z", "endDate": "2025-08-31T00:00:00Z", "submitByDate": "2025-08-18T00:00:00Z", "checkDate": "2025-08-20T00:00:00Z", "checkCount": 0, "id": "7a2249f9-16b2-4cb2-aaca-87dd271e7e56", "companyId": "17011209", "type": "payperiod", "_rid": "NmJkAKiCbEdNBAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdNBAEAAAAAAA==/", "_etag": "\"9d00bb63-0000-0100-0000-686ff65a0000\"", "_attachments": "attachments/", "_ts": 1752168026}, {"payPeriodId": "1080040773249287", "intervalCode": "MONTHLY", "status": "INITIAL", "description": "Monthly Payroll (2)", "startDate": "2025-09-01T00:00:00Z", "endDate": "2025-09-30T00:00:00Z", "submitByDate": "2025-09-18T00:00:00Z", "checkDate": "2025-09-22T00:00:00Z", "checkCount": 0, "id": "90efb778-57af-4e0c-9343-e71b7a8d324e", "companyId": "17011209", "type": "payperiod", "_rid": "NmJkAKiCbEdOBAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdOBAEAAAAAAA==/", "_etag": "\"9d00bc63-0000-0100-0000-686ff65a0000\"", "_attachments": "attachments/", "_ts": 1752168026}], "links": [{"rel": "self", "href": "https://ca-payroll-ai-mock-sb-001-secure.nicebeach-8a7a52ab.eastus.azurecontainerapps.io/ca/companies/17011209/payperiods"}]}, "status_code": 200}