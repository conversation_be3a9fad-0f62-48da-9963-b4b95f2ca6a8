{"success": true, "company_id": "70209108", "data": {"metadata": {"contentItemCount": 76}, "content": [{"payPeriodId": "1040047051410858", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "1st PR", "startDate": "2025-02-17T00:00:00Z", "endDate": "2025-02-23T00:00:00Z", "submitByDate": "2025-02-26T00:00:00Z", "checkDate": "2025-02-28T00:00:00Z", "checkCount": 2, "id": "7f43e28f-c0da-4ec5-be82-e5a1dd76b7ca", "companyId": "70209108", "type": "payperiod", "_rid": "NmJkAKiCbEdo1wAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdo1wAAAAAAAA==/", "_etag": "\"9a00152b-0000-0100-0000-686fe05b0000\"", "_attachments": "attachments/", "_ts": 1752162395}, {"payPeriodId": "1040047051410870", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-03-17T00:00:00Z", "endDate": "2025-03-23T00:00:00Z", "submitByDate": "2025-03-26T00:00:00Z", "checkDate": "2025-03-28T00:00:00Z", "checkCount": 2, "id": "64c53f4a-affb-43e5-8242-1ee56faa8b08", "companyId": "70209108", "type": "payperiod", "_rid": "NmJkAKiCbEdp1wAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdp1wAAAAAAAA==/", "_etag": "\"9a00172b-0000-0100-0000-686fe05c0000\"", "_attachments": "attachments/", "_ts": 1752162396}, {"payPeriodId": "1040047051410882", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-04-14T00:00:00Z", "endDate": "2025-04-20T00:00:00Z", "submitByDate": "2025-04-23T00:00:00Z", "checkDate": "2025-04-25T00:00:00Z", "checkCount": 0, "id": "a96353d9-ba4f-4ae8-8d82-e5ef4758d4e4", "companyId": "70209108", "type": "payperiod", "_rid": "NmJkAKiCbEdq1wAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdq1wAAAAAAAA==/", "_etag": "\"9a001c2b-0000-0100-0000-686fe05c0000\"", "_attachments": "attachments/", "_ts": 1752162396}, {"payPeriodId": "1040047070050986", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-05-19T00:00:00Z", "endDate": "2025-05-25T00:00:00Z", "submitByDate": "2025-05-28T00:00:00Z", "checkDate": "2025-05-30T00:00:00Z", "checkCount": 0, "id": "4b1c6181-7517-4a04-8717-c7f0c52ad7d7", "companyId": "70209108", "type": "payperiod", "_rid": "NmJkAKiCbEdr1wAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdr1wAAAAAAAA==/", "_etag": "\"9a001e2b-0000-0100-0000-686fe05c0000\"", "_attachments": "attachments/", "_ts": 1752162396}, {"payPeriodId": "1040047194185326", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-06-16T00:00:00Z", "endDate": "2025-06-22T00:00:00Z", "submitByDate": "2025-06-25T00:00:00Z", "checkDate": "2025-06-27T00:00:00Z", "checkCount": 0, "id": "5ab313d1-dfa2-40ca-a805-3aeaba14a203", "companyId": "70209108", "type": "payperiod", "_rid": "NmJkAKiCbEds1wAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEds1wAAAAAAAA==/", "_etag": "\"9a00202b-0000-0100-0000-686fe05c0000\"", "_attachments": "attachments/", "_ts": 1752162396}, {"payPeriodId": "1040047202900609", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-06-23T00:00:00Z", "endDate": "2025-06-29T00:00:00Z", "submitByDate": "2025-07-01T00:00:00Z", "checkDate": "2025-07-03T00:00:00Z", "checkCount": 0, "id": "bf4ef3df-0fff-4929-a93d-19ff834b8d80", "companyId": "70209108", "type": "payperiod", "_rid": "NmJkAKiCbEdt1wAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdt1wAAAAAAAA==/", "_etag": "\"9a00242b-0000-0100-0000-686fe05c0000\"", "_attachments": "attachments/", "_ts": 1752162396}, {"payPeriodId": "1040047278926506", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-06-30T00:00:00Z", "endDate": "2025-07-06T00:00:00Z", "submitByDate": "2025-07-09T00:00:00Z", "checkDate": "2025-07-11T00:00:00Z", "checkCount": 0, "id": "63942862-a452-4eab-8dba-b726e7ba281b", "companyId": "70209108", "type": "payperiod", "_rid": "NmJkAKiCbEdu1wAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdu1wAAAAAAAA==/", "_etag": "\"9a00282b-0000-0100-0000-686fe05c0000\"", "_attachments": "attachments/", "_ts": 1752162396}, {"payPeriodId": "1040047320963493", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-07-07T00:00:00Z", "endDate": "2025-07-13T00:00:00Z", "submitByDate": "2025-07-16T00:00:00Z", "checkDate": "2025-07-18T00:00:00Z", "checkCount": 0, "id": "ea504afd-8984-400e-9a79-7f7bcb6d1631", "companyId": "70209108", "type": "payperiod", "_rid": "NmJkAKiCbEdv1wAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdv1wAAAAAAAA==/", "_etag": "\"9a002d2b-0000-0100-0000-686fe05c0000\"", "_attachments": "attachments/", "_ts": 1752162396}, {"payPeriodId": "1040047361614598", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-07-14T00:00:00Z", "endDate": "2025-07-20T00:00:00Z", "submitByDate": "2025-07-23T00:00:00Z", "checkDate": "2025-07-25T00:00:00Z", "checkCount": 0, "id": "553ccb0b-a6be-44e2-b93e-9cbcbab63bc0", "companyId": "70209108", "type": "payperiod", "_rid": "NmJkAKiCbEdw1wAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdw1wAAAAAAAA==/", "_etag": "\"9a002f2b-0000-0100-0000-686fe05c0000\"", "_attachments": "attachments/", "_ts": 1752162396}, {"payPeriodId": "1040047388415582", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-07-21T00:00:00Z", "endDate": "2025-07-27T00:00:00Z", "submitByDate": "2025-07-30T00:00:00Z", "checkDate": "2025-08-01T00:00:00Z", "checkCount": 0, "id": "f29f9419-9796-4cbf-8fbe-4403382ca217", "companyId": "70209108", "type": "payperiod", "_rid": "NmJkAKiCbEdx1wAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdx1wAAAAAAAA==/", "_etag": "\"9a00332b-0000-0100-0000-686fe05c0000\"", "_attachments": "attachments/", "_ts": 1752162396}, {"payPeriodId": "1040047446624081", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-07-28T00:00:00Z", "endDate": "2025-08-03T00:00:00Z", "submitByDate": "2025-08-06T00:00:00Z", "checkDate": "2025-08-08T00:00:00Z", "checkCount": 0, "id": "073c0e4e-f44d-47bc-b05e-56108ab9547e", "companyId": "70209108", "type": "payperiod", "_rid": "NmJkAKiCbEdy1wAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdy1wAAAAAAAA==/", "_etag": "\"9a00382b-0000-0100-0000-686fe05c0000\"", "_attachments": "attachments/", "_ts": 1752162396}, {"payPeriodId": "1040047481846318", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-08-04T00:00:00Z", "endDate": "2025-08-10T00:00:00Z", "submitByDate": "2025-08-13T00:00:00Z", "checkDate": "2025-08-15T00:00:00Z", "checkCount": 0, "id": "8e7c12df-4b8d-4d5b-ac60-ca201325ebca", "companyId": "70209108", "type": "payperiod", "_rid": "NmJkAKiCbEdz1wAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdz1wAAAAAAAA==/", "_etag": "\"9a003c2b-0000-0100-0000-686fe05c0000\"", "_attachments": "attachments/", "_ts": 1752162396}, {"payPeriodId": "1040047523562546", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-08-11T00:00:00Z", "endDate": "2025-08-17T00:00:00Z", "submitByDate": "2025-08-20T00:00:00Z", "checkDate": "2025-08-22T00:00:00Z", "checkCount": 0, "id": "841775da-1d74-4c6b-ae8f-7ca569b03ad8", "companyId": "70209108", "type": "payperiod", "_rid": "NmJkAKiCbEd01wAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEd01wAAAAAAAA==/", "_etag": "\"9a003e2b-0000-0100-0000-686fe05c0000\"", "_attachments": "attachments/", "_ts": 1752162396}, {"payPeriodId": "1040047558417953", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-08-18T00:00:00Z", "endDate": "2025-08-24T00:00:00Z", "submitByDate": "2025-08-27T00:00:00Z", "checkDate": "2025-08-29T00:00:00Z", "checkCount": 0, "id": "8a81aa8e-3351-4260-8f1b-0116822e3926", "companyId": "70209108", "type": "payperiod", "_rid": "NmJkAKiCbEd11wAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEd11wAAAAAAAA==/", "_etag": "\"9a00402b-0000-0100-0000-686fe05c0000\"", "_attachments": "attachments/", "_ts": 1752162396}, {"payPeriodId": "1040047579974859", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-08-25T00:00:00Z", "endDate": "2025-08-31T00:00:00Z", "submitByDate": "2025-09-03T00:00:00Z", "checkDate": "2025-09-05T00:00:00Z", "checkCount": 0, "id": "d28c50f7-e0a6-4cca-b127-acab8f64a443", "companyId": "70209108", "type": "payperiod", "_rid": "NmJkAKiCbEd21wAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEd21wAAAAAAAA==/", "_etag": "\"9a00442b-0000-0100-0000-686fe05d0000\"", "_attachments": "attachments/", "_ts": 1752162397}, {"payPeriodId": "1040047634803064", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-09-01T00:00:00Z", "endDate": "2025-09-07T00:00:00Z", "submitByDate": "2025-09-10T00:00:00Z", "checkDate": "2025-09-12T00:00:00Z", "checkCount": 0, "id": "64b375c1-0cf5-43ea-84a5-b84ffc8864df", "companyId": "70209108", "type": "payperiod", "_rid": "NmJkAKiCbEd31wAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEd31wAAAAAAAA==/", "_etag": "\"9a00452b-0000-0100-0000-686fe05d0000\"", "_attachments": "attachments/", "_ts": 1752162397}, {"payPeriodId": "1040047674306143", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-09-08T00:00:00Z", "endDate": "2025-09-14T00:00:00Z", "submitByDate": "2025-09-17T00:00:00Z", "checkDate": "2025-09-19T00:00:00Z", "checkCount": 0, "id": "e580b635-0a5b-4bff-9503-786b2d4803a8", "companyId": "70209108", "type": "payperiod", "_rid": "NmJkAKiCbEd41wAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEd41wAAAAAAAA==/", "_etag": "\"9a00472b-0000-0100-0000-686fe05d0000\"", "_attachments": "attachments/", "_ts": 1752162397}, {"payPeriodId": "1040047710309804", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-09-15T00:00:00Z", "endDate": "2025-09-21T00:00:00Z", "submitByDate": "2025-09-24T00:00:00Z", "checkDate": "2025-09-26T00:00:00Z", "checkCount": 0, "id": "5b812889-35a4-4308-a619-358d0540f590", "companyId": "70209108", "type": "payperiod", "_rid": "NmJkAKiCbEd51wAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEd51wAAAAAAAA==/", "_etag": "\"9a004b2b-0000-0100-0000-686fe05d0000\"", "_attachments": "attachments/", "_ts": 1752162397}, {"payPeriodId": "1040047741289402", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-09-22T00:00:00Z", "endDate": "2025-09-28T00:00:00Z", "submitByDate": "2025-10-01T00:00:00Z", "checkDate": "2025-10-03T00:00:00Z", "checkCount": 0, "id": "33db41b1-42e4-4024-b2c3-7d3587e8f651", "companyId": "70209108", "type": "payperiod", "_rid": "NmJkAKiCbEd61wAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEd61wAAAAAAAA==/", "_etag": "\"9a00502b-0000-0100-0000-686fe05d0000\"", "_attachments": "attachments/", "_ts": 1752162397}, {"payPeriodId": "1040047051410858", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "1st PR", "startDate": "2025-02-17T00:00:00Z", "endDate": "2025-02-23T00:00:00Z", "submitByDate": "2025-02-26T00:00:00Z", "checkDate": "2025-02-28T00:00:00Z", "checkCount": 2, "id": "8524df55-334b-41f0-b6c1-c48fe27db726", "companyId": "70209108", "type": "payperiod", "_rid": "NmJkAKiCbEeA1wAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeA1wAAAAAAAA==/", "_etag": "\"9a005c2b-0000-0100-0000-686fe05d0000\"", "_attachments": "attachments/", "_ts": 1752162397}, {"payPeriodId": "1040047051410870", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-03-17T00:00:00Z", "endDate": "2025-03-23T00:00:00Z", "submitByDate": "2025-03-26T00:00:00Z", "checkDate": "2025-03-28T00:00:00Z", "checkCount": 2, "id": "5ef6495d-96ba-4168-8f64-2a679ddd9a5d", "companyId": "70209108", "type": "payperiod", "_rid": "NmJkAKiCbEeB1wAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeB1wAAAAAAAA==/", "_etag": "\"9a005f2b-0000-0100-0000-686fe05d0000\"", "_attachments": "attachments/", "_ts": 1752162397}, {"payPeriodId": "1040047051410882", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-04-14T00:00:00Z", "endDate": "2025-04-20T00:00:00Z", "submitByDate": "2025-04-23T00:00:00Z", "checkDate": "2025-04-25T00:00:00Z", "checkCount": 2, "id": "64dc33ef-1abe-46ef-9812-f53c1f7483b6", "companyId": "70209108", "type": "payperiod", "_rid": "NmJkAKiCbEeC1wAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeC1wAAAAAAAA==/", "_etag": "\"9a00602b-0000-0100-0000-686fe05d0000\"", "_attachments": "attachments/", "_ts": 1752162397}, {"payPeriodId": "1040047070050986", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-05-19T00:00:00Z", "endDate": "2025-05-25T00:00:00Z", "submitByDate": "2025-05-28T00:00:00Z", "checkDate": "2025-05-30T00:00:00Z", "checkCount": 2, "id": "531610e4-4794-4ba4-9419-4ddaac1c84a1", "companyId": "70209108", "type": "payperiod", "_rid": "NmJkAKiCbEeD1wAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeD1wAAAAAAAA==/", "_etag": "\"9a00652b-0000-0100-0000-686fe05d0000\"", "_attachments": "attachments/", "_ts": 1752162397}, {"payPeriodId": "1040047194185326", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-06-16T00:00:00Z", "endDate": "2025-06-22T00:00:00Z", "submitByDate": "2025-06-25T00:00:00Z", "checkDate": "2025-06-27T00:00:00Z", "checkCount": 2, "id": "b5a9aa72-5451-447d-aab8-d31b95bc09ea", "companyId": "70209108", "type": "payperiod", "_rid": "NmJkAKiCbEeE1wAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeE1wAAAAAAAA==/", "_etag": "\"9a00672b-0000-0100-0000-686fe05e0000\"", "_attachments": "attachments/", "_ts": 1752162398}, {"payPeriodId": "1040047202900609", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-06-23T00:00:00Z", "endDate": "2025-06-29T00:00:00Z", "submitByDate": "2025-07-01T00:00:00Z", "checkDate": "2025-07-03T00:00:00Z", "checkCount": 0, "id": "8d006237-d107-4e43-b28a-cb036cb10a2a", "companyId": "70209108", "type": "payperiod", "_rid": "NmJkAKiCbEeF1wAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeF1wAAAAAAAA==/", "_etag": "\"9a00692b-0000-0100-0000-686fe05e0000\"", "_attachments": "attachments/", "_ts": 1752162398}, {"payPeriodId": "1040047278926506", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-06-30T00:00:00Z", "endDate": "2025-07-06T00:00:00Z", "submitByDate": "2025-07-09T00:00:00Z", "checkDate": "2025-07-11T00:00:00Z", "checkCount": 0, "id": "4e5ee040-0ad4-44c0-b263-8b6f824ad3d2", "companyId": "70209108", "type": "payperiod", "_rid": "NmJkAKiCbEeG1wAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeG1wAAAAAAAA==/", "_etag": "\"9a00722b-0000-0100-0000-686fe05e0000\"", "_attachments": "attachments/", "_ts": 1752162398}, {"payPeriodId": "1040047320963493", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-07-07T00:00:00Z", "endDate": "2025-07-13T00:00:00Z", "submitByDate": "2025-07-16T00:00:00Z", "checkDate": "2025-07-18T00:00:00Z", "checkCount": 0, "id": "c3400600-2d11-4bcb-8865-213f5191d31f", "companyId": "70209108", "type": "payperiod", "_rid": "NmJkAKiCbEeH1wAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeH1wAAAAAAAA==/", "_etag": "\"9a00762b-0000-0100-0000-686fe05e0000\"", "_attachments": "attachments/", "_ts": 1752162398}, {"payPeriodId": "1040047361614598", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-07-14T00:00:00Z", "endDate": "2025-07-20T00:00:00Z", "submitByDate": "2025-07-23T00:00:00Z", "checkDate": "2025-07-25T00:00:00Z", "checkCount": 0, "id": "a29a9bc3-8ca7-4e8f-b564-e343ee67fbaf", "companyId": "70209108", "type": "payperiod", "_rid": "NmJkAKiCbEeI1wAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeI1wAAAAAAAA==/", "_etag": "\"9a00782b-0000-0100-0000-686fe05e0000\"", "_attachments": "attachments/", "_ts": 1752162398}, {"payPeriodId": "1040047388415582", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-07-21T00:00:00Z", "endDate": "2025-07-27T00:00:00Z", "submitByDate": "2025-07-30T00:00:00Z", "checkDate": "2025-08-01T00:00:00Z", "checkCount": 0, "id": "ab493a4a-30f6-4ec8-8e90-9d672a9c715e", "companyId": "70209108", "type": "payperiod", "_rid": "NmJkAKiCbEeJ1wAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeJ1wAAAAAAAA==/", "_etag": "\"9a007a2b-0000-0100-0000-686fe05e0000\"", "_attachments": "attachments/", "_ts": 1752162398}, {"payPeriodId": "1040047446624081", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-07-28T00:00:00Z", "endDate": "2025-08-03T00:00:00Z", "submitByDate": "2025-08-06T00:00:00Z", "checkDate": "2025-08-08T00:00:00Z", "checkCount": 0, "id": "d79b7795-bbfb-473e-826b-4b69677d3195", "companyId": "70209108", "type": "payperiod", "_rid": "NmJkAKiCbEeK1wAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeK1wAAAAAAAA==/", "_etag": "\"9a007f2b-0000-0100-0000-686fe05e0000\"", "_attachments": "attachments/", "_ts": 1752162398}, {"payPeriodId": "1040047481846318", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-08-04T00:00:00Z", "endDate": "2025-08-10T00:00:00Z", "submitByDate": "2025-08-13T00:00:00Z", "checkDate": "2025-08-15T00:00:00Z", "checkCount": 0, "id": "d8388813-d00e-46e9-aad6-416eb1298d15", "companyId": "70209108", "type": "payperiod", "_rid": "NmJkAKiCbEeL1wAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeL1wAAAAAAAA==/", "_etag": "\"9a00812b-0000-0100-0000-686fe05e0000\"", "_attachments": "attachments/", "_ts": 1752162398}, {"payPeriodId": "1040047523562546", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-08-11T00:00:00Z", "endDate": "2025-08-17T00:00:00Z", "submitByDate": "2025-08-20T00:00:00Z", "checkDate": "2025-08-22T00:00:00Z", "checkCount": 0, "id": "03d7e685-dc52-461f-a376-a19cbfadb8ee", "companyId": "70209108", "type": "payperiod", "_rid": "NmJkAKiCbEeM1wAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeM1wAAAAAAAA==/", "_etag": "\"9a00822b-0000-0100-0000-686fe05e0000\"", "_attachments": "attachments/", "_ts": 1752162398}, {"payPeriodId": "1040047558417953", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-08-18T00:00:00Z", "endDate": "2025-08-24T00:00:00Z", "submitByDate": "2025-08-27T00:00:00Z", "checkDate": "2025-08-29T00:00:00Z", "checkCount": 0, "id": "58d7dce4-1ec9-4726-b6fc-b6915de27c8a", "companyId": "70209108", "type": "payperiod", "_rid": "NmJkAKiCbEeN1wAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeN1wAAAAAAAA==/", "_etag": "\"9a00832b-0000-0100-0000-686fe05e0000\"", "_attachments": "attachments/", "_ts": 1752162398}, {"payPeriodId": "1040047579974859", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-08-25T00:00:00Z", "endDate": "2025-08-31T00:00:00Z", "submitByDate": "2025-09-03T00:00:00Z", "checkDate": "2025-09-05T00:00:00Z", "checkCount": 0, "id": "ddf71378-f774-4241-9df1-c4ef86ba820e", "companyId": "70209108", "type": "payperiod", "_rid": "NmJkAKiCbEeO1wAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeO1wAAAAAAAA==/", "_etag": "\"9a00842b-0000-0100-0000-686fe05e0000\"", "_attachments": "attachments/", "_ts": 1752162398}, {"payPeriodId": "1040047634803064", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-09-01T00:00:00Z", "endDate": "2025-09-07T00:00:00Z", "submitByDate": "2025-09-10T00:00:00Z", "checkDate": "2025-09-12T00:00:00Z", "checkCount": 0, "id": "3cee6d7f-36d1-4280-b839-95bc4442413f", "companyId": "70209108", "type": "payperiod", "_rid": "NmJkAKiCbEeP1wAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeP1wAAAAAAAA==/", "_etag": "\"9a00862b-0000-0100-0000-686fe05e0000\"", "_attachments": "attachments/", "_ts": 1752162398}, {"payPeriodId": "1040047674306143", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-09-08T00:00:00Z", "endDate": "2025-09-14T00:00:00Z", "submitByDate": "2025-09-17T00:00:00Z", "checkDate": "2025-09-19T00:00:00Z", "checkCount": 0, "id": "2fb5bf62-415e-4413-ba11-20e436de5b2a", "companyId": "70209108", "type": "payperiod", "_rid": "NmJkAKiCbEeQ1wAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeQ1wAAAAAAAA==/", "_etag": "\"9a00872b-0000-0100-0000-686fe05e0000\"", "_attachments": "attachments/", "_ts": 1752162398}, {"payPeriodId": "1040047710309804", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-09-15T00:00:00Z", "endDate": "2025-09-21T00:00:00Z", "submitByDate": "2025-09-24T00:00:00Z", "checkDate": "2025-09-26T00:00:00Z", "checkCount": 0, "id": "f7b7b2d3-02e6-4492-be0d-d8ea1e2f3860", "companyId": "70209108", "type": "payperiod", "_rid": "NmJkAKiCbEeR1wAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeR1wAAAAAAAA==/", "_etag": "\"9a008a2b-0000-0100-0000-686fe05e0000\"", "_attachments": "attachments/", "_ts": 1752162398}, {"payPeriodId": "1040047741289402", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-09-22T00:00:00Z", "endDate": "2025-09-28T00:00:00Z", "submitByDate": "2025-10-01T00:00:00Z", "checkDate": "2025-10-03T00:00:00Z", "checkCount": 0, "id": "4c9940dc-25cb-456a-8129-77da0500db12", "companyId": "70209108", "type": "payperiod", "_rid": "NmJkAKiCbEeS1wAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeS1wAAAAAAAA==/", "_etag": "\"9a008c2b-0000-0100-0000-686fe05f0000\"", "_attachments": "attachments/", "_ts": 1752162399}, {"payPeriodId": "1040047051410858", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "1st PR", "startDate": "2025-02-17T00:00:00Z", "endDate": "2025-02-23T00:00:00Z", "submitByDate": "2025-02-26T00:00:00Z", "checkDate": "2025-02-28T00:00:00Z", "checkCount": 2, "id": "9dbf6d43-e20b-4e4b-9ac0-02c5e6e5a977", "companyId": "70209108", "type": "payperiod", "_rid": "NmJkAKiCbEeV0AEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeV0AEAAAAAAA==/", "_etag": "\"a000a547-0000-0100-0000-687007300000\"", "_attachments": "attachments/", "_ts": 1752172336}, {"payPeriodId": "1040047051410870", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-03-17T00:00:00Z", "endDate": "2025-03-23T00:00:00Z", "submitByDate": "2025-03-26T00:00:00Z", "checkDate": "2025-03-28T00:00:00Z", "checkCount": 2, "id": "f46a3392-ee49-4a68-a31e-1ad3825becec", "companyId": "70209108", "type": "payperiod", "_rid": "NmJkAKiCbEeW0AEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeW0AEAAAAAAA==/", "_etag": "\"a000a847-0000-0100-0000-687007300000\"", "_attachments": "attachments/", "_ts": 1752172336}, {"payPeriodId": "1040047051410882", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-04-14T00:00:00Z", "endDate": "2025-04-20T00:00:00Z", "submitByDate": "2025-04-23T00:00:00Z", "checkDate": "2025-04-25T00:00:00Z", "checkCount": 0, "id": "53499297-eab5-4638-9f81-42d8ed518819", "companyId": "70209108", "type": "payperiod", "_rid": "NmJkAKiCbEeX0AEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeX0AEAAAAAAA==/", "_etag": "\"a000ac47-0000-0100-0000-687007300000\"", "_attachments": "attachments/", "_ts": 1752172336}, {"payPeriodId": "1040047070050986", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-05-19T00:00:00Z", "endDate": "2025-05-25T00:00:00Z", "submitByDate": "2025-05-28T00:00:00Z", "checkDate": "2025-05-30T00:00:00Z", "checkCount": 0, "id": "cf4187ba-88dc-4059-9709-850360ad45da", "companyId": "70209108", "type": "payperiod", "_rid": "NmJkAKiCbEeY0AEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeY0AEAAAAAAA==/", "_etag": "\"a000af47-0000-0100-0000-687007300000\"", "_attachments": "attachments/", "_ts": 1752172336}, {"payPeriodId": "1040047194185326", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-06-16T00:00:00Z", "endDate": "2025-06-22T00:00:00Z", "submitByDate": "2025-06-25T00:00:00Z", "checkDate": "2025-06-27T00:00:00Z", "checkCount": 0, "id": "acad60bd-ee99-4b2a-aa0d-ecb66bd4b353", "companyId": "70209108", "type": "payperiod", "_rid": "NmJkAKiCbEeZ0AEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeZ0AEAAAAAAA==/", "_etag": "\"a000b647-0000-0100-0000-687007300000\"", "_attachments": "attachments/", "_ts": 1752172336}, {"payPeriodId": "1040047202900609", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-06-23T00:00:00Z", "endDate": "2025-06-29T00:00:00Z", "submitByDate": "2025-07-01T00:00:00Z", "checkDate": "2025-07-03T00:00:00Z", "checkCount": 0, "id": "78c4e43d-2e65-4132-9b85-4cd2e9921c29", "companyId": "70209108", "type": "payperiod", "_rid": "NmJkAKiCbEea0AEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEea0AEAAAAAAA==/", "_etag": "\"a000b747-0000-0100-0000-687007300000\"", "_attachments": "attachments/", "_ts": 1752172336}, {"payPeriodId": "1040047278926506", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-06-30T00:00:00Z", "endDate": "2025-07-06T00:00:00Z", "submitByDate": "2025-07-09T00:00:00Z", "checkDate": "2025-07-11T00:00:00Z", "checkCount": 0, "id": "1955459b-2c1a-44a3-8c2e-9499b83f7108", "companyId": "70209108", "type": "payperiod", "_rid": "NmJkAKiCbEeb0AEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeb0AEAAAAAAA==/", "_etag": "\"a000ba47-0000-0100-0000-687007300000\"", "_attachments": "attachments/", "_ts": 1752172336}, {"payPeriodId": "1040047320963493", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-07-07T00:00:00Z", "endDate": "2025-07-13T00:00:00Z", "submitByDate": "2025-07-16T00:00:00Z", "checkDate": "2025-07-18T00:00:00Z", "checkCount": 0, "id": "21a13808-e25e-4b4b-a2ae-ed2b8f0ca5ab", "companyId": "70209108", "type": "payperiod", "_rid": "NmJkAKiCbEec0AEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEec0AEAAAAAAA==/", "_etag": "\"a000bc47-0000-0100-0000-687007310000\"", "_attachments": "attachments/", "_ts": 1752172337}, {"payPeriodId": "1040047361614598", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-07-14T00:00:00Z", "endDate": "2025-07-20T00:00:00Z", "submitByDate": "2025-07-23T00:00:00Z", "checkDate": "2025-07-25T00:00:00Z", "checkCount": 0, "id": "a5ba1d15-5c46-4047-b15b-bf51053be4af", "companyId": "70209108", "type": "payperiod", "_rid": "NmJkAKiCbEed0AEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEed0AEAAAAAAA==/", "_etag": "\"a000be47-0000-0100-0000-687007310000\"", "_attachments": "attachments/", "_ts": 1752172337}, {"payPeriodId": "1040047388415582", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-07-21T00:00:00Z", "endDate": "2025-07-27T00:00:00Z", "submitByDate": "2025-07-30T00:00:00Z", "checkDate": "2025-08-01T00:00:00Z", "checkCount": 0, "id": "df5c3188-ac05-48f1-b133-38cde63f906c", "companyId": "70209108", "type": "payperiod", "_rid": "NmJkAKiCbEee0AEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEee0AEAAAAAAA==/", "_etag": "\"a000c147-0000-0100-0000-687007310000\"", "_attachments": "attachments/", "_ts": 1752172337}, {"payPeriodId": "1040047446624081", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-07-28T00:00:00Z", "endDate": "2025-08-03T00:00:00Z", "submitByDate": "2025-08-06T00:00:00Z", "checkDate": "2025-08-08T00:00:00Z", "checkCount": 0, "id": "d64563ba-856f-451e-be9c-12e5dfd310b1", "companyId": "70209108", "type": "payperiod", "_rid": "NmJkAKiCbEef0AEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEef0AEAAAAAAA==/", "_etag": "\"a000c347-0000-0100-0000-687007310000\"", "_attachments": "attachments/", "_ts": 1752172337}, {"payPeriodId": "1040047481846318", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-08-04T00:00:00Z", "endDate": "2025-08-10T00:00:00Z", "submitByDate": "2025-08-13T00:00:00Z", "checkDate": "2025-08-15T00:00:00Z", "checkCount": 0, "id": "576b1878-026a-44c2-9938-0950db573037", "companyId": "70209108", "type": "payperiod", "_rid": "NmJkAKiCbEeg0AEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeg0AEAAAAAAA==/", "_etag": "\"a000c747-0000-0100-0000-687007310000\"", "_attachments": "attachments/", "_ts": 1752172337}, {"payPeriodId": "1040047523562546", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-08-11T00:00:00Z", "endDate": "2025-08-17T00:00:00Z", "submitByDate": "2025-08-20T00:00:00Z", "checkDate": "2025-08-22T00:00:00Z", "checkCount": 0, "id": "a3ace97b-2aed-4111-88b5-163b97929af7", "companyId": "70209108", "type": "payperiod", "_rid": "NmJkAKiCbEeh0AEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeh0AEAAAAAAA==/", "_etag": "\"a000cc47-0000-0100-0000-687007310000\"", "_attachments": "attachments/", "_ts": 1752172337}, {"payPeriodId": "1040047558417953", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-08-18T00:00:00Z", "endDate": "2025-08-24T00:00:00Z", "submitByDate": "2025-08-27T00:00:00Z", "checkDate": "2025-08-29T00:00:00Z", "checkCount": 0, "id": "fcc10991-466e-44c5-8e17-73ea2be8d64a", "companyId": "70209108", "type": "payperiod", "_rid": "NmJkAKiCbEei0AEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEei0AEAAAAAAA==/", "_etag": "\"a000ce47-0000-0100-0000-687007310000\"", "_attachments": "attachments/", "_ts": 1752172337}, {"payPeriodId": "1040047579974859", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-08-25T00:00:00Z", "endDate": "2025-08-31T00:00:00Z", "submitByDate": "2025-09-03T00:00:00Z", "checkDate": "2025-09-05T00:00:00Z", "checkCount": 0, "id": "df8f684a-c88f-4355-88d5-fc67fcbf8fd5", "companyId": "70209108", "type": "payperiod", "_rid": "NmJkAKiCbEej0AEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEej0AEAAAAAAA==/", "_etag": "\"a000d147-0000-0100-0000-687007310000\"", "_attachments": "attachments/", "_ts": 1752172337}, {"payPeriodId": "1040047634803064", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-09-01T00:00:00Z", "endDate": "2025-09-07T00:00:00Z", "submitByDate": "2025-09-10T00:00:00Z", "checkDate": "2025-09-12T00:00:00Z", "checkCount": 0, "id": "da05ad16-8e09-4470-8286-e68751f604c5", "companyId": "70209108", "type": "payperiod", "_rid": "NmJkAKiCbEek0AEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEek0AEAAAAAAA==/", "_etag": "\"a000d547-0000-0100-0000-687007310000\"", "_attachments": "attachments/", "_ts": 1752172337}, {"payPeriodId": "1040047674306143", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-09-08T00:00:00Z", "endDate": "2025-09-14T00:00:00Z", "submitByDate": "2025-09-17T00:00:00Z", "checkDate": "2025-09-19T00:00:00Z", "checkCount": 0, "id": "2254b8eb-567a-4ed1-8d6f-9a533fbb395a", "companyId": "70209108", "type": "payperiod", "_rid": "NmJkAKiCbEel0AEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEel0AEAAAAAAA==/", "_etag": "\"a000d847-0000-0100-0000-687007310000\"", "_attachments": "attachments/", "_ts": 1752172337}, {"payPeriodId": "1040047710309804", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-09-15T00:00:00Z", "endDate": "2025-09-21T00:00:00Z", "submitByDate": "2025-09-24T00:00:00Z", "checkDate": "2025-09-26T00:00:00Z", "checkCount": 0, "id": "1fa74c2c-d687-4014-9eb9-f1602aa51102", "companyId": "70209108", "type": "payperiod", "_rid": "NmJkAKiCbEem0AEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEem0AEAAAAAAA==/", "_etag": "\"a000da47-0000-0100-0000-687007310000\"", "_attachments": "attachments/", "_ts": 1752172337}, {"payPeriodId": "1040047741289402", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-09-22T00:00:00Z", "endDate": "2025-09-28T00:00:00Z", "submitByDate": "2025-10-01T00:00:00Z", "checkDate": "2025-10-03T00:00:00Z", "checkCount": 0, "id": "5f5e8a86-106b-4cd7-a1e2-8a1af3a5b07d", "companyId": "70209108", "type": "payperiod", "_rid": "NmJkAKiCbEen0AEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEen0AEAAAAAAA==/", "_etag": "\"a000e047-0000-0100-0000-687007310000\"", "_attachments": "attachments/", "_ts": 1752172337}, {"payPeriodId": "1040047051410858", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "1st PR", "startDate": "2025-02-17T00:00:00Z", "endDate": "2025-02-23T00:00:00Z", "submitByDate": "2025-02-26T00:00:00Z", "checkDate": "2025-02-28T00:00:00Z", "checkCount": 2, "id": "f7a923da-b781-45ab-a355-a0b871cf60a1", "companyId": "70209108", "type": "payperiod", "_rid": "NmJkAKiCbEet0AEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEet0AEAAAAAAA==/", "_etag": "\"a000ee47-0000-0100-0000-687007320000\"", "_attachments": "attachments/", "_ts": 1752172338}, {"payPeriodId": "1040047051410870", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-03-17T00:00:00Z", "endDate": "2025-03-23T00:00:00Z", "submitByDate": "2025-03-26T00:00:00Z", "checkDate": "2025-03-28T00:00:00Z", "checkCount": 2, "id": "571a3ff6-89b7-4b59-830f-de6fb2a8c40a", "companyId": "70209108", "type": "payperiod", "_rid": "NmJkAKiCbEeu0AEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeu0AEAAAAAAA==/", "_etag": "\"a000f047-0000-0100-0000-687007320000\"", "_attachments": "attachments/", "_ts": 1752172338}, {"payPeriodId": "1040047051410882", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-04-14T00:00:00Z", "endDate": "2025-04-20T00:00:00Z", "submitByDate": "2025-04-23T00:00:00Z", "checkDate": "2025-04-25T00:00:00Z", "checkCount": 2, "id": "ce6103ab-394c-45d0-8053-fff21b0f7655", "companyId": "70209108", "type": "payperiod", "_rid": "NmJkAKiCbEev0AEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEev0AEAAAAAAA==/", "_etag": "\"a000f347-0000-0100-0000-687007320000\"", "_attachments": "attachments/", "_ts": 1752172338}, {"payPeriodId": "1040047070050986", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-05-19T00:00:00Z", "endDate": "2025-05-25T00:00:00Z", "submitByDate": "2025-05-28T00:00:00Z", "checkDate": "2025-05-30T00:00:00Z", "checkCount": 2, "id": "b5d6542e-686b-4ef9-90ea-7b6e85f3ee95", "companyId": "70209108", "type": "payperiod", "_rid": "NmJkAKiCbEew0AEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEew0AEAAAAAAA==/", "_etag": "\"a000f547-0000-0100-0000-687007320000\"", "_attachments": "attachments/", "_ts": 1752172338}, {"payPeriodId": "1040047194185326", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-06-16T00:00:00Z", "endDate": "2025-06-22T00:00:00Z", "submitByDate": "2025-06-25T00:00:00Z", "checkDate": "2025-06-27T00:00:00Z", "checkCount": 2, "id": "3d3da50e-09d5-4e99-b4e7-2d4c559371e5", "companyId": "70209108", "type": "payperiod", "_rid": "NmJkAKiCbEex0AEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEex0AEAAAAAAA==/", "_etag": "\"a000f947-0000-0100-0000-687007320000\"", "_attachments": "attachments/", "_ts": 1752172338}, {"payPeriodId": "1040047202900609", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-06-23T00:00:00Z", "endDate": "2025-06-29T00:00:00Z", "submitByDate": "2025-07-01T00:00:00Z", "checkDate": "2025-07-03T00:00:00Z", "checkCount": 0, "id": "0fb1837a-87c5-48e6-b588-db503018062c", "companyId": "70209108", "type": "payperiod", "_rid": "NmJkAKiCbEey0AEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEey0AEAAAAAAA==/", "_etag": "\"a000fd47-0000-0100-0000-687007320000\"", "_attachments": "attachments/", "_ts": 1752172338}, {"payPeriodId": "1040047278926506", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-06-30T00:00:00Z", "endDate": "2025-07-06T00:00:00Z", "submitByDate": "2025-07-09T00:00:00Z", "checkDate": "2025-07-11T00:00:00Z", "checkCount": 0, "id": "bdf990bc-5bc6-4ba5-8eb2-6fbc1556a4b1", "companyId": "70209108", "type": "payperiod", "_rid": "NmJkAKiCbEez0AEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEez0AEAAAAAAA==/", "_etag": "\"a0000048-0000-0100-0000-687007320000\"", "_attachments": "attachments/", "_ts": 1752172338}, {"payPeriodId": "1040047320963493", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-07-07T00:00:00Z", "endDate": "2025-07-13T00:00:00Z", "submitByDate": "2025-07-16T00:00:00Z", "checkDate": "2025-07-18T00:00:00Z", "checkCount": 0, "id": "a77400af-b93a-4154-9e8d-4033e1eda3f2", "companyId": "70209108", "type": "payperiod", "_rid": "NmJkAKiCbEe00AEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEe00AEAAAAAAA==/", "_etag": "\"a0000148-0000-0100-0000-687007320000\"", "_attachments": "attachments/", "_ts": 1752172338}, {"payPeriodId": "1040047361614598", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-07-14T00:00:00Z", "endDate": "2025-07-20T00:00:00Z", "submitByDate": "2025-07-23T00:00:00Z", "checkDate": "2025-07-25T00:00:00Z", "checkCount": 0, "id": "86698569-ac85-41c4-b679-163e313d06cb", "companyId": "70209108", "type": "payperiod", "_rid": "NmJkAKiCbEe10AEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEe10AEAAAAAAA==/", "_etag": "\"a0000448-0000-0100-0000-687007330000\"", "_attachments": "attachments/", "_ts": 1752172339}, {"payPeriodId": "1040047388415582", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-07-21T00:00:00Z", "endDate": "2025-07-27T00:00:00Z", "submitByDate": "2025-07-30T00:00:00Z", "checkDate": "2025-08-01T00:00:00Z", "checkCount": 0, "id": "8dce3043-b5f9-4737-9f56-e9f0cca0208b", "companyId": "70209108", "type": "payperiod", "_rid": "NmJkAKiCbEe20AEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEe20AEAAAAAAA==/", "_etag": "\"a0000848-0000-0100-0000-687007330000\"", "_attachments": "attachments/", "_ts": 1752172339}, {"payPeriodId": "1040047446624081", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-07-28T00:00:00Z", "endDate": "2025-08-03T00:00:00Z", "submitByDate": "2025-08-06T00:00:00Z", "checkDate": "2025-08-08T00:00:00Z", "checkCount": 0, "id": "0bdfadc8-31b4-45cd-af54-b9eb86957a74", "companyId": "70209108", "type": "payperiod", "_rid": "NmJkAKiCbEe30AEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEe30AEAAAAAAA==/", "_etag": "\"a0000b48-0000-0100-0000-687007330000\"", "_attachments": "attachments/", "_ts": 1752172339}, {"payPeriodId": "1040047481846318", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-08-04T00:00:00Z", "endDate": "2025-08-10T00:00:00Z", "submitByDate": "2025-08-13T00:00:00Z", "checkDate": "2025-08-15T00:00:00Z", "checkCount": 0, "id": "ac3e1a58-ee48-4068-9663-2d8fb98b1e63", "companyId": "70209108", "type": "payperiod", "_rid": "NmJkAKiCbEe40AEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEe40AEAAAAAAA==/", "_etag": "\"a0000c48-0000-0100-0000-687007330000\"", "_attachments": "attachments/", "_ts": 1752172339}, {"payPeriodId": "1040047523562546", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-08-11T00:00:00Z", "endDate": "2025-08-17T00:00:00Z", "submitByDate": "2025-08-20T00:00:00Z", "checkDate": "2025-08-22T00:00:00Z", "checkCount": 0, "id": "be3ca55d-cc5f-4fa9-bbdb-0ac83175cad3", "companyId": "70209108", "type": "payperiod", "_rid": "NmJkAKiCbEe50AEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEe50AEAAAAAAA==/", "_etag": "\"a0000f48-0000-0100-0000-687007330000\"", "_attachments": "attachments/", "_ts": 1752172339}, {"payPeriodId": "1040047558417953", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-08-18T00:00:00Z", "endDate": "2025-08-24T00:00:00Z", "submitByDate": "2025-08-27T00:00:00Z", "checkDate": "2025-08-29T00:00:00Z", "checkCount": 0, "id": "c504b4e0-1411-4511-bbbe-5d27b5c7fc4f", "companyId": "70209108", "type": "payperiod", "_rid": "NmJkAKiCbEe60AEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEe60AEAAAAAAA==/", "_etag": "\"a0001148-0000-0100-0000-687007330000\"", "_attachments": "attachments/", "_ts": 1752172339}, {"payPeriodId": "1040047579974859", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-08-25T00:00:00Z", "endDate": "2025-08-31T00:00:00Z", "submitByDate": "2025-09-03T00:00:00Z", "checkDate": "2025-09-05T00:00:00Z", "checkCount": 0, "id": "5d044239-7f96-44b2-82b2-7886ab1cc4a2", "companyId": "70209108", "type": "payperiod", "_rid": "NmJkAKiCbEe70AEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEe70AEAAAAAAA==/", "_etag": "\"a0001248-0000-0100-0000-687007330000\"", "_attachments": "attachments/", "_ts": 1752172339}, {"payPeriodId": "1040047634803064", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-09-01T00:00:00Z", "endDate": "2025-09-07T00:00:00Z", "submitByDate": "2025-09-10T00:00:00Z", "checkDate": "2025-09-12T00:00:00Z", "checkCount": 0, "id": "0b16e40e-a856-4f6a-b4a3-ecd9cf2a59cf", "companyId": "70209108", "type": "payperiod", "_rid": "NmJkAKiCbEe80AEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEe80AEAAAAAAA==/", "_etag": "\"a0001548-0000-0100-0000-687007330000\"", "_attachments": "attachments/", "_ts": 1752172339}, {"payPeriodId": "1040047674306143", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-09-08T00:00:00Z", "endDate": "2025-09-14T00:00:00Z", "submitByDate": "2025-09-17T00:00:00Z", "checkDate": "2025-09-19T00:00:00Z", "checkCount": 0, "id": "6f754661-5b8d-437c-af89-03c95514435c", "companyId": "70209108", "type": "payperiod", "_rid": "NmJkAKiCbEe90AEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEe90AEAAAAAAA==/", "_etag": "\"a0001848-0000-0100-0000-687007330000\"", "_attachments": "attachments/", "_ts": 1752172339}, {"payPeriodId": "1040047710309804", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-09-15T00:00:00Z", "endDate": "2025-09-21T00:00:00Z", "submitByDate": "2025-09-24T00:00:00Z", "checkDate": "2025-09-26T00:00:00Z", "checkCount": 0, "id": "eb083ece-24f3-4769-a772-0bacea12f6ea", "companyId": "70209108", "type": "payperiod", "_rid": "NmJkAKiCbEe+0AEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEe+0AEAAAAAAA==/", "_etag": "\"a0001b48-0000-0100-0000-687007330000\"", "_attachments": "attachments/", "_ts": 1752172339}, {"payPeriodId": "1040047741289402", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-09-22T00:00:00Z", "endDate": "2025-09-28T00:00:00Z", "submitByDate": "2025-10-01T00:00:00Z", "checkDate": "2025-10-03T00:00:00Z", "checkCount": 0, "id": "d9937812-0d0a-40b4-8523-c5ccd75c3a93", "companyId": "70209108", "type": "payperiod", "_rid": "NmJkAKiCbEe-0AEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEe-0AEAAAAAAA==/", "_etag": "\"a0001c48-0000-0100-0000-687007330000\"", "_attachments": "attachments/", "_ts": 1752172339}], "links": [{"rel": "self", "href": "https://ca-payroll-ai-mock-sb-001-secure.nicebeach-8a7a52ab.eastus.azurecontainerapps.io/ca/companies/70209108/payperiods"}]}, "status_code": 200}