{"success": true, "company_id": "18056120", "data": {"metadata": {"contentItemCount": 38}, "content": [{"payPeriodId": "1090065692220320", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2024-12-27T00:00:00Z", "endDate": "2025-01-11T00:00:00Z", "submitByDate": "2025-01-13T00:00:00Z", "checkDate": "2025-01-15T00:00:00Z", "checkCount": 8, "id": "76ffa116-1a3e-4f23-8a75-6c2f4101a73f", "companyId": "18056120", "type": "payperiod", "_rid": "NmJkAKiCbEcLLgQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcLLgQAAAAAAA==/", "_etag": "\"a800871e-0000-0100-0000-68703ad00000\"", "_attachments": "attachments/", "_ts": 1752185552}, {"payPeriodId": "1090066292171142", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-01-12T00:00:00Z", "endDate": "2025-01-26T00:00:00Z", "submitByDate": "2025-01-29T00:00:00Z", "checkDate": "2025-01-31T00:00:00Z", "checkCount": 13, "id": "ea2b22b3-8674-44c1-8a97-88fa8c2e973d", "companyId": "18056120", "type": "payperiod", "_rid": "NmJkAKiCbEcMLgQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcMLgQAAAAAAA==/", "_etag": "\"a800881e-0000-0100-0000-68703ad00000\"", "_attachments": "attachments/", "_ts": 1752185552}, {"payPeriodId": "1090066292171143", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-01-27T00:00:00Z", "endDate": "2025-02-11T00:00:00Z", "submitByDate": "2025-02-12T00:00:00Z", "checkDate": "2025-02-14T00:00:00Z", "checkCount": 13, "id": "d6bb0774-5781-466e-ab44-bdc60931f04d", "companyId": "18056120", "type": "payperiod", "_rid": "NmJkAKiCbEcNLgQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcNLgQAAAAAAA==/", "_etag": "\"a8008c1e-0000-0100-0000-68703ad00000\"", "_attachments": "attachments/", "_ts": 1752185552}, {"payPeriodId": "1090067000220031", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-02-12T00:00:00Z", "endDate": "2025-02-26T00:00:00Z", "submitByDate": "2025-02-26T00:00:00Z", "checkDate": "2025-02-28T00:00:00Z", "checkCount": 8, "id": "e4982770-ae8c-418e-8b53-c259fea0048a", "companyId": "18056120", "type": "payperiod", "_rid": "NmJkAKiCbEcOLgQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcOLgQAAAAAAA==/", "_etag": "\"a800901e-0000-0100-0000-68703ad10000\"", "_attachments": "attachments/", "_ts": 1752185553}, {"payPeriodId": "1090067000220032", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-02-27T00:00:00Z", "endDate": "2025-03-11T00:00:00Z", "submitByDate": "2025-03-12T00:00:00Z", "checkDate": "2025-03-14T00:00:00Z", "checkCount": 8, "id": "c8e556dc-f36a-4c2e-86ff-b121d52babca", "companyId": "18056120", "type": "payperiod", "_rid": "NmJkAKiCbEcPLgQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcPLgQAAAAAAA==/", "_etag": "\"a800921e-0000-0100-0000-68703ad10000\"", "_attachments": "attachments/", "_ts": 1752185553}, {"payPeriodId": "1090067601314963", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-03-12T00:00:00Z", "endDate": "2025-03-26T00:00:00Z", "submitByDate": "2025-03-27T00:00:00Z", "checkDate": "2025-03-31T00:00:00Z", "checkCount": 13, "id": "088cdcdc-9404-430f-b007-96e9ec4b4ea2", "companyId": "18056120", "type": "payperiod", "_rid": "NmJkAKiCbEcQLgQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcQLgQAAAAAAA==/", "_etag": "\"a800931e-0000-0100-0000-68703ad10000\"", "_attachments": "attachments/", "_ts": 1752185553}, {"payPeriodId": "1090067601314964", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-03-27T00:00:00Z", "endDate": "2025-04-11T00:00:00Z", "submitByDate": "2025-04-11T00:00:00Z", "checkDate": "2025-04-15T00:00:00Z", "checkCount": 0, "id": "975def3c-be16-4f6e-8bfb-66bdf442fdc9", "companyId": "18056120", "type": "payperiod", "_rid": "NmJkAKiCbEcRLgQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcRLgQAAAAAAA==/", "_etag": "\"a800971e-0000-0100-0000-68703ad10000\"", "_attachments": "attachments/", "_ts": 1752185553}, {"payPeriodId": "1090068321009345", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-04-12T00:00:00Z", "endDate": "2025-04-26T00:00:00Z", "submitByDate": "2025-04-28T00:00:00Z", "checkDate": "2025-04-30T00:00:00Z", "checkCount": 0, "id": "a98d91fc-9c04-4bef-80cf-eccbe41bb217", "companyId": "18056120", "type": "payperiod", "_rid": "NmJkAKiCbEcSLgQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcSLgQAAAAAAA==/", "_etag": "\"a800991e-0000-0100-0000-68703ad10000\"", "_attachments": "attachments/", "_ts": 1752185553}, {"payPeriodId": "1090068321009346", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-04-27T00:00:00Z", "endDate": "2025-05-11T00:00:00Z", "submitByDate": "2025-05-13T00:00:00Z", "checkDate": "2025-05-15T00:00:00Z", "checkCount": 0, "id": "7e3ab0a3-ef89-4aed-98b2-eee347c7c7fe", "companyId": "18056120", "type": "payperiod", "_rid": "NmJkAKiCbEcTLgQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcTLgQAAAAAAA==/", "_etag": "\"a8009c1e-0000-0100-0000-68703ad10000\"", "_attachments": "attachments/", "_ts": 1752185553}, {"payPeriodId": "1090068987795714", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-05-12T00:00:00Z", "endDate": "2025-05-26T00:00:00Z", "submitByDate": "2025-05-28T00:00:00Z", "checkDate": "2025-05-30T00:00:00Z", "checkCount": 0, "id": "b5fccf43-dc08-4fd8-b5d3-a0db26b99ecf", "companyId": "18056120", "type": "payperiod", "_rid": "NmJkAKiCbEcULgQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcULgQAAAAAAA==/", "_etag": "\"a8009d1e-0000-0100-0000-68703ad10000\"", "_attachments": "attachments/", "_ts": 1752185553}, {"payPeriodId": "1090068987795715", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-05-27T00:00:00Z", "endDate": "2025-06-11T00:00:00Z", "submitByDate": "2025-06-11T00:00:00Z", "checkDate": "2025-06-13T00:00:00Z", "checkCount": 0, "id": "aa772fa0-8d10-429a-bfe6-dc43dd089eb7", "companyId": "18056120", "type": "payperiod", "_rid": "NmJkAKiCbEcVLgQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcVLgQAAAAAAA==/", "_etag": "\"a8009f1e-0000-0100-0000-68703ad10000\"", "_attachments": "attachments/", "_ts": 1752185553}, {"payPeriodId": "1090069597159077", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-06-12T00:00:00Z", "endDate": "2025-06-26T00:00:00Z", "submitByDate": "2025-06-26T00:00:00Z", "checkDate": "2025-06-30T00:00:00Z", "checkCount": 0, "id": "71385859-902b-4e3b-b3b1-6f319e45bb46", "companyId": "18056120", "type": "payperiod", "_rid": "NmJkAKiCbEcWLgQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcWLgQAAAAAAA==/", "_etag": "\"a800a01e-0000-0100-0000-68703ad10000\"", "_attachments": "attachments/", "_ts": 1752185553}, {"payPeriodId": "1090069597159078", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-06-27T00:00:00Z", "endDate": "2025-07-11T00:00:00Z", "submitByDate": "2025-07-11T00:00:00Z", "checkDate": "2025-07-15T00:00:00Z", "checkCount": 0, "id": "c78f2538-c541-4454-aa32-44c7691a2b96", "companyId": "18056120", "type": "payperiod", "_rid": "NmJkAKiCbEcXLgQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcXLgQAAAAAAA==/", "_etag": "\"a800a31e-0000-0100-0000-68703ad10000\"", "_attachments": "attachments/", "_ts": 1752185553}, {"payPeriodId": "1090070275769847", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-07-12T00:00:00Z", "endDate": "2025-07-26T00:00:00Z", "submitByDate": "2025-07-29T00:00:00Z", "checkDate": "2025-07-31T00:00:00Z", "checkCount": 0, "id": "d6e5dec2-7ccc-43a8-8763-3c67aa318c4d", "companyId": "18056120", "type": "payperiod", "_rid": "NmJkAKiCbEcYLgQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcYLgQAAAAAAA==/", "_etag": "\"a800a71e-0000-0100-0000-68703ad10000\"", "_attachments": "attachments/", "_ts": 1752185553}, {"payPeriodId": "1090070275769848", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-07-27T00:00:00Z", "endDate": "2025-08-11T00:00:00Z", "submitByDate": "2025-08-13T00:00:00Z", "checkDate": "2025-08-15T00:00:00Z", "checkCount": 0, "id": "2149e65b-9703-408c-8cdd-efb279af891b", "companyId": "18056120", "type": "payperiod", "_rid": "NmJkAKiCbEcZLgQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcZLgQAAAAAAA==/", "_etag": "\"a800a91e-0000-0100-0000-68703ad10000\"", "_attachments": "attachments/", "_ts": 1752185553}, {"payPeriodId": "1090070945065590", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-08-12T00:00:00Z", "endDate": "2025-08-26T00:00:00Z", "submitByDate": "2025-08-27T00:00:00Z", "checkDate": "2025-08-29T00:00:00Z", "checkCount": 0, "id": "8d9941b6-b2be-4aca-bcc8-8ff7f556db8b", "companyId": "18056120", "type": "payperiod", "_rid": "NmJkAKiCbEcaLgQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcaLgQAAAAAAA==/", "_etag": "\"a800ab1e-0000-0100-0000-68703ad10000\"", "_attachments": "attachments/", "_ts": 1752185553}, {"payPeriodId": "1090070945065591", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-08-27T00:00:00Z", "endDate": "2025-09-11T00:00:00Z", "submitByDate": "2025-09-11T00:00:00Z", "checkDate": "2025-09-15T00:00:00Z", "checkCount": 0, "id": "6a8b7fe0-0003-42eb-a07f-99f0fb6b48b7", "companyId": "18056120", "type": "payperiod", "_rid": "NmJkAKiCbEcbLgQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcbLgQAAAAAAA==/", "_etag": "\"a800ae1e-0000-0100-0000-68703ad20000\"", "_attachments": "attachments/", "_ts": 1752185554}, {"payPeriodId": "1090071610925616", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-09-12T00:00:00Z", "endDate": "2025-09-26T00:00:00Z", "submitByDate": "2025-09-26T00:00:00Z", "checkDate": "2025-09-30T00:00:00Z", "checkCount": 0, "id": "4d94b9a7-9e40-47b2-9c28-81794a5f7668", "companyId": "18056120", "type": "payperiod", "_rid": "NmJkAKiCbEccLgQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEccLgQAAAAAAA==/", "_etag": "\"a800af1e-0000-0100-0000-68703ad20000\"", "_attachments": "attachments/", "_ts": 1752185554}, {"payPeriodId": "1090071610925617", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-09-27T00:00:00Z", "endDate": "2025-10-11T00:00:00Z", "submitByDate": "2025-10-13T00:00:00Z", "checkDate": "2025-10-15T00:00:00Z", "checkCount": 0, "id": "e1298ddb-3e8a-43a8-a3e2-0e50a0c7c28a", "companyId": "18056120", "type": "payperiod", "_rid": "NmJkAKiCbEcdLgQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcdLgQAAAAAAA==/", "_etag": "\"a800b11e-0000-0100-0000-68703ad20000\"", "_attachments": "attachments/", "_ts": 1752185554}, {"payPeriodId": "1090065692220320", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2024-12-27T00:00:00Z", "endDate": "2025-01-11T00:00:00Z", "submitByDate": "2025-01-13T00:00:00Z", "checkDate": "2025-01-15T00:00:00Z", "checkCount": 8, "id": "8dce42e7-3dc3-4c94-97b4-19d2459b85b9", "companyId": "18056120", "type": "payperiod", "_rid": "NmJkAKiCbEc6LgQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc6LgQAAAAAAA==/", "_etag": "\"a800f51e-0000-0100-0000-68703ad40000\"", "_attachments": "attachments/", "_ts": 1752185556}, {"payPeriodId": "1090066292171142", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-01-12T00:00:00Z", "endDate": "2025-01-26T00:00:00Z", "submitByDate": "2025-01-29T00:00:00Z", "checkDate": "2025-01-31T00:00:00Z", "checkCount": 13, "id": "6b1e9573-0e29-4419-9e04-d65557976e87", "companyId": "18056120", "type": "payperiod", "_rid": "NmJkAKiCbEc7LgQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc7LgQAAAAAAA==/", "_etag": "\"a800f91e-0000-0100-0000-68703ad40000\"", "_attachments": "attachments/", "_ts": 1752185556}, {"payPeriodId": "1090066292171143", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-01-27T00:00:00Z", "endDate": "2025-02-11T00:00:00Z", "submitByDate": "2025-02-12T00:00:00Z", "checkDate": "2025-02-14T00:00:00Z", "checkCount": 13, "id": "ed8643b4-af76-42cd-960e-4cc8677db162", "companyId": "18056120", "type": "payperiod", "_rid": "NmJkAKiCbEc8LgQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc8LgQAAAAAAA==/", "_etag": "\"a800fc1e-0000-0100-0000-68703ad40000\"", "_attachments": "attachments/", "_ts": 1752185556}, {"payPeriodId": "1090067000220031", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-02-12T00:00:00Z", "endDate": "2025-02-26T00:00:00Z", "submitByDate": "2025-02-26T00:00:00Z", "checkDate": "2025-02-28T00:00:00Z", "checkCount": 8, "id": "64bdd2a2-d2e5-4f38-8caf-f572cc8d59a7", "companyId": "18056120", "type": "payperiod", "_rid": "NmJkAKiCbEc9LgQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc9LgQAAAAAAA==/", "_etag": "\"a800fd1e-0000-0100-0000-68703ad40000\"", "_attachments": "attachments/", "_ts": 1752185556}, {"payPeriodId": "1090067000220032", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-02-27T00:00:00Z", "endDate": "2025-03-11T00:00:00Z", "submitByDate": "2025-03-12T00:00:00Z", "checkDate": "2025-03-14T00:00:00Z", "checkCount": 8, "id": "e336ebe8-5d19-461d-84cd-131dd6b16391", "companyId": "18056120", "type": "payperiod", "_rid": "NmJkAKiCbEc+LgQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc+LgQAAAAAAA==/", "_etag": "\"a800ff1e-0000-0100-0000-68703ad40000\"", "_attachments": "attachments/", "_ts": 1752185556}, {"payPeriodId": "1090067601314963", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-03-12T00:00:00Z", "endDate": "2025-03-26T00:00:00Z", "submitByDate": "2025-03-27T00:00:00Z", "checkDate": "2025-03-31T00:00:00Z", "checkCount": 13, "id": "48255539-8248-40ca-a3a6-3befcf88aa4f", "companyId": "18056120", "type": "payperiod", "_rid": "NmJkAKiCbEc-LgQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc-LgQAAAAAAA==/", "_etag": "\"a800011f-0000-0100-0000-68703ad40000\"", "_attachments": "attachments/", "_ts": 1752185556}, {"payPeriodId": "1090067601314964", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-03-27T00:00:00Z", "endDate": "2025-04-11T00:00:00Z", "submitByDate": "2025-04-11T00:00:00Z", "checkDate": "2025-04-15T00:00:00Z", "checkCount": 12, "id": "fd5cb578-5f43-4f23-96ca-d2ca6b6c265d", "companyId": "18056120", "type": "payperiod", "_rid": "NmJkAKiCbEdALgQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdALgQAAAAAAA==/", "_etag": "\"a800021f-0000-0100-0000-68703ad50000\"", "_attachments": "attachments/", "_ts": 1752185557}, {"payPeriodId": "1090068321009345", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-04-12T00:00:00Z", "endDate": "2025-04-26T00:00:00Z", "submitByDate": "2025-04-28T00:00:00Z", "checkDate": "2025-04-30T00:00:00Z", "checkCount": 8, "id": "67df3cf4-4b64-4121-a06a-6f1c8a487b52", "companyId": "18056120", "type": "payperiod", "_rid": "NmJkAKiCbEdBLgQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdBLgQAAAAAAA==/", "_etag": "\"a800031f-0000-0100-0000-68703ad50000\"", "_attachments": "attachments/", "_ts": 1752185557}, {"payPeriodId": "1090068321009346", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-04-27T00:00:00Z", "endDate": "2025-05-11T00:00:00Z", "submitByDate": "2025-05-13T00:00:00Z", "checkDate": "2025-05-15T00:00:00Z", "checkCount": 8, "id": "24017d4e-99f8-4f4d-ba45-2e86c5b34ae7", "companyId": "18056120", "type": "payperiod", "_rid": "NmJkAKiCbEdCLgQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdCLgQAAAAAAA==/", "_etag": "\"a800061f-0000-0100-0000-68703ad50000\"", "_attachments": "attachments/", "_ts": 1752185557}, {"payPeriodId": "1090068987795714", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-05-12T00:00:00Z", "endDate": "2025-05-26T00:00:00Z", "submitByDate": "2025-05-28T00:00:00Z", "checkDate": "2025-05-30T00:00:00Z", "checkCount": 13, "id": "4df9d2f6-fedd-4e8d-86cd-5814a087454e", "companyId": "18056120", "type": "payperiod", "_rid": "NmJkAKiCbEdDLgQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdDLgQAAAAAAA==/", "_etag": "\"a800091f-0000-0100-0000-68703ad50000\"", "_attachments": "attachments/", "_ts": 1752185557}, {"payPeriodId": "1090068987795715", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-05-27T00:00:00Z", "endDate": "2025-06-11T00:00:00Z", "submitByDate": "2025-06-11T00:00:00Z", "checkDate": "2025-06-13T00:00:00Z", "checkCount": 8, "id": "8b585dcc-ee40-4e76-b88c-4d6fcc835b45", "companyId": "18056120", "type": "payperiod", "_rid": "NmJkAKiCbEdELgQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdELgQAAAAAAA==/", "_etag": "\"a8000a1f-0000-0100-0000-68703ad50000\"", "_attachments": "attachments/", "_ts": 1752185557}, {"payPeriodId": "1090069597159077", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-06-12T00:00:00Z", "endDate": "2025-06-26T00:00:00Z", "submitByDate": "2025-06-26T00:00:00Z", "checkDate": "2025-06-30T00:00:00Z", "checkCount": 13, "id": "b8d077e2-39c9-4dca-96f5-62fc87cd2c42", "companyId": "18056120", "type": "payperiod", "_rid": "NmJkAKiCbEdFLgQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdFLgQAAAAAAA==/", "_etag": "\"a8000f1f-0000-0100-0000-68703ad50000\"", "_attachments": "attachments/", "_ts": 1752185557}, {"payPeriodId": "1090069597159078", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-06-27T00:00:00Z", "endDate": "2025-07-11T00:00:00Z", "submitByDate": "2025-07-11T00:00:00Z", "checkDate": "2025-07-15T00:00:00Z", "checkCount": 0, "id": "95f4ebf6-6b06-44c8-830d-b74d23ab5cda", "companyId": "18056120", "type": "payperiod", "_rid": "NmJkAKiCbEdGLgQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdGLgQAAAAAAA==/", "_etag": "\"a8001e1f-0000-0100-0000-68703ad50000\"", "_attachments": "attachments/", "_ts": 1752185557}, {"payPeriodId": "1090070275769847", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-07-12T00:00:00Z", "endDate": "2025-07-26T00:00:00Z", "submitByDate": "2025-07-29T00:00:00Z", "checkDate": "2025-07-31T00:00:00Z", "checkCount": 0, "id": "5793ca84-041a-4dbf-aff9-a2b8735948f7", "companyId": "18056120", "type": "payperiod", "_rid": "NmJkAKiCbEdHLgQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdHLgQAAAAAAA==/", "_etag": "\"a800221f-0000-0100-0000-68703ad50000\"", "_attachments": "attachments/", "_ts": 1752185557}, {"payPeriodId": "1090070275769848", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-07-27T00:00:00Z", "endDate": "2025-08-11T00:00:00Z", "submitByDate": "2025-08-13T00:00:00Z", "checkDate": "2025-08-15T00:00:00Z", "checkCount": 0, "id": "3d6d229e-d95c-403f-a17f-4c4ea586ad67", "companyId": "18056120", "type": "payperiod", "_rid": "NmJkAKiCbEdILgQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdILgQAAAAAAA==/", "_etag": "\"a800241f-0000-0100-0000-68703ad50000\"", "_attachments": "attachments/", "_ts": 1752185557}, {"payPeriodId": "1090070945065590", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-08-12T00:00:00Z", "endDate": "2025-08-26T00:00:00Z", "submitByDate": "2025-08-27T00:00:00Z", "checkDate": "2025-08-29T00:00:00Z", "checkCount": 0, "id": "5759e159-4f24-4307-80a7-ff8aaf69b351", "companyId": "18056120", "type": "payperiod", "_rid": "NmJkAKiCbEdJLgQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdJLgQAAAAAAA==/", "_etag": "\"a800261f-0000-0100-0000-68703ad50000\"", "_attachments": "attachments/", "_ts": 1752185557}, {"payPeriodId": "1090070945065591", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-08-27T00:00:00Z", "endDate": "2025-09-11T00:00:00Z", "submitByDate": "2025-09-11T00:00:00Z", "checkDate": "2025-09-15T00:00:00Z", "checkCount": 0, "id": "3ac37569-c8ea-4600-b15d-dda8d551a5e3", "companyId": "18056120", "type": "payperiod", "_rid": "NmJkAKiCbEdKLgQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdKLgQAAAAAAA==/", "_etag": "\"a8002a1f-0000-0100-0000-68703ad50000\"", "_attachments": "attachments/", "_ts": 1752185557}, {"payPeriodId": "1090071610925616", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-09-12T00:00:00Z", "endDate": "2025-09-26T00:00:00Z", "submitByDate": "2025-09-26T00:00:00Z", "checkDate": "2025-09-30T00:00:00Z", "checkCount": 0, "id": "84074f4a-829a-4100-b35b-1f2ed3e452bf", "companyId": "18056120", "type": "payperiod", "_rid": "NmJkAKiCbEdLLgQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdLLgQAAAAAAA==/", "_etag": "\"a800301f-0000-0100-0000-68703ad50000\"", "_attachments": "attachments/", "_ts": 1752185557}, {"payPeriodId": "1090071610925617", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-09-27T00:00:00Z", "endDate": "2025-10-11T00:00:00Z", "submitByDate": "2025-10-13T00:00:00Z", "checkDate": "2025-10-15T00:00:00Z", "checkCount": 0, "id": "95705fe5-68ee-4af9-b08b-eeae0c85cacf", "companyId": "18056120", "type": "payperiod", "_rid": "NmJkAKiCbEdMLgQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdMLgQAAAAAAA==/", "_etag": "\"a800321f-0000-0100-0000-68703ad50000\"", "_attachments": "attachments/", "_ts": 1752185557}], "links": [{"rel": "self", "href": "https://ca-payroll-ai-mock-sb-001-secure.nicebeach-8a7a52ab.eastus.azurecontainerapps.io/ca/companies/18056120/payperiods"}]}, "status_code": 200}