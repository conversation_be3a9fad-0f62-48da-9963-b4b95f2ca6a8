{"success": true, "company_id": "00176167", "data": {"metadata": {"contentItemCount": 40}, "content": [{"payPeriodId": "1080039085335370", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-01-01T00:00:00Z", "endDate": "2025-01-15T00:00:00Z", "submitByDate": "2025-01-13T00:00:00Z", "checkDate": "2025-01-15T00:00:00Z", "checkCount": 2, "id": "9d2ff1f1-a05b-46c2-9265-8212aafcc6a6", "companyId": "00176167", "type": "payperiod", "_rid": "NmJkAKiCbEe1iQIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEe1iQIAAAAAAA==/", "_etag": "\"a3003517-0000-0100-0000-687018c80000\"", "_attachments": "attachments/", "_ts": 1752176840}, {"payPeriodId": "1080039085335371", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-01-16T00:00:00Z", "endDate": "2025-01-31T00:00:00Z", "submitByDate": "2025-01-29T00:00:00Z", "checkDate": "2025-01-31T00:00:00Z", "checkCount": 2, "id": "3a3d9855-543c-41aa-9644-e28f37487670", "companyId": "00176167", "type": "payperiod", "_rid": "NmJkAKiCbEe2iQIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEe2iQIAAAAAAA==/", "_etag": "\"a3003917-0000-0100-0000-687018c80000\"", "_attachments": "attachments/", "_ts": 1752176840}, {"payPeriodId": "1080039280919324", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-02-01T00:00:00Z", "endDate": "2025-02-15T00:00:00Z", "submitByDate": "2025-02-12T00:00:00Z", "checkDate": "2025-02-14T00:00:00Z", "checkCount": 1, "id": "76bace22-a15c-47fe-a976-d794a04d8794", "companyId": "00176167", "type": "payperiod", "_rid": "NmJkAKiCbEe3iQIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEe3iQIAAAAAAA==/", "_etag": "\"a3003c17-0000-0100-0000-687018c80000\"", "_attachments": "attachments/", "_ts": 1752176840}, {"payPeriodId": "1080039280919325", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-02-16T00:00:00Z", "endDate": "2025-02-28T00:00:00Z", "submitByDate": "2025-02-26T00:00:00Z", "checkDate": "2025-02-28T00:00:00Z", "checkCount": 1, "id": "75c5442b-2580-4260-adf1-a0b0768c1c04", "companyId": "00176167", "type": "payperiod", "_rid": "NmJkAKiCbEe4iQIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEe4iQIAAAAAAA==/", "_etag": "\"a3003f17-0000-0100-0000-687018c80000\"", "_attachments": "attachments/", "_ts": 1752176840}, {"payPeriodId": "1080039510103853", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-03-01T00:00:00Z", "endDate": "2025-03-15T00:00:00Z", "submitByDate": "2025-03-12T00:00:00Z", "checkDate": "2025-03-14T00:00:00Z", "checkCount": 1, "id": "c62cb000-fb95-487a-9bee-4d712604e527", "companyId": "00176167", "type": "payperiod", "_rid": "NmJkAKiCbEe5iQIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEe5iQIAAAAAAA==/", "_etag": "\"a3004217-0000-0100-0000-687018c80000\"", "_attachments": "attachments/", "_ts": 1752176840}, {"payPeriodId": "1080039510103854", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-03-16T00:00:00Z", "endDate": "2025-03-31T00:00:00Z", "submitByDate": "2025-03-27T00:00:00Z", "checkDate": "2025-03-31T00:00:00Z", "checkCount": 2, "id": "57dc70ee-aeee-40e8-8c1e-42e9b873857b", "companyId": "00176167", "type": "payperiod", "_rid": "NmJkAKiCbEe6iQIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEe6iQIAAAAAAA==/", "_etag": "\"a3004b17-0000-0100-0000-687018c80000\"", "_attachments": "attachments/", "_ts": 1752176840}, {"payPeriodId": "1080039711411022", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-04-01T00:00:00Z", "endDate": "2025-04-15T00:00:00Z", "submitByDate": "2025-04-15T00:00:00Z", "checkDate": "2025-04-16T00:00:00Z", "checkCount": 0, "id": "c3f4154e-3095-47dd-a2a6-5651dfcbe0e9", "companyId": "00176167", "type": "payperiod", "_rid": "NmJkAKiCbEe7iQIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEe7iQIAAAAAAA==/", "_etag": "\"a3004d17-0000-0100-0000-687018c80000\"", "_attachments": "attachments/", "_ts": 1752176840}, {"payPeriodId": "1080039711411023", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-04-16T00:00:00Z", "endDate": "2025-04-30T00:00:00Z", "submitByDate": "2025-04-28T00:00:00Z", "checkDate": "2025-04-30T00:00:00Z", "checkCount": 0, "id": "f74dcb62-2f62-4cb5-b168-eafc4262d0fb", "companyId": "00176167", "type": "payperiod", "_rid": "NmJkAKiCbEe8iQIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEe8iQIAAAAAAA==/", "_etag": "\"a3005017-0000-0100-0000-687018c80000\"", "_attachments": "attachments/", "_ts": 1752176840}, {"payPeriodId": "1080039899133336", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-05-01T00:00:00Z", "endDate": "2025-05-15T00:00:00Z", "submitByDate": "2025-05-13T00:00:00Z", "checkDate": "2025-05-15T00:00:00Z", "checkCount": 0, "id": "c09b7fcf-8aee-4d2b-92bc-1c08da75c70e", "companyId": "00176167", "type": "payperiod", "_rid": "NmJkAKiCbEe9iQIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEe9iQIAAAAAAA==/", "_etag": "\"a3005317-0000-0100-0000-687018c80000\"", "_attachments": "attachments/", "_ts": 1752176840}, {"payPeriodId": "1080039899133337", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-05-16T00:00:00Z", "endDate": "2025-05-31T00:00:00Z", "submitByDate": "2025-05-28T00:00:00Z", "checkDate": "2025-05-30T00:00:00Z", "checkCount": 0, "id": "3ceeb570-aa87-4c68-b998-000567ea585d", "companyId": "00176167", "type": "payperiod", "_rid": "NmJkAKiCbEe+iQIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEe+iQIAAAAAAA==/", "_etag": "\"a3005617-0000-0100-0000-687018c80000\"", "_attachments": "attachments/", "_ts": 1752176840}, {"payPeriodId": "1080040128181657", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-06-01T00:00:00Z", "endDate": "2025-06-15T00:00:00Z", "submitByDate": "2025-06-11T00:00:00Z", "checkDate": "2025-06-13T00:00:00Z", "checkCount": 0, "id": "fc6dcbed-b5b8-4593-8421-2f318b54fbcb", "companyId": "00176167", "type": "payperiod", "_rid": "NmJkAKiCbEe-iQIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEe-iQIAAAAAAA==/", "_etag": "\"a3005b17-0000-0100-0000-687018c80000\"", "_attachments": "attachments/", "_ts": 1752176840}, {"payPeriodId": "1080040128181658", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-06-16T00:00:00Z", "endDate": "2025-06-30T00:00:00Z", "submitByDate": "2025-06-26T00:00:00Z", "checkDate": "2025-06-30T00:00:00Z", "checkCount": 0, "id": "37aadaa6-1f57-44d1-bce3-ce37f11a1d5f", "companyId": "00176167", "type": "payperiod", "_rid": "NmJkAKiCbEfAiQIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfAiQIAAAAAAA==/", "_etag": "\"a3005d17-0000-0100-0000-687018c90000\"", "_attachments": "attachments/", "_ts": 1752176841}, {"payPeriodId": "1080040325138379", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-07-01T00:00:00Z", "endDate": "2025-07-15T00:00:00Z", "submitByDate": "2025-07-11T00:00:00Z", "checkDate": "2025-07-15T00:00:00Z", "checkCount": 0, "id": "218d19ac-4a6c-41c5-98d0-c989c17c3fd4", "companyId": "00176167", "type": "payperiod", "_rid": "NmJkAKiCbEfBiQIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfBiQIAAAAAAA==/", "_etag": "\"a3005e17-0000-0100-0000-687018c90000\"", "_attachments": "attachments/", "_ts": 1752176841}, {"payPeriodId": "1080040325138380", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-07-16T00:00:00Z", "endDate": "2025-07-31T00:00:00Z", "submitByDate": "2025-07-29T00:00:00Z", "checkDate": "2025-07-31T00:00:00Z", "checkCount": 0, "id": "d71a84ec-d0e4-4535-8979-6854f042ad9b", "companyId": "00176167", "type": "payperiod", "_rid": "NmJkAKiCbEfCiQIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfCiQIAAAAAAA==/", "_etag": "\"a3006217-0000-0100-0000-687018c90000\"", "_attachments": "attachments/", "_ts": 1752176841}, {"payPeriodId": "1080040539035017", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-08-01T00:00:00Z", "endDate": "2025-08-15T00:00:00Z", "submitByDate": "2025-08-13T00:00:00Z", "checkDate": "2025-08-15T00:00:00Z", "checkCount": 0, "id": "4762ebcc-e8d3-4626-9a4c-7c9981d993c5", "companyId": "00176167", "type": "payperiod", "_rid": "NmJkAKiCbEfDiQIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfDiQIAAAAAAA==/", "_etag": "\"a3006b17-0000-0100-0000-687018c90000\"", "_attachments": "attachments/", "_ts": 1752176841}, {"payPeriodId": "1080040539035018", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-08-16T00:00:00Z", "endDate": "2025-08-31T00:00:00Z", "submitByDate": "2025-08-27T00:00:00Z", "checkDate": "2025-08-29T00:00:00Z", "checkCount": 0, "id": "4948ab80-7d06-4348-875b-eb161430f025", "companyId": "00176167", "type": "payperiod", "_rid": "NmJkAKiCbEfEiQIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfEiQIAAAAAAA==/", "_etag": "\"a3006d17-0000-0100-0000-687018c90000\"", "_attachments": "attachments/", "_ts": 1752176841}, {"payPeriodId": "1080040773180796", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-09-01T00:00:00Z", "endDate": "2025-09-15T00:00:00Z", "submitByDate": "2025-09-11T00:00:00Z", "checkDate": "2025-09-15T00:00:00Z", "checkCount": 0, "id": "f2404ed1-74fa-401c-98f1-ed7721b1cc5c", "companyId": "00176167", "type": "payperiod", "_rid": "NmJkAKiCbEfFiQIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfFiQIAAAAAAA==/", "_etag": "\"a3007817-0000-0100-0000-687018c90000\"", "_attachments": "attachments/", "_ts": 1752176841}, {"payPeriodId": "1080040773180797", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-09-16T00:00:00Z", "endDate": "2025-09-30T00:00:00Z", "submitByDate": "2025-09-26T00:00:00Z", "checkDate": "2025-09-30T00:00:00Z", "checkCount": 0, "id": "bd6dbf72-46f1-443f-843e-85da0c41dd45", "companyId": "00176167", "type": "payperiod", "_rid": "NmJkAKiCbEfGiQIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfGiQIAAAAAAA==/", "_etag": "\"a3007e17-0000-0100-0000-687018c90000\"", "_attachments": "attachments/", "_ts": 1752176841}, {"payPeriodId": "1080040961923962", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-10-01T00:00:00Z", "endDate": "2025-10-15T00:00:00Z", "submitByDate": "2025-10-13T00:00:00Z", "checkDate": "2025-10-15T00:00:00Z", "checkCount": 0, "id": "31ef6c62-a59b-45d9-84cc-ae4d30870cac", "companyId": "00176167", "type": "payperiod", "_rid": "NmJkAKiCbEfHiQIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfHiQIAAAAAAA==/", "_etag": "\"a3008217-0000-0100-0000-687018c90000\"", "_attachments": "attachments/", "_ts": 1752176841}, {"payPeriodId": "1080040961923963", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-10-16T00:00:00Z", "endDate": "2025-10-31T00:00:00Z", "submitByDate": "2025-10-29T00:00:00Z", "checkDate": "2025-10-31T00:00:00Z", "checkCount": 0, "id": "28fda8eb-1e57-4d9f-bb7f-3e4ade708393", "companyId": "00176167", "type": "payperiod", "_rid": "NmJkAKiCbEfIiQIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfIiQIAAAAAAA==/", "_etag": "\"a3008317-0000-0100-0000-687018c90000\"", "_attachments": "attachments/", "_ts": 1752176841}, {"payPeriodId": "1080039085335370", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-01-01T00:00:00Z", "endDate": "2025-01-15T00:00:00Z", "submitByDate": "2025-01-13T00:00:00Z", "checkDate": "2025-01-15T00:00:00Z", "checkCount": 2, "id": "c8fa5ac7-deaa-4a26-88cb-b0bee9c604ec", "companyId": "00176167", "type": "payperiod", "_rid": "NmJkAKiCbEfOiQIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfOiQIAAAAAAA==/", "_etag": "\"a3009e17-0000-0100-0000-687018ca0000\"", "_attachments": "attachments/", "_ts": 1752176842}, {"payPeriodId": "1080039085335371", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-01-16T00:00:00Z", "endDate": "2025-01-31T00:00:00Z", "submitByDate": "2025-01-29T00:00:00Z", "checkDate": "2025-01-31T00:00:00Z", "checkCount": 2, "id": "63225b65-1438-40b1-b521-8d05aa638f13", "companyId": "00176167", "type": "payperiod", "_rid": "NmJkAKiCbEfPiQIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfPiQIAAAAAAA==/", "_etag": "\"a300a217-0000-0100-0000-687018ca0000\"", "_attachments": "attachments/", "_ts": 1752176842}, {"payPeriodId": "1080039280919324", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-02-01T00:00:00Z", "endDate": "2025-02-15T00:00:00Z", "submitByDate": "2025-02-12T00:00:00Z", "checkDate": "2025-02-14T00:00:00Z", "checkCount": 1, "id": "da5e06e6-b69d-47f9-b4ff-18e600125b77", "companyId": "00176167", "type": "payperiod", "_rid": "NmJkAKiCbEfQiQIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfQiQIAAAAAAA==/", "_etag": "\"a300a417-0000-0100-0000-687018ca0000\"", "_attachments": "attachments/", "_ts": 1752176842}, {"payPeriodId": "1080039280919325", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-02-16T00:00:00Z", "endDate": "2025-02-28T00:00:00Z", "submitByDate": "2025-02-26T00:00:00Z", "checkDate": "2025-02-28T00:00:00Z", "checkCount": 1, "id": "97045f17-8859-4c67-a48b-517e14ec120c", "companyId": "00176167", "type": "payperiod", "_rid": "NmJkAKiCbEfRiQIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfRiQIAAAAAAA==/", "_etag": "\"a300ab17-0000-0100-0000-687018ca0000\"", "_attachments": "attachments/", "_ts": 1752176842}, {"payPeriodId": "1080039510103853", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-03-01T00:00:00Z", "endDate": "2025-03-15T00:00:00Z", "submitByDate": "2025-03-12T00:00:00Z", "checkDate": "2025-03-14T00:00:00Z", "checkCount": 1, "id": "b8643502-f274-425a-bf1a-8f0d94e9d6a7", "companyId": "00176167", "type": "payperiod", "_rid": "NmJkAKiCbEfSiQIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfSiQIAAAAAAA==/", "_etag": "\"a300ae17-0000-0100-0000-687018ca0000\"", "_attachments": "attachments/", "_ts": 1752176842}, {"payPeriodId": "1080039510103854", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-03-16T00:00:00Z", "endDate": "2025-03-31T00:00:00Z", "submitByDate": "2025-03-27T00:00:00Z", "checkDate": "2025-03-31T00:00:00Z", "checkCount": 2, "id": "55a68837-7c05-41ff-a049-fb006ff16d23", "companyId": "00176167", "type": "payperiod", "_rid": "NmJkAKiCbEfTiQIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfTiQIAAAAAAA==/", "_etag": "\"a300be17-0000-0100-0000-687018ca0000\"", "_attachments": "attachments/", "_ts": 1752176842}, {"payPeriodId": "1080039711411022", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-04-01T00:00:00Z", "endDate": "2025-04-15T00:00:00Z", "submitByDate": "2025-04-15T00:00:00Z", "checkDate": "2025-04-16T00:00:00Z", "checkCount": 1, "id": "101aa30e-523d-4494-9aa7-1ff0e72e699e", "companyId": "00176167", "type": "payperiod", "_rid": "NmJkAKiCbEfUiQIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfUiQIAAAAAAA==/", "_etag": "\"a300c417-0000-0100-0000-687018ca0000\"", "_attachments": "attachments/", "_ts": 1752176842}, {"payPeriodId": "1080039711411023", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-04-16T00:00:00Z", "endDate": "2025-04-30T00:00:00Z", "submitByDate": "2025-04-28T00:00:00Z", "checkDate": "2025-04-30T00:00:00Z", "checkCount": 2, "id": "cd87dbf1-d432-4300-a8e7-dd4bf122e66a", "companyId": "00176167", "type": "payperiod", "_rid": "NmJkAKiCbEfViQIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfViQIAAAAAAA==/", "_etag": "\"a300cb17-0000-0100-0000-687018ca0000\"", "_attachments": "attachments/", "_ts": 1752176842}, {"payPeriodId": "1080039899133336", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-05-01T00:00:00Z", "endDate": "2025-05-15T00:00:00Z", "submitByDate": "2025-05-13T00:00:00Z", "checkDate": "2025-05-15T00:00:00Z", "checkCount": 1, "id": "b3adcf0a-19b9-42dc-87cb-a9849ee1784f", "companyId": "00176167", "type": "payperiod", "_rid": "NmJkAKiCbEfWiQIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfWiQIAAAAAAA==/", "_etag": "\"a300ce17-0000-0100-0000-687018ca0000\"", "_attachments": "attachments/", "_ts": 1752176842}, {"payPeriodId": "1080039899133337", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-05-16T00:00:00Z", "endDate": "2025-05-31T00:00:00Z", "submitByDate": "2025-05-28T00:00:00Z", "checkDate": "2025-05-30T00:00:00Z", "checkCount": 2, "id": "d9d13631-c614-4a4e-9ef2-aa414e9c2469", "companyId": "00176167", "type": "payperiod", "_rid": "NmJkAKiCbEfXiQIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfXiQIAAAAAAA==/", "_etag": "\"a300cf17-0000-0100-0000-687018ca0000\"", "_attachments": "attachments/", "_ts": 1752176842}, {"payPeriodId": "1080040128181657", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-06-01T00:00:00Z", "endDate": "2025-06-15T00:00:00Z", "submitByDate": "2025-06-11T00:00:00Z", "checkDate": "2025-06-13T00:00:00Z", "checkCount": 2, "id": "08e0d37e-f1ac-4b61-a668-6fa02e912d11", "companyId": "00176167", "type": "payperiod", "_rid": "NmJkAKiCbEfYiQIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfYiQIAAAAAAA==/", "_etag": "\"a300d117-0000-0100-0000-687018ca0000\"", "_attachments": "attachments/", "_ts": 1752176842}, {"payPeriodId": "1080040128181658", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-06-16T00:00:00Z", "endDate": "2025-06-30T00:00:00Z", "submitByDate": "2025-06-26T00:00:00Z", "checkDate": "2025-06-30T00:00:00Z", "checkCount": 2, "id": "2d141078-9750-40d6-8807-a1a08f090ad7", "companyId": "00176167", "type": "payperiod", "_rid": "NmJkAKiCbEfZiQIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfZiQIAAAAAAA==/", "_etag": "\"a300d517-0000-0100-0000-687018cb0000\"", "_attachments": "attachments/", "_ts": 1752176843}, {"payPeriodId": "1080040325138379", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-07-01T00:00:00Z", "endDate": "2025-07-15T00:00:00Z", "submitByDate": "2025-07-11T00:00:00Z", "checkDate": "2025-07-15T00:00:00Z", "checkCount": 0, "id": "7f17a334-6f91-42a8-9506-07c99cabb0ce", "companyId": "00176167", "type": "payperiod", "_rid": "NmJkAKiCbEfaiQIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfaiQIAAAAAAA==/", "_etag": "\"a300d717-0000-0100-0000-687018cb0000\"", "_attachments": "attachments/", "_ts": 1752176843}, {"payPeriodId": "1080040325138380", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-07-16T00:00:00Z", "endDate": "2025-07-31T00:00:00Z", "submitByDate": "2025-07-29T00:00:00Z", "checkDate": "2025-07-31T00:00:00Z", "checkCount": 0, "id": "6b9221ad-0bdc-4d4b-b553-f82fedf71d7b", "companyId": "00176167", "type": "payperiod", "_rid": "NmJkAKiCbEfbiQIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfbiQIAAAAAAA==/", "_etag": "\"a300da17-0000-0100-0000-687018cb0000\"", "_attachments": "attachments/", "_ts": 1752176843}, {"payPeriodId": "1080040539035017", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-08-01T00:00:00Z", "endDate": "2025-08-15T00:00:00Z", "submitByDate": "2025-08-13T00:00:00Z", "checkDate": "2025-08-15T00:00:00Z", "checkCount": 0, "id": "a8bc0f4d-1eb7-4dbd-9634-1d7943b43722", "companyId": "00176167", "type": "payperiod", "_rid": "NmJkAKiCbEfciQIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfciQIAAAAAAA==/", "_etag": "\"a300dd17-0000-0100-0000-687018cb0000\"", "_attachments": "attachments/", "_ts": 1752176843}, {"payPeriodId": "1080040539035018", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-08-16T00:00:00Z", "endDate": "2025-08-31T00:00:00Z", "submitByDate": "2025-08-27T00:00:00Z", "checkDate": "2025-08-29T00:00:00Z", "checkCount": 0, "id": "6cf698dd-71a0-4f0f-888f-a88f301b29b4", "companyId": "00176167", "type": "payperiod", "_rid": "NmJkAKiCbEfdiQIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfdiQIAAAAAAA==/", "_etag": "\"a300e217-0000-0100-0000-687018cb0000\"", "_attachments": "attachments/", "_ts": 1752176843}, {"payPeriodId": "1080040773180796", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-09-01T00:00:00Z", "endDate": "2025-09-15T00:00:00Z", "submitByDate": "2025-09-11T00:00:00Z", "checkDate": "2025-09-15T00:00:00Z", "checkCount": 0, "id": "8db04c12-73fb-4ce8-bf47-b224986362fe", "companyId": "00176167", "type": "payperiod", "_rid": "NmJkAKiCbEfeiQIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfeiQIAAAAAAA==/", "_etag": "\"a300e417-0000-0100-0000-687018cb0000\"", "_attachments": "attachments/", "_ts": 1752176843}, {"payPeriodId": "1080040773180797", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-09-16T00:00:00Z", "endDate": "2025-09-30T00:00:00Z", "submitByDate": "2025-09-26T00:00:00Z", "checkDate": "2025-09-30T00:00:00Z", "checkCount": 0, "id": "2142a4df-dddb-4a92-9647-df7a0096e865", "companyId": "00176167", "type": "payperiod", "_rid": "NmJkAKiCbEffiQIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEffiQIAAAAAAA==/", "_etag": "\"a300e617-0000-0100-0000-687018cb0000\"", "_attachments": "attachments/", "_ts": 1752176843}, {"payPeriodId": "1080040961923962", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-10-01T00:00:00Z", "endDate": "2025-10-15T00:00:00Z", "submitByDate": "2025-10-13T00:00:00Z", "checkDate": "2025-10-15T00:00:00Z", "checkCount": 0, "id": "825a563a-d9d8-4fff-a2c0-c164f2ce4f63", "companyId": "00176167", "type": "payperiod", "_rid": "NmJkAKiCbEfgiQIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfgiQIAAAAAAA==/", "_etag": "\"a300e917-0000-0100-0000-687018cb0000\"", "_attachments": "attachments/", "_ts": 1752176843}, {"payPeriodId": "1080040961923963", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-10-16T00:00:00Z", "endDate": "2025-10-31T00:00:00Z", "submitByDate": "2025-10-29T00:00:00Z", "checkDate": "2025-10-31T00:00:00Z", "checkCount": 0, "id": "d4272c14-bfd0-42fe-95a0-021ac253f54c", "companyId": "00176167", "type": "payperiod", "_rid": "NmJkAKiCbEfhiQIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfhiQIAAAAAAA==/", "_etag": "\"a300ed17-0000-0100-0000-687018cb0000\"", "_attachments": "attachments/", "_ts": 1752176843}], "links": [{"rel": "self", "href": "https://ca-payroll-ai-mock-sb-001-secure.nicebeach-8a7a52ab.eastus.azurecontainerapps.io/ca/companies/00176167/payperiods"}]}, "status_code": 200}