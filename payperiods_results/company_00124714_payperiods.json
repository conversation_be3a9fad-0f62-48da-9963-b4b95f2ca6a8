{"success": true, "company_id": "00124714", "data": {"metadata": {"contentItemCount": 180}, "content": [{"payPeriodId": "1020050118164640", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2024-12-26T00:00:00Z", "endDate": "2025-01-01T00:00:00Z", "submitByDate": "2024-12-31T00:00:00Z", "checkDate": "2025-01-03T00:00:00Z", "checkCount": 26, "id": "2a624df4-e464-4431-bb39-b1bf8cedece4", "companyId": "00124714", "type": "payperiod", "_rid": "NmJkAKiCbEfcVgAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfcVgAAAAAAAA==/", "_etag": "\"9800fa2e-0000-0100-0000-686fd5b50000\"", "_attachments": "attachments/", "_ts": 1752159669}, {"payPeriodId": "1020050179417984", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-01-02T00:00:00Z", "endDate": "2025-01-08T00:00:00Z", "submitByDate": "2025-01-08T00:00:00Z", "checkDate": "2025-01-10T00:00:00Z", "checkCount": 28, "id": "07dafbe2-2ab5-471b-a872-bbbaf52f74a9", "companyId": "00124714", "type": "payperiod", "_rid": "NmJkAKiCbEfdVgAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfdVgAAAAAAAA==/", "_etag": "\"9800012f-0000-0100-0000-686fd5b50000\"", "_attachments": "attachments/", "_ts": 1752159669}, {"payPeriodId": "1020050239948857", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-01-09T00:00:00Z", "endDate": "2025-01-15T00:00:00Z", "submitByDate": "2025-01-15T00:00:00Z", "checkDate": "2025-01-17T00:00:00Z", "checkCount": 28, "id": "8848eb19-e895-483f-a829-262abba70a21", "companyId": "00124714", "type": "payperiod", "_rid": "NmJkAKiCbEfeVgAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfeVgAAAAAAAA==/", "_etag": "\"9800032f-0000-0100-0000-686fd5b50000\"", "_attachments": "attachments/", "_ts": 1752159669}, {"payPeriodId": "1020050300187219", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-01-16T00:00:00Z", "endDate": "2025-01-22T00:00:00Z", "submitByDate": "2025-01-22T00:00:00Z", "checkDate": "2025-01-24T00:00:00Z", "checkCount": 28, "id": "e8bd0d2e-3daf-4dd8-aa98-b95a2b06b51e", "companyId": "00124714", "type": "payperiod", "_rid": "NmJkAKiCbEffVgAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEffVgAAAAAAAA==/", "_etag": "\"9800072f-0000-0100-0000-686fd5b50000\"", "_attachments": "attachments/", "_ts": 1752159669}, {"payPeriodId": "1020050368003729", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-01-23T00:00:00Z", "endDate": "2025-01-29T00:00:00Z", "submitByDate": "2025-01-29T00:00:00Z", "checkDate": "2025-01-31T00:00:00Z", "checkCount": 28, "id": "8d9d9d1a-ea0c-4097-914d-cb14aa2a1cca", "companyId": "00124714", "type": "payperiod", "_rid": "NmJkAKiCbEfgVgAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfgVgAAAAAAAA==/", "_etag": "\"98000b2f-0000-0100-0000-686fd5b50000\"", "_attachments": "attachments/", "_ts": 1752159669}, {"payPeriodId": "1020051223764195", "status": "COMPLETED", "description": "errors", "startDate": "2025-01-23T00:00:00Z", "endDate": "2025-01-29T00:00:00Z", "submitByDate": "2025-01-30T00:00:00Z", "checkDate": "2025-01-31T00:00:00Z", "checkCount": 1, "id": "04caa276-6e49-479e-88f9-fb5f7a8eb008", "companyId": "00124714", "type": "payperiod", "_rid": "NmJkAKiCbEfhVgAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfhVgAAAAAAAA==/", "_etag": "\"9800132f-0000-0100-0000-686fd5b50000\"", "_attachments": "attachments/", "_ts": 1752159669}, {"payPeriodId": "1020051223846182", "status": "COMPLETED", "description": "correction", "startDate": "2025-01-23T00:00:00Z", "endDate": "2025-01-29T00:00:00Z", "submitByDate": "2025-01-30T00:00:00Z", "checkDate": "2025-01-31T00:00:00Z", "checkCount": 1, "id": "5dbf32ed-31ef-4781-8001-8dfb8d0ae772", "companyId": "00124714", "type": "payperiod", "_rid": "NmJkAKiCbEfiVgAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfiVgAAAAAAAA==/", "_etag": "\"98001a2f-0000-0100-0000-686fd5b50000\"", "_attachments": "attachments/", "_ts": 1752159669}, {"payPeriodId": "1020050433244788", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-01-30T00:00:00Z", "endDate": "2025-02-05T00:00:00Z", "submitByDate": "2025-02-05T00:00:00Z", "checkDate": "2025-02-07T00:00:00Z", "checkCount": 28, "id": "83f4d91e-e499-445c-b9bc-3075cdcd7110", "companyId": "00124714", "type": "payperiod", "_rid": "NmJkAKiCbEfjVgAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfjVgAAAAAAAA==/", "_etag": "\"98001c2f-0000-0100-0000-686fd5b50000\"", "_attachments": "attachments/", "_ts": 1752159669}, {"payPeriodId": "1020051340017568", "status": "COMPLETED", "description": "Reverse", "startDate": "2025-02-06T00:00:00Z", "endDate": "2025-02-12T00:00:00Z", "submitByDate": "2025-02-13T00:00:00Z", "checkDate": "2025-02-14T00:00:00Z", "checkCount": 18, "originalPayPeriodID": "1020050493312591", "id": "aecaa363-4540-4e1a-bb52-ea63486955df", "companyId": "00124714", "type": "payperiod", "_rid": "NmJkAKiCbEfkVgAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfkVgAAAAAAAA==/", "_etag": "\"98001e2f-0000-0100-0000-686fd5b50000\"", "_attachments": "attachments/", "_ts": 1752159669}, {"payPeriodId": "1020051340017962", "status": "COMPLETED", "description": "Payroll", "startDate": "2025-02-06T00:00:00Z", "endDate": "2025-02-12T00:00:00Z", "submitByDate": "2025-02-13T00:00:00Z", "checkDate": "2025-02-14T00:00:00Z", "checkCount": 28, "id": "b5123fc5-b8c4-4d75-807b-8600fb37d052", "companyId": "00124714", "type": "payperiod", "_rid": "NmJkAKiCbEflVgAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEflVgAAAAAAAA==/", "_etag": "\"9800232f-0000-0100-0000-686fd5b50000\"", "_attachments": "attachments/", "_ts": 1752159669}, {"payPeriodId": "1020050553568829", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-02-13T00:00:00Z", "endDate": "2025-02-19T00:00:00Z", "submitByDate": "2025-02-19T00:00:00Z", "checkDate": "2025-02-21T00:00:00Z", "checkCount": 28, "id": "123c5f6a-3ee7-4a70-89f3-0b6617423090", "companyId": "00124714", "type": "payperiod", "_rid": "NmJkAKiCbEfmVgAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfmVgAAAAAAAA==/", "_etag": "\"9800252f-0000-0100-0000-686fd5b60000\"", "_attachments": "attachments/", "_ts": 1752159670}, {"payPeriodId": "1020050611310415", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-02-20T00:00:00Z", "endDate": "2025-02-26T00:00:00Z", "submitByDate": "2025-02-26T00:00:00Z", "checkDate": "2025-02-28T00:00:00Z", "checkCount": 28, "id": "6d531d4b-3c10-4ea7-b127-526f03fb70cd", "companyId": "00124714", "type": "payperiod", "_rid": "NmJkAKiCbEfnVgAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfnVgAAAAAAAA==/", "_etag": "\"9800272f-0000-0100-0000-686fd5b60000\"", "_attachments": "attachments/", "_ts": 1752159670}, {"payPeriodId": "1020050672307040", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-02-27T00:00:00Z", "endDate": "2025-03-05T00:00:00Z", "submitByDate": "2025-03-05T00:00:00Z", "checkDate": "2025-03-07T00:00:00Z", "checkCount": 28, "id": "2472ec03-0a77-4419-ac87-7ef37815662b", "companyId": "00124714", "type": "payperiod", "_rid": "NmJkAKiCbEfoVgAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfoVgAAAAAAAA==/", "_etag": "\"98002b2f-0000-0100-0000-686fd5b60000\"", "_attachments": "attachments/", "_ts": 1752159670}, {"payPeriodId": "1020050737626005", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-03-06T00:00:00Z", "endDate": "2025-03-12T00:00:00Z", "submitByDate": "2025-03-12T00:00:00Z", "checkDate": "2025-03-14T00:00:00Z", "checkCount": 28, "id": "2d14ef3a-9d20-41a7-8bc7-6edf6340412d", "companyId": "00124714", "type": "payperiod", "_rid": "NmJkAKiCbEfpVgAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfpVgAAAAAAAA==/", "_etag": "\"9800332f-0000-0100-0000-686fd5b60000\"", "_attachments": "attachments/", "_ts": 1752159670}, {"payPeriodId": "1020050809511318", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-03-13T00:00:00Z", "endDate": "2025-03-19T00:00:00Z", "submitByDate": "2025-03-19T00:00:00Z", "checkDate": "2025-03-21T00:00:00Z", "checkCount": 28, "id": "96c9a70d-1178-44d5-8301-f4f9a29d9256", "companyId": "00124714", "type": "payperiod", "_rid": "NmJkAKiCbEfqVgAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfqVgAAAAAAAA==/", "_etag": "\"98003a2f-0000-0100-0000-686fd5b60000\"", "_attachments": "attachments/", "_ts": 1752159670}, {"payPeriodId": "1020050876081578", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-03-20T00:00:00Z", "endDate": "2025-03-26T00:00:00Z", "submitByDate": "2025-03-26T00:00:00Z", "checkDate": "2025-03-28T00:00:00Z", "checkCount": 29, "id": "bdbdeb4f-31b2-4c8a-8a1c-cbe8020bbab5", "companyId": "00124714", "type": "payperiod", "_rid": "NmJkAKiCbEfrVgAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfrVgAAAAAAAA==/", "_etag": "\"98003e2f-0000-0100-0000-686fd5b60000\"", "_attachments": "attachments/", "_ts": 1752159670}, {"payPeriodId": "1020050939298015", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-03-27T00:00:00Z", "endDate": "2025-04-02T00:00:00Z", "submitByDate": "2025-04-02T00:00:00Z", "checkDate": "2025-04-04T00:00:00Z", "checkCount": 0, "id": "dd11386a-5073-4903-81bb-00587cb24b12", "companyId": "00124714", "type": "payperiod", "_rid": "NmJkAKiCbEfsVgAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfsVgAAAAAAAA==/", "_etag": "\"9800412f-0000-0100-0000-686fd5b60000\"", "_attachments": "attachments/", "_ts": 1752159670}, {"payPeriodId": "1020050999831923", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-04-03T00:00:00Z", "endDate": "2025-04-09T00:00:00Z", "submitByDate": "2025-04-09T00:00:00Z", "checkDate": "2025-04-11T00:00:00Z", "checkCount": 0, "id": "ae20e03b-8d7f-46c9-b82b-828df36574a5", "companyId": "00124714", "type": "payperiod", "_rid": "NmJkAKiCbEftVgAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEftVgAAAAAAAA==/", "_etag": "\"9800462f-0000-0100-0000-686fd5b60000\"", "_attachments": "attachments/", "_ts": 1752159670}, {"payPeriodId": "1020051056559877", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-04-10T00:00:00Z", "endDate": "2025-04-16T00:00:00Z", "submitByDate": "2025-04-16T00:00:00Z", "checkDate": "2025-04-18T00:00:00Z", "checkCount": 0, "id": "ce000a96-540c-478e-a0f7-f09043600b81", "companyId": "00124714", "type": "payperiod", "_rid": "NmJkAKiCbEfuVgAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfuVgAAAAAAAA==/", "_etag": "\"9800482f-0000-0100-0000-686fd5b60000\"", "_attachments": "attachments/", "_ts": 1752159670}, {"payPeriodId": "1020051118300633", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-04-17T00:00:00Z", "endDate": "2025-04-23T00:00:00Z", "submitByDate": "2025-04-23T00:00:00Z", "checkDate": "2025-04-25T00:00:00Z", "checkCount": 0, "id": "f64c88d6-cbcd-471e-a7d3-2dd30cbd3275", "companyId": "00124714", "type": "payperiod", "_rid": "NmJkAKiCbEfvVgAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfvVgAAAAAAAA==/", "_etag": "\"98004e2f-0000-0100-0000-686fd5b60000\"", "_attachments": "attachments/", "_ts": 1752159670}, {"payPeriodId": "1020051169090619", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-04-24T00:00:00Z", "endDate": "2025-04-30T00:00:00Z", "submitByDate": "2025-04-30T00:00:00Z", "checkDate": "2025-05-02T00:00:00Z", "checkCount": 0, "id": "1394149f-7690-405c-a27f-da6181d815e3", "companyId": "00124714", "type": "payperiod", "_rid": "NmJkAKiCbEfwVgAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfwVgAAAAAAAA==/", "_etag": "\"9800512f-0000-0100-0000-686fd5b60000\"", "_attachments": "attachments/", "_ts": 1752159670}, {"payPeriodId": "1020051232084028", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-05-01T00:00:00Z", "endDate": "2025-05-07T00:00:00Z", "submitByDate": "2025-05-07T00:00:00Z", "checkDate": "2025-05-09T00:00:00Z", "checkCount": 0, "id": "7135091d-c09f-4ef2-97b7-849e9da421dd", "companyId": "00124714", "type": "payperiod", "_rid": "NmJkAKiCbEfxVgAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfxVgAAAAAAAA==/", "_etag": "\"9800522f-0000-0100-0000-686fd5b60000\"", "_attachments": "attachments/", "_ts": 1752159670}, {"payPeriodId": "1020051284632311", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-05-08T00:00:00Z", "endDate": "2025-05-14T00:00:00Z", "submitByDate": "2025-05-14T00:00:00Z", "checkDate": "2025-05-16T00:00:00Z", "checkCount": 0, "id": "63d7f7ff-81a1-45aa-b83c-c9f27a45cf68", "companyId": "00124714", "type": "payperiod", "_rid": "NmJkAKiCbEfyVgAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfyVgAAAAAAAA==/", "_etag": "\"9800552f-0000-0100-0000-686fd5b60000\"", "_attachments": "attachments/", "_ts": 1752159670}, {"payPeriodId": "1020051345400467", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-05-15T00:00:00Z", "endDate": "2025-05-21T00:00:00Z", "submitByDate": "2025-05-21T00:00:00Z", "checkDate": "2025-05-23T00:00:00Z", "checkCount": 0, "id": "24907062-5993-405e-abcf-ed2b83496ec9", "companyId": "00124714", "type": "payperiod", "_rid": "NmJkAKiCbEfzVgAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfzVgAAAAAAAA==/", "_etag": "\"9800582f-0000-0100-0000-686fd5b60000\"", "_attachments": "attachments/", "_ts": 1752159670}, {"payPeriodId": "1020051397078881", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-05-22T00:00:00Z", "endDate": "2025-05-28T00:00:00Z", "submitByDate": "2025-05-28T00:00:00Z", "checkDate": "2025-05-30T00:00:00Z", "checkCount": 0, "id": "7853efee-2aa7-4945-b81c-48963f726201", "companyId": "00124714", "type": "payperiod", "_rid": "NmJkAKiCbEf0VgAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf0VgAAAAAAAA==/", "_etag": "\"98005b2f-0000-0100-0000-686fd5b70000\"", "_attachments": "attachments/", "_ts": 1752159671}, {"payPeriodId": "1020051459777814", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-05-29T00:00:00Z", "endDate": "2025-06-04T00:00:00Z", "submitByDate": "2025-06-04T00:00:00Z", "checkDate": "2025-06-06T00:00:00Z", "checkCount": 0, "id": "f142d142-777f-4585-af67-79171c5c23f3", "companyId": "00124714", "type": "payperiod", "_rid": "NmJkAKiCbEf1VgAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf1VgAAAAAAAA==/", "_etag": "\"9800602f-0000-0100-0000-686fd5b70000\"", "_attachments": "attachments/", "_ts": 1752159671}, {"payPeriodId": "1020051504643784", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-06-05T00:00:00Z", "endDate": "2025-06-11T00:00:00Z", "submitByDate": "2025-06-11T00:00:00Z", "checkDate": "2025-06-13T00:00:00Z", "checkCount": 0, "id": "920f032a-e3a8-4268-ac61-a7bee424ba0e", "companyId": "00124714", "type": "payperiod", "_rid": "NmJkAKiCbEf2VgAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf2VgAAAAAAAA==/", "_etag": "\"9800652f-0000-0100-0000-686fd5b70000\"", "_attachments": "attachments/", "_ts": 1752159671}, {"payPeriodId": "1020051563376655", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-06-12T00:00:00Z", "endDate": "2025-06-18T00:00:00Z", "submitByDate": "2025-06-18T00:00:00Z", "checkDate": "2025-06-20T00:00:00Z", "checkCount": 0, "id": "9179e85d-722e-496e-8593-f84ae6308ff8", "companyId": "00124714", "type": "payperiod", "_rid": "NmJkAKiCbEf3VgAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf3VgAAAAAAAA==/", "_etag": "\"98006b2f-0000-0100-0000-686fd5b70000\"", "_attachments": "attachments/", "_ts": 1752159671}, {"payPeriodId": "1020051617606794", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-06-19T00:00:00Z", "endDate": "2025-06-25T00:00:00Z", "submitByDate": "2025-06-25T00:00:00Z", "checkDate": "2025-06-27T00:00:00Z", "checkCount": 0, "id": "d2216eb3-b86c-4a30-998a-e93f415d53d6", "companyId": "00124714", "type": "payperiod", "_rid": "NmJkAKiCbEf4VgAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf4VgAAAAAAAA==/", "_etag": "\"98006d2f-0000-0100-0000-686fd5b70000\"", "_attachments": "attachments/", "_ts": 1752159671}, {"payPeriodId": "1020051676831472", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-06-26T00:00:00Z", "endDate": "2025-07-02T00:00:00Z", "submitByDate": "2025-07-02T00:00:00Z", "checkDate": "2025-07-04T00:00:00Z", "checkCount": 0, "id": "69fc6e64-5c19-47a3-b4ba-9b5e8be273d6", "companyId": "00124714", "type": "payperiod", "_rid": "NmJkAKiCbEf5VgAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf5VgAAAAAAAA==/", "_etag": "\"9800712f-0000-0100-0000-686fd5b70000\"", "_attachments": "attachments/", "_ts": 1752159671}, {"payPeriodId": "1020051741619653", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-07-03T00:00:00Z", "endDate": "2025-07-09T00:00:00Z", "submitByDate": "2025-07-09T00:00:00Z", "checkDate": "2025-07-11T00:00:00Z", "checkCount": 0, "id": "0ecbcf40-61b4-40be-9975-669e5efabd69", "companyId": "00124714", "type": "payperiod", "_rid": "NmJkAKiCbEf6VgAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf6VgAAAAAAAA==/", "_etag": "\"9800772f-0000-0100-0000-686fd5b70000\"", "_attachments": "attachments/", "_ts": 1752159671}, {"payPeriodId": "1020051802286883", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-07-10T00:00:00Z", "endDate": "2025-07-16T00:00:00Z", "submitByDate": "2025-07-16T00:00:00Z", "checkDate": "2025-07-18T00:00:00Z", "checkCount": 0, "id": "51168331-e924-4dce-93fe-59792f58df35", "companyId": "00124714", "type": "payperiod", "_rid": "NmJkAKiCbEf7VgAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf7VgAAAAAAAA==/", "_etag": "\"9800792f-0000-0100-0000-686fd5b70000\"", "_attachments": "attachments/", "_ts": 1752159671}, {"payPeriodId": "1020051868807776", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-07-17T00:00:00Z", "endDate": "2025-07-23T00:00:00Z", "submitByDate": "2025-07-23T00:00:00Z", "checkDate": "2025-07-25T00:00:00Z", "checkCount": 0, "id": "1e05949e-dbd7-4694-b76a-f5a14c54caa9", "companyId": "00124714", "type": "payperiod", "_rid": "NmJkAKiCbEf8VgAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf8VgAAAAAAAA==/", "_etag": "\"98007c2f-0000-0100-0000-686fd5b70000\"", "_attachments": "attachments/", "_ts": 1752159671}, {"payPeriodId": "1020051926678374", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-07-24T00:00:00Z", "endDate": "2025-07-30T00:00:00Z", "submitByDate": "2025-07-30T00:00:00Z", "checkDate": "2025-08-01T00:00:00Z", "checkCount": 0, "id": "ef8d0905-7b9e-4945-84e6-48c4290f28b3", "companyId": "00124714", "type": "payperiod", "_rid": "NmJkAKiCbEf9VgAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf9VgAAAAAAAA==/", "_etag": "\"98007e2f-0000-0100-0000-686fd5b70000\"", "_attachments": "attachments/", "_ts": 1752159671}, {"payPeriodId": "1020051992092657", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-07-31T00:00:00Z", "endDate": "2025-08-06T00:00:00Z", "submitByDate": "2025-08-06T00:00:00Z", "checkDate": "2025-08-08T00:00:00Z", "checkCount": 0, "id": "fc7d0a91-09a2-449a-94c7-b766f85a6113", "companyId": "00124714", "type": "payperiod", "_rid": "NmJkAKiCbEf+VgAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf+VgAAAAAAAA==/", "_etag": "\"9800892f-0000-0100-0000-686fd5b70000\"", "_attachments": "attachments/", "_ts": 1752159671}, {"payPeriodId": "1020052051065529", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-08-07T00:00:00Z", "endDate": "2025-08-13T00:00:00Z", "submitByDate": "2025-08-13T00:00:00Z", "checkDate": "2025-08-15T00:00:00Z", "checkCount": 0, "id": "98ab98ad-1ace-444c-9430-148c1a66c715", "companyId": "00124714", "type": "payperiod", "_rid": "NmJkAKiCbEf-VgAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf-VgAAAAAAAA==/", "_etag": "\"98008d2f-0000-0100-0000-686fd5b70000\"", "_attachments": "attachments/", "_ts": 1752159671}, {"payPeriodId": "1020052113855717", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-08-14T00:00:00Z", "endDate": "2025-08-20T00:00:00Z", "submitByDate": "2025-08-20T00:00:00Z", "checkDate": "2025-08-22T00:00:00Z", "checkCount": 0, "id": "451137fe-587e-43b7-83db-db3d6defde61", "companyId": "00124714", "type": "payperiod", "_rid": "NmJkAKiCbEcAVwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcAVwAAAAAAAA==/", "_etag": "\"9800902f-0000-0100-0000-686fd5b70000\"", "_attachments": "attachments/", "_ts": 1752159671}, {"payPeriodId": "1020052169817032", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-08-21T00:00:00Z", "endDate": "2025-08-27T00:00:00Z", "submitByDate": "2025-08-27T00:00:00Z", "checkDate": "2025-08-29T00:00:00Z", "checkCount": 0, "id": "84dc076a-5663-424b-86aa-52523c69da16", "companyId": "00124714", "type": "payperiod", "_rid": "NmJkAKiCbEcBVwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcBVwAAAAAAAA==/", "_etag": "\"9800942f-0000-0100-0000-686fd5b70000\"", "_attachments": "attachments/", "_ts": 1752159671}, {"payPeriodId": "1020052230237121", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-08-28T00:00:00Z", "endDate": "2025-09-03T00:00:00Z", "submitByDate": "2025-09-03T00:00:00Z", "checkDate": "2025-09-05T00:00:00Z", "checkCount": 0, "id": "707a2d8a-0a48-400a-a1cb-524c0759dbbe", "companyId": "00124714", "type": "payperiod", "_rid": "NmJkAKiCbEcCVwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcCVwAAAAAAAA==/", "_etag": "\"9800982f-0000-0100-0000-686fd5b80000\"", "_attachments": "attachments/", "_ts": 1752159672}, {"payPeriodId": "1020052289959859", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-09-04T00:00:00Z", "endDate": "2025-09-10T00:00:00Z", "submitByDate": "2025-09-10T00:00:00Z", "checkDate": "2025-09-12T00:00:00Z", "checkCount": 0, "id": "b7f76a08-e879-485d-9c22-d619dd7379c5", "companyId": "00124714", "type": "payperiod", "_rid": "NmJkAKiCbEcDVwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcDVwAAAAAAAA==/", "_etag": "\"9800992f-0000-0100-0000-686fd5b80000\"", "_attachments": "attachments/", "_ts": 1752159672}, {"payPeriodId": "1020052350668009", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-09-11T00:00:00Z", "endDate": "2025-09-17T00:00:00Z", "submitByDate": "2025-09-17T00:00:00Z", "checkDate": "2025-09-19T00:00:00Z", "checkCount": 0, "id": "a1533fba-905e-415e-9754-b7cca45eb2b3", "companyId": "00124714", "type": "payperiod", "_rid": "NmJkAKiCbEcEVwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcEVwAAAAAAAA==/", "_etag": "\"98009b2f-0000-0100-0000-686fd5b80000\"", "_attachments": "attachments/", "_ts": 1752159672}, {"payPeriodId": "1020052402312519", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-09-18T00:00:00Z", "endDate": "2025-09-24T00:00:00Z", "submitByDate": "2025-09-24T00:00:00Z", "checkDate": "2025-09-26T00:00:00Z", "checkCount": 0, "id": "834f989c-7ebb-46af-a82d-51ca75ef17f8", "companyId": "00124714", "type": "payperiod", "_rid": "NmJkAKiCbEcFVwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcFVwAAAAAAAA==/", "_etag": "\"98009f2f-0000-0100-0000-686fd5b80000\"", "_attachments": "attachments/", "_ts": 1752159672}, {"payPeriodId": "1020052470777823", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-09-25T00:00:00Z", "endDate": "2025-10-01T00:00:00Z", "submitByDate": "2025-10-01T00:00:00Z", "checkDate": "2025-10-03T00:00:00Z", "checkCount": 0, "id": "77a44742-18ab-4884-b8d4-969e3ac2861d", "companyId": "00124714", "type": "payperiod", "_rid": "NmJkAKiCbEcGVwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcGVwAAAAAAAA==/", "_etag": "\"9800a62f-0000-0100-0000-686fd5b80000\"", "_attachments": "attachments/", "_ts": 1752159672}, {"payPeriodId": "1020052536471687", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-10-02T00:00:00Z", "endDate": "2025-10-08T00:00:00Z", "submitByDate": "2025-10-08T00:00:00Z", "checkDate": "2025-10-10T00:00:00Z", "checkCount": 0, "id": "ea9fbe57-5437-474e-8d5b-e4c8e2ed77cb", "companyId": "00124714", "type": "payperiod", "_rid": "NmJkAKiCbEcHVwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcHVwAAAAAAAA==/", "_etag": "\"9800aa2f-0000-0100-0000-686fd5b80000\"", "_attachments": "attachments/", "_ts": 1752159672}, {"payPeriodId": "1020050493312591", "intervalCode": "WEEKLY", "status": "REVERSED", "description": "Weekly Payroll (1)", "startDate": "2025-02-06T00:00:00Z", "endDate": "2025-02-12T00:00:00Z", "submitByDate": "2025-02-12T00:00:00Z", "checkDate": "2025-02-14T00:00:00Z", "checkCount": 18, "id": "0a7fdfdd-4fa3-4429-a350-b5378a12cd29", "companyId": "00124714", "type": "payperiod", "_rid": "NmJkAKiCbEcIVwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcIVwAAAAAAAA==/", "_etag": "\"9800ac2f-0000-0100-0000-686fd5b80000\"", "_attachments": "attachments/", "_ts": 1752159672}, {"payPeriodId": "1020050118164640", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2024-12-26T00:00:00Z", "endDate": "2025-01-01T00:00:00Z", "submitByDate": "2024-12-31T00:00:00Z", "checkDate": "2025-01-03T00:00:00Z", "checkCount": 26, "id": "cc6ae7fc-968f-4c02-9178-6465b837782b", "companyId": "00124714", "type": "payperiod", "_rid": "NmJkAKiCbEfPVwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfPVwAAAAAAAA==/", "_etag": "\"9800c132-0000-0100-0000-686fd5c70000\"", "_attachments": "attachments/", "_ts": 1752159687}, {"payPeriodId": "1020050179417984", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-01-02T00:00:00Z", "endDate": "2025-01-08T00:00:00Z", "submitByDate": "2025-01-08T00:00:00Z", "checkDate": "2025-01-10T00:00:00Z", "checkCount": 28, "id": "cc7ffc9a-6ed7-4619-84dd-f6c800434b02", "companyId": "00124714", "type": "payperiod", "_rid": "NmJkAKiCbEfQVwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfQVwAAAAAAAA==/", "_etag": "\"9800c532-0000-0100-0000-686fd5c70000\"", "_attachments": "attachments/", "_ts": 1752159687}, {"payPeriodId": "1020050239948857", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-01-09T00:00:00Z", "endDate": "2025-01-15T00:00:00Z", "submitByDate": "2025-01-15T00:00:00Z", "checkDate": "2025-01-17T00:00:00Z", "checkCount": 28, "id": "ef3f6e94-00fd-4dc1-aa96-0265f2729603", "companyId": "00124714", "type": "payperiod", "_rid": "NmJkAKiCbEfRVwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfRVwAAAAAAAA==/", "_etag": "\"9800ca32-0000-0100-0000-686fd5c70000\"", "_attachments": "attachments/", "_ts": 1752159687}, {"payPeriodId": "1020050300187219", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-01-16T00:00:00Z", "endDate": "2025-01-22T00:00:00Z", "submitByDate": "2025-01-22T00:00:00Z", "checkDate": "2025-01-24T00:00:00Z", "checkCount": 28, "id": "35a641f3-61cd-488b-ae4b-c56736b128cd", "companyId": "00124714", "type": "payperiod", "_rid": "NmJkAKiCbEfSVwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfSVwAAAAAAAA==/", "_etag": "\"9800ce32-0000-0100-0000-686fd5c70000\"", "_attachments": "attachments/", "_ts": 1752159687}, {"payPeriodId": "1020050368003729", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-01-23T00:00:00Z", "endDate": "2025-01-29T00:00:00Z", "submitByDate": "2025-01-29T00:00:00Z", "checkDate": "2025-01-31T00:00:00Z", "checkCount": 28, "id": "2272fd88-c63e-4b51-8296-c9a395a70d5a", "companyId": "00124714", "type": "payperiod", "_rid": "NmJkAKiCbEfTVwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfTVwAAAAAAAA==/", "_etag": "\"9800d332-0000-0100-0000-686fd5c70000\"", "_attachments": "attachments/", "_ts": 1752159687}, {"payPeriodId": "1020051223764195", "status": "COMPLETED", "description": "errors", "startDate": "2025-01-23T00:00:00Z", "endDate": "2025-01-29T00:00:00Z", "submitByDate": "2025-01-30T00:00:00Z", "checkDate": "2025-01-31T00:00:00Z", "checkCount": 1, "id": "089c67a5-7efd-4ddf-a73d-ec36f2b37221", "companyId": "00124714", "type": "payperiod", "_rid": "NmJkAKiCbEfUVwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfUVwAAAAAAAA==/", "_etag": "\"9800d432-0000-0100-0000-686fd5c70000\"", "_attachments": "attachments/", "_ts": 1752159687}, {"payPeriodId": "1020051223846182", "status": "COMPLETED", "description": "correction", "startDate": "2025-01-23T00:00:00Z", "endDate": "2025-01-29T00:00:00Z", "submitByDate": "2025-01-30T00:00:00Z", "checkDate": "2025-01-31T00:00:00Z", "checkCount": 1, "id": "30f6989b-c263-4196-b5a0-a6d302986e47", "companyId": "00124714", "type": "payperiod", "_rid": "NmJkAKiCbEfVVwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfVVwAAAAAAAA==/", "_etag": "\"9800d732-0000-0100-0000-686fd5c70000\"", "_attachments": "attachments/", "_ts": 1752159687}, {"payPeriodId": "1020050433244788", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-01-30T00:00:00Z", "endDate": "2025-02-05T00:00:00Z", "submitByDate": "2025-02-05T00:00:00Z", "checkDate": "2025-02-07T00:00:00Z", "checkCount": 28, "id": "ac4173b4-04ef-4ade-8daf-91e78e596563", "companyId": "00124714", "type": "payperiod", "_rid": "NmJkAKiCbEfWVwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfWVwAAAAAAAA==/", "_etag": "\"9800db32-0000-0100-0000-686fd5c70000\"", "_attachments": "attachments/", "_ts": 1752159687}, {"payPeriodId": "1020051340017568", "status": "COMPLETED", "description": "Reverse", "startDate": "2025-02-06T00:00:00Z", "endDate": "2025-02-12T00:00:00Z", "submitByDate": "2025-02-13T00:00:00Z", "checkDate": "2025-02-14T00:00:00Z", "checkCount": 18, "originalPayPeriodID": "1020050493312591", "id": "e63b1bda-eeea-4dd9-8720-3b54f7e69179", "companyId": "00124714", "type": "payperiod", "_rid": "NmJkAKiCbEfXVwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfXVwAAAAAAAA==/", "_etag": "\"9800e032-0000-0100-0000-686fd5c70000\"", "_attachments": "attachments/", "_ts": 1752159687}, {"payPeriodId": "1020051340017962", "status": "COMPLETED", "description": "Payroll", "startDate": "2025-02-06T00:00:00Z", "endDate": "2025-02-12T00:00:00Z", "submitByDate": "2025-02-13T00:00:00Z", "checkDate": "2025-02-14T00:00:00Z", "checkCount": 28, "id": "d1ff803e-4a29-49c7-ab49-b1cca015ba33", "companyId": "00124714", "type": "payperiod", "_rid": "NmJkAKiCbEfYVwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfYVwAAAAAAAA==/", "_etag": "\"9800e232-0000-0100-0000-686fd5c70000\"", "_attachments": "attachments/", "_ts": 1752159687}, {"payPeriodId": "1020050553568829", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-02-13T00:00:00Z", "endDate": "2025-02-19T00:00:00Z", "submitByDate": "2025-02-19T00:00:00Z", "checkDate": "2025-02-21T00:00:00Z", "checkCount": 28, "id": "65418f4c-b96f-4d0e-b226-10e385d50be7", "companyId": "00124714", "type": "payperiod", "_rid": "NmJkAKiCbEfZVwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfZVwAAAAAAAA==/", "_etag": "\"9800e332-0000-0100-0000-686fd5c80000\"", "_attachments": "attachments/", "_ts": 1752159688}, {"payPeriodId": "1020050611310415", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-02-20T00:00:00Z", "endDate": "2025-02-26T00:00:00Z", "submitByDate": "2025-02-26T00:00:00Z", "checkDate": "2025-02-28T00:00:00Z", "checkCount": 28, "id": "18092448-9ef8-411b-a8c4-39e9e2bde33d", "companyId": "00124714", "type": "payperiod", "_rid": "NmJkAKiCbEfaVwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfaVwAAAAAAAA==/", "_etag": "\"9800e532-0000-0100-0000-686fd5c80000\"", "_attachments": "attachments/", "_ts": 1752159688}, {"payPeriodId": "1020050672307040", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-02-27T00:00:00Z", "endDate": "2025-03-05T00:00:00Z", "submitByDate": "2025-03-05T00:00:00Z", "checkDate": "2025-03-07T00:00:00Z", "checkCount": 28, "id": "d830ebf8-f44f-4ca0-aade-a3d45c376419", "companyId": "00124714", "type": "payperiod", "_rid": "NmJkAKiCbEfbVwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfbVwAAAAAAAA==/", "_etag": "\"9800ec32-0000-0100-0000-686fd5c80000\"", "_attachments": "attachments/", "_ts": 1752159688}, {"payPeriodId": "1020050737626005", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-03-06T00:00:00Z", "endDate": "2025-03-12T00:00:00Z", "submitByDate": "2025-03-12T00:00:00Z", "checkDate": "2025-03-14T00:00:00Z", "checkCount": 28, "id": "71086e0a-7e2c-409e-953c-0e8fd5b9a598", "companyId": "00124714", "type": "payperiod", "_rid": "NmJkAKiCbEfcVwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfcVwAAAAAAAA==/", "_etag": "\"9800ef32-0000-0100-0000-686fd5c80000\"", "_attachments": "attachments/", "_ts": 1752159688}, {"payPeriodId": "1020050809511318", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-03-13T00:00:00Z", "endDate": "2025-03-19T00:00:00Z", "submitByDate": "2025-03-19T00:00:00Z", "checkDate": "2025-03-21T00:00:00Z", "checkCount": 28, "id": "57bea6eb-dc53-412d-bcea-25df20e7d318", "companyId": "00124714", "type": "payperiod", "_rid": "NmJkAKiCbEfdVwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfdVwAAAAAAAA==/", "_etag": "\"9800f132-0000-0100-0000-686fd5c80000\"", "_attachments": "attachments/", "_ts": 1752159688}, {"payPeriodId": "1020050876081578", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-03-20T00:00:00Z", "endDate": "2025-03-26T00:00:00Z", "submitByDate": "2025-03-26T00:00:00Z", "checkDate": "2025-03-28T00:00:00Z", "checkCount": 29, "id": "8ba497ad-c6b6-407a-b4cd-ab9cc05ca1d9", "companyId": "00124714", "type": "payperiod", "_rid": "NmJkAKiCbEfeVwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfeVwAAAAAAAA==/", "_etag": "\"9800f332-0000-0100-0000-686fd5c80000\"", "_attachments": "attachments/", "_ts": 1752159688}, {"payPeriodId": "1020050939298015", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-03-27T00:00:00Z", "endDate": "2025-04-02T00:00:00Z", "submitByDate": "2025-04-02T00:00:00Z", "checkDate": "2025-04-04T00:00:00Z", "checkCount": 29, "id": "bdb61602-af1b-4267-a800-c5588a0630d8", "companyId": "00124714", "type": "payperiod", "_rid": "NmJkAKiCbEffVwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEffVwAAAAAAAA==/", "_etag": "\"9800f632-0000-0100-0000-686fd5c80000\"", "_attachments": "attachments/", "_ts": 1752159688}, {"payPeriodId": "1020050999831923", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-04-03T00:00:00Z", "endDate": "2025-04-09T00:00:00Z", "submitByDate": "2025-04-09T00:00:00Z", "checkDate": "2025-04-11T00:00:00Z", "checkCount": 29, "id": "9e442691-5381-4ef7-af55-b80482704b51", "companyId": "00124714", "type": "payperiod", "_rid": "NmJkAKiCbEfgVwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfgVwAAAAAAAA==/", "_etag": "\"9800f832-0000-0100-0000-686fd5c80000\"", "_attachments": "attachments/", "_ts": 1752159688}, {"payPeriodId": "1020051056559877", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-04-10T00:00:00Z", "endDate": "2025-04-16T00:00:00Z", "submitByDate": "2025-04-16T00:00:00Z", "checkDate": "2025-04-18T00:00:00Z", "checkCount": 28, "id": "fcce6e5f-c724-4fea-98a3-6c3e1204da7b", "companyId": "00124714", "type": "payperiod", "_rid": "NmJkAKiCbEfhVwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfhVwAAAAAAAA==/", "_etag": "\"9800fa32-0000-0100-0000-686fd5c80000\"", "_attachments": "attachments/", "_ts": 1752159688}, {"payPeriodId": "1020051118300633", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-04-17T00:00:00Z", "endDate": "2025-04-23T00:00:00Z", "submitByDate": "2025-04-23T00:00:00Z", "checkDate": "2025-04-25T00:00:00Z", "checkCount": 28, "id": "7e29bf00-ca27-455d-8c10-cf57c158c153", "companyId": "00124714", "type": "payperiod", "_rid": "NmJkAKiCbEfiVwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfiVwAAAAAAAA==/", "_etag": "\"9800fc32-0000-0100-0000-686fd5c80000\"", "_attachments": "attachments/", "_ts": 1752159688}, {"payPeriodId": "1020051169090619", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-04-24T00:00:00Z", "endDate": "2025-04-30T00:00:00Z", "submitByDate": "2025-04-30T00:00:00Z", "checkDate": "2025-05-02T00:00:00Z", "checkCount": 27, "id": "34bfc05d-bfc8-4870-87ef-0f25d660b923", "companyId": "00124714", "type": "payperiod", "_rid": "NmJkAKiCbEfjVwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfjVwAAAAAAAA==/", "_etag": "\"9800fe32-0000-0100-0000-686fd5c80000\"", "_attachments": "attachments/", "_ts": 1752159688}, {"payPeriodId": "1020051232084028", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-05-01T00:00:00Z", "endDate": "2025-05-07T00:00:00Z", "submitByDate": "2025-05-07T00:00:00Z", "checkDate": "2025-05-09T00:00:00Z", "checkCount": 28, "id": "3e7f04a8-9de0-4bec-87a3-cebecd9b27b5", "companyId": "00124714", "type": "payperiod", "_rid": "NmJkAKiCbEfkVwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfkVwAAAAAAAA==/", "_etag": "\"98000333-0000-0100-0000-686fd5c80000\"", "_attachments": "attachments/", "_ts": 1752159688}, {"payPeriodId": "1020051284632311", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-05-08T00:00:00Z", "endDate": "2025-05-14T00:00:00Z", "submitByDate": "2025-05-14T00:00:00Z", "checkDate": "2025-05-16T00:00:00Z", "checkCount": 28, "id": "7718caf7-9a39-4686-b193-8228c6b99189", "companyId": "00124714", "type": "payperiod", "_rid": "NmJkAKiCbEflVwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEflVwAAAAAAAA==/", "_etag": "\"98000633-0000-0100-0000-686fd5c80000\"", "_attachments": "attachments/", "_ts": 1752159688}, {"payPeriodId": "1020051345400467", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-05-15T00:00:00Z", "endDate": "2025-05-21T00:00:00Z", "submitByDate": "2025-05-21T00:00:00Z", "checkDate": "2025-05-23T00:00:00Z", "checkCount": 28, "id": "eabcf860-15ba-4fba-8fef-59646422e5cc", "companyId": "00124714", "type": "payperiod", "_rid": "NmJkAKiCbEfmVwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfmVwAAAAAAAA==/", "_etag": "\"98000933-0000-0100-0000-686fd5c80000\"", "_attachments": "attachments/", "_ts": 1752159688}, {"payPeriodId": "1020051397078881", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-05-22T00:00:00Z", "endDate": "2025-05-28T00:00:00Z", "submitByDate": "2025-05-28T00:00:00Z", "checkDate": "2025-05-30T00:00:00Z", "checkCount": 28, "id": "333f3143-fd38-4192-a22c-93e47057c1e5", "companyId": "00124714", "type": "payperiod", "_rid": "NmJkAKiCbEfnVwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfnVwAAAAAAAA==/", "_etag": "\"98000d33-0000-0100-0000-686fd5c90000\"", "_attachments": "attachments/", "_ts": 1752159689}, {"payPeriodId": "1020051459777814", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-05-29T00:00:00Z", "endDate": "2025-06-04T00:00:00Z", "submitByDate": "2025-06-04T00:00:00Z", "checkDate": "2025-06-06T00:00:00Z", "checkCount": 28, "id": "1ecc5474-54bb-498f-b01c-677e77d3e7a3", "companyId": "00124714", "type": "payperiod", "_rid": "NmJkAKiCbEfoVwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfoVwAAAAAAAA==/", "_etag": "\"98001033-0000-0100-0000-686fd5c90000\"", "_attachments": "attachments/", "_ts": 1752159689}, {"payPeriodId": "1020051504643784", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-06-05T00:00:00Z", "endDate": "2025-06-11T00:00:00Z", "submitByDate": "2025-06-11T00:00:00Z", "checkDate": "2025-06-13T00:00:00Z", "checkCount": 28, "id": "cb33c358-6092-4e79-9c24-75557eb82ffa", "companyId": "00124714", "type": "payperiod", "_rid": "NmJkAKiCbEfpVwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfpVwAAAAAAAA==/", "_etag": "\"98001533-0000-0100-0000-686fd5c90000\"", "_attachments": "attachments/", "_ts": 1752159689}, {"payPeriodId": "1020051563376655", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-06-12T00:00:00Z", "endDate": "2025-06-18T00:00:00Z", "submitByDate": "2025-06-18T00:00:00Z", "checkDate": "2025-06-20T00:00:00Z", "checkCount": 27, "id": "cc32ba25-802d-4251-bd6b-4ca5f1f9eb10", "companyId": "00124714", "type": "payperiod", "_rid": "NmJkAKiCbEfqVwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfqVwAAAAAAAA==/", "_etag": "\"98001733-0000-0100-0000-686fd5c90000\"", "_attachments": "attachments/", "_ts": 1752159689}, {"payPeriodId": "1020051617606794", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-06-19T00:00:00Z", "endDate": "2025-06-25T00:00:00Z", "submitByDate": "2025-06-25T00:00:00Z", "checkDate": "2025-06-27T00:00:00Z", "checkCount": 26, "id": "9235db32-d1d8-4e20-b57c-520a1d6c3f95", "companyId": "00124714", "type": "payperiod", "_rid": "NmJkAKiCbEfrVwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfrVwAAAAAAAA==/", "_etag": "\"98001933-0000-0100-0000-686fd5c90000\"", "_attachments": "attachments/", "_ts": 1752159689}, {"payPeriodId": "1020051676831472", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-06-26T00:00:00Z", "endDate": "2025-07-02T00:00:00Z", "submitByDate": "2025-07-02T00:00:00Z", "checkDate": "2025-07-04T00:00:00Z", "checkCount": 27, "id": "82a135df-264c-442f-a24c-8556833d6d46", "companyId": "00124714", "type": "payperiod", "_rid": "NmJkAKiCbEfsVwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfsVwAAAAAAAA==/", "_etag": "\"98001a33-0000-0100-0000-686fd5c90000\"", "_attachments": "attachments/", "_ts": 1752159689}, {"payPeriodId": "1020051741619653", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-07-03T00:00:00Z", "endDate": "2025-07-09T00:00:00Z", "submitByDate": "2025-07-09T00:00:00Z", "checkDate": "2025-07-11T00:00:00Z", "checkCount": 0, "id": "8c2bc341-0c94-43f2-b318-2245c4784d7c", "companyId": "00124714", "type": "payperiod", "_rid": "NmJkAKiCbEftVwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEftVwAAAAAAAA==/", "_etag": "\"98001c33-0000-0100-0000-686fd5c90000\"", "_attachments": "attachments/", "_ts": 1752159689}, {"payPeriodId": "1020051802286883", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-07-10T00:00:00Z", "endDate": "2025-07-16T00:00:00Z", "submitByDate": "2025-07-16T00:00:00Z", "checkDate": "2025-07-18T00:00:00Z", "checkCount": 0, "id": "fba1d14a-b745-40bc-9b14-ceb448c31520", "companyId": "00124714", "type": "payperiod", "_rid": "NmJkAKiCbEfuVwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfuVwAAAAAAAA==/", "_etag": "\"98002233-0000-0100-0000-686fd5c90000\"", "_attachments": "attachments/", "_ts": 1752159689}, {"payPeriodId": "1020051868807776", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-07-17T00:00:00Z", "endDate": "2025-07-23T00:00:00Z", "submitByDate": "2025-07-23T00:00:00Z", "checkDate": "2025-07-25T00:00:00Z", "checkCount": 0, "id": "d93a473a-5bd7-496a-bea7-86b4585b8f9c", "companyId": "00124714", "type": "payperiod", "_rid": "NmJkAKiCbEfvVwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfvVwAAAAAAAA==/", "_etag": "\"98002433-0000-0100-0000-686fd5c90000\"", "_attachments": "attachments/", "_ts": 1752159689}, {"payPeriodId": "1020051926678374", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-07-24T00:00:00Z", "endDate": "2025-07-30T00:00:00Z", "submitByDate": "2025-07-30T00:00:00Z", "checkDate": "2025-08-01T00:00:00Z", "checkCount": 0, "id": "3ed3e429-5af2-4ea9-a194-768e46e88c45", "companyId": "00124714", "type": "payperiod", "_rid": "NmJkAKiCbEfwVwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfwVwAAAAAAAA==/", "_etag": "\"98002933-0000-0100-0000-686fd5c90000\"", "_attachments": "attachments/", "_ts": 1752159689}, {"payPeriodId": "1020051992092657", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-07-31T00:00:00Z", "endDate": "2025-08-06T00:00:00Z", "submitByDate": "2025-08-06T00:00:00Z", "checkDate": "2025-08-08T00:00:00Z", "checkCount": 0, "id": "2db6be8f-1858-4882-896e-4238635a98e5", "companyId": "00124714", "type": "payperiod", "_rid": "NmJkAKiCbEfxVwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfxVwAAAAAAAA==/", "_etag": "\"98002a33-0000-0100-0000-686fd5c90000\"", "_attachments": "attachments/", "_ts": 1752159689}, {"payPeriodId": "1020052051065529", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-08-07T00:00:00Z", "endDate": "2025-08-13T00:00:00Z", "submitByDate": "2025-08-13T00:00:00Z", "checkDate": "2025-08-15T00:00:00Z", "checkCount": 0, "id": "ef0e7fe3-d421-4e1f-9dda-3fa84375694b", "companyId": "00124714", "type": "payperiod", "_rid": "NmJkAKiCbEfyVwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfyVwAAAAAAAA==/", "_etag": "\"98002c33-0000-0100-0000-686fd5c90000\"", "_attachments": "attachments/", "_ts": 1752159689}, {"payPeriodId": "1020052113855717", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-08-14T00:00:00Z", "endDate": "2025-08-20T00:00:00Z", "submitByDate": "2025-08-20T00:00:00Z", "checkDate": "2025-08-22T00:00:00Z", "checkCount": 0, "id": "0cfdd5a7-2127-487e-9f8b-e8eb7d1ab990", "companyId": "00124714", "type": "payperiod", "_rid": "NmJkAKiCbEfzVwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfzVwAAAAAAAA==/", "_etag": "\"98003133-0000-0100-0000-686fd5c90000\"", "_attachments": "attachments/", "_ts": 1752159689}, {"payPeriodId": "1020052169817032", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-08-21T00:00:00Z", "endDate": "2025-08-27T00:00:00Z", "submitByDate": "2025-08-27T00:00:00Z", "checkDate": "2025-08-29T00:00:00Z", "checkCount": 0, "id": "d835f13c-40fc-45ec-b5d5-8b0aa41ab80b", "companyId": "00124714", "type": "payperiod", "_rid": "NmJkAKiCbEf0VwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf0VwAAAAAAAA==/", "_etag": "\"98003333-0000-0100-0000-686fd5c90000\"", "_attachments": "attachments/", "_ts": 1752159689}, {"payPeriodId": "1020052230237121", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-08-28T00:00:00Z", "endDate": "2025-09-03T00:00:00Z", "submitByDate": "2025-09-03T00:00:00Z", "checkDate": "2025-09-05T00:00:00Z", "checkCount": 0, "id": "1d0961d6-8ef4-4c5a-b9f1-16bfcc3b2e27", "companyId": "00124714", "type": "payperiod", "_rid": "NmJkAKiCbEf1VwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf1VwAAAAAAAA==/", "_etag": "\"98003533-0000-0100-0000-686fd5ca0000\"", "_attachments": "attachments/", "_ts": 1752159690}, {"payPeriodId": "1020052289959859", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-09-04T00:00:00Z", "endDate": "2025-09-10T00:00:00Z", "submitByDate": "2025-09-10T00:00:00Z", "checkDate": "2025-09-12T00:00:00Z", "checkCount": 0, "id": "15620e3d-cdf9-46d4-b7f5-6b067722dfbf", "companyId": "00124714", "type": "payperiod", "_rid": "NmJkAKiCbEf2VwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf2VwAAAAAAAA==/", "_etag": "\"98003c33-0000-0100-0000-686fd5ca0000\"", "_attachments": "attachments/", "_ts": 1752159690}, {"payPeriodId": "1020052350668009", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-09-11T00:00:00Z", "endDate": "2025-09-17T00:00:00Z", "submitByDate": "2025-09-17T00:00:00Z", "checkDate": "2025-09-19T00:00:00Z", "checkCount": 0, "id": "6433b6d6-d706-4e49-968f-0168ff1fb8b0", "companyId": "00124714", "type": "payperiod", "_rid": "NmJkAKiCbEf3VwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf3VwAAAAAAAA==/", "_etag": "\"98003f33-0000-0100-0000-686fd5ca0000\"", "_attachments": "attachments/", "_ts": 1752159690}, {"payPeriodId": "1020052402312519", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-09-18T00:00:00Z", "endDate": "2025-09-24T00:00:00Z", "submitByDate": "2025-09-24T00:00:00Z", "checkDate": "2025-09-26T00:00:00Z", "checkCount": 0, "id": "2622daab-e202-45fb-a209-e88074f25220", "companyId": "00124714", "type": "payperiod", "_rid": "NmJkAKiCbEf4VwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf4VwAAAAAAAA==/", "_etag": "\"98004533-0000-0100-0000-686fd5ca0000\"", "_attachments": "attachments/", "_ts": 1752159690}, {"payPeriodId": "1020052470777823", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-09-25T00:00:00Z", "endDate": "2025-10-01T00:00:00Z", "submitByDate": "2025-10-01T00:00:00Z", "checkDate": "2025-10-03T00:00:00Z", "checkCount": 0, "id": "ba7a6553-5613-449b-ae9c-813bdc2bff87", "companyId": "00124714", "type": "payperiod", "_rid": "NmJkAKiCbEf5VwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf5VwAAAAAAAA==/", "_etag": "\"98004b33-0000-0100-0000-686fd5ca0000\"", "_attachments": "attachments/", "_ts": 1752159690}, {"payPeriodId": "1020052536471687", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-10-02T00:00:00Z", "endDate": "2025-10-08T00:00:00Z", "submitByDate": "2025-10-08T00:00:00Z", "checkDate": "2025-10-10T00:00:00Z", "checkCount": 0, "id": "d73e6d19-4bba-4d5f-a66b-115238969f92", "companyId": "00124714", "type": "payperiod", "_rid": "NmJkAKiCbEf6VwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf6VwAAAAAAAA==/", "_etag": "\"98004f33-0000-0100-0000-686fd5ca0000\"", "_attachments": "attachments/", "_ts": 1752159690}, {"payPeriodId": "1020050493312591", "intervalCode": "WEEKLY", "status": "REVERSED", "description": "Weekly Payroll (1)", "startDate": "2025-02-06T00:00:00Z", "endDate": "2025-02-12T00:00:00Z", "submitByDate": "2025-02-12T00:00:00Z", "checkDate": "2025-02-14T00:00:00Z", "checkCount": 18, "id": "ac427bb5-2423-4fc3-a556-2c50b961fbe5", "companyId": "00124714", "type": "payperiod", "_rid": "NmJkAKiCbEf7VwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf7VwAAAAAAAA==/", "_etag": "\"98005333-0000-0100-0000-686fd5ca0000\"", "_attachments": "attachments/", "_ts": 1752159690}, {"payPeriodId": "1020050118164640", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2024-12-26T00:00:00Z", "endDate": "2025-01-01T00:00:00Z", "submitByDate": "2024-12-31T00:00:00Z", "checkDate": "2025-01-03T00:00:00Z", "checkCount": 26, "id": "5d0ce162-53d5-49db-ac24-832a2f95fc8f", "companyId": "00124714", "type": "payperiod", "_rid": "NmJkAKiCbEcJUAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcJUAEAAAAAAA==/", "_etag": "\"9e00955c-0000-0100-0000-686ffc6b0000\"", "_attachments": "attachments/", "_ts": 1752169579}, {"payPeriodId": "1020050179417984", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-01-02T00:00:00Z", "endDate": "2025-01-08T00:00:00Z", "submitByDate": "2025-01-08T00:00:00Z", "checkDate": "2025-01-10T00:00:00Z", "checkCount": 28, "id": "1473c12c-810a-42dc-ab1a-de6b02b61e70", "companyId": "00124714", "type": "payperiod", "_rid": "NmJkAKiCbEcKUAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcKUAEAAAAAAA==/", "_etag": "\"9e00975c-0000-0100-0000-686ffc6b0000\"", "_attachments": "attachments/", "_ts": 1752169579}, {"payPeriodId": "1020050239948857", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-01-09T00:00:00Z", "endDate": "2025-01-15T00:00:00Z", "submitByDate": "2025-01-15T00:00:00Z", "checkDate": "2025-01-17T00:00:00Z", "checkCount": 28, "id": "b72c9dd5-d6dd-42ce-aa8b-9d8821141970", "companyId": "00124714", "type": "payperiod", "_rid": "NmJkAKiCbEcLUAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcLUAEAAAAAAA==/", "_etag": "\"9e00995c-0000-0100-0000-686ffc6b0000\"", "_attachments": "attachments/", "_ts": 1752169579}, {"payPeriodId": "1020050300187219", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-01-16T00:00:00Z", "endDate": "2025-01-22T00:00:00Z", "submitByDate": "2025-01-22T00:00:00Z", "checkDate": "2025-01-24T00:00:00Z", "checkCount": 28, "id": "cb62cd6d-5bd8-4499-a7c1-57707194866e", "companyId": "00124714", "type": "payperiod", "_rid": "NmJkAKiCbEcMUAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcMUAEAAAAAAA==/", "_etag": "\"9e009c5c-0000-0100-0000-686ffc6b0000\"", "_attachments": "attachments/", "_ts": 1752169579}, {"payPeriodId": "1020050368003729", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-01-23T00:00:00Z", "endDate": "2025-01-29T00:00:00Z", "submitByDate": "2025-01-29T00:00:00Z", "checkDate": "2025-01-31T00:00:00Z", "checkCount": 28, "id": "671c906d-edd3-4f8e-a06d-83f1a34e7232", "companyId": "00124714", "type": "payperiod", "_rid": "NmJkAKiCbEcNUAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcNUAEAAAAAAA==/", "_etag": "\"9e00a05c-0000-0100-0000-686ffc6b0000\"", "_attachments": "attachments/", "_ts": 1752169579}, {"payPeriodId": "1020051223764195", "status": "COMPLETED", "description": "errors", "startDate": "2025-01-23T00:00:00Z", "endDate": "2025-01-29T00:00:00Z", "submitByDate": "2025-01-30T00:00:00Z", "checkDate": "2025-01-31T00:00:00Z", "checkCount": 1, "id": "840312bd-eb10-4111-956d-3f335eff7a1e", "companyId": "00124714", "type": "payperiod", "_rid": "NmJkAKiCbEcOUAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcOUAEAAAAAAA==/", "_etag": "\"9e00a35c-0000-0100-0000-686ffc6b0000\"", "_attachments": "attachments/", "_ts": 1752169579}, {"payPeriodId": "1020051223846182", "status": "COMPLETED", "description": "correction", "startDate": "2025-01-23T00:00:00Z", "endDate": "2025-01-29T00:00:00Z", "submitByDate": "2025-01-30T00:00:00Z", "checkDate": "2025-01-31T00:00:00Z", "checkCount": 1, "id": "64b70178-206b-425a-8405-7dee0eb29dac", "companyId": "00124714", "type": "payperiod", "_rid": "NmJkAKiCbEcPUAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcPUAEAAAAAAA==/", "_etag": "\"9e00a55c-0000-0100-0000-686ffc6b0000\"", "_attachments": "attachments/", "_ts": 1752169579}, {"payPeriodId": "1020050433244788", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-01-30T00:00:00Z", "endDate": "2025-02-05T00:00:00Z", "submitByDate": "2025-02-05T00:00:00Z", "checkDate": "2025-02-07T00:00:00Z", "checkCount": 28, "id": "9c0d2e76-fbf3-4c55-a419-38e4fc69e216", "companyId": "00124714", "type": "payperiod", "_rid": "NmJkAKiCbEcQUAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcQUAEAAAAAAA==/", "_etag": "\"9e00ab5c-0000-0100-0000-686ffc6b0000\"", "_attachments": "attachments/", "_ts": 1752169579}, {"payPeriodId": "1020051340017568", "status": "COMPLETED", "description": "Reverse", "startDate": "2025-02-06T00:00:00Z", "endDate": "2025-02-12T00:00:00Z", "submitByDate": "2025-02-13T00:00:00Z", "checkDate": "2025-02-14T00:00:00Z", "checkCount": 18, "originalPayPeriodID": "1020050493312591", "id": "824446ed-f1cc-4395-b497-10ad92310043", "companyId": "00124714", "type": "payperiod", "_rid": "NmJkAKiCbEcRUAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcRUAEAAAAAAA==/", "_etag": "\"9e00af5c-0000-0100-0000-686ffc6b0000\"", "_attachments": "attachments/", "_ts": 1752169579}, {"payPeriodId": "1020051340017962", "status": "COMPLETED", "description": "Payroll", "startDate": "2025-02-06T00:00:00Z", "endDate": "2025-02-12T00:00:00Z", "submitByDate": "2025-02-13T00:00:00Z", "checkDate": "2025-02-14T00:00:00Z", "checkCount": 28, "id": "76391c9c-9abc-418f-aa89-8c94e34fafdc", "companyId": "00124714", "type": "payperiod", "_rid": "NmJkAKiCbEcSUAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcSUAEAAAAAAA==/", "_etag": "\"9e00b15c-0000-0100-0000-686ffc6b0000\"", "_attachments": "attachments/", "_ts": 1752169579}, {"payPeriodId": "1020050553568829", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-02-13T00:00:00Z", "endDate": "2025-02-19T00:00:00Z", "submitByDate": "2025-02-19T00:00:00Z", "checkDate": "2025-02-21T00:00:00Z", "checkCount": 28, "id": "3a154b74-f8c0-44d4-8d61-e2f223e89aa2", "companyId": "00124714", "type": "payperiod", "_rid": "NmJkAKiCbEcTUAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcTUAEAAAAAAA==/", "_etag": "\"9e00b45c-0000-0100-0000-686ffc6c0000\"", "_attachments": "attachments/", "_ts": 1752169580}, {"payPeriodId": "1020050611310415", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-02-20T00:00:00Z", "endDate": "2025-02-26T00:00:00Z", "submitByDate": "2025-02-26T00:00:00Z", "checkDate": "2025-02-28T00:00:00Z", "checkCount": 28, "id": "cbd0fcb2-6f0b-4a71-b084-47d78d77be6d", "companyId": "00124714", "type": "payperiod", "_rid": "NmJkAKiCbEcUUAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcUUAEAAAAAAA==/", "_etag": "\"9e00b85c-0000-0100-0000-686ffc6c0000\"", "_attachments": "attachments/", "_ts": 1752169580}, {"payPeriodId": "1020050672307040", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-02-27T00:00:00Z", "endDate": "2025-03-05T00:00:00Z", "submitByDate": "2025-03-05T00:00:00Z", "checkDate": "2025-03-07T00:00:00Z", "checkCount": 28, "id": "92269522-f4ac-45bb-a751-0ffdf5030c24", "companyId": "00124714", "type": "payperiod", "_rid": "NmJkAKiCbEcVUAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcVUAEAAAAAAA==/", "_etag": "\"9e00bb5c-0000-0100-0000-686ffc6c0000\"", "_attachments": "attachments/", "_ts": 1752169580}, {"payPeriodId": "1020050737626005", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-03-06T00:00:00Z", "endDate": "2025-03-12T00:00:00Z", "submitByDate": "2025-03-12T00:00:00Z", "checkDate": "2025-03-14T00:00:00Z", "checkCount": 28, "id": "96c0c549-b232-46e8-87a8-7701a822c399", "companyId": "00124714", "type": "payperiod", "_rid": "NmJkAKiCbEcWUAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcWUAEAAAAAAA==/", "_etag": "\"9e00bc5c-0000-0100-0000-686ffc6c0000\"", "_attachments": "attachments/", "_ts": 1752169580}, {"payPeriodId": "1020050809511318", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-03-13T00:00:00Z", "endDate": "2025-03-19T00:00:00Z", "submitByDate": "2025-03-19T00:00:00Z", "checkDate": "2025-03-21T00:00:00Z", "checkCount": 28, "id": "51492745-6940-469b-a568-a218eb8fe96a", "companyId": "00124714", "type": "payperiod", "_rid": "NmJkAKiCbEcXUAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcXUAEAAAAAAA==/", "_etag": "\"9e00bf5c-0000-0100-0000-686ffc6c0000\"", "_attachments": "attachments/", "_ts": 1752169580}, {"payPeriodId": "1020050876081578", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-03-20T00:00:00Z", "endDate": "2025-03-26T00:00:00Z", "submitByDate": "2025-03-26T00:00:00Z", "checkDate": "2025-03-28T00:00:00Z", "checkCount": 29, "id": "f2e8c54b-a914-4cc9-b757-5d3eab98fb17", "companyId": "00124714", "type": "payperiod", "_rid": "NmJkAKiCbEcYUAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcYUAEAAAAAAA==/", "_etag": "\"9e00c25c-0000-0100-0000-686ffc6c0000\"", "_attachments": "attachments/", "_ts": 1752169580}, {"payPeriodId": "1020050939298015", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-03-27T00:00:00Z", "endDate": "2025-04-02T00:00:00Z", "submitByDate": "2025-04-02T00:00:00Z", "checkDate": "2025-04-04T00:00:00Z", "checkCount": 0, "id": "4fd20d19-8a3f-4ea3-b0b5-93fc085b3a50", "companyId": "00124714", "type": "payperiod", "_rid": "NmJkAKiCbEcZUAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcZUAEAAAAAAA==/", "_etag": "\"9e00c75c-0000-0100-0000-686ffc6c0000\"", "_attachments": "attachments/", "_ts": 1752169580}, {"payPeriodId": "1020050999831923", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-04-03T00:00:00Z", "endDate": "2025-04-09T00:00:00Z", "submitByDate": "2025-04-09T00:00:00Z", "checkDate": "2025-04-11T00:00:00Z", "checkCount": 0, "id": "4a46b717-f9ff-4c13-b272-b7e6853d2fd3", "companyId": "00124714", "type": "payperiod", "_rid": "NmJkAKiCbEcaUAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcaUAEAAAAAAA==/", "_etag": "\"9e00c95c-0000-0100-0000-686ffc6c0000\"", "_attachments": "attachments/", "_ts": 1752169580}, {"payPeriodId": "1020051056559877", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-04-10T00:00:00Z", "endDate": "2025-04-16T00:00:00Z", "submitByDate": "2025-04-16T00:00:00Z", "checkDate": "2025-04-18T00:00:00Z", "checkCount": 0, "id": "bc6a3622-9c62-4f0e-8955-c0b1d164d60a", "companyId": "00124714", "type": "payperiod", "_rid": "NmJkAKiCbEcbUAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcbUAEAAAAAAA==/", "_etag": "\"9e00ca5c-0000-0100-0000-686ffc6c0000\"", "_attachments": "attachments/", "_ts": 1752169580}, {"payPeriodId": "1020051118300633", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-04-17T00:00:00Z", "endDate": "2025-04-23T00:00:00Z", "submitByDate": "2025-04-23T00:00:00Z", "checkDate": "2025-04-25T00:00:00Z", "checkCount": 0, "id": "01b29387-c3fc-420a-8e5a-9f246abf619f", "companyId": "00124714", "type": "payperiod", "_rid": "NmJkAKiCbEccUAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEccUAEAAAAAAA==/", "_etag": "\"9e00cc5c-0000-0100-0000-686ffc6c0000\"", "_attachments": "attachments/", "_ts": 1752169580}, {"payPeriodId": "1020051169090619", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-04-24T00:00:00Z", "endDate": "2025-04-30T00:00:00Z", "submitByDate": "2025-04-30T00:00:00Z", "checkDate": "2025-05-02T00:00:00Z", "checkCount": 0, "id": "5a8b6562-6730-4f7c-ab95-5b44b095ee3d", "companyId": "00124714", "type": "payperiod", "_rid": "NmJkAKiCbEcdUAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcdUAEAAAAAAA==/", "_etag": "\"9e00cd5c-0000-0100-0000-686ffc6c0000\"", "_attachments": "attachments/", "_ts": 1752169580}, {"payPeriodId": "1020051232084028", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-05-01T00:00:00Z", "endDate": "2025-05-07T00:00:00Z", "submitByDate": "2025-05-07T00:00:00Z", "checkDate": "2025-05-09T00:00:00Z", "checkCount": 0, "id": "a0fad8b8-ee29-4c07-a906-a797da2c87bf", "companyId": "00124714", "type": "payperiod", "_rid": "NmJkAKiCbEceUAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEceUAEAAAAAAA==/", "_etag": "\"9e00d15c-0000-0100-0000-686ffc6c0000\"", "_attachments": "attachments/", "_ts": 1752169580}, {"payPeriodId": "1020051284632311", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-05-08T00:00:00Z", "endDate": "2025-05-14T00:00:00Z", "submitByDate": "2025-05-14T00:00:00Z", "checkDate": "2025-05-16T00:00:00Z", "checkCount": 0, "id": "8cbf84f1-a389-4692-916b-2d41d95504de", "companyId": "00124714", "type": "payperiod", "_rid": "NmJkAKiCbEcfUAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcfUAEAAAAAAA==/", "_etag": "\"9e00d45c-0000-0100-0000-686ffc6c0000\"", "_attachments": "attachments/", "_ts": 1752169580}, {"payPeriodId": "1020051345400467", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-05-15T00:00:00Z", "endDate": "2025-05-21T00:00:00Z", "submitByDate": "2025-05-21T00:00:00Z", "checkDate": "2025-05-23T00:00:00Z", "checkCount": 0, "id": "8eea9655-e8ae-4259-887d-82d43419e18e", "companyId": "00124714", "type": "payperiod", "_rid": "NmJkAKiCbEcgUAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcgUAEAAAAAAA==/", "_etag": "\"9e00d65c-0000-0100-0000-686ffc6d0000\"", "_attachments": "attachments/", "_ts": 1752169581}, {"payPeriodId": "1020051397078881", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-05-22T00:00:00Z", "endDate": "2025-05-28T00:00:00Z", "submitByDate": "2025-05-28T00:00:00Z", "checkDate": "2025-05-30T00:00:00Z", "checkCount": 0, "id": "043b69fc-3def-444e-b6d8-d595bf4a1c15", "companyId": "00124714", "type": "payperiod", "_rid": "NmJkAKiCbEchUAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEchUAEAAAAAAA==/", "_etag": "\"9e00d95c-0000-0100-0000-686ffc6d0000\"", "_attachments": "attachments/", "_ts": 1752169581}, {"payPeriodId": "1020051459777814", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-05-29T00:00:00Z", "endDate": "2025-06-04T00:00:00Z", "submitByDate": "2025-06-04T00:00:00Z", "checkDate": "2025-06-06T00:00:00Z", "checkCount": 0, "id": "f1a81588-6b21-4896-9c67-b08a5049cf19", "companyId": "00124714", "type": "payperiod", "_rid": "NmJkAKiCbEciUAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEciUAEAAAAAAA==/", "_etag": "\"9e00dc5c-0000-0100-0000-686ffc6d0000\"", "_attachments": "attachments/", "_ts": 1752169581}, {"payPeriodId": "1020051504643784", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-06-05T00:00:00Z", "endDate": "2025-06-11T00:00:00Z", "submitByDate": "2025-06-11T00:00:00Z", "checkDate": "2025-06-13T00:00:00Z", "checkCount": 0, "id": "84c95ab7-0928-41dc-9c97-925efb265c86", "companyId": "00124714", "type": "payperiod", "_rid": "NmJkAKiCbEcjUAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcjUAEAAAAAAA==/", "_etag": "\"9e00de5c-0000-0100-0000-686ffc6d0000\"", "_attachments": "attachments/", "_ts": 1752169581}, {"payPeriodId": "1020051563376655", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-06-12T00:00:00Z", "endDate": "2025-06-18T00:00:00Z", "submitByDate": "2025-06-18T00:00:00Z", "checkDate": "2025-06-20T00:00:00Z", "checkCount": 0, "id": "6b594df3-97a6-4a6b-ac75-216dd441ba48", "companyId": "00124714", "type": "payperiod", "_rid": "NmJkAKiCbEckUAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEckUAEAAAAAAA==/", "_etag": "\"9e00e15c-0000-0100-0000-686ffc6d0000\"", "_attachments": "attachments/", "_ts": 1752169581}, {"payPeriodId": "1020051617606794", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-06-19T00:00:00Z", "endDate": "2025-06-25T00:00:00Z", "submitByDate": "2025-06-25T00:00:00Z", "checkDate": "2025-06-27T00:00:00Z", "checkCount": 0, "id": "70c96c55-622f-45be-92ee-4f3a816ecdf0", "companyId": "00124714", "type": "payperiod", "_rid": "NmJkAKiCbEclUAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEclUAEAAAAAAA==/", "_etag": "\"9e00e35c-0000-0100-0000-686ffc6d0000\"", "_attachments": "attachments/", "_ts": 1752169581}, {"payPeriodId": "1020051676831472", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-06-26T00:00:00Z", "endDate": "2025-07-02T00:00:00Z", "submitByDate": "2025-07-02T00:00:00Z", "checkDate": "2025-07-04T00:00:00Z", "checkCount": 0, "id": "0c379aa3-5c37-4e6f-9df7-41d7480874ff", "companyId": "00124714", "type": "payperiod", "_rid": "NmJkAKiCbEcmUAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcmUAEAAAAAAA==/", "_etag": "\"9e00e75c-0000-0100-0000-686ffc6d0000\"", "_attachments": "attachments/", "_ts": 1752169581}, {"payPeriodId": "1020051741619653", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-07-03T00:00:00Z", "endDate": "2025-07-09T00:00:00Z", "submitByDate": "2025-07-09T00:00:00Z", "checkDate": "2025-07-11T00:00:00Z", "checkCount": 0, "id": "df1eda12-912e-4f86-8d0f-7258faf3db6e", "companyId": "00124714", "type": "payperiod", "_rid": "NmJkAKiCbEcnUAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcnUAEAAAAAAA==/", "_etag": "\"9e00e85c-0000-0100-0000-686ffc6d0000\"", "_attachments": "attachments/", "_ts": 1752169581}, {"payPeriodId": "1020051802286883", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-07-10T00:00:00Z", "endDate": "2025-07-16T00:00:00Z", "submitByDate": "2025-07-16T00:00:00Z", "checkDate": "2025-07-18T00:00:00Z", "checkCount": 0, "id": "0c113d40-4423-4097-92b9-eaa6645c6b66", "companyId": "00124714", "type": "payperiod", "_rid": "NmJkAKiCbEcoUAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcoUAEAAAAAAA==/", "_etag": "\"9e00e95c-0000-0100-0000-686ffc6d0000\"", "_attachments": "attachments/", "_ts": 1752169581}, {"payPeriodId": "1020051868807776", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-07-17T00:00:00Z", "endDate": "2025-07-23T00:00:00Z", "submitByDate": "2025-07-23T00:00:00Z", "checkDate": "2025-07-25T00:00:00Z", "checkCount": 0, "id": "6460a4e2-8eaf-44ec-afdc-61526b1e69fd", "companyId": "00124714", "type": "payperiod", "_rid": "NmJkAKiCbEcpUAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcpUAEAAAAAAA==/", "_etag": "\"9e00ec5c-0000-0100-0000-686ffc6d0000\"", "_attachments": "attachments/", "_ts": 1752169581}, {"payPeriodId": "1020051926678374", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-07-24T00:00:00Z", "endDate": "2025-07-30T00:00:00Z", "submitByDate": "2025-07-30T00:00:00Z", "checkDate": "2025-08-01T00:00:00Z", "checkCount": 0, "id": "06b32595-6e29-442b-8187-57de6a740048", "companyId": "00124714", "type": "payperiod", "_rid": "NmJkAKiCbEcqUAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcqUAEAAAAAAA==/", "_etag": "\"9e00ed5c-0000-0100-0000-686ffc6d0000\"", "_attachments": "attachments/", "_ts": 1752169581}, {"payPeriodId": "1020051992092657", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-07-31T00:00:00Z", "endDate": "2025-08-06T00:00:00Z", "submitByDate": "2025-08-06T00:00:00Z", "checkDate": "2025-08-08T00:00:00Z", "checkCount": 0, "id": "aa66555b-6634-4741-ada4-02a38cf5ec0a", "companyId": "00124714", "type": "payperiod", "_rid": "NmJkAKiCbEcrUAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcrUAEAAAAAAA==/", "_etag": "\"9e00ee5c-0000-0100-0000-686ffc6d0000\"", "_attachments": "attachments/", "_ts": 1752169581}, {"payPeriodId": "1020052051065529", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-08-07T00:00:00Z", "endDate": "2025-08-13T00:00:00Z", "submitByDate": "2025-08-13T00:00:00Z", "checkDate": "2025-08-15T00:00:00Z", "checkCount": 0, "id": "d8b29378-f9d5-46a9-8b05-5c8d9663651f", "companyId": "00124714", "type": "payperiod", "_rid": "NmJkAKiCbEcsUAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcsUAEAAAAAAA==/", "_etag": "\"9e00f05c-0000-0100-0000-686ffc6d0000\"", "_attachments": "attachments/", "_ts": 1752169581}, {"payPeriodId": "1020052113855717", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-08-14T00:00:00Z", "endDate": "2025-08-20T00:00:00Z", "submitByDate": "2025-08-20T00:00:00Z", "checkDate": "2025-08-22T00:00:00Z", "checkCount": 0, "id": "15be7e00-540a-4f7d-9ba5-435f1f985b6b", "companyId": "00124714", "type": "payperiod", "_rid": "NmJkAKiCbEctUAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEctUAEAAAAAAA==/", "_etag": "\"9e00f15c-0000-0100-0000-686ffc6e0000\"", "_attachments": "attachments/", "_ts": 1752169582}, {"payPeriodId": "1020052169817032", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-08-21T00:00:00Z", "endDate": "2025-08-27T00:00:00Z", "submitByDate": "2025-08-27T00:00:00Z", "checkDate": "2025-08-29T00:00:00Z", "checkCount": 0, "id": "2e1fed82-cec4-480e-ba82-b7253490a6c2", "companyId": "00124714", "type": "payperiod", "_rid": "NmJkAKiCbEcuUAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcuUAEAAAAAAA==/", "_etag": "\"9e00f65c-0000-0100-0000-686ffc6e0000\"", "_attachments": "attachments/", "_ts": 1752169582}, {"payPeriodId": "1020052230237121", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-08-28T00:00:00Z", "endDate": "2025-09-03T00:00:00Z", "submitByDate": "2025-09-03T00:00:00Z", "checkDate": "2025-09-05T00:00:00Z", "checkCount": 0, "id": "ee6ebebe-65d2-4095-bd80-741680bee240", "companyId": "00124714", "type": "payperiod", "_rid": "NmJkAKiCbEcvUAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcvUAEAAAAAAA==/", "_etag": "\"9e00f95c-0000-0100-0000-686ffc6e0000\"", "_attachments": "attachments/", "_ts": 1752169582}, {"payPeriodId": "1020052289959859", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-09-04T00:00:00Z", "endDate": "2025-09-10T00:00:00Z", "submitByDate": "2025-09-10T00:00:00Z", "checkDate": "2025-09-12T00:00:00Z", "checkCount": 0, "id": "04d75e41-441a-4f2e-96c5-ddde2962ebbf", "companyId": "00124714", "type": "payperiod", "_rid": "NmJkAKiCbEcwUAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcwUAEAAAAAAA==/", "_etag": "\"9e00fa5c-0000-0100-0000-686ffc6e0000\"", "_attachments": "attachments/", "_ts": 1752169582}, {"payPeriodId": "1020052350668009", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-09-11T00:00:00Z", "endDate": "2025-09-17T00:00:00Z", "submitByDate": "2025-09-17T00:00:00Z", "checkDate": "2025-09-19T00:00:00Z", "checkCount": 0, "id": "0c8c137b-4313-4b4c-a6d1-ed425f835c0f", "companyId": "00124714", "type": "payperiod", "_rid": "NmJkAKiCbEcxUAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcxUAEAAAAAAA==/", "_etag": "\"9e00fc5c-0000-0100-0000-686ffc6e0000\"", "_attachments": "attachments/", "_ts": 1752169582}, {"payPeriodId": "1020052402312519", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-09-18T00:00:00Z", "endDate": "2025-09-24T00:00:00Z", "submitByDate": "2025-09-24T00:00:00Z", "checkDate": "2025-09-26T00:00:00Z", "checkCount": 0, "id": "1cd66397-a5f7-4fe5-8896-6713d3d63be8", "companyId": "00124714", "type": "payperiod", "_rid": "NmJkAKiCbEcyUAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcyUAEAAAAAAA==/", "_etag": "\"9e00005d-0000-0100-0000-686ffc6e0000\"", "_attachments": "attachments/", "_ts": 1752169582}, {"payPeriodId": "1020052470777823", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-09-25T00:00:00Z", "endDate": "2025-10-01T00:00:00Z", "submitByDate": "2025-10-01T00:00:00Z", "checkDate": "2025-10-03T00:00:00Z", "checkCount": 0, "id": "524cf52e-42f7-4345-9e3c-e63820639c33", "companyId": "00124714", "type": "payperiod", "_rid": "NmJkAKiCbEczUAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEczUAEAAAAAAA==/", "_etag": "\"9e00045d-0000-0100-0000-686ffc6e0000\"", "_attachments": "attachments/", "_ts": 1752169582}, {"payPeriodId": "1020052536471687", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-10-02T00:00:00Z", "endDate": "2025-10-08T00:00:00Z", "submitByDate": "2025-10-08T00:00:00Z", "checkDate": "2025-10-10T00:00:00Z", "checkCount": 0, "id": "db6eb1bf-2c0b-44e4-8b9f-b88086a4229f", "companyId": "00124714", "type": "payperiod", "_rid": "NmJkAKiCbEc0UAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc0UAEAAAAAAA==/", "_etag": "\"9e00095d-0000-0100-0000-686ffc6e0000\"", "_attachments": "attachments/", "_ts": 1752169582}, {"payPeriodId": "1020050493312591", "intervalCode": "WEEKLY", "status": "REVERSED", "description": "Weekly Payroll (1)", "startDate": "2025-02-06T00:00:00Z", "endDate": "2025-02-12T00:00:00Z", "submitByDate": "2025-02-12T00:00:00Z", "checkDate": "2025-02-14T00:00:00Z", "checkCount": 18, "id": "eaa4eba8-ca8c-4ea8-92d3-70dc2d0af0d0", "companyId": "00124714", "type": "payperiod", "_rid": "NmJkAKiCbEc1UAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc1UAEAAAAAAA==/", "_etag": "\"9e000b5d-0000-0100-0000-686ffc6e0000\"", "_attachments": "attachments/", "_ts": 1752169582}, {"payPeriodId": "1020050118164640", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2024-12-26T00:00:00Z", "endDate": "2025-01-01T00:00:00Z", "submitByDate": "2024-12-31T00:00:00Z", "checkDate": "2025-01-03T00:00:00Z", "checkCount": 26, "id": "6b06a2f8-2efb-497f-9b7d-120843695dcc", "companyId": "00124714", "type": "payperiod", "_rid": "NmJkAKiCbEf8UAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf8UAEAAAAAAA==/", "_etag": "\"9e00a45f-0000-0100-0000-686ffc800000\"", "_attachments": "attachments/", "_ts": 1752169600}, {"payPeriodId": "1020050179417984", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-01-02T00:00:00Z", "endDate": "2025-01-08T00:00:00Z", "submitByDate": "2025-01-08T00:00:00Z", "checkDate": "2025-01-10T00:00:00Z", "checkCount": 28, "id": "6e46d305-63df-4d42-9e36-4543fe3bebcc", "companyId": "00124714", "type": "payperiod", "_rid": "NmJkAKiCbEf9UAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf9UAEAAAAAAA==/", "_etag": "\"9e00a75f-0000-0100-0000-686ffc800000\"", "_attachments": "attachments/", "_ts": 1752169600}, {"payPeriodId": "1020050239948857", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-01-09T00:00:00Z", "endDate": "2025-01-15T00:00:00Z", "submitByDate": "2025-01-15T00:00:00Z", "checkDate": "2025-01-17T00:00:00Z", "checkCount": 28, "id": "d7467838-d6d2-46b9-b7d7-af6c3d602f28", "companyId": "00124714", "type": "payperiod", "_rid": "NmJkAKiCbEf+UAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf+UAEAAAAAAA==/", "_etag": "\"9e00aa5f-0000-0100-0000-686ffc800000\"", "_attachments": "attachments/", "_ts": 1752169600}, {"payPeriodId": "1020050300187219", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-01-16T00:00:00Z", "endDate": "2025-01-22T00:00:00Z", "submitByDate": "2025-01-22T00:00:00Z", "checkDate": "2025-01-24T00:00:00Z", "checkCount": 28, "id": "99f0ad27-5d39-4c31-b360-19ae6dfc14de", "companyId": "00124714", "type": "payperiod", "_rid": "NmJkAKiCbEf-UAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf-UAEAAAAAAA==/", "_etag": "\"9e00ad5f-0000-0100-0000-686ffc800000\"", "_attachments": "attachments/", "_ts": 1752169600}, {"payPeriodId": "1020050368003729", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-01-23T00:00:00Z", "endDate": "2025-01-29T00:00:00Z", "submitByDate": "2025-01-29T00:00:00Z", "checkDate": "2025-01-31T00:00:00Z", "checkCount": 28, "id": "ccf422df-742e-4cd3-a426-d0f2d248a6e3", "companyId": "00124714", "type": "payperiod", "_rid": "NmJkAKiCbEcAUQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcAUQEAAAAAAA==/", "_etag": "\"9e00af5f-0000-0100-0000-686ffc800000\"", "_attachments": "attachments/", "_ts": 1752169600}, {"payPeriodId": "1020051223764195", "status": "COMPLETED", "description": "errors", "startDate": "2025-01-23T00:00:00Z", "endDate": "2025-01-29T00:00:00Z", "submitByDate": "2025-01-30T00:00:00Z", "checkDate": "2025-01-31T00:00:00Z", "checkCount": 1, "id": "c3e1140d-710f-4a18-900b-d7a6584f4755", "companyId": "00124714", "type": "payperiod", "_rid": "NmJkAKiCbEcBUQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcBUQEAAAAAAA==/", "_etag": "\"9e00b55f-0000-0100-0000-686ffc800000\"", "_attachments": "attachments/", "_ts": 1752169600}, {"payPeriodId": "1020051223846182", "status": "COMPLETED", "description": "correction", "startDate": "2025-01-23T00:00:00Z", "endDate": "2025-01-29T00:00:00Z", "submitByDate": "2025-01-30T00:00:00Z", "checkDate": "2025-01-31T00:00:00Z", "checkCount": 1, "id": "957791f0-1f5f-4563-8f20-0a72cb86bc1c", "companyId": "00124714", "type": "payperiod", "_rid": "NmJkAKiCbEcCUQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcCUQEAAAAAAA==/", "_etag": "\"9e00b95f-0000-0100-0000-686ffc800000\"", "_attachments": "attachments/", "_ts": 1752169600}, {"payPeriodId": "1020050433244788", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-01-30T00:00:00Z", "endDate": "2025-02-05T00:00:00Z", "submitByDate": "2025-02-05T00:00:00Z", "checkDate": "2025-02-07T00:00:00Z", "checkCount": 28, "id": "487cdd72-0b85-451e-98dd-97cf9cbfd8a7", "companyId": "00124714", "type": "payperiod", "_rid": "NmJkAKiCbEcDUQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcDUQEAAAAAAA==/", "_etag": "\"9e00bb5f-0000-0100-0000-686ffc800000\"", "_attachments": "attachments/", "_ts": 1752169600}, {"payPeriodId": "1020051340017568", "status": "COMPLETED", "description": "Reverse", "startDate": "2025-02-06T00:00:00Z", "endDate": "2025-02-12T00:00:00Z", "submitByDate": "2025-02-13T00:00:00Z", "checkDate": "2025-02-14T00:00:00Z", "checkCount": 18, "originalPayPeriodID": "1020050493312591", "id": "199fa561-9b69-4459-9f6e-6c2200c194d7", "companyId": "00124714", "type": "payperiod", "_rid": "NmJkAKiCbEcEUQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcEUQEAAAAAAA==/", "_etag": "\"9e00bd5f-0000-0100-0000-686ffc810000\"", "_attachments": "attachments/", "_ts": 1752169601}, {"payPeriodId": "1020051340017962", "status": "COMPLETED", "description": "Payroll", "startDate": "2025-02-06T00:00:00Z", "endDate": "2025-02-12T00:00:00Z", "submitByDate": "2025-02-13T00:00:00Z", "checkDate": "2025-02-14T00:00:00Z", "checkCount": 28, "id": "ab53d137-3866-4df8-b661-989b6acb0793", "companyId": "00124714", "type": "payperiod", "_rid": "NmJkAKiCbEcFUQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcFUQEAAAAAAA==/", "_etag": "\"9e00be5f-0000-0100-0000-686ffc810000\"", "_attachments": "attachments/", "_ts": 1752169601}, {"payPeriodId": "1020050553568829", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-02-13T00:00:00Z", "endDate": "2025-02-19T00:00:00Z", "submitByDate": "2025-02-19T00:00:00Z", "checkDate": "2025-02-21T00:00:00Z", "checkCount": 28, "id": "a3ca3e35-b845-4ad0-b56b-1b58f046b5da", "companyId": "00124714", "type": "payperiod", "_rid": "NmJkAKiCbEcGUQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcGUQEAAAAAAA==/", "_etag": "\"9e00c25f-0000-0100-0000-686ffc810000\"", "_attachments": "attachments/", "_ts": 1752169601}, {"payPeriodId": "1020050611310415", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-02-20T00:00:00Z", "endDate": "2025-02-26T00:00:00Z", "submitByDate": "2025-02-26T00:00:00Z", "checkDate": "2025-02-28T00:00:00Z", "checkCount": 28, "id": "09449ee0-bd33-4a5f-8a59-39ae718341d2", "companyId": "00124714", "type": "payperiod", "_rid": "NmJkAKiCbEcHUQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcHUQEAAAAAAA==/", "_etag": "\"9e00c35f-0000-0100-0000-686ffc810000\"", "_attachments": "attachments/", "_ts": 1752169601}, {"payPeriodId": "1020050672307040", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-02-27T00:00:00Z", "endDate": "2025-03-05T00:00:00Z", "submitByDate": "2025-03-05T00:00:00Z", "checkDate": "2025-03-07T00:00:00Z", "checkCount": 28, "id": "cdbaf5a8-6a7e-4870-b166-275be3cf8d00", "companyId": "00124714", "type": "payperiod", "_rid": "NmJkAKiCbEcIUQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcIUQEAAAAAAA==/", "_etag": "\"9e00c45f-0000-0100-0000-686ffc810000\"", "_attachments": "attachments/", "_ts": 1752169601}, {"payPeriodId": "1020050737626005", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-03-06T00:00:00Z", "endDate": "2025-03-12T00:00:00Z", "submitByDate": "2025-03-12T00:00:00Z", "checkDate": "2025-03-14T00:00:00Z", "checkCount": 28, "id": "2356190d-5365-484a-8d5e-5cc8e8586b0d", "companyId": "00124714", "type": "payperiod", "_rid": "NmJkAKiCbEcJUQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcJUQEAAAAAAA==/", "_etag": "\"9e00c55f-0000-0100-0000-686ffc810000\"", "_attachments": "attachments/", "_ts": 1752169601}, {"payPeriodId": "1020050809511318", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-03-13T00:00:00Z", "endDate": "2025-03-19T00:00:00Z", "submitByDate": "2025-03-19T00:00:00Z", "checkDate": "2025-03-21T00:00:00Z", "checkCount": 28, "id": "23b42180-88d9-4691-bab6-3fe4f9799ccf", "companyId": "00124714", "type": "payperiod", "_rid": "NmJkAKiCbEcKUQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcKUQEAAAAAAA==/", "_etag": "\"9e00c85f-0000-0100-0000-686ffc810000\"", "_attachments": "attachments/", "_ts": 1752169601}, {"payPeriodId": "1020050876081578", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-03-20T00:00:00Z", "endDate": "2025-03-26T00:00:00Z", "submitByDate": "2025-03-26T00:00:00Z", "checkDate": "2025-03-28T00:00:00Z", "checkCount": 29, "id": "39a6d91e-9581-4f27-a011-2ce4f72c0bfc", "companyId": "00124714", "type": "payperiod", "_rid": "NmJkAKiCbEcLUQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcLUQEAAAAAAA==/", "_etag": "\"9e00c95f-0000-0100-0000-686ffc810000\"", "_attachments": "attachments/", "_ts": 1752169601}, {"payPeriodId": "1020050939298015", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-03-27T00:00:00Z", "endDate": "2025-04-02T00:00:00Z", "submitByDate": "2025-04-02T00:00:00Z", "checkDate": "2025-04-04T00:00:00Z", "checkCount": 29, "id": "ce997c0d-c680-41df-a9ee-65af30ca3fc4", "companyId": "00124714", "type": "payperiod", "_rid": "NmJkAKiCbEcMUQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcMUQEAAAAAAA==/", "_etag": "\"9e00cb5f-0000-0100-0000-686ffc810000\"", "_attachments": "attachments/", "_ts": 1752169601}, {"payPeriodId": "1020050999831923", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-04-03T00:00:00Z", "endDate": "2025-04-09T00:00:00Z", "submitByDate": "2025-04-09T00:00:00Z", "checkDate": "2025-04-11T00:00:00Z", "checkCount": 29, "id": "cf846084-8af0-4380-b284-de482d615816", "companyId": "00124714", "type": "payperiod", "_rid": "NmJkAKiCbEcNUQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcNUQEAAAAAAA==/", "_etag": "\"9e00d35f-0000-0100-0000-686ffc810000\"", "_attachments": "attachments/", "_ts": 1752169601}, {"payPeriodId": "1020051056559877", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-04-10T00:00:00Z", "endDate": "2025-04-16T00:00:00Z", "submitByDate": "2025-04-16T00:00:00Z", "checkDate": "2025-04-18T00:00:00Z", "checkCount": 28, "id": "3cb29839-645b-4096-b632-429d8d8432b2", "companyId": "00124714", "type": "payperiod", "_rid": "NmJkAKiCbEcOUQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcOUQEAAAAAAA==/", "_etag": "\"9e00d55f-0000-0100-0000-686ffc810000\"", "_attachments": "attachments/", "_ts": 1752169601}, {"payPeriodId": "1020051118300633", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-04-17T00:00:00Z", "endDate": "2025-04-23T00:00:00Z", "submitByDate": "2025-04-23T00:00:00Z", "checkDate": "2025-04-25T00:00:00Z", "checkCount": 28, "id": "509af482-0e4d-4d9d-8f42-6c01d6866388", "companyId": "00124714", "type": "payperiod", "_rid": "NmJkAKiCbEcPUQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcPUQEAAAAAAA==/", "_etag": "\"9e00d75f-0000-0100-0000-686ffc810000\"", "_attachments": "attachments/", "_ts": 1752169601}, {"payPeriodId": "1020051169090619", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-04-24T00:00:00Z", "endDate": "2025-04-30T00:00:00Z", "submitByDate": "2025-04-30T00:00:00Z", "checkDate": "2025-05-02T00:00:00Z", "checkCount": 27, "id": "891c89a7-9ade-4a15-8f42-811710854c18", "companyId": "00124714", "type": "payperiod", "_rid": "NmJkAKiCbEcQUQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcQUQEAAAAAAA==/", "_etag": "\"9e00d85f-0000-0100-0000-686ffc810000\"", "_attachments": "attachments/", "_ts": 1752169601}, {"payPeriodId": "1020051232084028", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-05-01T00:00:00Z", "endDate": "2025-05-07T00:00:00Z", "submitByDate": "2025-05-07T00:00:00Z", "checkDate": "2025-05-09T00:00:00Z", "checkCount": 28, "id": "3cd068f4-6464-4caa-9350-c829c17d1186", "companyId": "00124714", "type": "payperiod", "_rid": "NmJkAKiCbEcRUQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcRUQEAAAAAAA==/", "_etag": "\"9e00da5f-0000-0100-0000-686ffc820000\"", "_attachments": "attachments/", "_ts": 1752169602}, {"payPeriodId": "1020051284632311", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-05-08T00:00:00Z", "endDate": "2025-05-14T00:00:00Z", "submitByDate": "2025-05-14T00:00:00Z", "checkDate": "2025-05-16T00:00:00Z", "checkCount": 28, "id": "e3139e09-409e-447b-85cc-c3f79edf5ad5", "companyId": "00124714", "type": "payperiod", "_rid": "NmJkAKiCbEcSUQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcSUQEAAAAAAA==/", "_etag": "\"9e00de5f-0000-0100-0000-686ffc820000\"", "_attachments": "attachments/", "_ts": 1752169602}, {"payPeriodId": "1020051345400467", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-05-15T00:00:00Z", "endDate": "2025-05-21T00:00:00Z", "submitByDate": "2025-05-21T00:00:00Z", "checkDate": "2025-05-23T00:00:00Z", "checkCount": 28, "id": "7918d802-a72a-4407-9f88-24200afbf45b", "companyId": "00124714", "type": "payperiod", "_rid": "NmJkAKiCbEcTUQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcTUQEAAAAAAA==/", "_etag": "\"9e00e05f-0000-0100-0000-686ffc820000\"", "_attachments": "attachments/", "_ts": 1752169602}, {"payPeriodId": "1020051397078881", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-05-22T00:00:00Z", "endDate": "2025-05-28T00:00:00Z", "submitByDate": "2025-05-28T00:00:00Z", "checkDate": "2025-05-30T00:00:00Z", "checkCount": 28, "id": "7c93cb36-4f52-46dd-b350-8c32e250fc9e", "companyId": "00124714", "type": "payperiod", "_rid": "NmJkAKiCbEcUUQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcUUQEAAAAAAA==/", "_etag": "\"9e00e25f-0000-0100-0000-686ffc820000\"", "_attachments": "attachments/", "_ts": 1752169602}, {"payPeriodId": "1020051459777814", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-05-29T00:00:00Z", "endDate": "2025-06-04T00:00:00Z", "submitByDate": "2025-06-04T00:00:00Z", "checkDate": "2025-06-06T00:00:00Z", "checkCount": 28, "id": "3f279e2e-c6ee-4a82-92e9-7bb5d8d033cd", "companyId": "00124714", "type": "payperiod", "_rid": "NmJkAKiCbEcVUQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcVUQEAAAAAAA==/", "_etag": "\"9e00e55f-0000-0100-0000-686ffc820000\"", "_attachments": "attachments/", "_ts": 1752169602}, {"payPeriodId": "1020051504643784", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-06-05T00:00:00Z", "endDate": "2025-06-11T00:00:00Z", "submitByDate": "2025-06-11T00:00:00Z", "checkDate": "2025-06-13T00:00:00Z", "checkCount": 28, "id": "5636cf7c-1e30-4073-a975-5e1140ba2e09", "companyId": "00124714", "type": "payperiod", "_rid": "NmJkAKiCbEcWUQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcWUQEAAAAAAA==/", "_etag": "\"9e00e85f-0000-0100-0000-686ffc820000\"", "_attachments": "attachments/", "_ts": 1752169602}, {"payPeriodId": "1020051563376655", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-06-12T00:00:00Z", "endDate": "2025-06-18T00:00:00Z", "submitByDate": "2025-06-18T00:00:00Z", "checkDate": "2025-06-20T00:00:00Z", "checkCount": 27, "id": "bbd72548-e2c0-4414-9ac4-313fade8a99c", "companyId": "00124714", "type": "payperiod", "_rid": "NmJkAKiCbEcXUQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcXUQEAAAAAAA==/", "_etag": "\"9e00e95f-0000-0100-0000-686ffc820000\"", "_attachments": "attachments/", "_ts": 1752169602}, {"payPeriodId": "1020051617606794", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-06-19T00:00:00Z", "endDate": "2025-06-25T00:00:00Z", "submitByDate": "2025-06-25T00:00:00Z", "checkDate": "2025-06-27T00:00:00Z", "checkCount": 26, "id": "86f0447c-8c93-446d-90c5-696ac4fbfdab", "companyId": "00124714", "type": "payperiod", "_rid": "NmJkAKiCbEcYUQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcYUQEAAAAAAA==/", "_etag": "\"9e00eb5f-0000-0100-0000-686ffc820000\"", "_attachments": "attachments/", "_ts": 1752169602}, {"payPeriodId": "1020051676831472", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-06-26T00:00:00Z", "endDate": "2025-07-02T00:00:00Z", "submitByDate": "2025-07-02T00:00:00Z", "checkDate": "2025-07-04T00:00:00Z", "checkCount": 27, "id": "779db9ca-9d5f-4806-b151-e618f504dd5f", "companyId": "00124714", "type": "payperiod", "_rid": "NmJkAKiCbEcZUQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcZUQEAAAAAAA==/", "_etag": "\"9e00ee5f-0000-0100-0000-686ffc820000\"", "_attachments": "attachments/", "_ts": 1752169602}, {"payPeriodId": "1020051741619653", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-07-03T00:00:00Z", "endDate": "2025-07-09T00:00:00Z", "submitByDate": "2025-07-09T00:00:00Z", "checkDate": "2025-07-11T00:00:00Z", "checkCount": 0, "id": "8d66b370-e440-4315-ab77-ac3131a541d4", "companyId": "00124714", "type": "payperiod", "_rid": "NmJkAKiCbEcaUQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcaUQEAAAAAAA==/", "_etag": "\"9e00f15f-0000-0100-0000-686ffc820000\"", "_attachments": "attachments/", "_ts": 1752169602}, {"payPeriodId": "1020051802286883", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-07-10T00:00:00Z", "endDate": "2025-07-16T00:00:00Z", "submitByDate": "2025-07-16T00:00:00Z", "checkDate": "2025-07-18T00:00:00Z", "checkCount": 0, "id": "b6f7692c-6624-4ba1-bcba-d875f24e64b2", "companyId": "00124714", "type": "payperiod", "_rid": "NmJkAKiCbEcbUQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcbUQEAAAAAAA==/", "_etag": "\"9e00f55f-0000-0100-0000-686ffc820000\"", "_attachments": "attachments/", "_ts": 1752169602}, {"payPeriodId": "1020051868807776", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-07-17T00:00:00Z", "endDate": "2025-07-23T00:00:00Z", "submitByDate": "2025-07-23T00:00:00Z", "checkDate": "2025-07-25T00:00:00Z", "checkCount": 0, "id": "dd28e89a-e3cf-484c-956d-6f8c3e3e6fa5", "companyId": "00124714", "type": "payperiod", "_rid": "NmJkAKiCbEccUQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEccUQEAAAAAAA==/", "_etag": "\"9e00f85f-0000-0100-0000-686ffc820000\"", "_attachments": "attachments/", "_ts": 1752169602}, {"payPeriodId": "1020051926678374", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-07-24T00:00:00Z", "endDate": "2025-07-30T00:00:00Z", "submitByDate": "2025-07-30T00:00:00Z", "checkDate": "2025-08-01T00:00:00Z", "checkCount": 0, "id": "61d6600a-762a-40a1-a1e4-2ce29f2be708", "companyId": "00124714", "type": "payperiod", "_rid": "NmJkAKiCbEcdUQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcdUQEAAAAAAA==/", "_etag": "\"9e00fb5f-0000-0100-0000-686ffc820000\"", "_attachments": "attachments/", "_ts": 1752169602}, {"payPeriodId": "1020051992092657", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-07-31T00:00:00Z", "endDate": "2025-08-06T00:00:00Z", "submitByDate": "2025-08-06T00:00:00Z", "checkDate": "2025-08-08T00:00:00Z", "checkCount": 0, "id": "a6186de2-5962-4ac3-ac2c-e89ea3abecf4", "companyId": "00124714", "type": "payperiod", "_rid": "NmJkAKiCbEceUQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEceUQEAAAAAAA==/", "_etag": "\"9e00ff5f-0000-0100-0000-686ffc830000\"", "_attachments": "attachments/", "_ts": 1752169603}, {"payPeriodId": "1020052051065529", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-08-07T00:00:00Z", "endDate": "2025-08-13T00:00:00Z", "submitByDate": "2025-08-13T00:00:00Z", "checkDate": "2025-08-15T00:00:00Z", "checkCount": 0, "id": "8a3902bf-4658-445e-82c1-d2f277a5a5fe", "companyId": "00124714", "type": "payperiod", "_rid": "NmJkAKiCbEcfUQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcfUQEAAAAAAA==/", "_etag": "\"9e000260-0000-0100-0000-686ffc830000\"", "_attachments": "attachments/", "_ts": 1752169603}, {"payPeriodId": "1020052113855717", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-08-14T00:00:00Z", "endDate": "2025-08-20T00:00:00Z", "submitByDate": "2025-08-20T00:00:00Z", "checkDate": "2025-08-22T00:00:00Z", "checkCount": 0, "id": "138febcc-f434-40bb-a7d4-84505231c80e", "companyId": "00124714", "type": "payperiod", "_rid": "NmJkAKiCbEcgUQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcgUQEAAAAAAA==/", "_etag": "\"9e000460-0000-0100-0000-686ffc830000\"", "_attachments": "attachments/", "_ts": 1752169603}, {"payPeriodId": "1020052169817032", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-08-21T00:00:00Z", "endDate": "2025-08-27T00:00:00Z", "submitByDate": "2025-08-27T00:00:00Z", "checkDate": "2025-08-29T00:00:00Z", "checkCount": 0, "id": "15461f4b-43d0-40f5-96b0-2ab8c6945092", "companyId": "00124714", "type": "payperiod", "_rid": "NmJkAKiCbEchUQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEchUQEAAAAAAA==/", "_etag": "\"9e000660-0000-0100-0000-686ffc830000\"", "_attachments": "attachments/", "_ts": 1752169603}, {"payPeriodId": "1020052230237121", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-08-28T00:00:00Z", "endDate": "2025-09-03T00:00:00Z", "submitByDate": "2025-09-03T00:00:00Z", "checkDate": "2025-09-05T00:00:00Z", "checkCount": 0, "id": "f340ef9c-7f48-4ab7-9ecc-b0f0dcf4dee2", "companyId": "00124714", "type": "payperiod", "_rid": "NmJkAKiCbEciUQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEciUQEAAAAAAA==/", "_etag": "\"9e000a60-0000-0100-0000-686ffc830000\"", "_attachments": "attachments/", "_ts": 1752169603}, {"payPeriodId": "1020052289959859", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-09-04T00:00:00Z", "endDate": "2025-09-10T00:00:00Z", "submitByDate": "2025-09-10T00:00:00Z", "checkDate": "2025-09-12T00:00:00Z", "checkCount": 0, "id": "2bbedb48-c5f3-402f-b62f-ee61ccab7aa8", "companyId": "00124714", "type": "payperiod", "_rid": "NmJkAKiCbEcjUQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcjUQEAAAAAAA==/", "_etag": "\"9e000d60-0000-0100-0000-686ffc830000\"", "_attachments": "attachments/", "_ts": 1752169603}, {"payPeriodId": "1020052350668009", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-09-11T00:00:00Z", "endDate": "2025-09-17T00:00:00Z", "submitByDate": "2025-09-17T00:00:00Z", "checkDate": "2025-09-19T00:00:00Z", "checkCount": 0, "id": "d0270c20-8b97-4c13-9a96-8f0380089970", "companyId": "00124714", "type": "payperiod", "_rid": "NmJkAKiCbEckUQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEckUQEAAAAAAA==/", "_etag": "\"9e001260-0000-0100-0000-686ffc830000\"", "_attachments": "attachments/", "_ts": 1752169603}, {"payPeriodId": "1020052402312519", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-09-18T00:00:00Z", "endDate": "2025-09-24T00:00:00Z", "submitByDate": "2025-09-24T00:00:00Z", "checkDate": "2025-09-26T00:00:00Z", "checkCount": 0, "id": "5f6e61dd-69ce-4f56-bf5c-7cc73fd69265", "companyId": "00124714", "type": "payperiod", "_rid": "NmJkAKiCbEclUQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEclUQEAAAAAAA==/", "_etag": "\"9e001360-0000-0100-0000-686ffc830000\"", "_attachments": "attachments/", "_ts": 1752169603}, {"payPeriodId": "1020052470777823", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-09-25T00:00:00Z", "endDate": "2025-10-01T00:00:00Z", "submitByDate": "2025-10-01T00:00:00Z", "checkDate": "2025-10-03T00:00:00Z", "checkCount": 0, "id": "bc0eca09-4183-4b8d-bbd6-a9978621098b", "companyId": "00124714", "type": "payperiod", "_rid": "NmJkAKiCbEcmUQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcmUQEAAAAAAA==/", "_etag": "\"9e001760-0000-0100-0000-686ffc830000\"", "_attachments": "attachments/", "_ts": 1752169603}, {"payPeriodId": "1020052536471687", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-10-02T00:00:00Z", "endDate": "2025-10-08T00:00:00Z", "submitByDate": "2025-10-08T00:00:00Z", "checkDate": "2025-10-10T00:00:00Z", "checkCount": 0, "id": "23f8c42c-6e4d-469c-a03a-712eb1b21c77", "companyId": "00124714", "type": "payperiod", "_rid": "NmJkAKiCbEcnUQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcnUQEAAAAAAA==/", "_etag": "\"9e001860-0000-0100-0000-686ffc830000\"", "_attachments": "attachments/", "_ts": 1752169603}, {"payPeriodId": "1020050493312591", "intervalCode": "WEEKLY", "status": "REVERSED", "description": "Weekly Payroll (1)", "startDate": "2025-02-06T00:00:00Z", "endDate": "2025-02-12T00:00:00Z", "submitByDate": "2025-02-12T00:00:00Z", "checkDate": "2025-02-14T00:00:00Z", "checkCount": 18, "id": "0122a544-1627-4b40-8d02-ea84777b7234", "companyId": "00124714", "type": "payperiod", "_rid": "NmJkAKiCbEcoUQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcoUQEAAAAAAA==/", "_etag": "\"9e001e60-0000-0100-0000-686ffc830000\"", "_attachments": "attachments/", "_ts": 1752169603}], "links": [{"rel": "self", "href": "https://ca-payroll-ai-mock-sb-001-secure.nicebeach-8a7a52ab.eastus.azurecontainerapps.io/ca/companies/00124714/payperiods"}]}, "status_code": 200}