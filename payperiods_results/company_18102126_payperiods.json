{"success": true, "company_id": "18102126", "data": {"metadata": {"contentItemCount": 42}, "content": [{"payPeriodId": "1090066033071936", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (2)", "startDate": "2024-12-24T00:00:00Z", "endDate": "2025-01-06T00:00:00Z", "submitByDate": "2025-01-08T00:00:00Z", "checkDate": "2025-01-10T00:00:00Z", "checkCount": 6, "id": "8c48c83d-811d-427d-acdb-6f78b5438ddc", "companyId": "18102126", "type": "payperiod", "_rid": "NmJkAKiCbEfFCgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfFCgMAAAAAAA==/", "_etag": "\"a40057de-0000-0100-0000-687023400000\"", "_attachments": "attachments/", "_ts": 1752179520}, {"payPeriodId": "1090066328060782", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (2)", "startDate": "2025-01-07T00:00:00Z", "endDate": "2025-01-20T00:00:00Z", "submitByDate": "2025-01-22T00:00:00Z", "checkDate": "2025-01-24T00:00:00Z", "checkCount": 6, "id": "724c2967-e744-44cc-8e5b-9626c714bfa0", "companyId": "18102126", "type": "payperiod", "_rid": "NmJkAKiCbEfGCgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfGCgMAAAAAAA==/", "_etag": "\"a40059de-0000-0100-0000-687023400000\"", "_attachments": "attachments/", "_ts": 1752179520}, {"payPeriodId": "1090066619506353", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (2)", "startDate": "2025-01-21T00:00:00Z", "endDate": "2025-02-03T00:00:00Z", "submitByDate": "2025-02-05T00:00:00Z", "checkDate": "2025-02-07T00:00:00Z", "checkCount": 6, "id": "48db29df-dc73-4a18-b18d-8ac02a98ee83", "companyId": "18102126", "type": "payperiod", "_rid": "NmJkAKiCbEfHCgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfHCgMAAAAAAA==/", "_etag": "\"a4005dde-0000-0100-0000-687023400000\"", "_attachments": "attachments/", "_ts": 1752179520}, {"payPeriodId": "1090066916719360", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (2)", "startDate": "2025-02-04T00:00:00Z", "endDate": "2025-02-17T00:00:00Z", "submitByDate": "2025-02-19T00:00:00Z", "checkDate": "2025-02-20T00:00:00Z", "checkCount": 6, "id": "357bf848-20c8-4cfe-a853-f7ac968863eb", "companyId": "18102126", "type": "payperiod", "_rid": "NmJkAKiCbEfICgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfICgMAAAAAAA==/", "_etag": "\"a40064de-0000-0100-0000-687023400000\"", "_attachments": "attachments/", "_ts": 1752179520}, {"payPeriodId": "1090067218423301", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (2)", "startDate": "2025-02-18T00:00:00Z", "endDate": "2025-03-04T00:00:00Z", "submitByDate": "2025-03-05T00:00:00Z", "checkDate": "2025-03-07T00:00:00Z", "checkCount": 3, "id": "b7b5b26e-ee97-4c5c-9715-240b8d5d95d3", "companyId": "18102126", "type": "payperiod", "_rid": "NmJkAKiCbEfJCgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfJCgMAAAAAAA==/", "_etag": "\"a40065de-0000-0100-0000-687023400000\"", "_attachments": "attachments/", "_ts": 1752179520}, {"payPeriodId": "1090067521712462", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (2)", "startDate": "2025-03-04T00:00:00Z", "endDate": "2025-03-18T00:00:00Z", "submitByDate": "2025-03-19T00:00:00Z", "checkDate": "2025-03-21T00:00:00Z", "checkCount": 3, "id": "3f23db99-cad4-4206-bd49-124265dc459f", "companyId": "18102126", "type": "payperiod", "_rid": "NmJkAKiCbEfKCgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfKCgMAAAAAAA==/", "_etag": "\"a4006bde-0000-0100-0000-687023400000\"", "_attachments": "attachments/", "_ts": 1752179520}, {"payPeriodId": "1090067844285989", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (2)", "startDate": "2025-03-18T00:00:00Z", "endDate": "2025-03-31T00:00:00Z", "submitByDate": "2025-04-02T00:00:00Z", "checkDate": "2025-04-04T00:00:00Z", "checkCount": 0, "id": "19840a9b-e047-4399-bac7-c342087b6abb", "companyId": "18102126", "type": "payperiod", "_rid": "NmJkAKiCbEfLCgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfLCgMAAAAAAA==/", "_etag": "\"a40070de-0000-0100-0000-687023400000\"", "_attachments": "attachments/", "_ts": 1752179520}, {"payPeriodId": "1090068160266409", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (2)", "startDate": "2025-04-01T00:00:00Z", "endDate": "2025-04-14T00:00:00Z", "submitByDate": "2025-04-16T00:00:00Z", "checkDate": "2025-04-18T00:00:00Z", "checkCount": 0, "id": "c9332c38-8175-46db-9519-1e6e9b4c825b", "companyId": "18102126", "type": "payperiod", "_rid": "NmJkAKiCbEfMCgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfMCgMAAAAAAA==/", "_etag": "\"a40078de-0000-0100-0000-687023410000\"", "_attachments": "attachments/", "_ts": 1752179521}, {"payPeriodId": "1090068472873571", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (2)", "startDate": "2025-04-15T00:00:00Z", "endDate": "2025-04-28T00:00:00Z", "submitByDate": "2025-04-30T00:00:00Z", "checkDate": "2025-05-02T00:00:00Z", "checkCount": 0, "id": "8f944109-c757-4f10-bfc1-421a142d381a", "companyId": "18102126", "type": "payperiod", "_rid": "NmJkAKiCbEfNCgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfNCgMAAAAAAA==/", "_etag": "\"a4007ede-0000-0100-0000-687023410000\"", "_attachments": "attachments/", "_ts": 1752179521}, {"payPeriodId": "1090068767983477", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (2)", "startDate": "2025-04-29T00:00:00Z", "endDate": "2025-05-12T00:00:00Z", "submitByDate": "2025-05-14T00:00:00Z", "checkDate": "2025-05-16T00:00:00Z", "checkCount": 0, "id": "70e440ef-313e-483f-87aa-3c00e61869a3", "companyId": "18102126", "type": "payperiod", "_rid": "NmJkAKiCbEfOCgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfOCgMAAAAAAA==/", "_etag": "\"a4007fde-0000-0100-0000-687023410000\"", "_attachments": "attachments/", "_ts": 1752179521}, {"payPeriodId": "1090069068303849", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (2)", "startDate": "2025-05-13T00:00:00Z", "endDate": "2025-05-26T00:00:00Z", "submitByDate": "2025-05-28T00:00:00Z", "checkDate": "2025-05-30T00:00:00Z", "checkCount": 0, "id": "ee409a6a-4043-4512-85cd-71e12077948e", "companyId": "18102126", "type": "payperiod", "_rid": "NmJkAKiCbEfPCgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfPCgMAAAAAAA==/", "_etag": "\"a40085de-0000-0100-0000-687023410000\"", "_attachments": "attachments/", "_ts": 1752179521}, {"payPeriodId": "1090069372932463", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (2)", "startDate": "2025-05-27T00:00:00Z", "endDate": "2025-06-09T00:00:00Z", "submitByDate": "2025-06-11T00:00:00Z", "checkDate": "2025-06-13T00:00:00Z", "checkCount": 0, "id": "a8e3cb27-b64b-4632-af5a-032a3c1e9dd4", "companyId": "18102126", "type": "payperiod", "_rid": "NmJkAKiCbEfQCgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfQCgMAAAAAAA==/", "_etag": "\"a40086de-0000-0100-0000-687023410000\"", "_attachments": "attachments/", "_ts": 1752179521}, {"payPeriodId": "1090069676545451", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (2)", "startDate": "2025-06-10T00:00:00Z", "endDate": "2025-06-23T00:00:00Z", "submitByDate": "2025-06-25T00:00:00Z", "checkDate": "2025-06-27T00:00:00Z", "checkCount": 0, "id": "b513b277-9ae4-42e2-9718-d900b06f2fa7", "companyId": "18102126", "type": "payperiod", "_rid": "NmJkAKiCbEfRCgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfRCgMAAAAAAA==/", "_etag": "\"a40089de-0000-0100-0000-687023410000\"", "_attachments": "attachments/", "_ts": 1752179521}, {"payPeriodId": "1090069981649164", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (2)", "startDate": "2025-06-24T00:00:00Z", "endDate": "2025-07-07T00:00:00Z", "submitByDate": "2025-07-09T00:00:00Z", "checkDate": "2025-07-11T00:00:00Z", "checkCount": 0, "id": "b33f401b-3777-4a21-844a-a0f73e2878b7", "companyId": "18102126", "type": "payperiod", "_rid": "NmJkAKiCbEfSCgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfSCgMAAAAAAA==/", "_etag": "\"a4008dde-0000-0100-0000-687023410000\"", "_attachments": "attachments/", "_ts": 1752179521}, {"payPeriodId": "1090070290989145", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (2)", "startDate": "2025-07-08T00:00:00Z", "endDate": "2025-07-21T00:00:00Z", "submitByDate": "2025-07-23T00:00:00Z", "checkDate": "2025-07-25T00:00:00Z", "checkCount": 0, "id": "22823358-9980-45e5-9c8b-c8a6a14eb141", "companyId": "18102126", "type": "payperiod", "_rid": "NmJkAKiCbEfTCgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfTCgMAAAAAAA==/", "_etag": "\"a4008fde-0000-0100-0000-687023410000\"", "_attachments": "attachments/", "_ts": 1752179521}, {"payPeriodId": "1090070592095808", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (2)", "startDate": "2025-07-22T00:00:00Z", "endDate": "2025-08-04T00:00:00Z", "submitByDate": "2025-08-06T00:00:00Z", "checkDate": "2025-08-08T00:00:00Z", "checkCount": 0, "id": "aadc43d7-eb5c-4192-aefb-21d75ef5c39b", "companyId": "18102126", "type": "payperiod", "_rid": "NmJkAKiCbEfUCgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfUCgMAAAAAAA==/", "_etag": "\"a40091de-0000-0100-0000-687023410000\"", "_attachments": "attachments/", "_ts": 1752179521}, {"payPeriodId": "1090070902389795", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (2)", "startDate": "2025-08-05T00:00:00Z", "endDate": "2025-08-18T00:00:00Z", "submitByDate": "2025-08-20T00:00:00Z", "checkDate": "2025-08-22T00:00:00Z", "checkCount": 0, "id": "92d27a59-7d2c-4bfb-a644-c0879fe81687", "companyId": "18102126", "type": "payperiod", "_rid": "NmJkAKiCbEfVCgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfVCgMAAAAAAA==/", "_etag": "\"a40094de-0000-0100-0000-687023410000\"", "_attachments": "attachments/", "_ts": 1752179521}, {"payPeriodId": "1090071223850378", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (2)", "startDate": "2025-08-19T00:00:00Z", "endDate": "2025-09-01T00:00:00Z", "submitByDate": "2025-09-03T00:00:00Z", "checkDate": "2025-09-05T00:00:00Z", "checkCount": 0, "id": "2a49dd9b-cd97-43a3-bb68-1b6744ab9422", "companyId": "18102126", "type": "payperiod", "_rid": "NmJkAKiCbEfWCgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfWCgMAAAAAAA==/", "_etag": "\"a40095de-0000-0100-0000-687023410000\"", "_attachments": "attachments/", "_ts": 1752179521}, {"payPeriodId": "1090071527123402", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (2)", "startDate": "2025-09-02T00:00:00Z", "endDate": "2025-09-15T00:00:00Z", "submitByDate": "2025-09-17T00:00:00Z", "checkDate": "2025-09-19T00:00:00Z", "checkCount": 0, "id": "5b4a5d2b-1915-4098-90ce-e98c2a6a0c68", "companyId": "18102126", "type": "payperiod", "_rid": "NmJkAKiCbEfXCgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfXCgMAAAAAAA==/", "_etag": "\"a40097de-0000-0100-0000-687023410000\"", "_attachments": "attachments/", "_ts": 1752179521}, {"payPeriodId": "1090071838514701", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (2)", "startDate": "2025-09-16T00:00:00Z", "endDate": "2025-09-29T00:00:00Z", "submitByDate": "2025-10-01T00:00:00Z", "checkDate": "2025-10-03T00:00:00Z", "checkCount": 0, "id": "cc57bb46-5b5d-47a2-86d7-f42592fca070", "companyId": "18102126", "type": "payperiod", "_rid": "NmJkAKiCbEfYCgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfYCgMAAAAAAA==/", "_etag": "\"a4009ade-0000-0100-0000-687023410000\"", "_attachments": "attachments/", "_ts": 1752179521}, {"payPeriodId": "1090072149997429", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (2)", "startDate": "2025-09-30T00:00:00Z", "endDate": "2025-10-13T00:00:00Z", "submitByDate": "2025-10-15T00:00:00Z", "checkDate": "2025-10-17T00:00:00Z", "checkCount": 0, "id": "4db55e91-57fd-4c01-9176-c3b1d48c901d", "companyId": "18102126", "type": "payperiod", "_rid": "NmJkAKiCbEfZCgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfZCgMAAAAAAA==/", "_etag": "\"a400a0de-0000-0100-0000-687023420000\"", "_attachments": "attachments/", "_ts": 1752179522}, {"payPeriodId": "1090066033071936", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (2)", "startDate": "2024-12-24T00:00:00Z", "endDate": "2025-01-06T00:00:00Z", "submitByDate": "2025-01-08T00:00:00Z", "checkDate": "2025-01-10T00:00:00Z", "checkCount": 6, "id": "50e94b9c-1b28-4db9-8fec-3b2a5e880558", "companyId": "18102126", "type": "payperiod", "_rid": "NmJkAKiCbEfmCgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfmCgMAAAAAAA==/", "_etag": "\"a400c2de-0000-0100-0000-687023430000\"", "_attachments": "attachments/", "_ts": 1752179523}, {"payPeriodId": "1090066328060782", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (2)", "startDate": "2025-01-07T00:00:00Z", "endDate": "2025-01-20T00:00:00Z", "submitByDate": "2025-01-22T00:00:00Z", "checkDate": "2025-01-24T00:00:00Z", "checkCount": 6, "id": "a0f81f64-c362-47fc-9e6f-3cf9de3752f5", "companyId": "18102126", "type": "payperiod", "_rid": "NmJkAKiCbEfnCgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfnCgMAAAAAAA==/", "_etag": "\"a400c4de-0000-0100-0000-687023430000\"", "_attachments": "attachments/", "_ts": 1752179523}, {"payPeriodId": "1090066619506353", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (2)", "startDate": "2025-01-21T00:00:00Z", "endDate": "2025-02-03T00:00:00Z", "submitByDate": "2025-02-05T00:00:00Z", "checkDate": "2025-02-07T00:00:00Z", "checkCount": 6, "id": "84d3832d-d95b-42b5-a6c1-12a78eaaffa1", "companyId": "18102126", "type": "payperiod", "_rid": "NmJkAKiCbEfoCgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfoCgMAAAAAAA==/", "_etag": "\"a400c7de-0000-0100-0000-687023430000\"", "_attachments": "attachments/", "_ts": 1752179523}, {"payPeriodId": "1090066916719360", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (2)", "startDate": "2025-02-04T00:00:00Z", "endDate": "2025-02-17T00:00:00Z", "submitByDate": "2025-02-19T00:00:00Z", "checkDate": "2025-02-20T00:00:00Z", "checkCount": 6, "id": "0e21667b-5eec-4c16-827b-c6e4988404a3", "companyId": "18102126", "type": "payperiod", "_rid": "NmJkAKiCbEfpCgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfpCgMAAAAAAA==/", "_etag": "\"a400c8de-0000-0100-0000-687023430000\"", "_attachments": "attachments/", "_ts": 1752179523}, {"payPeriodId": "1090067218423301", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (2)", "startDate": "2025-02-18T00:00:00Z", "endDate": "2025-03-04T00:00:00Z", "submitByDate": "2025-03-05T00:00:00Z", "checkDate": "2025-03-07T00:00:00Z", "checkCount": 3, "id": "2bcb09ac-bc72-46e6-b26b-34e42e081957", "companyId": "18102126", "type": "payperiod", "_rid": "NmJkAKiCbEfqCgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfqCgMAAAAAAA==/", "_etag": "\"a400cdde-0000-0100-0000-687023430000\"", "_attachments": "attachments/", "_ts": 1752179523}, {"payPeriodId": "1090067521712462", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (2)", "startDate": "2025-03-04T00:00:00Z", "endDate": "2025-03-18T00:00:00Z", "submitByDate": "2025-03-19T00:00:00Z", "checkDate": "2025-03-21T00:00:00Z", "checkCount": 3, "id": "864ef03d-3afc-4eb5-bdc5-b9a6e59ba97b", "companyId": "18102126", "type": "payperiod", "_rid": "NmJkAKiCbEfrCgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfrCgMAAAAAAA==/", "_etag": "\"a400d0de-0000-0100-0000-687023430000\"", "_attachments": "attachments/", "_ts": 1752179523}, {"payPeriodId": "1090067844285989", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (2)", "startDate": "2025-03-18T00:00:00Z", "endDate": "2025-03-31T00:00:00Z", "submitByDate": "2025-04-02T00:00:00Z", "checkDate": "2025-04-04T00:00:00Z", "checkCount": 3, "id": "6dc862fa-f596-40ab-a47b-2b372215a0e4", "companyId": "18102126", "type": "payperiod", "_rid": "NmJkAKiCbEfsCgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfsCgMAAAAAAA==/", "_etag": "\"a400d5de-0000-0100-0000-687023430000\"", "_attachments": "attachments/", "_ts": 1752179523}, {"payPeriodId": "1090068160266409", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (2)", "startDate": "2025-04-01T00:00:00Z", "endDate": "2025-04-14T00:00:00Z", "submitByDate": "2025-04-16T00:00:00Z", "checkDate": "2025-04-18T00:00:00Z", "checkCount": 3, "id": "677219c3-e051-4e08-82e5-04a919b39362", "companyId": "18102126", "type": "payperiod", "_rid": "NmJkAKiCbEftCgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEftCgMAAAAAAA==/", "_etag": "\"a400d7de-0000-0100-0000-687023430000\"", "_attachments": "attachments/", "_ts": 1752179523}, {"payPeriodId": "1090068472873571", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (2)", "startDate": "2025-04-15T00:00:00Z", "endDate": "2025-04-28T00:00:00Z", "submitByDate": "2025-04-30T00:00:00Z", "checkDate": "2025-05-02T00:00:00Z", "checkCount": 3, "id": "82214353-e666-4552-9569-fadab7689476", "companyId": "18102126", "type": "payperiod", "_rid": "NmJkAKiCbEfuCgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfuCgMAAAAAAA==/", "_etag": "\"a400dade-0000-0100-0000-687023430000\"", "_attachments": "attachments/", "_ts": 1752179523}, {"payPeriodId": "1090068767983477", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (2)", "startDate": "2025-04-29T00:00:00Z", "endDate": "2025-05-12T00:00:00Z", "submitByDate": "2025-05-14T00:00:00Z", "checkDate": "2025-05-16T00:00:00Z", "checkCount": 3, "id": "34a3c3b2-7697-4062-89e1-8f90ddf56688", "companyId": "18102126", "type": "payperiod", "_rid": "NmJkAKiCbEfvCgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfvCgMAAAAAAA==/", "_etag": "\"a400ddde-0000-0100-0000-687023430000\"", "_attachments": "attachments/", "_ts": 1752179523}, {"payPeriodId": "1090069068303849", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (2)", "startDate": "2025-05-13T00:00:00Z", "endDate": "2025-05-26T00:00:00Z", "submitByDate": "2025-05-28T00:00:00Z", "checkDate": "2025-05-30T00:00:00Z", "checkCount": 3, "id": "b6633a80-1f21-41a9-8756-f8b1417fbfdf", "companyId": "18102126", "type": "payperiod", "_rid": "NmJkAKiCbEfwCgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfwCgMAAAAAAA==/", "_etag": "\"a400dede-0000-0100-0000-687023440000\"", "_attachments": "attachments/", "_ts": 1752179524}, {"payPeriodId": "1090069372932463", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (2)", "startDate": "2025-05-27T00:00:00Z", "endDate": "2025-06-09T00:00:00Z", "submitByDate": "2025-06-11T00:00:00Z", "checkDate": "2025-06-13T00:00:00Z", "checkCount": 3, "id": "f2fac2c1-e8f4-4628-81fd-4910e0f8ff94", "companyId": "18102126", "type": "payperiod", "_rid": "NmJkAKiCbEfxCgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfxCgMAAAAAAA==/", "_etag": "\"a400e5de-0000-0100-0000-687023440000\"", "_attachments": "attachments/", "_ts": 1752179524}, {"payPeriodId": "1090069676545451", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (2)", "startDate": "2025-06-10T00:00:00Z", "endDate": "2025-06-23T00:00:00Z", "submitByDate": "2025-06-25T00:00:00Z", "checkDate": "2025-06-27T00:00:00Z", "checkCount": 4, "id": "a3c56463-06b3-4ce9-bb19-3da9330ce4ae", "companyId": "18102126", "type": "payperiod", "_rid": "NmJkAKiCbEfyCgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfyCgMAAAAAAA==/", "_etag": "\"a400e8de-0000-0100-0000-687023440000\"", "_attachments": "attachments/", "_ts": 1752179524}, {"payPeriodId": "1090069981649164", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (2)", "startDate": "2025-06-24T00:00:00Z", "endDate": "2025-07-07T00:00:00Z", "submitByDate": "2025-07-09T00:00:00Z", "checkDate": "2025-07-11T00:00:00Z", "checkCount": 0, "id": "fd386da8-2479-4e22-b7d3-a65e7a7f8c49", "companyId": "18102126", "type": "payperiod", "_rid": "NmJkAKiCbEfzCgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfzCgMAAAAAAA==/", "_etag": "\"a400eade-0000-0100-0000-687023440000\"", "_attachments": "attachments/", "_ts": 1752179524}, {"payPeriodId": "1090070290989145", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (2)", "startDate": "2025-07-08T00:00:00Z", "endDate": "2025-07-21T00:00:00Z", "submitByDate": "2025-07-23T00:00:00Z", "checkDate": "2025-07-25T00:00:00Z", "checkCount": 0, "id": "615bcc35-6e9c-4501-a863-3b7f9c89325d", "companyId": "18102126", "type": "payperiod", "_rid": "NmJkAKiCbEf0CgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf0CgMAAAAAAA==/", "_etag": "\"a400edde-0000-0100-0000-687023440000\"", "_attachments": "attachments/", "_ts": 1752179524}, {"payPeriodId": "1090070592095808", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (2)", "startDate": "2025-07-22T00:00:00Z", "endDate": "2025-08-04T00:00:00Z", "submitByDate": "2025-08-06T00:00:00Z", "checkDate": "2025-08-08T00:00:00Z", "checkCount": 0, "id": "73ce4aab-971a-49db-9f7f-2a60a056048c", "companyId": "18102126", "type": "payperiod", "_rid": "NmJkAKiCbEf1CgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf1CgMAAAAAAA==/", "_etag": "\"a400efde-0000-0100-0000-687023440000\"", "_attachments": "attachments/", "_ts": 1752179524}, {"payPeriodId": "1090070902389795", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (2)", "startDate": "2025-08-05T00:00:00Z", "endDate": "2025-08-18T00:00:00Z", "submitByDate": "2025-08-20T00:00:00Z", "checkDate": "2025-08-22T00:00:00Z", "checkCount": 0, "id": "c47bf591-790e-44ea-85e8-e3561d732d23", "companyId": "18102126", "type": "payperiod", "_rid": "NmJkAKiCbEf2CgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf2CgMAAAAAAA==/", "_etag": "\"a400f8de-0000-0100-0000-687023440000\"", "_attachments": "attachments/", "_ts": 1752179524}, {"payPeriodId": "1090071223850378", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (2)", "startDate": "2025-08-19T00:00:00Z", "endDate": "2025-09-01T00:00:00Z", "submitByDate": "2025-09-03T00:00:00Z", "checkDate": "2025-09-05T00:00:00Z", "checkCount": 0, "id": "2fb3644d-d3ea-464e-be8c-36741220f484", "companyId": "18102126", "type": "payperiod", "_rid": "NmJkAKiCbEf3CgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf3CgMAAAAAAA==/", "_etag": "\"a400fdde-0000-0100-0000-687023440000\"", "_attachments": "attachments/", "_ts": 1752179524}, {"payPeriodId": "1090071527123402", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (2)", "startDate": "2025-09-02T00:00:00Z", "endDate": "2025-09-15T00:00:00Z", "submitByDate": "2025-09-17T00:00:00Z", "checkDate": "2025-09-19T00:00:00Z", "checkCount": 0, "id": "0c207671-9347-4a62-ab4e-5a30e5ab7c91", "companyId": "18102126", "type": "payperiod", "_rid": "NmJkAKiCbEf4CgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf4CgMAAAAAAA==/", "_etag": "\"a400fede-0000-0100-0000-687023440000\"", "_attachments": "attachments/", "_ts": 1752179524}, {"payPeriodId": "1090071838514701", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (2)", "startDate": "2025-09-16T00:00:00Z", "endDate": "2025-09-29T00:00:00Z", "submitByDate": "2025-10-01T00:00:00Z", "checkDate": "2025-10-03T00:00:00Z", "checkCount": 0, "id": "21060f46-4576-48e1-b541-082b3d7f0073", "companyId": "18102126", "type": "payperiod", "_rid": "NmJkAKiCbEf5CgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf5CgMAAAAAAA==/", "_etag": "\"a40006df-0000-0100-0000-687023440000\"", "_attachments": "attachments/", "_ts": 1752179524}, {"payPeriodId": "1090072149997429", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (2)", "startDate": "2025-09-30T00:00:00Z", "endDate": "2025-10-13T00:00:00Z", "submitByDate": "2025-10-15T00:00:00Z", "checkDate": "2025-10-17T00:00:00Z", "checkCount": 0, "id": "f688f34f-b650-4bfa-834c-4c9bb57e6714", "companyId": "18102126", "type": "payperiod", "_rid": "NmJkAKiCbEf6CgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf6CgMAAAAAAA==/", "_etag": "\"a4000fdf-0000-0100-0000-687023440000\"", "_attachments": "attachments/", "_ts": 1752179524}], "links": [{"rel": "self", "href": "https://ca-payroll-ai-mock-sb-001-secure.nicebeach-8a7a52ab.eastus.azurecontainerapps.io/ca/companies/18102126/payperiods"}]}, "status_code": 200}