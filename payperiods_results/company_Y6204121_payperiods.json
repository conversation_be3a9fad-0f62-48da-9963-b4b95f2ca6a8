{"success": true, "company_id": "********", "data": {"metadata": {"contentItemCount": 20}, "content": [{"payPeriodId": "1090065731939183", "intervalCode": "MONTHLY", "status": "COMPLETED", "description": "Monthly Payroll (1)", "startDate": "2024-12-08T00:00:00Z", "endDate": "2025-01-07T00:00:00Z", "submitByDate": "2025-01-03T00:00:00Z", "checkDate": "2025-01-07T00:00:00Z", "checkCount": 4, "id": "aa632561-6c58-41d6-80e6-0d3234a1d81c", "companyId": "********", "type": "payperiod", "_rid": "NmJkAKiCbEdFvwQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdFvwQAAAAAAA==/", "_etag": "\"a9002c63-0000-0100-0000-687047a20000\"", "_attachments": "attachments/", "_ts": 1752188834}, {"payPeriodId": "1090066328149671", "intervalCode": "MONTHLY", "status": "COMPLETED", "description": "Monthly Payroll (1)", "startDate": "2025-01-08T00:00:00Z", "endDate": "2025-02-07T00:00:00Z", "submitByDate": "2025-02-05T00:00:00Z", "checkDate": "2025-02-07T00:00:00Z", "checkCount": 4, "id": "f427dd94-12aa-4393-a5aa-a9694e053b77", "companyId": "********", "type": "payperiod", "_rid": "NmJkAKiCbEdGvwQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdGvwQAAAAAAA==/", "_etag": "\"a9002d63-0000-0100-0000-687047a20000\"", "_attachments": "attachments/", "_ts": 1752188834}, {"payPeriodId": "1090066916782912", "intervalCode": "MONTHLY", "status": "COMPLETED", "description": "Monthly Payroll (1)", "startDate": "2025-02-08T00:00:00Z", "endDate": "2025-03-07T00:00:00Z", "submitByDate": "2025-03-05T00:00:00Z", "checkDate": "2025-03-07T00:00:00Z", "checkCount": 5, "id": "532b2394-30da-4825-9ffb-742f4fed0a00", "companyId": "********", "type": "payperiod", "_rid": "NmJkAKiCbEdHvwQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdHvwQAAAAAAA==/", "_etag": "\"a9002e63-0000-0100-0000-687047a20000\"", "_attachments": "attachments/", "_ts": 1752188834}, {"payPeriodId": "1090067690793084", "intervalCode": "MONTHLY", "status": "INITIAL", "description": "Monthly Payroll (1)", "startDate": "2025-03-08T00:00:00Z", "endDate": "2025-04-07T00:00:00Z", "submitByDate": "2025-04-03T00:00:00Z", "checkDate": "2025-04-07T00:00:00Z", "checkCount": 0, "id": "f46cda40-1c41-43a4-915a-3738bd8bb8cc", "companyId": "********", "type": "payperiod", "_rid": "NmJkAKiCbEdIvwQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdIvwQAAAAAAA==/", "_etag": "\"a9003063-0000-0100-0000-687047a20000\"", "_attachments": "attachments/", "_ts": 1752188834}, {"payPeriodId": "1090068308947401", "intervalCode": "MONTHLY", "status": "INITIAL", "description": "Monthly Payroll (1)", "startDate": "2025-04-08T00:00:00Z", "endDate": "2025-05-07T00:00:00Z", "submitByDate": "2025-05-05T00:00:00Z", "checkDate": "2025-05-07T00:00:00Z", "checkCount": 0, "id": "92cdb15d-de43-420f-a8c7-99466bb47a7b", "companyId": "********", "type": "payperiod", "_rid": "NmJkAKiCbEdJvwQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdJvwQAAAAAAA==/", "_etag": "\"a9003463-0000-0100-0000-687047a20000\"", "_attachments": "attachments/", "_ts": 1752188834}, {"payPeriodId": "1090068910274257", "intervalCode": "MONTHLY", "status": "INITIAL", "description": "Monthly Payroll (1)", "startDate": "2025-05-08T00:00:00Z", "endDate": "2025-06-07T00:00:00Z", "submitByDate": "2025-06-04T00:00:00Z", "checkDate": "2025-06-06T00:00:00Z", "checkCount": 0, "id": "c8ff5432-af92-4cee-a888-36823cf4ca9f", "companyId": "********", "type": "payperiod", "_rid": "NmJkAKiCbEdKvwQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdKvwQAAAAAAA==/", "_etag": "\"a9003863-0000-0100-0000-687047a20000\"", "_attachments": "attachments/", "_ts": 1752188834}, {"payPeriodId": "1090069676640412", "intervalCode": "MONTHLY", "status": "INITIAL", "description": "Monthly Payroll (1)", "startDate": "2025-06-08T00:00:00Z", "endDate": "2025-07-07T00:00:00Z", "submitByDate": "2025-07-02T00:00:00Z", "checkDate": "2025-07-07T00:00:00Z", "checkCount": 0, "id": "89fbdd91-ae0d-4344-a548-7e4551243998", "companyId": "********", "type": "payperiod", "_rid": "NmJkAKiCbEdLvwQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdLvwQAAAAAAA==/", "_etag": "\"a9003b63-0000-0100-0000-687047a20000\"", "_attachments": "attachments/", "_ts": 1752188834}, {"payPeriodId": "1090070291061610", "intervalCode": "MONTHLY", "status": "INITIAL", "description": "Monthly Payroll (1)", "startDate": "2025-07-08T00:00:00Z", "endDate": "2025-08-07T00:00:00Z", "submitByDate": "2025-08-05T00:00:00Z", "checkDate": "2025-08-07T00:00:00Z", "checkCount": 0, "id": "28eae8f8-b5d8-4ae5-beb0-3c59e82e6880", "companyId": "********", "type": "payperiod", "_rid": "NmJkAKiCbEdMvwQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdMvwQAAAAAAA==/", "_etag": "\"a9004063-0000-0100-0000-687047a20000\"", "_attachments": "attachments/", "_ts": 1752188834}, {"payPeriodId": "1090070902452824", "intervalCode": "MONTHLY", "status": "INITIAL", "description": "Monthly Payroll (1)", "startDate": "2025-08-08T00:00:00Z", "endDate": "2025-09-07T00:00:00Z", "submitByDate": "2025-09-03T00:00:00Z", "checkDate": "2025-09-05T00:00:00Z", "checkCount": 0, "id": "cf8c8271-81ec-4868-a95f-c10cb7a1f71b", "companyId": "********", "type": "payperiod", "_rid": "NmJkAKiCbEdNvwQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdNvwQAAAAAAA==/", "_etag": "\"a9004263-0000-0100-0000-687047a20000\"", "_attachments": "attachments/", "_ts": 1752188834}, {"payPeriodId": "1090071689617962", "intervalCode": "MONTHLY", "status": "INITIAL", "description": "Monthly Payroll (1)", "startDate": "2025-09-08T00:00:00Z", "endDate": "2025-10-07T00:00:00Z", "submitByDate": "2025-10-03T00:00:00Z", "checkDate": "2025-10-07T00:00:00Z", "checkCount": 0, "id": "dcdcc212-4448-4d41-a68c-ae9be844150f", "companyId": "********", "type": "payperiod", "_rid": "NmJkAKiCbEdOvwQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdOvwQAAAAAAA==/", "_etag": "\"a9004463-0000-0100-0000-687047a30000\"", "_attachments": "attachments/", "_ts": 1752188835}, {"payPeriodId": "1090065731939183", "intervalCode": "MONTHLY", "status": "COMPLETED", "description": "Monthly Payroll (1)", "startDate": "2024-12-08T00:00:00Z", "endDate": "2025-01-07T00:00:00Z", "submitByDate": "2025-01-03T00:00:00Z", "checkDate": "2025-01-07T00:00:00Z", "checkCount": 4, "id": "5614c44a-7286-4325-83ec-171e6cde00d4", "companyId": "********", "type": "payperiod", "_rid": "NmJkAKiCbEdUvwQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdUvwQAAAAAAA==/", "_etag": "\"a9005563-0000-0100-0000-687047a30000\"", "_attachments": "attachments/", "_ts": 1752188835}, {"payPeriodId": "1090066328149671", "intervalCode": "MONTHLY", "status": "COMPLETED", "description": "Monthly Payroll (1)", "startDate": "2025-01-08T00:00:00Z", "endDate": "2025-02-07T00:00:00Z", "submitByDate": "2025-02-05T00:00:00Z", "checkDate": "2025-02-07T00:00:00Z", "checkCount": 4, "id": "29803b87-68d1-41eb-99ab-134b9a338cdb", "companyId": "********", "type": "payperiod", "_rid": "NmJkAKiCbEdVvwQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdVvwQAAAAAAA==/", "_etag": "\"a9005663-0000-0100-0000-687047a30000\"", "_attachments": "attachments/", "_ts": 1752188835}, {"payPeriodId": "1090066916782912", "intervalCode": "MONTHLY", "status": "COMPLETED", "description": "Monthly Payroll (1)", "startDate": "2025-02-08T00:00:00Z", "endDate": "2025-03-07T00:00:00Z", "submitByDate": "2025-03-05T00:00:00Z", "checkDate": "2025-03-07T00:00:00Z", "checkCount": 5, "id": "6dec615f-1b17-4b71-9b2b-93ddc80eb55f", "companyId": "********", "type": "payperiod", "_rid": "NmJkAKiCbEdWvwQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdWvwQAAAAAAA==/", "_etag": "\"a9005c63-0000-0100-0000-687047a30000\"", "_attachments": "attachments/", "_ts": 1752188835}, {"payPeriodId": "1090067690793084", "intervalCode": "MONTHLY", "status": "COMPLETED", "description": "Monthly Payroll (1)", "startDate": "2025-03-08T00:00:00Z", "endDate": "2025-04-07T00:00:00Z", "submitByDate": "2025-04-03T00:00:00Z", "checkDate": "2025-04-07T00:00:00Z", "checkCount": 5, "id": "55e60167-18fd-4af0-b472-aeeca1f8798a", "companyId": "********", "type": "payperiod", "_rid": "NmJkAKiCbEdXvwQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdXvwQAAAAAAA==/", "_etag": "\"a9005e63-0000-0100-0000-687047a30000\"", "_attachments": "attachments/", "_ts": 1752188835}, {"payPeriodId": "1090068308947401", "intervalCode": "MONTHLY", "status": "COMPLETED", "description": "Monthly Payroll (1)", "startDate": "2025-04-08T00:00:00Z", "endDate": "2025-05-07T00:00:00Z", "submitByDate": "2025-05-05T00:00:00Z", "checkDate": "2025-05-07T00:00:00Z", "checkCount": 5, "id": "dae5edb6-eb15-4b82-b39b-db9994d6ff37", "companyId": "********", "type": "payperiod", "_rid": "NmJkAKiCbEdYvwQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdYvwQAAAAAAA==/", "_etag": "\"a9006163-0000-0100-0000-687047a30000\"", "_attachments": "attachments/", "_ts": 1752188835}, {"payPeriodId": "1090068910274257", "intervalCode": "MONTHLY", "status": "COMPLETED", "description": "Monthly Payroll (1)", "startDate": "2025-05-08T00:00:00Z", "endDate": "2025-06-07T00:00:00Z", "submitByDate": "2025-06-04T00:00:00Z", "checkDate": "2025-06-06T00:00:00Z", "checkCount": 6, "id": "174038fb-0869-434b-9be2-d81269c8fce4", "companyId": "********", "type": "payperiod", "_rid": "NmJkAKiCbEdZvwQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdZvwQAAAAAAA==/", "_etag": "\"a9006663-0000-0100-0000-687047a30000\"", "_attachments": "attachments/", "_ts": 1752188835}, {"payPeriodId": "1090069676640412", "intervalCode": "MONTHLY", "status": "COMPLETED", "description": "Monthly Payroll (1)", "startDate": "2025-06-08T00:00:00Z", "endDate": "2025-07-07T00:00:00Z", "submitByDate": "2025-07-02T00:00:00Z", "checkDate": "2025-07-07T00:00:00Z", "checkCount": 6, "id": "2416a00d-332a-4054-aa7f-cb8f6c13b707", "companyId": "********", "type": "payperiod", "_rid": "NmJkAKiCbEdavwQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdavwQAAAAAAA==/", "_etag": "\"a9006763-0000-0100-0000-687047a30000\"", "_attachments": "attachments/", "_ts": 1752188835}, {"payPeriodId": "1090070291061610", "intervalCode": "MONTHLY", "status": "INITIAL", "description": "Monthly Payroll (1)", "startDate": "2025-07-08T00:00:00Z", "endDate": "2025-08-07T00:00:00Z", "submitByDate": "2025-08-05T00:00:00Z", "checkDate": "2025-08-07T00:00:00Z", "checkCount": 0, "id": "24a672f3-debd-4eff-8aa5-057ec7eaf4f6", "companyId": "********", "type": "payperiod", "_rid": "NmJkAKiCbEdbvwQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdbvwQAAAAAAA==/", "_etag": "\"a9006863-0000-0100-0000-687047a40000\"", "_attachments": "attachments/", "_ts": 1752188836}, {"payPeriodId": "1090070902452824", "intervalCode": "MONTHLY", "status": "INITIAL", "description": "Monthly Payroll (1)", "startDate": "2025-08-08T00:00:00Z", "endDate": "2025-09-07T00:00:00Z", "submitByDate": "2025-09-03T00:00:00Z", "checkDate": "2025-09-05T00:00:00Z", "checkCount": 0, "id": "eb5259f5-e364-4d85-a5fc-1b3f061cea0f", "companyId": "********", "type": "payperiod", "_rid": "NmJkAKiCbEdcvwQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdcvwQAAAAAAA==/", "_etag": "\"a9006963-0000-0100-0000-687047a40000\"", "_attachments": "attachments/", "_ts": 1752188836}, {"payPeriodId": "1090071689617962", "intervalCode": "MONTHLY", "status": "INITIAL", "description": "Monthly Payroll (1)", "startDate": "2025-09-08T00:00:00Z", "endDate": "2025-10-07T00:00:00Z", "submitByDate": "2025-10-03T00:00:00Z", "checkDate": "2025-10-07T00:00:00Z", "checkCount": 0, "id": "db16245f-329d-4620-8e7c-7fdeb0e5642d", "companyId": "********", "type": "payperiod", "_rid": "NmJkAKiCbEddvwQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEddvwQAAAAAAA==/", "_etag": "\"a9006b63-0000-0100-0000-687047a40000\"", "_attachments": "attachments/", "_ts": 1752188836}], "links": [{"rel": "self", "href": "https://ca-payroll-ai-mock-sb-001-secure.nicebeach-8a7a52ab.eastus.azurecontainerapps.io/ca/companies/********/payperiods"}]}, "status_code": 200}