{"success": true, "company_id": "13074568", "data": {"metadata": {"contentItemCount": 40}, "content": [{"payPeriodId": "1050103134617862", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-01-01T00:00:00Z", "endDate": "2025-01-15T00:00:00Z", "submitByDate": "2025-01-13T00:00:00Z", "checkDate": "2025-01-15T00:00:00Z", "checkCount": 9, "id": "586175cd-8dd0-4365-b47a-f03807208518", "companyId": "13074568", "type": "payperiod", "_rid": "NmJkAKiCbEeS9AMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeS9AMAAAAAAA==/", "_etag": "\"a7007c7f-0000-0100-0000-6870362d0000\"", "_attachments": "attachments/", "_ts": 1752184365}, {"payPeriodId": "1050103134617863", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-01-16T00:00:00Z", "endDate": "2025-01-31T00:00:00Z", "submitByDate": "2025-01-29T00:00:00Z", "checkDate": "2025-01-31T00:00:00Z", "checkCount": 3, "id": "7075fd97-45ba-4cb2-b5b5-3ce89b355bd8", "companyId": "13074568", "type": "payperiod", "_rid": "NmJkAKiCbEeT9AMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeT9AMAAAAAAA==/", "_etag": "\"a7007d7f-0000-0100-0000-6870362d0000\"", "_attachments": "attachments/", "_ts": 1752184365}, {"payPeriodId": "1050104092747686", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-02-01T00:00:00Z", "endDate": "2025-02-15T00:00:00Z", "submitByDate": "2025-02-12T00:00:00Z", "checkDate": "2025-02-14T00:00:00Z", "checkCount": 3, "id": "46d12678-896d-497c-b474-5f7ed6e85f3f", "companyId": "13074568", "type": "payperiod", "_rid": "NmJkAKiCbEeU9AMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeU9AMAAAAAAA==/", "_etag": "\"a700807f-0000-0100-0000-6870362d0000\"", "_attachments": "attachments/", "_ts": 1752184365}, {"payPeriodId": "1050104092747687", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-02-16T00:00:00Z", "endDate": "2025-02-28T00:00:00Z", "submitByDate": "2025-02-26T00:00:00Z", "checkDate": "2025-02-28T00:00:00Z", "checkCount": 3, "id": "00aeeb82-5016-4010-9860-2e1d5d7d1e06", "companyId": "13074568", "type": "payperiod", "_rid": "NmJkAKiCbEeV9AMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeV9AMAAAAAAA==/", "_etag": "\"a700827f-0000-0100-0000-6870362d0000\"", "_attachments": "attachments/", "_ts": 1752184365}, {"payPeriodId": "1050105271072361", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-03-01T00:00:00Z", "endDate": "2025-03-15T00:00:00Z", "submitByDate": "2025-03-12T00:00:00Z", "checkDate": "2025-03-14T00:00:00Z", "checkCount": 3, "id": "ed72c20c-6df8-475d-9307-9b367c330b40", "companyId": "13074568", "type": "payperiod", "_rid": "NmJkAKiCbEeW9AMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeW9AMAAAAAAA==/", "_etag": "\"a700857f-0000-0100-0000-6870362d0000\"", "_attachments": "attachments/", "_ts": 1752184365}, {"payPeriodId": "1050105271072362", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-03-16T00:00:00Z", "endDate": "2025-03-31T00:00:00Z", "submitByDate": "2025-03-27T00:00:00Z", "checkDate": "2025-03-31T00:00:00Z", "checkCount": 3, "id": "8a04ae9e-0fdb-472b-a670-6cfd9e903e97", "companyId": "13074568", "type": "payperiod", "_rid": "NmJkAKiCbEeX9AMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeX9AMAAAAAAA==/", "_etag": "\"a7008e7f-0000-0100-0000-6870362d0000\"", "_attachments": "attachments/", "_ts": 1752184365}, {"payPeriodId": "1050106291892685", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-04-01T00:00:00Z", "endDate": "2025-04-15T00:00:00Z", "submitByDate": "2025-04-11T00:00:00Z", "checkDate": "2025-04-15T00:00:00Z", "checkCount": 0, "id": "d252dec7-f7f4-4f26-ab21-d104b7833088", "companyId": "13074568", "type": "payperiod", "_rid": "NmJkAKiCbEeY9AMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeY9AMAAAAAAA==/", "_etag": "\"a700907f-0000-0100-0000-6870362e0000\"", "_attachments": "attachments/", "_ts": 1752184366}, {"payPeriodId": "1050106291892686", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-04-16T00:00:00Z", "endDate": "2025-04-30T00:00:00Z", "submitByDate": "2025-04-28T00:00:00Z", "checkDate": "2025-04-30T00:00:00Z", "checkCount": 0, "id": "6e42965f-195d-463d-bf28-cabece769580", "companyId": "13074568", "type": "payperiod", "_rid": "NmJkAKiCbEeZ9AMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeZ9AMAAAAAAA==/", "_etag": "\"a700927f-0000-0100-0000-6870362e0000\"", "_attachments": "attachments/", "_ts": 1752184366}, {"payPeriodId": "1050107264186136", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-05-01T00:00:00Z", "endDate": "2025-05-15T00:00:00Z", "submitByDate": "2025-05-13T00:00:00Z", "checkDate": "2025-05-15T00:00:00Z", "checkCount": 0, "id": "9aca564d-8073-476f-895b-525dcd863948", "companyId": "13074568", "type": "payperiod", "_rid": "NmJkAKiCbEea9AMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEea9AMAAAAAAA==/", "_etag": "\"a700947f-0000-0100-0000-6870362e0000\"", "_attachments": "attachments/", "_ts": 1752184366}, {"payPeriodId": "1050107264186137", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-05-16T00:00:00Z", "endDate": "2025-05-31T00:00:00Z", "submitByDate": "2025-05-28T00:00:00Z", "checkDate": "2025-05-30T00:00:00Z", "checkCount": 0, "id": "d6b49ab3-e07a-43cc-952f-40de3f01ec00", "companyId": "13074568", "type": "payperiod", "_rid": "NmJkAKiCbEeb9AMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeb9AMAAAAAAA==/", "_etag": "\"a7009a7f-0000-0100-0000-6870362e0000\"", "_attachments": "attachments/", "_ts": 1752184366}, {"payPeriodId": "1050108448739053", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-06-01T00:00:00Z", "endDate": "2025-06-15T00:00:00Z", "submitByDate": "2025-06-11T00:00:00Z", "checkDate": "2025-06-13T00:00:00Z", "checkCount": 0, "id": "157bd06c-7201-4649-8a22-be9811bb0026", "companyId": "13074568", "type": "payperiod", "_rid": "NmJkAKiCbEec9AMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEec9AMAAAAAAA==/", "_etag": "\"a7009c7f-0000-0100-0000-6870362e0000\"", "_attachments": "attachments/", "_ts": 1752184366}, {"payPeriodId": "1050108448739054", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-06-16T00:00:00Z", "endDate": "2025-06-30T00:00:00Z", "submitByDate": "2025-06-26T00:00:00Z", "checkDate": "2025-06-30T00:00:00Z", "checkCount": 0, "id": "8c75b8ca-7177-4741-8a15-21a427d16f22", "companyId": "13074568", "type": "payperiod", "_rid": "NmJkAKiCbEed9AMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEed9AMAAAAAAA==/", "_etag": "\"a7009e7f-0000-0100-0000-6870362e0000\"", "_attachments": "attachments/", "_ts": 1752184366}, {"payPeriodId": "1050109420236221", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-07-01T00:00:00Z", "endDate": "2025-07-15T00:00:00Z", "submitByDate": "2025-07-11T00:00:00Z", "checkDate": "2025-07-15T00:00:00Z", "checkCount": 0, "id": "2508bf05-85fc-4917-a7f7-77aaf1c658a9", "companyId": "13074568", "type": "payperiod", "_rid": "NmJkAKiCbEee9AMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEee9AMAAAAAAA==/", "_etag": "\"a7009f7f-0000-0100-0000-6870362e0000\"", "_attachments": "attachments/", "_ts": 1752184366}, {"payPeriodId": "1050109420236222", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-07-16T00:00:00Z", "endDate": "2025-07-31T00:00:00Z", "submitByDate": "2025-07-29T00:00:00Z", "checkDate": "2025-07-31T00:00:00Z", "checkCount": 0, "id": "d95c4524-f44f-408f-9827-060dfb9e5865", "companyId": "13074568", "type": "payperiod", "_rid": "NmJkAKiCbEef9AMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEef9AMAAAAAAA==/", "_etag": "\"a700a17f-0000-0100-0000-6870362e0000\"", "_attachments": "attachments/", "_ts": 1752184366}, {"payPeriodId": "1050110397750547", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-08-01T00:00:00Z", "endDate": "2025-08-15T00:00:00Z", "submitByDate": "2025-08-13T00:00:00Z", "checkDate": "2025-08-15T00:00:00Z", "checkCount": 0, "id": "06d94c31-84f2-4ddb-a732-630b1f10d4b9", "companyId": "13074568", "type": "payperiod", "_rid": "NmJkAKiCbEeg9AMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeg9AMAAAAAAA==/", "_etag": "\"a700a47f-0000-0100-0000-6870362e0000\"", "_attachments": "attachments/", "_ts": 1752184366}, {"payPeriodId": "1050110397750548", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-08-16T00:00:00Z", "endDate": "2025-08-31T00:00:00Z", "submitByDate": "2025-08-27T00:00:00Z", "checkDate": "2025-08-29T00:00:00Z", "checkCount": 0, "id": "6e8d7a7d-83c0-42bf-832f-202da2a4298a", "companyId": "13074568", "type": "payperiod", "_rid": "NmJkAKiCbEeh9AMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeh9AMAAAAAAA==/", "_etag": "\"a700a57f-0000-0100-0000-6870362e0000\"", "_attachments": "attachments/", "_ts": 1752184366}, {"payPeriodId": "1050111631893312", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-09-01T00:00:00Z", "endDate": "2025-09-15T00:00:00Z", "submitByDate": "2025-09-11T00:00:00Z", "checkDate": "2025-09-15T00:00:00Z", "checkCount": 0, "id": "0da25404-bb91-4d78-98b9-7fc3fe4ac378", "companyId": "13074568", "type": "payperiod", "_rid": "NmJkAKiCbEei9AMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEei9AMAAAAAAA==/", "_etag": "\"a700a87f-0000-0100-0000-6870362e0000\"", "_attachments": "attachments/", "_ts": 1752184366}, {"payPeriodId": "1050111631893313", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-09-16T00:00:00Z", "endDate": "2025-09-30T00:00:00Z", "submitByDate": "2025-09-26T00:00:00Z", "checkDate": "2025-09-30T00:00:00Z", "checkCount": 0, "id": "8f8d711f-22f9-457e-a2e2-fc8362503604", "companyId": "13074568", "type": "payperiod", "_rid": "NmJkAKiCbEej9AMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEej9AMAAAAAAA==/", "_etag": "\"a700a97f-0000-0100-0000-6870362e0000\"", "_attachments": "attachments/", "_ts": 1752184366}, {"payPeriodId": "1050112637231830", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-10-01T00:00:00Z", "endDate": "2025-10-15T00:00:00Z", "submitByDate": "2025-10-13T00:00:00Z", "checkDate": "2025-10-15T00:00:00Z", "checkCount": 0, "id": "705ecd5f-647c-4cca-89c8-ef09aa2bb9d8", "companyId": "13074568", "type": "payperiod", "_rid": "NmJkAKiCbEek9AMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEek9AMAAAAAAA==/", "_etag": "\"a700ab7f-0000-0100-0000-6870362e0000\"", "_attachments": "attachments/", "_ts": 1752184366}, {"payPeriodId": "1050112637231831", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-10-16T00:00:00Z", "endDate": "2025-10-31T00:00:00Z", "submitByDate": "2025-10-29T00:00:00Z", "checkDate": "2025-10-31T00:00:00Z", "checkCount": 0, "id": "f3e4ee74-29af-44f1-a1f2-a15cdcc4d6e4", "companyId": "13074568", "type": "payperiod", "_rid": "NmJkAKiCbEel9AMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEel9AMAAAAAAA==/", "_etag": "\"a700ac7f-0000-0100-0000-6870362f0000\"", "_attachments": "attachments/", "_ts": 1752184367}, {"payPeriodId": "1050103134617862", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-01-01T00:00:00Z", "endDate": "2025-01-15T00:00:00Z", "submitByDate": "2025-01-13T00:00:00Z", "checkDate": "2025-01-15T00:00:00Z", "checkCount": 9, "id": "80bd093d-4963-4292-a50b-28f7c2997e1e", "companyId": "13074568", "type": "payperiod", "_rid": "NmJkAKiCbEet9AMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEet9AMAAAAAAA==/", "_etag": "\"a700be7f-0000-0100-0000-6870362f0000\"", "_attachments": "attachments/", "_ts": 1752184367}, {"payPeriodId": "1050103134617863", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-01-16T00:00:00Z", "endDate": "2025-01-31T00:00:00Z", "submitByDate": "2025-01-29T00:00:00Z", "checkDate": "2025-01-31T00:00:00Z", "checkCount": 3, "id": "3ba426be-0d9e-455d-9d7c-415d75539462", "companyId": "13074568", "type": "payperiod", "_rid": "NmJkAKiCbEeu9AMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeu9AMAAAAAAA==/", "_etag": "\"a700c17f-0000-0100-0000-6870362f0000\"", "_attachments": "attachments/", "_ts": 1752184367}, {"payPeriodId": "1050104092747686", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-02-01T00:00:00Z", "endDate": "2025-02-15T00:00:00Z", "submitByDate": "2025-02-12T00:00:00Z", "checkDate": "2025-02-14T00:00:00Z", "checkCount": 3, "id": "6f743244-0d98-4d8b-aa8d-496f71e93a4e", "companyId": "13074568", "type": "payperiod", "_rid": "NmJkAKiCbEev9AMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEev9AMAAAAAAA==/", "_etag": "\"a700c27f-0000-0100-0000-6870362f0000\"", "_attachments": "attachments/", "_ts": 1752184367}, {"payPeriodId": "1050104092747687", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-02-16T00:00:00Z", "endDate": "2025-02-28T00:00:00Z", "submitByDate": "2025-02-26T00:00:00Z", "checkDate": "2025-02-28T00:00:00Z", "checkCount": 3, "id": "683f46d0-43c0-4247-ba9c-70b0ec9aa25b", "companyId": "13074568", "type": "payperiod", "_rid": "NmJkAKiCbEew9AMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEew9AMAAAAAAA==/", "_etag": "\"a700c87f-0000-0100-0000-6870362f0000\"", "_attachments": "attachments/", "_ts": 1752184367}, {"payPeriodId": "1050105271072361", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-03-01T00:00:00Z", "endDate": "2025-03-15T00:00:00Z", "submitByDate": "2025-03-12T00:00:00Z", "checkDate": "2025-03-14T00:00:00Z", "checkCount": 3, "id": "b3c057bf-c1ee-4a95-8957-ca835f172f79", "companyId": "13074568", "type": "payperiod", "_rid": "NmJkAKiCbEex9AMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEex9AMAAAAAAA==/", "_etag": "\"a700ca7f-0000-0100-0000-687036300000\"", "_attachments": "attachments/", "_ts": 1752184368}, {"payPeriodId": "1050105271072362", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-03-16T00:00:00Z", "endDate": "2025-03-31T00:00:00Z", "submitByDate": "2025-03-27T00:00:00Z", "checkDate": "2025-03-31T00:00:00Z", "checkCount": 3, "id": "0df63801-70a1-4585-ab32-8522cab9162a", "companyId": "13074568", "type": "payperiod", "_rid": "NmJkAKiCbEey9AMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEey9AMAAAAAAA==/", "_etag": "\"a700d17f-0000-0100-0000-687036300000\"", "_attachments": "attachments/", "_ts": 1752184368}, {"payPeriodId": "1050106291892685", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-04-01T00:00:00Z", "endDate": "2025-04-15T00:00:00Z", "submitByDate": "2025-04-11T00:00:00Z", "checkDate": "2025-04-15T00:00:00Z", "checkCount": 6, "id": "d7f7a498-ef05-40d3-81fc-f7cc918a888c", "companyId": "13074568", "type": "payperiod", "_rid": "NmJkAKiCbEez9AMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEez9AMAAAAAAA==/", "_etag": "\"a700d27f-0000-0100-0000-687036300000\"", "_attachments": "attachments/", "_ts": 1752184368}, {"payPeriodId": "1050106291892686", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-04-16T00:00:00Z", "endDate": "2025-04-30T00:00:00Z", "submitByDate": "2025-04-28T00:00:00Z", "checkDate": "2025-04-30T00:00:00Z", "checkCount": 3, "id": "cf16f454-2be7-42c5-b58c-4e5d408abca5", "companyId": "13074568", "type": "payperiod", "_rid": "NmJkAKiCbEe09AMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEe09AMAAAAAAA==/", "_etag": "\"a700d47f-0000-0100-0000-687036300000\"", "_attachments": "attachments/", "_ts": 1752184368}, {"payPeriodId": "1050107264186136", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-05-01T00:00:00Z", "endDate": "2025-05-15T00:00:00Z", "submitByDate": "2025-05-13T00:00:00Z", "checkDate": "2025-05-15T00:00:00Z", "checkCount": 6, "id": "c4d710af-cc4c-4d1b-9cb2-bdbdbc1776c0", "companyId": "13074568", "type": "payperiod", "_rid": "NmJkAKiCbEe19AMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEe19AMAAAAAAA==/", "_etag": "\"a700df7f-0000-0100-0000-687036300000\"", "_attachments": "attachments/", "_ts": 1752184368}, {"payPeriodId": "1050107264186137", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-05-16T00:00:00Z", "endDate": "2025-05-31T00:00:00Z", "submitByDate": "2025-05-28T00:00:00Z", "checkDate": "2025-05-30T00:00:00Z", "checkCount": 3, "id": "a2b76c46-6db1-47da-8b4e-cfd4562b757c", "companyId": "13074568", "type": "payperiod", "_rid": "NmJkAKiCbEe29AMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEe29AMAAAAAAA==/", "_etag": "\"a700e27f-0000-0100-0000-687036300000\"", "_attachments": "attachments/", "_ts": 1752184368}, {"payPeriodId": "1050108448739053", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-06-01T00:00:00Z", "endDate": "2025-06-15T00:00:00Z", "submitByDate": "2025-06-11T00:00:00Z", "checkDate": "2025-06-13T00:00:00Z", "checkCount": 3, "id": "377bdd7d-f2dd-4a5b-bd87-614f896a612e", "companyId": "13074568", "type": "payperiod", "_rid": "NmJkAKiCbEe39AMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEe39AMAAAAAAA==/", "_etag": "\"a700e47f-0000-0100-0000-687036300000\"", "_attachments": "attachments/", "_ts": 1752184368}, {"payPeriodId": "1050108448739054", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-06-16T00:00:00Z", "endDate": "2025-06-30T00:00:00Z", "submitByDate": "2025-06-26T00:00:00Z", "checkDate": "2025-06-30T00:00:00Z", "checkCount": 3, "id": "c14e8d62-014d-49ac-8ef7-07116e12b9f8", "companyId": "13074568", "type": "payperiod", "_rid": "NmJkAKiCbEe49AMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEe49AMAAAAAAA==/", "_etag": "\"a700ef7f-0000-0100-0000-687036300000\"", "_attachments": "attachments/", "_ts": 1752184368}, {"payPeriodId": "1050109420236221", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-07-01T00:00:00Z", "endDate": "2025-07-15T00:00:00Z", "submitByDate": "2025-07-11T00:00:00Z", "checkDate": "2025-07-15T00:00:00Z", "checkCount": 0, "id": "e1bb68f6-937f-4b8b-9f7a-5aed07d3f619", "companyId": "13074568", "type": "payperiod", "_rid": "NmJkAKiCbEe59AMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEe59AMAAAAAAA==/", "_etag": "\"a700f37f-0000-0100-0000-687036300000\"", "_attachments": "attachments/", "_ts": 1752184368}, {"payPeriodId": "1050109420236222", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-07-16T00:00:00Z", "endDate": "2025-07-31T00:00:00Z", "submitByDate": "2025-07-29T00:00:00Z", "checkDate": "2025-07-31T00:00:00Z", "checkCount": 0, "id": "86ef000a-58ab-4bc1-aeeb-1ddac04e0be1", "companyId": "13074568", "type": "payperiod", "_rid": "NmJkAKiCbEe69AMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEe69AMAAAAAAA==/", "_etag": "\"a700f57f-0000-0100-0000-687036300000\"", "_attachments": "attachments/", "_ts": 1752184368}, {"payPeriodId": "1050110397750547", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-08-01T00:00:00Z", "endDate": "2025-08-15T00:00:00Z", "submitByDate": "2025-08-13T00:00:00Z", "checkDate": "2025-08-15T00:00:00Z", "checkCount": 0, "id": "27f6448c-d1a3-427f-b610-37524bf67974", "companyId": "13074568", "type": "payperiod", "_rid": "NmJkAKiCbEe79AMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEe79AMAAAAAAA==/", "_etag": "\"a700f87f-0000-0100-0000-687036300000\"", "_attachments": "attachments/", "_ts": 1752184368}, {"payPeriodId": "1050110397750548", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-08-16T00:00:00Z", "endDate": "2025-08-31T00:00:00Z", "submitByDate": "2025-08-27T00:00:00Z", "checkDate": "2025-08-29T00:00:00Z", "checkCount": 0, "id": "cb4d9156-8985-429f-beeb-17aec3d8e3bf", "companyId": "13074568", "type": "payperiod", "_rid": "NmJkAKiCbEe89AMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEe89AMAAAAAAA==/", "_etag": "\"a700fa7f-0000-0100-0000-687036300000\"", "_attachments": "attachments/", "_ts": 1752184368}, {"payPeriodId": "1050111631893312", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-09-01T00:00:00Z", "endDate": "2025-09-15T00:00:00Z", "submitByDate": "2025-09-11T00:00:00Z", "checkDate": "2025-09-15T00:00:00Z", "checkCount": 0, "id": "ed5c690b-cbd9-462f-9a29-3526b59e44b6", "companyId": "13074568", "type": "payperiod", "_rid": "NmJkAKiCbEe99AMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEe99AMAAAAAAA==/", "_etag": "\"a7000080-0000-0100-0000-687036300000\"", "_attachments": "attachments/", "_ts": 1752184368}, {"payPeriodId": "1050111631893313", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-09-16T00:00:00Z", "endDate": "2025-09-30T00:00:00Z", "submitByDate": "2025-09-26T00:00:00Z", "checkDate": "2025-09-30T00:00:00Z", "checkCount": 0, "id": "372faf0a-1b23-4db7-8508-298c7e7ae95e", "companyId": "13074568", "type": "payperiod", "_rid": "NmJkAKiCbEe+9AMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEe+9AMAAAAAAA==/", "_etag": "\"a7000580-0000-0100-0000-687036310000\"", "_attachments": "attachments/", "_ts": 1752184369}, {"payPeriodId": "1050112637231830", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-10-01T00:00:00Z", "endDate": "2025-10-15T00:00:00Z", "submitByDate": "2025-10-13T00:00:00Z", "checkDate": "2025-10-15T00:00:00Z", "checkCount": 0, "id": "a9fffd98-7a6d-4763-b876-7c9615e770aa", "companyId": "13074568", "type": "payperiod", "_rid": "NmJkAKiCbEe-9AMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEe-9AMAAAAAAA==/", "_etag": "\"a7000680-0000-0100-0000-687036310000\"", "_attachments": "attachments/", "_ts": 1752184369}, {"payPeriodId": "1050112637231831", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-10-16T00:00:00Z", "endDate": "2025-10-31T00:00:00Z", "submitByDate": "2025-10-29T00:00:00Z", "checkDate": "2025-10-31T00:00:00Z", "checkCount": 0, "id": "b2fd48b7-2b9f-4d16-b66a-d2f954eecaf0", "companyId": "13074568", "type": "payperiod", "_rid": "NmJkAKiCbEfA9AMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfA9AMAAAAAAA==/", "_etag": "\"a7000c80-0000-0100-0000-687036310000\"", "_attachments": "attachments/", "_ts": 1752184369}], "links": [{"rel": "self", "href": "https://ca-payroll-ai-mock-sb-001-secure.nicebeach-8a7a52ab.eastus.azurecontainerapps.io/ca/companies/13074568/payperiods"}]}, "status_code": 200}