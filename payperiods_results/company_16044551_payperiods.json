{"success": true, "company_id": "16044551", "data": {"metadata": {"contentItemCount": 164}, "content": [{"payPeriodId": "1070074886406525", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2024-12-29T00:00:00Z", "endDate": "2025-01-04T00:00:00Z", "submitByDate": "2025-01-07T00:00:00Z", "checkDate": "2025-01-08T00:00:00Z", "checkCount": 2, "id": "08d0de51-0fce-4464-a458-18f651b452b1", "companyId": "16044551", "type": "payperiod", "_rid": "NmJkAKiCbEfK1gAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfK1gAAAAAAAA==/", "_etag": "\"9a003c29-0000-0100-0000-686fe0500000\"", "_attachments": "attachments/", "_ts": 1752162384}, {"payPeriodId": "1070075018742093", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-01-05T00:00:00Z", "endDate": "2025-01-11T00:00:00Z", "submitByDate": "2025-01-14T00:00:00Z", "checkDate": "2025-01-15T00:00:00Z", "checkCount": 3, "id": "7ab511ec-486e-4191-8b6e-9439c1d76e47", "companyId": "16044551", "type": "payperiod", "_rid": "NmJkAKiCbEfL1gAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfL1gAAAAAAAA==/", "_etag": "\"9a003e29-0000-0100-0000-686fe0500000\"", "_attachments": "attachments/", "_ts": 1752162384}, {"payPeriodId": "1070075148197844", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-01-12T00:00:00Z", "endDate": "2025-01-18T00:00:00Z", "submitByDate": "2025-01-21T00:00:00Z", "checkDate": "2025-01-22T00:00:00Z", "checkCount": 3, "id": "56927ead-e1e9-4e82-9fc4-7a2d5d730eb1", "companyId": "16044551", "type": "payperiod", "_rid": "NmJkAKiCbEfM1gAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfM1gAAAAAAAA==/", "_etag": "\"9a004029-0000-0100-0000-686fe0500000\"", "_attachments": "attachments/", "_ts": 1752162384}, {"payPeriodId": "1070075289862789", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-01-19T00:00:00Z", "endDate": "2025-01-25T00:00:00Z", "submitByDate": "2025-01-28T00:00:00Z", "checkDate": "2025-01-29T00:00:00Z", "checkCount": 3, "id": "d75d183d-a037-41d5-a2e0-082c3b4daacf", "companyId": "16044551", "type": "payperiod", "_rid": "NmJkAKiCbEfN1gAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfN1gAAAAAAAA==/", "_etag": "\"9a004129-0000-0100-0000-686fe0500000\"", "_attachments": "attachments/", "_ts": 1752162384}, {"payPeriodId": "1070077125207556", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-01-26T00:00:00Z", "endDate": "2025-02-01T00:00:00Z", "submitByDate": "2025-02-11T00:00:00Z", "checkDate": "2025-02-05T00:00:00Z", "checkCount": 4, "id": "1d0889a5-59a2-46ca-bbdd-ae50fbadbee3", "companyId": "16044551", "type": "payperiod", "_rid": "NmJkAKiCbEfO1gAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfO1gAAAAAAAA==/", "_etag": "\"9a004429-0000-0100-0000-686fe0500000\"", "_attachments": "attachments/", "_ts": 1752162384}, {"payPeriodId": "1070077680026549", "status": "COMPLETED", "description": "Void", "startDate": "2025-01-26T00:00:00Z", "endDate": "2025-02-01T00:00:00Z", "submitByDate": "2025-02-18T00:00:00Z", "checkDate": "2025-02-19T00:00:00Z", "checkCount": 2, "id": "54addce7-a595-4646-868f-24a2bcbc30c4", "companyId": "16044551", "type": "payperiod", "_rid": "NmJkAKiCbEfP1gAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfP1gAAAAAAAA==/", "_etag": "\"9a004529-0000-0100-0000-686fe0500000\"", "_attachments": "attachments/", "_ts": 1752162384}, {"payPeriodId": "1070077262118268", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-02-02T00:00:00Z", "endDate": "2025-02-08T00:00:00Z", "submitByDate": "2025-02-11T00:00:00Z", "checkDate": "2025-02-12T00:00:00Z", "checkCount": 3, "id": "5cc9039d-9031-4c9d-93d6-5774181ba443", "companyId": "16044551", "type": "payperiod", "_rid": "NmJkAKiCbEfQ1gAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfQ1gAAAAAAAA==/", "_etag": "\"9a004b29-0000-0100-0000-686fe0500000\"", "_attachments": "attachments/", "_ts": 1752162384}, {"payPeriodId": "1070075548967384", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-02-09T00:00:00Z", "endDate": "2025-02-15T00:00:00Z", "submitByDate": "2025-02-18T00:00:00Z", "checkDate": "2025-02-19T00:00:00Z", "checkCount": 6, "id": "8803981b-a4ba-4ef8-af15-f844eb1c9167", "companyId": "16044551", "type": "payperiod", "_rid": "NmJkAKiCbEfR1gAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfR1gAAAAAAAA==/", "_etag": "\"9a004d29-0000-0100-0000-686fe0500000\"", "_attachments": "attachments/", "_ts": 1752162384}, {"payPeriodId": "1070075672290593", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-02-16T00:00:00Z", "endDate": "2025-02-22T00:00:00Z", "submitByDate": "2025-02-25T00:00:00Z", "checkDate": "2025-02-26T00:00:00Z", "checkCount": 6, "id": "bb085432-11c9-4529-bea0-3e0393a45486", "companyId": "16044551", "type": "payperiod", "_rid": "NmJkAKiCbEfS1gAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfS1gAAAAAAAA==/", "_etag": "\"9a005029-0000-0100-0000-686fe0500000\"", "_attachments": "attachments/", "_ts": 1752162384}, {"payPeriodId": "1070075823380151", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-02-23T00:00:00Z", "endDate": "2025-03-01T00:00:00Z", "submitByDate": "2025-03-04T00:00:00Z", "checkDate": "2025-03-05T00:00:00Z", "checkCount": 5, "id": "21f4ebfe-b6d1-4cf5-8ec8-4a36b3d37298", "companyId": "16044551", "type": "payperiod", "_rid": "NmJkAKiCbEfT1gAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfT1gAAAAAAAA==/", "_etag": "\"9a005529-0000-0100-0000-686fe0500000\"", "_attachments": "attachments/", "_ts": 1752162384}, {"payPeriodId": "1070075929147526", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-03-02T00:00:00Z", "endDate": "2025-03-08T00:00:00Z", "submitByDate": "2025-03-11T00:00:00Z", "checkDate": "2025-03-12T00:00:00Z", "checkCount": 5, "id": "2dc6fd12-edba-4806-bcaf-ece2e3c56d84", "companyId": "16044551", "type": "payperiod", "_rid": "NmJkAKiCbEfU1gAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfU1gAAAAAAAA==/", "_etag": "\"9a005729-0000-0100-0000-686fe0500000\"", "_attachments": "attachments/", "_ts": 1752162384}, {"payPeriodId": "1070076070901499", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-03-09T00:00:00Z", "endDate": "2025-03-15T00:00:00Z", "submitByDate": "2025-03-18T00:00:00Z", "checkDate": "2025-03-19T00:00:00Z", "checkCount": 5, "id": "380a2ad7-2f85-4f64-bc1e-343aa4cb776d", "companyId": "16044551", "type": "payperiod", "_rid": "NmJkAKiCbEfV1gAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfV1gAAAAAAAA==/", "_etag": "\"9a005d29-0000-0100-0000-686fe0510000\"", "_attachments": "attachments/", "_ts": 1752162385}, {"payPeriodId": "1070076205845872", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-03-16T00:00:00Z", "endDate": "2025-03-22T00:00:00Z", "submitByDate": "2025-03-25T00:00:00Z", "checkDate": "2025-03-26T00:00:00Z", "checkCount": 5, "id": "2b1e5431-e539-4b68-a2d7-d542a07b4e47", "companyId": "16044551", "type": "payperiod", "_rid": "NmJkAKiCbEfW1gAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfW1gAAAAAAAA==/", "_etag": "\"9a005f29-0000-0100-0000-686fe0510000\"", "_attachments": "attachments/", "_ts": 1752162385}, {"payPeriodId": "1070076297858020", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-03-23T00:00:00Z", "endDate": "2025-03-29T00:00:00Z", "submitByDate": "2025-04-01T00:00:00Z", "checkDate": "2025-04-02T00:00:00Z", "checkCount": 0, "id": "fbdd031f-6b44-41c2-bc1c-17830413a294", "companyId": "16044551", "type": "payperiod", "_rid": "NmJkAKiCbEfX1gAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfX1gAAAAAAAA==/", "_etag": "\"9a006129-0000-0100-0000-686fe0510000\"", "_attachments": "attachments/", "_ts": 1752162385}, {"payPeriodId": "1070076476749810", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-03-30T00:00:00Z", "endDate": "2025-04-05T00:00:00Z", "submitByDate": "2025-04-08T00:00:00Z", "checkDate": "2025-04-09T00:00:00Z", "checkCount": 0, "id": "4b26a7ad-290d-44d8-b225-bb28d3d0dfec", "companyId": "16044551", "type": "payperiod", "_rid": "NmJkAKiCbEfY1gAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfY1gAAAAAAAA==/", "_etag": "\"9a006229-0000-0100-0000-686fe0510000\"", "_attachments": "attachments/", "_ts": 1752162385}, {"payPeriodId": "1070076607043608", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-04-06T00:00:00Z", "endDate": "2025-04-12T00:00:00Z", "submitByDate": "2025-04-15T00:00:00Z", "checkDate": "2025-04-16T00:00:00Z", "checkCount": 0, "id": "1c7c6cc3-24b7-4176-977a-92866d406ec6", "companyId": "16044551", "type": "payperiod", "_rid": "NmJkAKiCbEfZ1gAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfZ1gAAAAAAAA==/", "_etag": "\"9a006629-0000-0100-0000-686fe0510000\"", "_attachments": "attachments/", "_ts": 1752162385}, {"payPeriodId": "1070076734462157", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-04-13T00:00:00Z", "endDate": "2025-04-19T00:00:00Z", "submitByDate": "2025-04-22T00:00:00Z", "checkDate": "2025-04-23T00:00:00Z", "checkCount": 0, "id": "9d0548ad-0e06-4600-95a8-edf3e3753b61", "companyId": "16044551", "type": "payperiod", "_rid": "NmJkAKiCbEfa1gAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfa1gAAAAAAAA==/", "_etag": "\"9a006b29-0000-0100-0000-686fe0510000\"", "_attachments": "attachments/", "_ts": 1752162385}, {"payPeriodId": "1070076874836473", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-04-20T00:00:00Z", "endDate": "2025-04-26T00:00:00Z", "submitByDate": "2025-04-29T00:00:00Z", "checkDate": "2025-04-30T00:00:00Z", "checkCount": 0, "id": "d495ccab-76e1-4d9b-ba81-5efd18deb3c5", "companyId": "16044551", "type": "payperiod", "_rid": "NmJkAKiCbEfb1gAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfb1gAAAAAAAA==/", "_etag": "\"9a007229-0000-0100-0000-686fe0510000\"", "_attachments": "attachments/", "_ts": 1752162385}, {"payPeriodId": "1070076995941457", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-04-27T00:00:00Z", "endDate": "2025-05-03T00:00:00Z", "submitByDate": "2025-05-06T00:00:00Z", "checkDate": "2025-05-07T00:00:00Z", "checkCount": 0, "id": "0b0a80cb-4238-4fff-8490-59c950e04ba5", "companyId": "16044551", "type": "payperiod", "_rid": "NmJkAKiCbEfc1gAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfc1gAAAAAAAA==/", "_etag": "\"9a007529-0000-0100-0000-686fe0510000\"", "_attachments": "attachments/", "_ts": 1752162385}, {"payPeriodId": "1070077259396382", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-05-04T00:00:00Z", "endDate": "2025-05-10T00:00:00Z", "submitByDate": "2025-05-13T00:00:00Z", "checkDate": "2025-05-14T00:00:00Z", "checkCount": 0, "id": "5daeb9c8-b8b2-462e-a501-fa538b436358", "companyId": "16044551", "type": "payperiod", "_rid": "NmJkAKiCbEfd1gAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfd1gAAAAAAAA==/", "_etag": "\"9a007729-0000-0100-0000-686fe0510000\"", "_attachments": "attachments/", "_ts": 1752162385}, {"payPeriodId": "1070077386398323", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-05-11T00:00:00Z", "endDate": "2025-05-17T00:00:00Z", "submitByDate": "2025-05-20T00:00:00Z", "checkDate": "2025-05-21T00:00:00Z", "checkCount": 0, "id": "33be3be1-d721-4bf7-a03f-3f7ed9677dd8", "companyId": "16044551", "type": "payperiod", "_rid": "NmJkAKiCbEfe1gAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfe1gAAAAAAAA==/", "_etag": "\"9a007929-0000-0100-0000-686fe0510000\"", "_attachments": "attachments/", "_ts": 1752162385}, {"payPeriodId": "1070077390477912", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-05-18T00:00:00Z", "endDate": "2025-05-24T00:00:00Z", "submitByDate": "2025-05-27T00:00:00Z", "checkDate": "2025-05-28T00:00:00Z", "checkCount": 0, "id": "157d957d-e557-4bae-8ba3-9c8ae5edcc3c", "companyId": "16044551", "type": "payperiod", "_rid": "NmJkAKiCbEff1gAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEff1gAAAAAAAA==/", "_etag": "\"9a007c29-0000-0100-0000-686fe0510000\"", "_attachments": "attachments/", "_ts": 1752162385}, {"payPeriodId": "1070077523756262", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-05-25T00:00:00Z", "endDate": "2025-05-31T00:00:00Z", "submitByDate": "2025-06-03T00:00:00Z", "checkDate": "2025-06-04T00:00:00Z", "checkCount": 0, "id": "98517bf6-f8ef-4022-95c8-d53b97bb424f", "companyId": "16044551", "type": "payperiod", "_rid": "NmJkAKiCbEfg1gAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfg1gAAAAAAAA==/", "_etag": "\"9a007e29-0000-0100-0000-686fe0510000\"", "_attachments": "attachments/", "_ts": 1752162385}, {"payPeriodId": "1070077668386224", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-06-01T00:00:00Z", "endDate": "2025-06-07T00:00:00Z", "submitByDate": "2025-06-11T00:00:00Z", "checkDate": "2025-06-12T00:00:00Z", "checkCount": 0, "id": "3d4d1bf4-e3d0-4b54-bffa-21448d966ab5", "companyId": "16044551", "type": "payperiod", "_rid": "NmJkAKiCbEfh1gAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfh1gAAAAAAAA==/", "_etag": "\"9a007f29-0000-0100-0000-686fe0510000\"", "_attachments": "attachments/", "_ts": 1752162385}, {"payPeriodId": "1070077788815174", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-06-08T00:00:00Z", "endDate": "2025-06-14T00:00:00Z", "submitByDate": "2025-06-17T00:00:00Z", "checkDate": "2025-06-18T00:00:00Z", "checkCount": 0, "id": "de1b75a7-cfdf-4985-88e5-0c1185da2db0", "companyId": "16044551", "type": "payperiod", "_rid": "NmJkAKiCbEfi1gAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfi1gAAAAAAAA==/", "_etag": "\"9a008029-0000-0100-0000-686fe0510000\"", "_attachments": "attachments/", "_ts": 1752162385}, {"payPeriodId": "1070077926830832", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-06-15T00:00:00Z", "endDate": "2025-06-21T00:00:00Z", "submitByDate": "2025-06-24T00:00:00Z", "checkDate": "2025-06-25T00:00:00Z", "checkCount": 0, "id": "ce2fc4f3-f2dd-475c-973f-977c77ffce28", "companyId": "16044551", "type": "payperiod", "_rid": "NmJkAKiCbEfj1gAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfj1gAAAAAAAA==/", "_etag": "\"9a008429-0000-0100-0000-686fe0520000\"", "_attachments": "attachments/", "_ts": 1752162386}, {"payPeriodId": "1070078053377840", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-06-22T00:00:00Z", "endDate": "2025-06-28T00:00:00Z", "submitByDate": "2025-07-01T00:00:00Z", "checkDate": "2025-07-02T00:00:00Z", "checkCount": 0, "id": "7d13aa31-69a8-42b2-aa1e-a913405c6127", "companyId": "16044551", "type": "payperiod", "_rid": "NmJkAKiCbEfk1gAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfk1gAAAAAAAA==/", "_etag": "\"9a008729-0000-0100-0000-686fe0520000\"", "_attachments": "attachments/", "_ts": 1752162386}, {"payPeriodId": "1070078184479527", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-06-29T00:00:00Z", "endDate": "2025-07-05T00:00:00Z", "submitByDate": "2025-07-08T00:00:00Z", "checkDate": "2025-07-09T00:00:00Z", "checkCount": 0, "id": "2043d1c9-3c0c-4b3b-ae1f-2055535b6fda", "companyId": "16044551", "type": "payperiod", "_rid": "NmJkAKiCbEfl1gAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfl1gAAAAAAAA==/", "_etag": "\"9a008a29-0000-0100-0000-686fe0520000\"", "_attachments": "attachments/", "_ts": 1752162386}, {"payPeriodId": "1070078317960996", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-07-06T00:00:00Z", "endDate": "2025-07-12T00:00:00Z", "submitByDate": "2025-07-15T00:00:00Z", "checkDate": "2025-07-16T00:00:00Z", "checkCount": 0, "id": "0ecb32c0-21b7-46d7-a427-382de0c1f36b", "companyId": "16044551", "type": "payperiod", "_rid": "NmJkAKiCbEfm1gAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfm1gAAAAAAAA==/", "_etag": "\"9a008c29-0000-0100-0000-686fe0520000\"", "_attachments": "attachments/", "_ts": 1752162386}, {"payPeriodId": "1070078457755470", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-07-13T00:00:00Z", "endDate": "2025-07-19T00:00:00Z", "submitByDate": "2025-07-22T00:00:00Z", "checkDate": "2025-07-23T00:00:00Z", "checkCount": 0, "id": "51657cbd-bb53-4aed-9e3e-241dc7fa12a2", "companyId": "16044551", "type": "payperiod", "_rid": "NmJkAKiCbEfn1gAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfn1gAAAAAAAA==/", "_etag": "\"9a008e29-0000-0100-0000-686fe0520000\"", "_attachments": "attachments/", "_ts": 1752162386}, {"payPeriodId": "1070078582295956", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-07-20T00:00:00Z", "endDate": "2025-07-26T00:00:00Z", "submitByDate": "2025-07-29T00:00:00Z", "checkDate": "2025-07-30T00:00:00Z", "checkCount": 0, "id": "db99286d-560c-4c92-9f75-329ce0bb5dc6", "companyId": "16044551", "type": "payperiod", "_rid": "NmJkAKiCbEfo1gAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfo1gAAAAAAAA==/", "_etag": "\"9a009229-0000-0100-0000-686fe0520000\"", "_attachments": "attachments/", "_ts": 1752162386}, {"payPeriodId": "1070078709681258", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-07-27T00:00:00Z", "endDate": "2025-08-02T00:00:00Z", "submitByDate": "2025-08-05T00:00:00Z", "checkDate": "2025-08-06T00:00:00Z", "checkCount": 0, "id": "3dcb6e5c-685b-4951-b99d-ab65f01e2a3b", "companyId": "16044551", "type": "payperiod", "_rid": "NmJkAKiCbEfp1gAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfp1gAAAAAAAA==/", "_etag": "\"9a009329-0000-0100-0000-686fe0520000\"", "_attachments": "attachments/", "_ts": 1752162386}, {"payPeriodId": "1070078863931409", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-08-03T00:00:00Z", "endDate": "2025-08-09T00:00:00Z", "submitByDate": "2025-08-12T00:00:00Z", "checkDate": "2025-08-13T00:00:00Z", "checkCount": 0, "id": "ab9c8628-97e0-4df3-86b9-9ffc58bd46a0", "companyId": "16044551", "type": "payperiod", "_rid": "NmJkAKiCbEfq1gAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfq1gAAAAAAAA==/", "_etag": "\"9a009629-0000-0100-0000-686fe0520000\"", "_attachments": "attachments/", "_ts": 1752162386}, {"payPeriodId": "1070078981884189", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-08-10T00:00:00Z", "endDate": "2025-08-16T00:00:00Z", "submitByDate": "2025-08-19T00:00:00Z", "checkDate": "2025-08-20T00:00:00Z", "checkCount": 0, "id": "e8560095-4b77-48fd-a538-32e956023779", "companyId": "16044551", "type": "payperiod", "_rid": "NmJkAKiCbEfr1gAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfr1gAAAAAAAA==/", "_etag": "\"9a009c29-0000-0100-0000-686fe0520000\"", "_attachments": "attachments/", "_ts": 1752162386}, {"payPeriodId": "1070079113264706", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-08-17T00:00:00Z", "endDate": "2025-08-23T00:00:00Z", "submitByDate": "2025-08-26T00:00:00Z", "checkDate": "2025-08-27T00:00:00Z", "checkCount": 0, "id": "171d4415-f2fa-4af0-a967-9d7c8924d765", "companyId": "16044551", "type": "payperiod", "_rid": "NmJkAKiCbEfs1gAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfs1gAAAAAAAA==/", "_etag": "\"9a009f29-0000-0100-0000-686fe0520000\"", "_attachments": "attachments/", "_ts": 1752162386}, {"payPeriodId": "1070079255314197", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-08-24T00:00:00Z", "endDate": "2025-08-30T00:00:00Z", "submitByDate": "2025-09-02T00:00:00Z", "checkDate": "2025-09-03T00:00:00Z", "checkCount": 0, "id": "499d39ae-b62f-4cbe-aa78-9e853b174171", "companyId": "16044551", "type": "payperiod", "_rid": "NmJkAKiCbEft1gAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEft1gAAAAAAAA==/", "_etag": "\"9a00a329-0000-0100-0000-686fe0520000\"", "_attachments": "attachments/", "_ts": 1752162386}, {"payPeriodId": "1070079366434907", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-08-31T00:00:00Z", "endDate": "2025-09-06T00:00:00Z", "submitByDate": "2025-09-09T00:00:00Z", "checkDate": "2025-09-10T00:00:00Z", "checkCount": 0, "id": "ae7ce63d-9117-4c63-bbc0-93ed875d0be9", "companyId": "16044551", "type": "payperiod", "_rid": "NmJkAKiCbEfu1gAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfu1gAAAAAAAA==/", "_etag": "\"9a00a629-0000-0100-0000-686fe0520000\"", "_attachments": "attachments/", "_ts": 1752162386}, {"payPeriodId": "1070079538076557", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-09-07T00:00:00Z", "endDate": "2025-09-13T00:00:00Z", "submitByDate": "2025-09-16T00:00:00Z", "checkDate": "2025-09-17T00:00:00Z", "checkCount": 0, "id": "4a962ca4-239c-486b-aaba-95f8b02aa2e4", "companyId": "16044551", "type": "payperiod", "_rid": "NmJkAKiCbEfv1gAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfv1gAAAAAAAA==/", "_etag": "\"9a00a929-0000-0100-0000-686fe0520000\"", "_attachments": "attachments/", "_ts": 1752162386}, {"payPeriodId": "1070079634819073", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-09-14T00:00:00Z", "endDate": "2025-09-20T00:00:00Z", "submitByDate": "2025-09-23T00:00:00Z", "checkDate": "2025-09-24T00:00:00Z", "checkCount": 0, "id": "3549389f-6a06-431e-991d-6237b0d7ad2e", "companyId": "16044551", "type": "payperiod", "_rid": "NmJkAKiCbEfw1gAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfw1gAAAAAAAA==/", "_etag": "\"9a00ab29-0000-0100-0000-686fe0530000\"", "_attachments": "attachments/", "_ts": 1752162387}, {"payPeriodId": "1070079762026418", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-09-21T00:00:00Z", "endDate": "2025-09-27T00:00:00Z", "submitByDate": "2025-09-30T00:00:00Z", "checkDate": "2025-10-01T00:00:00Z", "checkCount": 0, "id": "ea495704-7d90-46e7-a467-1422d624f21b", "companyId": "16044551", "type": "payperiod", "_rid": "NmJkAKiCbEfx1gAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfx1gAAAAAAAA==/", "_etag": "\"9a00ad29-0000-0100-0000-686fe0530000\"", "_attachments": "attachments/", "_ts": 1752162387}, {"payPeriodId": "1070079899958907", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-09-28T00:00:00Z", "endDate": "2025-10-04T00:00:00Z", "submitByDate": "2025-10-07T00:00:00Z", "checkDate": "2025-10-08T00:00:00Z", "checkCount": 0, "id": "f56458d1-5418-4c33-b431-bed3441c3d7c", "companyId": "16044551", "type": "payperiod", "_rid": "NmJkAKiCbEfy1gAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfy1gAAAAAAAA==/", "_etag": "\"9a00ae29-0000-0100-0000-686fe0530000\"", "_attachments": "attachments/", "_ts": 1752162387}, {"payPeriodId": "1070074886406525", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2024-12-29T00:00:00Z", "endDate": "2025-01-04T00:00:00Z", "submitByDate": "2025-01-07T00:00:00Z", "checkDate": "2025-01-08T00:00:00Z", "checkCount": 2, "id": "baa62034-39ba-45be-b8a0-c4fdd97b222e", "companyId": "16044551", "type": "payperiod", "_rid": "NmJkAKiCbEf+1gAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf+1gAAAAAAAA==/", "_etag": "\"9a00cc29-0000-0100-0000-686fe0540000\"", "_attachments": "attachments/", "_ts": 1752162388}, {"payPeriodId": "1070075018742093", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-01-05T00:00:00Z", "endDate": "2025-01-11T00:00:00Z", "submitByDate": "2025-01-14T00:00:00Z", "checkDate": "2025-01-15T00:00:00Z", "checkCount": 3, "id": "4c701375-765f-4c3b-9073-fb410f152e46", "companyId": "16044551", "type": "payperiod", "_rid": "NmJkAKiCbEf-1gAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf-1gAAAAAAAA==/", "_etag": "\"9a00ce29-0000-0100-0000-686fe0540000\"", "_attachments": "attachments/", "_ts": 1752162388}, {"payPeriodId": "1070075148197844", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-01-12T00:00:00Z", "endDate": "2025-01-18T00:00:00Z", "submitByDate": "2025-01-21T00:00:00Z", "checkDate": "2025-01-22T00:00:00Z", "checkCount": 3, "id": "a067654e-fdad-4341-8fde-ffcc16dba320", "companyId": "16044551", "type": "payperiod", "_rid": "NmJkAKiCbEcA1wAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcA1wAAAAAAAA==/", "_etag": "\"9a00d029-0000-0100-0000-686fe0540000\"", "_attachments": "attachments/", "_ts": 1752162388}, {"payPeriodId": "1070075289862789", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-01-19T00:00:00Z", "endDate": "2025-01-25T00:00:00Z", "submitByDate": "2025-01-28T00:00:00Z", "checkDate": "2025-01-29T00:00:00Z", "checkCount": 3, "id": "fe3db18b-ddb6-42a3-84ea-3757e885743e", "companyId": "16044551", "type": "payperiod", "_rid": "NmJkAKiCbEcB1wAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcB1wAAAAAAAA==/", "_etag": "\"9a00d329-0000-0100-0000-686fe0540000\"", "_attachments": "attachments/", "_ts": 1752162388}, {"payPeriodId": "1070077125207556", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-01-26T00:00:00Z", "endDate": "2025-02-01T00:00:00Z", "submitByDate": "2025-02-11T00:00:00Z", "checkDate": "2025-02-05T00:00:00Z", "checkCount": 4, "id": "9c413e03-ad88-4fad-b909-abc42fcba07d", "companyId": "16044551", "type": "payperiod", "_rid": "NmJkAKiCbEcC1wAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcC1wAAAAAAAA==/", "_etag": "\"9a00d829-0000-0100-0000-686fe0540000\"", "_attachments": "attachments/", "_ts": 1752162388}, {"payPeriodId": "1070077680026549", "status": "COMPLETED", "description": "Void", "startDate": "2025-01-26T00:00:00Z", "endDate": "2025-02-01T00:00:00Z", "submitByDate": "2025-02-18T00:00:00Z", "checkDate": "2025-02-19T00:00:00Z", "checkCount": 2, "id": "ca46f2a5-15c1-469f-b46a-d2db2e5e1b69", "companyId": "16044551", "type": "payperiod", "_rid": "NmJkAKiCbEcD1wAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcD1wAAAAAAAA==/", "_etag": "\"9a00dd29-0000-0100-0000-686fe0540000\"", "_attachments": "attachments/", "_ts": 1752162388}, {"payPeriodId": "1070077262118268", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-02-02T00:00:00Z", "endDate": "2025-02-08T00:00:00Z", "submitByDate": "2025-02-11T00:00:00Z", "checkDate": "2025-02-12T00:00:00Z", "checkCount": 3, "id": "54060417-1eb9-4996-b581-9f6289678344", "companyId": "16044551", "type": "payperiod", "_rid": "NmJkAKiCbEcE1wAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcE1wAAAAAAAA==/", "_etag": "\"9a00df29-0000-0100-0000-686fe0540000\"", "_attachments": "attachments/", "_ts": 1752162388}, {"payPeriodId": "1070075548967384", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-02-09T00:00:00Z", "endDate": "2025-02-15T00:00:00Z", "submitByDate": "2025-02-18T00:00:00Z", "checkDate": "2025-02-19T00:00:00Z", "checkCount": 6, "id": "7fefbecb-f9d1-4bf1-8e51-58fb2eca44f5", "companyId": "16044551", "type": "payperiod", "_rid": "NmJkAKiCbEcF1wAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcF1wAAAAAAAA==/", "_etag": "\"9a00e229-0000-0100-0000-686fe0540000\"", "_attachments": "attachments/", "_ts": 1752162388}, {"payPeriodId": "1070075672290593", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-02-16T00:00:00Z", "endDate": "2025-02-22T00:00:00Z", "submitByDate": "2025-02-25T00:00:00Z", "checkDate": "2025-02-26T00:00:00Z", "checkCount": 6, "id": "ec845b02-65be-46a3-8e46-efcbfe40c772", "companyId": "16044551", "type": "payperiod", "_rid": "NmJkAKiCbEcG1wAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcG1wAAAAAAAA==/", "_etag": "\"9a00e829-0000-0100-0000-686fe0540000\"", "_attachments": "attachments/", "_ts": 1752162388}, {"payPeriodId": "1070075823380151", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-02-23T00:00:00Z", "endDate": "2025-03-01T00:00:00Z", "submitByDate": "2025-03-04T00:00:00Z", "checkDate": "2025-03-05T00:00:00Z", "checkCount": 5, "id": "b50748a6-a096-42dd-9e76-f56145c38819", "companyId": "16044551", "type": "payperiod", "_rid": "NmJkAKiCbEcH1wAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcH1wAAAAAAAA==/", "_etag": "\"9a00ea29-0000-0100-0000-686fe0540000\"", "_attachments": "attachments/", "_ts": 1752162388}, {"payPeriodId": "1070075929147526", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-03-02T00:00:00Z", "endDate": "2025-03-08T00:00:00Z", "submitByDate": "2025-03-11T00:00:00Z", "checkDate": "2025-03-12T00:00:00Z", "checkCount": 5, "id": "15f1e8dc-db6a-4b21-a747-53d4125b3ad3", "companyId": "16044551", "type": "payperiod", "_rid": "NmJkAKiCbEcI1wAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcI1wAAAAAAAA==/", "_etag": "\"9a00eb29-0000-0100-0000-686fe0540000\"", "_attachments": "attachments/", "_ts": 1752162388}, {"payPeriodId": "1070076070901499", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-03-09T00:00:00Z", "endDate": "2025-03-15T00:00:00Z", "submitByDate": "2025-03-18T00:00:00Z", "checkDate": "2025-03-19T00:00:00Z", "checkCount": 5, "id": "98283db5-a825-4253-814b-4beb4220d373", "companyId": "16044551", "type": "payperiod", "_rid": "NmJkAKiCbEcJ1wAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcJ1wAAAAAAAA==/", "_etag": "\"9a00ed29-0000-0100-0000-686fe0540000\"", "_attachments": "attachments/", "_ts": 1752162388}, {"payPeriodId": "1070076205845872", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-03-16T00:00:00Z", "endDate": "2025-03-22T00:00:00Z", "submitByDate": "2025-03-25T00:00:00Z", "checkDate": "2025-03-26T00:00:00Z", "checkCount": 5, "id": "2cc3bfa2-dcce-4538-98c1-0fc812a46c77", "companyId": "16044551", "type": "payperiod", "_rid": "NmJkAKiCbEcK1wAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcK1wAAAAAAAA==/", "_etag": "\"9a00ef29-0000-0100-0000-686fe0540000\"", "_attachments": "attachments/", "_ts": 1752162388}, {"payPeriodId": "1070076297858020", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-03-23T00:00:00Z", "endDate": "2025-03-29T00:00:00Z", "submitByDate": "2025-04-01T00:00:00Z", "checkDate": "2025-04-02T00:00:00Z", "checkCount": 5, "id": "d4a7a5ac-1de9-4780-aa3c-a161df23bfc8", "companyId": "16044551", "type": "payperiod", "_rid": "NmJkAKiCbEcL1wAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcL1wAAAAAAAA==/", "_etag": "\"9a00f129-0000-0100-0000-686fe0550000\"", "_attachments": "attachments/", "_ts": 1752162389}, {"payPeriodId": "1070076476749810", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-03-30T00:00:00Z", "endDate": "2025-04-05T00:00:00Z", "submitByDate": "2025-04-08T00:00:00Z", "checkDate": "2025-04-09T00:00:00Z", "checkCount": 5, "id": "d5f873a4-5014-48c8-b53d-1e5c0b491053", "companyId": "16044551", "type": "payperiod", "_rid": "NmJkAKiCbEcM1wAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcM1wAAAAAAAA==/", "_etag": "\"9a00f329-0000-0100-0000-686fe0550000\"", "_attachments": "attachments/", "_ts": 1752162389}, {"payPeriodId": "1070076607043608", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-04-06T00:00:00Z", "endDate": "2025-04-12T00:00:00Z", "submitByDate": "2025-04-15T00:00:00Z", "checkDate": "2025-04-16T00:00:00Z", "checkCount": 4, "id": "b44b2c2f-2d2a-4ab7-ad3e-48e9a159bb78", "companyId": "16044551", "type": "payperiod", "_rid": "NmJkAKiCbEcN1wAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcN1wAAAAAAAA==/", "_etag": "\"9a00f729-0000-0100-0000-686fe0550000\"", "_attachments": "attachments/", "_ts": 1752162389}, {"payPeriodId": "1070076734462157", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-04-13T00:00:00Z", "endDate": "2025-04-19T00:00:00Z", "submitByDate": "2025-04-22T00:00:00Z", "checkDate": "2025-04-23T00:00:00Z", "checkCount": 4, "id": "0ea8da27-0319-41f7-b529-c3da0053282b", "companyId": "16044551", "type": "payperiod", "_rid": "NmJkAKiCbEcO1wAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcO1wAAAAAAAA==/", "_etag": "\"9a00f829-0000-0100-0000-686fe0550000\"", "_attachments": "attachments/", "_ts": 1752162389}, {"payPeriodId": "1070076874836473", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-04-20T00:00:00Z", "endDate": "2025-04-26T00:00:00Z", "submitByDate": "2025-04-29T00:00:00Z", "checkDate": "2025-04-30T00:00:00Z", "checkCount": 4, "id": "fe60cd0f-dca9-4114-94cd-f07bd2ba68d9", "companyId": "16044551", "type": "payperiod", "_rid": "NmJkAKiCbEcP1wAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcP1wAAAAAAAA==/", "_etag": "\"9a00fb29-0000-0100-0000-686fe0550000\"", "_attachments": "attachments/", "_ts": 1752162389}, {"payPeriodId": "1070076995941457", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-04-27T00:00:00Z", "endDate": "2025-05-03T00:00:00Z", "submitByDate": "2025-05-06T00:00:00Z", "checkDate": "2025-05-07T00:00:00Z", "checkCount": 4, "id": "b8c54726-fedb-4bc0-9966-8eee85922fee", "companyId": "16044551", "type": "payperiod", "_rid": "NmJkAKiCbEcQ1wAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcQ1wAAAAAAAA==/", "_etag": "\"9a00ff29-0000-0100-0000-686fe0550000\"", "_attachments": "attachments/", "_ts": 1752162389}, {"payPeriodId": "1070077259396382", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-05-04T00:00:00Z", "endDate": "2025-05-10T00:00:00Z", "submitByDate": "2025-05-13T00:00:00Z", "checkDate": "2025-05-14T00:00:00Z", "checkCount": 4, "id": "5994910a-c1e4-4691-a0b7-a0baef1349a7", "companyId": "16044551", "type": "payperiod", "_rid": "NmJkAKiCbEcR1wAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcR1wAAAAAAAA==/", "_etag": "\"9a00032a-0000-0100-0000-686fe0550000\"", "_attachments": "attachments/", "_ts": 1752162389}, {"payPeriodId": "1070077386398323", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-05-11T00:00:00Z", "endDate": "2025-05-17T00:00:00Z", "submitByDate": "2025-05-20T00:00:00Z", "checkDate": "2025-05-21T00:00:00Z", "checkCount": 4, "id": "ecd9e819-946a-44b3-acc8-6cb70c962813", "companyId": "16044551", "type": "payperiod", "_rid": "NmJkAKiCbEcS1wAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcS1wAAAAAAAA==/", "_etag": "\"9a00052a-0000-0100-0000-686fe0550000\"", "_attachments": "attachments/", "_ts": 1752162389}, {"payPeriodId": "1070077390477912", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-05-18T00:00:00Z", "endDate": "2025-05-24T00:00:00Z", "submitByDate": "2025-05-27T00:00:00Z", "checkDate": "2025-05-28T00:00:00Z", "checkCount": 4, "id": "355766f8-42bd-481f-a3f0-394a915fd25e", "companyId": "16044551", "type": "payperiod", "_rid": "NmJkAKiCbEcT1wAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcT1wAAAAAAAA==/", "_etag": "\"9a00082a-0000-0100-0000-686fe0550000\"", "_attachments": "attachments/", "_ts": 1752162389}, {"payPeriodId": "1070077523756262", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-05-25T00:00:00Z", "endDate": "2025-05-31T00:00:00Z", "submitByDate": "2025-06-03T00:00:00Z", "checkDate": "2025-06-04T00:00:00Z", "checkCount": 5, "id": "c7e52a90-871c-4573-8979-0bc8ebadcc2b", "companyId": "16044551", "type": "payperiod", "_rid": "NmJkAKiCbEcU1wAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcU1wAAAAAAAA==/", "_etag": "\"9a000a2a-0000-0100-0000-686fe0550000\"", "_attachments": "attachments/", "_ts": 1752162389}, {"payPeriodId": "1070077668386224", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-06-01T00:00:00Z", "endDate": "2025-06-07T00:00:00Z", "submitByDate": "2025-06-11T00:00:00Z", "checkDate": "2025-06-12T00:00:00Z", "checkCount": 5, "id": "6c8e5d70-42f8-4368-a861-44f800f5b0fb", "companyId": "16044551", "type": "payperiod", "_rid": "NmJkAKiCbEcV1wAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcV1wAAAAAAAA==/", "_etag": "\"9a000d2a-0000-0100-0000-686fe0550000\"", "_attachments": "attachments/", "_ts": 1752162389}, {"payPeriodId": "1070077788815174", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-06-08T00:00:00Z", "endDate": "2025-06-14T00:00:00Z", "submitByDate": "2025-06-17T00:00:00Z", "checkDate": "2025-06-18T00:00:00Z", "checkCount": 5, "id": "3b31ec09-0c24-4da0-912d-2f678ebda490", "companyId": "16044551", "type": "payperiod", "_rid": "NmJkAKiCbEcW1wAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcW1wAAAAAAAA==/", "_etag": "\"9a00132a-0000-0100-0000-686fe0550000\"", "_attachments": "attachments/", "_ts": 1752162389}, {"payPeriodId": "1070077926830832", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-06-15T00:00:00Z", "endDate": "2025-06-21T00:00:00Z", "submitByDate": "2025-06-24T00:00:00Z", "checkDate": "2025-06-25T00:00:00Z", "checkCount": 4, "id": "02c7e04d-32f4-456a-bcb4-6b680588b33d", "companyId": "16044551", "type": "payperiod", "_rid": "NmJkAKiCbEcX1wAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcX1wAAAAAAAA==/", "_etag": "\"9a00172a-0000-0100-0000-686fe0550000\"", "_attachments": "attachments/", "_ts": 1752162389}, {"payPeriodId": "1070078053377840", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-06-22T00:00:00Z", "endDate": "2025-06-28T00:00:00Z", "submitByDate": "2025-07-01T00:00:00Z", "checkDate": "2025-07-02T00:00:00Z", "checkCount": 4, "id": "98a084d6-7d67-44ec-be94-c42e95771efc", "companyId": "16044551", "type": "payperiod", "_rid": "NmJkAKiCbEcY1wAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcY1wAAAAAAAA==/", "_etag": "\"9a001c2a-0000-0100-0000-686fe0550000\"", "_attachments": "attachments/", "_ts": 1752162389}, {"payPeriodId": "1070078184479527", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-06-29T00:00:00Z", "endDate": "2025-07-05T00:00:00Z", "submitByDate": "2025-07-08T00:00:00Z", "checkDate": "2025-07-09T00:00:00Z", "checkCount": 0, "id": "94721af1-9488-43fb-b816-3cc886e47119", "companyId": "16044551", "type": "payperiod", "_rid": "NmJkAKiCbEcZ1wAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcZ1wAAAAAAAA==/", "_etag": "\"9a001e2a-0000-0100-0000-686fe0560000\"", "_attachments": "attachments/", "_ts": 1752162390}, {"payPeriodId": "1070078317960996", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-07-06T00:00:00Z", "endDate": "2025-07-12T00:00:00Z", "submitByDate": "2025-07-15T00:00:00Z", "checkDate": "2025-07-16T00:00:00Z", "checkCount": 0, "id": "706f9965-840b-4395-8275-8f5d85787c32", "companyId": "16044551", "type": "payperiod", "_rid": "NmJkAKiCbEca1wAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEca1wAAAAAAAA==/", "_etag": "\"9a00202a-0000-0100-0000-686fe0560000\"", "_attachments": "attachments/", "_ts": 1752162390}, {"payPeriodId": "1070078457755470", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-07-13T00:00:00Z", "endDate": "2025-07-19T00:00:00Z", "submitByDate": "2025-07-22T00:00:00Z", "checkDate": "2025-07-23T00:00:00Z", "checkCount": 0, "id": "0cad92a6-29f1-402a-bec3-8692d7ea6447", "companyId": "16044551", "type": "payperiod", "_rid": "NmJkAKiCbEcb1wAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcb1wAAAAAAAA==/", "_etag": "\"9a00242a-0000-0100-0000-686fe0560000\"", "_attachments": "attachments/", "_ts": 1752162390}, {"payPeriodId": "1070078582295956", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-07-20T00:00:00Z", "endDate": "2025-07-26T00:00:00Z", "submitByDate": "2025-07-29T00:00:00Z", "checkDate": "2025-07-30T00:00:00Z", "checkCount": 0, "id": "7627808b-ae75-4464-9dd6-2e33463ed85b", "companyId": "16044551", "type": "payperiod", "_rid": "NmJkAKiCbEcc1wAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcc1wAAAAAAAA==/", "_etag": "\"9a00272a-0000-0100-0000-686fe0560000\"", "_attachments": "attachments/", "_ts": 1752162390}, {"payPeriodId": "1070078709681258", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-07-27T00:00:00Z", "endDate": "2025-08-02T00:00:00Z", "submitByDate": "2025-08-05T00:00:00Z", "checkDate": "2025-08-06T00:00:00Z", "checkCount": 0, "id": "5ba67b6b-4a47-4877-ac05-a0e8e11e7e1f", "companyId": "16044551", "type": "payperiod", "_rid": "NmJkAKiCbEcd1wAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcd1wAAAAAAAA==/", "_etag": "\"9a002d2a-0000-0100-0000-686fe0560000\"", "_attachments": "attachments/", "_ts": 1752162390}, {"payPeriodId": "1070078863931409", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-08-03T00:00:00Z", "endDate": "2025-08-09T00:00:00Z", "submitByDate": "2025-08-12T00:00:00Z", "checkDate": "2025-08-13T00:00:00Z", "checkCount": 0, "id": "c70fca4d-0ca4-487f-8017-249a7737f1e9", "companyId": "16044551", "type": "payperiod", "_rid": "NmJkAKiCbEce1wAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEce1wAAAAAAAA==/", "_etag": "\"9a00332a-0000-0100-0000-686fe0560000\"", "_attachments": "attachments/", "_ts": 1752162390}, {"payPeriodId": "1070078981884189", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-08-10T00:00:00Z", "endDate": "2025-08-16T00:00:00Z", "submitByDate": "2025-08-19T00:00:00Z", "checkDate": "2025-08-20T00:00:00Z", "checkCount": 0, "id": "5fc6ef14-82b6-48c7-a667-009551100db7", "companyId": "16044551", "type": "payperiod", "_rid": "NmJkAKiCbEcf1wAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcf1wAAAAAAAA==/", "_etag": "\"9a00372a-0000-0100-0000-686fe0560000\"", "_attachments": "attachments/", "_ts": 1752162390}, {"payPeriodId": "1070079113264706", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-08-17T00:00:00Z", "endDate": "2025-08-23T00:00:00Z", "submitByDate": "2025-08-26T00:00:00Z", "checkDate": "2025-08-27T00:00:00Z", "checkCount": 0, "id": "da6be70c-d51b-45b4-9bc8-3c420f93d918", "companyId": "16044551", "type": "payperiod", "_rid": "NmJkAKiCbEcg1wAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcg1wAAAAAAAA==/", "_etag": "\"9a003a2a-0000-0100-0000-686fe0560000\"", "_attachments": "attachments/", "_ts": 1752162390}, {"payPeriodId": "1070079255314197", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-08-24T00:00:00Z", "endDate": "2025-08-30T00:00:00Z", "submitByDate": "2025-09-02T00:00:00Z", "checkDate": "2025-09-03T00:00:00Z", "checkCount": 0, "id": "5f5db56b-a7f1-467a-99a4-7486b55f3423", "companyId": "16044551", "type": "payperiod", "_rid": "NmJkAKiCbEch1wAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEch1wAAAAAAAA==/", "_etag": "\"9a003f2a-0000-0100-0000-686fe0560000\"", "_attachments": "attachments/", "_ts": 1752162390}, {"payPeriodId": "1070079366434907", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-08-31T00:00:00Z", "endDate": "2025-09-06T00:00:00Z", "submitByDate": "2025-09-09T00:00:00Z", "checkDate": "2025-09-10T00:00:00Z", "checkCount": 0, "id": "dc54eb94-bfa1-435e-bf7b-5c07f3d16eef", "companyId": "16044551", "type": "payperiod", "_rid": "NmJkAKiCbEci1wAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEci1wAAAAAAAA==/", "_etag": "\"9a00432a-0000-0100-0000-686fe0560000\"", "_attachments": "attachments/", "_ts": 1752162390}, {"payPeriodId": "1070079538076557", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-09-07T00:00:00Z", "endDate": "2025-09-13T00:00:00Z", "submitByDate": "2025-09-16T00:00:00Z", "checkDate": "2025-09-17T00:00:00Z", "checkCount": 0, "id": "aa6d0004-f515-4c0d-b377-f8ff4d904e90", "companyId": "16044551", "type": "payperiod", "_rid": "NmJkAKiCbEcj1wAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcj1wAAAAAAAA==/", "_etag": "\"9a00452a-0000-0100-0000-686fe0560000\"", "_attachments": "attachments/", "_ts": 1752162390}, {"payPeriodId": "1070079634819073", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-09-14T00:00:00Z", "endDate": "2025-09-20T00:00:00Z", "submitByDate": "2025-09-23T00:00:00Z", "checkDate": "2025-09-24T00:00:00Z", "checkCount": 0, "id": "e3e7cb62-e9b0-42cd-a8d8-47fe3e0517a7", "companyId": "16044551", "type": "payperiod", "_rid": "NmJkAKiCbEck1wAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEck1wAAAAAAAA==/", "_etag": "\"9a00462a-0000-0100-0000-686fe0560000\"", "_attachments": "attachments/", "_ts": 1752162390}, {"payPeriodId": "1070079762026418", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-09-21T00:00:00Z", "endDate": "2025-09-27T00:00:00Z", "submitByDate": "2025-09-30T00:00:00Z", "checkDate": "2025-10-01T00:00:00Z", "checkCount": 0, "id": "60532def-9669-46c8-a499-515e574f25bb", "companyId": "16044551", "type": "payperiod", "_rid": "NmJkAKiCbEcl1wAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcl1wAAAAAAAA==/", "_etag": "\"9a00492a-0000-0100-0000-686fe0560000\"", "_attachments": "attachments/", "_ts": 1752162390}, {"payPeriodId": "1070079899958907", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-09-28T00:00:00Z", "endDate": "2025-10-04T00:00:00Z", "submitByDate": "2025-10-07T00:00:00Z", "checkDate": "2025-10-08T00:00:00Z", "checkCount": 0, "id": "c0513640-d7fc-4cb5-bd5d-e9435797a7dd", "companyId": "16044551", "type": "payperiod", "_rid": "NmJkAKiCbEcm1wAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcm1wAAAAAAAA==/", "_etag": "\"9a004b2a-0000-0100-0000-686fe0570000\"", "_attachments": "attachments/", "_ts": 1752162391}, {"payPeriodId": "1070074886406525", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2024-12-29T00:00:00Z", "endDate": "2025-01-04T00:00:00Z", "submitByDate": "2025-01-07T00:00:00Z", "checkDate": "2025-01-08T00:00:00Z", "checkCount": 2, "id": "e2fe9c27-65db-4f67-924a-e865288b5ceb", "companyId": "16044551", "type": "payperiod", "_rid": "NmJkAKiCbEf3zwEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf3zwEAAAAAAA==/", "_etag": "\"a000b845-0000-0100-0000-687007230000\"", "_attachments": "attachments/", "_ts": 1752172323}, {"payPeriodId": "1070075018742093", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-01-05T00:00:00Z", "endDate": "2025-01-11T00:00:00Z", "submitByDate": "2025-01-14T00:00:00Z", "checkDate": "2025-01-15T00:00:00Z", "checkCount": 3, "id": "1ddd99ab-1da6-4d31-90ca-838d82454551", "companyId": "16044551", "type": "payperiod", "_rid": "NmJkAKiCbEf4zwEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf4zwEAAAAAAA==/", "_etag": "\"a000bd45-0000-0100-0000-687007230000\"", "_attachments": "attachments/", "_ts": 1752172323}, {"payPeriodId": "1070075148197844", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-01-12T00:00:00Z", "endDate": "2025-01-18T00:00:00Z", "submitByDate": "2025-01-21T00:00:00Z", "checkDate": "2025-01-22T00:00:00Z", "checkCount": 3, "id": "6caee289-c6de-4aae-9209-9a82d7a9f2dd", "companyId": "16044551", "type": "payperiod", "_rid": "NmJkAKiCbEf5zwEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf5zwEAAAAAAA==/", "_etag": "\"a000c345-0000-0100-0000-687007230000\"", "_attachments": "attachments/", "_ts": 1752172323}, {"payPeriodId": "1070075289862789", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-01-19T00:00:00Z", "endDate": "2025-01-25T00:00:00Z", "submitByDate": "2025-01-28T00:00:00Z", "checkDate": "2025-01-29T00:00:00Z", "checkCount": 3, "id": "0297b296-7439-4cda-a99f-ae294ba2fb82", "companyId": "16044551", "type": "payperiod", "_rid": "NmJkAKiCbEf6zwEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf6zwEAAAAAAA==/", "_etag": "\"a000c745-0000-0100-0000-687007230000\"", "_attachments": "attachments/", "_ts": 1752172323}, {"payPeriodId": "1070077125207556", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-01-26T00:00:00Z", "endDate": "2025-02-01T00:00:00Z", "submitByDate": "2025-02-11T00:00:00Z", "checkDate": "2025-02-05T00:00:00Z", "checkCount": 4, "id": "b19e82f7-a5f2-43ea-abf2-c0f2759b332a", "companyId": "16044551", "type": "payperiod", "_rid": "NmJkAKiCbEf7zwEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf7zwEAAAAAAA==/", "_etag": "\"a000ca45-0000-0100-0000-687007230000\"", "_attachments": "attachments/", "_ts": 1752172323}, {"payPeriodId": "1070077680026549", "status": "COMPLETED", "description": "Void", "startDate": "2025-01-26T00:00:00Z", "endDate": "2025-02-01T00:00:00Z", "submitByDate": "2025-02-18T00:00:00Z", "checkDate": "2025-02-19T00:00:00Z", "checkCount": 2, "id": "47d1556a-7527-4a1a-b177-d85e6bf48327", "companyId": "16044551", "type": "payperiod", "_rid": "NmJkAKiCbEf8zwEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf8zwEAAAAAAA==/", "_etag": "\"a000cb45-0000-0100-0000-687007230000\"", "_attachments": "attachments/", "_ts": 1752172323}, {"payPeriodId": "1070077262118268", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-02-02T00:00:00Z", "endDate": "2025-02-08T00:00:00Z", "submitByDate": "2025-02-11T00:00:00Z", "checkDate": "2025-02-12T00:00:00Z", "checkCount": 3, "id": "0b6734ed-f7a9-4446-b454-5c94787eb480", "companyId": "16044551", "type": "payperiod", "_rid": "NmJkAKiCbEf9zwEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf9zwEAAAAAAA==/", "_etag": "\"a000cf45-0000-0100-0000-687007240000\"", "_attachments": "attachments/", "_ts": 1752172324}, {"payPeriodId": "1070075548967384", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-02-09T00:00:00Z", "endDate": "2025-02-15T00:00:00Z", "submitByDate": "2025-02-18T00:00:00Z", "checkDate": "2025-02-19T00:00:00Z", "checkCount": 6, "id": "04d4cf34-cc21-42e5-84d1-55fb58cd2274", "companyId": "16044551", "type": "payperiod", "_rid": "NmJkAKiCbEf+zwEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf+zwEAAAAAAA==/", "_etag": "\"a000d145-0000-0100-0000-687007240000\"", "_attachments": "attachments/", "_ts": 1752172324}, {"payPeriodId": "1070075672290593", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-02-16T00:00:00Z", "endDate": "2025-02-22T00:00:00Z", "submitByDate": "2025-02-25T00:00:00Z", "checkDate": "2025-02-26T00:00:00Z", "checkCount": 6, "id": "5dcb489f-9c44-4a9b-bdab-6f43decb5720", "companyId": "16044551", "type": "payperiod", "_rid": "NmJkAKiCbEf-zwEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf-zwEAAAAAAA==/", "_etag": "\"a000d245-0000-0100-0000-687007240000\"", "_attachments": "attachments/", "_ts": 1752172324}, {"payPeriodId": "1070075823380151", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-02-23T00:00:00Z", "endDate": "2025-03-01T00:00:00Z", "submitByDate": "2025-03-04T00:00:00Z", "checkDate": "2025-03-05T00:00:00Z", "checkCount": 5, "id": "0521075d-d52e-488f-bcde-f21c9aebb139", "companyId": "16044551", "type": "payperiod", "_rid": "NmJkAKiCbEcA0AEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcA0AEAAAAAAA==/", "_etag": "\"a000d545-0000-0100-0000-687007240000\"", "_attachments": "attachments/", "_ts": 1752172324}, {"payPeriodId": "1070075929147526", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-03-02T00:00:00Z", "endDate": "2025-03-08T00:00:00Z", "submitByDate": "2025-03-11T00:00:00Z", "checkDate": "2025-03-12T00:00:00Z", "checkCount": 5, "id": "03b73391-2b11-48df-89d3-0e5261269044", "companyId": "16044551", "type": "payperiod", "_rid": "NmJkAKiCbEcB0AEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcB0AEAAAAAAA==/", "_etag": "\"a000d845-0000-0100-0000-687007240000\"", "_attachments": "attachments/", "_ts": 1752172324}, {"payPeriodId": "1070076070901499", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-03-09T00:00:00Z", "endDate": "2025-03-15T00:00:00Z", "submitByDate": "2025-03-18T00:00:00Z", "checkDate": "2025-03-19T00:00:00Z", "checkCount": 5, "id": "db72c4d5-c51f-4751-a766-f219275b997a", "companyId": "16044551", "type": "payperiod", "_rid": "NmJkAKiCbEcC0AEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcC0AEAAAAAAA==/", "_etag": "\"a000db45-0000-0100-0000-687007240000\"", "_attachments": "attachments/", "_ts": 1752172324}, {"payPeriodId": "1070076205845872", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-03-16T00:00:00Z", "endDate": "2025-03-22T00:00:00Z", "submitByDate": "2025-03-25T00:00:00Z", "checkDate": "2025-03-26T00:00:00Z", "checkCount": 5, "id": "f585a5df-8752-4b99-95d9-ad7ccdb5a1ce", "companyId": "16044551", "type": "payperiod", "_rid": "NmJkAKiCbEcD0AEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcD0AEAAAAAAA==/", "_etag": "\"a000de45-0000-0100-0000-687007240000\"", "_attachments": "attachments/", "_ts": 1752172324}, {"payPeriodId": "1070076297858020", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-03-23T00:00:00Z", "endDate": "2025-03-29T00:00:00Z", "submitByDate": "2025-04-01T00:00:00Z", "checkDate": "2025-04-02T00:00:00Z", "checkCount": 0, "id": "58a9f66b-1dc3-4ace-a892-0b710498b508", "companyId": "16044551", "type": "payperiod", "_rid": "NmJkAKiCbEcE0AEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcE0AEAAAAAAA==/", "_etag": "\"a000e145-0000-0100-0000-687007240000\"", "_attachments": "attachments/", "_ts": 1752172324}, {"payPeriodId": "1070076476749810", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-03-30T00:00:00Z", "endDate": "2025-04-05T00:00:00Z", "submitByDate": "2025-04-08T00:00:00Z", "checkDate": "2025-04-09T00:00:00Z", "checkCount": 0, "id": "eabd7fac-cfc2-4213-ae00-ecc2188a92da", "companyId": "16044551", "type": "payperiod", "_rid": "NmJkAKiCbEcF0AEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcF0AEAAAAAAA==/", "_etag": "\"a000e445-0000-0100-0000-687007240000\"", "_attachments": "attachments/", "_ts": 1752172324}, {"payPeriodId": "1070076607043608", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-04-06T00:00:00Z", "endDate": "2025-04-12T00:00:00Z", "submitByDate": "2025-04-15T00:00:00Z", "checkDate": "2025-04-16T00:00:00Z", "checkCount": 0, "id": "df395f9e-99d1-41cf-b568-a5fad48b5f1c", "companyId": "16044551", "type": "payperiod", "_rid": "NmJkAKiCbEcG0AEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcG0AEAAAAAAA==/", "_etag": "\"a000e645-0000-0100-0000-687007240000\"", "_attachments": "attachments/", "_ts": 1752172324}, {"payPeriodId": "1070076734462157", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-04-13T00:00:00Z", "endDate": "2025-04-19T00:00:00Z", "submitByDate": "2025-04-22T00:00:00Z", "checkDate": "2025-04-23T00:00:00Z", "checkCount": 0, "id": "2e0a2770-4f69-4085-b923-18ad0f4d794f", "companyId": "16044551", "type": "payperiod", "_rid": "NmJkAKiCbEcH0AEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcH0AEAAAAAAA==/", "_etag": "\"a000ea45-0000-0100-0000-687007240000\"", "_attachments": "attachments/", "_ts": 1752172324}, {"payPeriodId": "1070076874836473", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-04-20T00:00:00Z", "endDate": "2025-04-26T00:00:00Z", "submitByDate": "2025-04-29T00:00:00Z", "checkDate": "2025-04-30T00:00:00Z", "checkCount": 0, "id": "21568f60-9793-4f3d-8f5c-71fba30df6e0", "companyId": "16044551", "type": "payperiod", "_rid": "NmJkAKiCbEcI0AEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcI0AEAAAAAAA==/", "_etag": "\"a000eb45-0000-0100-0000-687007240000\"", "_attachments": "attachments/", "_ts": 1752172324}, {"payPeriodId": "1070076995941457", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-04-27T00:00:00Z", "endDate": "2025-05-03T00:00:00Z", "submitByDate": "2025-05-06T00:00:00Z", "checkDate": "2025-05-07T00:00:00Z", "checkCount": 0, "id": "498f08ef-21c3-4c92-90d2-78702e44decf", "companyId": "16044551", "type": "payperiod", "_rid": "NmJkAKiCbEcJ0AEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcJ0AEAAAAAAA==/", "_etag": "\"a000ec45-0000-0100-0000-687007250000\"", "_attachments": "attachments/", "_ts": 1752172325}, {"payPeriodId": "1070077259396382", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-05-04T00:00:00Z", "endDate": "2025-05-10T00:00:00Z", "submitByDate": "2025-05-13T00:00:00Z", "checkDate": "2025-05-14T00:00:00Z", "checkCount": 0, "id": "40cb2d82-b531-4c62-bf08-7b84a946f895", "companyId": "16044551", "type": "payperiod", "_rid": "NmJkAKiCbEcK0AEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcK0AEAAAAAAA==/", "_etag": "\"a000ee45-0000-0100-0000-687007250000\"", "_attachments": "attachments/", "_ts": 1752172325}, {"payPeriodId": "1070077386398323", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-05-11T00:00:00Z", "endDate": "2025-05-17T00:00:00Z", "submitByDate": "2025-05-20T00:00:00Z", "checkDate": "2025-05-21T00:00:00Z", "checkCount": 0, "id": "24a228bd-1954-4d86-bd73-68f9b7a986b7", "companyId": "16044551", "type": "payperiod", "_rid": "NmJkAKiCbEcL0AEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcL0AEAAAAAAA==/", "_etag": "\"a000f545-0000-0100-0000-687007250000\"", "_attachments": "attachments/", "_ts": 1752172325}, {"payPeriodId": "1070077390477912", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-05-18T00:00:00Z", "endDate": "2025-05-24T00:00:00Z", "submitByDate": "2025-05-27T00:00:00Z", "checkDate": "2025-05-28T00:00:00Z", "checkCount": 0, "id": "288cc9f2-86d6-473f-b87d-eb9d95ffc722", "companyId": "16044551", "type": "payperiod", "_rid": "NmJkAKiCbEcM0AEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcM0AEAAAAAAA==/", "_etag": "\"a000f745-0000-0100-0000-687007250000\"", "_attachments": "attachments/", "_ts": 1752172325}, {"payPeriodId": "1070077523756262", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-05-25T00:00:00Z", "endDate": "2025-05-31T00:00:00Z", "submitByDate": "2025-06-03T00:00:00Z", "checkDate": "2025-06-04T00:00:00Z", "checkCount": 0, "id": "3046e1b0-81db-45d5-9ac0-3c9f88b6d6c0", "companyId": "16044551", "type": "payperiod", "_rid": "NmJkAKiCbEcN0AEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcN0AEAAAAAAA==/", "_etag": "\"a000fa45-0000-0100-0000-687007250000\"", "_attachments": "attachments/", "_ts": 1752172325}, {"payPeriodId": "1070077668386224", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-06-01T00:00:00Z", "endDate": "2025-06-07T00:00:00Z", "submitByDate": "2025-06-11T00:00:00Z", "checkDate": "2025-06-12T00:00:00Z", "checkCount": 0, "id": "91a09804-8e94-49ff-a5c4-bc1d3afa27df", "companyId": "16044551", "type": "payperiod", "_rid": "NmJkAKiCbEcO0AEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcO0AEAAAAAAA==/", "_etag": "\"a000fd45-0000-0100-0000-687007250000\"", "_attachments": "attachments/", "_ts": 1752172325}, {"payPeriodId": "1070077788815174", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-06-08T00:00:00Z", "endDate": "2025-06-14T00:00:00Z", "submitByDate": "2025-06-17T00:00:00Z", "checkDate": "2025-06-18T00:00:00Z", "checkCount": 0, "id": "835dc13e-6752-4d5d-8557-d4d9df271ae2", "companyId": "16044551", "type": "payperiod", "_rid": "NmJkAKiCbEcP0AEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcP0AEAAAAAAA==/", "_etag": "\"a0000146-0000-0100-0000-687007250000\"", "_attachments": "attachments/", "_ts": 1752172325}, {"payPeriodId": "1070077926830832", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-06-15T00:00:00Z", "endDate": "2025-06-21T00:00:00Z", "submitByDate": "2025-06-24T00:00:00Z", "checkDate": "2025-06-25T00:00:00Z", "checkCount": 0, "id": "4cb9ac15-cbb3-4517-abba-6f03e381e972", "companyId": "16044551", "type": "payperiod", "_rid": "NmJkAKiCbEcQ0AEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcQ0AEAAAAAAA==/", "_etag": "\"a0000446-0000-0100-0000-687007250000\"", "_attachments": "attachments/", "_ts": 1752172325}, {"payPeriodId": "1070078053377840", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-06-22T00:00:00Z", "endDate": "2025-06-28T00:00:00Z", "submitByDate": "2025-07-01T00:00:00Z", "checkDate": "2025-07-02T00:00:00Z", "checkCount": 0, "id": "1c05d1a5-dabe-4c51-9642-23486695b880", "companyId": "16044551", "type": "payperiod", "_rid": "NmJkAKiCbEcR0AEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcR0AEAAAAAAA==/", "_etag": "\"a0000746-0000-0100-0000-687007250000\"", "_attachments": "attachments/", "_ts": 1752172325}, {"payPeriodId": "1070078184479527", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-06-29T00:00:00Z", "endDate": "2025-07-05T00:00:00Z", "submitByDate": "2025-07-08T00:00:00Z", "checkDate": "2025-07-09T00:00:00Z", "checkCount": 0, "id": "44cd56c4-4939-4a13-afe3-12ded0100801", "companyId": "16044551", "type": "payperiod", "_rid": "NmJkAKiCbEcS0AEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcS0AEAAAAAAA==/", "_etag": "\"a0000a46-0000-0100-0000-687007250000\"", "_attachments": "attachments/", "_ts": 1752172325}, {"payPeriodId": "1070078317960996", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-07-06T00:00:00Z", "endDate": "2025-07-12T00:00:00Z", "submitByDate": "2025-07-15T00:00:00Z", "checkDate": "2025-07-16T00:00:00Z", "checkCount": 0, "id": "3269f6a0-722a-4ccf-b082-b786afb4f601", "companyId": "16044551", "type": "payperiod", "_rid": "NmJkAKiCbEcT0AEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcT0AEAAAAAAA==/", "_etag": "\"a0000c46-0000-0100-0000-687007250000\"", "_attachments": "attachments/", "_ts": 1752172325}, {"payPeriodId": "1070078457755470", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-07-13T00:00:00Z", "endDate": "2025-07-19T00:00:00Z", "submitByDate": "2025-07-22T00:00:00Z", "checkDate": "2025-07-23T00:00:00Z", "checkCount": 0, "id": "85c0dc60-9979-4f90-b676-f571c7f1f34d", "companyId": "16044551", "type": "payperiod", "_rid": "NmJkAKiCbEcU0AEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcU0AEAAAAAAA==/", "_etag": "\"a0001046-0000-0100-0000-687007250000\"", "_attachments": "attachments/", "_ts": 1752172325}, {"payPeriodId": "1070078582295956", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-07-20T00:00:00Z", "endDate": "2025-07-26T00:00:00Z", "submitByDate": "2025-07-29T00:00:00Z", "checkDate": "2025-07-30T00:00:00Z", "checkCount": 0, "id": "05a6269b-731c-4ea9-9f59-ae599202f862", "companyId": "16044551", "type": "payperiod", "_rid": "NmJkAKiCbEcV0AEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcV0AEAAAAAAA==/", "_etag": "\"a0001346-0000-0100-0000-687007250000\"", "_attachments": "attachments/", "_ts": 1752172325}, {"payPeriodId": "1070078709681258", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-07-27T00:00:00Z", "endDate": "2025-08-02T00:00:00Z", "submitByDate": "2025-08-05T00:00:00Z", "checkDate": "2025-08-06T00:00:00Z", "checkCount": 0, "id": "2b101326-78ae-443f-a774-cec90768645d", "companyId": "16044551", "type": "payperiod", "_rid": "NmJkAKiCbEcW0AEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcW0AEAAAAAAA==/", "_etag": "\"a0001746-0000-0100-0000-687007260000\"", "_attachments": "attachments/", "_ts": 1752172326}, {"payPeriodId": "1070078863931409", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-08-03T00:00:00Z", "endDate": "2025-08-09T00:00:00Z", "submitByDate": "2025-08-12T00:00:00Z", "checkDate": "2025-08-13T00:00:00Z", "checkCount": 0, "id": "7f82461f-25c0-44e8-9a3a-7e776fbb55d8", "companyId": "16044551", "type": "payperiod", "_rid": "NmJkAKiCbEcX0AEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcX0AEAAAAAAA==/", "_etag": "\"a0001b46-0000-0100-0000-687007260000\"", "_attachments": "attachments/", "_ts": 1752172326}, {"payPeriodId": "1070078981884189", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-08-10T00:00:00Z", "endDate": "2025-08-16T00:00:00Z", "submitByDate": "2025-08-19T00:00:00Z", "checkDate": "2025-08-20T00:00:00Z", "checkCount": 0, "id": "68976ad8-2b85-4740-bafc-b00ef4f11f8e", "companyId": "16044551", "type": "payperiod", "_rid": "NmJkAKiCbEcY0AEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcY0AEAAAAAAA==/", "_etag": "\"a0001e46-0000-0100-0000-687007260000\"", "_attachments": "attachments/", "_ts": 1752172326}, {"payPeriodId": "1070079113264706", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-08-17T00:00:00Z", "endDate": "2025-08-23T00:00:00Z", "submitByDate": "2025-08-26T00:00:00Z", "checkDate": "2025-08-27T00:00:00Z", "checkCount": 0, "id": "06df887c-980f-410f-84a0-6bce1af6f8ac", "companyId": "16044551", "type": "payperiod", "_rid": "NmJkAKiCbEcZ0AEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcZ0AEAAAAAAA==/", "_etag": "\"a0001f46-0000-0100-0000-687007260000\"", "_attachments": "attachments/", "_ts": 1752172326}, {"payPeriodId": "1070079255314197", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-08-24T00:00:00Z", "endDate": "2025-08-30T00:00:00Z", "submitByDate": "2025-09-02T00:00:00Z", "checkDate": "2025-09-03T00:00:00Z", "checkCount": 0, "id": "624988bc-0335-4143-8365-2ee18ca707aa", "companyId": "16044551", "type": "payperiod", "_rid": "NmJkAKiCbEca0AEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEca0AEAAAAAAA==/", "_etag": "\"a0002346-0000-0100-0000-687007260000\"", "_attachments": "attachments/", "_ts": 1752172326}, {"payPeriodId": "1070079366434907", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-08-31T00:00:00Z", "endDate": "2025-09-06T00:00:00Z", "submitByDate": "2025-09-09T00:00:00Z", "checkDate": "2025-09-10T00:00:00Z", "checkCount": 0, "id": "8d32504e-d7b5-4d71-9394-129f2eb4d2d2", "companyId": "16044551", "type": "payperiod", "_rid": "NmJkAKiCbEcb0AEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcb0AEAAAAAAA==/", "_etag": "\"a0002546-0000-0100-0000-687007260000\"", "_attachments": "attachments/", "_ts": 1752172326}, {"payPeriodId": "1070079538076557", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-09-07T00:00:00Z", "endDate": "2025-09-13T00:00:00Z", "submitByDate": "2025-09-16T00:00:00Z", "checkDate": "2025-09-17T00:00:00Z", "checkCount": 0, "id": "0d80dc3a-1866-4078-a9ad-fdfc865e9d2c", "companyId": "16044551", "type": "payperiod", "_rid": "NmJkAKiCbEcc0AEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcc0AEAAAAAAA==/", "_etag": "\"a0002746-0000-0100-0000-687007260000\"", "_attachments": "attachments/", "_ts": 1752172326}, {"payPeriodId": "1070079634819073", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-09-14T00:00:00Z", "endDate": "2025-09-20T00:00:00Z", "submitByDate": "2025-09-23T00:00:00Z", "checkDate": "2025-09-24T00:00:00Z", "checkCount": 0, "id": "9b4a789b-6592-48a8-ae96-7e21f3338e14", "companyId": "16044551", "type": "payperiod", "_rid": "NmJkAKiCbEcd0AEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcd0AEAAAAAAA==/", "_etag": "\"a0002d46-0000-0100-0000-687007260000\"", "_attachments": "attachments/", "_ts": 1752172326}, {"payPeriodId": "1070079762026418", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-09-21T00:00:00Z", "endDate": "2025-09-27T00:00:00Z", "submitByDate": "2025-09-30T00:00:00Z", "checkDate": "2025-10-01T00:00:00Z", "checkCount": 0, "id": "a1f7d7b5-1e6f-4d16-8cfa-c0c94a317f62", "companyId": "16044551", "type": "payperiod", "_rid": "NmJkAKiCbEce0AEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEce0AEAAAAAAA==/", "_etag": "\"a0003046-0000-0100-0000-687007260000\"", "_attachments": "attachments/", "_ts": 1752172326}, {"payPeriodId": "1070079899958907", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-09-28T00:00:00Z", "endDate": "2025-10-04T00:00:00Z", "submitByDate": "2025-10-07T00:00:00Z", "checkDate": "2025-10-08T00:00:00Z", "checkCount": 0, "id": "880aa5e3-39c1-4a3a-9f2f-bfb01ffcade0", "companyId": "16044551", "type": "payperiod", "_rid": "NmJkAKiCbEcf0AEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcf0AEAAAAAAA==/", "_etag": "\"a0003246-0000-0100-0000-687007260000\"", "_attachments": "attachments/", "_ts": 1752172326}, {"payPeriodId": "1070074886406525", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2024-12-29T00:00:00Z", "endDate": "2025-01-04T00:00:00Z", "submitByDate": "2025-01-07T00:00:00Z", "checkDate": "2025-01-08T00:00:00Z", "checkCount": 2, "id": "027ece1f-a5a0-45aa-a932-672b5608ed96", "companyId": "16044551", "type": "payperiod", "_rid": "NmJkAKiCbEcr0AEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcr0AEAAAAAAA==/", "_etag": "\"a0005946-0000-0100-0000-687007270000\"", "_attachments": "attachments/", "_ts": 1752172327}, {"payPeriodId": "1070075018742093", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-01-05T00:00:00Z", "endDate": "2025-01-11T00:00:00Z", "submitByDate": "2025-01-14T00:00:00Z", "checkDate": "2025-01-15T00:00:00Z", "checkCount": 3, "id": "f0953aaa-ce81-4068-998d-84aa4c64933f", "companyId": "16044551", "type": "payperiod", "_rid": "NmJkAKiCbEcs0AEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcs0AEAAAAAAA==/", "_etag": "\"a0005b46-0000-0100-0000-687007270000\"", "_attachments": "attachments/", "_ts": 1752172327}, {"payPeriodId": "1070075148197844", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-01-12T00:00:00Z", "endDate": "2025-01-18T00:00:00Z", "submitByDate": "2025-01-21T00:00:00Z", "checkDate": "2025-01-22T00:00:00Z", "checkCount": 3, "id": "4f0de0b1-6711-4413-a3bf-a19b9de7bb2d", "companyId": "16044551", "type": "payperiod", "_rid": "NmJkAKiCbEct0AEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEct0AEAAAAAAA==/", "_etag": "\"a0005d46-0000-0100-0000-687007270000\"", "_attachments": "attachments/", "_ts": 1752172327}, {"payPeriodId": "1070075289862789", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-01-19T00:00:00Z", "endDate": "2025-01-25T00:00:00Z", "submitByDate": "2025-01-28T00:00:00Z", "checkDate": "2025-01-29T00:00:00Z", "checkCount": 3, "id": "52dce9e0-d821-482d-9561-0e2b1219fa0f", "companyId": "16044551", "type": "payperiod", "_rid": "NmJkAKiCbEcu0AEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcu0AEAAAAAAA==/", "_etag": "\"a0006046-0000-0100-0000-687007280000\"", "_attachments": "attachments/", "_ts": 1752172328}, {"payPeriodId": "1070077125207556", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-01-26T00:00:00Z", "endDate": "2025-02-01T00:00:00Z", "submitByDate": "2025-02-11T00:00:00Z", "checkDate": "2025-02-05T00:00:00Z", "checkCount": 4, "id": "95803171-3b72-41b8-883f-454740c2e5ad", "companyId": "16044551", "type": "payperiod", "_rid": "NmJkAKiCbEcv0AEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcv0AEAAAAAAA==/", "_etag": "\"a0006646-0000-0100-0000-687007280000\"", "_attachments": "attachments/", "_ts": 1752172328}, {"payPeriodId": "1070077680026549", "status": "COMPLETED", "description": "Void", "startDate": "2025-01-26T00:00:00Z", "endDate": "2025-02-01T00:00:00Z", "submitByDate": "2025-02-18T00:00:00Z", "checkDate": "2025-02-19T00:00:00Z", "checkCount": 2, "id": "e3ff4c85-ce3e-4404-8923-cfe3072fc08e", "companyId": "16044551", "type": "payperiod", "_rid": "NmJkAKiCbEcw0AEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcw0AEAAAAAAA==/", "_etag": "\"a0006946-0000-0100-0000-687007280000\"", "_attachments": "attachments/", "_ts": 1752172328}, {"payPeriodId": "1070077262118268", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-02-02T00:00:00Z", "endDate": "2025-02-08T00:00:00Z", "submitByDate": "2025-02-11T00:00:00Z", "checkDate": "2025-02-12T00:00:00Z", "checkCount": 3, "id": "896b0a1f-1d06-47ce-a60f-5e21b3e5853f", "companyId": "16044551", "type": "payperiod", "_rid": "NmJkAKiCbEcx0AEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcx0AEAAAAAAA==/", "_etag": "\"a0006d46-0000-0100-0000-687007280000\"", "_attachments": "attachments/", "_ts": 1752172328}, {"payPeriodId": "1070075548967384", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-02-09T00:00:00Z", "endDate": "2025-02-15T00:00:00Z", "submitByDate": "2025-02-18T00:00:00Z", "checkDate": "2025-02-19T00:00:00Z", "checkCount": 6, "id": "6c84e17a-0135-4b04-a347-0b62e7044bce", "companyId": "16044551", "type": "payperiod", "_rid": "NmJkAKiCbEcy0AEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcy0AEAAAAAAA==/", "_etag": "\"a0006e46-0000-0100-0000-687007280000\"", "_attachments": "attachments/", "_ts": 1752172328}, {"payPeriodId": "1070075672290593", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-02-16T00:00:00Z", "endDate": "2025-02-22T00:00:00Z", "submitByDate": "2025-02-25T00:00:00Z", "checkDate": "2025-02-26T00:00:00Z", "checkCount": 6, "id": "32fab9d3-0441-426e-9594-9cfa3118d01b", "companyId": "16044551", "type": "payperiod", "_rid": "NmJkAKiCbEcz0AEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcz0AEAAAAAAA==/", "_etag": "\"a0007346-0000-0100-0000-687007280000\"", "_attachments": "attachments/", "_ts": 1752172328}, {"payPeriodId": "1070075823380151", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-02-23T00:00:00Z", "endDate": "2025-03-01T00:00:00Z", "submitByDate": "2025-03-04T00:00:00Z", "checkDate": "2025-03-05T00:00:00Z", "checkCount": 5, "id": "f36e9575-a67d-4da1-8df6-24b54befe629", "companyId": "16044551", "type": "payperiod", "_rid": "NmJkAKiCbEc00AEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc00AEAAAAAAA==/", "_etag": "\"a0007446-0000-0100-0000-687007280000\"", "_attachments": "attachments/", "_ts": 1752172328}, {"payPeriodId": "1070075929147526", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-03-02T00:00:00Z", "endDate": "2025-03-08T00:00:00Z", "submitByDate": "2025-03-11T00:00:00Z", "checkDate": "2025-03-12T00:00:00Z", "checkCount": 5, "id": "f713d016-2d14-4f88-939c-397b3e7bedae", "companyId": "16044551", "type": "payperiod", "_rid": "NmJkAKiCbEc10AEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc10AEAAAAAAA==/", "_etag": "\"a0007a46-0000-0100-0000-687007280000\"", "_attachments": "attachments/", "_ts": 1752172328}, {"payPeriodId": "1070076070901499", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-03-09T00:00:00Z", "endDate": "2025-03-15T00:00:00Z", "submitByDate": "2025-03-18T00:00:00Z", "checkDate": "2025-03-19T00:00:00Z", "checkCount": 5, "id": "42d1c6b7-3d09-4b71-8f0f-d293d8701b57", "companyId": "16044551", "type": "payperiod", "_rid": "NmJkAKiCbEc20AEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc20AEAAAAAAA==/", "_etag": "\"a0007d46-0000-0100-0000-687007280000\"", "_attachments": "attachments/", "_ts": 1752172328}, {"payPeriodId": "1070076205845872", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-03-16T00:00:00Z", "endDate": "2025-03-22T00:00:00Z", "submitByDate": "2025-03-25T00:00:00Z", "checkDate": "2025-03-26T00:00:00Z", "checkCount": 5, "id": "0d11a54b-855a-42b4-8465-0eac2f215bd4", "companyId": "16044551", "type": "payperiod", "_rid": "NmJkAKiCbEc30AEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc30AEAAAAAAA==/", "_etag": "\"a0008046-0000-0100-0000-687007280000\"", "_attachments": "attachments/", "_ts": 1752172328}, {"payPeriodId": "1070076297858020", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-03-23T00:00:00Z", "endDate": "2025-03-29T00:00:00Z", "submitByDate": "2025-04-01T00:00:00Z", "checkDate": "2025-04-02T00:00:00Z", "checkCount": 5, "id": "3b1d9add-a91b-4933-ad7a-e5afcfc57714", "companyId": "16044551", "type": "payperiod", "_rid": "NmJkAKiCbEc40AEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc40AEAAAAAAA==/", "_etag": "\"a0008246-0000-0100-0000-687007280000\"", "_attachments": "attachments/", "_ts": 1752172328}, {"payPeriodId": "1070076476749810", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-03-30T00:00:00Z", "endDate": "2025-04-05T00:00:00Z", "submitByDate": "2025-04-08T00:00:00Z", "checkDate": "2025-04-09T00:00:00Z", "checkCount": 5, "id": "6c69ef16-5f02-4070-8fc4-e2f8835bedaf", "companyId": "16044551", "type": "payperiod", "_rid": "NmJkAKiCbEc50AEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc50AEAAAAAAA==/", "_etag": "\"a0008446-0000-0100-0000-687007280000\"", "_attachments": "attachments/", "_ts": 1752172328}, {"payPeriodId": "1070076607043608", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-04-06T00:00:00Z", "endDate": "2025-04-12T00:00:00Z", "submitByDate": "2025-04-15T00:00:00Z", "checkDate": "2025-04-16T00:00:00Z", "checkCount": 4, "id": "c879f357-22dd-414e-8a40-db7ef47595e9", "companyId": "16044551", "type": "payperiod", "_rid": "NmJkAKiCbEc60AEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc60AEAAAAAAA==/", "_etag": "\"a0008746-0000-0100-0000-687007280000\"", "_attachments": "attachments/", "_ts": 1752172328}, {"payPeriodId": "1070076734462157", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-04-13T00:00:00Z", "endDate": "2025-04-19T00:00:00Z", "submitByDate": "2025-04-22T00:00:00Z", "checkDate": "2025-04-23T00:00:00Z", "checkCount": 4, "id": "c2f21dbf-2f01-4292-af4a-8a0a7ee4a153", "companyId": "16044551", "type": "payperiod", "_rid": "NmJkAKiCbEc70AEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc70AEAAAAAAA==/", "_etag": "\"a0008946-0000-0100-0000-687007290000\"", "_attachments": "attachments/", "_ts": 1752172329}, {"payPeriodId": "1070076874836473", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-04-20T00:00:00Z", "endDate": "2025-04-26T00:00:00Z", "submitByDate": "2025-04-29T00:00:00Z", "checkDate": "2025-04-30T00:00:00Z", "checkCount": 4, "id": "2897e6c8-cc64-4374-ab15-1e2b22ba74cb", "companyId": "16044551", "type": "payperiod", "_rid": "NmJkAKiCbEc80AEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc80AEAAAAAAA==/", "_etag": "\"a0008b46-0000-0100-0000-687007290000\"", "_attachments": "attachments/", "_ts": 1752172329}, {"payPeriodId": "1070076995941457", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-04-27T00:00:00Z", "endDate": "2025-05-03T00:00:00Z", "submitByDate": "2025-05-06T00:00:00Z", "checkDate": "2025-05-07T00:00:00Z", "checkCount": 4, "id": "ddfcf155-4860-4640-aefd-0ca809a4a87e", "companyId": "16044551", "type": "payperiod", "_rid": "NmJkAKiCbEc90AEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc90AEAAAAAAA==/", "_etag": "\"a0008c46-0000-0100-0000-687007290000\"", "_attachments": "attachments/", "_ts": 1752172329}, {"payPeriodId": "1070077259396382", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-05-04T00:00:00Z", "endDate": "2025-05-10T00:00:00Z", "submitByDate": "2025-05-13T00:00:00Z", "checkDate": "2025-05-14T00:00:00Z", "checkCount": 4, "id": "853124a0-c71e-4ff0-88f9-122acfcb8167", "companyId": "16044551", "type": "payperiod", "_rid": "NmJkAKiCbEc+0AEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc+0AEAAAAAAA==/", "_etag": "\"a0008e46-0000-0100-0000-687007290000\"", "_attachments": "attachments/", "_ts": 1752172329}, {"payPeriodId": "1070077386398323", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-05-11T00:00:00Z", "endDate": "2025-05-17T00:00:00Z", "submitByDate": "2025-05-20T00:00:00Z", "checkDate": "2025-05-21T00:00:00Z", "checkCount": 4, "id": "9210fad8-95d5-4a23-a75f-6ea7c75314a0", "companyId": "16044551", "type": "payperiod", "_rid": "NmJkAKiCbEc-0AEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc-0AEAAAAAAA==/", "_etag": "\"a0009146-0000-0100-0000-687007290000\"", "_attachments": "attachments/", "_ts": 1752172329}, {"payPeriodId": "1070077390477912", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-05-18T00:00:00Z", "endDate": "2025-05-24T00:00:00Z", "submitByDate": "2025-05-27T00:00:00Z", "checkDate": "2025-05-28T00:00:00Z", "checkCount": 4, "id": "65917be2-1a72-446b-8e45-965fd6226b95", "companyId": "16044551", "type": "payperiod", "_rid": "NmJkAKiCbEdA0AEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdA0AEAAAAAAA==/", "_etag": "\"a0009346-0000-0100-0000-687007290000\"", "_attachments": "attachments/", "_ts": 1752172329}, {"payPeriodId": "1070077523756262", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-05-25T00:00:00Z", "endDate": "2025-05-31T00:00:00Z", "submitByDate": "2025-06-03T00:00:00Z", "checkDate": "2025-06-04T00:00:00Z", "checkCount": 5, "id": "d5fb41da-efea-45ec-a9a8-ca086d2ef35b", "companyId": "16044551", "type": "payperiod", "_rid": "NmJkAKiCbEdB0AEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdB0AEAAAAAAA==/", "_etag": "\"a0009546-0000-0100-0000-687007290000\"", "_attachments": "attachments/", "_ts": 1752172329}, {"payPeriodId": "1070077668386224", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-06-01T00:00:00Z", "endDate": "2025-06-07T00:00:00Z", "submitByDate": "2025-06-11T00:00:00Z", "checkDate": "2025-06-12T00:00:00Z", "checkCount": 5, "id": "fd280bc6-21e4-45a3-8e2b-f37518dca5a7", "companyId": "16044551", "type": "payperiod", "_rid": "NmJkAKiCbEdC0AEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdC0AEAAAAAAA==/", "_etag": "\"a0009746-0000-0100-0000-687007290000\"", "_attachments": "attachments/", "_ts": 1752172329}, {"payPeriodId": "1070077788815174", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-06-08T00:00:00Z", "endDate": "2025-06-14T00:00:00Z", "submitByDate": "2025-06-17T00:00:00Z", "checkDate": "2025-06-18T00:00:00Z", "checkCount": 5, "id": "40f97340-4b37-4e4f-a9a7-362d673b1dff", "companyId": "16044551", "type": "payperiod", "_rid": "NmJkAKiCbEdD0AEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdD0AEAAAAAAA==/", "_etag": "\"a0009d46-0000-0100-0000-687007290000\"", "_attachments": "attachments/", "_ts": 1752172329}, {"payPeriodId": "1070077926830832", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-06-15T00:00:00Z", "endDate": "2025-06-21T00:00:00Z", "submitByDate": "2025-06-24T00:00:00Z", "checkDate": "2025-06-25T00:00:00Z", "checkCount": 4, "id": "70722ae4-25a5-4ea0-b718-3c5e707c7c6a", "companyId": "16044551", "type": "payperiod", "_rid": "NmJkAKiCbEdE0AEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdE0AEAAAAAAA==/", "_etag": "\"a000a246-0000-0100-0000-687007290000\"", "_attachments": "attachments/", "_ts": 1752172329}, {"payPeriodId": "1070078053377840", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-06-22T00:00:00Z", "endDate": "2025-06-28T00:00:00Z", "submitByDate": "2025-07-01T00:00:00Z", "checkDate": "2025-07-02T00:00:00Z", "checkCount": 4, "id": "a66d8bd2-42ac-4c0f-af99-6094d573fc05", "companyId": "16044551", "type": "payperiod", "_rid": "NmJkAKiCbEdF0AEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdF0AEAAAAAAA==/", "_etag": "\"a000a446-0000-0100-0000-687007290000\"", "_attachments": "attachments/", "_ts": 1752172329}, {"payPeriodId": "1070078184479527", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-06-29T00:00:00Z", "endDate": "2025-07-05T00:00:00Z", "submitByDate": "2025-07-08T00:00:00Z", "checkDate": "2025-07-09T00:00:00Z", "checkCount": 0, "id": "d9476af9-034f-4586-a7df-a3c27416d75a", "companyId": "16044551", "type": "payperiod", "_rid": "NmJkAKiCbEdG0AEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdG0AEAAAAAAA==/", "_etag": "\"a000a946-0000-0100-0000-687007290000\"", "_attachments": "attachments/", "_ts": 1752172329}, {"payPeriodId": "1070078317960996", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-07-06T00:00:00Z", "endDate": "2025-07-12T00:00:00Z", "submitByDate": "2025-07-15T00:00:00Z", "checkDate": "2025-07-16T00:00:00Z", "checkCount": 0, "id": "c12a4cf5-b3d5-424f-8209-8af84eccb79c", "companyId": "16044551", "type": "payperiod", "_rid": "NmJkAKiCbEdH0AEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdH0AEAAAAAAA==/", "_etag": "\"a000ac46-0000-0100-0000-687007290000\"", "_attachments": "attachments/", "_ts": 1752172329}, {"payPeriodId": "1070078457755470", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-07-13T00:00:00Z", "endDate": "2025-07-19T00:00:00Z", "submitByDate": "2025-07-22T00:00:00Z", "checkDate": "2025-07-23T00:00:00Z", "checkCount": 0, "id": "eb76c5e2-fbc9-43c0-bb10-8e82fec4515f", "companyId": "16044551", "type": "payperiod", "_rid": "NmJkAKiCbEdI0AEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdI0AEAAAAAAA==/", "_etag": "\"a000b146-0000-0100-0000-6870072a0000\"", "_attachments": "attachments/", "_ts": 1752172330}, {"payPeriodId": "1070078582295956", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-07-20T00:00:00Z", "endDate": "2025-07-26T00:00:00Z", "submitByDate": "2025-07-29T00:00:00Z", "checkDate": "2025-07-30T00:00:00Z", "checkCount": 0, "id": "a61e94cf-ccbc-43de-9abd-40a4ee7c88fe", "companyId": "16044551", "type": "payperiod", "_rid": "NmJkAKiCbEdJ0AEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdJ0AEAAAAAAA==/", "_etag": "\"a000b246-0000-0100-0000-6870072a0000\"", "_attachments": "attachments/", "_ts": 1752172330}, {"payPeriodId": "1070078709681258", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-07-27T00:00:00Z", "endDate": "2025-08-02T00:00:00Z", "submitByDate": "2025-08-05T00:00:00Z", "checkDate": "2025-08-06T00:00:00Z", "checkCount": 0, "id": "c0d4e87e-d1f0-491e-bdb5-7779baecc13a", "companyId": "16044551", "type": "payperiod", "_rid": "NmJkAKiCbEdK0AEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdK0AEAAAAAAA==/", "_etag": "\"a000b446-0000-0100-0000-6870072a0000\"", "_attachments": "attachments/", "_ts": 1752172330}, {"payPeriodId": "1070078863931409", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-08-03T00:00:00Z", "endDate": "2025-08-09T00:00:00Z", "submitByDate": "2025-08-12T00:00:00Z", "checkDate": "2025-08-13T00:00:00Z", "checkCount": 0, "id": "3f3305ff-8e6d-41d0-98b7-e1903bd6ec84", "companyId": "16044551", "type": "payperiod", "_rid": "NmJkAKiCbEdL0AEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdL0AEAAAAAAA==/", "_etag": "\"a000b946-0000-0100-0000-6870072a0000\"", "_attachments": "attachments/", "_ts": 1752172330}, {"payPeriodId": "1070078981884189", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-08-10T00:00:00Z", "endDate": "2025-08-16T00:00:00Z", "submitByDate": "2025-08-19T00:00:00Z", "checkDate": "2025-08-20T00:00:00Z", "checkCount": 0, "id": "f31e40a3-3345-462b-afe4-ddc26c837ce2", "companyId": "16044551", "type": "payperiod", "_rid": "NmJkAKiCbEdM0AEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdM0AEAAAAAAA==/", "_etag": "\"a000bb46-0000-0100-0000-6870072a0000\"", "_attachments": "attachments/", "_ts": 1752172330}, {"payPeriodId": "1070079113264706", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-08-17T00:00:00Z", "endDate": "2025-08-23T00:00:00Z", "submitByDate": "2025-08-26T00:00:00Z", "checkDate": "2025-08-27T00:00:00Z", "checkCount": 0, "id": "cd3af1be-07ea-49ea-adea-541a0a77c80f", "companyId": "16044551", "type": "payperiod", "_rid": "NmJkAKiCbEdN0AEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdN0AEAAAAAAA==/", "_etag": "\"a000c046-0000-0100-0000-6870072a0000\"", "_attachments": "attachments/", "_ts": 1752172330}, {"payPeriodId": "1070079255314197", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-08-24T00:00:00Z", "endDate": "2025-08-30T00:00:00Z", "submitByDate": "2025-09-02T00:00:00Z", "checkDate": "2025-09-03T00:00:00Z", "checkCount": 0, "id": "6804cadc-baa9-4852-a3c0-de53379f0c23", "companyId": "16044551", "type": "payperiod", "_rid": "NmJkAKiCbEdO0AEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdO0AEAAAAAAA==/", "_etag": "\"a000c246-0000-0100-0000-6870072a0000\"", "_attachments": "attachments/", "_ts": 1752172330}, {"payPeriodId": "1070079366434907", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-08-31T00:00:00Z", "endDate": "2025-09-06T00:00:00Z", "submitByDate": "2025-09-09T00:00:00Z", "checkDate": "2025-09-10T00:00:00Z", "checkCount": 0, "id": "0e1299e7-fc4b-4d64-b1fa-7e2e7e714031", "companyId": "16044551", "type": "payperiod", "_rid": "NmJkAKiCbEdP0AEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdP0AEAAAAAAA==/", "_etag": "\"a000c746-0000-0100-0000-6870072a0000\"", "_attachments": "attachments/", "_ts": 1752172330}, {"payPeriodId": "1070079538076557", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-09-07T00:00:00Z", "endDate": "2025-09-13T00:00:00Z", "submitByDate": "2025-09-16T00:00:00Z", "checkDate": "2025-09-17T00:00:00Z", "checkCount": 0, "id": "53f043af-dcc7-490c-8fdf-83ec89e57833", "companyId": "16044551", "type": "payperiod", "_rid": "NmJkAKiCbEdQ0AEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdQ0AEAAAAAAA==/", "_etag": "\"a000cb46-0000-0100-0000-6870072a0000\"", "_attachments": "attachments/", "_ts": 1752172330}, {"payPeriodId": "1070079634819073", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-09-14T00:00:00Z", "endDate": "2025-09-20T00:00:00Z", "submitByDate": "2025-09-23T00:00:00Z", "checkDate": "2025-09-24T00:00:00Z", "checkCount": 0, "id": "6d9208ad-1ba5-4295-a6ff-ec1ec4041552", "companyId": "16044551", "type": "payperiod", "_rid": "NmJkAKiCbEdR0AEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdR0AEAAAAAAA==/", "_etag": "\"a000cd46-0000-0100-0000-6870072a0000\"", "_attachments": "attachments/", "_ts": 1752172330}, {"payPeriodId": "1070079762026418", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-09-21T00:00:00Z", "endDate": "2025-09-27T00:00:00Z", "submitByDate": "2025-09-30T00:00:00Z", "checkDate": "2025-10-01T00:00:00Z", "checkCount": 0, "id": "6c247de4-90f8-4b44-974a-b7695acd926b", "companyId": "16044551", "type": "payperiod", "_rid": "NmJkAKiCbEdS0AEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdS0AEAAAAAAA==/", "_etag": "\"a000d046-0000-0100-0000-6870072a0000\"", "_attachments": "attachments/", "_ts": 1752172330}, {"payPeriodId": "1070079899958907", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-09-28T00:00:00Z", "endDate": "2025-10-04T00:00:00Z", "submitByDate": "2025-10-07T00:00:00Z", "checkDate": "2025-10-08T00:00:00Z", "checkCount": 0, "id": "59d770fe-4681-4cba-8893-751b5ed2fcd3", "companyId": "16044551", "type": "payperiod", "_rid": "NmJkAKiCbEdT0AEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdT0AEAAAAAAA==/", "_etag": "\"a000d646-0000-0100-0000-6870072a0000\"", "_attachments": "attachments/", "_ts": 1752172330}], "links": [{"rel": "self", "href": "https://ca-payroll-ai-mock-sb-001-secure.nicebeach-8a7a52ab.eastus.azurecontainerapps.io/ca/companies/16044551/payperiods"}]}, "status_code": 200}