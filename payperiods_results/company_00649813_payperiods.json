{"success": true, "company_id": "00649813", "data": {"metadata": {"contentItemCount": 76}, "content": [{"payPeriodId": "1080039085349400", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-01-01T00:00:00Z", "endDate": "2025-01-15T00:00:00Z", "submitByDate": "2025-01-14T00:00:00Z", "checkDate": "2025-01-16T00:00:00Z", "checkCount": 14, "id": "5ebeee8c-5e10-409a-9a87-52f3bd3f9079", "companyId": "00649813", "type": "payperiod", "_rid": "NmJkAKiCbEd5FAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEd5FAAAAAAAAA==/", "_etag": "\"9700d341-0000-0100-0000-686fd0cb0000\"", "_attachments": "attachments/", "_ts": 1752158411}, {"payPeriodId": "1080039085349401", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-01-16T00:00:00Z", "endDate": "2025-01-31T00:00:00Z", "submitByDate": "2025-01-29T00:00:00Z", "checkDate": "2025-01-31T00:00:00Z", "checkCount": 14, "id": "ccfeb9a0-cc05-4936-9bad-9cb52bf6670e", "companyId": "00649813", "type": "payperiod", "_rid": "NmJkAKiCbEd6FAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEd6FAAAAAAAAA==/", "_etag": "\"9700d541-0000-0100-0000-686fd0cb0000\"", "_attachments": "attachments/", "_ts": 1752158411}, {"payPeriodId": "1080039322390646", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-02-01T00:00:00Z", "endDate": "2025-02-15T00:00:00Z", "submitByDate": "2025-02-12T00:00:00Z", "checkDate": "2025-02-14T00:00:00Z", "checkCount": 14, "id": "054a6a71-b622-4ec0-bc53-496d972277f2", "companyId": "00649813", "type": "payperiod", "_rid": "NmJkAKiCbEd7FAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEd7FAAAAAAAAA==/", "_etag": "\"9700d941-0000-0100-0000-686fd0cb0000\"", "_attachments": "attachments/", "_ts": 1752158411}, {"payPeriodId": "1080039322390647", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-02-16T00:00:00Z", "endDate": "2025-02-28T00:00:00Z", "submitByDate": "2025-02-26T00:00:00Z", "checkDate": "2025-02-28T00:00:00Z", "checkCount": 14, "id": "439333cc-6376-4a81-842c-a8d50d621d66", "companyId": "00649813", "type": "payperiod", "_rid": "NmJkAKiCbEd8FAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEd8FAAAAAAAAA==/", "_etag": "\"9700de41-0000-0100-0000-686fd0cb0000\"", "_attachments": "attachments/", "_ts": 1752158411}, {"payPeriodId": "1080039510120709", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-03-01T00:00:00Z", "endDate": "2025-03-15T00:00:00Z", "submitByDate": "2025-03-12T00:00:00Z", "checkDate": "2025-03-14T00:00:00Z", "checkCount": 14, "id": "5cc1a434-731a-4cda-8158-8cae95037221", "companyId": "00649813", "type": "payperiod", "_rid": "NmJkAKiCbEd9FAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEd9FAAAAAAAAA==/", "_etag": "\"9700e141-0000-0100-0000-686fd0cb0000\"", "_attachments": "attachments/", "_ts": 1752158411}, {"payPeriodId": "1080039510120710", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-03-16T00:00:00Z", "endDate": "2025-03-31T00:00:00Z", "submitByDate": "2025-03-28T00:00:00Z", "checkDate": "2025-04-01T00:00:00Z", "checkCount": 0, "id": "62113b23-ac91-48e7-8634-e70542cd0cfd", "companyId": "00649813", "type": "payperiod", "_rid": "NmJkAKiCbEd+FAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEd+FAAAAAAAAA==/", "_etag": "\"9700e341-0000-0100-0000-686fd0cb0000\"", "_attachments": "attachments/", "_ts": 1752158411}, {"payPeriodId": "1080039711512829", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-04-01T00:00:00Z", "endDate": "2025-04-15T00:00:00Z", "submitByDate": "2025-04-14T00:00:00Z", "checkDate": "2025-04-16T00:00:00Z", "checkCount": 0, "id": "0e033a5a-7280-4915-a677-46e8a96a0801", "companyId": "00649813", "type": "payperiod", "_rid": "NmJkAKiCbEd-FAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEd-FAAAAAAAAA==/", "_etag": "\"9700e541-0000-0100-0000-686fd0cc0000\"", "_attachments": "attachments/", "_ts": 1752158412}, {"payPeriodId": "1080040366458648", "status": "INITIAL", "description": "Correction", "startDate": "2025-04-01T00:00:00Z", "endDate": "2025-04-15T00:00:00Z", "submitByDate": "2025-04-15T00:00:00Z", "checkDate": "2025-04-16T00:00:00Z", "checkCount": 0, "id": "52daef43-d2c5-4195-aa91-011b26f38813", "companyId": "00649813", "type": "payperiod", "_rid": "NmJkAKiCbEeAFAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeAFAAAAAAAAA==/", "_etag": "\"9700e741-0000-0100-0000-686fd0cc0000\"", "_attachments": "attachments/", "_ts": 1752158412}, {"payPeriodId": "1080039711512830", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-04-16T00:00:00Z", "endDate": "2025-04-30T00:00:00Z", "submitByDate": "2025-04-29T00:00:00Z", "checkDate": "2025-05-01T00:00:00Z", "checkCount": 0, "id": "20712eb6-c212-425c-a9ad-5c32ccf00935", "companyId": "00649813", "type": "payperiod", "_rid": "NmJkAKiCbEeBFAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeBFAAAAAAAAA==/", "_etag": "\"9700ea41-0000-0100-0000-686fd0cc0000\"", "_attachments": "attachments/", "_ts": 1752158412}, {"payPeriodId": "1080039899148144", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-05-01T00:00:00Z", "endDate": "2025-05-15T00:00:00Z", "submitByDate": "2025-05-14T00:00:00Z", "checkDate": "2025-05-16T00:00:00Z", "checkCount": 0, "id": "05f09f20-d4c9-4c87-9f31-657f5dbb8169", "companyId": "00649813", "type": "payperiod", "_rid": "NmJkAKiCbEeCFAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeCFAAAAAAAAA==/", "_etag": "\"9700ee41-0000-0100-0000-686fd0cc0000\"", "_attachments": "attachments/", "_ts": 1752158412}, {"payPeriodId": "1080039899148145", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-05-16T00:00:00Z", "endDate": "2025-05-31T00:00:00Z", "submitByDate": "2025-05-28T00:00:00Z", "checkDate": "2025-05-30T00:00:00Z", "checkCount": 0, "id": "3b92d1f0-3e47-4186-ba27-b49dd99dd6b7", "companyId": "00649813", "type": "payperiod", "_rid": "NmJkAKiCbEeDFAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeDFAAAAAAAAA==/", "_etag": "\"9700f141-0000-0100-0000-686fd0cc0000\"", "_attachments": "attachments/", "_ts": 1752158412}, {"payPeriodId": "1080040127857014", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-06-01T00:00:00Z", "endDate": "2025-06-15T00:00:00Z", "submitByDate": "2025-06-12T00:00:00Z", "checkDate": "2025-06-16T00:00:00Z", "checkCount": 0, "id": "1fb41857-69d3-42a0-aa38-2ff8a609c12f", "companyId": "00649813", "type": "payperiod", "_rid": "NmJkAKiCbEeEFAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeEFAAAAAAAAA==/", "_etag": "\"9700f441-0000-0100-0000-686fd0cc0000\"", "_attachments": "attachments/", "_ts": 1752158412}, {"payPeriodId": "1080040127857015", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-06-16T00:00:00Z", "endDate": "2025-06-30T00:00:00Z", "submitByDate": "2025-06-27T00:00:00Z", "checkDate": "2025-07-01T00:00:00Z", "checkCount": 0, "id": "c8e0a01c-a39d-40b3-a7fe-79f0145f5520", "companyId": "00649813", "type": "payperiod", "_rid": "NmJkAKiCbEeFFAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeFFAAAAAAAAA==/", "_etag": "\"9700f741-0000-0100-0000-686fd0cc0000\"", "_attachments": "attachments/", "_ts": 1752158412}, {"payPeriodId": "1080040325156968", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-07-01T00:00:00Z", "endDate": "2025-07-15T00:00:00Z", "submitByDate": "2025-07-14T00:00:00Z", "checkDate": "2025-07-16T00:00:00Z", "checkCount": 0, "id": "c864f590-d345-45d8-88dc-0ed138e8461e", "companyId": "00649813", "type": "payperiod", "_rid": "NmJkAKiCbEeGFAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeGFAAAAAAAAA==/", "_etag": "\"9700fc41-0000-0100-0000-686fd0cc0000\"", "_attachments": "attachments/", "_ts": 1752158412}, {"payPeriodId": "1080040325156969", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-07-16T00:00:00Z", "endDate": "2025-07-31T00:00:00Z", "submitByDate": "2025-07-30T00:00:00Z", "checkDate": "2025-08-01T00:00:00Z", "checkCount": 0, "id": "bc82fddd-d964-4562-b30e-d773a4ffb7c5", "companyId": "00649813", "type": "payperiod", "_rid": "NmJkAKiCbEeHFAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeHFAAAAAAAAA==/", "_etag": "\"9700ff41-0000-0100-0000-686fd0cc0000\"", "_attachments": "attachments/", "_ts": 1752158412}, {"payPeriodId": "1080040539085257", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-08-01T00:00:00Z", "endDate": "2025-08-15T00:00:00Z", "submitByDate": "2025-08-13T00:00:00Z", "checkDate": "2025-08-15T00:00:00Z", "checkCount": 0, "id": "1893cfbf-afff-4910-b6d0-243bf6450cb5", "companyId": "00649813", "type": "payperiod", "_rid": "NmJkAKiCbEeIFAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeIFAAAAAAAAA==/", "_etag": "\"97000142-0000-0100-0000-686fd0cc0000\"", "_attachments": "attachments/", "_ts": 1752158412}, {"payPeriodId": "1080040539085258", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-08-16T00:00:00Z", "endDate": "2025-08-31T00:00:00Z", "submitByDate": "2025-08-27T00:00:00Z", "checkDate": "2025-08-29T00:00:00Z", "checkCount": 0, "id": "a1d5444f-a3c8-4c22-ac47-178d68125eee", "companyId": "00649813", "type": "payperiod", "_rid": "NmJkAKiCbEeJFAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeJFAAAAAAAAA==/", "_etag": "\"97000642-0000-0100-0000-686fd0cc0000\"", "_attachments": "attachments/", "_ts": 1752158412}, {"payPeriodId": "1080040773199338", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-09-01T00:00:00Z", "endDate": "2025-09-15T00:00:00Z", "submitByDate": "2025-09-12T00:00:00Z", "checkDate": "2025-09-16T00:00:00Z", "checkCount": 0, "id": "c5398b1a-2c15-4273-81e4-934daafee3ca", "companyId": "00649813", "type": "payperiod", "_rid": "NmJkAKiCbEeKFAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeKFAAAAAAAAA==/", "_etag": "\"97000a42-0000-0100-0000-686fd0cc0000\"", "_attachments": "attachments/", "_ts": 1752158412}, {"payPeriodId": "1080040773199339", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-09-16T00:00:00Z", "endDate": "2025-09-30T00:00:00Z", "submitByDate": "2025-09-29T00:00:00Z", "checkDate": "2025-10-01T00:00:00Z", "checkCount": 0, "id": "3797b435-1a2a-4778-adac-bf87f7967b6d", "companyId": "00649813", "type": "payperiod", "_rid": "NmJkAKiCbEeLFAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeLFAAAAAAAAA==/", "_etag": "\"97000b42-0000-0100-0000-686fd0cc0000\"", "_attachments": "attachments/", "_ts": 1752158412}, {"payPeriodId": "1080039085349400", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-01-01T00:00:00Z", "endDate": "2025-01-15T00:00:00Z", "submitByDate": "2025-01-14T00:00:00Z", "checkDate": "2025-01-16T00:00:00Z", "checkCount": 14, "id": "758f7530-6c29-4a01-af1b-963c7566d156", "companyId": "00649813", "type": "payperiod", "_rid": "NmJkAKiCbEfGFAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfGFAAAAAAAAA==/", "_etag": "\"9700bf42-0000-0100-0000-686fd0d10000\"", "_attachments": "attachments/", "_ts": 1752158417}, {"payPeriodId": "1080039085349401", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-01-16T00:00:00Z", "endDate": "2025-01-31T00:00:00Z", "submitByDate": "2025-01-29T00:00:00Z", "checkDate": "2025-01-31T00:00:00Z", "checkCount": 14, "id": "fefc7ab2-25fe-4f40-b559-e3a5dcf12c2a", "companyId": "00649813", "type": "payperiod", "_rid": "NmJkAKiCbEfHFAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfHFAAAAAAAAA==/", "_etag": "\"9700c142-0000-0100-0000-686fd0d10000\"", "_attachments": "attachments/", "_ts": 1752158417}, {"payPeriodId": "1080039322390646", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-02-01T00:00:00Z", "endDate": "2025-02-15T00:00:00Z", "submitByDate": "2025-02-12T00:00:00Z", "checkDate": "2025-02-14T00:00:00Z", "checkCount": 14, "id": "dc89b5de-754e-4ec9-ba44-65f1bd248b19", "companyId": "00649813", "type": "payperiod", "_rid": "NmJkAKiCbEfIFAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfIFAAAAAAAAA==/", "_etag": "\"9700c442-0000-0100-0000-686fd0d10000\"", "_attachments": "attachments/", "_ts": 1752158417}, {"payPeriodId": "1080039322390647", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-02-16T00:00:00Z", "endDate": "2025-02-28T00:00:00Z", "submitByDate": "2025-02-26T00:00:00Z", "checkDate": "2025-02-28T00:00:00Z", "checkCount": 14, "id": "0d7f763a-ec5f-4ac5-9f90-6770794f7169", "companyId": "00649813", "type": "payperiod", "_rid": "NmJkAKiCbEfJFAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfJFAAAAAAAAA==/", "_etag": "\"9700c942-0000-0100-0000-686fd0d10000\"", "_attachments": "attachments/", "_ts": 1752158417}, {"payPeriodId": "1080039510120709", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-03-01T00:00:00Z", "endDate": "2025-03-15T00:00:00Z", "submitByDate": "2025-03-12T00:00:00Z", "checkDate": "2025-03-14T00:00:00Z", "checkCount": 14, "id": "0338eec7-ed19-4871-8467-2d17dd013ccc", "companyId": "00649813", "type": "payperiod", "_rid": "NmJkAKiCbEfKFAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfKFAAAAAAAAA==/", "_etag": "\"9700cd42-0000-0100-0000-686fd0d10000\"", "_attachments": "attachments/", "_ts": 1752158417}, {"payPeriodId": "1080039510120710", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-03-16T00:00:00Z", "endDate": "2025-03-31T00:00:00Z", "submitByDate": "2025-03-28T00:00:00Z", "checkDate": "2025-04-01T00:00:00Z", "checkCount": 14, "id": "756a12af-93e8-49de-94bc-5e1901a5ca45", "companyId": "00649813", "type": "payperiod", "_rid": "NmJkAKiCbEfLFAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfLFAAAAAAAAA==/", "_etag": "\"9700d142-0000-0100-0000-686fd0d10000\"", "_attachments": "attachments/", "_ts": 1752158417}, {"payPeriodId": "1080039711512829", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-04-01T00:00:00Z", "endDate": "2025-04-15T00:00:00Z", "submitByDate": "2025-04-14T00:00:00Z", "checkDate": "2025-04-16T00:00:00Z", "checkCount": 11, "id": "12ac66cd-6d03-4a6f-95ae-ddd0e44e688f", "companyId": "00649813", "type": "payperiod", "_rid": "NmJkAKiCbEfMFAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfMFAAAAAAAAA==/", "_etag": "\"9700d242-0000-0100-0000-686fd0d10000\"", "_attachments": "attachments/", "_ts": 1752158417}, {"payPeriodId": "1080040366458648", "status": "COMPLETED", "description": "Correction", "startDate": "2025-04-01T00:00:00Z", "endDate": "2025-04-15T00:00:00Z", "submitByDate": "2025-04-15T00:00:00Z", "checkDate": "2025-04-16T00:00:00Z", "checkCount": 3, "id": "2c99ebf3-530f-4cbf-9f4f-fd09acc9f66d", "companyId": "00649813", "type": "payperiod", "_rid": "NmJkAKiCbEfNFAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfNFAAAAAAAAA==/", "_etag": "\"9700d442-0000-0100-0000-686fd0d10000\"", "_attachments": "attachments/", "_ts": 1752158417}, {"payPeriodId": "1080039711512830", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-04-16T00:00:00Z", "endDate": "2025-04-30T00:00:00Z", "submitByDate": "2025-04-29T00:00:00Z", "checkDate": "2025-05-01T00:00:00Z", "checkCount": 14, "id": "b9c17ba2-aaf3-4b0b-a1e5-c8be3f7c1589", "companyId": "00649813", "type": "payperiod", "_rid": "NmJkAKiCbEfOFAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfOFAAAAAAAAA==/", "_etag": "\"9700d842-0000-0100-0000-686fd0d10000\"", "_attachments": "attachments/", "_ts": 1752158417}, {"payPeriodId": "1080039899148144", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-05-01T00:00:00Z", "endDate": "2025-05-15T00:00:00Z", "submitByDate": "2025-05-14T00:00:00Z", "checkDate": "2025-05-16T00:00:00Z", "checkCount": 14, "id": "76ddfc63-7598-44ea-91d1-77e8fc964f21", "companyId": "00649813", "type": "payperiod", "_rid": "NmJkAKiCbEfPFAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfPFAAAAAAAAA==/", "_etag": "\"9700d942-0000-0100-0000-686fd0d10000\"", "_attachments": "attachments/", "_ts": 1752158417}, {"payPeriodId": "1080039899148145", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-05-16T00:00:00Z", "endDate": "2025-05-31T00:00:00Z", "submitByDate": "2025-05-28T00:00:00Z", "checkDate": "2025-05-30T00:00:00Z", "checkCount": 14, "id": "66ac0b81-7ee9-40ac-9042-24d54808858c", "companyId": "00649813", "type": "payperiod", "_rid": "NmJkAKiCbEfQFAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfQFAAAAAAAAA==/", "_etag": "\"9700dd42-0000-0100-0000-686fd0d10000\"", "_attachments": "attachments/", "_ts": 1752158417}, {"payPeriodId": "1080040127857014", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-06-01T00:00:00Z", "endDate": "2025-06-15T00:00:00Z", "submitByDate": "2025-06-12T00:00:00Z", "checkDate": "2025-06-16T00:00:00Z", "checkCount": 14, "id": "cbb25226-1e94-45d1-8bcc-7c1224fdf508", "companyId": "00649813", "type": "payperiod", "_rid": "NmJkAKiCbEfRFAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfRFAAAAAAAAA==/", "_etag": "\"9700e142-0000-0100-0000-686fd0d20000\"", "_attachments": "attachments/", "_ts": 1752158418}, {"payPeriodId": "1080040127857015", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-06-16T00:00:00Z", "endDate": "2025-06-30T00:00:00Z", "submitByDate": "2025-06-27T00:00:00Z", "checkDate": "2025-07-01T00:00:00Z", "checkCount": 14, "id": "baa2dec6-3f6f-47ba-8aeb-6b1be7bf472c", "companyId": "00649813", "type": "payperiod", "_rid": "NmJkAKiCbEfSFAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfSFAAAAAAAAA==/", "_etag": "\"9700e942-0000-0100-0000-686fd0d20000\"", "_attachments": "attachments/", "_ts": 1752158418}, {"payPeriodId": "1080040325156968", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-07-01T00:00:00Z", "endDate": "2025-07-15T00:00:00Z", "submitByDate": "2025-07-14T00:00:00Z", "checkDate": "2025-07-16T00:00:00Z", "checkCount": 0, "id": "2458454a-9150-41fb-b4e5-a6aecaed3bcf", "companyId": "00649813", "type": "payperiod", "_rid": "NmJkAKiCbEfTFAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfTFAAAAAAAAA==/", "_etag": "\"9700ec42-0000-0100-0000-686fd0d20000\"", "_attachments": "attachments/", "_ts": 1752158418}, {"payPeriodId": "1080040325156969", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-07-16T00:00:00Z", "endDate": "2025-07-31T00:00:00Z", "submitByDate": "2025-07-30T00:00:00Z", "checkDate": "2025-08-01T00:00:00Z", "checkCount": 0, "id": "8cea2ba4-2b15-4edd-a694-1cd3bace158a", "companyId": "00649813", "type": "payperiod", "_rid": "NmJkAKiCbEfUFAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfUFAAAAAAAAA==/", "_etag": "\"9700ee42-0000-0100-0000-686fd0d20000\"", "_attachments": "attachments/", "_ts": 1752158418}, {"payPeriodId": "1080040539085257", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-08-01T00:00:00Z", "endDate": "2025-08-15T00:00:00Z", "submitByDate": "2025-08-13T00:00:00Z", "checkDate": "2025-08-15T00:00:00Z", "checkCount": 0, "id": "f03614c9-ca14-4c31-b921-547b148fa1ee", "companyId": "00649813", "type": "payperiod", "_rid": "NmJkAKiCbEfVFAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfVFAAAAAAAAA==/", "_etag": "\"9700f142-0000-0100-0000-686fd0d20000\"", "_attachments": "attachments/", "_ts": 1752158418}, {"payPeriodId": "1080040539085258", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-08-16T00:00:00Z", "endDate": "2025-08-31T00:00:00Z", "submitByDate": "2025-08-27T00:00:00Z", "checkDate": "2025-08-29T00:00:00Z", "checkCount": 0, "id": "0e0078d1-a1d3-4342-8095-ea3fd46dfcbb", "companyId": "00649813", "type": "payperiod", "_rid": "NmJkAKiCbEfWFAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfWFAAAAAAAAA==/", "_etag": "\"9700f742-0000-0100-0000-686fd0d20000\"", "_attachments": "attachments/", "_ts": 1752158418}, {"payPeriodId": "1080040773199338", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-09-01T00:00:00Z", "endDate": "2025-09-15T00:00:00Z", "submitByDate": "2025-09-12T00:00:00Z", "checkDate": "2025-09-16T00:00:00Z", "checkCount": 0, "id": "3e3a97bc-e6a3-4d9e-b415-775bc9fbb427", "companyId": "00649813", "type": "payperiod", "_rid": "NmJkAKiCbEfXFAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfXFAAAAAAAAA==/", "_etag": "\"9700fa42-0000-0100-0000-686fd0d20000\"", "_attachments": "attachments/", "_ts": 1752158418}, {"payPeriodId": "1080040773199339", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-09-16T00:00:00Z", "endDate": "2025-09-30T00:00:00Z", "submitByDate": "2025-09-29T00:00:00Z", "checkDate": "2025-10-01T00:00:00Z", "checkCount": 0, "id": "cffa3317-e466-4024-81dd-a65dcce27661", "companyId": "00649813", "type": "payperiod", "_rid": "NmJkAKiCbEfYFAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfYFAAAAAAAAA==/", "_etag": "\"9700fc42-0000-0100-0000-686fd0d20000\"", "_attachments": "attachments/", "_ts": 1752158418}, {"payPeriodId": "1080039085349400", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-01-01T00:00:00Z", "endDate": "2025-01-15T00:00:00Z", "submitByDate": "2025-01-14T00:00:00Z", "checkDate": "2025-01-16T00:00:00Z", "checkCount": 14, "id": "ccf24c3e-a507-47ee-9034-b8bf291bd0e5", "companyId": "00649813", "type": "payperiod", "_rid": "NmJkAKiCbEemDQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEemDQEAAAAAAA==/", "_etag": "\"9d005a83-0000-0100-0000-686ff7180000\"", "_attachments": "attachments/", "_ts": 1752168216}, {"payPeriodId": "1080039085349401", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-01-16T00:00:00Z", "endDate": "2025-01-31T00:00:00Z", "submitByDate": "2025-01-29T00:00:00Z", "checkDate": "2025-01-31T00:00:00Z", "checkCount": 14, "id": "ee986c09-5711-429e-99f0-c6349dd7aaa8", "companyId": "00649813", "type": "payperiod", "_rid": "NmJkAKiCbEenDQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEenDQEAAAAAAA==/", "_etag": "\"9d005b83-0000-0100-0000-686ff7180000\"", "_attachments": "attachments/", "_ts": 1752168216}, {"payPeriodId": "1080039322390646", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-02-01T00:00:00Z", "endDate": "2025-02-15T00:00:00Z", "submitByDate": "2025-02-12T00:00:00Z", "checkDate": "2025-02-14T00:00:00Z", "checkCount": 14, "id": "b3b831ac-6ee4-4739-8516-6bd0c52ccfd6", "companyId": "00649813", "type": "payperiod", "_rid": "NmJkAKiCbEeoDQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeoDQEAAAAAAA==/", "_etag": "\"9d005d83-0000-0100-0000-686ff7180000\"", "_attachments": "attachments/", "_ts": 1752168216}, {"payPeriodId": "1080039322390647", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-02-16T00:00:00Z", "endDate": "2025-02-28T00:00:00Z", "submitByDate": "2025-02-26T00:00:00Z", "checkDate": "2025-02-28T00:00:00Z", "checkCount": 14, "id": "2e5b8e4e-ae4a-4421-beee-0e908ad7e625", "companyId": "00649813", "type": "payperiod", "_rid": "NmJkAKiCbEepDQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEepDQEAAAAAAA==/", "_etag": "\"9d005f83-0000-0100-0000-686ff7180000\"", "_attachments": "attachments/", "_ts": 1752168216}, {"payPeriodId": "1080039510120709", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-03-01T00:00:00Z", "endDate": "2025-03-15T00:00:00Z", "submitByDate": "2025-03-12T00:00:00Z", "checkDate": "2025-03-14T00:00:00Z", "checkCount": 14, "id": "8daeff16-e6f5-45b6-b4be-d612bad031ab", "companyId": "00649813", "type": "payperiod", "_rid": "NmJkAKiCbEeqDQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeqDQEAAAAAAA==/", "_etag": "\"9d006183-0000-0100-0000-686ff7180000\"", "_attachments": "attachments/", "_ts": 1752168216}, {"payPeriodId": "1080039510120710", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-03-16T00:00:00Z", "endDate": "2025-03-31T00:00:00Z", "submitByDate": "2025-03-28T00:00:00Z", "checkDate": "2025-04-01T00:00:00Z", "checkCount": 0, "id": "c42bb725-3f19-45c4-b087-5ef91ddcb09b", "companyId": "00649813", "type": "payperiod", "_rid": "NmJkAKiCbEerDQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEerDQEAAAAAAA==/", "_etag": "\"9d006583-0000-0100-0000-686ff7190000\"", "_attachments": "attachments/", "_ts": 1752168217}, {"payPeriodId": "1080039711512829", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-04-01T00:00:00Z", "endDate": "2025-04-15T00:00:00Z", "submitByDate": "2025-04-14T00:00:00Z", "checkDate": "2025-04-16T00:00:00Z", "checkCount": 0, "id": "532441a3-dd32-41b6-ae58-6fbe314a328d", "companyId": "00649813", "type": "payperiod", "_rid": "NmJkAKiCbEesDQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEesDQEAAAAAAA==/", "_etag": "\"9d006783-0000-0100-0000-686ff7190000\"", "_attachments": "attachments/", "_ts": 1752168217}, {"payPeriodId": "1080040366458648", "status": "INITIAL", "description": "Correction", "startDate": "2025-04-01T00:00:00Z", "endDate": "2025-04-15T00:00:00Z", "submitByDate": "2025-04-15T00:00:00Z", "checkDate": "2025-04-16T00:00:00Z", "checkCount": 0, "id": "71067543-8a0b-4e8c-a088-9123647ceb92", "companyId": "00649813", "type": "payperiod", "_rid": "NmJkAKiCbEetDQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEetDQEAAAAAAA==/", "_etag": "\"9d006c83-0000-0100-0000-686ff7190000\"", "_attachments": "attachments/", "_ts": 1752168217}, {"payPeriodId": "1080039711512830", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-04-16T00:00:00Z", "endDate": "2025-04-30T00:00:00Z", "submitByDate": "2025-04-29T00:00:00Z", "checkDate": "2025-05-01T00:00:00Z", "checkCount": 0, "id": "f6a43fbf-7296-4b00-85c3-308f5d8c8713", "companyId": "00649813", "type": "payperiod", "_rid": "NmJkAKiCbEeuDQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeuDQEAAAAAAA==/", "_etag": "\"9d007083-0000-0100-0000-686ff7190000\"", "_attachments": "attachments/", "_ts": 1752168217}, {"payPeriodId": "1080039899148144", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-05-01T00:00:00Z", "endDate": "2025-05-15T00:00:00Z", "submitByDate": "2025-05-14T00:00:00Z", "checkDate": "2025-05-16T00:00:00Z", "checkCount": 0, "id": "fc969347-5355-40d5-8006-4d4cabf23bbd", "companyId": "00649813", "type": "payperiod", "_rid": "NmJkAKiCbEevDQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEevDQEAAAAAAA==/", "_etag": "\"9d007283-0000-0100-0000-686ff7190000\"", "_attachments": "attachments/", "_ts": 1752168217}, {"payPeriodId": "1080039899148145", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-05-16T00:00:00Z", "endDate": "2025-05-31T00:00:00Z", "submitByDate": "2025-05-28T00:00:00Z", "checkDate": "2025-05-30T00:00:00Z", "checkCount": 0, "id": "5aeaa519-385e-4b6b-9a77-85f3668474ae", "companyId": "00649813", "type": "payperiod", "_rid": "NmJkAKiCbEewDQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEewDQEAAAAAAA==/", "_etag": "\"9d007583-0000-0100-0000-686ff7190000\"", "_attachments": "attachments/", "_ts": 1752168217}, {"payPeriodId": "1080040127857014", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-06-01T00:00:00Z", "endDate": "2025-06-15T00:00:00Z", "submitByDate": "2025-06-12T00:00:00Z", "checkDate": "2025-06-16T00:00:00Z", "checkCount": 0, "id": "818fa132-bafd-460b-91c9-ad1b88221e59", "companyId": "00649813", "type": "payperiod", "_rid": "NmJkAKiCbEexDQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEexDQEAAAAAAA==/", "_etag": "\"9d007a83-0000-0100-0000-686ff7190000\"", "_attachments": "attachments/", "_ts": 1752168217}, {"payPeriodId": "1080040127857015", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-06-16T00:00:00Z", "endDate": "2025-06-30T00:00:00Z", "submitByDate": "2025-06-27T00:00:00Z", "checkDate": "2025-07-01T00:00:00Z", "checkCount": 0, "id": "747b031a-d21a-455f-ae92-05a99cf04bdf", "companyId": "00649813", "type": "payperiod", "_rid": "NmJkAKiCbEeyDQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeyDQEAAAAAAA==/", "_etag": "\"9d007c83-0000-0100-0000-686ff7190000\"", "_attachments": "attachments/", "_ts": 1752168217}, {"payPeriodId": "1080040325156968", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-07-01T00:00:00Z", "endDate": "2025-07-15T00:00:00Z", "submitByDate": "2025-07-14T00:00:00Z", "checkDate": "2025-07-16T00:00:00Z", "checkCount": 0, "id": "373c9224-0871-4505-a679-948474db0b7d", "companyId": "00649813", "type": "payperiod", "_rid": "NmJkAKiCbEezDQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEezDQEAAAAAAA==/", "_etag": "\"9d007e83-0000-0100-0000-686ff7190000\"", "_attachments": "attachments/", "_ts": 1752168217}, {"payPeriodId": "1080040325156969", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-07-16T00:00:00Z", "endDate": "2025-07-31T00:00:00Z", "submitByDate": "2025-07-30T00:00:00Z", "checkDate": "2025-08-01T00:00:00Z", "checkCount": 0, "id": "5061e333-7174-4ebf-b7c1-a0a29bca83dc", "companyId": "00649813", "type": "payperiod", "_rid": "NmJkAKiCbEe0DQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEe0DQEAAAAAAA==/", "_etag": "\"9d007f83-0000-0100-0000-686ff7190000\"", "_attachments": "attachments/", "_ts": 1752168217}, {"payPeriodId": "1080040539085257", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-08-01T00:00:00Z", "endDate": "2025-08-15T00:00:00Z", "submitByDate": "2025-08-13T00:00:00Z", "checkDate": "2025-08-15T00:00:00Z", "checkCount": 0, "id": "6bb6a285-c43e-4b7d-8892-d828b79fe2e7", "companyId": "00649813", "type": "payperiod", "_rid": "NmJkAKiCbEe1DQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEe1DQEAAAAAAA==/", "_etag": "\"9d008083-0000-0100-0000-686ff7190000\"", "_attachments": "attachments/", "_ts": 1752168217}, {"payPeriodId": "1080040539085258", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-08-16T00:00:00Z", "endDate": "2025-08-31T00:00:00Z", "submitByDate": "2025-08-27T00:00:00Z", "checkDate": "2025-08-29T00:00:00Z", "checkCount": 0, "id": "9e349fbc-4f16-4ec4-b70f-8edb9f73ec49", "companyId": "00649813", "type": "payperiod", "_rid": "NmJkAKiCbEe2DQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEe2DQEAAAAAAA==/", "_etag": "\"9d008283-0000-0100-0000-686ff7190000\"", "_attachments": "attachments/", "_ts": 1752168217}, {"payPeriodId": "1080040773199338", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-09-01T00:00:00Z", "endDate": "2025-09-15T00:00:00Z", "submitByDate": "2025-09-12T00:00:00Z", "checkDate": "2025-09-16T00:00:00Z", "checkCount": 0, "id": "261e1d9f-1ac8-4cca-a611-38b18e00f602", "companyId": "00649813", "type": "payperiod", "_rid": "NmJkAKiCbEe3DQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEe3DQEAAAAAAA==/", "_etag": "\"9d008883-0000-0100-0000-686ff7190000\"", "_attachments": "attachments/", "_ts": 1752168217}, {"payPeriodId": "1080040773199339", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-09-16T00:00:00Z", "endDate": "2025-09-30T00:00:00Z", "submitByDate": "2025-09-29T00:00:00Z", "checkDate": "2025-10-01T00:00:00Z", "checkCount": 0, "id": "48092869-5a9e-4245-90f8-98d93ce6147c", "companyId": "00649813", "type": "payperiod", "_rid": "NmJkAKiCbEe4DQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEe4DQEAAAAAAA==/", "_etag": "\"9d008e83-0000-0100-0000-686ff71a0000\"", "_attachments": "attachments/", "_ts": 1752168218}, {"payPeriodId": "1080039085349400", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-01-01T00:00:00Z", "endDate": "2025-01-15T00:00:00Z", "submitByDate": "2025-01-14T00:00:00Z", "checkDate": "2025-01-16T00:00:00Z", "checkCount": 14, "id": "300f8e25-c9ee-4f3f-862f-302d17d87091", "companyId": "00649813", "type": "payperiod", "_rid": "NmJkAKiCbEfzDQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfzDQEAAAAAAA==/", "_etag": "\"9d003f84-0000-0100-0000-686ff71e0000\"", "_attachments": "attachments/", "_ts": 1752168222}, {"payPeriodId": "1080039085349401", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-01-16T00:00:00Z", "endDate": "2025-01-31T00:00:00Z", "submitByDate": "2025-01-29T00:00:00Z", "checkDate": "2025-01-31T00:00:00Z", "checkCount": 14, "id": "c8d0847a-5e27-4879-80c6-84dab9a52f57", "companyId": "00649813", "type": "payperiod", "_rid": "NmJkAKiCbEf0DQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf0DQEAAAAAAA==/", "_etag": "\"9d004284-0000-0100-0000-686ff71e0000\"", "_attachments": "attachments/", "_ts": 1752168222}, {"payPeriodId": "1080039322390646", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-02-01T00:00:00Z", "endDate": "2025-02-15T00:00:00Z", "submitByDate": "2025-02-12T00:00:00Z", "checkDate": "2025-02-14T00:00:00Z", "checkCount": 14, "id": "53467f7c-a25a-4163-bd08-8009f42bcdb9", "companyId": "00649813", "type": "payperiod", "_rid": "NmJkAKiCbEf1DQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf1DQEAAAAAAA==/", "_etag": "\"9d004584-0000-0100-0000-686ff71e0000\"", "_attachments": "attachments/", "_ts": 1752168222}, {"payPeriodId": "1080039322390647", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-02-16T00:00:00Z", "endDate": "2025-02-28T00:00:00Z", "submitByDate": "2025-02-26T00:00:00Z", "checkDate": "2025-02-28T00:00:00Z", "checkCount": 14, "id": "e199a072-592d-4fc8-91db-840bb01511e2", "companyId": "00649813", "type": "payperiod", "_rid": "NmJkAKiCbEf2DQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf2DQEAAAAAAA==/", "_etag": "\"9d004784-0000-0100-0000-686ff71f0000\"", "_attachments": "attachments/", "_ts": 1752168223}, {"payPeriodId": "1080039510120709", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-03-01T00:00:00Z", "endDate": "2025-03-15T00:00:00Z", "submitByDate": "2025-03-12T00:00:00Z", "checkDate": "2025-03-14T00:00:00Z", "checkCount": 14, "id": "574bb9f4-ca86-47a9-8b50-19fcc3bf1f3b", "companyId": "00649813", "type": "payperiod", "_rid": "NmJkAKiCbEf3DQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf3DQEAAAAAAA==/", "_etag": "\"9d004d84-0000-0100-0000-686ff71f0000\"", "_attachments": "attachments/", "_ts": 1752168223}, {"payPeriodId": "1080039510120710", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-03-16T00:00:00Z", "endDate": "2025-03-31T00:00:00Z", "submitByDate": "2025-03-28T00:00:00Z", "checkDate": "2025-04-01T00:00:00Z", "checkCount": 14, "id": "e446a34f-1939-4623-9d6f-36e94eaafbc6", "companyId": "00649813", "type": "payperiod", "_rid": "NmJkAKiCbEf4DQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf4DQEAAAAAAA==/", "_etag": "\"9d005384-0000-0100-0000-686ff71f0000\"", "_attachments": "attachments/", "_ts": 1752168223}, {"payPeriodId": "1080039711512829", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-04-01T00:00:00Z", "endDate": "2025-04-15T00:00:00Z", "submitByDate": "2025-04-14T00:00:00Z", "checkDate": "2025-04-16T00:00:00Z", "checkCount": 11, "id": "04a9bb08-08fe-4088-84a9-886516ced564", "companyId": "00649813", "type": "payperiod", "_rid": "NmJkAKiCbEf5DQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf5DQEAAAAAAA==/", "_etag": "\"9d005484-0000-0100-0000-686ff71f0000\"", "_attachments": "attachments/", "_ts": 1752168223}, {"payPeriodId": "1080040366458648", "status": "COMPLETED", "description": "Correction", "startDate": "2025-04-01T00:00:00Z", "endDate": "2025-04-15T00:00:00Z", "submitByDate": "2025-04-15T00:00:00Z", "checkDate": "2025-04-16T00:00:00Z", "checkCount": 3, "id": "3e0397ea-f7a9-40ad-b60c-4da5a1f452a2", "companyId": "00649813", "type": "payperiod", "_rid": "NmJkAKiCbEf6DQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf6DQEAAAAAAA==/", "_etag": "\"9d005d84-0000-0100-0000-686ff71f0000\"", "_attachments": "attachments/", "_ts": 1752168223}, {"payPeriodId": "1080039711512830", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-04-16T00:00:00Z", "endDate": "2025-04-30T00:00:00Z", "submitByDate": "2025-04-29T00:00:00Z", "checkDate": "2025-05-01T00:00:00Z", "checkCount": 14, "id": "dfe0e7fd-4704-4045-9e8f-dcf96fa095b7", "companyId": "00649813", "type": "payperiod", "_rid": "NmJkAKiCbEf7DQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf7DQEAAAAAAA==/", "_etag": "\"9d006084-0000-0100-0000-686ff71f0000\"", "_attachments": "attachments/", "_ts": 1752168223}, {"payPeriodId": "1080039899148144", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-05-01T00:00:00Z", "endDate": "2025-05-15T00:00:00Z", "submitByDate": "2025-05-14T00:00:00Z", "checkDate": "2025-05-16T00:00:00Z", "checkCount": 14, "id": "317f0f67-4059-41fe-b238-cf525f66518d", "companyId": "00649813", "type": "payperiod", "_rid": "NmJkAKiCbEf8DQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf8DQEAAAAAAA==/", "_etag": "\"9d006384-0000-0100-0000-686ff71f0000\"", "_attachments": "attachments/", "_ts": 1752168223}, {"payPeriodId": "1080039899148145", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-05-16T00:00:00Z", "endDate": "2025-05-31T00:00:00Z", "submitByDate": "2025-05-28T00:00:00Z", "checkDate": "2025-05-30T00:00:00Z", "checkCount": 14, "id": "9c45edd2-3b43-4122-aa3d-c6d3c373c849", "companyId": "00649813", "type": "payperiod", "_rid": "NmJkAKiCbEf9DQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf9DQEAAAAAAA==/", "_etag": "\"9d006684-0000-0100-0000-686ff71f0000\"", "_attachments": "attachments/", "_ts": 1752168223}, {"payPeriodId": "1080040127857014", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-06-01T00:00:00Z", "endDate": "2025-06-15T00:00:00Z", "submitByDate": "2025-06-12T00:00:00Z", "checkDate": "2025-06-16T00:00:00Z", "checkCount": 14, "id": "e60a1552-8840-401b-909b-361c4fcd8343", "companyId": "00649813", "type": "payperiod", "_rid": "NmJkAKiCbEf+DQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf+DQEAAAAAAA==/", "_etag": "\"9d006984-0000-0100-0000-686ff71f0000\"", "_attachments": "attachments/", "_ts": 1752168223}, {"payPeriodId": "1080040127857015", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-06-16T00:00:00Z", "endDate": "2025-06-30T00:00:00Z", "submitByDate": "2025-06-27T00:00:00Z", "checkDate": "2025-07-01T00:00:00Z", "checkCount": 14, "id": "5cf889a4-8986-484c-ac15-efbae0e59ac6", "companyId": "00649813", "type": "payperiod", "_rid": "NmJkAKiCbEf-DQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf-DQEAAAAAAA==/", "_etag": "\"9d006c84-0000-0100-0000-686ff71f0000\"", "_attachments": "attachments/", "_ts": 1752168223}, {"payPeriodId": "1080040325156968", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-07-01T00:00:00Z", "endDate": "2025-07-15T00:00:00Z", "submitByDate": "2025-07-14T00:00:00Z", "checkDate": "2025-07-16T00:00:00Z", "checkCount": 0, "id": "229f75e1-a984-4426-ab3b-48d301c4dc6e", "companyId": "00649813", "type": "payperiod", "_rid": "NmJkAKiCbEcADgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcADgEAAAAAAA==/", "_etag": "\"9d007184-0000-0100-0000-686ff71f0000\"", "_attachments": "attachments/", "_ts": 1752168223}, {"payPeriodId": "1080040325156969", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-07-16T00:00:00Z", "endDate": "2025-07-31T00:00:00Z", "submitByDate": "2025-07-30T00:00:00Z", "checkDate": "2025-08-01T00:00:00Z", "checkCount": 0, "id": "3ad3485e-d80a-4f90-a6b5-099a715dd41c", "companyId": "00649813", "type": "payperiod", "_rid": "NmJkAKiCbEcBDgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcBDgEAAAAAAA==/", "_etag": "\"9d007684-0000-0100-0000-686ff71f0000\"", "_attachments": "attachments/", "_ts": 1752168223}, {"payPeriodId": "1080040539085257", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-08-01T00:00:00Z", "endDate": "2025-08-15T00:00:00Z", "submitByDate": "2025-08-13T00:00:00Z", "checkDate": "2025-08-15T00:00:00Z", "checkCount": 0, "id": "8b38629f-eb6b-4a1e-bc17-32e652a33225", "companyId": "00649813", "type": "payperiod", "_rid": "NmJkAKiCbEcCDgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcCDgEAAAAAAA==/", "_etag": "\"9d007b84-0000-0100-0000-686ff71f0000\"", "_attachments": "attachments/", "_ts": 1752168223}, {"payPeriodId": "1080040539085258", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-08-16T00:00:00Z", "endDate": "2025-08-31T00:00:00Z", "submitByDate": "2025-08-27T00:00:00Z", "checkDate": "2025-08-29T00:00:00Z", "checkCount": 0, "id": "7625f04f-e4ad-4a1b-bea8-e7f90330d0cc", "companyId": "00649813", "type": "payperiod", "_rid": "NmJkAKiCbEcDDgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcDDgEAAAAAAA==/", "_etag": "\"9d007d84-0000-0100-0000-686ff7200000\"", "_attachments": "attachments/", "_ts": 1752168224}, {"payPeriodId": "1080040773199338", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-09-01T00:00:00Z", "endDate": "2025-09-15T00:00:00Z", "submitByDate": "2025-09-12T00:00:00Z", "checkDate": "2025-09-16T00:00:00Z", "checkCount": 0, "id": "fb018dfa-677a-4325-aaf0-081a3caa069a", "companyId": "00649813", "type": "payperiod", "_rid": "NmJkAKiCbEcEDgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcEDgEAAAAAAA==/", "_etag": "\"9d007e84-0000-0100-0000-686ff7200000\"", "_attachments": "attachments/", "_ts": 1752168224}, {"payPeriodId": "1080040773199339", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-09-16T00:00:00Z", "endDate": "2025-09-30T00:00:00Z", "submitByDate": "2025-09-29T00:00:00Z", "checkDate": "2025-10-01T00:00:00Z", "checkCount": 0, "id": "2477eedd-5a1a-4023-ad7a-9ea6bfa08df1", "companyId": "00649813", "type": "payperiod", "_rid": "NmJkAKiCbEcFDgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcFDgEAAAAAAA==/", "_etag": "\"9d008184-0000-0100-0000-686ff7200000\"", "_attachments": "attachments/", "_ts": 1752168224}], "links": [{"rel": "self", "href": "https://ca-payroll-ai-mock-sb-001-secure.nicebeach-8a7a52ab.eastus.azurecontainerapps.io/ca/companies/00649813/payperiods"}]}, "status_code": 200}