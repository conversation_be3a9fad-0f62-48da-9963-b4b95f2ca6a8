{"success": true, "company_id": "70208819", "data": {"metadata": {"contentItemCount": 24}, "content": [{"payPeriodId": "1150017387729999", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-04-23T00:00:00Z", "endDate": "2025-05-06T00:00:00Z", "submitByDate": "2025-05-07T00:00:00Z", "checkDate": "2025-05-09T00:00:00Z", "checkCount": 0, "id": "f44de399-ee88-4138-a375-caadeb954239", "companyId": "70208819", "type": "payperiod", "_rid": "NmJkAKiCbEeMoAMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeMoAMAAAAAAA==/", "_etag": "\"a60061b0-0000-0100-0000-68702f6d0000\"", "_attachments": "attachments/", "_ts": 1752182637}, {"payPeriodId": "1150017387730002", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-05-07T00:00:00Z", "endDate": "2025-05-20T00:00:00Z", "submitByDate": "2025-05-21T00:00:00Z", "checkDate": "2025-05-23T00:00:00Z", "checkCount": 0, "id": "eacb9ccf-da86-4445-802b-03cff8944397", "companyId": "70208819", "type": "payperiod", "_rid": "NmJkAKiCbEeNoAMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeNoAMAAAAAAA==/", "_etag": "\"a60064b0-0000-0100-0000-68702f6e0000\"", "_attachments": "attachments/", "_ts": 1752182638}, {"payPeriodId": "1150017387730005", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-05-21T00:00:00Z", "endDate": "2025-06-03T00:00:00Z", "submitByDate": "2025-06-04T00:00:00Z", "checkDate": "2025-06-06T00:00:00Z", "checkCount": 0, "id": "944dffdd-157a-4240-b787-eeae79553c94", "companyId": "70208819", "type": "payperiod", "_rid": "NmJkAKiCbEeOoAMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeOoAMAAAAAAA==/", "_etag": "\"a60067b0-0000-0100-0000-68702f6e0000\"", "_attachments": "attachments/", "_ts": 1752182638}, {"payPeriodId": "1150017387730008", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-06-04T00:00:00Z", "endDate": "2025-06-17T00:00:00Z", "submitByDate": "2025-06-18T00:00:00Z", "checkDate": "2025-06-20T00:00:00Z", "checkCount": 0, "id": "6f572eea-c677-48ab-865a-5a67cc59704a", "companyId": "70208819", "type": "payperiod", "_rid": "NmJkAKiCbEePoAMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEePoAMAAAAAAA==/", "_etag": "\"a60068b0-0000-0100-0000-68702f6e0000\"", "_attachments": "attachments/", "_ts": 1752182638}, {"payPeriodId": "1150017387730011", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-06-18T00:00:00Z", "endDate": "2025-06-30T00:00:00Z", "submitByDate": "2025-06-30T00:00:00Z", "checkDate": "2025-07-01T00:00:00Z", "checkCount": 0, "id": "89d253c4-7af7-4b15-8f9f-95864c45ab20", "companyId": "70208819", "type": "payperiod", "_rid": "NmJkAKiCbEeQoAMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeQoAMAAAAAAA==/", "_etag": "\"a60069b0-0000-0100-0000-68702f6e0000\"", "_attachments": "attachments/", "_ts": 1752182638}, {"payPeriodId": "1150017814586513", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-07-01T00:00:00Z", "endDate": "2025-07-15T00:00:00Z", "submitByDate": "2025-07-16T00:00:00Z", "checkDate": "2025-07-18T00:00:00Z", "checkCount": 0, "id": "e8b48d77-7071-4ba0-9af0-24b409597c8f", "companyId": "70208819", "type": "payperiod", "_rid": "NmJkAKiCbEeRoAMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeRoAMAAAAAAA==/", "_etag": "\"a6006bb0-0000-0100-0000-68702f6e0000\"", "_attachments": "attachments/", "_ts": 1752182638}, {"payPeriodId": "1150017814586514", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-07-16T00:00:00Z", "endDate": "2025-07-29T00:00:00Z", "submitByDate": "2025-07-30T00:00:00Z", "checkDate": "2025-08-01T00:00:00Z", "checkCount": 0, "id": "049de010-317f-43a4-b649-bd8a06661b33", "companyId": "70208819", "type": "payperiod", "_rid": "NmJkAKiCbEeSoAMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeSoAMAAAAAAA==/", "_etag": "\"a6006db0-0000-0100-0000-68702f6e0000\"", "_attachments": "attachments/", "_ts": 1752182638}, {"payPeriodId": "1150017814586515", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-07-30T00:00:00Z", "endDate": "2025-08-12T00:00:00Z", "submitByDate": "2025-08-13T00:00:00Z", "checkDate": "2025-08-15T00:00:00Z", "checkCount": 0, "id": "169aafcc-af9e-4255-84b3-1004403fd214", "companyId": "70208819", "type": "payperiod", "_rid": "NmJkAKiCbEeToAMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeToAMAAAAAAA==/", "_etag": "\"a60072b0-0000-0100-0000-68702f6e0000\"", "_attachments": "attachments/", "_ts": 1752182638}, {"payPeriodId": "1150017878728048", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-08-13T00:00:00Z", "endDate": "2025-08-26T00:00:00Z", "submitByDate": "2025-08-27T00:00:00Z", "checkDate": "2025-08-29T00:00:00Z", "checkCount": 0, "id": "e60c6e7c-d474-40cc-8d35-b2ba295c417d", "companyId": "70208819", "type": "payperiod", "_rid": "NmJkAKiCbEeUoAMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeUoAMAAAAAAA==/", "_etag": "\"a60074b0-0000-0100-0000-68702f6e0000\"", "_attachments": "attachments/", "_ts": 1752182638}, {"payPeriodId": "1150017977448457", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-08-27T00:00:00Z", "endDate": "2025-09-09T00:00:00Z", "submitByDate": "2025-09-10T00:00:00Z", "checkDate": "2025-09-12T00:00:00Z", "checkCount": 0, "id": "d3373ec7-00fa-4135-be2e-eb4982a0a7b7", "companyId": "70208819", "type": "payperiod", "_rid": "NmJkAKiCbEeVoAMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeVoAMAAAAAAA==/", "_etag": "\"a60076b0-0000-0100-0000-68702f6e0000\"", "_attachments": "attachments/", "_ts": 1752182638}, {"payPeriodId": "1150018084253387", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-09-10T00:00:00Z", "endDate": "2025-09-23T00:00:00Z", "submitByDate": "2025-09-24T00:00:00Z", "checkDate": "2025-09-26T00:00:00Z", "checkCount": 0, "id": "0d0ea037-06d7-4a29-afd9-05d8796b02a7", "companyId": "70208819", "type": "payperiod", "_rid": "NmJkAKiCbEeWoAMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeWoAMAAAAAAA==/", "_etag": "\"a60079b0-0000-0100-0000-68702f6e0000\"", "_attachments": "attachments/", "_ts": 1752182638}, {"payPeriodId": "1150018187173848", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-09-24T00:00:00Z", "endDate": "2025-10-07T00:00:00Z", "submitByDate": "2025-10-08T00:00:00Z", "checkDate": "2025-10-10T00:00:00Z", "checkCount": 0, "id": "07faa6c5-4463-40bf-9cc9-8b0bdf749ae2", "companyId": "70208819", "type": "payperiod", "_rid": "NmJkAKiCbEeXoAMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeXoAMAAAAAAA==/", "_etag": "\"a6007bb0-0000-0100-0000-68702f6e0000\"", "_attachments": "attachments/", "_ts": 1752182638}, {"payPeriodId": "1150017387729999", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-04-23T00:00:00Z", "endDate": "2025-05-06T00:00:00Z", "submitByDate": "2025-05-07T00:00:00Z", "checkDate": "2025-05-09T00:00:00Z", "checkCount": 19, "id": "eebb1a5b-cce1-4649-80ee-028c205cf97c", "companyId": "70208819", "type": "payperiod", "_rid": "NmJkAKiCbEecoAMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEecoAMAAAAAAA==/", "_etag": "\"a60089b0-0000-0100-0000-68702f6f0000\"", "_attachments": "attachments/", "_ts": 1752182639}, {"payPeriodId": "1150017387730002", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-05-07T00:00:00Z", "endDate": "2025-05-20T00:00:00Z", "submitByDate": "2025-05-21T00:00:00Z", "checkDate": "2025-05-23T00:00:00Z", "checkCount": 19, "id": "af3cb6bd-6e8f-4fd9-a295-00d063492473", "companyId": "70208819", "type": "payperiod", "_rid": "NmJkAKiCbEedoAMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEedoAMAAAAAAA==/", "_etag": "\"a60090b0-0000-0100-0000-68702f6f0000\"", "_attachments": "attachments/", "_ts": 1752182639}, {"payPeriodId": "1150017387730005", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-05-21T00:00:00Z", "endDate": "2025-06-03T00:00:00Z", "submitByDate": "2025-06-04T00:00:00Z", "checkDate": "2025-06-06T00:00:00Z", "checkCount": 19, "id": "d39e5881-0034-4062-99da-afb91cdaf05f", "companyId": "70208819", "type": "payperiod", "_rid": "NmJkAKiCbEeeoAMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeeoAMAAAAAAA==/", "_etag": "\"a60092b0-0000-0100-0000-68702f6f0000\"", "_attachments": "attachments/", "_ts": 1752182639}, {"payPeriodId": "1150017387730008", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-06-04T00:00:00Z", "endDate": "2025-06-17T00:00:00Z", "submitByDate": "2025-06-18T00:00:00Z", "checkDate": "2025-06-20T00:00:00Z", "checkCount": 19, "id": "e8d865fc-e7b8-4cbb-bb24-bd05582fc0ae", "companyId": "70208819", "type": "payperiod", "_rid": "NmJkAKiCbEefoAMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEefoAMAAAAAAA==/", "_etag": "\"a60093b0-0000-0100-0000-68702f6f0000\"", "_attachments": "attachments/", "_ts": 1752182639}, {"payPeriodId": "1150017387730011", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-06-18T00:00:00Z", "endDate": "2025-06-30T00:00:00Z", "submitByDate": "2025-06-30T00:00:00Z", "checkDate": "2025-07-01T00:00:00Z", "checkCount": 19, "id": "2ca54d80-3b03-47c5-bea8-6d89736d4944", "companyId": "70208819", "type": "payperiod", "_rid": "NmJkAKiCbEegoAMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEegoAMAAAAAAA==/", "_etag": "\"a60095b0-0000-0100-0000-68702f6f0000\"", "_attachments": "attachments/", "_ts": 1752182639}, {"payPeriodId": "1150017814586513", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-07-01T00:00:00Z", "endDate": "2025-07-15T00:00:00Z", "submitByDate": "2025-07-16T00:00:00Z", "checkDate": "2025-07-18T00:00:00Z", "checkCount": 0, "id": "41589edd-88b1-4174-932a-9dd7dbf40dcc", "companyId": "70208819", "type": "payperiod", "_rid": "NmJkAKiCbEehoAMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEehoAMAAAAAAA==/", "_etag": "\"a60097b0-0000-0100-0000-68702f6f0000\"", "_attachments": "attachments/", "_ts": 1752182639}, {"payPeriodId": "1150017814586514", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-07-16T00:00:00Z", "endDate": "2025-07-29T00:00:00Z", "submitByDate": "2025-07-30T00:00:00Z", "checkDate": "2025-08-01T00:00:00Z", "checkCount": 0, "id": "52367a5a-beca-4739-a3b3-7e8420b8ecba", "companyId": "70208819", "type": "payperiod", "_rid": "NmJkAKiCbEeioAMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeioAMAAAAAAA==/", "_etag": "\"a6009ab0-0000-0100-0000-68702f6f0000\"", "_attachments": "attachments/", "_ts": 1752182639}, {"payPeriodId": "1150017814586515", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-07-30T00:00:00Z", "endDate": "2025-08-12T00:00:00Z", "submitByDate": "2025-08-13T00:00:00Z", "checkDate": "2025-08-15T00:00:00Z", "checkCount": 0, "id": "b889c655-ac8e-4f37-9fcc-120bf44c22b9", "companyId": "70208819", "type": "payperiod", "_rid": "NmJkAKiCbEejoAMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEejoAMAAAAAAA==/", "_etag": "\"a6009bb0-0000-0100-0000-68702f6f0000\"", "_attachments": "attachments/", "_ts": 1752182639}, {"payPeriodId": "1150017878728048", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-08-13T00:00:00Z", "endDate": "2025-08-26T00:00:00Z", "submitByDate": "2025-08-27T00:00:00Z", "checkDate": "2025-08-29T00:00:00Z", "checkCount": 0, "id": "4c35f7c0-7064-4d64-8900-8bebdf2bc55f", "companyId": "70208819", "type": "payperiod", "_rid": "NmJkAKiCbEekoAMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEekoAMAAAAAAA==/", "_etag": "\"a6009eb0-0000-0100-0000-68702f700000\"", "_attachments": "attachments/", "_ts": 1752182640}, {"payPeriodId": "1150017977448457", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-08-27T00:00:00Z", "endDate": "2025-09-09T00:00:00Z", "submitByDate": "2025-09-10T00:00:00Z", "checkDate": "2025-09-12T00:00:00Z", "checkCount": 0, "id": "7a4a31f8-ee95-4bb3-935d-759cf3a75fb7", "companyId": "70208819", "type": "payperiod", "_rid": "NmJkAKiCbEeloAMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeloAMAAAAAAA==/", "_etag": "\"a600a3b0-0000-0100-0000-68702f700000\"", "_attachments": "attachments/", "_ts": 1752182640}, {"payPeriodId": "1150018084253387", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-09-10T00:00:00Z", "endDate": "2025-09-23T00:00:00Z", "submitByDate": "2025-09-24T00:00:00Z", "checkDate": "2025-09-26T00:00:00Z", "checkCount": 0, "id": "a63c10d6-b343-4035-ad02-89a58b329091", "companyId": "70208819", "type": "payperiod", "_rid": "NmJkAKiCbEemoAMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEemoAMAAAAAAA==/", "_etag": "\"a600a8b0-0000-0100-0000-68702f700000\"", "_attachments": "attachments/", "_ts": 1752182640}, {"payPeriodId": "1150018187173848", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-09-24T00:00:00Z", "endDate": "2025-10-07T00:00:00Z", "submitByDate": "2025-10-08T00:00:00Z", "checkDate": "2025-10-10T00:00:00Z", "checkCount": 0, "id": "323cfbac-52c4-4af4-9c60-a880be4f35f9", "companyId": "70208819", "type": "payperiod", "_rid": "NmJkAKiCbEenoAMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEenoAMAAAAAAA==/", "_etag": "\"a600a9b0-0000-0100-0000-68702f700000\"", "_attachments": "attachments/", "_ts": 1752182640}], "links": [{"rel": "self", "href": "https://ca-payroll-ai-mock-sb-001-secure.nicebeach-8a7a52ab.eastus.azurecontainerapps.io/ca/companies/70208819/payperiods"}]}, "status_code": 200}