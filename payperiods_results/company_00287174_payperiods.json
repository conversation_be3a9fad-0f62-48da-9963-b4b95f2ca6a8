{"success": true, "company_id": "00287174", "data": {"metadata": {"contentItemCount": 42}, "content": [{"payPeriodId": "1040046160700695", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2024-12-19T00:00:00Z", "endDate": "2025-01-01T00:00:00Z", "submitByDate": "2025-01-02T00:00:00Z", "checkDate": "2025-01-03T00:00:00Z", "checkCount": 4, "id": "23ebd8fd-d20e-49fb-9cf0-8db3b0b84aff", "companyId": "00287174", "type": "payperiod", "_rid": "NmJkAKiCbEd9vQIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEd9vQIAAAAAAA==/", "_etag": "\"a300ebd5-0000-0100-0000-68701cfd0000\"", "_attachments": "attachments/", "_ts": 1752177917}, {"payPeriodId": "1040046243842008", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-01-02T00:00:00Z", "endDate": "2025-01-15T00:00:00Z", "submitByDate": "2025-01-16T00:00:00Z", "checkDate": "2025-01-17T00:00:00Z", "checkCount": 4, "id": "3a0018e5-e99a-487a-8fa0-7c01d15534c1", "companyId": "00287174", "type": "payperiod", "_rid": "NmJkAKiCbEd+vQIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEd+vQIAAAAAAA==/", "_etag": "\"a300edd5-0000-0100-0000-68701cfd0000\"", "_attachments": "attachments/", "_ts": 1752177917}, {"payPeriodId": "1040046322716558", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-01-16T00:00:00Z", "endDate": "2025-01-29T00:00:00Z", "submitByDate": "2025-01-30T00:00:00Z", "checkDate": "2025-01-31T00:00:00Z", "checkCount": 4, "id": "34e0bbfd-09b1-4207-b4c1-bc3a3259edad", "companyId": "00287174", "type": "payperiod", "_rid": "NmJkAKiCbEd-vQIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEd-vQIAAAAAAA==/", "_etag": "\"a300f1d5-0000-0100-0000-68701cfd0000\"", "_attachments": "attachments/", "_ts": 1752177917}, {"payPeriodId": "1040046414233333", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-01-30T00:00:00Z", "endDate": "2025-02-12T00:00:00Z", "submitByDate": "2025-02-13T00:00:00Z", "checkDate": "2025-02-14T00:00:00Z", "checkCount": 5, "id": "5fa0cb46-3be7-4c3d-9587-868499148b63", "companyId": "00287174", "type": "payperiod", "_rid": "NmJkAKiCbEeAvQIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeAvQIAAAAAAA==/", "_etag": "\"a300f4d5-0000-0100-0000-68701cfd0000\"", "_attachments": "attachments/", "_ts": 1752177917}, {"payPeriodId": "1040046486635142", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-02-13T00:00:00Z", "endDate": "2025-02-26T00:00:00Z", "submitByDate": "2025-02-27T00:00:00Z", "checkDate": "2025-02-28T00:00:00Z", "checkCount": 5, "id": "dfc44c94-37bb-45e2-9b8b-83808a078b44", "companyId": "00287174", "type": "payperiod", "_rid": "NmJkAKiCbEeBvQIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeBvQIAAAAAAA==/", "_etag": "\"a300f5d5-0000-0100-0000-68701cfd0000\"", "_attachments": "attachments/", "_ts": 1752177917}, {"payPeriodId": "1040046569192485", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-02-27T00:00:00Z", "endDate": "2025-03-12T00:00:00Z", "submitByDate": "2025-03-13T00:00:00Z", "checkDate": "2025-03-14T00:00:00Z", "checkCount": 5, "id": "657e8cb1-530b-4895-b734-3610b09693f6", "companyId": "00287174", "type": "payperiod", "_rid": "NmJkAKiCbEeCvQIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeCvQIAAAAAAA==/", "_etag": "\"a300f7d5-0000-0100-0000-68701cfd0000\"", "_attachments": "attachments/", "_ts": 1752177917}, {"payPeriodId": "1040046653281744", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-03-13T00:00:00Z", "endDate": "2025-03-26T00:00:00Z", "submitByDate": "2025-03-27T00:00:00Z", "checkDate": "2025-03-28T00:00:00Z", "checkCount": 5, "id": "1c2ab237-ed1e-4919-b68b-4e8296dc57bb", "companyId": "00287174", "type": "payperiod", "_rid": "NmJkAKiCbEeDvQIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeDvQIAAAAAAA==/", "_etag": "\"a300fad5-0000-0100-0000-68701cfd0000\"", "_attachments": "attachments/", "_ts": 1752177917}, {"payPeriodId": "1040046732938077", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-03-27T00:00:00Z", "endDate": "2025-04-09T00:00:00Z", "submitByDate": "2025-04-10T00:00:00Z", "checkDate": "2025-04-11T00:00:00Z", "checkCount": 0, "id": "8d28aeb2-fc78-4cd9-9f6f-8567a18d2722", "companyId": "00287174", "type": "payperiod", "_rid": "NmJkAKiCbEeEvQIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeEvQIAAAAAAA==/", "_etag": "\"a300fcd5-0000-0100-0000-68701cfd0000\"", "_attachments": "attachments/", "_ts": 1752177917}, {"payPeriodId": "1040046816007237", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-04-10T00:00:00Z", "endDate": "2025-04-23T00:00:00Z", "submitByDate": "2025-04-24T00:00:00Z", "checkDate": "2025-04-25T00:00:00Z", "checkCount": 0, "id": "eae773fa-210d-4613-afd6-b0273a9b265e", "companyId": "00287174", "type": "payperiod", "_rid": "NmJkAKiCbEeFvQIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeFvQIAAAAAAA==/", "_etag": "\"a30000d6-0000-0100-0000-68701cfd0000\"", "_attachments": "attachments/", "_ts": 1752177917}, {"payPeriodId": "1040046889122805", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-04-24T00:00:00Z", "endDate": "2025-05-07T00:00:00Z", "submitByDate": "2025-05-08T00:00:00Z", "checkDate": "2025-05-09T00:00:00Z", "checkCount": 0, "id": "a0179d4f-1074-4437-8f56-1e9a0d088d71", "companyId": "00287174", "type": "payperiod", "_rid": "NmJkAKiCbEeGvQIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeGvQIAAAAAAA==/", "_etag": "\"a30002d6-0000-0100-0000-68701cfd0000\"", "_attachments": "attachments/", "_ts": 1752177917}, {"payPeriodId": "1040046967654947", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-05-08T00:00:00Z", "endDate": "2025-05-21T00:00:00Z", "submitByDate": "2025-05-22T00:00:00Z", "checkDate": "2025-05-23T00:00:00Z", "checkCount": 0, "id": "9ed3b288-fa03-4d79-bc37-bc0d1810d728", "companyId": "00287174", "type": "payperiod", "_rid": "NmJkAKiCbEeHvQIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeHvQIAAAAAAA==/", "_etag": "\"a30004d6-0000-0100-0000-68701cfd0000\"", "_attachments": "attachments/", "_ts": 1752177917}, {"payPeriodId": "1040047041455748", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-05-22T00:00:00Z", "endDate": "2025-06-04T00:00:00Z", "submitByDate": "2025-06-05T00:00:00Z", "checkDate": "2025-06-06T00:00:00Z", "checkCount": 0, "id": "86851e32-cafa-46a2-811f-ca76a6695ae4", "companyId": "00287174", "type": "payperiod", "_rid": "NmJkAKiCbEeIvQIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeIvQIAAAAAAA==/", "_etag": "\"a30006d6-0000-0100-0000-68701cfd0000\"", "_attachments": "attachments/", "_ts": 1752177917}, {"payPeriodId": "1040047119559040", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-06-05T00:00:00Z", "endDate": "2025-06-18T00:00:00Z", "submitByDate": "2025-06-19T00:00:00Z", "checkDate": "2025-06-20T00:00:00Z", "checkCount": 0, "id": "5decf89c-5ab0-44b8-ac99-9df25ca58ddb", "companyId": "00287174", "type": "payperiod", "_rid": "NmJkAKiCbEeJvQIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeJvQIAAAAAAA==/", "_etag": "\"a30008d6-0000-0100-0000-68701cfe0000\"", "_attachments": "attachments/", "_ts": 1752177918}, {"payPeriodId": "1040047193565591", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-06-19T00:00:00Z", "endDate": "2025-07-02T00:00:00Z", "submitByDate": "2025-07-02T00:00:00Z", "checkDate": "2025-07-03T00:00:00Z", "checkCount": 0, "id": "fcd03149-6e67-4b98-bf67-6f0e62cd70bb", "companyId": "00287174", "type": "payperiod", "_rid": "NmJkAKiCbEeKvQIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeKvQIAAAAAAA==/", "_etag": "\"a3000ad6-0000-0100-0000-68701cfe0000\"", "_attachments": "attachments/", "_ts": 1752177918}, {"payPeriodId": "1040047278789099", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-07-03T00:00:00Z", "endDate": "2025-07-16T00:00:00Z", "submitByDate": "2025-07-17T00:00:00Z", "checkDate": "2025-07-18T00:00:00Z", "checkCount": 0, "id": "d86a66b3-63c0-4275-93b8-f3c334e76867", "companyId": "00287174", "type": "payperiod", "_rid": "NmJkAKiCbEeLvQIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeLvQIAAAAAAA==/", "_etag": "\"a30010d6-0000-0100-0000-68701cfe0000\"", "_attachments": "attachments/", "_ts": 1752177918}, {"payPeriodId": "1040047361482436", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-07-17T00:00:00Z", "endDate": "2025-07-30T00:00:00Z", "submitByDate": "2025-07-31T00:00:00Z", "checkDate": "2025-08-01T00:00:00Z", "checkCount": 0, "id": "5d795a60-cfa3-4e45-a10a-c3ba67472652", "companyId": "00287174", "type": "payperiod", "_rid": "NmJkAKiCbEeMvQIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeMvQIAAAAAAA==/", "_etag": "\"a30016d6-0000-0100-0000-68701cfe0000\"", "_attachments": "attachments/", "_ts": 1752177918}, {"payPeriodId": "1040047446496250", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-07-31T00:00:00Z", "endDate": "2025-08-13T00:00:00Z", "submitByDate": "2025-08-14T00:00:00Z", "checkDate": "2025-08-15T00:00:00Z", "checkCount": 0, "id": "6b18791d-ca96-44c5-aa96-c79f82e6a389", "companyId": "00287174", "type": "payperiod", "_rid": "NmJkAKiCbEeNvQIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeNvQIAAAAAAA==/", "_etag": "\"a30017d6-0000-0100-0000-68701cfe0000\"", "_attachments": "attachments/", "_ts": 1752177918}, {"payPeriodId": "1040047523387914", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-08-14T00:00:00Z", "endDate": "2025-08-27T00:00:00Z", "submitByDate": "2025-08-28T00:00:00Z", "checkDate": "2025-08-29T00:00:00Z", "checkCount": 0, "id": "2597a8ae-69b9-4d7e-9b81-99712a96c776", "companyId": "00287174", "type": "payperiod", "_rid": "NmJkAKiCbEeOvQIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeOvQIAAAAAAA==/", "_etag": "\"a30018d6-0000-0100-0000-68701cfe0000\"", "_attachments": "attachments/", "_ts": 1752177918}, {"payPeriodId": "1040047597864364", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-08-28T00:00:00Z", "endDate": "2025-09-10T00:00:00Z", "submitByDate": "2025-09-11T00:00:00Z", "checkDate": "2025-09-12T00:00:00Z", "checkCount": 0, "id": "229056d4-3a23-43a0-b727-1571ac2cd03d", "companyId": "00287174", "type": "payperiod", "_rid": "NmJkAKiCbEePvQIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEePvQIAAAAAAA==/", "_etag": "\"a3001bd6-0000-0100-0000-68701cfe0000\"", "_attachments": "attachments/", "_ts": 1752177918}, {"payPeriodId": "1040047674178326", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-09-11T00:00:00Z", "endDate": "2025-09-24T00:00:00Z", "submitByDate": "2025-09-25T00:00:00Z", "checkDate": "2025-09-26T00:00:00Z", "checkCount": 0, "id": "986fe4ff-c357-4c0f-957c-2a1f6c056f86", "companyId": "00287174", "type": "payperiod", "_rid": "NmJkAKiCbEeQvQIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeQvQIAAAAAAA==/", "_etag": "\"a3001fd6-0000-0100-0000-68701cfe0000\"", "_attachments": "attachments/", "_ts": 1752177918}, {"payPeriodId": "1040047752950362", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-09-25T00:00:00Z", "endDate": "2025-10-08T00:00:00Z", "submitByDate": "2025-10-08T00:00:00Z", "checkDate": "2025-10-10T00:00:00Z", "checkCount": 0, "id": "2d398c9c-ddfc-499c-ae56-50b6594fc38f", "companyId": "00287174", "type": "payperiod", "_rid": "NmJkAKiCbEeRvQIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeRvQIAAAAAAA==/", "_etag": "\"a30023d6-0000-0100-0000-68701cfe0000\"", "_attachments": "attachments/", "_ts": 1752177918}, {"payPeriodId": "1040046160700695", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2024-12-19T00:00:00Z", "endDate": "2025-01-01T00:00:00Z", "submitByDate": "2025-01-02T00:00:00Z", "checkDate": "2025-01-03T00:00:00Z", "checkCount": 4, "id": "a65a8f2b-e6c9-43a8-ab51-9bc022ff2884", "companyId": "00287174", "type": "payperiod", "_rid": "NmJkAKiCbEedvQIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEedvQIAAAAAAA==/", "_etag": "\"a3004fd6-0000-0100-0000-68701cff0000\"", "_attachments": "attachments/", "_ts": 1752177919}, {"payPeriodId": "1040046243842008", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-01-02T00:00:00Z", "endDate": "2025-01-15T00:00:00Z", "submitByDate": "2025-01-16T00:00:00Z", "checkDate": "2025-01-17T00:00:00Z", "checkCount": 4, "id": "48364427-1672-4501-a1b1-361bacf0d9c0", "companyId": "00287174", "type": "payperiod", "_rid": "NmJkAKiCbEeevQIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeevQIAAAAAAA==/", "_etag": "\"a30052d6-0000-0100-0000-68701cff0000\"", "_attachments": "attachments/", "_ts": 1752177919}, {"payPeriodId": "1040046322716558", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-01-16T00:00:00Z", "endDate": "2025-01-29T00:00:00Z", "submitByDate": "2025-01-30T00:00:00Z", "checkDate": "2025-01-31T00:00:00Z", "checkCount": 4, "id": "0e4e38d0-5089-488b-b18d-e3edc1a71722", "companyId": "00287174", "type": "payperiod", "_rid": "NmJkAKiCbEefvQIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEefvQIAAAAAAA==/", "_etag": "\"a30055d6-0000-0100-0000-68701cff0000\"", "_attachments": "attachments/", "_ts": 1752177919}, {"payPeriodId": "1040046414233333", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-01-30T00:00:00Z", "endDate": "2025-02-12T00:00:00Z", "submitByDate": "2025-02-13T00:00:00Z", "checkDate": "2025-02-14T00:00:00Z", "checkCount": 5, "id": "155d2e3b-7329-4053-84fc-0a82c2aed0a3", "companyId": "00287174", "type": "payperiod", "_rid": "NmJkAKiCbEegvQIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEegvQIAAAAAAA==/", "_etag": "\"a30058d6-0000-0100-0000-68701cff0000\"", "_attachments": "attachments/", "_ts": 1752177919}, {"payPeriodId": "1040046486635142", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-02-13T00:00:00Z", "endDate": "2025-02-26T00:00:00Z", "submitByDate": "2025-02-27T00:00:00Z", "checkDate": "2025-02-28T00:00:00Z", "checkCount": 5, "id": "09d6342c-47b5-451c-8be6-b89fcdcf7729", "companyId": "00287174", "type": "payperiod", "_rid": "NmJkAKiCbEehvQIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEehvQIAAAAAAA==/", "_etag": "\"a3005ad6-0000-0100-0000-68701cff0000\"", "_attachments": "attachments/", "_ts": 1752177919}, {"payPeriodId": "1040046569192485", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-02-27T00:00:00Z", "endDate": "2025-03-12T00:00:00Z", "submitByDate": "2025-03-13T00:00:00Z", "checkDate": "2025-03-14T00:00:00Z", "checkCount": 5, "id": "6bccd471-9aec-4b38-9f19-977b312552f5", "companyId": "00287174", "type": "payperiod", "_rid": "NmJkAKiCbEeivQIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeivQIAAAAAAA==/", "_etag": "\"a3005cd6-0000-0100-0000-68701d000000\"", "_attachments": "attachments/", "_ts": 1752177920}, {"payPeriodId": "1040046653281744", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-03-13T00:00:00Z", "endDate": "2025-03-26T00:00:00Z", "submitByDate": "2025-03-27T00:00:00Z", "checkDate": "2025-03-28T00:00:00Z", "checkCount": 5, "id": "8c70c030-d041-4b2a-8c0d-954c7e6c9c1e", "companyId": "00287174", "type": "payperiod", "_rid": "NmJkAKiCbEejvQIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEejvQIAAAAAAA==/", "_etag": "\"a3005fd6-0000-0100-0000-68701d000000\"", "_attachments": "attachments/", "_ts": 1752177920}, {"payPeriodId": "1040046732938077", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-03-27T00:00:00Z", "endDate": "2025-04-09T00:00:00Z", "submitByDate": "2025-04-10T00:00:00Z", "checkDate": "2025-04-11T00:00:00Z", "checkCount": 5, "id": "1266f8b9-d724-48ce-b758-286a3a9e0c90", "companyId": "00287174", "type": "payperiod", "_rid": "NmJkAKiCbEekvQIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEekvQIAAAAAAA==/", "_etag": "\"a30063d6-0000-0100-0000-68701d000000\"", "_attachments": "attachments/", "_ts": 1752177920}, {"payPeriodId": "1040046816007237", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-04-10T00:00:00Z", "endDate": "2025-04-23T00:00:00Z", "submitByDate": "2025-04-24T00:00:00Z", "checkDate": "2025-04-25T00:00:00Z", "checkCount": 5, "id": "d5efd6bd-de14-4c42-a3b2-36afafde20c6", "companyId": "00287174", "type": "payperiod", "_rid": "NmJkAKiCbEelvQIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEelvQIAAAAAAA==/", "_etag": "\"a3006ad6-0000-0100-0000-68701d000000\"", "_attachments": "attachments/", "_ts": 1752177920}, {"payPeriodId": "1040046889122805", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-04-24T00:00:00Z", "endDate": "2025-05-07T00:00:00Z", "submitByDate": "2025-05-08T00:00:00Z", "checkDate": "2025-05-09T00:00:00Z", "checkCount": 5, "id": "c6f6c4cf-4b61-44df-aa94-9b9604ae7fb0", "companyId": "00287174", "type": "payperiod", "_rid": "NmJkAKiCbEemvQIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEemvQIAAAAAAA==/", "_etag": "\"a3006dd6-0000-0100-0000-68701d000000\"", "_attachments": "attachments/", "_ts": 1752177920}, {"payPeriodId": "1040046967654947", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-05-08T00:00:00Z", "endDate": "2025-05-21T00:00:00Z", "submitByDate": "2025-05-22T00:00:00Z", "checkDate": "2025-05-23T00:00:00Z", "checkCount": 5, "id": "e0198813-b042-4a04-8576-5ac29bfb4d7e", "companyId": "00287174", "type": "payperiod", "_rid": "NmJkAKiCbEenvQIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEenvQIAAAAAAA==/", "_etag": "\"a30072d6-0000-0100-0000-68701d000000\"", "_attachments": "attachments/", "_ts": 1752177920}, {"payPeriodId": "1040047041455748", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-05-22T00:00:00Z", "endDate": "2025-06-04T00:00:00Z", "submitByDate": "2025-06-05T00:00:00Z", "checkDate": "2025-06-06T00:00:00Z", "checkCount": 5, "id": "11465489-4af8-4c5c-80f9-dadd9e70620e", "companyId": "00287174", "type": "payperiod", "_rid": "NmJkAKiCbEeovQIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeovQIAAAAAAA==/", "_etag": "\"a30073d6-0000-0100-0000-68701d000000\"", "_attachments": "attachments/", "_ts": 1752177920}, {"payPeriodId": "1040047119559040", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-06-05T00:00:00Z", "endDate": "2025-06-18T00:00:00Z", "submitByDate": "2025-06-19T00:00:00Z", "checkDate": "2025-06-20T00:00:00Z", "checkCount": 5, "id": "8ab076a3-ba6c-4993-8bc8-105ce38bb49f", "companyId": "00287174", "type": "payperiod", "_rid": "NmJkAKiCbEepvQIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEepvQIAAAAAAA==/", "_etag": "\"a30076d6-0000-0100-0000-68701d000000\"", "_attachments": "attachments/", "_ts": 1752177920}, {"payPeriodId": "1040047193565591", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-06-19T00:00:00Z", "endDate": "2025-07-02T00:00:00Z", "submitByDate": "2025-07-02T00:00:00Z", "checkDate": "2025-07-03T00:00:00Z", "checkCount": 5, "id": "52343de7-07c5-441b-abea-122b86d2dc89", "companyId": "00287174", "type": "payperiod", "_rid": "NmJkAKiCbEeqvQIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeqvQIAAAAAAA==/", "_etag": "\"a3007ad6-0000-0100-0000-68701d000000\"", "_attachments": "attachments/", "_ts": 1752177920}, {"payPeriodId": "1040047278789099", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-07-03T00:00:00Z", "endDate": "2025-07-16T00:00:00Z", "submitByDate": "2025-07-17T00:00:00Z", "checkDate": "2025-07-18T00:00:00Z", "checkCount": 0, "id": "a3811a8f-23b8-4b04-a1a2-a2975807e40b", "companyId": "00287174", "type": "payperiod", "_rid": "NmJkAKiCbEervQIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEervQIAAAAAAA==/", "_etag": "\"a3007ed6-0000-0100-0000-68701d000000\"", "_attachments": "attachments/", "_ts": 1752177920}, {"payPeriodId": "1040047361482436", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-07-17T00:00:00Z", "endDate": "2025-07-30T00:00:00Z", "submitByDate": "2025-07-31T00:00:00Z", "checkDate": "2025-08-01T00:00:00Z", "checkCount": 0, "id": "e554f086-f706-4db3-bf00-4bf7d8c8823f", "companyId": "00287174", "type": "payperiod", "_rid": "NmJkAKiCbEesvQIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEesvQIAAAAAAA==/", "_etag": "\"a30080d6-0000-0100-0000-68701d000000\"", "_attachments": "attachments/", "_ts": 1752177920}, {"payPeriodId": "1040047446496250", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-07-31T00:00:00Z", "endDate": "2025-08-13T00:00:00Z", "submitByDate": "2025-08-14T00:00:00Z", "checkDate": "2025-08-15T00:00:00Z", "checkCount": 0, "id": "35ea85fc-7623-4a27-b3ac-6ff3e5b0c5ff", "companyId": "00287174", "type": "payperiod", "_rid": "NmJkAKiCbEetvQIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEetvQIAAAAAAA==/", "_etag": "\"a30083d6-0000-0100-0000-68701d000000\"", "_attachments": "attachments/", "_ts": 1752177920}, {"payPeriodId": "1040047523387914", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-08-14T00:00:00Z", "endDate": "2025-08-27T00:00:00Z", "submitByDate": "2025-08-28T00:00:00Z", "checkDate": "2025-08-29T00:00:00Z", "checkCount": 0, "id": "9d6907dc-5e66-4ab2-84fb-01d1cd44c38d", "companyId": "00287174", "type": "payperiod", "_rid": "NmJkAKiCbEeuvQIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeuvQIAAAAAAA==/", "_etag": "\"a30088d6-0000-0100-0000-68701d000000\"", "_attachments": "attachments/", "_ts": 1752177920}, {"payPeriodId": "1040047597864364", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-08-28T00:00:00Z", "endDate": "2025-09-10T00:00:00Z", "submitByDate": "2025-09-11T00:00:00Z", "checkDate": "2025-09-12T00:00:00Z", "checkCount": 0, "id": "fbc84b9b-3efb-41da-8981-4ea82103af3b", "companyId": "00287174", "type": "payperiod", "_rid": "NmJkAKiCbEevvQIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEevvQIAAAAAAA==/", "_etag": "\"a3008bd6-0000-0100-0000-68701d010000\"", "_attachments": "attachments/", "_ts": 1752177921}, {"payPeriodId": "1040047674178326", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-09-11T00:00:00Z", "endDate": "2025-09-24T00:00:00Z", "submitByDate": "2025-09-25T00:00:00Z", "checkDate": "2025-09-26T00:00:00Z", "checkCount": 0, "id": "1e04e65f-d630-4468-8eee-cd72e533f03d", "companyId": "00287174", "type": "payperiod", "_rid": "NmJkAKiCbEewvQIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEewvQIAAAAAAA==/", "_etag": "\"a3008dd6-0000-0100-0000-68701d010000\"", "_attachments": "attachments/", "_ts": 1752177921}, {"payPeriodId": "1040047752950362", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-09-25T00:00:00Z", "endDate": "2025-10-08T00:00:00Z", "submitByDate": "2025-10-08T00:00:00Z", "checkDate": "2025-10-10T00:00:00Z", "checkCount": 0, "id": "69b01806-a2ea-4bb1-a515-c05078b93a94", "companyId": "00287174", "type": "payperiod", "_rid": "NmJkAKiCbEexvQIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEexvQIAAAAAAA==/", "_etag": "\"a30094d6-0000-0100-0000-68701d010000\"", "_attachments": "attachments/", "_ts": 1752177921}], "links": [{"rel": "self", "href": "https://ca-payroll-ai-mock-sb-001-secure.nicebeach-8a7a52ab.eastus.azurecontainerapps.io/ca/companies/00287174/payperiods"}]}, "status_code": 200}