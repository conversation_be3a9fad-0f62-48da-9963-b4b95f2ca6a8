{"success": true, "company_id": "0075C745", "data": {"metadata": {"contentItemCount": 46}, "content": [{"payPeriodId": "1060038923860633", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (3)", "startDate": "2024-12-19T00:00:00Z", "endDate": "2025-01-01T00:00:00Z", "submitByDate": "2025-01-02T00:00:00Z", "checkDate": "2025-01-06T00:00:00Z", "checkCount": 8, "id": "43b9ede1-db10-4d5b-93b4-e519ceb55f42", "companyId": "0075C745", "type": "payperiod", "_rid": "NmJkAKiCbEfMxwIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfMxwIAAAAAAA==/", "_etag": "\"a30076f6-0000-0100-0000-68701dd10000\"", "_attachments": "attachments/", "_ts": 1752178129}, {"payPeriodId": "1060038989663545", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (3)", "startDate": "2025-01-02T00:00:00Z", "endDate": "2025-01-15T00:00:00Z", "submitByDate": "2025-01-15T00:00:00Z", "checkDate": "2025-01-17T00:00:00Z", "checkCount": 8, "id": "1149246d-4a94-4c74-bed8-7eaa93e5e486", "companyId": "0075C745", "type": "payperiod", "_rid": "NmJkAKiCbEfNxwIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfNxwIAAAAAAA==/", "_etag": "\"a30078f6-0000-0100-0000-68701dd10000\"", "_attachments": "attachments/", "_ts": 1752178129}, {"payPeriodId": "1060039074496799", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (3)", "startDate": "2025-01-16T00:00:00Z", "endDate": "2025-01-29T00:00:00Z", "submitByDate": "2025-01-30T00:00:00Z", "checkDate": "2025-02-03T00:00:00Z", "checkCount": 8, "id": "48d814e2-0c54-42f7-b337-8f2e8ef24d39", "companyId": "0075C745", "type": "payperiod", "_rid": "NmJkAKiCbEfOxwIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfOxwIAAAAAAA==/", "_etag": "\"a3007af6-0000-0100-0000-68701dd10000\"", "_attachments": "attachments/", "_ts": 1752178129}, {"payPeriodId": "1060039145268994", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (3)", "startDate": "2025-01-30T00:00:00Z", "endDate": "2025-02-12T00:00:00Z", "submitByDate": "2025-02-12T00:00:00Z", "checkDate": "2025-02-14T00:00:00Z", "checkCount": 8, "id": "bb768f53-8fcd-432b-8336-0dc1a73d5256", "companyId": "0075C745", "type": "payperiod", "_rid": "NmJkAKiCbEfPxwIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfPxwIAAAAAAA==/", "_etag": "\"a3007ef6-0000-0100-0000-68701dd10000\"", "_attachments": "attachments/", "_ts": 1752178129}, {"payPeriodId": "1060039220671150", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (3)", "startDate": "2025-02-13T00:00:00Z", "endDate": "2025-02-26T00:00:00Z", "submitByDate": "2025-02-27T00:00:00Z", "checkDate": "2025-03-03T00:00:00Z", "checkCount": 8, "id": "18e3a906-0327-44fd-9536-3502e48ea5fe", "companyId": "0075C745", "type": "payperiod", "_rid": "NmJkAKiCbEfQxwIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfQxwIAAAAAAA==/", "_etag": "\"a30081f6-0000-0100-0000-68701dd10000\"", "_attachments": "attachments/", "_ts": 1752178129}, {"payPeriodId": "1060039293022096", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (3)", "startDate": "2025-02-27T00:00:00Z", "endDate": "2025-03-12T00:00:00Z", "submitByDate": "2025-03-13T00:00:00Z", "checkDate": "2025-03-17T00:00:00Z", "checkCount": 8, "id": "ec5b7d3c-12f6-49cb-a414-aaf0e574c2f1", "companyId": "0075C745", "type": "payperiod", "_rid": "NmJkAKiCbEfRxwIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfRxwIAAAAAAA==/", "_etag": "\"a30084f6-0000-0100-0000-68701dd10000\"", "_attachments": "attachments/", "_ts": 1752178129}, {"payPeriodId": "1060039372464483", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (3)", "startDate": "2025-03-13T00:00:00Z", "endDate": "2025-03-26T00:00:00Z", "submitByDate": "2025-03-27T00:00:00Z", "checkDate": "2025-03-31T00:00:00Z", "checkCount": 8, "id": "59c997e8-0327-4860-bce2-a7c5282bbb9b", "companyId": "0075C745", "type": "payperiod", "_rid": "NmJkAKiCbEfSxwIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfSxwIAAAAAAA==/", "_etag": "\"a30089f6-0000-0100-0000-68701dd10000\"", "_attachments": "attachments/", "_ts": 1752178129}, {"payPeriodId": "1060039447450461", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (3)", "startDate": "2025-03-27T00:00:00Z", "endDate": "2025-04-09T00:00:00Z", "submitByDate": "2025-04-10T00:00:00Z", "checkDate": "2025-04-14T00:00:00Z", "checkCount": 0, "id": "ac31d8e2-ac00-4926-a03c-d8ade2551826", "companyId": "0075C745", "type": "payperiod", "_rid": "NmJkAKiCbEfTxwIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfTxwIAAAAAAA==/", "_etag": "\"a3008cf6-0000-0100-0000-68701dd20000\"", "_attachments": "attachments/", "_ts": 1752178130}, {"payPeriodId": "1060039512059451", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (3)", "startDate": "2025-04-10T00:00:00Z", "endDate": "2025-04-23T00:00:00Z", "submitByDate": "2025-04-24T00:00:00Z", "checkDate": "2025-04-28T00:00:00Z", "checkCount": 0, "id": "be9c1c6d-7d39-4a47-8b70-6b6f61edeb18", "companyId": "0075C745", "type": "payperiod", "_rid": "NmJkAKiCbEfUxwIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfUxwIAAAAAAA==/", "_etag": "\"a3008ff6-0000-0100-0000-68701dd20000\"", "_attachments": "attachments/", "_ts": 1752178130}, {"payPeriodId": "1060039583077592", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (3)", "startDate": "2025-04-24T00:00:00Z", "endDate": "2025-05-07T00:00:00Z", "submitByDate": "2025-05-08T00:00:00Z", "checkDate": "2025-05-12T00:00:00Z", "checkCount": 0, "id": "a80ae598-e9ea-4e73-bd68-910b5ed543d5", "companyId": "0075C745", "type": "payperiod", "_rid": "NmJkAKiCbEfVxwIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfVxwIAAAAAAA==/", "_etag": "\"a30091f6-0000-0100-0000-68701dd20000\"", "_attachments": "attachments/", "_ts": 1752178130}, {"payPeriodId": "1060040086771427", "status": "INITIAL", "description": "Void", "startDate": "2025-04-24T00:00:00Z", "endDate": "2025-05-07T00:00:00Z", "submitByDate": "2025-05-11T00:00:00Z", "checkDate": "2025-05-12T00:00:00Z", "checkCount": 0, "id": "2ab20fb7-d4fd-4173-b93e-82a35499b72f", "companyId": "0075C745", "type": "payperiod", "_rid": "NmJkAKiCbEfWxwIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfWxwIAAAAAAA==/", "_etag": "\"a30095f6-0000-0100-0000-68701dd20000\"", "_attachments": "attachments/", "_ts": 1752178130}, {"payPeriodId": "1060040086771572", "status": "INITIAL", "description": "Correction", "startDate": "2025-04-24T00:00:00Z", "endDate": "2025-05-07T00:00:00Z", "submitByDate": "2025-05-11T00:00:00Z", "checkDate": "2025-05-12T00:00:00Z", "checkCount": 0, "id": "4a16e4e1-c3e4-48fb-85fb-35d2b152584f", "companyId": "0075C745", "type": "payperiod", "_rid": "NmJkAKiCbEfXxwIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfXxwIAAAAAAA==/", "_etag": "\"a30097f6-0000-0100-0000-68701dd20000\"", "_attachments": "attachments/", "_ts": 1752178130}, {"payPeriodId": "1060039640246984", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (3)", "startDate": "2025-05-08T00:00:00Z", "endDate": "2025-05-21T00:00:00Z", "submitByDate": "2025-05-21T00:00:00Z", "checkDate": "2025-05-23T00:00:00Z", "checkCount": 0, "id": "e25bd5c8-ca34-4947-b479-5501d1038f50", "companyId": "0075C745", "type": "payperiod", "_rid": "NmJkAKiCbEfYxwIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfYxwIAAAAAAA==/", "_etag": "\"a30099f6-0000-0100-0000-68701dd20000\"", "_attachments": "attachments/", "_ts": 1752178130}, {"payPeriodId": "1060039720774669", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (3)", "startDate": "2025-05-22T00:00:00Z", "endDate": "2025-06-04T00:00:00Z", "submitByDate": "2025-06-05T00:00:00Z", "checkDate": "2025-06-09T00:00:00Z", "checkCount": 0, "id": "0028e7eb-5d67-4380-ba2e-2c90d44e07ed", "companyId": "0075C745", "type": "payperiod", "_rid": "NmJkAKiCbEfZxwIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfZxwIAAAAAAA==/", "_etag": "\"a3009bf6-0000-0100-0000-68701dd20000\"", "_attachments": "attachments/", "_ts": 1752178130}, {"payPeriodId": "1060039789657466", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (3)", "startDate": "2025-06-05T00:00:00Z", "endDate": "2025-06-18T00:00:00Z", "submitByDate": "2025-06-19T00:00:00Z", "checkDate": "2025-06-23T00:00:00Z", "checkCount": 0, "id": "6cc5f821-537b-4b4a-bd2c-25fdd5e8ab03", "companyId": "0075C745", "type": "payperiod", "_rid": "NmJkAKiCbEfaxwIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfaxwIAAAAAAA==/", "_etag": "\"a3009ff6-0000-0100-0000-68701dd20000\"", "_attachments": "attachments/", "_ts": 1752178130}, {"payPeriodId": "1060039856281345", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (3)", "startDate": "2025-06-19T00:00:00Z", "endDate": "2025-07-02T00:00:00Z", "submitByDate": "2025-07-02T00:00:00Z", "checkDate": "2025-07-07T00:00:00Z", "checkCount": 0, "id": "2acfb041-1f36-4f3f-84aa-e94038d55f84", "companyId": "0075C745", "type": "payperiod", "_rid": "NmJkAKiCbEfbxwIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfbxwIAAAAAAA==/", "_etag": "\"a300a5f6-0000-0100-0000-68701dd20000\"", "_attachments": "attachments/", "_ts": 1752178130}, {"payPeriodId": "1060039934816998", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (3)", "startDate": "2025-07-03T00:00:00Z", "endDate": "2025-07-16T00:00:00Z", "submitByDate": "2025-07-17T00:00:00Z", "checkDate": "2025-07-21T00:00:00Z", "checkCount": 0, "id": "3b3c8499-4522-42ad-9489-1d74e2b0b406", "companyId": "0075C745", "type": "payperiod", "_rid": "NmJkAKiCbEfcxwIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfcxwIAAAAAAA==/", "_etag": "\"a300a6f6-0000-0100-0000-68701dd20000\"", "_attachments": "attachments/", "_ts": 1752178130}, {"payPeriodId": "1060040005634064", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (3)", "startDate": "2025-07-17T00:00:00Z", "endDate": "2025-07-30T00:00:00Z", "submitByDate": "2025-07-31T00:00:00Z", "checkDate": "2025-08-04T00:00:00Z", "checkCount": 0, "id": "f9c87162-a210-43d8-afae-daaeb85ebfb0", "companyId": "0075C745", "type": "payperiod", "_rid": "NmJkAKiCbEfdxwIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfdxwIAAAAAAA==/", "_etag": "\"a300abf6-0000-0100-0000-68701dd20000\"", "_attachments": "attachments/", "_ts": 1752178130}, {"payPeriodId": "1060040084168388", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (3)", "startDate": "2025-07-31T00:00:00Z", "endDate": "2025-08-13T00:00:00Z", "submitByDate": "2025-08-14T00:00:00Z", "checkDate": "2025-08-18T00:00:00Z", "checkCount": 0, "id": "37251360-80f2-433e-a261-77c57109ebb7", "companyId": "0075C745", "type": "payperiod", "_rid": "NmJkAKiCbEfexwIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfexwIAAAAAAA==/", "_etag": "\"a300adf6-0000-0100-0000-68701dd20000\"", "_attachments": "attachments/", "_ts": 1752178130}, {"payPeriodId": "1060040147940578", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (3)", "startDate": "2025-08-14T00:00:00Z", "endDate": "2025-08-27T00:00:00Z", "submitByDate": "2025-08-27T00:00:00Z", "checkDate": "2025-08-29T00:00:00Z", "checkCount": 0, "id": "f4e95c60-80d3-4ead-98f0-023c67f372de", "companyId": "0075C745", "type": "payperiod", "_rid": "NmJkAKiCbEffxwIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEffxwIAAAAAAA==/", "_etag": "\"a300aff6-0000-0100-0000-68701dd20000\"", "_attachments": "attachments/", "_ts": 1752178130}, {"payPeriodId": "1060040223540614", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (3)", "startDate": "2025-08-28T00:00:00Z", "endDate": "2025-09-10T00:00:00Z", "submitByDate": "2025-09-11T00:00:00Z", "checkDate": "2025-09-15T00:00:00Z", "checkCount": 0, "id": "a26172e5-025e-4c34-9f27-c476820d7163", "companyId": "0075C745", "type": "payperiod", "_rid": "NmJkAKiCbEfgxwIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfgxwIAAAAAAA==/", "_etag": "\"a300b3f6-0000-0100-0000-68701dd30000\"", "_attachments": "attachments/", "_ts": 1752178131}, {"payPeriodId": "1060040288700414", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (3)", "startDate": "2025-09-11T00:00:00Z", "endDate": "2025-09-24T00:00:00Z", "submitByDate": "2025-09-25T00:00:00Z", "checkDate": "2025-09-29T00:00:00Z", "checkCount": 0, "id": "218be31b-d848-4926-8d24-fcd1d92cd79e", "companyId": "0075C745", "type": "payperiod", "_rid": "NmJkAKiCbEfhxwIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfhxwIAAAAAAA==/", "_etag": "\"a300b6f6-0000-0100-0000-68701dd30000\"", "_attachments": "attachments/", "_ts": 1752178131}, {"payPeriodId": "1060040361968352", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (3)", "startDate": "2025-09-25T00:00:00Z", "endDate": "2025-10-08T00:00:00Z", "submitByDate": "2025-10-08T00:00:00Z", "checkDate": "2025-10-10T00:00:00Z", "checkCount": 0, "id": "d0cee747-e113-45dc-a4b8-f9dbb81df5a0", "companyId": "0075C745", "type": "payperiod", "_rid": "NmJkAKiCbEfixwIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfixwIAAAAAAA==/", "_etag": "\"a300b8f6-0000-0100-0000-68701dd30000\"", "_attachments": "attachments/", "_ts": 1752178131}, {"payPeriodId": "1060038923860633", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (3)", "startDate": "2024-12-19T00:00:00Z", "endDate": "2025-01-01T00:00:00Z", "submitByDate": "2025-01-02T00:00:00Z", "checkDate": "2025-01-06T00:00:00Z", "checkCount": 8, "id": "02c30369-1556-4751-badf-1024c47e237b", "companyId": "0075C745", "type": "payperiod", "_rid": "NmJkAKiCbEcFyAIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcFyAIAAAAAAA==/", "_etag": "\"a3002ff7-0000-0100-0000-68701dd50000\"", "_attachments": "attachments/", "_ts": 1752178133}, {"payPeriodId": "1060038989663545", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (3)", "startDate": "2025-01-02T00:00:00Z", "endDate": "2025-01-15T00:00:00Z", "submitByDate": "2025-01-15T00:00:00Z", "checkDate": "2025-01-17T00:00:00Z", "checkCount": 8, "id": "e64b151a-23f9-440a-85b8-dcb20cb835f1", "companyId": "0075C745", "type": "payperiod", "_rid": "NmJkAKiCbEcGyAIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcGyAIAAAAAAA==/", "_etag": "\"a30034f7-0000-0100-0000-68701dd60000\"", "_attachments": "attachments/", "_ts": 1752178134}, {"payPeriodId": "1060039074496799", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (3)", "startDate": "2025-01-16T00:00:00Z", "endDate": "2025-01-29T00:00:00Z", "submitByDate": "2025-01-30T00:00:00Z", "checkDate": "2025-02-03T00:00:00Z", "checkCount": 8, "id": "bd2ccb8b-2497-4af2-bc3a-cffc811972a6", "companyId": "0075C745", "type": "payperiod", "_rid": "NmJkAKiCbEcHyAIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcHyAIAAAAAAA==/", "_etag": "\"a30038f7-0000-0100-0000-68701dd60000\"", "_attachments": "attachments/", "_ts": 1752178134}, {"payPeriodId": "1060039145268994", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (3)", "startDate": "2025-01-30T00:00:00Z", "endDate": "2025-02-12T00:00:00Z", "submitByDate": "2025-02-12T00:00:00Z", "checkDate": "2025-02-14T00:00:00Z", "checkCount": 8, "id": "445cc4ef-bb90-487e-8319-cbff3d3eaf16", "companyId": "0075C745", "type": "payperiod", "_rid": "NmJkAKiCbEcIyAIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcIyAIAAAAAAA==/", "_etag": "\"a30039f7-0000-0100-0000-68701dd60000\"", "_attachments": "attachments/", "_ts": 1752178134}, {"payPeriodId": "1060039220671150", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (3)", "startDate": "2025-02-13T00:00:00Z", "endDate": "2025-02-26T00:00:00Z", "submitByDate": "2025-02-27T00:00:00Z", "checkDate": "2025-03-03T00:00:00Z", "checkCount": 8, "id": "8f5d2c85-44a8-47ab-8a7e-71c690f51979", "companyId": "0075C745", "type": "payperiod", "_rid": "NmJkAKiCbEcJyAIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcJyAIAAAAAAA==/", "_etag": "\"a3003bf7-0000-0100-0000-68701dd60000\"", "_attachments": "attachments/", "_ts": 1752178134}, {"payPeriodId": "1060039293022096", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (3)", "startDate": "2025-02-27T00:00:00Z", "endDate": "2025-03-12T00:00:00Z", "submitByDate": "2025-03-13T00:00:00Z", "checkDate": "2025-03-17T00:00:00Z", "checkCount": 8, "id": "e9532901-1c28-41d0-8678-cf17cb95e6ba", "companyId": "0075C745", "type": "payperiod", "_rid": "NmJkAKiCbEcKyAIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcKyAIAAAAAAA==/", "_etag": "\"a30041f7-0000-0100-0000-68701dd60000\"", "_attachments": "attachments/", "_ts": 1752178134}, {"payPeriodId": "1060039372464483", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (3)", "startDate": "2025-03-13T00:00:00Z", "endDate": "2025-03-26T00:00:00Z", "submitByDate": "2025-03-27T00:00:00Z", "checkDate": "2025-03-31T00:00:00Z", "checkCount": 8, "id": "deeb79af-d00d-4f40-9656-bcd37626cca9", "companyId": "0075C745", "type": "payperiod", "_rid": "NmJkAKiCbEcLyAIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcLyAIAAAAAAA==/", "_etag": "\"a30044f7-0000-0100-0000-68701dd60000\"", "_attachments": "attachments/", "_ts": 1752178134}, {"payPeriodId": "1060039447450461", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (3)", "startDate": "2025-03-27T00:00:00Z", "endDate": "2025-04-09T00:00:00Z", "submitByDate": "2025-04-10T00:00:00Z", "checkDate": "2025-04-14T00:00:00Z", "checkCount": 8, "id": "761f2b80-22cd-4774-b83c-144a31c2d3f9", "companyId": "0075C745", "type": "payperiod", "_rid": "NmJkAKiCbEcMyAIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcMyAIAAAAAAA==/", "_etag": "\"a30047f7-0000-0100-0000-68701dd60000\"", "_attachments": "attachments/", "_ts": 1752178134}, {"payPeriodId": "1060039512059451", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (3)", "startDate": "2025-04-10T00:00:00Z", "endDate": "2025-04-23T00:00:00Z", "submitByDate": "2025-04-24T00:00:00Z", "checkDate": "2025-04-28T00:00:00Z", "checkCount": 8, "id": "8b73beb4-53a7-4886-b883-1fa6722faa80", "companyId": "0075C745", "type": "payperiod", "_rid": "NmJkAKiCbEcNyAIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcNyAIAAAAAAA==/", "_etag": "\"a30048f7-0000-0100-0000-68701dd60000\"", "_attachments": "attachments/", "_ts": 1752178134}, {"payPeriodId": "1060039583077592", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (3)", "startDate": "2025-04-24T00:00:00Z", "endDate": "2025-05-07T00:00:00Z", "submitByDate": "2025-05-08T00:00:00Z", "checkDate": "2025-05-12T00:00:00Z", "checkCount": 9, "id": "5ba67d47-e058-46bb-bad1-c9fcf5230883", "companyId": "0075C745", "type": "payperiod", "_rid": "NmJkAKiCbEcOyAIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcOyAIAAAAAAA==/", "_etag": "\"a3004bf7-0000-0100-0000-68701dd60000\"", "_attachments": "attachments/", "_ts": 1752178134}, {"payPeriodId": "1060040086771427", "status": "COMPLETED", "description": "Void", "startDate": "2025-04-24T00:00:00Z", "endDate": "2025-05-07T00:00:00Z", "submitByDate": "2025-05-11T00:00:00Z", "checkDate": "2025-05-12T00:00:00Z", "checkCount": 1, "id": "f9c746cd-b45e-4081-be93-6225dd8217d3", "companyId": "0075C745", "type": "payperiod", "_rid": "NmJkAKiCbEcPyAIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcPyAIAAAAAAA==/", "_etag": "\"a3004ef7-0000-0100-0000-68701dd60000\"", "_attachments": "attachments/", "_ts": 1752178134}, {"payPeriodId": "1060040086771572", "status": "COMPLETED", "description": "Correction", "startDate": "2025-04-24T00:00:00Z", "endDate": "2025-05-07T00:00:00Z", "submitByDate": "2025-05-11T00:00:00Z", "checkDate": "2025-05-12T00:00:00Z", "checkCount": 1, "id": "4d5ba693-b3d7-4bb0-8796-7c6cc30fa008", "companyId": "0075C745", "type": "payperiod", "_rid": "NmJkAKiCbEcQyAIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcQyAIAAAAAAA==/", "_etag": "\"a30051f7-0000-0100-0000-68701dd60000\"", "_attachments": "attachments/", "_ts": 1752178134}, {"payPeriodId": "1060039640246984", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (3)", "startDate": "2025-05-08T00:00:00Z", "endDate": "2025-05-21T00:00:00Z", "submitByDate": "2025-05-21T00:00:00Z", "checkDate": "2025-05-23T00:00:00Z", "checkCount": 8, "id": "637bb516-2298-477a-b3c0-c8cd50cc0447", "companyId": "0075C745", "type": "payperiod", "_rid": "NmJkAKiCbEcRyAIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcRyAIAAAAAAA==/", "_etag": "\"a30053f7-0000-0100-0000-68701dd60000\"", "_attachments": "attachments/", "_ts": 1752178134}, {"payPeriodId": "1060039720774669", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (3)", "startDate": "2025-05-22T00:00:00Z", "endDate": "2025-06-04T00:00:00Z", "submitByDate": "2025-06-05T00:00:00Z", "checkDate": "2025-06-09T00:00:00Z", "checkCount": 8, "id": "272f6850-93ad-4ca7-b18c-79492ffedefa", "companyId": "0075C745", "type": "payperiod", "_rid": "NmJkAKiCbEcSyAIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcSyAIAAAAAAA==/", "_etag": "\"a30056f7-0000-0100-0000-68701dd60000\"", "_attachments": "attachments/", "_ts": 1752178134}, {"payPeriodId": "1060039789657466", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (3)", "startDate": "2025-06-05T00:00:00Z", "endDate": "2025-06-18T00:00:00Z", "submitByDate": "2025-06-19T00:00:00Z", "checkDate": "2025-06-23T00:00:00Z", "checkCount": 8, "id": "7a751a8f-775e-4add-a750-c040716860c8", "companyId": "0075C745", "type": "payperiod", "_rid": "NmJkAKiCbEcTyAIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcTyAIAAAAAAA==/", "_etag": "\"a30059f7-0000-0100-0000-68701dd70000\"", "_attachments": "attachments/", "_ts": 1752178135}, {"payPeriodId": "1060039856281345", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (3)", "startDate": "2025-06-19T00:00:00Z", "endDate": "2025-07-02T00:00:00Z", "submitByDate": "2025-07-02T00:00:00Z", "checkDate": "2025-07-07T00:00:00Z", "checkCount": 8, "id": "224d54a2-3405-444c-9f41-b1a0838b4f83", "companyId": "0075C745", "type": "payperiod", "_rid": "NmJkAKiCbEcUyAIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcUyAIAAAAAAA==/", "_etag": "\"a3005df7-0000-0100-0000-68701dd70000\"", "_attachments": "attachments/", "_ts": 1752178135}, {"payPeriodId": "1060039934816998", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (3)", "startDate": "2025-07-03T00:00:00Z", "endDate": "2025-07-16T00:00:00Z", "submitByDate": "2025-07-17T00:00:00Z", "checkDate": "2025-07-21T00:00:00Z", "checkCount": 0, "id": "2f0e92d5-6a5f-471e-9935-154e3d417321", "companyId": "0075C745", "type": "payperiod", "_rid": "NmJkAKiCbEcVyAIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcVyAIAAAAAAA==/", "_etag": "\"a30060f7-0000-0100-0000-68701dd70000\"", "_attachments": "attachments/", "_ts": 1752178135}, {"payPeriodId": "1060040005634064", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (3)", "startDate": "2025-07-17T00:00:00Z", "endDate": "2025-07-30T00:00:00Z", "submitByDate": "2025-07-31T00:00:00Z", "checkDate": "2025-08-04T00:00:00Z", "checkCount": 0, "id": "45bb8b7b-0996-4b75-95cb-7acae42fee66", "companyId": "0075C745", "type": "payperiod", "_rid": "NmJkAKiCbEcWyAIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcWyAIAAAAAAA==/", "_etag": "\"a30062f7-0000-0100-0000-68701dd70000\"", "_attachments": "attachments/", "_ts": 1752178135}, {"payPeriodId": "1060040084168388", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (3)", "startDate": "2025-07-31T00:00:00Z", "endDate": "2025-08-13T00:00:00Z", "submitByDate": "2025-08-14T00:00:00Z", "checkDate": "2025-08-18T00:00:00Z", "checkCount": 0, "id": "c29a222a-8e1c-400f-bfd2-b677986c908c", "companyId": "0075C745", "type": "payperiod", "_rid": "NmJkAKiCbEcXyAIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcXyAIAAAAAAA==/", "_etag": "\"a30066f7-0000-0100-0000-68701dd70000\"", "_attachments": "attachments/", "_ts": 1752178135}, {"payPeriodId": "1060040147940578", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (3)", "startDate": "2025-08-14T00:00:00Z", "endDate": "2025-08-27T00:00:00Z", "submitByDate": "2025-08-27T00:00:00Z", "checkDate": "2025-08-29T00:00:00Z", "checkCount": 0, "id": "1c246d7a-972b-4d86-9641-a8b34759432c", "companyId": "0075C745", "type": "payperiod", "_rid": "NmJkAKiCbEcYyAIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcYyAIAAAAAAA==/", "_etag": "\"a3006af7-0000-0100-0000-68701dd70000\"", "_attachments": "attachments/", "_ts": 1752178135}, {"payPeriodId": "1060040223540614", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (3)", "startDate": "2025-08-28T00:00:00Z", "endDate": "2025-09-10T00:00:00Z", "submitByDate": "2025-09-11T00:00:00Z", "checkDate": "2025-09-15T00:00:00Z", "checkCount": 0, "id": "9c81ce1c-371e-4ca5-9d47-b34fc0628d3a", "companyId": "0075C745", "type": "payperiod", "_rid": "NmJkAKiCbEcZyAIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcZyAIAAAAAAA==/", "_etag": "\"a3006df7-0000-0100-0000-68701dd70000\"", "_attachments": "attachments/", "_ts": 1752178135}, {"payPeriodId": "1060040288700414", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (3)", "startDate": "2025-09-11T00:00:00Z", "endDate": "2025-09-24T00:00:00Z", "submitByDate": "2025-09-25T00:00:00Z", "checkDate": "2025-09-29T00:00:00Z", "checkCount": 0, "id": "1f993fc9-2c39-4926-b724-d65a05d2e6c9", "companyId": "0075C745", "type": "payperiod", "_rid": "NmJkAKiCbEcayAIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcayAIAAAAAAA==/", "_etag": "\"a30070f7-0000-0100-0000-68701dd70000\"", "_attachments": "attachments/", "_ts": 1752178135}, {"payPeriodId": "1060040361968352", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (3)", "startDate": "2025-09-25T00:00:00Z", "endDate": "2025-10-08T00:00:00Z", "submitByDate": "2025-10-08T00:00:00Z", "checkDate": "2025-10-10T00:00:00Z", "checkCount": 0, "id": "f5f879ec-f434-443e-8107-a4232871d555", "companyId": "0075C745", "type": "payperiod", "_rid": "NmJkAKiCbEcbyAIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcbyAIAAAAAAA==/", "_etag": "\"a30072f7-0000-0100-0000-68701dd70000\"", "_attachments": "attachments/", "_ts": 1752178135}], "links": [{"rel": "self", "href": "https://ca-payroll-ai-mock-sb-001-secure.nicebeach-8a7a52ab.eastus.azurecontainerapps.io/ca/companies/0075C745/payperiods"}]}, "status_code": 200}