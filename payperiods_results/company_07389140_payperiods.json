{"success": true, "company_id": "07389140", "data": {"metadata": {"contentItemCount": 80}, "content": [{"payPeriodId": "1150016293487515", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-01-01T00:00:00Z", "endDate": "2025-01-15T00:00:00Z", "submitByDate": "2025-01-13T00:00:00Z", "checkDate": "2025-01-15T00:00:00Z", "checkCount": 5, "id": "88baa9c5-8186-444c-a41d-627470d8eb7b", "companyId": "07389140", "type": "payperiod", "_rid": "NmJkAKiCbEcjFgAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcjFgAAAAAAAA==/", "_etag": "\"97008047-0000-0100-0000-686fd0eb0000\"", "_attachments": "attachments/", "_ts": 1752158443}, {"payPeriodId": "1150016293487516", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-01-16T00:00:00Z", "endDate": "2025-01-31T00:00:00Z", "submitByDate": "2025-01-29T00:00:00Z", "checkDate": "2025-01-30T00:00:00Z", "checkCount": 5, "id": "2c215660-b1a8-479e-b384-d480bd42881d", "companyId": "07389140", "type": "payperiod", "_rid": "NmJkAKiCbEckFgAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEckFgAAAAAAAA==/", "_etag": "\"97008347-0000-0100-0000-686fd0eb0000\"", "_attachments": "attachments/", "_ts": 1752158443}, {"payPeriodId": "1150017094564795", "status": "COMPLETED", "description": "Missed Hours", "startDate": "2025-01-16T00:00:00Z", "endDate": "2025-01-31T00:00:00Z", "submitByDate": "2025-01-29T00:00:00Z", "checkDate": "2025-01-30T00:00:00Z", "checkCount": 1, "id": "777bef1f-bd5c-4019-8180-be9e1f3ae91a", "companyId": "07389140", "type": "payperiod", "_rid": "NmJkAKiCbEclFgAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEclFgAAAAAAAA==/", "_etag": "\"97008647-0000-0100-0000-686fd0eb0000\"", "_attachments": "attachments/", "_ts": 1752158443}, {"payPeriodId": "1150016491843648", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-02-01T00:00:00Z", "endDate": "2025-02-15T00:00:00Z", "submitByDate": "2025-02-12T00:00:00Z", "checkDate": "2025-02-14T00:00:00Z", "checkCount": 6, "id": "8dc06e92-d039-4f96-b812-046dad18bf3a", "companyId": "07389140", "type": "payperiod", "_rid": "NmJkAKiCbEcmFgAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcmFgAAAAAAAA==/", "_etag": "\"97008847-0000-0100-0000-686fd0eb0000\"", "_attachments": "attachments/", "_ts": 1752158443}, {"payPeriodId": "1150016491843649", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-02-16T00:00:00Z", "endDate": "2025-02-28T00:00:00Z", "submitByDate": "2025-02-26T00:00:00Z", "checkDate": "2025-02-28T00:00:00Z", "checkCount": 6, "id": "f463f96b-328a-4a2f-9b85-d04a2eb87f9a", "companyId": "07389140", "type": "payperiod", "_rid": "NmJkAKiCbEcnFgAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcnFgAAAAAAAA==/", "_etag": "\"97008c47-0000-0100-0000-686fd0eb0000\"", "_attachments": "attachments/", "_ts": 1752158443}, {"payPeriodId": "1150016725043897", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-03-01T00:00:00Z", "endDate": "2025-03-15T00:00:00Z", "submitByDate": "2025-03-12T00:00:00Z", "checkDate": "2025-03-14T00:00:00Z", "checkCount": 6, "id": "556c0790-070c-4b1e-8bf1-bb6e5fe91615", "companyId": "07389140", "type": "payperiod", "_rid": "NmJkAKiCbEcoFgAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcoFgAAAAAAAA==/", "_etag": "\"97008d47-0000-0100-0000-686fd0eb0000\"", "_attachments": "attachments/", "_ts": 1752158443}, {"payPeriodId": "1150017386521557", "status": "COMPLETED", "description": "Void", "startDate": "2025-03-01T00:00:00Z", "endDate": "2025-03-15T00:00:00Z", "submitByDate": "2025-03-13T00:00:00Z", "checkDate": "2025-03-14T00:00:00Z", "checkCount": 2, "id": "3b47d64e-70b3-4c89-8a50-da6f26188792", "companyId": "07389140", "type": "payperiod", "_rid": "NmJkAKiCbEcpFgAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcpFgAAAAAAAA==/", "_etag": "\"97008f47-0000-0100-0000-686fd0eb0000\"", "_attachments": "attachments/", "_ts": 1752158443}, {"payPeriodId": "1150016725043898", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-03-16T00:00:00Z", "endDate": "2025-03-31T00:00:00Z", "submitByDate": "2025-03-27T00:00:00Z", "checkDate": "2025-03-28T00:00:00Z", "checkCount": 6, "id": "e7de5537-58a0-4609-ba28-3cfca76aad43", "companyId": "07389140", "type": "payperiod", "_rid": "NmJkAKiCbEcqFgAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcqFgAAAAAAAA==/", "_etag": "\"97009147-0000-0100-0000-686fd0eb0000\"", "_attachments": "attachments/", "_ts": 1752158443}, {"payPeriodId": "1150016935601503", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-04-01T00:00:00Z", "endDate": "2025-04-15T00:00:00Z", "submitByDate": "2025-04-11T00:00:00Z", "checkDate": "2025-04-15T00:00:00Z", "checkCount": 0, "id": "ebeeb2cd-6522-4815-b2b8-310992098f32", "companyId": "07389140", "type": "payperiod", "_rid": "NmJkAKiCbEcrFgAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcrFgAAAAAAAA==/", "_etag": "\"97009847-0000-0100-0000-686fd0eb0000\"", "_attachments": "attachments/", "_ts": 1752158443}, {"payPeriodId": "1150016935601504", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-04-16T00:00:00Z", "endDate": "2025-04-30T00:00:00Z", "submitByDate": "2025-04-28T00:00:00Z", "checkDate": "2025-04-30T00:00:00Z", "checkCount": 0, "id": "ecc4a0a1-c7dd-4bbd-a088-9c028c6836c5", "companyId": "07389140", "type": "payperiod", "_rid": "NmJkAKiCbEcsFgAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcsFgAAAAAAAA==/", "_etag": "\"97009e47-0000-0100-0000-686fd0eb0000\"", "_attachments": "attachments/", "_ts": 1752158443}, {"payPeriodId": "1150017129690014", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-05-01T00:00:00Z", "endDate": "2025-05-15T00:00:00Z", "submitByDate": "2025-05-13T00:00:00Z", "checkDate": "2025-05-15T00:00:00Z", "checkCount": 0, "id": "c67fc435-68f1-440f-975c-2cadf8016165", "companyId": "07389140", "type": "payperiod", "_rid": "NmJkAKiCbEctFgAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEctFgAAAAAAAA==/", "_etag": "\"9700a047-0000-0100-0000-686fd0eb0000\"", "_attachments": "attachments/", "_ts": 1752158443}, {"payPeriodId": "1150017129690016", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-05-16T00:00:00Z", "endDate": "2025-05-31T00:00:00Z", "submitByDate": "2025-05-28T00:00:00Z", "checkDate": "2025-05-30T00:00:00Z", "checkCount": 0, "id": "174397cb-6215-4a9f-82ea-78e8f8db9054", "companyId": "07389140", "type": "payperiod", "_rid": "NmJkAKiCbEcuFgAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcuFgAAAAAAAA==/", "_etag": "\"9700a247-0000-0100-0000-686fd0eb0000\"", "_attachments": "attachments/", "_ts": 1752158443}, {"payPeriodId": "1150017377046108", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-06-01T00:00:00Z", "endDate": "2025-06-15T00:00:00Z", "submitByDate": "2025-06-11T00:00:00Z", "checkDate": "2025-06-13T00:00:00Z", "checkCount": 0, "id": "e06fd7ff-b781-4b44-abaf-617cf9d62fbd", "companyId": "07389140", "type": "payperiod", "_rid": "NmJkAKiCbEcvFgAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcvFgAAAAAAAA==/", "_etag": "\"9700a747-0000-0100-0000-686fd0ec0000\"", "_attachments": "attachments/", "_ts": 1752158444}, {"payPeriodId": "1150017377046109", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-06-16T00:00:00Z", "endDate": "2025-06-30T00:00:00Z", "submitByDate": "2025-06-26T00:00:00Z", "checkDate": "2025-06-30T00:00:00Z", "checkCount": 0, "id": "8efc52f8-717f-49d1-91d5-f27a8cac15e8", "companyId": "07389140", "type": "payperiod", "_rid": "NmJkAKiCbEcwFgAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcwFgAAAAAAAA==/", "_etag": "\"9700aa47-0000-0100-0000-686fd0ec0000\"", "_attachments": "attachments/", "_ts": 1752158444}, {"payPeriodId": "1150017577020910", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-07-01T00:00:00Z", "endDate": "2025-07-15T00:00:00Z", "submitByDate": "2025-07-11T00:00:00Z", "checkDate": "2025-07-15T00:00:00Z", "checkCount": 0, "id": "d31a072c-427f-4a30-80cb-18bc5666d206", "companyId": "07389140", "type": "payperiod", "_rid": "NmJkAKiCbEcxFgAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcxFgAAAAAAAA==/", "_etag": "\"9700ad47-0000-0100-0000-686fd0ec0000\"", "_attachments": "attachments/", "_ts": 1752158444}, {"payPeriodId": "1150017577020911", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-07-16T00:00:00Z", "endDate": "2025-07-31T00:00:00Z", "submitByDate": "2025-07-29T00:00:00Z", "checkDate": "2025-07-31T00:00:00Z", "checkCount": 0, "id": "d4f5036b-1df3-4e14-ba3f-ce20fda67a50", "companyId": "07389140", "type": "payperiod", "_rid": "NmJkAKiCbEcyFgAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcyFgAAAAAAAA==/", "_etag": "\"9700af47-0000-0100-0000-686fd0ec0000\"", "_attachments": "attachments/", "_ts": 1752158444}, {"payPeriodId": "1150017778129215", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-08-01T00:00:00Z", "endDate": "2025-08-15T00:00:00Z", "submitByDate": "2025-08-13T00:00:00Z", "checkDate": "2025-08-15T00:00:00Z", "checkCount": 0, "id": "a391c089-89c3-4521-867c-a90a225d8cbb", "companyId": "07389140", "type": "payperiod", "_rid": "NmJkAKiCbEczFgAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEczFgAAAAAAAA==/", "_etag": "\"9700b347-0000-0100-0000-686fd0ec0000\"", "_attachments": "attachments/", "_ts": 1752158444}, {"payPeriodId": "1150017778129216", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-08-16T00:00:00Z", "endDate": "2025-08-31T00:00:00Z", "submitByDate": "2025-08-27T00:00:00Z", "checkDate": "2025-08-29T00:00:00Z", "checkCount": 0, "id": "45b5ff7f-7951-42f1-b200-b83226deaf94", "companyId": "07389140", "type": "payperiod", "_rid": "NmJkAKiCbEc0FgAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc0FgAAAAAAAA==/", "_etag": "\"9700b747-0000-0100-0000-686fd0ec0000\"", "_attachments": "attachments/", "_ts": 1752158444}, {"payPeriodId": "1150018027009390", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-09-01T00:00:00Z", "endDate": "2025-09-15T00:00:00Z", "submitByDate": "2025-09-11T00:00:00Z", "checkDate": "2025-09-15T00:00:00Z", "checkCount": 0, "id": "e92a8012-f2cc-4c1b-8f7e-4458133ce1e8", "companyId": "07389140", "type": "payperiod", "_rid": "NmJkAKiCbEc1FgAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc1FgAAAAAAAA==/", "_etag": "\"9700bb47-0000-0100-0000-686fd0ec0000\"", "_attachments": "attachments/", "_ts": 1752158444}, {"payPeriodId": "1150018027009391", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-09-16T00:00:00Z", "endDate": "2025-09-30T00:00:00Z", "submitByDate": "2025-09-26T00:00:00Z", "checkDate": "2025-09-30T00:00:00Z", "checkCount": 0, "id": "20cfcf55-c0de-43e6-a0a7-311e69a83319", "companyId": "07389140", "type": "payperiod", "_rid": "NmJkAKiCbEc2FgAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc2FgAAAAAAAA==/", "_etag": "\"9700bc47-0000-0100-0000-686fd0ec0000\"", "_attachments": "attachments/", "_ts": 1752158444}, {"payPeriodId": "1150016293487515", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-01-01T00:00:00Z", "endDate": "2025-01-15T00:00:00Z", "submitByDate": "2025-01-13T00:00:00Z", "checkDate": "2025-01-15T00:00:00Z", "checkCount": 5, "id": "f5a79d66-d0be-454b-b240-20027bb5112a", "companyId": "07389140", "type": "payperiod", "_rid": "NmJkAKiCbEdSFgAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdSFgAAAAAAAA==/", "_etag": "\"97002b48-0000-0100-0000-686fd0ee0000\"", "_attachments": "attachments/", "_ts": 1752158446}, {"payPeriodId": "1150016293487516", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-01-16T00:00:00Z", "endDate": "2025-01-31T00:00:00Z", "submitByDate": "2025-01-29T00:00:00Z", "checkDate": "2025-01-30T00:00:00Z", "checkCount": 5, "id": "172992bf-47f9-4b88-80c2-792083507fb3", "companyId": "07389140", "type": "payperiod", "_rid": "NmJkAKiCbEdTFgAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdTFgAAAAAAAA==/", "_etag": "\"97002f48-0000-0100-0000-686fd0ee0000\"", "_attachments": "attachments/", "_ts": 1752158446}, {"payPeriodId": "1150017094564795", "status": "COMPLETED", "description": "Missed Hours", "startDate": "2025-01-16T00:00:00Z", "endDate": "2025-01-31T00:00:00Z", "submitByDate": "2025-01-29T00:00:00Z", "checkDate": "2025-01-30T00:00:00Z", "checkCount": 1, "id": "cfa6d744-e396-4d94-8844-387afa60080c", "companyId": "07389140", "type": "payperiod", "_rid": "NmJkAKiCbEdUFgAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdUFgAAAAAAAA==/", "_etag": "\"97003148-0000-0100-0000-686fd0ee0000\"", "_attachments": "attachments/", "_ts": 1752158446}, {"payPeriodId": "1150016491843648", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-02-01T00:00:00Z", "endDate": "2025-02-15T00:00:00Z", "submitByDate": "2025-02-12T00:00:00Z", "checkDate": "2025-02-14T00:00:00Z", "checkCount": 6, "id": "d30ce954-fce0-47c3-9552-553e233f136b", "companyId": "07389140", "type": "payperiod", "_rid": "NmJkAKiCbEdVFgAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdVFgAAAAAAAA==/", "_etag": "\"97003948-0000-0100-0000-686fd0ee0000\"", "_attachments": "attachments/", "_ts": 1752158446}, {"payPeriodId": "1150016491843649", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-02-16T00:00:00Z", "endDate": "2025-02-28T00:00:00Z", "submitByDate": "2025-02-26T00:00:00Z", "checkDate": "2025-02-28T00:00:00Z", "checkCount": 6, "id": "f8901812-f060-45d7-a239-a64d7e501bb3", "companyId": "07389140", "type": "payperiod", "_rid": "NmJkAKiCbEdWFgAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdWFgAAAAAAAA==/", "_etag": "\"97003c48-0000-0100-0000-686fd0ee0000\"", "_attachments": "attachments/", "_ts": 1752158446}, {"payPeriodId": "1150016725043897", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-03-01T00:00:00Z", "endDate": "2025-03-15T00:00:00Z", "submitByDate": "2025-03-12T00:00:00Z", "checkDate": "2025-03-14T00:00:00Z", "checkCount": 6, "id": "65335fba-1db2-4e93-8904-86bac187669e", "companyId": "07389140", "type": "payperiod", "_rid": "NmJkAKiCbEdXFgAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdXFgAAAAAAAA==/", "_etag": "\"97003f48-0000-0100-0000-686fd0ee0000\"", "_attachments": "attachments/", "_ts": 1752158446}, {"payPeriodId": "1150017386521557", "status": "COMPLETED", "description": "Void", "startDate": "2025-03-01T00:00:00Z", "endDate": "2025-03-15T00:00:00Z", "submitByDate": "2025-03-13T00:00:00Z", "checkDate": "2025-03-14T00:00:00Z", "checkCount": 2, "id": "2df64a6c-8575-4f5b-b537-8a8d71f414f5", "companyId": "07389140", "type": "payperiod", "_rid": "NmJkAKiCbEdYFgAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdYFgAAAAAAAA==/", "_etag": "\"97004148-0000-0100-0000-686fd0ef0000\"", "_attachments": "attachments/", "_ts": 1752158447}, {"payPeriodId": "1150016725043898", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-03-16T00:00:00Z", "endDate": "2025-03-31T00:00:00Z", "submitByDate": "2025-03-27T00:00:00Z", "checkDate": "2025-03-28T00:00:00Z", "checkCount": 6, "id": "83d1dcef-c304-4102-874f-f947cde9fb69", "companyId": "07389140", "type": "payperiod", "_rid": "NmJkAKiCbEdZFgAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdZFgAAAAAAAA==/", "_etag": "\"97004348-0000-0100-0000-686fd0ef0000\"", "_attachments": "attachments/", "_ts": 1752158447}, {"payPeriodId": "1150016935601503", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-04-01T00:00:00Z", "endDate": "2025-04-15T00:00:00Z", "submitByDate": "2025-04-11T00:00:00Z", "checkDate": "2025-04-15T00:00:00Z", "checkCount": 6, "id": "f03e1063-fa3c-4e94-9e94-516bf5152fc8", "companyId": "07389140", "type": "payperiod", "_rid": "NmJkAKiCbEdaFgAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdaFgAAAAAAAA==/", "_etag": "\"97004648-0000-0100-0000-686fd0ef0000\"", "_attachments": "attachments/", "_ts": 1752158447}, {"payPeriodId": "1150016935601504", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-04-16T00:00:00Z", "endDate": "2025-04-30T00:00:00Z", "submitByDate": "2025-04-28T00:00:00Z", "checkDate": "2025-04-30T00:00:00Z", "checkCount": 6, "id": "f8223688-30e8-483f-a7f3-b0f00a0592a7", "companyId": "07389140", "type": "payperiod", "_rid": "NmJkAKiCbEdbFgAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdbFgAAAAAAAA==/", "_etag": "\"97004b48-0000-0100-0000-686fd0ef0000\"", "_attachments": "attachments/", "_ts": 1752158447}, {"payPeriodId": "1150017129690014", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-05-01T00:00:00Z", "endDate": "2025-05-15T00:00:00Z", "submitByDate": "2025-05-13T00:00:00Z", "checkDate": "2025-05-15T00:00:00Z", "checkCount": 6, "id": "58d7734c-2ffb-461d-888a-374eb2cebefd", "companyId": "07389140", "type": "payperiod", "_rid": "NmJkAKiCbEdcFgAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdcFgAAAAAAAA==/", "_etag": "\"97004d48-0000-0100-0000-686fd0ef0000\"", "_attachments": "attachments/", "_ts": 1752158447}, {"payPeriodId": "1150017129690016", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-05-16T00:00:00Z", "endDate": "2025-05-31T00:00:00Z", "submitByDate": "2025-05-28T00:00:00Z", "checkDate": "2025-05-30T00:00:00Z", "checkCount": 6, "id": "70c4b22e-9609-4a13-b745-6355a28d14c5", "companyId": "07389140", "type": "payperiod", "_rid": "NmJkAKiCbEddFgAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEddFgAAAAAAAA==/", "_etag": "\"97005548-0000-0100-0000-686fd0ef0000\"", "_attachments": "attachments/", "_ts": 1752158447}, {"payPeriodId": "1150017377046108", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-06-01T00:00:00Z", "endDate": "2025-06-15T00:00:00Z", "submitByDate": "2025-06-11T00:00:00Z", "checkDate": "2025-06-13T00:00:00Z", "checkCount": 6, "id": "b0eaf218-60a6-44cc-b648-a2263b1bfd2b", "companyId": "07389140", "type": "payperiod", "_rid": "NmJkAKiCbEdeFgAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdeFgAAAAAAAA==/", "_etag": "\"97005948-0000-0100-0000-686fd0ef0000\"", "_attachments": "attachments/", "_ts": 1752158447}, {"payPeriodId": "1150017377046109", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-06-16T00:00:00Z", "endDate": "2025-06-30T00:00:00Z", "submitByDate": "2025-06-26T00:00:00Z", "checkDate": "2025-06-30T00:00:00Z", "checkCount": 6, "id": "443466c1-c789-4e36-8520-f3509cef5f03", "companyId": "07389140", "type": "payperiod", "_rid": "NmJkAKiCbEdfFgAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdfFgAAAAAAAA==/", "_etag": "\"97005b48-0000-0100-0000-686fd0ef0000\"", "_attachments": "attachments/", "_ts": 1752158447}, {"payPeriodId": "1150017577020910", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-07-01T00:00:00Z", "endDate": "2025-07-15T00:00:00Z", "submitByDate": "2025-07-11T00:00:00Z", "checkDate": "2025-07-15T00:00:00Z", "checkCount": 0, "id": "ce6566da-48b5-4d89-9225-4ab6a402dfa9", "companyId": "07389140", "type": "payperiod", "_rid": "NmJkAKiCbEdgFgAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdgFgAAAAAAAA==/", "_etag": "\"97005e48-0000-0100-0000-686fd0ef0000\"", "_attachments": "attachments/", "_ts": 1752158447}, {"payPeriodId": "1150017577020911", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-07-16T00:00:00Z", "endDate": "2025-07-31T00:00:00Z", "submitByDate": "2025-07-29T00:00:00Z", "checkDate": "2025-07-31T00:00:00Z", "checkCount": 0, "id": "3825cc02-e49d-4b28-8e1c-4bc801d61aca", "companyId": "07389140", "type": "payperiod", "_rid": "NmJkAKiCbEdhFgAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdhFgAAAAAAAA==/", "_etag": "\"97006348-0000-0100-0000-686fd0ef0000\"", "_attachments": "attachments/", "_ts": 1752158447}, {"payPeriodId": "1150017778129215", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-08-01T00:00:00Z", "endDate": "2025-08-15T00:00:00Z", "submitByDate": "2025-08-13T00:00:00Z", "checkDate": "2025-08-15T00:00:00Z", "checkCount": 0, "id": "355cf139-3c92-491d-bd7b-5732e85e0995", "companyId": "07389140", "type": "payperiod", "_rid": "NmJkAKiCbEdiFgAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdiFgAAAAAAAA==/", "_etag": "\"97006848-0000-0100-0000-686fd0ef0000\"", "_attachments": "attachments/", "_ts": 1752158447}, {"payPeriodId": "1150017778129216", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-08-16T00:00:00Z", "endDate": "2025-08-31T00:00:00Z", "submitByDate": "2025-08-27T00:00:00Z", "checkDate": "2025-08-29T00:00:00Z", "checkCount": 0, "id": "f53ee6f1-4aab-45c9-88bd-86463da6739d", "companyId": "07389140", "type": "payperiod", "_rid": "NmJkAKiCbEdjFgAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdjFgAAAAAAAA==/", "_etag": "\"97006c48-0000-0100-0000-686fd0ef0000\"", "_attachments": "attachments/", "_ts": 1752158447}, {"payPeriodId": "1150018027009390", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-09-01T00:00:00Z", "endDate": "2025-09-15T00:00:00Z", "submitByDate": "2025-09-11T00:00:00Z", "checkDate": "2025-09-15T00:00:00Z", "checkCount": 0, "id": "36fb4d77-9fdc-40dd-808e-309efe74f711", "companyId": "07389140", "type": "payperiod", "_rid": "NmJkAKiCbEdkFgAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdkFgAAAAAAAA==/", "_etag": "\"97006f48-0000-0100-0000-686fd0ef0000\"", "_attachments": "attachments/", "_ts": 1752158447}, {"payPeriodId": "1150018027009391", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-09-16T00:00:00Z", "endDate": "2025-09-30T00:00:00Z", "submitByDate": "2025-09-26T00:00:00Z", "checkDate": "2025-09-30T00:00:00Z", "checkCount": 0, "id": "594ad808-c83f-4513-b7b3-3b85e63f3b28", "companyId": "07389140", "type": "payperiod", "_rid": "NmJkAKiCbEdlFgAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdlFgAAAAAAAA==/", "_etag": "\"97007348-0000-0100-0000-686fd0f00000\"", "_attachments": "attachments/", "_ts": 1752158448}, {"payPeriodId": "1150016293487515", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-01-01T00:00:00Z", "endDate": "2025-01-15T00:00:00Z", "submitByDate": "2025-01-13T00:00:00Z", "checkDate": "2025-01-15T00:00:00Z", "checkCount": 5, "id": "5c6e4b0b-aaeb-462d-9213-04cc13bc415d", "companyId": "07389140", "type": "payperiod", "_rid": "NmJkAKiCbEdQDwEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdQDwEAAAAAAA==/", "_etag": "\"9d00da88-0000-0100-0000-686ff73a0000\"", "_attachments": "attachments/", "_ts": 1752168250}, {"payPeriodId": "1150016293487516", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-01-16T00:00:00Z", "endDate": "2025-01-31T00:00:00Z", "submitByDate": "2025-01-29T00:00:00Z", "checkDate": "2025-01-30T00:00:00Z", "checkCount": 5, "id": "7507598c-d3e7-4c94-8948-e8b4577de0b6", "companyId": "07389140", "type": "payperiod", "_rid": "NmJkAKiCbEdRDwEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdRDwEAAAAAAA==/", "_etag": "\"9d00dc88-0000-0100-0000-686ff73a0000\"", "_attachments": "attachments/", "_ts": 1752168250}, {"payPeriodId": "1150017094564795", "status": "COMPLETED", "description": "Missed Hours", "startDate": "2025-01-16T00:00:00Z", "endDate": "2025-01-31T00:00:00Z", "submitByDate": "2025-01-29T00:00:00Z", "checkDate": "2025-01-30T00:00:00Z", "checkCount": 1, "id": "7e40bc41-8989-4967-9512-e20cc8a03fae", "companyId": "07389140", "type": "payperiod", "_rid": "NmJkAKiCbEdSDwEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdSDwEAAAAAAA==/", "_etag": "\"9d00e088-0000-0100-0000-686ff73a0000\"", "_attachments": "attachments/", "_ts": 1752168250}, {"payPeriodId": "1150016491843648", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-02-01T00:00:00Z", "endDate": "2025-02-15T00:00:00Z", "submitByDate": "2025-02-12T00:00:00Z", "checkDate": "2025-02-14T00:00:00Z", "checkCount": 6, "id": "1312a951-6ffe-4e2e-aa0f-fb011416569c", "companyId": "07389140", "type": "payperiod", "_rid": "NmJkAKiCbEdTDwEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdTDwEAAAAAAA==/", "_etag": "\"9d00e688-0000-0100-0000-686ff73a0000\"", "_attachments": "attachments/", "_ts": 1752168250}, {"payPeriodId": "1150016491843649", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-02-16T00:00:00Z", "endDate": "2025-02-28T00:00:00Z", "submitByDate": "2025-02-26T00:00:00Z", "checkDate": "2025-02-28T00:00:00Z", "checkCount": 6, "id": "dfe3d9cc-595a-446b-8fa5-7680c6532bda", "companyId": "07389140", "type": "payperiod", "_rid": "NmJkAKiCbEdUDwEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdUDwEAAAAAAA==/", "_etag": "\"9d00e888-0000-0100-0000-686ff73a0000\"", "_attachments": "attachments/", "_ts": 1752168250}, {"payPeriodId": "1150016725043897", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-03-01T00:00:00Z", "endDate": "2025-03-15T00:00:00Z", "submitByDate": "2025-03-12T00:00:00Z", "checkDate": "2025-03-14T00:00:00Z", "checkCount": 6, "id": "29670a19-6104-46d4-b22d-120a61bab8f8", "companyId": "07389140", "type": "payperiod", "_rid": "NmJkAKiCbEdVDwEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdVDwEAAAAAAA==/", "_etag": "\"9d00ea88-0000-0100-0000-686ff73a0000\"", "_attachments": "attachments/", "_ts": 1752168250}, {"payPeriodId": "1150017386521557", "status": "COMPLETED", "description": "Void", "startDate": "2025-03-01T00:00:00Z", "endDate": "2025-03-15T00:00:00Z", "submitByDate": "2025-03-13T00:00:00Z", "checkDate": "2025-03-14T00:00:00Z", "checkCount": 2, "id": "bf8d8e97-a08a-4491-9c9e-b08b8e32000c", "companyId": "07389140", "type": "payperiod", "_rid": "NmJkAKiCbEdWDwEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdWDwEAAAAAAA==/", "_etag": "\"9d00ec88-0000-0100-0000-686ff73a0000\"", "_attachments": "attachments/", "_ts": 1752168250}, {"payPeriodId": "1150016725043898", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-03-16T00:00:00Z", "endDate": "2025-03-31T00:00:00Z", "submitByDate": "2025-03-27T00:00:00Z", "checkDate": "2025-03-28T00:00:00Z", "checkCount": 6, "id": "b6a7b6bb-1b76-4718-b381-112f5e633458", "companyId": "07389140", "type": "payperiod", "_rid": "NmJkAKiCbEdXDwEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdXDwEAAAAAAA==/", "_etag": "\"9d00ed88-0000-0100-0000-686ff73b0000\"", "_attachments": "attachments/", "_ts": 1752168251}, {"payPeriodId": "1150016935601503", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-04-01T00:00:00Z", "endDate": "2025-04-15T00:00:00Z", "submitByDate": "2025-04-11T00:00:00Z", "checkDate": "2025-04-15T00:00:00Z", "checkCount": 0, "id": "0746af1e-3a14-4253-90c6-22174e72f82e", "companyId": "07389140", "type": "payperiod", "_rid": "NmJkAKiCbEdYDwEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdYDwEAAAAAAA==/", "_etag": "\"9d00ee88-0000-0100-0000-686ff73b0000\"", "_attachments": "attachments/", "_ts": 1752168251}, {"payPeriodId": "1150016935601504", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-04-16T00:00:00Z", "endDate": "2025-04-30T00:00:00Z", "submitByDate": "2025-04-28T00:00:00Z", "checkDate": "2025-04-30T00:00:00Z", "checkCount": 0, "id": "dc982d75-e06d-4366-b94a-c84253e94802", "companyId": "07389140", "type": "payperiod", "_rid": "NmJkAKiCbEdZDwEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdZDwEAAAAAAA==/", "_etag": "\"9d00f488-0000-0100-0000-686ff73b0000\"", "_attachments": "attachments/", "_ts": 1752168251}, {"payPeriodId": "1150017129690014", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-05-01T00:00:00Z", "endDate": "2025-05-15T00:00:00Z", "submitByDate": "2025-05-13T00:00:00Z", "checkDate": "2025-05-15T00:00:00Z", "checkCount": 0, "id": "25cde64e-5278-410d-9545-201b1e548181", "companyId": "07389140", "type": "payperiod", "_rid": "NmJkAKiCbEdaDwEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdaDwEAAAAAAA==/", "_etag": "\"9d00f588-0000-0100-0000-686ff73b0000\"", "_attachments": "attachments/", "_ts": 1752168251}, {"payPeriodId": "1150017129690016", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-05-16T00:00:00Z", "endDate": "2025-05-31T00:00:00Z", "submitByDate": "2025-05-28T00:00:00Z", "checkDate": "2025-05-30T00:00:00Z", "checkCount": 0, "id": "bf6336dc-c2b2-44eb-92b4-48105b2188c1", "companyId": "07389140", "type": "payperiod", "_rid": "NmJkAKiCbEdbDwEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdbDwEAAAAAAA==/", "_etag": "\"9d00f988-0000-0100-0000-686ff73b0000\"", "_attachments": "attachments/", "_ts": 1752168251}, {"payPeriodId": "1150017377046108", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-06-01T00:00:00Z", "endDate": "2025-06-15T00:00:00Z", "submitByDate": "2025-06-11T00:00:00Z", "checkDate": "2025-06-13T00:00:00Z", "checkCount": 0, "id": "c168cf66-dbd0-4368-903c-36623dcc0f35", "companyId": "07389140", "type": "payperiod", "_rid": "NmJkAKiCbEdcDwEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdcDwEAAAAAAA==/", "_etag": "\"9d00fb88-0000-0100-0000-686ff73b0000\"", "_attachments": "attachments/", "_ts": 1752168251}, {"payPeriodId": "1150017377046109", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-06-16T00:00:00Z", "endDate": "2025-06-30T00:00:00Z", "submitByDate": "2025-06-26T00:00:00Z", "checkDate": "2025-06-30T00:00:00Z", "checkCount": 0, "id": "395c72a8-f6dc-47c6-87fc-9fa482a2fb7c", "companyId": "07389140", "type": "payperiod", "_rid": "NmJkAKiCbEddDwEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEddDwEAAAAAAA==/", "_etag": "\"9d000189-0000-0100-0000-686ff73b0000\"", "_attachments": "attachments/", "_ts": 1752168251}, {"payPeriodId": "1150017577020910", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-07-01T00:00:00Z", "endDate": "2025-07-15T00:00:00Z", "submitByDate": "2025-07-11T00:00:00Z", "checkDate": "2025-07-15T00:00:00Z", "checkCount": 0, "id": "8e6cc6cd-5385-4288-8ae8-d7414fe6af74", "companyId": "07389140", "type": "payperiod", "_rid": "NmJkAKiCbEdeDwEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdeDwEAAAAAAA==/", "_etag": "\"9d000489-0000-0100-0000-686ff73b0000\"", "_attachments": "attachments/", "_ts": 1752168251}, {"payPeriodId": "1150017577020911", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-07-16T00:00:00Z", "endDate": "2025-07-31T00:00:00Z", "submitByDate": "2025-07-29T00:00:00Z", "checkDate": "2025-07-31T00:00:00Z", "checkCount": 0, "id": "c67e3b74-e48a-49de-be02-6712712bc1df", "companyId": "07389140", "type": "payperiod", "_rid": "NmJkAKiCbEdfDwEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdfDwEAAAAAAA==/", "_etag": "\"9d000689-0000-0100-0000-686ff73b0000\"", "_attachments": "attachments/", "_ts": 1752168251}, {"payPeriodId": "1150017778129215", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-08-01T00:00:00Z", "endDate": "2025-08-15T00:00:00Z", "submitByDate": "2025-08-13T00:00:00Z", "checkDate": "2025-08-15T00:00:00Z", "checkCount": 0, "id": "1d5f893f-ca22-49ed-88be-4dcab90c5d34", "companyId": "07389140", "type": "payperiod", "_rid": "NmJkAKiCbEdgDwEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdgDwEAAAAAAA==/", "_etag": "\"9d000c89-0000-0100-0000-686ff73b0000\"", "_attachments": "attachments/", "_ts": 1752168251}, {"payPeriodId": "1150017778129216", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-08-16T00:00:00Z", "endDate": "2025-08-31T00:00:00Z", "submitByDate": "2025-08-27T00:00:00Z", "checkDate": "2025-08-29T00:00:00Z", "checkCount": 0, "id": "664c6fbc-5ac0-45a3-8489-0254f28dc54a", "companyId": "07389140", "type": "payperiod", "_rid": "NmJkAKiCbEdhDwEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdhDwEAAAAAAA==/", "_etag": "\"9d000e89-0000-0100-0000-686ff73b0000\"", "_attachments": "attachments/", "_ts": 1752168251}, {"payPeriodId": "1150018027009390", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-09-01T00:00:00Z", "endDate": "2025-09-15T00:00:00Z", "submitByDate": "2025-09-11T00:00:00Z", "checkDate": "2025-09-15T00:00:00Z", "checkCount": 0, "id": "f79b94c6-72de-499c-8375-882e5646558a", "companyId": "07389140", "type": "payperiod", "_rid": "NmJkAKiCbEdiDwEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdiDwEAAAAAAA==/", "_etag": "\"9d001089-0000-0100-0000-686ff73b0000\"", "_attachments": "attachments/", "_ts": 1752168251}, {"payPeriodId": "1150018027009391", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-09-16T00:00:00Z", "endDate": "2025-09-30T00:00:00Z", "submitByDate": "2025-09-26T00:00:00Z", "checkDate": "2025-09-30T00:00:00Z", "checkCount": 0, "id": "bf60611c-1d4f-4c2a-8de0-c24bb88fa453", "companyId": "07389140", "type": "payperiod", "_rid": "NmJkAKiCbEdjDwEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdjDwEAAAAAAA==/", "_etag": "\"9d001589-0000-0100-0000-686ff73b0000\"", "_attachments": "attachments/", "_ts": 1752168251}, {"payPeriodId": "1150016293487515", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-01-01T00:00:00Z", "endDate": "2025-01-15T00:00:00Z", "submitByDate": "2025-01-13T00:00:00Z", "checkDate": "2025-01-15T00:00:00Z", "checkCount": 5, "id": "db496a6f-0d11-46a4-af4b-69e6fe0a17d3", "companyId": "07389140", "type": "payperiod", "_rid": "NmJkAKiCbEd-DwEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEd-DwEAAAAAAA==/", "_etag": "\"9d007489-0000-0100-0000-686ff73e0000\"", "_attachments": "attachments/", "_ts": 1752168254}, {"payPeriodId": "1150016293487516", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-01-16T00:00:00Z", "endDate": "2025-01-31T00:00:00Z", "submitByDate": "2025-01-29T00:00:00Z", "checkDate": "2025-01-30T00:00:00Z", "checkCount": 5, "id": "f2ca8193-531f-42ca-b6d7-007f3a3fb62e", "companyId": "07389140", "type": "payperiod", "_rid": "NmJkAKiCbEeADwEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeADwEAAAAAAA==/", "_etag": "\"9d007889-0000-0100-0000-686ff73e0000\"", "_attachments": "attachments/", "_ts": 1752168254}, {"payPeriodId": "1150017094564795", "status": "COMPLETED", "description": "Missed Hours", "startDate": "2025-01-16T00:00:00Z", "endDate": "2025-01-31T00:00:00Z", "submitByDate": "2025-01-29T00:00:00Z", "checkDate": "2025-01-30T00:00:00Z", "checkCount": 1, "id": "2e136c91-932a-426a-a18a-f8ebc24f076c", "companyId": "07389140", "type": "payperiod", "_rid": "NmJkAKiCbEeBDwEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeBDwEAAAAAAA==/", "_etag": "\"9d007a89-0000-0100-0000-686ff73e0000\"", "_attachments": "attachments/", "_ts": 1752168254}, {"payPeriodId": "1150016491843648", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-02-01T00:00:00Z", "endDate": "2025-02-15T00:00:00Z", "submitByDate": "2025-02-12T00:00:00Z", "checkDate": "2025-02-14T00:00:00Z", "checkCount": 6, "id": "d4e69f13-9816-40f5-abdf-e1ad1742f43c", "companyId": "07389140", "type": "payperiod", "_rid": "NmJkAKiCbEeCDwEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeCDwEAAAAAAA==/", "_etag": "\"9d007e89-0000-0100-0000-686ff73e0000\"", "_attachments": "attachments/", "_ts": 1752168254}, {"payPeriodId": "1150016491843649", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-02-16T00:00:00Z", "endDate": "2025-02-28T00:00:00Z", "submitByDate": "2025-02-26T00:00:00Z", "checkDate": "2025-02-28T00:00:00Z", "checkCount": 6, "id": "763ad434-09e0-40ae-a7fe-5dd881dd87f7", "companyId": "07389140", "type": "payperiod", "_rid": "NmJkAKiCbEeDDwEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeDDwEAAAAAAA==/", "_etag": "\"9d008489-0000-0100-0000-686ff73e0000\"", "_attachments": "attachments/", "_ts": 1752168254}, {"payPeriodId": "1150016725043897", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-03-01T00:00:00Z", "endDate": "2025-03-15T00:00:00Z", "submitByDate": "2025-03-12T00:00:00Z", "checkDate": "2025-03-14T00:00:00Z", "checkCount": 6, "id": "8590cd3d-da27-4386-aff3-2185a051d2a3", "companyId": "07389140", "type": "payperiod", "_rid": "NmJkAKiCbEeEDwEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeEDwEAAAAAAA==/", "_etag": "\"9d008789-0000-0100-0000-686ff73e0000\"", "_attachments": "attachments/", "_ts": 1752168254}, {"payPeriodId": "1150017386521557", "status": "COMPLETED", "description": "Void", "startDate": "2025-03-01T00:00:00Z", "endDate": "2025-03-15T00:00:00Z", "submitByDate": "2025-03-13T00:00:00Z", "checkDate": "2025-03-14T00:00:00Z", "checkCount": 2, "id": "42e5140d-dd42-49e5-85c3-95fca30df5bb", "companyId": "07389140", "type": "payperiod", "_rid": "NmJkAKiCbEeFDwEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeFDwEAAAAAAA==/", "_etag": "\"9d008a89-0000-0100-0000-686ff73e0000\"", "_attachments": "attachments/", "_ts": 1752168254}, {"payPeriodId": "1150016725043898", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-03-16T00:00:00Z", "endDate": "2025-03-31T00:00:00Z", "submitByDate": "2025-03-27T00:00:00Z", "checkDate": "2025-03-28T00:00:00Z", "checkCount": 6, "id": "09cb974a-33a5-49e2-870e-66e0160ad6ee", "companyId": "07389140", "type": "payperiod", "_rid": "NmJkAKiCbEeGDwEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeGDwEAAAAAAA==/", "_etag": "\"9d008b89-0000-0100-0000-686ff73e0000\"", "_attachments": "attachments/", "_ts": 1752168254}, {"payPeriodId": "1150016935601503", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-04-01T00:00:00Z", "endDate": "2025-04-15T00:00:00Z", "submitByDate": "2025-04-11T00:00:00Z", "checkDate": "2025-04-15T00:00:00Z", "checkCount": 6, "id": "bbc78ed7-a0f7-4dc8-a6d8-80597b36e179", "companyId": "07389140", "type": "payperiod", "_rid": "NmJkAKiCbEeHDwEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeHDwEAAAAAAA==/", "_etag": "\"9d009089-0000-0100-0000-686ff73e0000\"", "_attachments": "attachments/", "_ts": 1752168254}, {"payPeriodId": "1150016935601504", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-04-16T00:00:00Z", "endDate": "2025-04-30T00:00:00Z", "submitByDate": "2025-04-28T00:00:00Z", "checkDate": "2025-04-30T00:00:00Z", "checkCount": 6, "id": "491dc3c9-31ab-4e43-abeb-31cb9e582b0d", "companyId": "07389140", "type": "payperiod", "_rid": "NmJkAKiCbEeIDwEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeIDwEAAAAAAA==/", "_etag": "\"9d009289-0000-0100-0000-686ff73e0000\"", "_attachments": "attachments/", "_ts": 1752168254}, {"payPeriodId": "1150017129690014", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-05-01T00:00:00Z", "endDate": "2025-05-15T00:00:00Z", "submitByDate": "2025-05-13T00:00:00Z", "checkDate": "2025-05-15T00:00:00Z", "checkCount": 6, "id": "e4cb6851-d82b-448e-b62c-4f3ef3c859d6", "companyId": "07389140", "type": "payperiod", "_rid": "NmJkAKiCbEeJDwEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeJDwEAAAAAAA==/", "_etag": "\"9d009589-0000-0100-0000-686ff73e0000\"", "_attachments": "attachments/", "_ts": 1752168254}, {"payPeriodId": "1150017129690016", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-05-16T00:00:00Z", "endDate": "2025-05-31T00:00:00Z", "submitByDate": "2025-05-28T00:00:00Z", "checkDate": "2025-05-30T00:00:00Z", "checkCount": 6, "id": "a64290cf-2114-47ca-a126-b1266114ed4e", "companyId": "07389140", "type": "payperiod", "_rid": "NmJkAKiCbEeKDwEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeKDwEAAAAAAA==/", "_etag": "\"9d009789-0000-0100-0000-686ff73f0000\"", "_attachments": "attachments/", "_ts": 1752168255}, {"payPeriodId": "1150017377046108", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-06-01T00:00:00Z", "endDate": "2025-06-15T00:00:00Z", "submitByDate": "2025-06-11T00:00:00Z", "checkDate": "2025-06-13T00:00:00Z", "checkCount": 6, "id": "99d4c0fb-e466-4059-8c9c-47d23c3c3633", "companyId": "07389140", "type": "payperiod", "_rid": "NmJkAKiCbEeLDwEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeLDwEAAAAAAA==/", "_etag": "\"9d009989-0000-0100-0000-686ff73f0000\"", "_attachments": "attachments/", "_ts": 1752168255}, {"payPeriodId": "1150017377046109", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-06-16T00:00:00Z", "endDate": "2025-06-30T00:00:00Z", "submitByDate": "2025-06-26T00:00:00Z", "checkDate": "2025-06-30T00:00:00Z", "checkCount": 6, "id": "279d51b7-5a20-44f4-8fcf-f17463665111", "companyId": "07389140", "type": "payperiod", "_rid": "NmJkAKiCbEeMDwEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeMDwEAAAAAAA==/", "_etag": "\"9d009c89-0000-0100-0000-686ff73f0000\"", "_attachments": "attachments/", "_ts": 1752168255}, {"payPeriodId": "1150017577020910", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-07-01T00:00:00Z", "endDate": "2025-07-15T00:00:00Z", "submitByDate": "2025-07-11T00:00:00Z", "checkDate": "2025-07-15T00:00:00Z", "checkCount": 0, "id": "c6f6bb99-249f-4475-a8e0-f8262ed5f3f8", "companyId": "07389140", "type": "payperiod", "_rid": "NmJkAKiCbEeNDwEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeNDwEAAAAAAA==/", "_etag": "\"9d00a189-0000-0100-0000-686ff73f0000\"", "_attachments": "attachments/", "_ts": 1752168255}, {"payPeriodId": "1150017577020911", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-07-16T00:00:00Z", "endDate": "2025-07-31T00:00:00Z", "submitByDate": "2025-07-29T00:00:00Z", "checkDate": "2025-07-31T00:00:00Z", "checkCount": 0, "id": "e7ba01e2-712b-4866-a763-74197acd7fb3", "companyId": "07389140", "type": "payperiod", "_rid": "NmJkAKiCbEeODwEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeODwEAAAAAAA==/", "_etag": "\"9d00a389-0000-0100-0000-686ff73f0000\"", "_attachments": "attachments/", "_ts": 1752168255}, {"payPeriodId": "1150017778129215", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-08-01T00:00:00Z", "endDate": "2025-08-15T00:00:00Z", "submitByDate": "2025-08-13T00:00:00Z", "checkDate": "2025-08-15T00:00:00Z", "checkCount": 0, "id": "53402cb5-4a06-43de-a68f-27718ae3af6e", "companyId": "07389140", "type": "payperiod", "_rid": "NmJkAKiCbEePDwEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEePDwEAAAAAAA==/", "_etag": "\"9d00a589-0000-0100-0000-686ff73f0000\"", "_attachments": "attachments/", "_ts": 1752168255}, {"payPeriodId": "1150017778129216", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-08-16T00:00:00Z", "endDate": "2025-08-31T00:00:00Z", "submitByDate": "2025-08-27T00:00:00Z", "checkDate": "2025-08-29T00:00:00Z", "checkCount": 0, "id": "7a4c8fdc-29d3-4bb2-918e-cea3dad7a80d", "companyId": "07389140", "type": "payperiod", "_rid": "NmJkAKiCbEeQDwEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeQDwEAAAAAAA==/", "_etag": "\"9d00a689-0000-0100-0000-686ff73f0000\"", "_attachments": "attachments/", "_ts": 1752168255}, {"payPeriodId": "1150018027009390", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-09-01T00:00:00Z", "endDate": "2025-09-15T00:00:00Z", "submitByDate": "2025-09-11T00:00:00Z", "checkDate": "2025-09-15T00:00:00Z", "checkCount": 0, "id": "66883b9d-04ac-464c-aeaf-cf20863d0d76", "companyId": "07389140", "type": "payperiod", "_rid": "NmJkAKiCbEeRDwEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeRDwEAAAAAAA==/", "_etag": "\"9d00ac89-0000-0100-0000-686ff73f0000\"", "_attachments": "attachments/", "_ts": 1752168255}, {"payPeriodId": "1150018027009391", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-09-16T00:00:00Z", "endDate": "2025-09-30T00:00:00Z", "submitByDate": "2025-09-26T00:00:00Z", "checkDate": "2025-09-30T00:00:00Z", "checkCount": 0, "id": "bae95fb1-b146-4483-ba48-f3f94595ee2f", "companyId": "07389140", "type": "payperiod", "_rid": "NmJkAKiCbEeSDwEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeSDwEAAAAAAA==/", "_etag": "\"9d00af89-0000-0100-0000-686ff73f0000\"", "_attachments": "attachments/", "_ts": 1752168255}], "links": [{"rel": "self", "href": "https://ca-payroll-ai-mock-sb-001-secure.nicebeach-8a7a52ab.eastus.azurecontainerapps.io/ca/companies/07389140/payperiods"}]}, "status_code": 200}