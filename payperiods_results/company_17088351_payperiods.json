{"success": true, "company_id": "17088351", "data": {"metadata": {"contentItemCount": 36}, "content": [{"payPeriodId": "1080039129947661", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-01-01T00:00:00Z", "endDate": "2025-01-15T00:00:00Z", "submitByDate": "2025-01-29T00:00:00Z", "checkDate": "2025-01-31T00:00:00Z", "checkCount": 5, "id": "427dc92b-c3b4-460b-9455-2b6212422a95", "companyId": "17088351", "type": "payperiod", "_rid": "NmJkAKiCbEfo+wIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfo+wIAAAAAAA==/", "_etag": "\"a4005eb2-0000-0100-0000-6870220d0000\"", "_attachments": "attachments/", "_ts": 1752179213}, {"payPeriodId": "1080039129947662", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-01-16T00:00:00Z", "endDate": "2025-01-31T00:00:00Z", "submitByDate": "2025-02-12T00:00:00Z", "checkDate": "2025-02-14T00:00:00Z", "checkCount": 8, "id": "c4013023-a045-46a0-80aa-526ba84231a0", "companyId": "17088351", "type": "payperiod", "_rid": "NmJkAKiCbEfp+wIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfp+wIAAAAAAA==/", "_etag": "\"a40064b2-0000-0100-0000-6870220d0000\"", "_attachments": "attachments/", "_ts": 1752179213}, {"payPeriodId": "1080039348420165", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-02-01T00:00:00Z", "endDate": "2025-02-15T00:00:00Z", "submitByDate": "2025-02-26T00:00:00Z", "checkDate": "2025-02-28T00:00:00Z", "checkCount": 5, "id": "de095df6-b0cc-4549-9836-5f730e94d71e", "companyId": "17088351", "type": "payperiod", "_rid": "NmJkAKiCbEfq+wIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfq+wIAAAAAAA==/", "_etag": "\"a40068b2-0000-0100-0000-6870220d0000\"", "_attachments": "attachments/", "_ts": 1752179213}, {"payPeriodId": "1080039348420166", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-02-16T00:00:00Z", "endDate": "2025-02-28T00:00:00Z", "submitByDate": "2025-03-12T00:00:00Z", "checkDate": "2025-03-14T00:00:00Z", "checkCount": 7, "id": "51ded212-2069-4ff5-aa4d-0a123ee449c6", "companyId": "17088351", "type": "payperiod", "_rid": "NmJkAKiCbEfr+wIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfr+wIAAAAAAA==/", "_etag": "\"a4006bb2-0000-0100-0000-6870220d0000\"", "_attachments": "attachments/", "_ts": 1752179213}, {"payPeriodId": "1080039560900903", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-03-01T00:00:00Z", "endDate": "2025-03-15T00:00:00Z", "submitByDate": "2025-03-27T00:00:00Z", "checkDate": "2025-03-31T00:00:00Z", "checkCount": 5, "id": "3bdf8873-64b5-4d11-bcfe-1a1641b67e02", "companyId": "17088351", "type": "payperiod", "_rid": "NmJkAKiCbEfs+wIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfs+wIAAAAAAA==/", "_etag": "\"a4006fb2-0000-0100-0000-6870220d0000\"", "_attachments": "attachments/", "_ts": 1752179213}, {"payPeriodId": "1080039560900904", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-03-16T00:00:00Z", "endDate": "2025-03-31T00:00:00Z", "submitByDate": "2025-04-11T00:00:00Z", "checkDate": "2025-04-15T00:00:00Z", "checkCount": 0, "id": "999786a8-1585-4eb1-8acf-32b4d2f3b49d", "companyId": "17088351", "type": "payperiod", "_rid": "NmJkAKiCbEft+wIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEft+wIAAAAAAA==/", "_etag": "\"a40075b2-0000-0100-0000-6870220d0000\"", "_attachments": "attachments/", "_ts": 1752179213}, {"payPeriodId": "1080039773567358", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-04-01T00:00:00Z", "endDate": "2025-04-15T00:00:00Z", "submitByDate": "2025-04-28T00:00:00Z", "checkDate": "2025-04-30T00:00:00Z", "checkCount": 0, "id": "084ffabd-802d-432e-904e-bf22db369d3d", "companyId": "17088351", "type": "payperiod", "_rid": "NmJkAKiCbEfu+wIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfu+wIAAAAAAA==/", "_etag": "\"a40078b2-0000-0100-0000-6870220d0000\"", "_attachments": "attachments/", "_ts": 1752179213}, {"payPeriodId": "1080039773567359", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-04-16T00:00:00Z", "endDate": "2025-04-30T00:00:00Z", "submitByDate": "2025-05-13T00:00:00Z", "checkDate": "2025-05-15T00:00:00Z", "checkCount": 0, "id": "72f42b78-ee86-4116-9b78-31e88c9b53de", "companyId": "17088351", "type": "payperiod", "_rid": "NmJkAKiCbEfv+wIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfv+wIAAAAAAA==/", "_etag": "\"a4007ab2-0000-0100-0000-6870220d0000\"", "_attachments": "attachments/", "_ts": 1752179213}, {"payPeriodId": "1080039980162590", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-05-01T00:00:00Z", "endDate": "2025-05-15T00:00:00Z", "submitByDate": "2025-05-28T00:00:00Z", "checkDate": "2025-05-30T00:00:00Z", "checkCount": 0, "id": "e1bd1866-cb19-4d0d-ab2e-762c674cc76c", "companyId": "17088351", "type": "payperiod", "_rid": "NmJkAKiCbEfw+wIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfw+wIAAAAAAA==/", "_etag": "\"a4007cb2-0000-0100-0000-6870220d0000\"", "_attachments": "attachments/", "_ts": 1752179213}, {"payPeriodId": "1080039980162591", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-05-16T00:00:00Z", "endDate": "2025-05-31T00:00:00Z", "submitByDate": "2025-06-11T00:00:00Z", "checkDate": "2025-06-13T00:00:00Z", "checkCount": 0, "id": "8340f998-0093-4d09-9790-d3a11b8eff9e", "companyId": "17088351", "type": "payperiod", "_rid": "NmJkAKiCbEfx+wIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfx+wIAAAAAAA==/", "_etag": "\"a4007fb2-0000-0100-0000-6870220d0000\"", "_attachments": "attachments/", "_ts": 1752179213}, {"payPeriodId": "1080040173453613", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-06-01T00:00:00Z", "endDate": "2025-06-15T00:00:00Z", "submitByDate": "2025-06-26T00:00:00Z", "checkDate": "2025-06-30T00:00:00Z", "checkCount": 0, "id": "fad55ca2-8baa-42c6-a1c7-ec30002921eb", "companyId": "17088351", "type": "payperiod", "_rid": "NmJkAKiCbEfy+wIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfy+wIAAAAAAA==/", "_etag": "\"a40081b2-0000-0100-0000-6870220d0000\"", "_attachments": "attachments/", "_ts": 1752179213}, {"payPeriodId": "1080040173453614", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-06-16T00:00:00Z", "endDate": "2025-06-30T00:00:00Z", "submitByDate": "2025-07-11T00:00:00Z", "checkDate": "2025-07-15T00:00:00Z", "checkCount": 0, "id": "a150bc89-593a-4fe8-a8db-9c650e5a6981", "companyId": "17088351", "type": "payperiod", "_rid": "NmJkAKiCbEfz+wIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfz+wIAAAAAAA==/", "_etag": "\"a40085b2-0000-0100-0000-6870220e0000\"", "_attachments": "attachments/", "_ts": 1752179214}, {"payPeriodId": "1080040383207514", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-07-01T00:00:00Z", "endDate": "2025-07-15T00:00:00Z", "submitByDate": "2025-07-29T00:00:00Z", "checkDate": "2025-07-31T00:00:00Z", "checkCount": 0, "id": "df909cd1-f877-45a7-a196-517b3e8073b9", "companyId": "17088351", "type": "payperiod", "_rid": "NmJkAKiCbEf0+wIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf0+wIAAAAAAA==/", "_etag": "\"a40087b2-0000-0100-0000-6870220e0000\"", "_attachments": "attachments/", "_ts": 1752179214}, {"payPeriodId": "1080040383207515", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-07-16T00:00:00Z", "endDate": "2025-07-31T00:00:00Z", "submitByDate": "2025-08-13T00:00:00Z", "checkDate": "2025-08-15T00:00:00Z", "checkCount": 0, "id": "7334eb0c-9279-441f-bf5b-bae562017c87", "companyId": "17088351", "type": "payperiod", "_rid": "NmJkAKiCbEf1+wIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf1+wIAAAAAAA==/", "_etag": "\"a40088b2-0000-0100-0000-6870220e0000\"", "_attachments": "attachments/", "_ts": 1752179214}, {"payPeriodId": "1080040598412899", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-08-01T00:00:00Z", "endDate": "2025-08-15T00:00:00Z", "submitByDate": "2025-08-27T00:00:00Z", "checkDate": "2025-08-29T00:00:00Z", "checkCount": 0, "id": "72895e98-76a1-4b12-bfbb-b9761d3e122a", "companyId": "17088351", "type": "payperiod", "_rid": "NmJkAKiCbEf2+wIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf2+wIAAAAAAA==/", "_etag": "\"a4008ab2-0000-0100-0000-6870220e0000\"", "_attachments": "attachments/", "_ts": 1752179214}, {"payPeriodId": "1080040598412900", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-08-16T00:00:00Z", "endDate": "2025-08-31T00:00:00Z", "submitByDate": "2025-09-11T00:00:00Z", "checkDate": "2025-09-15T00:00:00Z", "checkCount": 0, "id": "10a456a8-bf70-4c8b-8fa3-6ea7b8daf8c1", "companyId": "17088351", "type": "payperiod", "_rid": "NmJkAKiCbEf3+wIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf3+wIAAAAAAA==/", "_etag": "\"a4008cb2-0000-0100-0000-6870220e0000\"", "_attachments": "attachments/", "_ts": 1752179214}, {"payPeriodId": "1080040820008149", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-09-01T00:00:00Z", "endDate": "2025-09-15T00:00:00Z", "submitByDate": "2025-09-26T00:00:00Z", "checkDate": "2025-09-30T00:00:00Z", "checkCount": 0, "id": "67c99197-ee98-42a5-a2ce-a5ed9eeaf63b", "companyId": "17088351", "type": "payperiod", "_rid": "NmJkAKiCbEf4+wIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf4+wIAAAAAAA==/", "_etag": "\"a40090b2-0000-0100-0000-6870220e0000\"", "_attachments": "attachments/", "_ts": 1752179214}, {"payPeriodId": "1080040820008150", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-09-16T00:00:00Z", "endDate": "2025-09-30T00:00:00Z", "submitByDate": "2025-10-13T00:00:00Z", "checkDate": "2025-10-15T00:00:00Z", "checkCount": 0, "id": "040937fe-cca4-458e-a6f3-943c5eee7770", "companyId": "17088351", "type": "payperiod", "_rid": "NmJkAKiCbEf5+wIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf5+wIAAAAAAA==/", "_etag": "\"a40092b2-0000-0100-0000-6870220e0000\"", "_attachments": "attachments/", "_ts": 1752179214}, {"payPeriodId": "1080039129947661", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-01-01T00:00:00Z", "endDate": "2025-01-15T00:00:00Z", "submitByDate": "2025-01-29T00:00:00Z", "checkDate": "2025-01-31T00:00:00Z", "checkCount": 5, "id": "ceb08ec1-35cb-45cc-bdd8-39cc72423fbf", "companyId": "17088351", "type": "payperiod", "_rid": "NmJkAKiCbEcJ-AIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcJ-AIAAAAAAA==/", "_etag": "\"a400c2b2-0000-0100-0000-6870220f0000\"", "_attachments": "attachments/", "_ts": 1752179215}, {"payPeriodId": "1080039129947662", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-01-16T00:00:00Z", "endDate": "2025-01-31T00:00:00Z", "submitByDate": "2025-02-12T00:00:00Z", "checkDate": "2025-02-14T00:00:00Z", "checkCount": 8, "id": "b1d1debd-2617-46e3-bc18-59a10ab5fae4", "companyId": "17088351", "type": "payperiod", "_rid": "NmJkAKiCbEcK-AIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcK-AIAAAAAAA==/", "_etag": "\"a400c5b2-0000-0100-0000-6870220f0000\"", "_attachments": "attachments/", "_ts": 1752179215}, {"payPeriodId": "1080039348420165", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-02-01T00:00:00Z", "endDate": "2025-02-15T00:00:00Z", "submitByDate": "2025-02-26T00:00:00Z", "checkDate": "2025-02-28T00:00:00Z", "checkCount": 5, "id": "ee857e8e-ae48-4861-a781-6863d4d8500b", "companyId": "17088351", "type": "payperiod", "_rid": "NmJkAKiCbEcL-AIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcL-AIAAAAAAA==/", "_etag": "\"a400c7b2-0000-0100-0000-6870220f0000\"", "_attachments": "attachments/", "_ts": 1752179215}, {"payPeriodId": "1080039348420166", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-02-16T00:00:00Z", "endDate": "2025-02-28T00:00:00Z", "submitByDate": "2025-03-12T00:00:00Z", "checkDate": "2025-03-14T00:00:00Z", "checkCount": 7, "id": "18ed85df-87e6-4f1d-aadf-27e435c82a3c", "companyId": "17088351", "type": "payperiod", "_rid": "NmJkAKiCbEcM-AIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcM-AIAAAAAAA==/", "_etag": "\"a400c8b2-0000-0100-0000-687022100000\"", "_attachments": "attachments/", "_ts": 1752179216}, {"payPeriodId": "1080039560900903", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-03-01T00:00:00Z", "endDate": "2025-03-15T00:00:00Z", "submitByDate": "2025-03-27T00:00:00Z", "checkDate": "2025-03-31T00:00:00Z", "checkCount": 5, "id": "c735b70f-04fe-4f0d-b929-32f6840b228f", "companyId": "17088351", "type": "payperiod", "_rid": "NmJkAKiCbEcN-AIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcN-AIAAAAAAA==/", "_etag": "\"a400c9b2-0000-0100-0000-687022100000\"", "_attachments": "attachments/", "_ts": 1752179216}, {"payPeriodId": "1080039560900904", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-03-16T00:00:00Z", "endDate": "2025-03-31T00:00:00Z", "submitByDate": "2025-04-11T00:00:00Z", "checkDate": "2025-04-15T00:00:00Z", "checkCount": 8, "id": "37edcac9-281c-4176-8e77-c97ecc0ec661", "companyId": "17088351", "type": "payperiod", "_rid": "NmJkAKiCbEcO-AIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcO-AIAAAAAAA==/", "_etag": "\"a400cbb2-0000-0100-0000-687022100000\"", "_attachments": "attachments/", "_ts": 1752179216}, {"payPeriodId": "1080039773567358", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-04-01T00:00:00Z", "endDate": "2025-04-15T00:00:00Z", "submitByDate": "2025-04-28T00:00:00Z", "checkDate": "2025-04-30T00:00:00Z", "checkCount": 5, "id": "7df4736d-7578-4a6f-ae7e-ee10060b6a82", "companyId": "17088351", "type": "payperiod", "_rid": "NmJkAKiCbEcP-AIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcP-AIAAAAAAA==/", "_etag": "\"a400cdb2-0000-0100-0000-687022100000\"", "_attachments": "attachments/", "_ts": 1752179216}, {"payPeriodId": "1080039773567359", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-04-16T00:00:00Z", "endDate": "2025-04-30T00:00:00Z", "submitByDate": "2025-05-13T00:00:00Z", "checkDate": "2025-05-15T00:00:00Z", "checkCount": 8, "id": "901e0f2c-a2df-4965-9bbd-93b821d301ac", "companyId": "17088351", "type": "payperiod", "_rid": "NmJkAKiCbEcQ-AIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcQ-AIAAAAAAA==/", "_etag": "\"a400cfb2-0000-0100-0000-687022100000\"", "_attachments": "attachments/", "_ts": 1752179216}, {"payPeriodId": "1080039980162590", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-05-01T00:00:00Z", "endDate": "2025-05-15T00:00:00Z", "submitByDate": "2025-05-28T00:00:00Z", "checkDate": "2025-05-30T00:00:00Z", "checkCount": 5, "id": "97920fa9-fc90-4d47-a079-fe996e858d18", "companyId": "17088351", "type": "payperiod", "_rid": "NmJkAKiCbEcR-AIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcR-AIAAAAAAA==/", "_etag": "\"a400d4b2-0000-0100-0000-687022100000\"", "_attachments": "attachments/", "_ts": 1752179216}, {"payPeriodId": "1080039980162591", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-05-16T00:00:00Z", "endDate": "2025-05-31T00:00:00Z", "submitByDate": "2025-06-11T00:00:00Z", "checkDate": "2025-06-13T00:00:00Z", "checkCount": 8, "id": "e41ee2f0-4a76-4fc5-b6d3-8578a0e4c424", "companyId": "17088351", "type": "payperiod", "_rid": "NmJkAKiCbEcS-AIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcS-AIAAAAAAA==/", "_etag": "\"a400d7b2-0000-0100-0000-687022100000\"", "_attachments": "attachments/", "_ts": 1752179216}, {"payPeriodId": "1080040173453613", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-06-01T00:00:00Z", "endDate": "2025-06-15T00:00:00Z", "submitByDate": "2025-06-26T00:00:00Z", "checkDate": "2025-06-30T00:00:00Z", "checkCount": 5, "id": "8621df9e-48d1-42ba-8338-f8e303e46893", "companyId": "17088351", "type": "payperiod", "_rid": "NmJkAKiCbEcT-AIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcT-AIAAAAAAA==/", "_etag": "\"a400dbb2-0000-0100-0000-687022100000\"", "_attachments": "attachments/", "_ts": 1752179216}, {"payPeriodId": "1080040173453614", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-06-16T00:00:00Z", "endDate": "2025-06-30T00:00:00Z", "submitByDate": "2025-07-11T00:00:00Z", "checkDate": "2025-07-15T00:00:00Z", "checkCount": 0, "id": "fcf4ec63-248b-480e-bdcb-95a9ea3c34c8", "companyId": "17088351", "type": "payperiod", "_rid": "NmJkAKiCbEcU-AIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcU-AIAAAAAAA==/", "_etag": "\"a400dfb2-0000-0100-0000-687022100000\"", "_attachments": "attachments/", "_ts": 1752179216}, {"payPeriodId": "1080040383207514", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-07-01T00:00:00Z", "endDate": "2025-07-15T00:00:00Z", "submitByDate": "2025-07-29T00:00:00Z", "checkDate": "2025-07-31T00:00:00Z", "checkCount": 0, "id": "8f3fd50d-057d-4ab8-a8e2-a55374a909cf", "companyId": "17088351", "type": "payperiod", "_rid": "NmJkAKiCbEcV-AIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcV-AIAAAAAAA==/", "_etag": "\"a400e2b2-0000-0100-0000-687022100000\"", "_attachments": "attachments/", "_ts": 1752179216}, {"payPeriodId": "1080040383207515", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-07-16T00:00:00Z", "endDate": "2025-07-31T00:00:00Z", "submitByDate": "2025-08-13T00:00:00Z", "checkDate": "2025-08-15T00:00:00Z", "checkCount": 0, "id": "3c096bda-40d1-4daf-84b1-1816b58e9816", "companyId": "17088351", "type": "payperiod", "_rid": "NmJkAKiCbEcW-AIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcW-AIAAAAAAA==/", "_etag": "\"a400e3b2-0000-0100-0000-687022100000\"", "_attachments": "attachments/", "_ts": 1752179216}, {"payPeriodId": "1080040598412899", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-08-01T00:00:00Z", "endDate": "2025-08-15T00:00:00Z", "submitByDate": "2025-08-27T00:00:00Z", "checkDate": "2025-08-29T00:00:00Z", "checkCount": 0, "id": "c6b5148e-fffb-4015-ae96-964d282a9f7a", "companyId": "17088351", "type": "payperiod", "_rid": "NmJkAKiCbEcX-AIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcX-AIAAAAAAA==/", "_etag": "\"a400e6b2-0000-0100-0000-687022100000\"", "_attachments": "attachments/", "_ts": 1752179216}, {"payPeriodId": "1080040598412900", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-08-16T00:00:00Z", "endDate": "2025-08-31T00:00:00Z", "submitByDate": "2025-09-11T00:00:00Z", "checkDate": "2025-09-15T00:00:00Z", "checkCount": 0, "id": "a0398f67-9177-49c4-80bf-1b13dc1a11bd", "companyId": "17088351", "type": "payperiod", "_rid": "NmJkAKiCbEcY-AIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcY-AIAAAAAAA==/", "_etag": "\"a400e8b2-0000-0100-0000-687022100000\"", "_attachments": "attachments/", "_ts": 1752179216}, {"payPeriodId": "1080040820008149", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-09-01T00:00:00Z", "endDate": "2025-09-15T00:00:00Z", "submitByDate": "2025-09-26T00:00:00Z", "checkDate": "2025-09-30T00:00:00Z", "checkCount": 0, "id": "bf27dc83-28c3-425d-8cd4-47cf07a01ab0", "companyId": "17088351", "type": "payperiod", "_rid": "NmJkAKiCbEcZ-AIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcZ-AIAAAAAAA==/", "_etag": "\"a400e9b2-0000-0100-0000-687022110000\"", "_attachments": "attachments/", "_ts": 1752179217}, {"payPeriodId": "1080040820008150", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-09-16T00:00:00Z", "endDate": "2025-09-30T00:00:00Z", "submitByDate": "2025-10-13T00:00:00Z", "checkDate": "2025-10-15T00:00:00Z", "checkCount": 0, "id": "ae0e55ab-8742-47dd-bcf7-7227a656dcd3", "companyId": "17088351", "type": "payperiod", "_rid": "NmJkAKiCbEca-AIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEca-AIAAAAAAA==/", "_etag": "\"a400ecb2-0000-0100-0000-687022110000\"", "_attachments": "attachments/", "_ts": 1752179217}], "links": [{"rel": "self", "href": "https://ca-payroll-ai-mock-sb-001-secure.nicebeach-8a7a52ab.eastus.azurecontainerapps.io/ca/companies/17088351/payperiods"}]}, "status_code": 200}