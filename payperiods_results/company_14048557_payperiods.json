{"success": true, "company_id": "14048557", "data": {"metadata": {"contentItemCount": 44}, "content": [{"payPeriodId": "1050102902228931", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2024-12-22T00:00:00Z", "endDate": "2025-01-04T00:00:00Z", "submitByDate": "2025-01-08T00:00:00Z", "checkDate": "2025-01-10T00:00:00Z", "checkCount": 6, "id": "bbb26b88-4280-458d-92a5-c1caee9b4874", "companyId": "14048557", "type": "payperiod", "_rid": "NmJkAKiCbEddNwMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEddNwMAAAAAAA==/", "_etag": "\"a5001566-0000-0100-0000-687026db0000\"", "_attachments": "attachments/", "_ts": 1752180443}, {"payPeriodId": "1050103381923721", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-01-05T00:00:00Z", "endDate": "2025-01-18T00:00:00Z", "submitByDate": "2025-01-22T00:00:00Z", "checkDate": "2025-01-24T00:00:00Z", "checkCount": 8, "id": "19d82fb9-b4a2-4d54-a93c-ed4ec50932ef", "companyId": "14048557", "type": "payperiod", "_rid": "NmJkAKiCbEdeNwMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdeNwMAAAAAAA==/", "_etag": "\"a5001766-0000-0100-0000-687026dc0000\"", "_attachments": "attachments/", "_ts": 1752180444}, {"payPeriodId": "1050103838976304", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-01-19T00:00:00Z", "endDate": "2025-02-01T00:00:00Z", "submitByDate": "2025-02-05T00:00:00Z", "checkDate": "2025-02-07T00:00:00Z", "checkCount": 8, "id": "c91adb10-1d47-4b5f-a3a9-e3b07b3eff64", "companyId": "14048557", "type": "payperiod", "_rid": "NmJkAKiCbEdfNwMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdfNwMAAAAAAA==/", "_etag": "\"a5001c66-0000-0100-0000-687026dc0000\"", "_attachments": "attachments/", "_ts": 1752180444}, {"payPeriodId": "1050107471610173", "status": "COMPLETED", "description": "ADD ON CKS", "startDate": "2025-01-19T00:00:00Z", "endDate": "2025-02-01T00:00:00Z", "submitByDate": "2025-02-06T00:00:00Z", "checkDate": "2025-02-07T00:00:00Z", "checkCount": 2, "id": "43fcb0f4-54f9-49f0-a9c8-8f6001eb04e2", "companyId": "14048557", "type": "payperiod", "_rid": "NmJkAKiCbEdgNwMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdgNwMAAAAAAA==/", "_etag": "\"a5001d66-0000-0100-0000-687026dc0000\"", "_attachments": "attachments/", "_ts": 1752180444}, {"payPeriodId": "1050104321298965", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-02-02T00:00:00Z", "endDate": "2025-02-15T00:00:00Z", "submitByDate": "2025-02-19T00:00:00Z", "checkDate": "2025-02-21T00:00:00Z", "checkCount": 8, "id": "249e57fb-71be-46bb-8391-00dd744b4b15", "companyId": "14048557", "type": "payperiod", "_rid": "NmJkAKiCbEdhNwMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdhNwMAAAAAAA==/", "_etag": "\"a5002166-0000-0100-0000-687026dc0000\"", "_attachments": "attachments/", "_ts": 1752180444}, {"payPeriodId": "1050104800006284", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-02-16T00:00:00Z", "endDate": "2025-03-01T00:00:00Z", "submitByDate": "2025-03-05T00:00:00Z", "checkDate": "2025-03-07T00:00:00Z", "checkCount": 8, "id": "e678b72a-96e2-4f13-9e8f-614effd8c3be", "companyId": "14048557", "type": "payperiod", "_rid": "NmJkAKiCbEdiNwMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdiNwMAAAAAAA==/", "_etag": "\"a5002466-0000-0100-0000-687026dc0000\"", "_attachments": "attachments/", "_ts": 1752180444}, {"payPeriodId": "1050105278405566", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-03-02T00:00:00Z", "endDate": "2025-03-15T00:00:00Z", "submitByDate": "2025-03-19T00:00:00Z", "checkDate": "2025-03-21T00:00:00Z", "checkCount": 8, "id": "d5b75d7f-85fd-4284-a114-b3bf4feebc22", "companyId": "14048557", "type": "payperiod", "_rid": "NmJkAKiCbEdjNwMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdjNwMAAAAAAA==/", "_etag": "\"a5002966-0000-0100-0000-687026dc0000\"", "_attachments": "attachments/", "_ts": 1752180444}, {"payPeriodId": "1050105813438212", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-03-16T00:00:00Z", "endDate": "2025-03-29T00:00:00Z", "submitByDate": "2025-04-02T00:00:00Z", "checkDate": "2025-04-04T00:00:00Z", "checkCount": 0, "id": "a14a38e0-d57f-42cd-8f9a-fcac28f9ea0b", "companyId": "14048557", "type": "payperiod", "_rid": "NmJkAKiCbEdkNwMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdkNwMAAAAAAA==/", "_etag": "\"a5002d66-0000-0100-0000-687026dc0000\"", "_attachments": "attachments/", "_ts": 1752180444}, {"payPeriodId": "1050106296477265", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-03-30T00:00:00Z", "endDate": "2025-04-12T00:00:00Z", "submitByDate": "2025-04-16T00:00:00Z", "checkDate": "2025-04-18T00:00:00Z", "checkCount": 0, "id": "a0171b0d-e0a1-467d-84a3-fc5a75d34320", "companyId": "14048557", "type": "payperiod", "_rid": "NmJkAKiCbEdlNwMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdlNwMAAAAAAA==/", "_etag": "\"a5003066-0000-0100-0000-687026dc0000\"", "_attachments": "attachments/", "_ts": 1752180444}, {"payPeriodId": "1050106792519011", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-04-13T00:00:00Z", "endDate": "2025-04-26T00:00:00Z", "submitByDate": "2025-04-30T00:00:00Z", "checkDate": "2025-05-02T00:00:00Z", "checkCount": 0, "id": "e16629d8-9c4f-42ed-998a-dee7023e523c", "companyId": "14048557", "type": "payperiod", "_rid": "NmJkAKiCbEdmNwMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdmNwMAAAAAAA==/", "_etag": "\"a5003566-0000-0100-0000-687026dc0000\"", "_attachments": "attachments/", "_ts": 1752180444}, {"payPeriodId": "1050107264245187", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-04-27T00:00:00Z", "endDate": "2025-05-10T00:00:00Z", "submitByDate": "2025-05-14T00:00:00Z", "checkDate": "2025-05-16T00:00:00Z", "checkCount": 0, "id": "b9278685-fbd8-4cc4-b0b6-63881b2ddbe1", "companyId": "14048557", "type": "payperiod", "_rid": "NmJkAKiCbEdnNwMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdnNwMAAAAAAA==/", "_etag": "\"a5003a66-0000-0100-0000-687026dc0000\"", "_attachments": "attachments/", "_ts": 1752180444}, {"payPeriodId": "1050107744968363", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-05-11T00:00:00Z", "endDate": "2025-05-24T00:00:00Z", "submitByDate": "2025-05-28T00:00:00Z", "checkDate": "2025-05-30T00:00:00Z", "checkCount": 0, "id": "44d05c18-6aff-4ca7-8de2-13fda687e6c7", "companyId": "14048557", "type": "payperiod", "_rid": "NmJkAKiCbEdoNwMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdoNwMAAAAAAA==/", "_etag": "\"a5003d66-0000-0100-0000-687026dc0000\"", "_attachments": "attachments/", "_ts": 1752180444}, {"payPeriodId": "1050108217940373", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-05-25T00:00:00Z", "endDate": "2025-06-07T00:00:00Z", "submitByDate": "2025-06-11T00:00:00Z", "checkDate": "2025-06-13T00:00:00Z", "checkCount": 0, "id": "f3f2318d-32fa-4ea1-9187-24a4b84dd621", "companyId": "14048557", "type": "payperiod", "_rid": "NmJkAKiCbEdpNwMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdpNwMAAAAAAA==/", "_etag": "\"a5003f66-0000-0100-0000-687026dc0000\"", "_attachments": "attachments/", "_ts": 1752180444}, {"payPeriodId": "1050108688513306", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-06-08T00:00:00Z", "endDate": "2025-06-21T00:00:00Z", "submitByDate": "2025-06-25T00:00:00Z", "checkDate": "2025-06-27T00:00:00Z", "checkCount": 0, "id": "0cdde7cb-3533-4918-9b7b-81620bddf52f", "companyId": "14048557", "type": "payperiod", "_rid": "NmJkAKiCbEdqNwMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdqNwMAAAAAAA==/", "_etag": "\"a5004266-0000-0100-0000-687026dc0000\"", "_attachments": "attachments/", "_ts": 1752180444}, {"payPeriodId": "1050109172516168", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-06-22T00:00:00Z", "endDate": "2025-07-05T00:00:00Z", "submitByDate": "2025-07-09T00:00:00Z", "checkDate": "2025-07-11T00:00:00Z", "checkCount": 0, "id": "809fc91c-a352-4a9f-a092-4856ab7bc279", "companyId": "14048557", "type": "payperiod", "_rid": "NmJkAKiCbEdrNwMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdrNwMAAAAAAA==/", "_etag": "\"a5004366-0000-0100-0000-687026dd0000\"", "_attachments": "attachments/", "_ts": 1752180445}, {"payPeriodId": "1050109671661513", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-07-06T00:00:00Z", "endDate": "2025-07-19T00:00:00Z", "submitByDate": "2025-07-23T00:00:00Z", "checkDate": "2025-07-25T00:00:00Z", "checkCount": 0, "id": "af81859d-ca39-4778-861d-02b3822de238", "companyId": "14048557", "type": "payperiod", "_rid": "NmJkAKiCbEdsNwMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdsNwMAAAAAAA==/", "_etag": "\"a5004666-0000-0100-0000-687026dd0000\"", "_attachments": "attachments/", "_ts": 1752180445}, {"payPeriodId": "1050110136766633", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-07-20T00:00:00Z", "endDate": "2025-08-02T00:00:00Z", "submitByDate": "2025-08-06T00:00:00Z", "checkDate": "2025-08-08T00:00:00Z", "checkCount": 0, "id": "ed91e117-8ac1-40c3-807f-853c100e1c83", "companyId": "14048557", "type": "payperiod", "_rid": "NmJkAKiCbEdtNwMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdtNwMAAAAAAA==/", "_etag": "\"a5004766-0000-0100-0000-687026dd0000\"", "_attachments": "attachments/", "_ts": 1752180445}, {"payPeriodId": "1050110632041328", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-08-03T00:00:00Z", "endDate": "2025-08-16T00:00:00Z", "submitByDate": "2025-08-20T00:00:00Z", "checkDate": "2025-08-22T00:00:00Z", "checkCount": 0, "id": "21a9d7bd-3c75-4146-afd1-0574a7f4077d", "companyId": "14048557", "type": "payperiod", "_rid": "NmJkAKiCbEduNwMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEduNwMAAAAAAA==/", "_etag": "\"a5004966-0000-0100-0000-687026dd0000\"", "_attachments": "attachments/", "_ts": 1752180445}, {"payPeriodId": "1050111148897483", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-08-17T00:00:00Z", "endDate": "2025-08-30T00:00:00Z", "submitByDate": "2025-09-03T00:00:00Z", "checkDate": "2025-09-05T00:00:00Z", "checkCount": 0, "id": "a746d962-49f7-402c-a242-cfa3da8effd1", "companyId": "14048557", "type": "payperiod", "_rid": "NmJkAKiCbEdvNwMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdvNwMAAAAAAA==/", "_etag": "\"a5004d66-0000-0100-0000-687026dd0000\"", "_attachments": "attachments/", "_ts": 1752180445}, {"payPeriodId": "1050111632191952", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-08-31T00:00:00Z", "endDate": "2025-09-13T00:00:00Z", "submitByDate": "2025-09-17T00:00:00Z", "checkDate": "2025-09-19T00:00:00Z", "checkCount": 0, "id": "b95e6ee5-b581-4987-8507-2cc25836686f", "companyId": "14048557", "type": "payperiod", "_rid": "NmJkAKiCbEdwNwMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdwNwMAAAAAAA==/", "_etag": "\"a5005466-0000-0100-0000-687026dd0000\"", "_attachments": "attachments/", "_ts": 1752180445}, {"payPeriodId": "1050112129516821", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-09-14T00:00:00Z", "endDate": "2025-09-27T00:00:00Z", "submitByDate": "2025-10-01T00:00:00Z", "checkDate": "2025-10-03T00:00:00Z", "checkCount": 0, "id": "f02b25f5-0577-48e1-b9da-0ce421f538d9", "companyId": "14048557", "type": "payperiod", "_rid": "NmJkAKiCbEdxNwMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdxNwMAAAAAAA==/", "_etag": "\"a5005b66-0000-0100-0000-687026dd0000\"", "_attachments": "attachments/", "_ts": 1752180445}, {"payPeriodId": "1050112636890488", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-09-28T00:00:00Z", "endDate": "2025-10-11T00:00:00Z", "submitByDate": "2025-10-15T00:00:00Z", "checkDate": "2025-10-17T00:00:00Z", "checkCount": 0, "id": "0f61822e-1861-46a1-9d3b-bcf4e1a5e632", "companyId": "14048557", "type": "payperiod", "_rid": "NmJkAKiCbEdyNwMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdyNwMAAAAAAA==/", "_etag": "\"a5005d66-0000-0100-0000-687026dd0000\"", "_attachments": "attachments/", "_ts": 1752180445}, {"payPeriodId": "1050102902228931", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2024-12-22T00:00:00Z", "endDate": "2025-01-04T00:00:00Z", "submitByDate": "2025-01-08T00:00:00Z", "checkDate": "2025-01-10T00:00:00Z", "checkCount": 6, "id": "25739f29-09b5-4228-8f98-abb091acfde3", "companyId": "14048557", "type": "payperiod", "_rid": "NmJkAKiCbEd9NwMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEd9NwMAAAAAAA==/", "_etag": "\"a5008666-0000-0100-0000-687026de0000\"", "_attachments": "attachments/", "_ts": 1752180446}, {"payPeriodId": "1050103381923721", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-01-05T00:00:00Z", "endDate": "2025-01-18T00:00:00Z", "submitByDate": "2025-01-22T00:00:00Z", "checkDate": "2025-01-24T00:00:00Z", "checkCount": 8, "id": "9f9ee592-eff4-4744-9e4f-a269bdd36bbf", "companyId": "14048557", "type": "payperiod", "_rid": "NmJkAKiCbEd+NwMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEd+NwMAAAAAAA==/", "_etag": "\"a5008a66-0000-0100-0000-687026de0000\"", "_attachments": "attachments/", "_ts": 1752180446}, {"payPeriodId": "1050103838976304", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-01-19T00:00:00Z", "endDate": "2025-02-01T00:00:00Z", "submitByDate": "2025-02-05T00:00:00Z", "checkDate": "2025-02-07T00:00:00Z", "checkCount": 8, "id": "214a580f-3745-4b7e-b150-8eda6c8ce871", "companyId": "14048557", "type": "payperiod", "_rid": "NmJkAKiCbEd-NwMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEd-NwMAAAAAAA==/", "_etag": "\"a5008e66-0000-0100-0000-687026de0000\"", "_attachments": "attachments/", "_ts": 1752180446}, {"payPeriodId": "1050107471610173", "status": "COMPLETED", "description": "ADD ON CKS", "startDate": "2025-01-19T00:00:00Z", "endDate": "2025-02-01T00:00:00Z", "submitByDate": "2025-02-06T00:00:00Z", "checkDate": "2025-02-07T00:00:00Z", "checkCount": 2, "id": "974ef973-005a-44d2-b45e-9a1f73f67e89", "companyId": "14048557", "type": "payperiod", "_rid": "NmJkAKiCbEeANwMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeANwMAAAAAAA==/", "_etag": "\"a5009166-0000-0100-0000-687026de0000\"", "_attachments": "attachments/", "_ts": 1752180446}, {"payPeriodId": "1050104321298965", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-02-02T00:00:00Z", "endDate": "2025-02-15T00:00:00Z", "submitByDate": "2025-02-19T00:00:00Z", "checkDate": "2025-02-21T00:00:00Z", "checkCount": 8, "id": "8751e363-37e9-48b7-999c-6034819ab5b5", "companyId": "14048557", "type": "payperiod", "_rid": "NmJkAKiCbEeBNwMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeBNwMAAAAAAA==/", "_etag": "\"a5009466-0000-0100-0000-687026de0000\"", "_attachments": "attachments/", "_ts": 1752180446}, {"payPeriodId": "1050104800006284", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-02-16T00:00:00Z", "endDate": "2025-03-01T00:00:00Z", "submitByDate": "2025-03-05T00:00:00Z", "checkDate": "2025-03-07T00:00:00Z", "checkCount": 8, "id": "b4dfddbb-c233-4b52-8b24-4a0c7eb87f8e", "companyId": "14048557", "type": "payperiod", "_rid": "NmJkAKiCbEeCNwMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeCNwMAAAAAAA==/", "_etag": "\"a5009966-0000-0100-0000-687026de0000\"", "_attachments": "attachments/", "_ts": 1752180446}, {"payPeriodId": "1050105278405566", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-03-02T00:00:00Z", "endDate": "2025-03-15T00:00:00Z", "submitByDate": "2025-03-19T00:00:00Z", "checkDate": "2025-03-21T00:00:00Z", "checkCount": 8, "id": "e46dcf20-f456-42bf-bc54-d3000db46a80", "companyId": "14048557", "type": "payperiod", "_rid": "NmJkAKiCbEeDNwMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeDNwMAAAAAAA==/", "_etag": "\"a5009b66-0000-0100-0000-687026df0000\"", "_attachments": "attachments/", "_ts": 1752180447}, {"payPeriodId": "1050105813438212", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-03-16T00:00:00Z", "endDate": "2025-03-29T00:00:00Z", "submitByDate": "2025-04-02T00:00:00Z", "checkDate": "2025-04-04T00:00:00Z", "checkCount": 8, "id": "c44f5d97-9520-4a26-b9b8-a778e6c28321", "companyId": "14048557", "type": "payperiod", "_rid": "NmJkAKiCbEeENwMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeENwMAAAAAAA==/", "_etag": "\"a5009c66-0000-0100-0000-687026df0000\"", "_attachments": "attachments/", "_ts": 1752180447}, {"payPeriodId": "1050106296477265", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-03-30T00:00:00Z", "endDate": "2025-04-12T00:00:00Z", "submitByDate": "2025-04-16T00:00:00Z", "checkDate": "2025-04-18T00:00:00Z", "checkCount": 8, "id": "2f557476-1dd3-4d7c-bf1e-2ef8a24a9b45", "companyId": "14048557", "type": "payperiod", "_rid": "NmJkAKiCbEeFNwMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeFNwMAAAAAAA==/", "_etag": "\"a5009f66-0000-0100-0000-687026df0000\"", "_attachments": "attachments/", "_ts": 1752180447}, {"payPeriodId": "1050106792519011", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-04-13T00:00:00Z", "endDate": "2025-04-26T00:00:00Z", "submitByDate": "2025-04-30T00:00:00Z", "checkDate": "2025-05-02T00:00:00Z", "checkCount": 8, "id": "9a823300-955c-4b98-b28b-8f479fec915a", "companyId": "14048557", "type": "payperiod", "_rid": "NmJkAKiCbEeGNwMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeGNwMAAAAAAA==/", "_etag": "\"a500a466-0000-0100-0000-687026df0000\"", "_attachments": "attachments/", "_ts": 1752180447}, {"payPeriodId": "1050107264245187", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-04-27T00:00:00Z", "endDate": "2025-05-10T00:00:00Z", "submitByDate": "2025-05-14T00:00:00Z", "checkDate": "2025-05-16T00:00:00Z", "checkCount": 8, "id": "194549a3-4edb-4a4b-b926-ab7800875b1c", "companyId": "14048557", "type": "payperiod", "_rid": "NmJkAKiCbEeHNwMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeHNwMAAAAAAA==/", "_etag": "\"a500a766-0000-0100-0000-687026df0000\"", "_attachments": "attachments/", "_ts": 1752180447}, {"payPeriodId": "1050107744968363", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-05-11T00:00:00Z", "endDate": "2025-05-24T00:00:00Z", "submitByDate": "2025-05-28T00:00:00Z", "checkDate": "2025-05-30T00:00:00Z", "checkCount": 8, "id": "07312f54-587f-47d0-8feb-795612e8ba57", "companyId": "14048557", "type": "payperiod", "_rid": "NmJkAKiCbEeINwMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeINwMAAAAAAA==/", "_etag": "\"a500a966-0000-0100-0000-687026df0000\"", "_attachments": "attachments/", "_ts": 1752180447}, {"payPeriodId": "1050108217940373", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-05-25T00:00:00Z", "endDate": "2025-06-07T00:00:00Z", "submitByDate": "2025-06-11T00:00:00Z", "checkDate": "2025-06-13T00:00:00Z", "checkCount": 8, "id": "4f4bb78d-1b4f-421e-8e83-4fde7709eb4d", "companyId": "14048557", "type": "payperiod", "_rid": "NmJkAKiCbEeJNwMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeJNwMAAAAAAA==/", "_etag": "\"a500ab66-0000-0100-0000-687026df0000\"", "_attachments": "attachments/", "_ts": 1752180447}, {"payPeriodId": "1050108688513306", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-06-08T00:00:00Z", "endDate": "2025-06-21T00:00:00Z", "submitByDate": "2025-06-25T00:00:00Z", "checkDate": "2025-06-27T00:00:00Z", "checkCount": 8, "id": "d518a510-b358-453b-8843-837c971e6c14", "companyId": "14048557", "type": "payperiod", "_rid": "NmJkAKiCbEeKNwMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeKNwMAAAAAAA==/", "_etag": "\"a500ad66-0000-0100-0000-687026df0000\"", "_attachments": "attachments/", "_ts": 1752180447}, {"payPeriodId": "1050109172516168", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-06-22T00:00:00Z", "endDate": "2025-07-05T00:00:00Z", "submitByDate": "2025-07-09T00:00:00Z", "checkDate": "2025-07-11T00:00:00Z", "checkCount": 0, "id": "bcce2116-8024-426e-931e-1ef3fd0d2d61", "companyId": "14048557", "type": "payperiod", "_rid": "NmJkAKiCbEeLNwMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeLNwMAAAAAAA==/", "_etag": "\"a500b166-0000-0100-0000-687026df0000\"", "_attachments": "attachments/", "_ts": 1752180447}, {"payPeriodId": "1050109671661513", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-07-06T00:00:00Z", "endDate": "2025-07-19T00:00:00Z", "submitByDate": "2025-07-23T00:00:00Z", "checkDate": "2025-07-25T00:00:00Z", "checkCount": 0, "id": "fbb43ee9-b380-46a3-8c75-3095426ecde7", "companyId": "14048557", "type": "payperiod", "_rid": "NmJkAKiCbEeMNwMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeMNwMAAAAAAA==/", "_etag": "\"a500b566-0000-0100-0000-687026df0000\"", "_attachments": "attachments/", "_ts": 1752180447}, {"payPeriodId": "1050110136766633", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-07-20T00:00:00Z", "endDate": "2025-08-02T00:00:00Z", "submitByDate": "2025-08-06T00:00:00Z", "checkDate": "2025-08-08T00:00:00Z", "checkCount": 0, "id": "4d9c574f-bf5e-40ac-8bfa-280d6f1c1d8f", "companyId": "14048557", "type": "payperiod", "_rid": "NmJkAKiCbEeNNwMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeNNwMAAAAAAA==/", "_etag": "\"a500b966-0000-0100-0000-687026df0000\"", "_attachments": "attachments/", "_ts": 1752180447}, {"payPeriodId": "1050110632041328", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-08-03T00:00:00Z", "endDate": "2025-08-16T00:00:00Z", "submitByDate": "2025-08-20T00:00:00Z", "checkDate": "2025-08-22T00:00:00Z", "checkCount": 0, "id": "e01180bf-79a4-446e-a0e0-e241bd2ad6db", "companyId": "14048557", "type": "payperiod", "_rid": "NmJkAKiCbEeONwMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeONwMAAAAAAA==/", "_etag": "\"a500ba66-0000-0100-0000-687026df0000\"", "_attachments": "attachments/", "_ts": 1752180447}, {"payPeriodId": "1050111148897483", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-08-17T00:00:00Z", "endDate": "2025-08-30T00:00:00Z", "submitByDate": "2025-09-03T00:00:00Z", "checkDate": "2025-09-05T00:00:00Z", "checkCount": 0, "id": "7335e5ed-bdfb-4297-adfe-8f17acfa1a91", "companyId": "14048557", "type": "payperiod", "_rid": "NmJkAKiCbEePNwMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEePNwMAAAAAAA==/", "_etag": "\"a500be66-0000-0100-0000-687026df0000\"", "_attachments": "attachments/", "_ts": 1752180447}, {"payPeriodId": "1050111632191952", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-08-31T00:00:00Z", "endDate": "2025-09-13T00:00:00Z", "submitByDate": "2025-09-17T00:00:00Z", "checkDate": "2025-09-19T00:00:00Z", "checkCount": 0, "id": "31520e28-eda1-4434-9734-3f42acb86c6b", "companyId": "14048557", "type": "payperiod", "_rid": "NmJkAKiCbEeQNwMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeQNwMAAAAAAA==/", "_etag": "\"a500c166-0000-0100-0000-687026e00000\"", "_attachments": "attachments/", "_ts": 1752180448}, {"payPeriodId": "1050112129516821", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-09-14T00:00:00Z", "endDate": "2025-09-27T00:00:00Z", "submitByDate": "2025-10-01T00:00:00Z", "checkDate": "2025-10-03T00:00:00Z", "checkCount": 0, "id": "fa3a889f-3a76-4444-a374-c35ead538b03", "companyId": "14048557", "type": "payperiod", "_rid": "NmJkAKiCbEeRNwMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeRNwMAAAAAAA==/", "_etag": "\"a500c466-0000-0100-0000-687026e00000\"", "_attachments": "attachments/", "_ts": 1752180448}, {"payPeriodId": "1050112636890488", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-09-28T00:00:00Z", "endDate": "2025-10-11T00:00:00Z", "submitByDate": "2025-10-15T00:00:00Z", "checkDate": "2025-10-17T00:00:00Z", "checkCount": 0, "id": "3365255d-9d97-41e2-85fa-d354e832fa28", "companyId": "14048557", "type": "payperiod", "_rid": "NmJkAKiCbEeSNwMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeSNwMAAAAAAA==/", "_etag": "\"a500c866-0000-0100-0000-687026e00000\"", "_attachments": "attachments/", "_ts": 1752180448}], "links": [{"rel": "self", "href": "https://ca-payroll-ai-mock-sb-001-secure.nicebeach-8a7a52ab.eastus.azurecontainerapps.io/ca/companies/14048557/payperiods"}]}, "status_code": 200}