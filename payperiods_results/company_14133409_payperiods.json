{"success": true, "company_id": "14133409", "data": {"metadata": {"contentItemCount": 20}, "content": [{"payPeriodId": "1050103134867342", "intervalCode": "MONTHLY", "status": "COMPLETED", "description": "Monthly Payroll (1)", "startDate": "2025-01-01T00:00:00Z", "endDate": "2025-01-31T00:00:00Z", "submitByDate": "2025-01-29T00:00:00Z", "checkDate": "2025-01-31T00:00:00Z", "checkCount": 1, "id": "6c72387e-2bd9-46af-a51b-460d72e5ec2d", "companyId": "14133409", "type": "payperiod", "_rid": "NmJkAKiCbEeVmQQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeVmQQAAAAAAA==/", "_etag": "\"a9001213-0000-0100-0000-687044c40000\"", "_attachments": "attachments/", "_ts": 1752188100}, {"payPeriodId": "1050104092975028", "intervalCode": "MONTHLY", "status": "COMPLETED", "description": "Monthly Payroll (1)", "startDate": "2025-02-01T00:00:00Z", "endDate": "2025-02-28T00:00:00Z", "submitByDate": "2025-02-26T00:00:00Z", "checkDate": "2025-02-28T00:00:00Z", "checkCount": 1, "id": "fc69e962-9c6d-4885-b1f6-c082b2906fc6", "companyId": "14133409", "type": "payperiod", "_rid": "NmJkAKiCbEeWmQQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeWmQQAAAAAAA==/", "_etag": "\"a9001513-0000-0100-0000-687044c40000\"", "_attachments": "attachments/", "_ts": 1752188100}, {"payPeriodId": "1050104867375885", "intervalCode": "MONTHLY", "status": "COMPLETED", "description": "Monthly Payroll (1)", "startDate": "2025-03-01T00:00:00Z", "endDate": "2025-03-31T00:00:00Z", "submitByDate": "2025-03-27T00:00:00Z", "checkDate": "2025-03-31T00:00:00Z", "checkCount": 1, "id": "416e06c9-7559-4963-8c5d-9eee53f4a6f9", "companyId": "14133409", "type": "payperiod", "_rid": "NmJkAKiCbEeXmQQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeXmQQAAAAAAA==/", "_etag": "\"a9001613-0000-0100-0000-687044c40000\"", "_attachments": "attachments/", "_ts": 1752188100}, {"payPeriodId": "1050106296650446", "intervalCode": "MONTHLY", "status": "INITIAL", "description": "Monthly Payroll (1)", "startDate": "2025-04-01T00:00:00Z", "endDate": "2025-04-30T00:00:00Z", "submitByDate": "2025-04-28T00:00:00Z", "checkDate": "2025-04-30T00:00:00Z", "checkCount": 0, "id": "d6ce9028-7795-4ab0-ba50-7fca6421a501", "companyId": "14133409", "type": "payperiod", "_rid": "NmJkAKiCbEeYmQQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeYmQQAAAAAAA==/", "_etag": "\"a9001713-0000-0100-0000-687044c40000\"", "_attachments": "attachments/", "_ts": 1752188100}, {"payPeriodId": "1050107264425053", "intervalCode": "MONTHLY", "status": "INITIAL", "description": "Monthly Payroll (1)", "startDate": "2025-05-01T00:00:00Z", "endDate": "2025-05-31T00:00:00Z", "submitByDate": "2025-05-28T00:00:00Z", "checkDate": "2025-05-30T00:00:00Z", "checkCount": 0, "id": "92b237b0-4861-488a-9844-d35179457801", "companyId": "14133409", "type": "payperiod", "_rid": "NmJkAKiCbEeZmQQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeZmQQAAAAAAA==/", "_etag": "\"a9001913-0000-0100-0000-687044c50000\"", "_attachments": "attachments/", "_ts": 1752188101}, {"payPeriodId": "1050112554881772", "status": "INITIAL", "description": "Monthly Payroll (1)", "startDate": "2025-06-01T00:00:00Z", "endDate": "2025-06-30T00:00:00Z", "submitByDate": "2025-06-26T00:00:00Z", "checkDate": "2025-07-01T00:00:00Z", "checkCount": 0, "id": "eb19d33d-a3d1-4661-a96d-e85e1f80779d", "companyId": "14133409", "type": "payperiod", "_rid": "NmJkAKiCbEeamQQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeamQQAAAAAAA==/", "_etag": "\"a9001a13-0000-0100-0000-687044c50000\"", "_attachments": "attachments/", "_ts": 1752188101}, {"payPeriodId": "1050109420445698", "intervalCode": "MONTHLY", "status": "INITIAL", "description": "Monthly Payroll (1)", "startDate": "2025-07-01T00:00:00Z", "endDate": "2025-07-31T00:00:00Z", "submitByDate": "2025-07-29T00:00:00Z", "checkDate": "2025-07-31T00:00:00Z", "checkCount": 0, "id": "4136d3cf-241a-4d4f-bff9-2e4ea496ae84", "companyId": "14133409", "type": "payperiod", "_rid": "NmJkAKiCbEebmQQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEebmQQAAAAAAA==/", "_etag": "\"a9001b13-0000-0100-0000-687044c50000\"", "_attachments": "attachments/", "_ts": 1752188101}, {"payPeriodId": "1050110397937380", "intervalCode": "MONTHLY", "status": "INITIAL", "description": "Monthly Payroll (1)", "startDate": "2025-08-01T00:00:00Z", "endDate": "2025-08-31T00:00:00Z", "submitByDate": "2025-08-27T00:00:00Z", "checkDate": "2025-08-29T00:00:00Z", "checkCount": 0, "id": "cbe0dd48-71e8-4968-af89-1a081d124513", "companyId": "14133409", "type": "payperiod", "_rid": "NmJkAKiCbEecmQQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEecmQQAAAAAAA==/", "_etag": "\"a9001d13-0000-0100-0000-687044c50000\"", "_attachments": "attachments/", "_ts": 1752188101}, {"payPeriodId": "1050111632286509", "intervalCode": "MONTHLY", "status": "INITIAL", "description": "Monthly Payroll (1)", "startDate": "2025-09-01T00:00:00Z", "endDate": "2025-09-30T00:00:00Z", "submitByDate": "2025-09-26T00:00:00Z", "checkDate": "2025-09-30T00:00:00Z", "checkCount": 0, "id": "3a19df81-f4a3-4eb6-bc78-6b37542243eb", "companyId": "14133409", "type": "payperiod", "_rid": "NmJkAKiCbEedmQQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEedmQQAAAAAAA==/", "_etag": "\"a9001e13-0000-0100-0000-687044c50000\"", "_attachments": "attachments/", "_ts": 1752188101}, {"payPeriodId": "1050112637416662", "intervalCode": "MONTHLY", "status": "INITIAL", "description": "Monthly Payroll (1)", "startDate": "2025-10-01T00:00:00Z", "endDate": "2025-10-31T00:00:00Z", "submitByDate": "2025-10-29T00:00:00Z", "checkDate": "2025-10-31T00:00:00Z", "checkCount": 0, "id": "6314c58b-d96b-4de1-85af-df6d8fd226fe", "companyId": "14133409", "type": "payperiod", "_rid": "NmJkAKiCbEeemQQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeemQQAAAAAAA==/", "_etag": "\"a9001f13-0000-0100-0000-687044c50000\"", "_attachments": "attachments/", "_ts": 1752188101}, {"payPeriodId": "1050103134867342", "intervalCode": "MONTHLY", "status": "COMPLETED", "description": "Monthly Payroll (1)", "startDate": "2025-01-01T00:00:00Z", "endDate": "2025-01-31T00:00:00Z", "submitByDate": "2025-01-29T00:00:00Z", "checkDate": "2025-01-31T00:00:00Z", "checkCount": 1, "id": "dadae596-407e-4081-adf3-7e489ba5ff50", "companyId": "14133409", "type": "payperiod", "_rid": "NmJkAKiCbEefmQQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEefmQQAAAAAAA==/", "_etag": "\"a9002013-0000-0100-0000-687044c50000\"", "_attachments": "attachments/", "_ts": 1752188101}, {"payPeriodId": "1050104092975028", "intervalCode": "MONTHLY", "status": "COMPLETED", "description": "Monthly Payroll (1)", "startDate": "2025-02-01T00:00:00Z", "endDate": "2025-02-28T00:00:00Z", "submitByDate": "2025-02-26T00:00:00Z", "checkDate": "2025-02-28T00:00:00Z", "checkCount": 1, "id": "1edee9bb-1ce7-43a8-9f00-66023d196bf8", "companyId": "14133409", "type": "payperiod", "_rid": "NmJkAKiCbEegmQQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEegmQQAAAAAAA==/", "_etag": "\"a9002113-0000-0100-0000-687044c50000\"", "_attachments": "attachments/", "_ts": 1752188101}, {"payPeriodId": "1050104867375885", "intervalCode": "MONTHLY", "status": "COMPLETED", "description": "Monthly Payroll (1)", "startDate": "2025-03-01T00:00:00Z", "endDate": "2025-03-31T00:00:00Z", "submitByDate": "2025-03-27T00:00:00Z", "checkDate": "2025-03-31T00:00:00Z", "checkCount": 1, "id": "7fa2235f-ede9-49f6-a6b0-e84c451aafef", "companyId": "14133409", "type": "payperiod", "_rid": "NmJkAKiCbEehmQQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEehmQQAAAAAAA==/", "_etag": "\"a9002313-0000-0100-0000-687044c50000\"", "_attachments": "attachments/", "_ts": 1752188101}, {"payPeriodId": "1050106296650446", "intervalCode": "MONTHLY", "status": "COMPLETED", "description": "Monthly Payroll (1)", "startDate": "2025-04-01T00:00:00Z", "endDate": "2025-04-30T00:00:00Z", "submitByDate": "2025-04-28T00:00:00Z", "checkDate": "2025-04-30T00:00:00Z", "checkCount": 1, "id": "51821916-2812-4a97-8ef2-b0db172068ce", "companyId": "14133409", "type": "payperiod", "_rid": "NmJkAKiCbEeimQQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeimQQAAAAAAA==/", "_etag": "\"a9002413-0000-0100-0000-687044c50000\"", "_attachments": "attachments/", "_ts": 1752188101}, {"payPeriodId": "1050107264425053", "intervalCode": "MONTHLY", "status": "COMPLETED", "description": "Monthly Payroll (1)", "startDate": "2025-05-01T00:00:00Z", "endDate": "2025-05-31T00:00:00Z", "submitByDate": "2025-05-28T00:00:00Z", "checkDate": "2025-05-30T00:00:00Z", "checkCount": 1, "id": "2552d837-b202-42f3-9e58-facc49acd57c", "companyId": "14133409", "type": "payperiod", "_rid": "NmJkAKiCbEejmQQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEejmQQAAAAAAA==/", "_etag": "\"a9002513-0000-0100-0000-687044c50000\"", "_attachments": "attachments/", "_ts": 1752188101}, {"payPeriodId": "1050112554881772", "status": "COMPLETED", "description": "Monthly Payroll (1)", "startDate": "2025-06-01T00:00:00Z", "endDate": "2025-06-30T00:00:00Z", "submitByDate": "2025-06-26T00:00:00Z", "checkDate": "2025-07-01T00:00:00Z", "checkCount": 1, "id": "763243a2-4e96-4443-995f-88d37353f1ff", "companyId": "14133409", "type": "payperiod", "_rid": "NmJkAKiCbEekmQQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEekmQQAAAAAAA==/", "_etag": "\"a9002613-0000-0100-0000-687044c50000\"", "_attachments": "attachments/", "_ts": 1752188101}, {"payPeriodId": "1050109420445698", "intervalCode": "MONTHLY", "status": "INITIAL", "description": "Monthly Payroll (1)", "startDate": "2025-07-01T00:00:00Z", "endDate": "2025-07-31T00:00:00Z", "submitByDate": "2025-07-29T00:00:00Z", "checkDate": "2025-07-31T00:00:00Z", "checkCount": 0, "id": "928bfe5a-4cf5-4bf6-82d7-32d3a7fda09f", "companyId": "14133409", "type": "payperiod", "_rid": "NmJkAKiCbEelmQQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEelmQQAAAAAAA==/", "_etag": "\"a9002713-0000-0100-0000-687044c50000\"", "_attachments": "attachments/", "_ts": 1752188101}, {"payPeriodId": "1050110397937380", "intervalCode": "MONTHLY", "status": "INITIAL", "description": "Monthly Payroll (1)", "startDate": "2025-08-01T00:00:00Z", "endDate": "2025-08-31T00:00:00Z", "submitByDate": "2025-08-27T00:00:00Z", "checkDate": "2025-08-29T00:00:00Z", "checkCount": 0, "id": "2fc9a771-8273-4400-9fa3-93a14526226b", "companyId": "14133409", "type": "payperiod", "_rid": "NmJkAKiCbEemmQQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEemmQQAAAAAAA==/", "_etag": "\"a9002913-0000-0100-0000-687044c60000\"", "_attachments": "attachments/", "_ts": 1752188102}, {"payPeriodId": "1050111632286509", "intervalCode": "MONTHLY", "status": "INITIAL", "description": "Monthly Payroll (1)", "startDate": "2025-09-01T00:00:00Z", "endDate": "2025-09-30T00:00:00Z", "submitByDate": "2025-09-26T00:00:00Z", "checkDate": "2025-09-30T00:00:00Z", "checkCount": 0, "id": "f6c6a537-1fd4-4aea-8536-4144eceba53b", "companyId": "14133409", "type": "payperiod", "_rid": "NmJkAKiCbEenmQQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEenmQQAAAAAAA==/", "_etag": "\"a9002d13-0000-0100-0000-687044c60000\"", "_attachments": "attachments/", "_ts": 1752188102}, {"payPeriodId": "1050112637416662", "intervalCode": "MONTHLY", "status": "INITIAL", "description": "Monthly Payroll (1)", "startDate": "2025-10-01T00:00:00Z", "endDate": "2025-10-31T00:00:00Z", "submitByDate": "2025-10-29T00:00:00Z", "checkDate": "2025-10-31T00:00:00Z", "checkCount": 0, "id": "71425dda-18ed-4fc3-bf17-bb7ef86f9f3d", "companyId": "14133409", "type": "payperiod", "_rid": "NmJkAKiCbEeomQQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeomQQAAAAAAA==/", "_etag": "\"a9002e13-0000-0100-0000-687044c60000\"", "_attachments": "attachments/", "_ts": 1752188102}], "links": [{"rel": "self", "href": "https://ca-payroll-ai-mock-sb-001-secure.nicebeach-8a7a52ab.eastus.azurecontainerapps.io/ca/companies/14133409/payperiods"}]}, "status_code": 200}