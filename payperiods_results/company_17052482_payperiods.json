{"success": true, "company_id": "17052482", "data": {"metadata": {"contentItemCount": 40}, "content": [{"payPeriodId": "1080039029488607", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2024-12-28T00:00:00Z", "endDate": "2025-01-12T00:00:00Z", "submitByDate": "2025-01-13T00:00:00Z", "checkDate": "2025-01-15T00:00:00Z", "checkCount": 2, "id": "c2c6a16e-be7b-4cbd-9e36-309b8ec44e46", "companyId": "17052482", "type": "payperiod", "_rid": "NmJkAKiCbEcWzgIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcWzgIAAAAAAA==/", "_etag": "\"a4000a0a-0000-0100-0000-68701e520000\"", "_attachments": "attachments/", "_ts": 1752178258}, {"payPeriodId": "1080039029488608", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-01-16T00:00:00Z", "endDate": "2025-01-31T00:00:00Z", "submitByDate": "2025-01-29T00:00:00Z", "checkDate": "2025-01-31T00:00:00Z", "checkCount": 2, "id": "b13d454f-364c-4239-9fb6-d363c4705eca", "companyId": "17052482", "type": "payperiod", "_rid": "NmJkAKiCbEcXzgIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcXzgIAAAAAAA==/", "_etag": "\"a4000d0a-0000-0100-0000-68701e530000\"", "_attachments": "attachments/", "_ts": 1752178259}, {"payPeriodId": "1080039247548063", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-01-28T00:00:00Z", "endDate": "2025-02-12T00:00:00Z", "submitByDate": "2025-02-12T00:00:00Z", "checkDate": "2025-02-14T00:00:00Z", "checkCount": 2, "id": "bc25d8d7-6b1e-42de-a7b4-29f20f4f2f13", "companyId": "17052482", "type": "payperiod", "_rid": "NmJkAKiCbEcYzgIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcYzgIAAAAAAA==/", "_etag": "\"a400100a-0000-0100-0000-68701e530000\"", "_attachments": "attachments/", "_ts": 1752178259}, {"payPeriodId": "1080039247548064", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-02-13T00:00:00Z", "endDate": "2025-02-27T00:00:00Z", "submitByDate": "2025-02-26T00:00:00Z", "checkDate": "2025-02-28T00:00:00Z", "checkCount": 2, "id": "a4a76a84-4b82-43d5-bf12-9d6422ca81f5", "companyId": "17052482", "type": "payperiod", "_rid": "NmJkAKiCbEcZzgIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcZzgIAAAAAAA==/", "_etag": "\"a400130a-0000-0100-0000-68701e530000\"", "_attachments": "attachments/", "_ts": 1752178259}, {"payPeriodId": "1080039465665020", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-02-28T00:00:00Z", "endDate": "2025-03-12T00:00:00Z", "submitByDate": "2025-03-12T00:00:00Z", "checkDate": "2025-03-14T00:00:00Z", "checkCount": 2, "id": "496693a1-dd71-4bd2-87fc-c99a412c10d9", "companyId": "17052482", "type": "payperiod", "_rid": "NmJkAKiCbEcazgIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcazgIAAAAAAA==/", "_etag": "\"a400170a-0000-0100-0000-68701e530000\"", "_attachments": "attachments/", "_ts": 1752178259}, {"payPeriodId": "1080039465665021", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-03-13T00:00:00Z", "endDate": "2025-03-27T00:00:00Z", "submitByDate": "2025-03-27T00:00:00Z", "checkDate": "2025-03-31T00:00:00Z", "checkCount": 2, "id": "6260287f-6111-46ce-add0-64775bb06fae", "companyId": "17052482", "type": "payperiod", "_rid": "NmJkAKiCbEcbzgIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcbzgIAAAAAAA==/", "_etag": "\"a400190a-0000-0100-0000-68701e530000\"", "_attachments": "attachments/", "_ts": 1752178259}, {"payPeriodId": "1080039711589174", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-03-31T00:00:00Z", "endDate": "2025-04-12T00:00:00Z", "submitByDate": "2025-04-11T00:00:00Z", "checkDate": "2025-04-15T00:00:00Z", "checkCount": 0, "id": "b968e5b1-dc72-45e6-b4e0-cfc6f2f53e61", "companyId": "17052482", "type": "payperiod", "_rid": "NmJkAKiCbEcczgIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcczgIAAAAAAA==/", "_etag": "\"a4001b0a-0000-0100-0000-68701e530000\"", "_attachments": "attachments/", "_ts": 1752178259}, {"payPeriodId": "1080039711589175", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-04-13T00:00:00Z", "endDate": "2025-04-29T00:00:00Z", "submitByDate": "2025-04-28T00:00:00Z", "checkDate": "2025-04-30T00:00:00Z", "checkCount": 0, "id": "c6358107-5f22-49dd-a76f-92f2a0385d82", "companyId": "17052482", "type": "payperiod", "_rid": "NmJkAKiCbEcdzgIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcdzgIAAAAAAA==/", "_etag": "\"a4001e0a-0000-0100-0000-68701e530000\"", "_attachments": "attachments/", "_ts": 1752178259}, {"payPeriodId": "1080039887501532", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-04-28T00:00:00Z", "endDate": "2025-05-12T00:00:00Z", "submitByDate": "2025-05-13T00:00:00Z", "checkDate": "2025-05-15T00:00:00Z", "checkCount": 0, "id": "b7d97023-9007-432e-9f1d-29321099bdc7", "companyId": "17052482", "type": "payperiod", "_rid": "NmJkAKiCbEcezgIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcezgIAAAAAAA==/", "_etag": "\"a400210a-0000-0100-0000-68701e530000\"", "_attachments": "attachments/", "_ts": 1752178259}, {"payPeriodId": "1080039887501533", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-05-13T00:00:00Z", "endDate": "2025-05-27T00:00:00Z", "submitByDate": "2025-05-28T00:00:00Z", "checkDate": "2025-05-30T00:00:00Z", "checkCount": 0, "id": "286d954e-10a8-4562-b5cf-f5421c0288ca", "companyId": "17052482", "type": "payperiod", "_rid": "NmJkAKiCbEcfzgIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcfzgIAAAAAAA==/", "_etag": "\"a400240a-0000-0100-0000-68701e530000\"", "_attachments": "attachments/", "_ts": 1752178259}, {"payPeriodId": "1080040128239927", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-05-28T00:00:00Z", "endDate": "2025-06-12T00:00:00Z", "submitByDate": "2025-06-11T00:00:00Z", "checkDate": "2025-06-13T00:00:00Z", "checkCount": 0, "id": "f1db8c33-3d6c-43bd-9cc2-d5152847eae8", "companyId": "17052482", "type": "payperiod", "_rid": "NmJkAKiCbEcgzgIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcgzgIAAAAAAA==/", "_etag": "\"a400290a-0000-0100-0000-68701e530000\"", "_attachments": "attachments/", "_ts": 1752178259}, {"payPeriodId": "1080040128239928", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-06-13T00:00:00Z", "endDate": "2025-06-27T00:00:00Z", "submitByDate": "2025-06-26T00:00:00Z", "checkDate": "2025-06-30T00:00:00Z", "checkCount": 0, "id": "fb8518cf-645d-432a-a6e6-f24b9484df77", "companyId": "17052482", "type": "payperiod", "_rid": "NmJkAKiCbEchzgIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEchzgIAAAAAAA==/", "_etag": "\"a4002c0a-0000-0100-0000-68701e530000\"", "_attachments": "attachments/", "_ts": 1752178259}, {"payPeriodId": "1080040269615463", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-06-28T00:00:00Z", "endDate": "2025-07-12T00:00:00Z", "submitByDate": "2025-07-11T00:00:00Z", "checkDate": "2025-07-15T00:00:00Z", "checkCount": 0, "id": "b8654196-2444-48a8-adab-e6dc7b89c435", "companyId": "17052482", "type": "payperiod", "_rid": "NmJkAKiCbEcizgIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcizgIAAAAAAA==/", "_etag": "\"a4002e0a-0000-0100-0000-68701e530000\"", "_attachments": "attachments/", "_ts": 1752178259}, {"payPeriodId": "1080040269615464", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-07-13T00:00:00Z", "endDate": "2025-07-27T00:00:00Z", "submitByDate": "2025-07-29T00:00:00Z", "checkDate": "2025-07-31T00:00:00Z", "checkCount": 0, "id": "f1d67505-f03e-4382-9651-f17300e949e5", "companyId": "17052482", "type": "payperiod", "_rid": "NmJkAKiCbEcjzgIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcjzgIAAAAAAA==/", "_etag": "\"a400310a-0000-0100-0000-68701e530000\"", "_attachments": "attachments/", "_ts": 1752178259}, {"payPeriodId": "1080040496077187", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-07-28T00:00:00Z", "endDate": "2025-08-12T00:00:00Z", "submitByDate": "2025-08-13T00:00:00Z", "checkDate": "2025-08-15T00:00:00Z", "checkCount": 0, "id": "3371a1c6-ade0-4fee-8502-1e27a3c4c662", "companyId": "17052482", "type": "payperiod", "_rid": "NmJkAKiCbEckzgIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEckzgIAAAAAAA==/", "_etag": "\"a400340a-0000-0100-0000-68701e540000\"", "_attachments": "attachments/", "_ts": 1752178260}, {"payPeriodId": "1080040496077188", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-08-13T00:00:00Z", "endDate": "2025-08-27T00:00:00Z", "submitByDate": "2025-08-27T00:00:00Z", "checkDate": "2025-08-29T00:00:00Z", "checkCount": 0, "id": "2cc84eb8-bf74-4bae-b82e-4c011b1a98af", "companyId": "17052482", "type": "payperiod", "_rid": "NmJkAKiCbEclzgIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEclzgIAAAAAAA==/", "_etag": "\"a400350a-0000-0100-0000-68701e540000\"", "_attachments": "attachments/", "_ts": 1752178260}, {"payPeriodId": "1080040708445676", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-08-28T00:00:00Z", "endDate": "2025-09-12T00:00:00Z", "submitByDate": "2025-09-11T00:00:00Z", "checkDate": "2025-09-15T00:00:00Z", "checkCount": 0, "id": "97a7b19f-b4f1-4f0d-87ca-5b431d1a7c7f", "companyId": "17052482", "type": "payperiod", "_rid": "NmJkAKiCbEcmzgIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcmzgIAAAAAAA==/", "_etag": "\"a400360a-0000-0100-0000-68701e540000\"", "_attachments": "attachments/", "_ts": 1752178260}, {"payPeriodId": "1080040708445677", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-09-13T00:00:00Z", "endDate": "2025-09-27T00:00:00Z", "submitByDate": "2025-09-26T00:00:00Z", "checkDate": "2025-09-30T00:00:00Z", "checkCount": 0, "id": "b94e49a5-2370-46e0-8fdc-3293c931e3c7", "companyId": "17052482", "type": "payperiod", "_rid": "NmJkAKiCbEcnzgIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcnzgIAAAAAAA==/", "_etag": "\"a400390a-0000-0100-0000-68701e540000\"", "_attachments": "attachments/", "_ts": 1752178260}, {"payPeriodId": "1080040905116561", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-09-28T00:00:00Z", "endDate": "2025-10-12T00:00:00Z", "submitByDate": "2025-10-13T00:00:00Z", "checkDate": "2025-10-15T00:00:00Z", "checkCount": 0, "id": "e79537a6-60df-4c12-a9b1-980fdd625cea", "companyId": "17052482", "type": "payperiod", "_rid": "NmJkAKiCbEcozgIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcozgIAAAAAAA==/", "_etag": "\"a4003a0a-0000-0100-0000-68701e540000\"", "_attachments": "attachments/", "_ts": 1752178260}, {"payPeriodId": "1080040905116562", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-10-13T00:00:00Z", "endDate": "2025-10-27T00:00:00Z", "submitByDate": "2025-10-29T00:00:00Z", "checkDate": "2025-10-31T00:00:00Z", "checkCount": 0, "id": "6d52b156-bd65-4ba8-af1b-1a6b25842444", "companyId": "17052482", "type": "payperiod", "_rid": "NmJkAKiCbEcpzgIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcpzgIAAAAAAA==/", "_etag": "\"a4003d0a-0000-0100-0000-68701e540000\"", "_attachments": "attachments/", "_ts": 1752178260}, {"payPeriodId": "1080039029488607", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2024-12-28T00:00:00Z", "endDate": "2025-01-12T00:00:00Z", "submitByDate": "2025-01-13T00:00:00Z", "checkDate": "2025-01-15T00:00:00Z", "checkCount": 2, "id": "aeec80de-37b1-4020-b030-6ed10a88dade", "companyId": "17052482", "type": "payperiod", "_rid": "NmJkAKiCbEcwzgIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcwzgIAAAAAAA==/", "_etag": "\"a400540a-0000-0100-0000-68701e550000\"", "_attachments": "attachments/", "_ts": 1752178261}, {"payPeriodId": "1080039029488608", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-01-16T00:00:00Z", "endDate": "2025-01-31T00:00:00Z", "submitByDate": "2025-01-29T00:00:00Z", "checkDate": "2025-01-31T00:00:00Z", "checkCount": 2, "id": "3cccdc22-081f-439a-b5bd-9471ad1e02f9", "companyId": "17052482", "type": "payperiod", "_rid": "NmJkAKiCbEcxzgIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcxzgIAAAAAAA==/", "_etag": "\"a400590a-0000-0100-0000-68701e550000\"", "_attachments": "attachments/", "_ts": 1752178261}, {"payPeriodId": "1080039247548063", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-01-28T00:00:00Z", "endDate": "2025-02-12T00:00:00Z", "submitByDate": "2025-02-12T00:00:00Z", "checkDate": "2025-02-14T00:00:00Z", "checkCount": 2, "id": "6b7f6757-c5cc-4352-8474-42c975516296", "companyId": "17052482", "type": "payperiod", "_rid": "NmJkAKiCbEcyzgIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcyzgIAAAAAAA==/", "_etag": "\"a4005b0a-0000-0100-0000-68701e550000\"", "_attachments": "attachments/", "_ts": 1752178261}, {"payPeriodId": "1080039247548064", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-02-13T00:00:00Z", "endDate": "2025-02-27T00:00:00Z", "submitByDate": "2025-02-26T00:00:00Z", "checkDate": "2025-02-28T00:00:00Z", "checkCount": 2, "id": "edbe3109-e6fb-4d33-a451-b822a41c4f5f", "companyId": "17052482", "type": "payperiod", "_rid": "NmJkAKiCbEczzgIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEczzgIAAAAAAA==/", "_etag": "\"a400600a-0000-0100-0000-68701e550000\"", "_attachments": "attachments/", "_ts": 1752178261}, {"payPeriodId": "1080039465665020", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-02-28T00:00:00Z", "endDate": "2025-03-12T00:00:00Z", "submitByDate": "2025-03-12T00:00:00Z", "checkDate": "2025-03-14T00:00:00Z", "checkCount": 2, "id": "d6fd32d7-424f-4dc5-a7fd-3d3db483612c", "companyId": "17052482", "type": "payperiod", "_rid": "NmJkAKiCbEc0zgIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc0zgIAAAAAAA==/", "_etag": "\"a400630a-0000-0100-0000-68701e550000\"", "_attachments": "attachments/", "_ts": 1752178261}, {"payPeriodId": "1080039465665021", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-03-13T00:00:00Z", "endDate": "2025-03-27T00:00:00Z", "submitByDate": "2025-03-27T00:00:00Z", "checkDate": "2025-03-31T00:00:00Z", "checkCount": 2, "id": "d162762d-8a40-497e-8c6c-18f44c74d5a8", "companyId": "17052482", "type": "payperiod", "_rid": "NmJkAKiCbEc1zgIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc1zgIAAAAAAA==/", "_etag": "\"a400660a-0000-0100-0000-68701e550000\"", "_attachments": "attachments/", "_ts": 1752178261}, {"payPeriodId": "1080039711589174", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-03-31T00:00:00Z", "endDate": "2025-04-12T00:00:00Z", "submitByDate": "2025-04-11T00:00:00Z", "checkDate": "2025-04-15T00:00:00Z", "checkCount": 2, "id": "00e6cdfc-3103-43ac-addc-c3dc7c13adf8", "companyId": "17052482", "type": "payperiod", "_rid": "NmJkAKiCbEc2zgIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc2zgIAAAAAAA==/", "_etag": "\"a4006a0a-0000-0100-0000-68701e550000\"", "_attachments": "attachments/", "_ts": 1752178261}, {"payPeriodId": "1080039711589175", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-04-13T00:00:00Z", "endDate": "2025-04-29T00:00:00Z", "submitByDate": "2025-04-28T00:00:00Z", "checkDate": "2025-04-30T00:00:00Z", "checkCount": 2, "id": "0e98c76e-1bb1-404b-ad2c-f3a9735b8ec3", "companyId": "17052482", "type": "payperiod", "_rid": "NmJkAKiCbEc3zgIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc3zgIAAAAAAA==/", "_etag": "\"a4006e0a-0000-0100-0000-68701e550000\"", "_attachments": "attachments/", "_ts": 1752178261}, {"payPeriodId": "1080039887501532", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-04-28T00:00:00Z", "endDate": "2025-05-12T00:00:00Z", "submitByDate": "2025-05-13T00:00:00Z", "checkDate": "2025-05-15T00:00:00Z", "checkCount": 2, "id": "de6be0fb-068a-475e-8ca5-7cdbb5a23ada", "companyId": "17052482", "type": "payperiod", "_rid": "NmJkAKiCbEc4zgIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc4zgIAAAAAAA==/", "_etag": "\"a400700a-0000-0100-0000-68701e550000\"", "_attachments": "attachments/", "_ts": 1752178261}, {"payPeriodId": "1080039887501533", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-05-13T00:00:00Z", "endDate": "2025-05-27T00:00:00Z", "submitByDate": "2025-05-28T00:00:00Z", "checkDate": "2025-05-30T00:00:00Z", "checkCount": 2, "id": "b4a3adfa-d196-4aee-bdc6-11ee07ef1345", "companyId": "17052482", "type": "payperiod", "_rid": "NmJkAKiCbEc5zgIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc5zgIAAAAAAA==/", "_etag": "\"a400710a-0000-0100-0000-68701e550000\"", "_attachments": "attachments/", "_ts": 1752178261}, {"payPeriodId": "1080040128239927", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-05-28T00:00:00Z", "endDate": "2025-06-12T00:00:00Z", "submitByDate": "2025-06-11T00:00:00Z", "checkDate": "2025-06-13T00:00:00Z", "checkCount": 2, "id": "3e674bc5-c670-49c8-9a3e-d11dc96066ad", "companyId": "17052482", "type": "payperiod", "_rid": "NmJkAKiCbEc6zgIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc6zgIAAAAAAA==/", "_etag": "\"a400750a-0000-0100-0000-68701e550000\"", "_attachments": "attachments/", "_ts": 1752178261}, {"payPeriodId": "1080040128239928", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-06-13T00:00:00Z", "endDate": "2025-06-27T00:00:00Z", "submitByDate": "2025-06-26T00:00:00Z", "checkDate": "2025-06-30T00:00:00Z", "checkCount": 3, "id": "5dce230c-2022-4c26-abf0-0163d1685617", "companyId": "17052482", "type": "payperiod", "_rid": "NmJkAKiCbEc7zgIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc7zgIAAAAAAA==/", "_etag": "\"a400780a-0000-0100-0000-68701e550000\"", "_attachments": "attachments/", "_ts": 1752178261}, {"payPeriodId": "1080040269615463", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-06-28T00:00:00Z", "endDate": "2025-07-12T00:00:00Z", "submitByDate": "2025-07-11T00:00:00Z", "checkDate": "2025-07-15T00:00:00Z", "checkCount": 0, "id": "2c5f5c29-e101-42bf-a765-dbfc5c2778ae", "companyId": "17052482", "type": "payperiod", "_rid": "NmJkAKiCbEc8zgIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc8zgIAAAAAAA==/", "_etag": "\"a400790a-0000-0100-0000-68701e560000\"", "_attachments": "attachments/", "_ts": 1752178262}, {"payPeriodId": "1080040269615464", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-07-13T00:00:00Z", "endDate": "2025-07-27T00:00:00Z", "submitByDate": "2025-07-29T00:00:00Z", "checkDate": "2025-07-31T00:00:00Z", "checkCount": 0, "id": "82feeb8a-318f-4c77-b252-b06a481bc91c", "companyId": "17052482", "type": "payperiod", "_rid": "NmJkAKiCbEc9zgIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc9zgIAAAAAAA==/", "_etag": "\"a4007b0a-0000-0100-0000-68701e560000\"", "_attachments": "attachments/", "_ts": 1752178262}, {"payPeriodId": "1080040496077187", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-07-28T00:00:00Z", "endDate": "2025-08-12T00:00:00Z", "submitByDate": "2025-08-13T00:00:00Z", "checkDate": "2025-08-15T00:00:00Z", "checkCount": 0, "id": "60c2cb93-a54b-452e-906c-b9b9f77b6ead", "companyId": "17052482", "type": "payperiod", "_rid": "NmJkAKiCbEc+zgIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc+zgIAAAAAAA==/", "_etag": "\"a4007f0a-0000-0100-0000-68701e560000\"", "_attachments": "attachments/", "_ts": 1752178262}, {"payPeriodId": "1080040496077188", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-08-13T00:00:00Z", "endDate": "2025-08-27T00:00:00Z", "submitByDate": "2025-08-27T00:00:00Z", "checkDate": "2025-08-29T00:00:00Z", "checkCount": 0, "id": "36ef7822-ed3a-448c-87a5-e979f723118e", "companyId": "17052482", "type": "payperiod", "_rid": "NmJkAKiCbEc-zgIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc-zgIAAAAAAA==/", "_etag": "\"a400820a-0000-0100-0000-68701e560000\"", "_attachments": "attachments/", "_ts": 1752178262}, {"payPeriodId": "1080040708445676", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-08-28T00:00:00Z", "endDate": "2025-09-12T00:00:00Z", "submitByDate": "2025-09-11T00:00:00Z", "checkDate": "2025-09-15T00:00:00Z", "checkCount": 0, "id": "37c10ae1-ba26-4757-8323-530d63f2b372", "companyId": "17052482", "type": "payperiod", "_rid": "NmJkAKiCbEdAzgIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdAzgIAAAAAAA==/", "_etag": "\"a400880a-0000-0100-0000-68701e560000\"", "_attachments": "attachments/", "_ts": 1752178262}, {"payPeriodId": "1080040708445677", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-09-13T00:00:00Z", "endDate": "2025-09-27T00:00:00Z", "submitByDate": "2025-09-26T00:00:00Z", "checkDate": "2025-09-30T00:00:00Z", "checkCount": 0, "id": "48b510f5-dd37-41e7-baf6-e31925a66dc5", "companyId": "17052482", "type": "payperiod", "_rid": "NmJkAKiCbEdBzgIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdBzgIAAAAAAA==/", "_etag": "\"a4008a0a-0000-0100-0000-68701e560000\"", "_attachments": "attachments/", "_ts": 1752178262}, {"payPeriodId": "1080040905116561", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-09-28T00:00:00Z", "endDate": "2025-10-12T00:00:00Z", "submitByDate": "2025-10-13T00:00:00Z", "checkDate": "2025-10-15T00:00:00Z", "checkCount": 0, "id": "4dd21fa2-50c7-4cc5-b645-f2917812fd04", "companyId": "17052482", "type": "payperiod", "_rid": "NmJkAKiCbEdCzgIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdCzgIAAAAAAA==/", "_etag": "\"a4008d0a-0000-0100-0000-68701e560000\"", "_attachments": "attachments/", "_ts": 1752178262}, {"payPeriodId": "1080040905116562", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-10-13T00:00:00Z", "endDate": "2025-10-27T00:00:00Z", "submitByDate": "2025-10-29T00:00:00Z", "checkDate": "2025-10-31T00:00:00Z", "checkCount": 0, "id": "3dc97622-92cb-4526-9305-76327f9b7e8b", "companyId": "17052482", "type": "payperiod", "_rid": "NmJkAKiCbEdDzgIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdDzgIAAAAAAA==/", "_etag": "\"a4008e0a-0000-0100-0000-68701e560000\"", "_attachments": "attachments/", "_ts": 1752178262}], "links": [{"rel": "self", "href": "https://ca-payroll-ai-mock-sb-001-secure.nicebeach-8a7a52ab.eastus.azurecontainerapps.io/ca/companies/17052482/payperiods"}]}, "status_code": 200}