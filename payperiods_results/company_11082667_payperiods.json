{"success": true, "company_id": "11082667", "data": {"metadata": {"contentItemCount": 42}, "content": [{"payPeriodId": "1020050127683566", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2024-12-22T00:00:00Z", "endDate": "2025-01-04T00:00:00Z", "submitByDate": "2025-01-08T00:00:00Z", "checkDate": "2025-01-10T00:00:00Z", "checkCount": 3, "id": "3d75d6d5-513a-487a-a1a0-1797f80089e0", "companyId": "11082667", "type": "payperiod", "_rid": "NmJkAKiCbEdTvgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdTvgMAAAAAAA==/", "_etag": "\"a60007fb-0000-0100-0000-687031d30000\"", "_attachments": "attachments/", "_ts": 1752183251}, {"payPeriodId": "1020050253143415", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-01-05T00:00:00Z", "endDate": "2025-01-11T00:00:00Z", "submitByDate": "2025-01-22T00:00:00Z", "checkDate": "2025-01-24T00:00:00Z", "checkCount": 3, "id": "4c2538aa-7012-461c-8464-603eb83de4c5", "companyId": "11082667", "type": "payperiod", "_rid": "NmJkAKiCbEdUvgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdUvgMAAAAAAA==/", "_etag": "\"a60008fb-0000-0100-0000-687031d30000\"", "_attachments": "attachments/", "_ts": 1752183251}, {"payPeriodId": "1020050378109519", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-01-19T00:00:00Z", "endDate": "2025-02-02T00:00:00Z", "submitByDate": "2025-02-05T00:00:00Z", "checkDate": "2025-02-07T00:00:00Z", "checkCount": 3, "id": "918bea03-68d3-4111-8e3d-1ec789d33e81", "companyId": "11082667", "type": "payperiod", "_rid": "NmJkAKiCbEdVvgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdVvgMAAAAAAA==/", "_etag": "\"a60011fb-0000-0100-0000-687031d30000\"", "_attachments": "attachments/", "_ts": 1752183251}, {"payPeriodId": "1020050502351647", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-02-02T00:00:00Z", "endDate": "2025-02-15T00:00:00Z", "submitByDate": "2025-02-19T00:00:00Z", "checkDate": "2025-02-21T00:00:00Z", "checkCount": 3, "id": "568dbd7c-72b6-44bc-8786-780120f6f98b", "companyId": "11082667", "type": "payperiod", "_rid": "NmJkAKiCbEdWvgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdWvgMAAAAAAA==/", "_etag": "\"a60014fb-0000-0100-0000-687031d30000\"", "_attachments": "attachments/", "_ts": 1752183251}, {"payPeriodId": "1020050620514178", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-02-16T00:00:00Z", "endDate": "2025-03-01T00:00:00Z", "submitByDate": "2025-03-05T00:00:00Z", "checkDate": "2025-03-07T00:00:00Z", "checkCount": 3, "id": "66878e6d-e7df-4507-ad90-c5a74ff151f2", "companyId": "11082667", "type": "payperiod", "_rid": "NmJkAKiCbEdXvgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdXvgMAAAAAAA==/", "_etag": "\"a60015fb-0000-0100-0000-687031d30000\"", "_attachments": "attachments/", "_ts": 1752183251}, {"payPeriodId": "1020050743497755", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-03-02T00:00:00Z", "endDate": "2025-03-15T00:00:00Z", "submitByDate": "2025-03-19T00:00:00Z", "checkDate": "2025-03-21T00:00:00Z", "checkCount": 3, "id": "eaf88f62-353d-4455-ae98-a2262e3c1fd7", "companyId": "11082667", "type": "payperiod", "_rid": "NmJkAKiCbEdYvgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdYvgMAAAAAAA==/", "_etag": "\"a6001efb-0000-0100-0000-687031d30000\"", "_attachments": "attachments/", "_ts": 1752183251}, {"payPeriodId": "1020050877205486", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-03-16T00:00:00Z", "endDate": "2025-03-29T00:00:00Z", "submitByDate": "2025-04-02T00:00:00Z", "checkDate": "2025-04-04T00:00:00Z", "checkCount": 0, "id": "b012c17d-1be4-4978-b88e-36c57f982d42", "companyId": "11082667", "type": "payperiod", "_rid": "NmJkAKiCbEdZvgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdZvgMAAAAAAA==/", "_etag": "\"a60020fb-0000-0100-0000-687031d30000\"", "_attachments": "attachments/", "_ts": 1752183251}, {"payPeriodId": "1020051000154910", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-03-30T00:00:00Z", "endDate": "2025-04-12T00:00:00Z", "submitByDate": "2025-04-16T00:00:00Z", "checkDate": "2025-04-18T00:00:00Z", "checkCount": 0, "id": "c113b408-195d-4b7f-a580-5bc6760d7edc", "companyId": "11082667", "type": "payperiod", "_rid": "NmJkAKiCbEdavgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdavgMAAAAAAA==/", "_etag": "\"a60024fb-0000-0100-0000-687031d30000\"", "_attachments": "attachments/", "_ts": 1752183251}, {"payPeriodId": "1020051118378638", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-04-13T00:00:00Z", "endDate": "2025-04-26T00:00:00Z", "submitByDate": "2025-04-30T00:00:00Z", "checkDate": "2025-05-02T00:00:00Z", "checkCount": 0, "id": "2977bde6-4c46-4991-93e5-bec38609275f", "companyId": "11082667", "type": "payperiod", "_rid": "NmJkAKiCbEdbvgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdbvgMAAAAAAA==/", "_etag": "\"a60027fb-0000-0100-0000-687031d30000\"", "_attachments": "attachments/", "_ts": 1752183251}, {"payPeriodId": "1020051232346193", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-04-27T00:00:00Z", "endDate": "2025-05-10T00:00:00Z", "submitByDate": "2025-05-14T00:00:00Z", "checkDate": "2025-05-16T00:00:00Z", "checkCount": 0, "id": "4b3a70d2-8687-4472-999b-25ae774063c9", "companyId": "11082667", "type": "payperiod", "_rid": "NmJkAKiCbEdcvgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdcvgMAAAAAAA==/", "_etag": "\"a6002ffb-0000-0100-0000-687031d40000\"", "_attachments": "attachments/", "_ts": 1752183252}, {"payPeriodId": "1020051345530399", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-05-11T00:00:00Z", "endDate": "2025-05-24T00:00:00Z", "submitByDate": "2025-05-28T00:00:00Z", "checkDate": "2025-05-30T00:00:00Z", "checkCount": 0, "id": "7fca7043-3f5d-4682-ba96-cf1b934cca0f", "companyId": "11082667", "type": "payperiod", "_rid": "NmJkAKiCbEddvgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEddvgMAAAAAAA==/", "_etag": "\"a60030fb-0000-0100-0000-687031d40000\"", "_attachments": "attachments/", "_ts": 1752183252}, {"payPeriodId": "1020051460065325", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-05-25T00:00:00Z", "endDate": "2025-06-07T00:00:00Z", "submitByDate": "2025-06-11T00:00:00Z", "checkDate": "2025-06-13T00:00:00Z", "checkCount": 0, "id": "8c67bb3f-8cfb-4ada-a542-acb46779f468", "companyId": "11082667", "type": "payperiod", "_rid": "NmJkAKiCbEdevgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdevgMAAAAAAA==/", "_etag": "\"a60033fb-0000-0100-0000-687031d40000\"", "_attachments": "attachments/", "_ts": 1752183252}, {"payPeriodId": "1020051571582406", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-06-08T00:00:00Z", "endDate": "2025-06-21T00:00:00Z", "submitByDate": "2025-06-25T00:00:00Z", "checkDate": "2025-06-27T00:00:00Z", "checkCount": 0, "id": "1175fe05-7897-446a-99ee-ffcbde937aba", "companyId": "11082667", "type": "payperiod", "_rid": "NmJkAKiCbEdfvgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdfvgMAAAAAAA==/", "_etag": "\"a60036fb-0000-0100-0000-687031d40000\"", "_attachments": "attachments/", "_ts": 1752183252}, {"payPeriodId": "1020051688119054", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-06-22T00:00:00Z", "endDate": "2025-07-05T00:00:00Z", "submitByDate": "2025-07-09T00:00:00Z", "checkDate": "2025-07-11T00:00:00Z", "checkCount": 0, "id": "6ae8f4c4-15f9-4d93-a320-58bcf03a321b", "companyId": "11082667", "type": "payperiod", "_rid": "NmJkAKiCbEdgvgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdgvgMAAAAAAA==/", "_etag": "\"a60037fb-0000-0100-0000-687031d40000\"", "_attachments": "attachments/", "_ts": 1752183252}, {"payPeriodId": "1020051816070164", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-07-06T00:00:00Z", "endDate": "2025-07-19T00:00:00Z", "submitByDate": "2025-07-23T00:00:00Z", "checkDate": "2025-07-25T00:00:00Z", "checkCount": 0, "id": "b8b280f2-3c9d-4a16-8bc5-a32a4f2cb98a", "companyId": "11082667", "type": "payperiod", "_rid": "NmJkAKiCbEdhvgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdhvgMAAAAAAA==/", "_etag": "\"a60038fb-0000-0100-0000-687031d40000\"", "_attachments": "attachments/", "_ts": 1752183252}, {"payPeriodId": "1020051937697780", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-07-20T00:00:00Z", "endDate": "2025-08-02T00:00:00Z", "submitByDate": "2025-08-06T00:00:00Z", "checkDate": "2025-08-08T00:00:00Z", "checkCount": 0, "id": "4aa31894-6afe-402c-a869-7b2bf1961441", "companyId": "11082667", "type": "payperiod", "_rid": "NmJkAKiCbEdivgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdivgMAAAAAAA==/", "_etag": "\"a6003bfb-0000-0100-0000-687031d40000\"", "_attachments": "attachments/", "_ts": 1752183252}, {"payPeriodId": "1020052060406421", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-08-03T00:00:00Z", "endDate": "2025-08-16T00:00:00Z", "submitByDate": "2025-08-20T00:00:00Z", "checkDate": "2025-08-22T00:00:00Z", "checkCount": 0, "id": "05990ef5-5098-4622-9f71-b34b4b5427f1", "companyId": "11082667", "type": "payperiod", "_rid": "NmJkAKiCbEdjvgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdjvgMAAAAAAA==/", "_etag": "\"a60042fb-0000-0100-0000-687031d40000\"", "_attachments": "attachments/", "_ts": 1752183252}, {"payPeriodId": "1020052181478576", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-08-17T00:00:00Z", "endDate": "2025-08-30T00:00:00Z", "submitByDate": "2025-09-03T00:00:00Z", "checkDate": "2025-09-05T00:00:00Z", "checkCount": 0, "id": "0d07acac-e8af-46fc-beef-9664abb80e70", "companyId": "11082667", "type": "payperiod", "_rid": "NmJkAKiCbEdkvgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdkvgMAAAAAAA==/", "_etag": "\"a60049fb-0000-0100-0000-687031d40000\"", "_attachments": "attachments/", "_ts": 1752183252}, {"payPeriodId": "1020052297992202", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-08-31T00:00:00Z", "endDate": "2025-09-13T00:00:00Z", "submitByDate": "2025-09-17T00:00:00Z", "checkDate": "2025-09-19T00:00:00Z", "checkCount": 0, "id": "98255dfa-7520-4583-b50c-b29108a5967e", "companyId": "11082667", "type": "payperiod", "_rid": "NmJkAKiCbEdlvgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdlvgMAAAAAAA==/", "_etag": "\"a60050fb-0000-0100-0000-687031d40000\"", "_attachments": "attachments/", "_ts": 1752183252}, {"payPeriodId": "1020052415631102", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-09-14T00:00:00Z", "endDate": "2025-09-27T00:00:00Z", "submitByDate": "2025-10-01T00:00:00Z", "checkDate": "2025-10-03T00:00:00Z", "checkCount": 0, "id": "2d1f0942-16c8-48f2-ae5f-ed33f0528ffe", "companyId": "11082667", "type": "payperiod", "_rid": "NmJkAKiCbEdmvgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdmvgMAAAAAAA==/", "_etag": "\"a60052fb-0000-0100-0000-687031d40000\"", "_attachments": "attachments/", "_ts": 1752183252}, {"payPeriodId": "1020052539470784", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-09-28T00:00:00Z", "endDate": "2025-10-11T00:00:00Z", "submitByDate": "2025-10-15T00:00:00Z", "checkDate": "2025-10-17T00:00:00Z", "checkCount": 0, "id": "300d611d-9f87-42b0-9f79-87ddca77039f", "companyId": "11082667", "type": "payperiod", "_rid": "NmJkAKiCbEdnvgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdnvgMAAAAAAA==/", "_etag": "\"a60056fb-0000-0100-0000-687031d40000\"", "_attachments": "attachments/", "_ts": 1752183252}, {"payPeriodId": "1020050127683566", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2024-12-22T00:00:00Z", "endDate": "2025-01-04T00:00:00Z", "submitByDate": "2025-01-08T00:00:00Z", "checkDate": "2025-01-10T00:00:00Z", "checkCount": 3, "id": "cc5647b1-843e-4e78-a828-cb9a4cfe9bb5", "companyId": "11082667", "type": "payperiod", "_rid": "NmJkAKiCbEdxvgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdxvgMAAAAAAA==/", "_etag": "\"a6007cfb-0000-0100-0000-687031d50000\"", "_attachments": "attachments/", "_ts": 1752183253}, {"payPeriodId": "1020050253143415", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-01-05T00:00:00Z", "endDate": "2025-01-11T00:00:00Z", "submitByDate": "2025-01-22T00:00:00Z", "checkDate": "2025-01-24T00:00:00Z", "checkCount": 3, "id": "ba511561-cbe5-4aeb-aa7f-66942d4461f0", "companyId": "11082667", "type": "payperiod", "_rid": "NmJkAKiCbEdyvgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdyvgMAAAAAAA==/", "_etag": "\"a6007dfb-0000-0100-0000-687031d50000\"", "_attachments": "attachments/", "_ts": 1752183253}, {"payPeriodId": "1020050378109519", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-01-19T00:00:00Z", "endDate": "2025-02-02T00:00:00Z", "submitByDate": "2025-02-05T00:00:00Z", "checkDate": "2025-02-07T00:00:00Z", "checkCount": 3, "id": "4debebb5-09ad-4238-bd18-6c7399e340fd", "companyId": "11082667", "type": "payperiod", "_rid": "NmJkAKiCbEdzvgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdzvgMAAAAAAA==/", "_etag": "\"a6007efb-0000-0100-0000-687031d50000\"", "_attachments": "attachments/", "_ts": 1752183253}, {"payPeriodId": "1020050502351647", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-02-02T00:00:00Z", "endDate": "2025-02-15T00:00:00Z", "submitByDate": "2025-02-19T00:00:00Z", "checkDate": "2025-02-21T00:00:00Z", "checkCount": 3, "id": "1000c3d4-b967-4207-ad65-7a150e5fca70", "companyId": "11082667", "type": "payperiod", "_rid": "NmJkAKiCbEd0vgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEd0vgMAAAAAAA==/", "_etag": "\"a6007ffb-0000-0100-0000-687031d50000\"", "_attachments": "attachments/", "_ts": 1752183253}, {"payPeriodId": "1020050620514178", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-02-16T00:00:00Z", "endDate": "2025-03-01T00:00:00Z", "submitByDate": "2025-03-05T00:00:00Z", "checkDate": "2025-03-07T00:00:00Z", "checkCount": 3, "id": "63bd88c4-c66b-4188-881f-5555583f48a9", "companyId": "11082667", "type": "payperiod", "_rid": "NmJkAKiCbEd1vgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEd1vgMAAAAAAA==/", "_etag": "\"a60081fb-0000-0100-0000-687031d60000\"", "_attachments": "attachments/", "_ts": 1752183254}, {"payPeriodId": "1020050743497755", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-03-02T00:00:00Z", "endDate": "2025-03-15T00:00:00Z", "submitByDate": "2025-03-19T00:00:00Z", "checkDate": "2025-03-21T00:00:00Z", "checkCount": 3, "id": "0709ee1e-7f41-4192-bb1f-4a5cdad3f996", "companyId": "11082667", "type": "payperiod", "_rid": "NmJkAKiCbEd2vgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEd2vgMAAAAAAA==/", "_etag": "\"a60082fb-0000-0100-0000-687031d60000\"", "_attachments": "attachments/", "_ts": 1752183254}, {"payPeriodId": "1020050877205486", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-03-16T00:00:00Z", "endDate": "2025-03-29T00:00:00Z", "submitByDate": "2025-04-02T00:00:00Z", "checkDate": "2025-04-04T00:00:00Z", "checkCount": 3, "id": "b1efd8a4-43d4-487b-a995-f6c013246bb3", "companyId": "11082667", "type": "payperiod", "_rid": "NmJkAKiCbEd3vgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEd3vgMAAAAAAA==/", "_etag": "\"a60084fb-0000-0100-0000-687031d60000\"", "_attachments": "attachments/", "_ts": 1752183254}, {"payPeriodId": "1020051000154910", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-03-30T00:00:00Z", "endDate": "2025-04-12T00:00:00Z", "submitByDate": "2025-04-16T00:00:00Z", "checkDate": "2025-04-18T00:00:00Z", "checkCount": 3, "id": "8cbd323e-8401-4a6f-9648-60eec52deca9", "companyId": "11082667", "type": "payperiod", "_rid": "NmJkAKiCbEd4vgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEd4vgMAAAAAAA==/", "_etag": "\"a60089fb-0000-0100-0000-687031d60000\"", "_attachments": "attachments/", "_ts": 1752183254}, {"payPeriodId": "1020051118378638", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-04-13T00:00:00Z", "endDate": "2025-04-26T00:00:00Z", "submitByDate": "2025-04-30T00:00:00Z", "checkDate": "2025-05-02T00:00:00Z", "checkCount": 3, "id": "3d6b7269-547f-4de0-ba18-41ce36f61b24", "companyId": "11082667", "type": "payperiod", "_rid": "NmJkAKiCbEd5vgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEd5vgMAAAAAAA==/", "_etag": "\"a6008bfb-0000-0100-0000-687031d60000\"", "_attachments": "attachments/", "_ts": 1752183254}, {"payPeriodId": "1020051232346193", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-04-27T00:00:00Z", "endDate": "2025-05-10T00:00:00Z", "submitByDate": "2025-05-14T00:00:00Z", "checkDate": "2025-05-16T00:00:00Z", "checkCount": 3, "id": "9313faf1-129d-48dc-87d8-ca7c980e3084", "companyId": "11082667", "type": "payperiod", "_rid": "NmJkAKiCbEd6vgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEd6vgMAAAAAAA==/", "_etag": "\"a6008efb-0000-0100-0000-687031d60000\"", "_attachments": "attachments/", "_ts": 1752183254}, {"payPeriodId": "1020051345530399", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-05-11T00:00:00Z", "endDate": "2025-05-24T00:00:00Z", "submitByDate": "2025-05-28T00:00:00Z", "checkDate": "2025-05-30T00:00:00Z", "checkCount": 3, "id": "4310dbac-f382-406e-8297-9f170373525b", "companyId": "11082667", "type": "payperiod", "_rid": "NmJkAKiCbEd7vgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEd7vgMAAAAAAA==/", "_etag": "\"a60097fb-0000-0100-0000-687031d60000\"", "_attachments": "attachments/", "_ts": 1752183254}, {"payPeriodId": "1020051460065325", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-05-25T00:00:00Z", "endDate": "2025-06-07T00:00:00Z", "submitByDate": "2025-06-11T00:00:00Z", "checkDate": "2025-06-13T00:00:00Z", "checkCount": 3, "id": "db39a32f-f851-421c-bd17-33a813de2e62", "companyId": "11082667", "type": "payperiod", "_rid": "NmJkAKiCbEd8vgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEd8vgMAAAAAAA==/", "_etag": "\"a60099fb-0000-0100-0000-687031d60000\"", "_attachments": "attachments/", "_ts": 1752183254}, {"payPeriodId": "1020051571582406", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-06-08T00:00:00Z", "endDate": "2025-06-21T00:00:00Z", "submitByDate": "2025-06-25T00:00:00Z", "checkDate": "2025-06-27T00:00:00Z", "checkCount": 3, "id": "19abafaf-69d2-4e14-83ea-e65aac25c305", "companyId": "11082667", "type": "payperiod", "_rid": "NmJkAKiCbEd9vgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEd9vgMAAAAAAA==/", "_etag": "\"a6009cfb-0000-0100-0000-687031d60000\"", "_attachments": "attachments/", "_ts": 1752183254}, {"payPeriodId": "1020051688119054", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-06-22T00:00:00Z", "endDate": "2025-07-05T00:00:00Z", "submitByDate": "2025-07-09T00:00:00Z", "checkDate": "2025-07-11T00:00:00Z", "checkCount": 0, "id": "7e7e67f7-c858-4fef-a93f-2cc4eb7947f2", "companyId": "11082667", "type": "payperiod", "_rid": "NmJkAKiCbEd+vgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEd+vgMAAAAAAA==/", "_etag": "\"a6009efb-0000-0100-0000-687031d60000\"", "_attachments": "attachments/", "_ts": 1752183254}, {"payPeriodId": "1020051816070164", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-07-06T00:00:00Z", "endDate": "2025-07-19T00:00:00Z", "submitByDate": "2025-07-23T00:00:00Z", "checkDate": "2025-07-25T00:00:00Z", "checkCount": 0, "id": "891c6450-886d-4482-9f7e-f11fb570d2f7", "companyId": "11082667", "type": "payperiod", "_rid": "NmJkAKiCbEd-vgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEd-vgMAAAAAAA==/", "_etag": "\"a6009ffb-0000-0100-0000-687031d60000\"", "_attachments": "attachments/", "_ts": 1752183254}, {"payPeriodId": "1020051937697780", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-07-20T00:00:00Z", "endDate": "2025-08-02T00:00:00Z", "submitByDate": "2025-08-06T00:00:00Z", "checkDate": "2025-08-08T00:00:00Z", "checkCount": 0, "id": "edf686d9-aebc-4197-b886-60a219bfaf21", "companyId": "11082667", "type": "payperiod", "_rid": "NmJkAKiCbEeAvgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeAvgMAAAAAAA==/", "_etag": "\"a600a4fb-0000-0100-0000-687031d60000\"", "_attachments": "attachments/", "_ts": 1752183254}, {"payPeriodId": "1020052060406421", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-08-03T00:00:00Z", "endDate": "2025-08-16T00:00:00Z", "submitByDate": "2025-08-20T00:00:00Z", "checkDate": "2025-08-22T00:00:00Z", "checkCount": 0, "id": "3aea75aa-a00b-46c2-8979-9df5000e146e", "companyId": "11082667", "type": "payperiod", "_rid": "NmJkAKiCbEeBvgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeBvgMAAAAAAA==/", "_etag": "\"a600a7fb-0000-0100-0000-687031d60000\"", "_attachments": "attachments/", "_ts": 1752183254}, {"payPeriodId": "1020052181478576", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-08-17T00:00:00Z", "endDate": "2025-08-30T00:00:00Z", "submitByDate": "2025-09-03T00:00:00Z", "checkDate": "2025-09-05T00:00:00Z", "checkCount": 0, "id": "edd9e6c4-c734-434a-adda-b4ca8b0732ee", "companyId": "11082667", "type": "payperiod", "_rid": "NmJkAKiCbEeCvgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeCvgMAAAAAAA==/", "_etag": "\"a600a9fb-0000-0100-0000-687031d70000\"", "_attachments": "attachments/", "_ts": 1752183255}, {"payPeriodId": "1020052297992202", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-08-31T00:00:00Z", "endDate": "2025-09-13T00:00:00Z", "submitByDate": "2025-09-17T00:00:00Z", "checkDate": "2025-09-19T00:00:00Z", "checkCount": 0, "id": "ea692aea-57d4-4346-9ce0-98de6fbbd4a5", "companyId": "11082667", "type": "payperiod", "_rid": "NmJkAKiCbEeDvgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeDvgMAAAAAAA==/", "_etag": "\"a600aefb-0000-0100-0000-687031d70000\"", "_attachments": "attachments/", "_ts": 1752183255}, {"payPeriodId": "1020052415631102", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-09-14T00:00:00Z", "endDate": "2025-09-27T00:00:00Z", "submitByDate": "2025-10-01T00:00:00Z", "checkDate": "2025-10-03T00:00:00Z", "checkCount": 0, "id": "be832add-028e-405b-806d-082efea3eb18", "companyId": "11082667", "type": "payperiod", "_rid": "NmJkAKiCbEeEvgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeEvgMAAAAAAA==/", "_etag": "\"a600b3fb-0000-0100-0000-687031d70000\"", "_attachments": "attachments/", "_ts": 1752183255}, {"payPeriodId": "1020052539470784", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-09-28T00:00:00Z", "endDate": "2025-10-11T00:00:00Z", "submitByDate": "2025-10-15T00:00:00Z", "checkDate": "2025-10-17T00:00:00Z", "checkCount": 0, "id": "b99cd3c6-532b-40f4-a754-386de09cef5e", "companyId": "11082667", "type": "payperiod", "_rid": "NmJkAKiCbEeFvgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeFvgMAAAAAAA==/", "_etag": "\"a600b7fb-0000-0100-0000-687031d70000\"", "_attachments": "attachments/", "_ts": 1752183255}], "links": [{"rel": "self", "href": "https://ca-payroll-ai-mock-sb-001-secure.nicebeach-8a7a52ab.eastus.azurecontainerapps.io/ca/companies/11082667/payperiods"}]}, "status_code": 200}