{"success": true, "company_id": "14102976", "data": {"metadata": {"contentItemCount": 20}, "content": [{"payPeriodId": "1050103134924400", "intervalCode": "MONTHLY", "status": "COMPLETED", "description": "Monthly Payroll (1)", "startDate": "2025-01-01T00:00:00Z", "endDate": "2025-01-31T00:00:00Z", "submitByDate": "2025-01-30T00:00:00Z", "checkDate": "2025-01-31T00:00:00Z", "checkCount": 2, "id": "1e4bd6fe-21c4-4c0e-aca6-8ea3081f1e40", "companyId": "14102976", "type": "payperiod", "_rid": "NmJkAKiCbEdvxgIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdvxgIAAAAAAA==/", "_etag": "\"a30022f2-0000-0100-0000-68701db50000\"", "_attachments": "attachments/", "_ts": 1752178101}, {"payPeriodId": "1050104092934986", "intervalCode": "MONTHLY", "status": "COMPLETED", "description": "Monthly Payroll (1)", "startDate": "2025-02-01T00:00:00Z", "endDate": "2025-02-28T00:00:00Z", "submitByDate": "2025-02-27T00:00:00Z", "checkDate": "2025-02-28T00:00:00Z", "checkCount": 2, "id": "e9c85889-fc67-4b4a-9589-df6b5b6ef7aa", "companyId": "14102976", "type": "payperiod", "_rid": "NmJkAKiCbEdwxgIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdwxgIAAAAAAA==/", "_etag": "\"a30024f2-0000-0100-0000-68701db50000\"", "_attachments": "attachments/", "_ts": 1752178101}, {"payPeriodId": "1050105278499760", "intervalCode": "MONTHLY", "status": "COMPLETED", "description": "Monthly Payroll (1)", "startDate": "2025-03-01T00:00:00Z", "endDate": "2025-03-31T00:00:00Z", "submitByDate": "2025-03-28T00:00:00Z", "checkDate": "2025-03-31T00:00:00Z", "checkCount": 2, "id": "52a71b53-ff5a-4484-8674-552e055e7f58", "companyId": "14102976", "type": "payperiod", "_rid": "NmJkAKiCbEdxxgIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdxxgIAAAAAAA==/", "_etag": "\"a30026f2-0000-0100-0000-68701db50000\"", "_attachments": "attachments/", "_ts": 1752178101}, {"payPeriodId": "1050106296577639", "intervalCode": "MONTHLY", "status": "INITIAL", "description": "Monthly Payroll (1)", "startDate": "2025-04-01T00:00:00Z", "endDate": "2025-04-30T00:00:00Z", "submitByDate": "2025-04-29T00:00:00Z", "checkDate": "2025-04-30T00:00:00Z", "checkCount": 0, "id": "65d6feeb-0703-4ab5-8b57-43ee7e7e2de4", "companyId": "14102976", "type": "payperiod", "_rid": "NmJkAKiCbEdyxgIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdyxgIAAAAAAA==/", "_etag": "\"a3002af2-0000-0100-0000-68701db50000\"", "_attachments": "attachments/", "_ts": 1752178101}, {"payPeriodId": "1050107264296104", "intervalCode": "MONTHLY", "status": "INITIAL", "description": "Monthly Payroll (1)", "startDate": "2025-05-01T00:00:00Z", "endDate": "2025-05-31T00:00:00Z", "submitByDate": "2025-05-29T00:00:00Z", "checkDate": "2025-05-30T00:00:00Z", "checkCount": 0, "id": "4ccf8aa6-b574-4ecd-a7e2-25801a3d854d", "companyId": "14102976", "type": "payperiod", "_rid": "NmJkAKiCbEdzxgIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdzxgIAAAAAAA==/", "_etag": "\"a3002cf2-0000-0100-0000-68701db50000\"", "_attachments": "attachments/", "_ts": 1752178101}, {"payPeriodId": "1050108448847342", "intervalCode": "MONTHLY", "status": "INITIAL", "description": "Monthly Payroll (1)", "startDate": "2025-06-01T00:00:00Z", "endDate": "2025-06-30T00:00:00Z", "submitByDate": "2025-06-27T00:00:00Z", "checkDate": "2025-06-30T00:00:00Z", "checkCount": 0, "id": "128ae985-cabc-4de6-9a38-97e84d420018", "companyId": "14102976", "type": "payperiod", "_rid": "NmJkAKiCbEd0xgIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEd0xgIAAAAAAA==/", "_etag": "\"a3002ef2-0000-0100-0000-68701db50000\"", "_attachments": "attachments/", "_ts": 1752178101}, {"payPeriodId": "1050109420356157", "intervalCode": "MONTHLY", "status": "INITIAL", "description": "Monthly Payroll (1)", "startDate": "2025-07-01T00:00:00Z", "endDate": "2025-07-31T00:00:00Z", "submitByDate": "2025-07-30T00:00:00Z", "checkDate": "2025-07-31T00:00:00Z", "checkCount": 0, "id": "0c220931-0da6-49e3-b631-a6ac7465a0bc", "companyId": "14102976", "type": "payperiod", "_rid": "NmJkAKiCbEd1xgIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEd1xgIAAAAAAA==/", "_etag": "\"a3002ff2-0000-0100-0000-68701db50000\"", "_attachments": "attachments/", "_ts": 1752178101}, {"payPeriodId": "1050110397890107", "intervalCode": "MONTHLY", "status": "INITIAL", "description": "Monthly Payroll (1)", "startDate": "2025-08-01T00:00:00Z", "endDate": "2025-08-31T00:00:00Z", "submitByDate": "2025-08-28T00:00:00Z", "checkDate": "2025-08-29T00:00:00Z", "checkCount": 0, "id": "e2914b6a-c5d9-4138-8ce5-7bcf15f55b4b", "companyId": "14102976", "type": "payperiod", "_rid": "NmJkAKiCbEd2xgIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEd2xgIAAAAAAA==/", "_etag": "\"a30034f2-0000-0100-0000-68701db50000\"", "_attachments": "attachments/", "_ts": 1752178101}, {"payPeriodId": "1050111632276673", "intervalCode": "MONTHLY", "status": "INITIAL", "description": "Monthly Payroll (1)", "startDate": "2025-09-01T00:00:00Z", "endDate": "2025-09-30T00:00:00Z", "submitByDate": "2025-09-29T00:00:00Z", "checkDate": "2025-09-30T00:00:00Z", "checkCount": 0, "id": "d148ea80-5e08-4665-bd2d-72a247b3177c", "companyId": "14102976", "type": "payperiod", "_rid": "NmJkAKiCbEd3xgIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEd3xgIAAAAAAA==/", "_etag": "\"a30036f2-0000-0100-0000-68701db50000\"", "_attachments": "attachments/", "_ts": 1752178101}, {"payPeriodId": "1050112637358618", "intervalCode": "MONTHLY", "status": "INITIAL", "description": "Monthly Payroll (1)", "startDate": "2025-10-01T00:00:00Z", "endDate": "2025-10-31T00:00:00Z", "submitByDate": "2025-10-30T00:00:00Z", "checkDate": "2025-10-31T00:00:00Z", "checkCount": 0, "id": "1931d518-bdd6-48d1-bccc-c9c1a2856b10", "companyId": "14102976", "type": "payperiod", "_rid": "NmJkAKiCbEd4xgIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEd4xgIAAAAAAA==/", "_etag": "\"a3003bf2-0000-0100-0000-68701db50000\"", "_attachments": "attachments/", "_ts": 1752178101}, {"payPeriodId": "1050103134924400", "intervalCode": "MONTHLY", "status": "COMPLETED", "description": "Monthly Payroll (1)", "startDate": "2025-01-01T00:00:00Z", "endDate": "2025-01-31T00:00:00Z", "submitByDate": "2025-01-30T00:00:00Z", "checkDate": "2025-01-31T00:00:00Z", "checkCount": 2, "id": "817c27ed-4b19-409a-a773-72a368b42bf1", "companyId": "14102976", "type": "payperiod", "_rid": "NmJkAKiCbEd8xgIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEd8xgIAAAAAAA==/", "_etag": "\"a30046f2-0000-0100-0000-68701db60000\"", "_attachments": "attachments/", "_ts": 1752178102}, {"payPeriodId": "1050104092934986", "intervalCode": "MONTHLY", "status": "COMPLETED", "description": "Monthly Payroll (1)", "startDate": "2025-02-01T00:00:00Z", "endDate": "2025-02-28T00:00:00Z", "submitByDate": "2025-02-27T00:00:00Z", "checkDate": "2025-02-28T00:00:00Z", "checkCount": 2, "id": "5786faca-2748-48bb-8176-071d7f01c4c5", "companyId": "14102976", "type": "payperiod", "_rid": "NmJkAKiCbEd9xgIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEd9xgIAAAAAAA==/", "_etag": "\"a3004bf2-0000-0100-0000-68701db60000\"", "_attachments": "attachments/", "_ts": 1752178102}, {"payPeriodId": "1050105278499760", "intervalCode": "MONTHLY", "status": "COMPLETED", "description": "Monthly Payroll (1)", "startDate": "2025-03-01T00:00:00Z", "endDate": "2025-03-31T00:00:00Z", "submitByDate": "2025-03-28T00:00:00Z", "checkDate": "2025-03-31T00:00:00Z", "checkCount": 2, "id": "d3b28cd4-ebe7-45c7-898e-fa6d99365306", "companyId": "14102976", "type": "payperiod", "_rid": "NmJkAKiCbEd+xgIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEd+xgIAAAAAAA==/", "_etag": "\"a3004df2-0000-0100-0000-68701db60000\"", "_attachments": "attachments/", "_ts": 1752178102}, {"payPeriodId": "1050106296577639", "intervalCode": "MONTHLY", "status": "COMPLETED", "description": "Monthly Payroll (1)", "startDate": "2025-04-01T00:00:00Z", "endDate": "2025-04-30T00:00:00Z", "submitByDate": "2025-04-29T00:00:00Z", "checkDate": "2025-04-30T00:00:00Z", "checkCount": 2, "id": "ed798e86-0e1f-43e3-ac8f-ae1c47262298", "companyId": "14102976", "type": "payperiod", "_rid": "NmJkAKiCbEd-xgIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEd-xgIAAAAAAA==/", "_etag": "\"a3004ef2-0000-0100-0000-68701db60000\"", "_attachments": "attachments/", "_ts": 1752178102}, {"payPeriodId": "1050107264296104", "intervalCode": "MONTHLY", "status": "COMPLETED", "description": "Monthly Payroll (1)", "startDate": "2025-05-01T00:00:00Z", "endDate": "2025-05-31T00:00:00Z", "submitByDate": "2025-05-29T00:00:00Z", "checkDate": "2025-05-30T00:00:00Z", "checkCount": 2, "id": "2a1b18e5-22e8-49bb-a20b-915031362f75", "companyId": "14102976", "type": "payperiod", "_rid": "NmJkAKiCbEeAxgIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeAxgIAAAAAAA==/", "_etag": "\"a30050f2-0000-0100-0000-68701db60000\"", "_attachments": "attachments/", "_ts": 1752178102}, {"payPeriodId": "1050108448847342", "intervalCode": "MONTHLY", "status": "COMPLETED", "description": "Monthly Payroll (1)", "startDate": "2025-06-01T00:00:00Z", "endDate": "2025-06-30T00:00:00Z", "submitByDate": "2025-06-27T00:00:00Z", "checkDate": "2025-06-30T00:00:00Z", "checkCount": 2, "id": "5d009ef8-82df-47ed-8b61-f691f5ef7ef9", "companyId": "14102976", "type": "payperiod", "_rid": "NmJkAKiCbEeBxgIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeBxgIAAAAAAA==/", "_etag": "\"a30051f2-0000-0100-0000-68701db60000\"", "_attachments": "attachments/", "_ts": 1752178102}, {"payPeriodId": "1050109420356157", "intervalCode": "MONTHLY", "status": "INITIAL", "description": "Monthly Payroll (1)", "startDate": "2025-07-01T00:00:00Z", "endDate": "2025-07-31T00:00:00Z", "submitByDate": "2025-07-30T00:00:00Z", "checkDate": "2025-07-31T00:00:00Z", "checkCount": 0, "id": "3eb98869-b66f-4114-aa20-d6d71073b049", "companyId": "14102976", "type": "payperiod", "_rid": "NmJkAKiCbEeCxgIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeCxgIAAAAAAA==/", "_etag": "\"a30054f2-0000-0100-0000-68701db60000\"", "_attachments": "attachments/", "_ts": 1752178102}, {"payPeriodId": "1050110397890107", "intervalCode": "MONTHLY", "status": "INITIAL", "description": "Monthly Payroll (1)", "startDate": "2025-08-01T00:00:00Z", "endDate": "2025-08-31T00:00:00Z", "submitByDate": "2025-08-28T00:00:00Z", "checkDate": "2025-08-29T00:00:00Z", "checkCount": 0, "id": "105f5ea7-8952-4d84-934d-7b85d40e1d19", "companyId": "14102976", "type": "payperiod", "_rid": "NmJkAKiCbEeDxgIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeDxgIAAAAAAA==/", "_etag": "\"a30056f2-0000-0100-0000-68701db60000\"", "_attachments": "attachments/", "_ts": 1752178102}, {"payPeriodId": "1050111632276673", "intervalCode": "MONTHLY", "status": "INITIAL", "description": "Monthly Payroll (1)", "startDate": "2025-09-01T00:00:00Z", "endDate": "2025-09-30T00:00:00Z", "submitByDate": "2025-09-29T00:00:00Z", "checkDate": "2025-09-30T00:00:00Z", "checkCount": 0, "id": "879e014f-545b-4042-9921-f4f05a37aee8", "companyId": "14102976", "type": "payperiod", "_rid": "NmJkAKiCbEeExgIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeExgIAAAAAAA==/", "_etag": "\"a3005af2-0000-0100-0000-68701db60000\"", "_attachments": "attachments/", "_ts": 1752178102}, {"payPeriodId": "1050112637358618", "intervalCode": "MONTHLY", "status": "INITIAL", "description": "Monthly Payroll (1)", "startDate": "2025-10-01T00:00:00Z", "endDate": "2025-10-31T00:00:00Z", "submitByDate": "2025-10-30T00:00:00Z", "checkDate": "2025-10-31T00:00:00Z", "checkCount": 0, "id": "3b3bb84f-5227-48f1-9981-47898431027b", "companyId": "14102976", "type": "payperiod", "_rid": "NmJkAKiCbEeFxgIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeFxgIAAAAAAA==/", "_etag": "\"a3005ff2-0000-0100-0000-68701db60000\"", "_attachments": "attachments/", "_ts": 1752178102}], "links": [{"rel": "self", "href": "https://ca-payroll-ai-mock-sb-001-secure.nicebeach-8a7a52ab.eastus.azurecontainerapps.io/ca/companies/14102976/payperiods"}]}, "status_code": 200}