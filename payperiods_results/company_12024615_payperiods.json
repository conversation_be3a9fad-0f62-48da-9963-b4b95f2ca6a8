{"success": true, "company_id": "12024615", "data": {"metadata": {"contentItemCount": 192}, "content": [{"payPeriodId": "1030068963531201", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2024-12-26T00:00:00Z", "endDate": "2025-01-01T00:00:00Z", "submitByDate": "2024-12-31T00:00:00Z", "checkDate": "2025-01-03T00:00:00Z", "checkCount": 5, "id": "b82e24ab-a0a6-4a7f-a3cb-af8fa671d57f", "companyId": "12024615", "type": "payperiod", "_rid": "NmJkAKiCbEd5GAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEd5GAAAAAAAAA==/", "_etag": "\"9700fa4f-0000-0100-0000-686fd1170000\"", "_attachments": "attachments/", "_ts": 1752158487}, {"payPeriodId": "1030069094431703", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-01-02T00:00:00Z", "endDate": "2025-01-08T00:00:00Z", "submitByDate": "2025-01-08T00:00:00Z", "checkDate": "2025-01-10T00:00:00Z", "checkCount": 6, "id": "6463f5a9-d9db-47ce-b0c5-84e94716fb28", "companyId": "12024615", "type": "payperiod", "_rid": "NmJkAKiCbEd6GAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEd6GAAAAAAAAA==/", "_etag": "\"97000350-0000-0100-0000-686fd1170000\"", "_attachments": "attachments/", "_ts": 1752158487}, {"payPeriodId": "1030069238265692", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-01-09T00:00:00Z", "endDate": "2025-01-15T00:00:00Z", "submitByDate": "2025-01-15T00:00:00Z", "checkDate": "2025-01-17T00:00:00Z", "checkCount": 8, "id": "cd3138b0-ea39-4a49-af8e-33862342302f", "companyId": "12024615", "type": "payperiod", "_rid": "NmJkAKiCbEd7GAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEd7GAAAAAAAAA==/", "_etag": "\"97000b50-0000-0100-0000-686fd1170000\"", "_attachments": "attachments/", "_ts": 1752158487}, {"payPeriodId": "1030069373608991", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-01-16T00:00:00Z", "endDate": "2025-01-22T00:00:00Z", "submitByDate": "2025-01-22T00:00:00Z", "checkDate": "2025-01-24T00:00:00Z", "checkCount": 7, "id": "ec9b0414-c6a4-46f7-887d-f16130ddd319", "companyId": "12024615", "type": "payperiod", "_rid": "NmJkAKiCbEd8GAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEd8GAAAAAAAAA==/", "_etag": "\"97000f50-0000-0100-0000-686fd1170000\"", "_attachments": "attachments/", "_ts": 1752158487}, {"payPeriodId": "1030071455230638", "status": "COMPLETED", "description": "Reverse", "startDate": "2025-01-23T00:00:00Z", "endDate": "2025-01-29T00:00:00Z", "submitByDate": "2025-01-30T00:00:00Z", "checkDate": "2025-01-31T00:00:00Z", "checkCount": 2, "originalPayPeriodID": "1030069513553401", "id": "87db6c0c-fde7-412c-9c9d-e3c4c0c73726", "companyId": "12024615", "type": "payperiod", "_rid": "NmJkAKiCbEd9GAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEd9GAAAAAAAAA==/", "_etag": "\"97001150-0000-0100-0000-686fd1170000\"", "_attachments": "attachments/", "_ts": 1752158487}, {"payPeriodId": "1030071469477360", "status": "COMPLETED", "description": "payroll correction", "startDate": "2025-01-23T00:00:00Z", "endDate": "2025-01-29T00:00:00Z", "submitByDate": "2025-01-30T00:00:00Z", "checkDate": "2025-01-31T00:00:00Z", "checkCount": 5, "id": "88c02333-96bf-47af-a424-34908c5d13c4", "companyId": "12024615", "type": "payperiod", "_rid": "NmJkAKiCbEd+GAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEd+GAAAAAAAAA==/", "_etag": "\"97001350-0000-0100-0000-686fd1170000\"", "_attachments": "attachments/", "_ts": 1752158487}, {"payPeriodId": "1030069651102076", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-01-30T00:00:00Z", "endDate": "2025-02-05T00:00:00Z", "submitByDate": "2025-02-05T00:00:00Z", "checkDate": "2025-02-07T00:00:00Z", "checkCount": 7, "id": "4c07c39a-4beb-49f9-aaed-a352297ac1f5", "companyId": "12024615", "type": "payperiod", "_rid": "NmJkAKiCbEd-GAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEd-GAAAAAAAAA==/", "_etag": "\"97001750-0000-0100-0000-686fd1170000\"", "_attachments": "attachments/", "_ts": 1752158487}, {"payPeriodId": "1030069790247167", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-02-06T00:00:00Z", "endDate": "2025-02-12T00:00:00Z", "submitByDate": "2025-02-12T00:00:00Z", "checkDate": "2025-02-14T00:00:00Z", "checkCount": 7, "id": "9d17da80-6feb-4b0f-9e18-8f1bc7a7f418", "companyId": "12024615", "type": "payperiod", "_rid": "NmJkAKiCbEeAGAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeAGAAAAAAAAA==/", "_etag": "\"97001950-0000-0100-0000-686fd1170000\"", "_attachments": "attachments/", "_ts": 1752158487}, {"payPeriodId": "1030069920270090", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-02-13T00:00:00Z", "endDate": "2025-02-19T00:00:00Z", "submitByDate": "2025-02-19T00:00:00Z", "checkDate": "2025-02-21T00:00:00Z", "checkCount": 7, "id": "77fbc719-66ca-418e-a7b7-5d7cfd3c8369", "companyId": "12024615", "type": "payperiod", "_rid": "NmJkAKiCbEeBGAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeBGAAAAAAAAA==/", "_etag": "\"97001e50-0000-0100-0000-686fd1170000\"", "_attachments": "attachments/", "_ts": 1752158487}, {"payPeriodId": "1030070057251611", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-02-20T00:00:00Z", "endDate": "2025-02-26T00:00:00Z", "submitByDate": "2025-02-26T00:00:00Z", "checkDate": "2025-02-28T00:00:00Z", "checkCount": 4, "id": "fd93b4c8-1d0c-42dd-96c6-199e74a983f9", "companyId": "12024615", "type": "payperiod", "_rid": "NmJkAKiCbEeCGAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeCGAAAAAAAAA==/", "_etag": "\"97002450-0000-0100-0000-686fd1170000\"", "_attachments": "attachments/", "_ts": 1752158487}, {"payPeriodId": "1030070191358480", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-02-27T00:00:00Z", "endDate": "2025-03-05T00:00:00Z", "submitByDate": "2025-03-05T00:00:00Z", "checkDate": "2025-03-07T00:00:00Z", "checkCount": 4, "id": "3f29cb71-eed0-4f8b-b801-75dcb0e78e4b", "companyId": "12024615", "type": "payperiod", "_rid": "NmJkAKiCbEeDGAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeDGAAAAAAAAA==/", "_etag": "\"97002650-0000-0100-0000-686fd1170000\"", "_attachments": "attachments/", "_ts": 1752158487}, {"payPeriodId": "1030070356662740", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-03-06T00:00:00Z", "endDate": "2025-03-12T00:00:00Z", "submitByDate": "2025-03-12T00:00:00Z", "checkDate": "2025-03-14T00:00:00Z", "checkCount": 5, "id": "4c83ac93-11e5-4db3-a0f2-2b4577fa8c47", "companyId": "12024615", "type": "payperiod", "_rid": "NmJkAKiCbEeEGAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeEGAAAAAAAAA==/", "_etag": "\"97002850-0000-0100-0000-686fd1180000\"", "_attachments": "attachments/", "_ts": 1752158488}, {"payPeriodId": "1030070504948167", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-03-13T00:00:00Z", "endDate": "2025-03-19T00:00:00Z", "submitByDate": "2025-03-19T00:00:00Z", "checkDate": "2025-03-21T00:00:00Z", "checkCount": 6, "id": "2545722f-d0d9-4f47-ad76-7cef8990d835", "companyId": "12024615", "type": "payperiod", "_rid": "NmJkAKiCbEeFGAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeFGAAAAAAAAA==/", "_etag": "\"97002c50-0000-0100-0000-686fd1180000\"", "_attachments": "attachments/", "_ts": 1752158488}, {"payPeriodId": "1030070656686961", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-03-20T00:00:00Z", "endDate": "2025-03-26T00:00:00Z", "submitByDate": "2025-03-26T00:00:00Z", "checkDate": "2025-03-28T00:00:00Z", "checkCount": 13, "id": "65c8bb2e-a4ab-4878-b6ec-f42ad9995ad7", "companyId": "12024615", "type": "payperiod", "_rid": "NmJkAKiCbEeGGAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeGGAAAAAAAAA==/", "_etag": "\"97002f50-0000-0100-0000-686fd1180000\"", "_attachments": "attachments/", "_ts": 1752158488}, {"payPeriodId": "1030072567561844", "status": "COMPLETED", "description": "Payroll Correction", "startDate": "2025-03-20T00:00:00Z", "endDate": "2025-03-26T00:00:00Z", "submitByDate": "2025-03-27T00:00:00Z", "checkDate": "2025-03-28T00:00:00Z", "checkCount": 1, "id": "90eeb7da-bb95-430d-aae6-7d11f16ee1c7", "companyId": "12024615", "type": "payperiod", "_rid": "NmJkAKiCbEeHGAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeHGAAAAAAAAA==/", "_etag": "\"97003150-0000-0100-0000-686fd1180000\"", "_attachments": "attachments/", "_ts": 1752158488}, {"payPeriodId": "1030070772731858", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-03-27T00:00:00Z", "endDate": "2025-04-02T00:00:00Z", "submitByDate": "2025-04-02T00:00:00Z", "checkDate": "2025-04-04T00:00:00Z", "checkCount": 0, "id": "c172bdf9-7f74-4bc3-825b-0d535f5905c3", "companyId": "12024615", "type": "payperiod", "_rid": "NmJkAKiCbEeIGAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeIGAAAAAAAAA==/", "_etag": "\"97003450-0000-0100-0000-686fd1180000\"", "_attachments": "attachments/", "_ts": 1752158488}, {"payPeriodId": "1030070926322281", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-04-03T00:00:00Z", "endDate": "2025-04-09T00:00:00Z", "submitByDate": "2025-04-09T00:00:00Z", "checkDate": "2025-04-11T00:00:00Z", "checkCount": 0, "id": "96b395a9-df61-4d79-b45c-103bfa41c0a4", "companyId": "12024615", "type": "payperiod", "_rid": "NmJkAKiCbEeJGAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeJGAAAAAAAAA==/", "_etag": "\"97003b50-0000-0100-0000-686fd1180000\"", "_attachments": "attachments/", "_ts": 1752158488}, {"payPeriodId": "1030072828646893", "status": "INITIAL", "description": "Missing Pay", "startDate": "2025-04-03T00:00:00Z", "endDate": "2025-04-09T00:00:00Z", "submitByDate": "2025-04-10T00:00:00Z", "checkDate": "2025-04-11T00:00:00Z", "checkCount": 0, "id": "c2ebf619-4dd1-4e54-950d-4c0fdf84f4d6", "companyId": "12024615", "type": "payperiod", "_rid": "NmJkAKiCbEeKGAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeKGAAAAAAAAA==/", "_etag": "\"97003e50-0000-0100-0000-686fd1180000\"", "_attachments": "attachments/", "_ts": 1752158488}, {"payPeriodId": "1030071061567267", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-04-10T00:00:00Z", "endDate": "2025-04-16T00:00:00Z", "submitByDate": "2025-04-16T00:00:00Z", "checkDate": "2025-04-18T00:00:00Z", "checkCount": 0, "id": "4c6b967a-48a6-49eb-ad7e-651a46b21948", "companyId": "12024615", "type": "payperiod", "_rid": "NmJkAKiCbEeLGAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeLGAAAAAAAAA==/", "_etag": "\"97004750-0000-0100-0000-686fd1180000\"", "_attachments": "attachments/", "_ts": 1752158488}, {"payPeriodId": "1030071209647439", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-04-17T00:00:00Z", "endDate": "2025-04-23T00:00:00Z", "submitByDate": "2025-04-23T00:00:00Z", "checkDate": "2025-04-25T00:00:00Z", "checkCount": 0, "id": "49b16494-b6fe-4c3d-8aa6-935c931ccdcd", "companyId": "12024615", "type": "payperiod", "_rid": "NmJkAKiCbEeMGAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeMGAAAAAAAAA==/", "_etag": "\"97004950-0000-0100-0000-686fd1180000\"", "_attachments": "attachments/", "_ts": 1752158488}, {"payPeriodId": "1030071332922686", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-04-24T00:00:00Z", "endDate": "2025-04-30T00:00:00Z", "submitByDate": "2025-04-30T00:00:00Z", "checkDate": "2025-05-02T00:00:00Z", "checkCount": 0, "id": "a48b3598-9c84-42f2-a00d-9bf24950206d", "companyId": "12024615", "type": "payperiod", "_rid": "NmJkAKiCbEeNGAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeNGAAAAAAAAA==/", "_etag": "\"97004d50-0000-0100-0000-686fd1180000\"", "_attachments": "attachments/", "_ts": 1752158488}, {"payPeriodId": "1030071478190132", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-05-01T00:00:00Z", "endDate": "2025-05-07T00:00:00Z", "submitByDate": "2025-05-07T00:00:00Z", "checkDate": "2025-05-09T00:00:00Z", "checkCount": 0, "id": "2f61a2e6-df07-4985-9753-27d7ebb5792d", "companyId": "12024615", "type": "payperiod", "_rid": "NmJkAKiCbEeOGAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeOGAAAAAAAAA==/", "_etag": "\"97005350-0000-0100-0000-686fd1180000\"", "_attachments": "attachments/", "_ts": 1752158488}, {"payPeriodId": "1030073374164861", "status": "INITIAL", "description": "1 check", "startDate": "2025-05-01T00:00:00Z", "endDate": "2025-05-07T00:00:00Z", "submitByDate": "2025-05-08T00:00:00Z", "checkDate": "2025-05-09T00:00:00Z", "checkCount": 0, "id": "cb814ddd-e31d-4c38-ae02-22dade7192f8", "companyId": "12024615", "type": "payperiod", "_rid": "NmJkAKiCbEePGAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEePGAAAAAAAAA==/", "_etag": "\"97005850-0000-0100-0000-686fd1180000\"", "_attachments": "attachments/", "_ts": 1752158488}, {"payPeriodId": "1030071608642976", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-05-08T00:00:00Z", "endDate": "2025-05-14T00:00:00Z", "submitByDate": "2025-05-14T00:00:00Z", "checkDate": "2025-05-16T00:00:00Z", "checkCount": 0, "id": "7b8d6b2c-87f1-4d0e-8ec0-5b67c835d6fb", "companyId": "12024615", "type": "payperiod", "_rid": "NmJkAKiCbEeQGAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeQGAAAAAAAAA==/", "_etag": "\"97005e50-0000-0100-0000-686fd1180000\"", "_attachments": "attachments/", "_ts": 1752158488}, {"payPeriodId": "1030071752279670", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-05-15T00:00:00Z", "endDate": "2025-05-21T00:00:00Z", "submitByDate": "2025-05-21T00:00:00Z", "checkDate": "2025-05-23T00:00:00Z", "checkCount": 0, "id": "69429a9c-13cc-4a94-9b80-b7b09000d160", "companyId": "12024615", "type": "payperiod", "_rid": "NmJkAKiCbEeRGAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeRGAAAAAAAAA==/", "_etag": "\"97006150-0000-0100-0000-686fd1180000\"", "_attachments": "attachments/", "_ts": 1752158488}, {"payPeriodId": "1030071876827217", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-05-22T00:00:00Z", "endDate": "2025-05-28T00:00:00Z", "submitByDate": "2025-05-28T00:00:00Z", "checkDate": "2025-05-30T00:00:00Z", "checkCount": 0, "id": "96a64ae5-1359-4e35-bf43-abfc7073d4ea", "companyId": "12024615", "type": "payperiod", "_rid": "NmJkAKiCbEeSGAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeSGAAAAAAAAA==/", "_etag": "\"97006750-0000-0100-0000-686fd1190000\"", "_attachments": "attachments/", "_ts": 1752158489}, {"payPeriodId": "1030073826130058", "status": "INITIAL", "description": "client missed hrs one time", "startDate": "2025-05-22T00:00:00Z", "endDate": "2025-05-28T00:00:00Z", "submitByDate": "2025-06-02T00:00:00Z", "checkDate": "2025-06-03T00:00:00Z", "checkCount": 0, "id": "e1757a36-2ac3-4215-8eb5-55b6e77363b8", "companyId": "12024615", "type": "payperiod", "_rid": "NmJkAKiCbEeTGAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeTGAAAAAAAAA==/", "_etag": "\"97006850-0000-0100-0000-686fd1190000\"", "_attachments": "attachments/", "_ts": 1752158489}, {"payPeriodId": "1030072021776019", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-05-29T00:00:00Z", "endDate": "2025-06-04T00:00:00Z", "submitByDate": "2025-06-04T00:00:00Z", "checkDate": "2025-06-06T00:00:00Z", "checkCount": 0, "id": "42c97b04-0498-45e1-94d6-551e7ce146cd", "companyId": "12024615", "type": "payperiod", "_rid": "NmJkAKiCbEeUGAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeUGAAAAAAAAA==/", "_etag": "\"97006a50-0000-0100-0000-686fd1190000\"", "_attachments": "attachments/", "_ts": 1752158489}, {"payPeriodId": "1030072130969140", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-06-05T00:00:00Z", "endDate": "2025-06-11T00:00:00Z", "submitByDate": "2025-06-11T00:00:00Z", "checkDate": "2025-06-13T00:00:00Z", "checkCount": 0, "id": "983df753-c58a-4bae-817d-f46ae280bf5c", "companyId": "12024615", "type": "payperiod", "_rid": "NmJkAKiCbEeVGAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeVGAAAAAAAAA==/", "_etag": "\"97006c50-0000-0100-0000-686fd1190000\"", "_attachments": "attachments/", "_ts": 1752158489}, {"payPeriodId": "1030074109123261", "status": "INITIAL", "description": "Void", "startDate": "2025-06-05T00:00:00Z", "endDate": "2025-06-11T00:00:00Z", "submitByDate": "2025-06-12T00:00:00Z", "checkDate": "2025-06-13T00:00:00Z", "checkCount": 0, "id": "d2a1defd-949b-4fb6-910b-25ef05a3baf9", "companyId": "12024615", "type": "payperiod", "_rid": "NmJkAKiCbEeWGAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeWGAAAAAAAAA==/", "_etag": "\"97006e50-0000-0100-0000-686fd1190000\"", "_attachments": "attachments/", "_ts": 1752158489}, {"payPeriodId": "1030072277614893", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-06-12T00:00:00Z", "endDate": "2025-06-18T00:00:00Z", "submitByDate": "2025-06-18T00:00:00Z", "checkDate": "2025-06-20T00:00:00Z", "checkCount": 0, "id": "2d227288-e3f2-40c5-b287-151306e7a0f5", "companyId": "12024615", "type": "payperiod", "_rid": "NmJkAKiCbEeXGAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeXGAAAAAAAAA==/", "_etag": "\"97006f50-0000-0100-0000-686fd1190000\"", "_attachments": "attachments/", "_ts": 1752158489}, {"payPeriodId": "1030072398604414", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-06-19T00:00:00Z", "endDate": "2025-06-25T00:00:00Z", "submitByDate": "2025-06-25T00:00:00Z", "checkDate": "2025-06-27T00:00:00Z", "checkCount": 0, "id": "238117af-5d60-4151-9278-c8c203dffbd6", "companyId": "12024615", "type": "payperiod", "_rid": "NmJkAKiCbEeYGAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeYGAAAAAAAAA==/", "_etag": "\"97007250-0000-0100-0000-686fd1190000\"", "_attachments": "attachments/", "_ts": 1752158489}, {"payPeriodId": "1030072550063390", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-06-26T00:00:00Z", "endDate": "2025-07-02T00:00:00Z", "submitByDate": "2025-07-01T00:00:00Z", "checkDate": "2025-07-03T00:00:00Z", "checkCount": 0, "id": "94229326-fb73-4e8e-ba40-480519a8ba23", "companyId": "12024615", "type": "payperiod", "_rid": "NmJkAKiCbEeZGAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeZGAAAAAAAAA==/", "_etag": "\"97007450-0000-0100-0000-686fd1190000\"", "_attachments": "attachments/", "_ts": 1752158489}, {"payPeriodId": "1030072673464903", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-07-03T00:00:00Z", "endDate": "2025-07-09T00:00:00Z", "submitByDate": "2025-07-09T00:00:00Z", "checkDate": "2025-07-11T00:00:00Z", "checkCount": 0, "id": "ea022425-10d4-4682-b9a3-3362817fcc4b", "companyId": "12024615", "type": "payperiod", "_rid": "NmJkAKiCbEeaGAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeaGAAAAAAAAA==/", "_etag": "\"97007750-0000-0100-0000-686fd1190000\"", "_attachments": "attachments/", "_ts": 1752158489}, {"payPeriodId": "1030072812346055", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-07-10T00:00:00Z", "endDate": "2025-07-16T00:00:00Z", "submitByDate": "2025-07-16T00:00:00Z", "checkDate": "2025-07-18T00:00:00Z", "checkCount": 0, "id": "d8731f8c-0a40-4d62-80bf-2864c39ec254", "companyId": "12024615", "type": "payperiod", "_rid": "NmJkAKiCbEebGAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEebGAAAAAAAAA==/", "_etag": "\"97007950-0000-0100-0000-686fd1190000\"", "_attachments": "attachments/", "_ts": 1752158489}, {"payPeriodId": "1030072952324244", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-07-17T00:00:00Z", "endDate": "2025-07-23T00:00:00Z", "submitByDate": "2025-07-23T00:00:00Z", "checkDate": "2025-07-25T00:00:00Z", "checkCount": 0, "id": "5d7e465f-d4f6-4a42-a2da-8cad2391ea9c", "companyId": "12024615", "type": "payperiod", "_rid": "NmJkAKiCbEecGAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEecGAAAAAAAAA==/", "_etag": "\"97007c50-0000-0100-0000-686fd1190000\"", "_attachments": "attachments/", "_ts": 1752158489}, {"payPeriodId": "1030073078992493", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-07-24T00:00:00Z", "endDate": "2025-07-30T00:00:00Z", "submitByDate": "2025-07-30T00:00:00Z", "checkDate": "2025-08-01T00:00:00Z", "checkCount": 0, "id": "e4a7f07a-dcac-412c-a580-5e9973d85e3b", "companyId": "12024615", "type": "payperiod", "_rid": "NmJkAKiCbEedGAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEedGAAAAAAAAA==/", "_etag": "\"97008050-0000-0100-0000-686fd1190000\"", "_attachments": "attachments/", "_ts": 1752158489}, {"payPeriodId": "1030073221071764", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-07-31T00:00:00Z", "endDate": "2025-08-06T00:00:00Z", "submitByDate": "2025-08-06T00:00:00Z", "checkDate": "2025-08-08T00:00:00Z", "checkCount": 0, "id": "b9ee6a67-a459-4458-a4a1-ccba0b743ca9", "companyId": "12024615", "type": "payperiod", "_rid": "NmJkAKiCbEeeGAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeeGAAAAAAAAA==/", "_etag": "\"97008850-0000-0100-0000-686fd1190000\"", "_attachments": "attachments/", "_ts": 1752158489}, {"payPeriodId": "1030073357993284", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-08-07T00:00:00Z", "endDate": "2025-08-13T00:00:00Z", "submitByDate": "2025-08-13T00:00:00Z", "checkDate": "2025-08-15T00:00:00Z", "checkCount": 0, "id": "a955e970-d54e-466a-b1bf-62e439aabda8", "companyId": "12024615", "type": "payperiod", "_rid": "NmJkAKiCbEefGAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEefGAAAAAAAAA==/", "_etag": "\"97009250-0000-0100-0000-686fd1190000\"", "_attachments": "attachments/", "_ts": 1752158489}, {"payPeriodId": "1030073498594004", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-08-14T00:00:00Z", "endDate": "2025-08-20T00:00:00Z", "submitByDate": "2025-08-20T00:00:00Z", "checkDate": "2025-08-22T00:00:00Z", "checkCount": 0, "id": "cd43a7b1-03d9-46fa-8b44-32f38a11ca09", "companyId": "12024615", "type": "payperiod", "_rid": "NmJkAKiCbEegGAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEegGAAAAAAAAA==/", "_etag": "\"97009550-0000-0100-0000-686fd11a0000\"", "_attachments": "attachments/", "_ts": 1752158490}, {"payPeriodId": "1030073648955845", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-08-21T00:00:00Z", "endDate": "2025-08-27T00:00:00Z", "submitByDate": "2025-08-27T00:00:00Z", "checkDate": "2025-08-29T00:00:00Z", "checkCount": 0, "id": "4a15375e-cde7-49a1-b009-270d59e36896", "companyId": "12024615", "type": "payperiod", "_rid": "NmJkAKiCbEehGAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEehGAAAAAAAAA==/", "_etag": "\"97009750-0000-0100-0000-686fd11a0000\"", "_attachments": "attachments/", "_ts": 1752158490}, {"payPeriodId": "1030073787936347", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-08-28T00:00:00Z", "endDate": "2025-09-03T00:00:00Z", "submitByDate": "2025-09-03T00:00:00Z", "checkDate": "2025-09-05T00:00:00Z", "checkCount": 0, "id": "ddb135aa-3dae-450b-af2e-279baed2abf2", "companyId": "12024615", "type": "payperiod", "_rid": "NmJkAKiCbEeiGAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeiGAAAAAAAAA==/", "_etag": "\"97009850-0000-0100-0000-686fd11a0000\"", "_attachments": "attachments/", "_ts": 1752158490}, {"payPeriodId": "1030073920010891", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-09-04T00:00:00Z", "endDate": "2025-09-10T00:00:00Z", "submitByDate": "2025-09-10T00:00:00Z", "checkDate": "2025-09-12T00:00:00Z", "checkCount": 0, "id": "3caf1454-a74f-4442-a569-764bbb5968e3", "companyId": "12024615", "type": "payperiod", "_rid": "NmJkAKiCbEejGAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEejGAAAAAAAAA==/", "_etag": "\"97009a50-0000-0100-0000-686fd11a0000\"", "_attachments": "attachments/", "_ts": 1752158490}, {"payPeriodId": "1030074060588213", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-09-11T00:00:00Z", "endDate": "2025-09-17T00:00:00Z", "submitByDate": "2025-09-17T00:00:00Z", "checkDate": "2025-09-19T00:00:00Z", "checkCount": 0, "id": "eaa207ec-24b4-45d9-8e3b-8ab70b7fef0f", "companyId": "12024615", "type": "payperiod", "_rid": "NmJkAKiCbEekGAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEekGAAAAAAAAA==/", "_etag": "\"97009f50-0000-0100-0000-686fd11a0000\"", "_attachments": "attachments/", "_ts": 1752158490}, {"payPeriodId": "1030074190596992", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-09-18T00:00:00Z", "endDate": "2025-09-24T00:00:00Z", "submitByDate": "2025-09-24T00:00:00Z", "checkDate": "2025-09-26T00:00:00Z", "checkCount": 0, "id": "4a62ad24-dcee-4d5e-83bd-cbccfe72666b", "companyId": "12024615", "type": "payperiod", "_rid": "NmJkAKiCbEelGAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEelGAAAAAAAAA==/", "_etag": "\"9700a450-0000-0100-0000-686fd11a0000\"", "_attachments": "attachments/", "_ts": 1752158490}, {"payPeriodId": "1030074347462799", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-09-25T00:00:00Z", "endDate": "2025-10-01T00:00:00Z", "submitByDate": "2025-10-01T00:00:00Z", "checkDate": "2025-10-03T00:00:00Z", "checkCount": 0, "id": "64267191-58c8-48d2-9dd5-0a3a01e3f0c5", "companyId": "12024615", "type": "payperiod", "_rid": "NmJkAKiCbEemGAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEemGAAAAAAAAA==/", "_etag": "\"9700a750-0000-0100-0000-686fd11a0000\"", "_attachments": "attachments/", "_ts": 1752158490}, {"payPeriodId": "1030074467290270", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-10-02T00:00:00Z", "endDate": "2025-10-08T00:00:00Z", "submitByDate": "2025-10-08T00:00:00Z", "checkDate": "2025-10-10T00:00:00Z", "checkCount": 0, "id": "2d3a80ba-bd5d-46b5-9ac8-5e3e3846b9b3", "companyId": "12024615", "type": "payperiod", "_rid": "NmJkAKiCbEenGAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEenGAAAAAAAAA==/", "_etag": "\"9700a850-0000-0100-0000-686fd11a0000\"", "_attachments": "attachments/", "_ts": 1752158490}, {"payPeriodId": "1030069513553401", "intervalCode": "WEEKLY", "status": "REVERSED", "description": "Weekly Payroll (2)", "startDate": "2025-01-23T00:00:00Z", "endDate": "2025-01-29T00:00:00Z", "submitByDate": "2025-01-29T00:00:00Z", "checkDate": "2025-01-31T00:00:00Z", "checkCount": 2, "id": "f163d6d9-ecf3-4806-bb96-b6a9758d827c", "companyId": "12024615", "type": "payperiod", "_rid": "NmJkAKiCbEeoGAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeoGAAAAAAAAA==/", "_etag": "\"9700b050-0000-0100-0000-686fd11a0000\"", "_attachments": "attachments/", "_ts": 1752158490}, {"payPeriodId": "1030068963531201", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2024-12-26T00:00:00Z", "endDate": "2025-01-01T00:00:00Z", "submitByDate": "2024-12-31T00:00:00Z", "checkDate": "2025-01-03T00:00:00Z", "checkCount": 5, "id": "034a914e-ad75-49f7-aa07-27cf1cc8cd56", "companyId": "12024615", "type": "payperiod", "_rid": "NmJkAKiCbEfVGAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfVGAAAAAAAAA==/", "_etag": "\"97005d51-0000-0100-0000-686fd11e0000\"", "_attachments": "attachments/", "_ts": 1752158494}, {"payPeriodId": "1030069094431703", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-01-02T00:00:00Z", "endDate": "2025-01-08T00:00:00Z", "submitByDate": "2025-01-08T00:00:00Z", "checkDate": "2025-01-10T00:00:00Z", "checkCount": 6, "id": "d84b24fd-abef-43a6-b422-69650dd07d49", "companyId": "12024615", "type": "payperiod", "_rid": "NmJkAKiCbEfWGAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfWGAAAAAAAAA==/", "_etag": "\"97006351-0000-0100-0000-686fd11e0000\"", "_attachments": "attachments/", "_ts": 1752158494}, {"payPeriodId": "1030069238265692", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-01-09T00:00:00Z", "endDate": "2025-01-15T00:00:00Z", "submitByDate": "2025-01-15T00:00:00Z", "checkDate": "2025-01-17T00:00:00Z", "checkCount": 8, "id": "af05b002-5e5f-42e6-a5e9-d71e1220750c", "companyId": "12024615", "type": "payperiod", "_rid": "NmJkAKiCbEfXGAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfXGAAAAAAAAA==/", "_etag": "\"97006651-0000-0100-0000-686fd11e0000\"", "_attachments": "attachments/", "_ts": 1752158494}, {"payPeriodId": "1030069373608991", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-01-16T00:00:00Z", "endDate": "2025-01-22T00:00:00Z", "submitByDate": "2025-01-22T00:00:00Z", "checkDate": "2025-01-24T00:00:00Z", "checkCount": 7, "id": "a7259ad9-cd0b-418a-98b4-35cf6dc09170", "companyId": "12024615", "type": "payperiod", "_rid": "NmJkAKiCbEfYGAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfYGAAAAAAAAA==/", "_etag": "\"97006751-0000-0100-0000-686fd11e0000\"", "_attachments": "attachments/", "_ts": 1752158494}, {"payPeriodId": "1030071455230638", "status": "COMPLETED", "description": "Reverse", "startDate": "2025-01-23T00:00:00Z", "endDate": "2025-01-29T00:00:00Z", "submitByDate": "2025-01-30T00:00:00Z", "checkDate": "2025-01-31T00:00:00Z", "checkCount": 2, "originalPayPeriodID": "1030069513553401", "id": "b7800d70-f76d-4519-bec7-291784e51b16", "companyId": "12024615", "type": "payperiod", "_rid": "NmJkAKiCbEfZGAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfZGAAAAAAAAA==/", "_etag": "\"97006b51-0000-0100-0000-686fd11e0000\"", "_attachments": "attachments/", "_ts": 1752158494}, {"payPeriodId": "1030071469477360", "status": "COMPLETED", "description": "payroll correction", "startDate": "2025-01-23T00:00:00Z", "endDate": "2025-01-29T00:00:00Z", "submitByDate": "2025-01-30T00:00:00Z", "checkDate": "2025-01-31T00:00:00Z", "checkCount": 5, "id": "e4e08244-4937-4d7b-be9f-d75e704a15f8", "companyId": "12024615", "type": "payperiod", "_rid": "NmJkAKiCbEfaGAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfaGAAAAAAAAA==/", "_etag": "\"97006d51-0000-0100-0000-686fd11e0000\"", "_attachments": "attachments/", "_ts": 1752158494}, {"payPeriodId": "1030069651102076", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-01-30T00:00:00Z", "endDate": "2025-02-05T00:00:00Z", "submitByDate": "2025-02-05T00:00:00Z", "checkDate": "2025-02-07T00:00:00Z", "checkCount": 7, "id": "b15065ea-fff1-4de5-a5df-d66a269355ff", "companyId": "12024615", "type": "payperiod", "_rid": "NmJkAKiCbEfbGAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfbGAAAAAAAAA==/", "_etag": "\"97006f51-0000-0100-0000-686fd11e0000\"", "_attachments": "attachments/", "_ts": 1752158494}, {"payPeriodId": "1030069790247167", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-02-06T00:00:00Z", "endDate": "2025-02-12T00:00:00Z", "submitByDate": "2025-02-12T00:00:00Z", "checkDate": "2025-02-14T00:00:00Z", "checkCount": 7, "id": "f5689ac7-93d2-460a-be49-d1a48ec135cd", "companyId": "12024615", "type": "payperiod", "_rid": "NmJkAKiCbEfcGAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfcGAAAAAAAAA==/", "_etag": "\"97007351-0000-0100-0000-686fd11f0000\"", "_attachments": "attachments/", "_ts": 1752158495}, {"payPeriodId": "1030069920270090", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-02-13T00:00:00Z", "endDate": "2025-02-19T00:00:00Z", "submitByDate": "2025-02-19T00:00:00Z", "checkDate": "2025-02-21T00:00:00Z", "checkCount": 7, "id": "f25e4051-5d32-4e95-badd-619c6fd37ddc", "companyId": "12024615", "type": "payperiod", "_rid": "NmJkAKiCbEfdGAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfdGAAAAAAAAA==/", "_etag": "\"97007551-0000-0100-0000-686fd11f0000\"", "_attachments": "attachments/", "_ts": 1752158495}, {"payPeriodId": "1030070057251611", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-02-20T00:00:00Z", "endDate": "2025-02-26T00:00:00Z", "submitByDate": "2025-02-26T00:00:00Z", "checkDate": "2025-02-28T00:00:00Z", "checkCount": 4, "id": "6e2cec1e-2fbd-4b73-b548-cd4aa7d62df3", "companyId": "12024615", "type": "payperiod", "_rid": "NmJkAKiCbEfeGAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfeGAAAAAAAAA==/", "_etag": "\"97007751-0000-0100-0000-686fd11f0000\"", "_attachments": "attachments/", "_ts": 1752158495}, {"payPeriodId": "1030070191358480", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-02-27T00:00:00Z", "endDate": "2025-03-05T00:00:00Z", "submitByDate": "2025-03-05T00:00:00Z", "checkDate": "2025-03-07T00:00:00Z", "checkCount": 4, "id": "c8675c75-5994-4c05-825c-216ec08d179c", "companyId": "12024615", "type": "payperiod", "_rid": "NmJkAKiCbEffGAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEffGAAAAAAAAA==/", "_etag": "\"97007b51-0000-0100-0000-686fd11f0000\"", "_attachments": "attachments/", "_ts": 1752158495}, {"payPeriodId": "1030070356662740", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-03-06T00:00:00Z", "endDate": "2025-03-12T00:00:00Z", "submitByDate": "2025-03-12T00:00:00Z", "checkDate": "2025-03-14T00:00:00Z", "checkCount": 5, "id": "79dd883e-83f0-47f8-9832-bc0f7173e590", "companyId": "12024615", "type": "payperiod", "_rid": "NmJkAKiCbEfgGAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfgGAAAAAAAAA==/", "_etag": "\"97007f51-0000-0100-0000-686fd11f0000\"", "_attachments": "attachments/", "_ts": 1752158495}, {"payPeriodId": "1030070504948167", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-03-13T00:00:00Z", "endDate": "2025-03-19T00:00:00Z", "submitByDate": "2025-03-19T00:00:00Z", "checkDate": "2025-03-21T00:00:00Z", "checkCount": 6, "id": "66b57820-7a4b-44cb-9fc0-0d9778c506ea", "companyId": "12024615", "type": "payperiod", "_rid": "NmJkAKiCbEfhGAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfhGAAAAAAAAA==/", "_etag": "\"97008351-0000-0100-0000-686fd11f0000\"", "_attachments": "attachments/", "_ts": 1752158495}, {"payPeriodId": "1030070656686961", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-03-20T00:00:00Z", "endDate": "2025-03-26T00:00:00Z", "submitByDate": "2025-03-26T00:00:00Z", "checkDate": "2025-03-28T00:00:00Z", "checkCount": 13, "id": "c5104945-50d6-487a-b064-49d6606469ba", "companyId": "12024615", "type": "payperiod", "_rid": "NmJkAKiCbEfiGAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfiGAAAAAAAAA==/", "_etag": "\"97008751-0000-0100-0000-686fd11f0000\"", "_attachments": "attachments/", "_ts": 1752158495}, {"payPeriodId": "1030072567561844", "status": "COMPLETED", "description": "Payroll Correction", "startDate": "2025-03-20T00:00:00Z", "endDate": "2025-03-26T00:00:00Z", "submitByDate": "2025-03-27T00:00:00Z", "checkDate": "2025-03-28T00:00:00Z", "checkCount": 1, "id": "2f3aab32-d9a7-4208-aabb-8f9d70ed0080", "companyId": "12024615", "type": "payperiod", "_rid": "NmJkAKiCbEfjGAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfjGAAAAAAAAA==/", "_etag": "\"97008f51-0000-0100-0000-686fd11f0000\"", "_attachments": "attachments/", "_ts": 1752158495}, {"payPeriodId": "1030070772731858", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-03-27T00:00:00Z", "endDate": "2025-04-02T00:00:00Z", "submitByDate": "2025-04-02T00:00:00Z", "checkDate": "2025-04-04T00:00:00Z", "checkCount": 17, "id": "7f013de6-06a2-4a94-a3c9-15fcdd386445", "companyId": "12024615", "type": "payperiod", "_rid": "NmJkAKiCbEfkGAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfkGAAAAAAAAA==/", "_etag": "\"97009151-0000-0100-0000-686fd11f0000\"", "_attachments": "attachments/", "_ts": 1752158495}, {"payPeriodId": "1030070926322281", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-04-03T00:00:00Z", "endDate": "2025-04-09T00:00:00Z", "submitByDate": "2025-04-09T00:00:00Z", "checkDate": "2025-04-11T00:00:00Z", "checkCount": 17, "id": "94723ce5-ef4b-451a-9435-71de2c88c0a2", "companyId": "12024615", "type": "payperiod", "_rid": "NmJkAKiCbEflGAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEflGAAAAAAAAA==/", "_etag": "\"97009451-0000-0100-0000-686fd11f0000\"", "_attachments": "attachments/", "_ts": 1752158495}, {"payPeriodId": "1030072828646893", "status": "COMPLETED", "description": "Missing Pay", "startDate": "2025-04-03T00:00:00Z", "endDate": "2025-04-09T00:00:00Z", "submitByDate": "2025-04-10T00:00:00Z", "checkDate": "2025-04-11T00:00:00Z", "checkCount": 1, "id": "9d136ff8-1e0c-4def-8da4-ef129575166b", "companyId": "12024615", "type": "payperiod", "_rid": "NmJkAKiCbEfmGAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfmGAAAAAAAAA==/", "_etag": "\"97009751-0000-0100-0000-686fd11f0000\"", "_attachments": "attachments/", "_ts": 1752158495}, {"payPeriodId": "1030071061567267", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-04-10T00:00:00Z", "endDate": "2025-04-16T00:00:00Z", "submitByDate": "2025-04-16T00:00:00Z", "checkDate": "2025-04-18T00:00:00Z", "checkCount": 20, "id": "db6a92d9-0824-4805-8e70-b46739f9dd6f", "companyId": "12024615", "type": "payperiod", "_rid": "NmJkAKiCbEfnGAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfnGAAAAAAAAA==/", "_etag": "\"97009a51-0000-0100-0000-686fd11f0000\"", "_attachments": "attachments/", "_ts": 1752158495}, {"payPeriodId": "1030071209647439", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-04-17T00:00:00Z", "endDate": "2025-04-23T00:00:00Z", "submitByDate": "2025-04-23T00:00:00Z", "checkDate": "2025-04-25T00:00:00Z", "checkCount": 19, "id": "83ebfc78-5933-4513-ae3e-9d698025044b", "companyId": "12024615", "type": "payperiod", "_rid": "NmJkAKiCbEfoGAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfoGAAAAAAAAA==/", "_etag": "\"97009d51-0000-0100-0000-686fd11f0000\"", "_attachments": "attachments/", "_ts": 1752158495}, {"payPeriodId": "1030071332922686", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-04-24T00:00:00Z", "endDate": "2025-04-30T00:00:00Z", "submitByDate": "2025-04-30T00:00:00Z", "checkDate": "2025-05-02T00:00:00Z", "checkCount": 20, "id": "c449b738-6de7-4b45-a98e-bed2d5a2e62a", "companyId": "12024615", "type": "payperiod", "_rid": "NmJkAKiCbEfpGAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfpGAAAAAAAAA==/", "_etag": "\"9700a251-0000-0100-0000-686fd11f0000\"", "_attachments": "attachments/", "_ts": 1752158495}, {"payPeriodId": "1030071478190132", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-05-01T00:00:00Z", "endDate": "2025-05-07T00:00:00Z", "submitByDate": "2025-05-07T00:00:00Z", "checkDate": "2025-05-09T00:00:00Z", "checkCount": 19, "id": "d969f5bb-f03f-4f7f-8ab9-718cc002ed8c", "companyId": "12024615", "type": "payperiod", "_rid": "NmJkAKiCbEfqGAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfqGAAAAAAAAA==/", "_etag": "\"9700a651-0000-0100-0000-686fd1200000\"", "_attachments": "attachments/", "_ts": 1752158496}, {"payPeriodId": "1030073374164861", "status": "COMPLETED", "description": "1 check", "startDate": "2025-05-01T00:00:00Z", "endDate": "2025-05-07T00:00:00Z", "submitByDate": "2025-05-08T00:00:00Z", "checkDate": "2025-05-09T00:00:00Z", "checkCount": 1, "id": "56c3d68c-f3f5-47f1-bdb3-ddb4b6d8f75d", "companyId": "12024615", "type": "payperiod", "_rid": "NmJkAKiCbEfrGAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfrGAAAAAAAAA==/", "_etag": "\"9700a951-0000-0100-0000-686fd1200000\"", "_attachments": "attachments/", "_ts": 1752158496}, {"payPeriodId": "1030071608642976", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-05-08T00:00:00Z", "endDate": "2025-05-14T00:00:00Z", "submitByDate": "2025-05-14T00:00:00Z", "checkDate": "2025-05-16T00:00:00Z", "checkCount": 20, "id": "0e7c891f-bd48-4d58-81d5-a804c1450569", "companyId": "12024615", "type": "payperiod", "_rid": "NmJkAKiCbEfsGAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfsGAAAAAAAAA==/", "_etag": "\"9700b151-0000-0100-0000-686fd1200000\"", "_attachments": "attachments/", "_ts": 1752158496}, {"payPeriodId": "1030071752279670", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-05-15T00:00:00Z", "endDate": "2025-05-21T00:00:00Z", "submitByDate": "2025-05-21T00:00:00Z", "checkDate": "2025-05-23T00:00:00Z", "checkCount": 20, "id": "b2f0aeac-e09b-4938-b573-53566d939fdb", "companyId": "12024615", "type": "payperiod", "_rid": "NmJkAKiCbEftGAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEftGAAAAAAAAA==/", "_etag": "\"9700bb51-0000-0100-0000-686fd1200000\"", "_attachments": "attachments/", "_ts": 1752158496}, {"payPeriodId": "1030071876827217", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-05-22T00:00:00Z", "endDate": "2025-05-28T00:00:00Z", "submitByDate": "2025-05-28T00:00:00Z", "checkDate": "2025-05-30T00:00:00Z", "checkCount": 18, "id": "9a19e05b-b26e-4916-ad18-b7073f9ba195", "companyId": "12024615", "type": "payperiod", "_rid": "NmJkAKiCbEfuGAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfuGAAAAAAAAA==/", "_etag": "\"9700bd51-0000-0100-0000-686fd1200000\"", "_attachments": "attachments/", "_ts": 1752158496}, {"payPeriodId": "1030073826130058", "status": "COMPLETED", "description": "client missed hrs one time", "startDate": "2025-05-22T00:00:00Z", "endDate": "2025-05-28T00:00:00Z", "submitByDate": "2025-06-02T00:00:00Z", "checkDate": "2025-06-03T00:00:00Z", "checkCount": 1, "id": "68ab1865-f1bc-4b49-bf65-9c7072cafc0e", "companyId": "12024615", "type": "payperiod", "_rid": "NmJkAKiCbEfvGAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfvGAAAAAAAAA==/", "_etag": "\"9700c051-0000-0100-0000-686fd1200000\"", "_attachments": "attachments/", "_ts": 1752158496}, {"payPeriodId": "1030072021776019", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-05-29T00:00:00Z", "endDate": "2025-06-04T00:00:00Z", "submitByDate": "2025-06-04T00:00:00Z", "checkDate": "2025-06-06T00:00:00Z", "checkCount": 20, "id": "cbdb70d4-f24c-44c2-8e6d-cd80a1688226", "companyId": "12024615", "type": "payperiod", "_rid": "NmJkAKiCbEfwGAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfwGAAAAAAAAA==/", "_etag": "\"9700c351-0000-0100-0000-686fd1200000\"", "_attachments": "attachments/", "_ts": 1752158496}, {"payPeriodId": "1030072130969140", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-06-05T00:00:00Z", "endDate": "2025-06-11T00:00:00Z", "submitByDate": "2025-06-11T00:00:00Z", "checkDate": "2025-06-13T00:00:00Z", "checkCount": 19, "id": "2f3af78f-cd7a-4aef-859b-7767e0872674", "companyId": "12024615", "type": "payperiod", "_rid": "NmJkAKiCbEfxGAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfxGAAAAAAAAA==/", "_etag": "\"9700d151-0000-0100-0000-686fd1200000\"", "_attachments": "attachments/", "_ts": 1752158496}, {"payPeriodId": "1030074109123261", "status": "COMPLETED", "description": "Void", "startDate": "2025-06-05T00:00:00Z", "endDate": "2025-06-11T00:00:00Z", "submitByDate": "2025-06-12T00:00:00Z", "checkDate": "2025-06-13T00:00:00Z", "checkCount": 2, "id": "07c1c8e4-1321-4337-b88d-9a9c2aa26354", "companyId": "12024615", "type": "payperiod", "_rid": "NmJkAKiCbEfyGAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfyGAAAAAAAAA==/", "_etag": "\"9700d451-0000-0100-0000-686fd1200000\"", "_attachments": "attachments/", "_ts": 1752158496}, {"payPeriodId": "1030072277614893", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-06-12T00:00:00Z", "endDate": "2025-06-18T00:00:00Z", "submitByDate": "2025-06-18T00:00:00Z", "checkDate": "2025-06-20T00:00:00Z", "checkCount": 21, "id": "867a8848-6c39-40c2-be00-34203688dedf", "companyId": "12024615", "type": "payperiod", "_rid": "NmJkAKiCbEfzGAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfzGAAAAAAAAA==/", "_etag": "\"9700d751-0000-0100-0000-686fd1200000\"", "_attachments": "attachments/", "_ts": 1752158496}, {"payPeriodId": "1030072398604414", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-06-19T00:00:00Z", "endDate": "2025-06-25T00:00:00Z", "submitByDate": "2025-06-25T00:00:00Z", "checkDate": "2025-06-27T00:00:00Z", "checkCount": 20, "id": "76c3e22f-f090-4af5-90ab-02ee19614668", "companyId": "12024615", "type": "payperiod", "_rid": "NmJkAKiCbEf0GAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf0GAAAAAAAAA==/", "_etag": "\"9700dc51-0000-0100-0000-686fd1200000\"", "_attachments": "attachments/", "_ts": 1752158496}, {"payPeriodId": "1030072550063390", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-06-26T00:00:00Z", "endDate": "2025-07-02T00:00:00Z", "submitByDate": "2025-07-01T00:00:00Z", "checkDate": "2025-07-03T00:00:00Z", "checkCount": 22, "id": "3e57e296-0b0e-4d6c-b2af-06538028dc10", "companyId": "12024615", "type": "payperiod", "_rid": "NmJkAKiCbEf1GAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf1GAAAAAAAAA==/", "_etag": "\"9700e151-0000-0100-0000-686fd1200000\"", "_attachments": "attachments/", "_ts": 1752158496}, {"payPeriodId": "1030072673464903", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-07-03T00:00:00Z", "endDate": "2025-07-09T00:00:00Z", "submitByDate": "2025-07-09T00:00:00Z", "checkDate": "2025-07-11T00:00:00Z", "checkCount": 0, "id": "fa4798bf-940e-4585-bf7a-4fbfcb619885", "companyId": "12024615", "type": "payperiod", "_rid": "NmJkAKiCbEf2GAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf2GAAAAAAAAA==/", "_etag": "\"9700e651-0000-0100-0000-686fd1200000\"", "_attachments": "attachments/", "_ts": 1752158496}, {"payPeriodId": "1030072812346055", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-07-10T00:00:00Z", "endDate": "2025-07-16T00:00:00Z", "submitByDate": "2025-07-16T00:00:00Z", "checkDate": "2025-07-18T00:00:00Z", "checkCount": 0, "id": "3793c217-d796-4782-81f2-8f705ed62a33", "companyId": "12024615", "type": "payperiod", "_rid": "NmJkAKiCbEf3GAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf3GAAAAAAAAA==/", "_etag": "\"9700eb51-0000-0100-0000-686fd1200000\"", "_attachments": "attachments/", "_ts": 1752158496}, {"payPeriodId": "1030072952324244", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-07-17T00:00:00Z", "endDate": "2025-07-23T00:00:00Z", "submitByDate": "2025-07-23T00:00:00Z", "checkDate": "2025-07-25T00:00:00Z", "checkCount": 0, "id": "e2f80246-5dc0-4560-b1ef-c87b4954a1a8", "companyId": "12024615", "type": "payperiod", "_rid": "NmJkAKiCbEf4GAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf4GAAAAAAAAA==/", "_etag": "\"9700ee51-0000-0100-0000-686fd1210000\"", "_attachments": "attachments/", "_ts": 1752158497}, {"payPeriodId": "1030073078992493", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-07-24T00:00:00Z", "endDate": "2025-07-30T00:00:00Z", "submitByDate": "2025-07-30T00:00:00Z", "checkDate": "2025-08-01T00:00:00Z", "checkCount": 0, "id": "0e4a02dd-2d46-4ce6-8fda-d65057c9aa88", "companyId": "12024615", "type": "payperiod", "_rid": "NmJkAKiCbEf5GAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf5GAAAAAAAAA==/", "_etag": "\"9700ef51-0000-0100-0000-686fd1210000\"", "_attachments": "attachments/", "_ts": 1752158497}, {"payPeriodId": "1030073221071764", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-07-31T00:00:00Z", "endDate": "2025-08-06T00:00:00Z", "submitByDate": "2025-08-06T00:00:00Z", "checkDate": "2025-08-08T00:00:00Z", "checkCount": 0, "id": "24f36b2d-5f81-4583-b533-5ce542efb68b", "companyId": "12024615", "type": "payperiod", "_rid": "NmJkAKiCbEf6GAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf6GAAAAAAAAA==/", "_etag": "\"9700f351-0000-0100-0000-686fd1210000\"", "_attachments": "attachments/", "_ts": 1752158497}, {"payPeriodId": "1030073357993284", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-08-07T00:00:00Z", "endDate": "2025-08-13T00:00:00Z", "submitByDate": "2025-08-13T00:00:00Z", "checkDate": "2025-08-15T00:00:00Z", "checkCount": 0, "id": "38ce6a61-f492-420e-b9e4-e330cabdf4fb", "companyId": "12024615", "type": "payperiod", "_rid": "NmJkAKiCbEf7GAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf7GAAAAAAAAA==/", "_etag": "\"9700f551-0000-0100-0000-686fd1210000\"", "_attachments": "attachments/", "_ts": 1752158497}, {"payPeriodId": "1030073498594004", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-08-14T00:00:00Z", "endDate": "2025-08-20T00:00:00Z", "submitByDate": "2025-08-20T00:00:00Z", "checkDate": "2025-08-22T00:00:00Z", "checkCount": 0, "id": "ad36a622-5a62-44f8-81a3-0691954d68d5", "companyId": "12024615", "type": "payperiod", "_rid": "NmJkAKiCbEf8GAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf8GAAAAAAAAA==/", "_etag": "\"9700f951-0000-0100-0000-686fd1210000\"", "_attachments": "attachments/", "_ts": 1752158497}, {"payPeriodId": "1030073648955845", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-08-21T00:00:00Z", "endDate": "2025-08-27T00:00:00Z", "submitByDate": "2025-08-27T00:00:00Z", "checkDate": "2025-08-29T00:00:00Z", "checkCount": 0, "id": "7122651e-2261-4dc2-990b-2959df67832c", "companyId": "12024615", "type": "payperiod", "_rid": "NmJkAKiCbEf9GAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf9GAAAAAAAAA==/", "_etag": "\"9700ff51-0000-0100-0000-686fd1210000\"", "_attachments": "attachments/", "_ts": 1752158497}, {"payPeriodId": "1030073787936347", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-08-28T00:00:00Z", "endDate": "2025-09-03T00:00:00Z", "submitByDate": "2025-09-03T00:00:00Z", "checkDate": "2025-09-05T00:00:00Z", "checkCount": 0, "id": "7355128f-110f-4520-ac81-14ac2e71b23d", "companyId": "12024615", "type": "payperiod", "_rid": "NmJkAKiCbEf+GAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf+GAAAAAAAAA==/", "_etag": "\"97000152-0000-0100-0000-686fd1210000\"", "_attachments": "attachments/", "_ts": 1752158497}, {"payPeriodId": "1030073920010891", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-09-04T00:00:00Z", "endDate": "2025-09-10T00:00:00Z", "submitByDate": "2025-09-10T00:00:00Z", "checkDate": "2025-09-12T00:00:00Z", "checkCount": 0, "id": "aa8ee9dc-1bb6-4d04-8c8c-c8b838e90f42", "companyId": "12024615", "type": "payperiod", "_rid": "NmJkAKiCbEf-GAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf-GAAAAAAAAA==/", "_etag": "\"97000752-0000-0100-0000-686fd1210000\"", "_attachments": "attachments/", "_ts": 1752158497}, {"payPeriodId": "1030074060588213", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-09-11T00:00:00Z", "endDate": "2025-09-17T00:00:00Z", "submitByDate": "2025-09-17T00:00:00Z", "checkDate": "2025-09-19T00:00:00Z", "checkCount": 0, "id": "3fb033f6-26d7-4c13-b2f9-ed4f99d3cfdc", "companyId": "12024615", "type": "payperiod", "_rid": "NmJkAKiCbEcAGQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcAGQAAAAAAAA==/", "_etag": "\"97000d52-0000-0100-0000-686fd1210000\"", "_attachments": "attachments/", "_ts": 1752158497}, {"payPeriodId": "1030074190596992", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-09-18T00:00:00Z", "endDate": "2025-09-24T00:00:00Z", "submitByDate": "2025-09-24T00:00:00Z", "checkDate": "2025-09-26T00:00:00Z", "checkCount": 0, "id": "e9ebe508-a7b5-4f63-9116-9267d070962e", "companyId": "12024615", "type": "payperiod", "_rid": "NmJkAKiCbEcBGQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcBGQAAAAAAAA==/", "_etag": "\"97001052-0000-0100-0000-686fd1210000\"", "_attachments": "attachments/", "_ts": 1752158497}, {"payPeriodId": "1030074347462799", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-09-25T00:00:00Z", "endDate": "2025-10-01T00:00:00Z", "submitByDate": "2025-10-01T00:00:00Z", "checkDate": "2025-10-03T00:00:00Z", "checkCount": 0, "id": "56a96e20-5376-469a-ad73-dc32eaf419fd", "companyId": "12024615", "type": "payperiod", "_rid": "NmJkAKiCbEcCGQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcCGQAAAAAAAA==/", "_etag": "\"97001352-0000-0100-0000-686fd1210000\"", "_attachments": "attachments/", "_ts": 1752158497}, {"payPeriodId": "1030074467290270", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-10-02T00:00:00Z", "endDate": "2025-10-08T00:00:00Z", "submitByDate": "2025-10-08T00:00:00Z", "checkDate": "2025-10-10T00:00:00Z", "checkCount": 0, "id": "2f962100-b336-4b92-97fd-1b8c32c8ae7d", "companyId": "12024615", "type": "payperiod", "_rid": "NmJkAKiCbEcDGQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcDGQAAAAAAAA==/", "_etag": "\"97001552-0000-0100-0000-686fd1210000\"", "_attachments": "attachments/", "_ts": 1752158497}, {"payPeriodId": "1030069513553401", "intervalCode": "WEEKLY", "status": "REVERSED", "description": "Weekly Payroll (2)", "startDate": "2025-01-23T00:00:00Z", "endDate": "2025-01-29T00:00:00Z", "submitByDate": "2025-01-29T00:00:00Z", "checkDate": "2025-01-31T00:00:00Z", "checkCount": 2, "id": "e4782397-cf96-4233-8c83-7ac722e10acf", "companyId": "12024615", "type": "payperiod", "_rid": "NmJkAKiCbEcEGQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcEGQAAAAAAAA==/", "_etag": "\"97001952-0000-0100-0000-686fd1210000\"", "_attachments": "attachments/", "_ts": 1752158497}, {"payPeriodId": "1030068963531201", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2024-12-26T00:00:00Z", "endDate": "2025-01-01T00:00:00Z", "submitByDate": "2024-12-31T00:00:00Z", "checkDate": "2025-01-03T00:00:00Z", "checkCount": 5, "id": "0a8f6a1f-6f27-4425-a447-e247a6525f3c", "companyId": "12024615", "type": "payperiod", "_rid": "NmJkAKiCbEemEQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEemEQEAAAAAAA==/", "_etag": "\"9d00818f-0000-0100-0000-686ff76a0000\"", "_attachments": "attachments/", "_ts": 1752168298}, {"payPeriodId": "1030069094431703", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-01-02T00:00:00Z", "endDate": "2025-01-08T00:00:00Z", "submitByDate": "2025-01-08T00:00:00Z", "checkDate": "2025-01-10T00:00:00Z", "checkCount": 6, "id": "329159f4-1c06-447a-b809-48a567797c5a", "companyId": "12024615", "type": "payperiod", "_rid": "NmJkAKiCbEenEQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEenEQEAAAAAAA==/", "_etag": "\"9d00848f-0000-0100-0000-686ff76a0000\"", "_attachments": "attachments/", "_ts": 1752168298}, {"payPeriodId": "1030069238265692", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-01-09T00:00:00Z", "endDate": "2025-01-15T00:00:00Z", "submitByDate": "2025-01-15T00:00:00Z", "checkDate": "2025-01-17T00:00:00Z", "checkCount": 8, "id": "395e1c21-b481-4980-8cc8-2bfa732c0a5e", "companyId": "12024615", "type": "payperiod", "_rid": "NmJkAKiCbEeoEQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeoEQEAAAAAAA==/", "_etag": "\"9d00868f-0000-0100-0000-686ff76a0000\"", "_attachments": "attachments/", "_ts": 1752168298}, {"payPeriodId": "1030069373608991", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-01-16T00:00:00Z", "endDate": "2025-01-22T00:00:00Z", "submitByDate": "2025-01-22T00:00:00Z", "checkDate": "2025-01-24T00:00:00Z", "checkCount": 7, "id": "1400b788-4924-47d7-b61e-38bc3b9b0789", "companyId": "12024615", "type": "payperiod", "_rid": "NmJkAKiCbEepEQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEepEQEAAAAAAA==/", "_etag": "\"9d00878f-0000-0100-0000-686ff76a0000\"", "_attachments": "attachments/", "_ts": 1752168298}, {"payPeriodId": "1030071455230638", "status": "COMPLETED", "description": "Reverse", "startDate": "2025-01-23T00:00:00Z", "endDate": "2025-01-29T00:00:00Z", "submitByDate": "2025-01-30T00:00:00Z", "checkDate": "2025-01-31T00:00:00Z", "checkCount": 2, "originalPayPeriodID": "1030069513553401", "id": "b1d719d8-6da8-44f1-a56e-b4c171019e8b", "companyId": "12024615", "type": "payperiod", "_rid": "NmJkAKiCbEeqEQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeqEQEAAAAAAA==/", "_etag": "\"9d008a8f-0000-0100-0000-686ff76a0000\"", "_attachments": "attachments/", "_ts": 1752168298}, {"payPeriodId": "1030071469477360", "status": "COMPLETED", "description": "payroll correction", "startDate": "2025-01-23T00:00:00Z", "endDate": "2025-01-29T00:00:00Z", "submitByDate": "2025-01-30T00:00:00Z", "checkDate": "2025-01-31T00:00:00Z", "checkCount": 5, "id": "0ffff644-f154-46e6-b4b8-af69d33e7190", "companyId": "12024615", "type": "payperiod", "_rid": "NmJkAKiCbEerEQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEerEQEAAAAAAA==/", "_etag": "\"9d00918f-0000-0100-0000-686ff76a0000\"", "_attachments": "attachments/", "_ts": 1752168298}, {"payPeriodId": "1030069651102076", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-01-30T00:00:00Z", "endDate": "2025-02-05T00:00:00Z", "submitByDate": "2025-02-05T00:00:00Z", "checkDate": "2025-02-07T00:00:00Z", "checkCount": 7, "id": "d2fd47f6-2fb8-41bc-b836-cc911ad6698b", "companyId": "12024615", "type": "payperiod", "_rid": "NmJkAKiCbEesEQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEesEQEAAAAAAA==/", "_etag": "\"9d00968f-0000-0100-0000-686ff76a0000\"", "_attachments": "attachments/", "_ts": 1752168298}, {"payPeriodId": "1030069790247167", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-02-06T00:00:00Z", "endDate": "2025-02-12T00:00:00Z", "submitByDate": "2025-02-12T00:00:00Z", "checkDate": "2025-02-14T00:00:00Z", "checkCount": 7, "id": "62a7a3fa-e577-4b64-a80f-60c247b73e4a", "companyId": "12024615", "type": "payperiod", "_rid": "NmJkAKiCbEetEQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEetEQEAAAAAAA==/", "_etag": "\"9d00988f-0000-0100-0000-686ff76a0000\"", "_attachments": "attachments/", "_ts": 1752168298}, {"payPeriodId": "1030069920270090", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-02-13T00:00:00Z", "endDate": "2025-02-19T00:00:00Z", "submitByDate": "2025-02-19T00:00:00Z", "checkDate": "2025-02-21T00:00:00Z", "checkCount": 7, "id": "48cc000d-f5f0-410c-b2fd-71321915e06a", "companyId": "12024615", "type": "payperiod", "_rid": "NmJkAKiCbEeuEQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeuEQEAAAAAAA==/", "_etag": "\"9d00a08f-0000-0100-0000-686ff76a0000\"", "_attachments": "attachments/", "_ts": 1752168298}, {"payPeriodId": "1030070057251611", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-02-20T00:00:00Z", "endDate": "2025-02-26T00:00:00Z", "submitByDate": "2025-02-26T00:00:00Z", "checkDate": "2025-02-28T00:00:00Z", "checkCount": 4, "id": "55a11076-9846-453a-89c7-402a19d53fa2", "companyId": "12024615", "type": "payperiod", "_rid": "NmJkAKiCbEevEQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEevEQEAAAAAAA==/", "_etag": "\"9d00a48f-0000-0100-0000-686ff76a0000\"", "_attachments": "attachments/", "_ts": 1752168298}, {"payPeriodId": "1030070191358480", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-02-27T00:00:00Z", "endDate": "2025-03-05T00:00:00Z", "submitByDate": "2025-03-05T00:00:00Z", "checkDate": "2025-03-07T00:00:00Z", "checkCount": 4, "id": "fa335403-993f-4040-ab9b-520f972745f8", "companyId": "12024615", "type": "payperiod", "_rid": "NmJkAKiCbEewEQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEewEQEAAAAAAA==/", "_etag": "\"9d00a98f-0000-0100-0000-686ff76a0000\"", "_attachments": "attachments/", "_ts": 1752168298}, {"payPeriodId": "1030070356662740", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-03-06T00:00:00Z", "endDate": "2025-03-12T00:00:00Z", "submitByDate": "2025-03-12T00:00:00Z", "checkDate": "2025-03-14T00:00:00Z", "checkCount": 5, "id": "ef2f443f-6ea8-4a55-8c4b-8092020a641a", "companyId": "12024615", "type": "payperiod", "_rid": "NmJkAKiCbEexEQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEexEQEAAAAAAA==/", "_etag": "\"9d00ab8f-0000-0100-0000-686ff76b0000\"", "_attachments": "attachments/", "_ts": 1752168299}, {"payPeriodId": "1030070504948167", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-03-13T00:00:00Z", "endDate": "2025-03-19T00:00:00Z", "submitByDate": "2025-03-19T00:00:00Z", "checkDate": "2025-03-21T00:00:00Z", "checkCount": 6, "id": "07c888f9-7c4e-4794-96b4-684aeee929f4", "companyId": "12024615", "type": "payperiod", "_rid": "NmJkAKiCbEeyEQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeyEQEAAAAAAA==/", "_etag": "\"9d00ae8f-0000-0100-0000-686ff76b0000\"", "_attachments": "attachments/", "_ts": 1752168299}, {"payPeriodId": "1030070656686961", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-03-20T00:00:00Z", "endDate": "2025-03-26T00:00:00Z", "submitByDate": "2025-03-26T00:00:00Z", "checkDate": "2025-03-28T00:00:00Z", "checkCount": 13, "id": "e417059b-fb2d-4a79-bb19-0e34e85b1773", "companyId": "12024615", "type": "payperiod", "_rid": "NmJkAKiCbEezEQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEezEQEAAAAAAA==/", "_etag": "\"9d00b18f-0000-0100-0000-686ff76b0000\"", "_attachments": "attachments/", "_ts": 1752168299}, {"payPeriodId": "1030072567561844", "status": "COMPLETED", "description": "Payroll Correction", "startDate": "2025-03-20T00:00:00Z", "endDate": "2025-03-26T00:00:00Z", "submitByDate": "2025-03-27T00:00:00Z", "checkDate": "2025-03-28T00:00:00Z", "checkCount": 1, "id": "e1150973-f344-4d2f-982e-7a4e0065a400", "companyId": "12024615", "type": "payperiod", "_rid": "NmJkAKiCbEe0EQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEe0EQEAAAAAAA==/", "_etag": "\"9d00b38f-0000-0100-0000-686ff76b0000\"", "_attachments": "attachments/", "_ts": 1752168299}, {"payPeriodId": "1030070772731858", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-03-27T00:00:00Z", "endDate": "2025-04-02T00:00:00Z", "submitByDate": "2025-04-02T00:00:00Z", "checkDate": "2025-04-04T00:00:00Z", "checkCount": 0, "id": "e438dc4b-e64f-4a7f-b8ca-9e29ead29c65", "companyId": "12024615", "type": "payperiod", "_rid": "NmJkAKiCbEe1EQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEe1EQEAAAAAAA==/", "_etag": "\"9d00b68f-0000-0100-0000-686ff76b0000\"", "_attachments": "attachments/", "_ts": 1752168299}, {"payPeriodId": "1030070926322281", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-04-03T00:00:00Z", "endDate": "2025-04-09T00:00:00Z", "submitByDate": "2025-04-09T00:00:00Z", "checkDate": "2025-04-11T00:00:00Z", "checkCount": 0, "id": "5cf381c1-b0f4-4aef-968c-a934926144f8", "companyId": "12024615", "type": "payperiod", "_rid": "NmJkAKiCbEe2EQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEe2EQEAAAAAAA==/", "_etag": "\"9d00b88f-0000-0100-0000-686ff76b0000\"", "_attachments": "attachments/", "_ts": 1752168299}, {"payPeriodId": "1030072828646893", "status": "INITIAL", "description": "Missing Pay", "startDate": "2025-04-03T00:00:00Z", "endDate": "2025-04-09T00:00:00Z", "submitByDate": "2025-04-10T00:00:00Z", "checkDate": "2025-04-11T00:00:00Z", "checkCount": 0, "id": "c6a0f946-210a-4457-af22-6cf4e360ab92", "companyId": "12024615", "type": "payperiod", "_rid": "NmJkAKiCbEe3EQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEe3EQEAAAAAAA==/", "_etag": "\"9d00ba8f-0000-0100-0000-686ff76b0000\"", "_attachments": "attachments/", "_ts": 1752168299}, {"payPeriodId": "1030071061567267", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-04-10T00:00:00Z", "endDate": "2025-04-16T00:00:00Z", "submitByDate": "2025-04-16T00:00:00Z", "checkDate": "2025-04-18T00:00:00Z", "checkCount": 0, "id": "9e403048-b275-43f6-9675-f09473be7ddc", "companyId": "12024615", "type": "payperiod", "_rid": "NmJkAKiCbEe4EQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEe4EQEAAAAAAA==/", "_etag": "\"9d00c18f-0000-0100-0000-686ff76b0000\"", "_attachments": "attachments/", "_ts": 1752168299}, {"payPeriodId": "1030071209647439", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-04-17T00:00:00Z", "endDate": "2025-04-23T00:00:00Z", "submitByDate": "2025-04-23T00:00:00Z", "checkDate": "2025-04-25T00:00:00Z", "checkCount": 0, "id": "4fadf758-fdd6-40dc-8100-478ae1deedb1", "companyId": "12024615", "type": "payperiod", "_rid": "NmJkAKiCbEe5EQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEe5EQEAAAAAAA==/", "_etag": "\"9d00c68f-0000-0100-0000-686ff76b0000\"", "_attachments": "attachments/", "_ts": 1752168299}, {"payPeriodId": "1030071332922686", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-04-24T00:00:00Z", "endDate": "2025-04-30T00:00:00Z", "submitByDate": "2025-04-30T00:00:00Z", "checkDate": "2025-05-02T00:00:00Z", "checkCount": 0, "id": "31b02e50-e416-4180-8cfb-e1150674141b", "companyId": "12024615", "type": "payperiod", "_rid": "NmJkAKiCbEe6EQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEe6EQEAAAAAAA==/", "_etag": "\"9d00ce8f-0000-0100-0000-686ff76b0000\"", "_attachments": "attachments/", "_ts": 1752168299}, {"payPeriodId": "1030071478190132", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-05-01T00:00:00Z", "endDate": "2025-05-07T00:00:00Z", "submitByDate": "2025-05-07T00:00:00Z", "checkDate": "2025-05-09T00:00:00Z", "checkCount": 0, "id": "cc4bed5e-48de-46d8-b1ab-761dff7f7124", "companyId": "12024615", "type": "payperiod", "_rid": "NmJkAKiCbEe7EQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEe7EQEAAAAAAA==/", "_etag": "\"9d00d28f-0000-0100-0000-686ff76b0000\"", "_attachments": "attachments/", "_ts": 1752168299}, {"payPeriodId": "1030073374164861", "status": "INITIAL", "description": "1 check", "startDate": "2025-05-01T00:00:00Z", "endDate": "2025-05-07T00:00:00Z", "submitByDate": "2025-05-08T00:00:00Z", "checkDate": "2025-05-09T00:00:00Z", "checkCount": 0, "id": "a91d9008-a2b5-4ca6-ad94-ce0457158325", "companyId": "12024615", "type": "payperiod", "_rid": "NmJkAKiCbEe8EQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEe8EQEAAAAAAA==/", "_etag": "\"9d00d48f-0000-0100-0000-686ff76b0000\"", "_attachments": "attachments/", "_ts": 1752168299}, {"payPeriodId": "1030071608642976", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-05-08T00:00:00Z", "endDate": "2025-05-14T00:00:00Z", "submitByDate": "2025-05-14T00:00:00Z", "checkDate": "2025-05-16T00:00:00Z", "checkCount": 0, "id": "7870a982-d27e-44af-a05d-eb02a3e050c1", "companyId": "12024615", "type": "payperiod", "_rid": "NmJkAKiCbEe9EQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEe9EQEAAAAAAA==/", "_etag": "\"9d00d78f-0000-0100-0000-686ff76b0000\"", "_attachments": "attachments/", "_ts": 1752168299}, {"payPeriodId": "1030071752279670", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-05-15T00:00:00Z", "endDate": "2025-05-21T00:00:00Z", "submitByDate": "2025-05-21T00:00:00Z", "checkDate": "2025-05-23T00:00:00Z", "checkCount": 0, "id": "e4b9806f-faa7-4ad4-86c3-12841a264d09", "companyId": "12024615", "type": "payperiod", "_rid": "NmJkAKiCbEe+EQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEe+EQEAAAAAAA==/", "_etag": "\"9d00d98f-0000-0100-0000-686ff76c0000\"", "_attachments": "attachments/", "_ts": 1752168300}, {"payPeriodId": "1030071876827217", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-05-22T00:00:00Z", "endDate": "2025-05-28T00:00:00Z", "submitByDate": "2025-05-28T00:00:00Z", "checkDate": "2025-05-30T00:00:00Z", "checkCount": 0, "id": "de208e99-dcb3-4216-82f1-7e155006205d", "companyId": "12024615", "type": "payperiod", "_rid": "NmJkAKiCbEe-EQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEe-EQEAAAAAAA==/", "_etag": "\"9d00dd8f-0000-0100-0000-686ff76c0000\"", "_attachments": "attachments/", "_ts": 1752168300}, {"payPeriodId": "1030073826130058", "status": "INITIAL", "description": "client missed hrs one time", "startDate": "2025-05-22T00:00:00Z", "endDate": "2025-05-28T00:00:00Z", "submitByDate": "2025-06-02T00:00:00Z", "checkDate": "2025-06-03T00:00:00Z", "checkCount": 0, "id": "17b23155-784a-4a60-b9a4-fe31ea4192fb", "companyId": "12024615", "type": "payperiod", "_rid": "NmJkAKiCbEfAEQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfAEQEAAAAAAA==/", "_etag": "\"9d00e68f-0000-0100-0000-686ff76c0000\"", "_attachments": "attachments/", "_ts": 1752168300}, {"payPeriodId": "1030072021776019", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-05-29T00:00:00Z", "endDate": "2025-06-04T00:00:00Z", "submitByDate": "2025-06-04T00:00:00Z", "checkDate": "2025-06-06T00:00:00Z", "checkCount": 0, "id": "49c6c5c0-8ea7-4294-8ce1-6d0413a0f78e", "companyId": "12024615", "type": "payperiod", "_rid": "NmJkAKiCbEfBEQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfBEQEAAAAAAA==/", "_etag": "\"9d00e98f-0000-0100-0000-686ff76c0000\"", "_attachments": "attachments/", "_ts": 1752168300}, {"payPeriodId": "1030072130969140", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-06-05T00:00:00Z", "endDate": "2025-06-11T00:00:00Z", "submitByDate": "2025-06-11T00:00:00Z", "checkDate": "2025-06-13T00:00:00Z", "checkCount": 0, "id": "d66e4ad6-effa-41be-95ab-8a8f4dcd743e", "companyId": "12024615", "type": "payperiod", "_rid": "NmJkAKiCbEfCEQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfCEQEAAAAAAA==/", "_etag": "\"9d00ea8f-0000-0100-0000-686ff76c0000\"", "_attachments": "attachments/", "_ts": 1752168300}, {"payPeriodId": "1030074109123261", "status": "INITIAL", "description": "Void", "startDate": "2025-06-05T00:00:00Z", "endDate": "2025-06-11T00:00:00Z", "submitByDate": "2025-06-12T00:00:00Z", "checkDate": "2025-06-13T00:00:00Z", "checkCount": 0, "id": "b636cc8e-e512-46f0-b3dd-4dec49404d0b", "companyId": "12024615", "type": "payperiod", "_rid": "NmJkAKiCbEfDEQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfDEQEAAAAAAA==/", "_etag": "\"9d00ef8f-0000-0100-0000-686ff76c0000\"", "_attachments": "attachments/", "_ts": 1752168300}, {"payPeriodId": "1030072277614893", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-06-12T00:00:00Z", "endDate": "2025-06-18T00:00:00Z", "submitByDate": "2025-06-18T00:00:00Z", "checkDate": "2025-06-20T00:00:00Z", "checkCount": 0, "id": "2bdc0f51-aa7a-4321-8dc7-20d3f9a64ebb", "companyId": "12024615", "type": "payperiod", "_rid": "NmJkAKiCbEfEEQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfEEQEAAAAAAA==/", "_etag": "\"9d00f18f-0000-0100-0000-686ff76c0000\"", "_attachments": "attachments/", "_ts": 1752168300}, {"payPeriodId": "1030072398604414", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-06-19T00:00:00Z", "endDate": "2025-06-25T00:00:00Z", "submitByDate": "2025-06-25T00:00:00Z", "checkDate": "2025-06-27T00:00:00Z", "checkCount": 0, "id": "0e4843dc-3415-47a3-a687-dde0e28c006e", "companyId": "12024615", "type": "payperiod", "_rid": "NmJkAKiCbEfFEQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfFEQEAAAAAAA==/", "_etag": "\"9d00f58f-0000-0100-0000-686ff76c0000\"", "_attachments": "attachments/", "_ts": 1752168300}, {"payPeriodId": "1030072550063390", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-06-26T00:00:00Z", "endDate": "2025-07-02T00:00:00Z", "submitByDate": "2025-07-01T00:00:00Z", "checkDate": "2025-07-03T00:00:00Z", "checkCount": 0, "id": "2ca3e60f-adc2-41e5-8dc9-978f5c6ec973", "companyId": "12024615", "type": "payperiod", "_rid": "NmJkAKiCbEfGEQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfGEQEAAAAAAA==/", "_etag": "\"9d00fa8f-0000-0100-0000-686ff76c0000\"", "_attachments": "attachments/", "_ts": 1752168300}, {"payPeriodId": "1030072673464903", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-07-03T00:00:00Z", "endDate": "2025-07-09T00:00:00Z", "submitByDate": "2025-07-09T00:00:00Z", "checkDate": "2025-07-11T00:00:00Z", "checkCount": 0, "id": "8841a3cc-2357-45ac-86d2-ceccee0ff6b6", "companyId": "12024615", "type": "payperiod", "_rid": "NmJkAKiCbEfHEQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfHEQEAAAAAAA==/", "_etag": "\"9d00fd8f-0000-0100-0000-686ff76c0000\"", "_attachments": "attachments/", "_ts": 1752168300}, {"payPeriodId": "1030072812346055", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-07-10T00:00:00Z", "endDate": "2025-07-16T00:00:00Z", "submitByDate": "2025-07-16T00:00:00Z", "checkDate": "2025-07-18T00:00:00Z", "checkCount": 0, "id": "5426fd37-9715-42cd-90a2-3a129152dc85", "companyId": "12024615", "type": "payperiod", "_rid": "NmJkAKiCbEfIEQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfIEQEAAAAAAA==/", "_etag": "\"9d000090-0000-0100-0000-686ff76c0000\"", "_attachments": "attachments/", "_ts": 1752168300}, {"payPeriodId": "1030072952324244", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-07-17T00:00:00Z", "endDate": "2025-07-23T00:00:00Z", "submitByDate": "2025-07-23T00:00:00Z", "checkDate": "2025-07-25T00:00:00Z", "checkCount": 0, "id": "62c67ec6-9185-4600-83c0-45bbf3a8e70b", "companyId": "12024615", "type": "payperiod", "_rid": "NmJkAKiCbEfJEQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfJEQEAAAAAAA==/", "_etag": "\"9d000390-0000-0100-0000-686ff76c0000\"", "_attachments": "attachments/", "_ts": 1752168300}, {"payPeriodId": "1030073078992493", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-07-24T00:00:00Z", "endDate": "2025-07-30T00:00:00Z", "submitByDate": "2025-07-30T00:00:00Z", "checkDate": "2025-08-01T00:00:00Z", "checkCount": 0, "id": "ebb1d66c-05a6-47a1-b082-afe6a1d44e3a", "companyId": "12024615", "type": "payperiod", "_rid": "NmJkAKiCbEfKEQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfKEQEAAAAAAA==/", "_etag": "\"9d000990-0000-0100-0000-686ff76c0000\"", "_attachments": "attachments/", "_ts": 1752168300}, {"payPeriodId": "1030073221071764", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-07-31T00:00:00Z", "endDate": "2025-08-06T00:00:00Z", "submitByDate": "2025-08-06T00:00:00Z", "checkDate": "2025-08-08T00:00:00Z", "checkCount": 0, "id": "07e957bd-d25b-448c-889a-7946db42d7a7", "companyId": "12024615", "type": "payperiod", "_rid": "NmJkAKiCbEfLEQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfLEQEAAAAAAA==/", "_etag": "\"9d000b90-0000-0100-0000-686ff76d0000\"", "_attachments": "attachments/", "_ts": 1752168301}, {"payPeriodId": "1030073357993284", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-08-07T00:00:00Z", "endDate": "2025-08-13T00:00:00Z", "submitByDate": "2025-08-13T00:00:00Z", "checkDate": "2025-08-15T00:00:00Z", "checkCount": 0, "id": "6d1025bb-498a-4aad-93a3-fad4738dd6c3", "companyId": "12024615", "type": "payperiod", "_rid": "NmJkAKiCbEfMEQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfMEQEAAAAAAA==/", "_etag": "\"9d000d90-0000-0100-0000-686ff76d0000\"", "_attachments": "attachments/", "_ts": 1752168301}, {"payPeriodId": "1030073498594004", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-08-14T00:00:00Z", "endDate": "2025-08-20T00:00:00Z", "submitByDate": "2025-08-20T00:00:00Z", "checkDate": "2025-08-22T00:00:00Z", "checkCount": 0, "id": "d3513d88-c581-43ed-b107-a49f73375518", "companyId": "12024615", "type": "payperiod", "_rid": "NmJkAKiCbEfNEQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfNEQEAAAAAAA==/", "_etag": "\"9d000e90-0000-0100-0000-686ff76d0000\"", "_attachments": "attachments/", "_ts": 1752168301}, {"payPeriodId": "1030073648955845", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-08-21T00:00:00Z", "endDate": "2025-08-27T00:00:00Z", "submitByDate": "2025-08-27T00:00:00Z", "checkDate": "2025-08-29T00:00:00Z", "checkCount": 0, "id": "91087c21-b6e3-45f1-96a4-34554837bb56", "companyId": "12024615", "type": "payperiod", "_rid": "NmJkAKiCbEfOEQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfOEQEAAAAAAA==/", "_etag": "\"9d001090-0000-0100-0000-686ff76d0000\"", "_attachments": "attachments/", "_ts": 1752168301}, {"payPeriodId": "1030073787936347", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-08-28T00:00:00Z", "endDate": "2025-09-03T00:00:00Z", "submitByDate": "2025-09-03T00:00:00Z", "checkDate": "2025-09-05T00:00:00Z", "checkCount": 0, "id": "f3d33692-3c9a-4fb4-96fe-2764682dc115", "companyId": "12024615", "type": "payperiod", "_rid": "NmJkAKiCbEfPEQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfPEQEAAAAAAA==/", "_etag": "\"9d001290-0000-0100-0000-686ff76d0000\"", "_attachments": "attachments/", "_ts": 1752168301}, {"payPeriodId": "1030073920010891", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-09-04T00:00:00Z", "endDate": "2025-09-10T00:00:00Z", "submitByDate": "2025-09-10T00:00:00Z", "checkDate": "2025-09-12T00:00:00Z", "checkCount": 0, "id": "407e5ea0-a62d-4651-b629-17642162a708", "companyId": "12024615", "type": "payperiod", "_rid": "NmJkAKiCbEfQEQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfQEQEAAAAAAA==/", "_etag": "\"9d001390-0000-0100-0000-686ff76d0000\"", "_attachments": "attachments/", "_ts": 1752168301}, {"payPeriodId": "1030074060588213", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-09-11T00:00:00Z", "endDate": "2025-09-17T00:00:00Z", "submitByDate": "2025-09-17T00:00:00Z", "checkDate": "2025-09-19T00:00:00Z", "checkCount": 0, "id": "db5c7a4a-fdf5-44f7-ade6-a153228bd056", "companyId": "12024615", "type": "payperiod", "_rid": "NmJkAKiCbEfREQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfREQEAAAAAAA==/", "_etag": "\"9d001590-0000-0100-0000-686ff76d0000\"", "_attachments": "attachments/", "_ts": 1752168301}, {"payPeriodId": "1030074190596992", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-09-18T00:00:00Z", "endDate": "2025-09-24T00:00:00Z", "submitByDate": "2025-09-24T00:00:00Z", "checkDate": "2025-09-26T00:00:00Z", "checkCount": 0, "id": "cbeb9a69-fcc6-427f-8117-df7b01931ecf", "companyId": "12024615", "type": "payperiod", "_rid": "NmJkAKiCbEfSEQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfSEQEAAAAAAA==/", "_etag": "\"9d001790-0000-0100-0000-686ff76d0000\"", "_attachments": "attachments/", "_ts": 1752168301}, {"payPeriodId": "1030074347462799", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-09-25T00:00:00Z", "endDate": "2025-10-01T00:00:00Z", "submitByDate": "2025-10-01T00:00:00Z", "checkDate": "2025-10-03T00:00:00Z", "checkCount": 0, "id": "3e0fa631-639e-49d1-a77a-839e8b310d92", "companyId": "12024615", "type": "payperiod", "_rid": "NmJkAKiCbEfTEQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfTEQEAAAAAAA==/", "_etag": "\"9d001990-0000-0100-0000-686ff76d0000\"", "_attachments": "attachments/", "_ts": 1752168301}, {"payPeriodId": "1030074467290270", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-10-02T00:00:00Z", "endDate": "2025-10-08T00:00:00Z", "submitByDate": "2025-10-08T00:00:00Z", "checkDate": "2025-10-10T00:00:00Z", "checkCount": 0, "id": "952ddf39-334d-43ec-8e6b-4b618e27d3f2", "companyId": "12024615", "type": "payperiod", "_rid": "NmJkAKiCbEfUEQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfUEQEAAAAAAA==/", "_etag": "\"9d001f90-0000-0100-0000-686ff76d0000\"", "_attachments": "attachments/", "_ts": 1752168301}, {"payPeriodId": "1030069513553401", "intervalCode": "WEEKLY", "status": "REVERSED", "description": "Weekly Payroll (2)", "startDate": "2025-01-23T00:00:00Z", "endDate": "2025-01-29T00:00:00Z", "submitByDate": "2025-01-29T00:00:00Z", "checkDate": "2025-01-31T00:00:00Z", "checkCount": 2, "id": "98920bca-883f-4832-9159-ef9fb54297d0", "companyId": "12024615", "type": "payperiod", "_rid": "NmJkAKiCbEfVEQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfVEQEAAAAAAA==/", "_etag": "\"9d002190-0000-0100-0000-686ff76d0000\"", "_attachments": "attachments/", "_ts": 1752168301}, {"payPeriodId": "1030068963531201", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2024-12-26T00:00:00Z", "endDate": "2025-01-01T00:00:00Z", "submitByDate": "2024-12-31T00:00:00Z", "checkDate": "2025-01-03T00:00:00Z", "checkCount": 5, "id": "c96b7cfe-99d9-4d20-accf-4a39db73b524", "companyId": "12024615", "type": "payperiod", "_rid": "NmJkAKiCbEcCEgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcCEgEAAAAAAA==/", "_etag": "\"9d00b390-0000-0100-0000-686ff7710000\"", "_attachments": "attachments/", "_ts": 1752168305}, {"payPeriodId": "1030069094431703", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-01-02T00:00:00Z", "endDate": "2025-01-08T00:00:00Z", "submitByDate": "2025-01-08T00:00:00Z", "checkDate": "2025-01-10T00:00:00Z", "checkCount": 6, "id": "82fc22d5-9951-4af0-908d-584352be03ff", "companyId": "12024615", "type": "payperiod", "_rid": "NmJkAKiCbEcDEgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcDEgEAAAAAAA==/", "_etag": "\"9d00b690-0000-0100-0000-686ff7710000\"", "_attachments": "attachments/", "_ts": 1752168305}, {"payPeriodId": "1030069238265692", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-01-09T00:00:00Z", "endDate": "2025-01-15T00:00:00Z", "submitByDate": "2025-01-15T00:00:00Z", "checkDate": "2025-01-17T00:00:00Z", "checkCount": 8, "id": "95ee3eac-2cdd-4648-88ea-aa4c294df47e", "companyId": "12024615", "type": "payperiod", "_rid": "NmJkAKiCbEcEEgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcEEgEAAAAAAA==/", "_etag": "\"9d00b890-0000-0100-0000-686ff7710000\"", "_attachments": "attachments/", "_ts": 1752168305}, {"payPeriodId": "1030069373608991", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-01-16T00:00:00Z", "endDate": "2025-01-22T00:00:00Z", "submitByDate": "2025-01-22T00:00:00Z", "checkDate": "2025-01-24T00:00:00Z", "checkCount": 7, "id": "53ba6377-e9e9-4676-9a9c-a0e908202914", "companyId": "12024615", "type": "payperiod", "_rid": "NmJkAKiCbEcFEgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcFEgEAAAAAAA==/", "_etag": "\"9d00bb90-0000-0100-0000-686ff7720000\"", "_attachments": "attachments/", "_ts": 1752168306}, {"payPeriodId": "1030071455230638", "status": "COMPLETED", "description": "Reverse", "startDate": "2025-01-23T00:00:00Z", "endDate": "2025-01-29T00:00:00Z", "submitByDate": "2025-01-30T00:00:00Z", "checkDate": "2025-01-31T00:00:00Z", "checkCount": 2, "originalPayPeriodID": "1030069513553401", "id": "b3deeb51-6836-43f9-b20c-07bf07e89458", "companyId": "12024615", "type": "payperiod", "_rid": "NmJkAKiCbEcGEgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcGEgEAAAAAAA==/", "_etag": "\"9d00bd90-0000-0100-0000-686ff7720000\"", "_attachments": "attachments/", "_ts": 1752168306}, {"payPeriodId": "1030071469477360", "status": "COMPLETED", "description": "payroll correction", "startDate": "2025-01-23T00:00:00Z", "endDate": "2025-01-29T00:00:00Z", "submitByDate": "2025-01-30T00:00:00Z", "checkDate": "2025-01-31T00:00:00Z", "checkCount": 5, "id": "a42c4888-9d2a-4818-88c0-69b96ba39912", "companyId": "12024615", "type": "payperiod", "_rid": "NmJkAKiCbEcHEgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcHEgEAAAAAAA==/", "_etag": "\"9d00bf90-0000-0100-0000-686ff7720000\"", "_attachments": "attachments/", "_ts": 1752168306}, {"payPeriodId": "1030069651102076", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-01-30T00:00:00Z", "endDate": "2025-02-05T00:00:00Z", "submitByDate": "2025-02-05T00:00:00Z", "checkDate": "2025-02-07T00:00:00Z", "checkCount": 7, "id": "09351a81-4498-440f-99d3-5c92a1074f38", "companyId": "12024615", "type": "payperiod", "_rid": "NmJkAKiCbEcIEgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcIEgEAAAAAAA==/", "_etag": "\"9d00c290-0000-0100-0000-686ff7720000\"", "_attachments": "attachments/", "_ts": 1752168306}, {"payPeriodId": "1030069790247167", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-02-06T00:00:00Z", "endDate": "2025-02-12T00:00:00Z", "submitByDate": "2025-02-12T00:00:00Z", "checkDate": "2025-02-14T00:00:00Z", "checkCount": 7, "id": "e2b4426a-6a66-46e6-9ca5-36cd3827a1f2", "companyId": "12024615", "type": "payperiod", "_rid": "NmJkAKiCbEcJEgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcJEgEAAAAAAA==/", "_etag": "\"9d00c690-0000-0100-0000-686ff7720000\"", "_attachments": "attachments/", "_ts": 1752168306}, {"payPeriodId": "1030069920270090", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-02-13T00:00:00Z", "endDate": "2025-02-19T00:00:00Z", "submitByDate": "2025-02-19T00:00:00Z", "checkDate": "2025-02-21T00:00:00Z", "checkCount": 7, "id": "24e310b6-2ba4-4235-8789-04eb8d472ca5", "companyId": "12024615", "type": "payperiod", "_rid": "NmJkAKiCbEcKEgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcKEgEAAAAAAA==/", "_etag": "\"9d00c990-0000-0100-0000-686ff7720000\"", "_attachments": "attachments/", "_ts": 1752168306}, {"payPeriodId": "1030070057251611", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-02-20T00:00:00Z", "endDate": "2025-02-26T00:00:00Z", "submitByDate": "2025-02-26T00:00:00Z", "checkDate": "2025-02-28T00:00:00Z", "checkCount": 4, "id": "07e5962c-e1bf-4f68-96a4-c18bfcf1e0a4", "companyId": "12024615", "type": "payperiod", "_rid": "NmJkAKiCbEcLEgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcLEgEAAAAAAA==/", "_etag": "\"9d00cb90-0000-0100-0000-686ff7720000\"", "_attachments": "attachments/", "_ts": 1752168306}, {"payPeriodId": "1030070191358480", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-02-27T00:00:00Z", "endDate": "2025-03-05T00:00:00Z", "submitByDate": "2025-03-05T00:00:00Z", "checkDate": "2025-03-07T00:00:00Z", "checkCount": 4, "id": "039c0a73-8e67-4995-b9e4-91dfdcad100b", "companyId": "12024615", "type": "payperiod", "_rid": "NmJkAKiCbEcMEgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcMEgEAAAAAAA==/", "_etag": "\"9d00cd90-0000-0100-0000-686ff7720000\"", "_attachments": "attachments/", "_ts": 1752168306}, {"payPeriodId": "1030070356662740", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-03-06T00:00:00Z", "endDate": "2025-03-12T00:00:00Z", "submitByDate": "2025-03-12T00:00:00Z", "checkDate": "2025-03-14T00:00:00Z", "checkCount": 5, "id": "c127ba73-d4fc-44c6-a7c2-f8af60fc8281", "companyId": "12024615", "type": "payperiod", "_rid": "NmJkAKiCbEcNEgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcNEgEAAAAAAA==/", "_etag": "\"9d00ce90-0000-0100-0000-686ff7720000\"", "_attachments": "attachments/", "_ts": 1752168306}, {"payPeriodId": "1030070504948167", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-03-13T00:00:00Z", "endDate": "2025-03-19T00:00:00Z", "submitByDate": "2025-03-19T00:00:00Z", "checkDate": "2025-03-21T00:00:00Z", "checkCount": 6, "id": "7140f6b6-63aa-4363-a2fb-ec1d22647325", "companyId": "12024615", "type": "payperiod", "_rid": "NmJkAKiCbEcOEgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcOEgEAAAAAAA==/", "_etag": "\"9d00d090-0000-0100-0000-686ff7720000\"", "_attachments": "attachments/", "_ts": 1752168306}, {"payPeriodId": "1030070656686961", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-03-20T00:00:00Z", "endDate": "2025-03-26T00:00:00Z", "submitByDate": "2025-03-26T00:00:00Z", "checkDate": "2025-03-28T00:00:00Z", "checkCount": 13, "id": "236d77e8-ca9d-4e07-9c62-c8231e64ec34", "companyId": "12024615", "type": "payperiod", "_rid": "NmJkAKiCbEcPEgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcPEgEAAAAAAA==/", "_etag": "\"9d00d590-0000-0100-0000-686ff7720000\"", "_attachments": "attachments/", "_ts": 1752168306}, {"payPeriodId": "1030072567561844", "status": "COMPLETED", "description": "Payroll Correction", "startDate": "2025-03-20T00:00:00Z", "endDate": "2025-03-26T00:00:00Z", "submitByDate": "2025-03-27T00:00:00Z", "checkDate": "2025-03-28T00:00:00Z", "checkCount": 1, "id": "1c9c616c-0559-4eda-bcee-ca810565c8f7", "companyId": "12024615", "type": "payperiod", "_rid": "NmJkAKiCbEcQEgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcQEgEAAAAAAA==/", "_etag": "\"9d00d990-0000-0100-0000-686ff7720000\"", "_attachments": "attachments/", "_ts": 1752168306}, {"payPeriodId": "1030070772731858", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-03-27T00:00:00Z", "endDate": "2025-04-02T00:00:00Z", "submitByDate": "2025-04-02T00:00:00Z", "checkDate": "2025-04-04T00:00:00Z", "checkCount": 17, "id": "bf8686cc-25ae-419b-8f72-f3ce0371f138", "companyId": "12024615", "type": "payperiod", "_rid": "NmJkAKiCbEcREgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcREgEAAAAAAA==/", "_etag": "\"9d00dc90-0000-0100-0000-686ff7720000\"", "_attachments": "attachments/", "_ts": 1752168306}, {"payPeriodId": "1030070926322281", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-04-03T00:00:00Z", "endDate": "2025-04-09T00:00:00Z", "submitByDate": "2025-04-09T00:00:00Z", "checkDate": "2025-04-11T00:00:00Z", "checkCount": 17, "id": "3211bd09-7b1b-4e4e-a3b8-237fc93f3503", "companyId": "12024615", "type": "payperiod", "_rid": "NmJkAKiCbEcSEgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcSEgEAAAAAAA==/", "_etag": "\"9d00de90-0000-0100-0000-686ff7730000\"", "_attachments": "attachments/", "_ts": 1752168307}, {"payPeriodId": "1030072828646893", "status": "COMPLETED", "description": "Missing Pay", "startDate": "2025-04-03T00:00:00Z", "endDate": "2025-04-09T00:00:00Z", "submitByDate": "2025-04-10T00:00:00Z", "checkDate": "2025-04-11T00:00:00Z", "checkCount": 1, "id": "6ff36153-ae3d-4259-ab6e-bb27bed122a6", "companyId": "12024615", "type": "payperiod", "_rid": "NmJkAKiCbEcTEgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcTEgEAAAAAAA==/", "_etag": "\"9d00e490-0000-0100-0000-686ff7730000\"", "_attachments": "attachments/", "_ts": 1752168307}, {"payPeriodId": "1030071061567267", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-04-10T00:00:00Z", "endDate": "2025-04-16T00:00:00Z", "submitByDate": "2025-04-16T00:00:00Z", "checkDate": "2025-04-18T00:00:00Z", "checkCount": 20, "id": "2fab30e6-52d6-4b2c-aa2b-88739b90d039", "companyId": "12024615", "type": "payperiod", "_rid": "NmJkAKiCbEcUEgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcUEgEAAAAAAA==/", "_etag": "\"9d00e790-0000-0100-0000-686ff7730000\"", "_attachments": "attachments/", "_ts": 1752168307}, {"payPeriodId": "1030071209647439", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-04-17T00:00:00Z", "endDate": "2025-04-23T00:00:00Z", "submitByDate": "2025-04-23T00:00:00Z", "checkDate": "2025-04-25T00:00:00Z", "checkCount": 19, "id": "dc51a772-104b-448d-9c9f-15a6ac0a0795", "companyId": "12024615", "type": "payperiod", "_rid": "NmJkAKiCbEcVEgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcVEgEAAAAAAA==/", "_etag": "\"9d00eb90-0000-0100-0000-686ff7730000\"", "_attachments": "attachments/", "_ts": 1752168307}, {"payPeriodId": "1030071332922686", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-04-24T00:00:00Z", "endDate": "2025-04-30T00:00:00Z", "submitByDate": "2025-04-30T00:00:00Z", "checkDate": "2025-05-02T00:00:00Z", "checkCount": 20, "id": "752a5aba-27e8-4d8f-a290-8aec663e5001", "companyId": "12024615", "type": "payperiod", "_rid": "NmJkAKiCbEcWEgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcWEgEAAAAAAA==/", "_etag": "\"9d00f190-0000-0100-0000-686ff7730000\"", "_attachments": "attachments/", "_ts": 1752168307}, {"payPeriodId": "1030071478190132", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-05-01T00:00:00Z", "endDate": "2025-05-07T00:00:00Z", "submitByDate": "2025-05-07T00:00:00Z", "checkDate": "2025-05-09T00:00:00Z", "checkCount": 19, "id": "e0d7476e-e881-4faf-8b40-89c39f3ba42c", "companyId": "12024615", "type": "payperiod", "_rid": "NmJkAKiCbEcXEgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcXEgEAAAAAAA==/", "_etag": "\"9d00f490-0000-0100-0000-686ff7730000\"", "_attachments": "attachments/", "_ts": 1752168307}, {"payPeriodId": "1030073374164861", "status": "COMPLETED", "description": "1 check", "startDate": "2025-05-01T00:00:00Z", "endDate": "2025-05-07T00:00:00Z", "submitByDate": "2025-05-08T00:00:00Z", "checkDate": "2025-05-09T00:00:00Z", "checkCount": 1, "id": "eb986a20-11eb-4fc7-af2e-7c08f36736d0", "companyId": "12024615", "type": "payperiod", "_rid": "NmJkAKiCbEcYEgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcYEgEAAAAAAA==/", "_etag": "\"9d00f590-0000-0100-0000-686ff7730000\"", "_attachments": "attachments/", "_ts": 1752168307}, {"payPeriodId": "1030071608642976", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-05-08T00:00:00Z", "endDate": "2025-05-14T00:00:00Z", "submitByDate": "2025-05-14T00:00:00Z", "checkDate": "2025-05-16T00:00:00Z", "checkCount": 20, "id": "79c452b4-60f0-4bbd-bb45-d141f88bde90", "companyId": "12024615", "type": "payperiod", "_rid": "NmJkAKiCbEcZEgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcZEgEAAAAAAA==/", "_etag": "\"9d00f890-0000-0100-0000-686ff7730000\"", "_attachments": "attachments/", "_ts": 1752168307}, {"payPeriodId": "1030071752279670", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-05-15T00:00:00Z", "endDate": "2025-05-21T00:00:00Z", "submitByDate": "2025-05-21T00:00:00Z", "checkDate": "2025-05-23T00:00:00Z", "checkCount": 20, "id": "eaedfdec-7ea0-48ac-8bd3-7ced666641d5", "companyId": "12024615", "type": "payperiod", "_rid": "NmJkAKiCbEcaEgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcaEgEAAAAAAA==/", "_etag": "\"9d00f990-0000-0100-0000-686ff7730000\"", "_attachments": "attachments/", "_ts": 1752168307}, {"payPeriodId": "1030071876827217", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-05-22T00:00:00Z", "endDate": "2025-05-28T00:00:00Z", "submitByDate": "2025-05-28T00:00:00Z", "checkDate": "2025-05-30T00:00:00Z", "checkCount": 18, "id": "6dcd5859-6671-4d38-9f01-f6c2c8665c0b", "companyId": "12024615", "type": "payperiod", "_rid": "NmJkAKiCbEcbEgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcbEgEAAAAAAA==/", "_etag": "\"9d00fa90-0000-0100-0000-686ff7730000\"", "_attachments": "attachments/", "_ts": 1752168307}, {"payPeriodId": "1030073826130058", "status": "COMPLETED", "description": "client missed hrs one time", "startDate": "2025-05-22T00:00:00Z", "endDate": "2025-05-28T00:00:00Z", "submitByDate": "2025-06-02T00:00:00Z", "checkDate": "2025-06-03T00:00:00Z", "checkCount": 1, "id": "001a79e3-3cfd-4f4d-8bc3-2d65d068f8db", "companyId": "12024615", "type": "payperiod", "_rid": "NmJkAKiCbEccEgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEccEgEAAAAAAA==/", "_etag": "\"9d00fc90-0000-0100-0000-686ff7730000\"", "_attachments": "attachments/", "_ts": 1752168307}, {"payPeriodId": "1030072021776019", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-05-29T00:00:00Z", "endDate": "2025-06-04T00:00:00Z", "submitByDate": "2025-06-04T00:00:00Z", "checkDate": "2025-06-06T00:00:00Z", "checkCount": 20, "id": "b13281b2-8b89-4499-a41d-e46fb8faa4fa", "companyId": "12024615", "type": "payperiod", "_rid": "NmJkAKiCbEcdEgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcdEgEAAAAAAA==/", "_etag": "\"9d00fe90-0000-0100-0000-686ff7730000\"", "_attachments": "attachments/", "_ts": 1752168307}, {"payPeriodId": "1030072130969140", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-06-05T00:00:00Z", "endDate": "2025-06-11T00:00:00Z", "submitByDate": "2025-06-11T00:00:00Z", "checkDate": "2025-06-13T00:00:00Z", "checkCount": 19, "id": "eaa34809-23d8-435f-a69f-c6e371c1fcf0", "companyId": "12024615", "type": "payperiod", "_rid": "NmJkAKiCbEceEgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEceEgEAAAAAAA==/", "_etag": "\"9d000091-0000-0100-0000-686ff7730000\"", "_attachments": "attachments/", "_ts": 1752168307}, {"payPeriodId": "1030074109123261", "status": "COMPLETED", "description": "Void", "startDate": "2025-06-05T00:00:00Z", "endDate": "2025-06-11T00:00:00Z", "submitByDate": "2025-06-12T00:00:00Z", "checkDate": "2025-06-13T00:00:00Z", "checkCount": 2, "id": "c9a95601-f561-48f3-b94e-3adb8b2f45d0", "companyId": "12024615", "type": "payperiod", "_rid": "NmJkAKiCbEcfEgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcfEgEAAAAAAA==/", "_etag": "\"9d000491-0000-0100-0000-686ff7740000\"", "_attachments": "attachments/", "_ts": 1752168308}, {"payPeriodId": "1030072277614893", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-06-12T00:00:00Z", "endDate": "2025-06-18T00:00:00Z", "submitByDate": "2025-06-18T00:00:00Z", "checkDate": "2025-06-20T00:00:00Z", "checkCount": 21, "id": "fbe2e941-16b9-418d-9723-5ccac0284b26", "companyId": "12024615", "type": "payperiod", "_rid": "NmJkAKiCbEcgEgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcgEgEAAAAAAA==/", "_etag": "\"9d000891-0000-0100-0000-686ff7740000\"", "_attachments": "attachments/", "_ts": 1752168308}, {"payPeriodId": "1030072398604414", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-06-19T00:00:00Z", "endDate": "2025-06-25T00:00:00Z", "submitByDate": "2025-06-25T00:00:00Z", "checkDate": "2025-06-27T00:00:00Z", "checkCount": 20, "id": "e04db200-1227-411c-bebe-90e1400c7943", "companyId": "12024615", "type": "payperiod", "_rid": "NmJkAKiCbEchEgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEchEgEAAAAAAA==/", "_etag": "\"9d000a91-0000-0100-0000-686ff7740000\"", "_attachments": "attachments/", "_ts": 1752168308}, {"payPeriodId": "1030072550063390", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-06-26T00:00:00Z", "endDate": "2025-07-02T00:00:00Z", "submitByDate": "2025-07-01T00:00:00Z", "checkDate": "2025-07-03T00:00:00Z", "checkCount": 22, "id": "53068f1e-5098-457e-aaa5-0edfdefbf5ec", "companyId": "12024615", "type": "payperiod", "_rid": "NmJkAKiCbEciEgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEciEgEAAAAAAA==/", "_etag": "\"9d000d91-0000-0100-0000-686ff7740000\"", "_attachments": "attachments/", "_ts": 1752168308}, {"payPeriodId": "1030072673464903", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-07-03T00:00:00Z", "endDate": "2025-07-09T00:00:00Z", "submitByDate": "2025-07-09T00:00:00Z", "checkDate": "2025-07-11T00:00:00Z", "checkCount": 0, "id": "f5def498-753e-4885-b9cb-70bbdbf6a956", "companyId": "12024615", "type": "payperiod", "_rid": "NmJkAKiCbEcjEgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcjEgEAAAAAAA==/", "_etag": "\"9d001191-0000-0100-0000-686ff7740000\"", "_attachments": "attachments/", "_ts": 1752168308}, {"payPeriodId": "1030072812346055", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-07-10T00:00:00Z", "endDate": "2025-07-16T00:00:00Z", "submitByDate": "2025-07-16T00:00:00Z", "checkDate": "2025-07-18T00:00:00Z", "checkCount": 0, "id": "f6b4f192-00c8-44df-b25c-7e36f8dae68c", "companyId": "12024615", "type": "payperiod", "_rid": "NmJkAKiCbEckEgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEckEgEAAAAAAA==/", "_etag": "\"9d001291-0000-0100-0000-686ff7740000\"", "_attachments": "attachments/", "_ts": 1752168308}, {"payPeriodId": "1030072952324244", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-07-17T00:00:00Z", "endDate": "2025-07-23T00:00:00Z", "submitByDate": "2025-07-23T00:00:00Z", "checkDate": "2025-07-25T00:00:00Z", "checkCount": 0, "id": "1315d90e-bc6b-4ed6-91aa-130abc718ced", "companyId": "12024615", "type": "payperiod", "_rid": "NmJkAKiCbEclEgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEclEgEAAAAAAA==/", "_etag": "\"9d001491-0000-0100-0000-686ff7740000\"", "_attachments": "attachments/", "_ts": 1752168308}, {"payPeriodId": "1030073078992493", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-07-24T00:00:00Z", "endDate": "2025-07-30T00:00:00Z", "submitByDate": "2025-07-30T00:00:00Z", "checkDate": "2025-08-01T00:00:00Z", "checkCount": 0, "id": "c162cfca-1725-4d91-b30a-a7cb0d28daf0", "companyId": "12024615", "type": "payperiod", "_rid": "NmJkAKiCbEcmEgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcmEgEAAAAAAA==/", "_etag": "\"9d001591-0000-0100-0000-686ff7740000\"", "_attachments": "attachments/", "_ts": 1752168308}, {"payPeriodId": "1030073221071764", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-07-31T00:00:00Z", "endDate": "2025-08-06T00:00:00Z", "submitByDate": "2025-08-06T00:00:00Z", "checkDate": "2025-08-08T00:00:00Z", "checkCount": 0, "id": "948aa13f-fd88-4ee3-a359-52a99908e6d9", "companyId": "12024615", "type": "payperiod", "_rid": "NmJkAKiCbEcnEgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcnEgEAAAAAAA==/", "_etag": "\"9d001c91-0000-0100-0000-686ff7740000\"", "_attachments": "attachments/", "_ts": 1752168308}, {"payPeriodId": "1030073357993284", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-08-07T00:00:00Z", "endDate": "2025-08-13T00:00:00Z", "submitByDate": "2025-08-13T00:00:00Z", "checkDate": "2025-08-15T00:00:00Z", "checkCount": 0, "id": "014e6fe5-e5cf-4560-ad48-7b99df455d35", "companyId": "12024615", "type": "payperiod", "_rid": "NmJkAKiCbEcoEgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcoEgEAAAAAAA==/", "_etag": "\"9d001d91-0000-0100-0000-686ff7740000\"", "_attachments": "attachments/", "_ts": 1752168308}, {"payPeriodId": "1030073498594004", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-08-14T00:00:00Z", "endDate": "2025-08-20T00:00:00Z", "submitByDate": "2025-08-20T00:00:00Z", "checkDate": "2025-08-22T00:00:00Z", "checkCount": 0, "id": "04f81cc6-814a-40f7-ba68-1aa043346b46", "companyId": "12024615", "type": "payperiod", "_rid": "NmJkAKiCbEcpEgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcpEgEAAAAAAA==/", "_etag": "\"9d002091-0000-0100-0000-686ff7740000\"", "_attachments": "attachments/", "_ts": 1752168308}, {"payPeriodId": "1030073648955845", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-08-21T00:00:00Z", "endDate": "2025-08-27T00:00:00Z", "submitByDate": "2025-08-27T00:00:00Z", "checkDate": "2025-08-29T00:00:00Z", "checkCount": 0, "id": "5904def0-c843-4ae4-bc0f-9885b116bff3", "companyId": "12024615", "type": "payperiod", "_rid": "NmJkAKiCbEcqEgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcqEgEAAAAAAA==/", "_etag": "\"9d002991-0000-0100-0000-686ff7740000\"", "_attachments": "attachments/", "_ts": 1752168308}, {"payPeriodId": "1030073787936347", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-08-28T00:00:00Z", "endDate": "2025-09-03T00:00:00Z", "submitByDate": "2025-09-03T00:00:00Z", "checkDate": "2025-09-05T00:00:00Z", "checkCount": 0, "id": "9205069c-a9b2-4cc1-a025-39f4a87b33a1", "companyId": "12024615", "type": "payperiod", "_rid": "NmJkAKiCbEcrEgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcrEgEAAAAAAA==/", "_etag": "\"9d002b91-0000-0100-0000-686ff7740000\"", "_attachments": "attachments/", "_ts": 1752168308}, {"payPeriodId": "1030073920010891", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-09-04T00:00:00Z", "endDate": "2025-09-10T00:00:00Z", "submitByDate": "2025-09-10T00:00:00Z", "checkDate": "2025-09-12T00:00:00Z", "checkCount": 0, "id": "86440e4f-2275-489d-8a43-96944e8c8437", "companyId": "12024615", "type": "payperiod", "_rid": "NmJkAKiCbEcsEgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcsEgEAAAAAAA==/", "_etag": "\"9d002c91-0000-0100-0000-686ff7750000\"", "_attachments": "attachments/", "_ts": 1752168309}, {"payPeriodId": "1030074060588213", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-09-11T00:00:00Z", "endDate": "2025-09-17T00:00:00Z", "submitByDate": "2025-09-17T00:00:00Z", "checkDate": "2025-09-19T00:00:00Z", "checkCount": 0, "id": "bac8ef30-456f-4977-8325-def183278a5c", "companyId": "12024615", "type": "payperiod", "_rid": "NmJkAKiCbEctEgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEctEgEAAAAAAA==/", "_etag": "\"9d002f91-0000-0100-0000-686ff7750000\"", "_attachments": "attachments/", "_ts": 1752168309}, {"payPeriodId": "1030074190596992", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-09-18T00:00:00Z", "endDate": "2025-09-24T00:00:00Z", "submitByDate": "2025-09-24T00:00:00Z", "checkDate": "2025-09-26T00:00:00Z", "checkCount": 0, "id": "cbe7375f-36e4-4280-a89c-224256f69dd4", "companyId": "12024615", "type": "payperiod", "_rid": "NmJkAKiCbEcuEgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcuEgEAAAAAAA==/", "_etag": "\"9d003391-0000-0100-0000-686ff7750000\"", "_attachments": "attachments/", "_ts": 1752168309}, {"payPeriodId": "1030074347462799", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-09-25T00:00:00Z", "endDate": "2025-10-01T00:00:00Z", "submitByDate": "2025-10-01T00:00:00Z", "checkDate": "2025-10-03T00:00:00Z", "checkCount": 0, "id": "0bcf066f-abeb-41ea-b253-206213bd3bee", "companyId": "12024615", "type": "payperiod", "_rid": "NmJkAKiCbEcvEgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcvEgEAAAAAAA==/", "_etag": "\"9d003691-0000-0100-0000-686ff7750000\"", "_attachments": "attachments/", "_ts": 1752168309}, {"payPeriodId": "1030074467290270", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-10-02T00:00:00Z", "endDate": "2025-10-08T00:00:00Z", "submitByDate": "2025-10-08T00:00:00Z", "checkDate": "2025-10-10T00:00:00Z", "checkCount": 0, "id": "26408686-e445-432e-8f6f-c46efc8e96c0", "companyId": "12024615", "type": "payperiod", "_rid": "NmJkAKiCbEcwEgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcwEgEAAAAAAA==/", "_etag": "\"9d003891-0000-0100-0000-686ff7750000\"", "_attachments": "attachments/", "_ts": 1752168309}, {"payPeriodId": "1030069513553401", "intervalCode": "WEEKLY", "status": "REVERSED", "description": "Weekly Payroll (2)", "startDate": "2025-01-23T00:00:00Z", "endDate": "2025-01-29T00:00:00Z", "submitByDate": "2025-01-29T00:00:00Z", "checkDate": "2025-01-31T00:00:00Z", "checkCount": 2, "id": "dec2a9fb-0e96-4261-b8d2-c9f67724b34e", "companyId": "12024615", "type": "payperiod", "_rid": "NmJkAKiCbEcxEgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcxEgEAAAAAAA==/", "_etag": "\"9d003a91-0000-0100-0000-686ff7750000\"", "_attachments": "attachments/", "_ts": 1752168309}], "links": [{"rel": "self", "href": "https://ca-payroll-ai-mock-sb-001-secure.nicebeach-8a7a52ab.eastus.azurecontainerapps.io/ca/companies/12024615/payperiods"}]}, "status_code": 200}