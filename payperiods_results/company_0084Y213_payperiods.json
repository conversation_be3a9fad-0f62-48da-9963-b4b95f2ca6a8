{"success": true, "company_id": "0084Y213", "data": {"metadata": {"contentItemCount": 44}, "content": [{"payPeriodId": "1060038926897493", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (2)", "startDate": "2024-12-21T00:00:00Z", "endDate": "2025-01-03T00:00:00Z", "submitByDate": "2025-01-03T00:00:00Z", "checkDate": "2025-01-06T00:00:00Z", "checkCount": 2, "id": "54db2764-917a-4943-8cad-8e732f986700", "companyId": "0084Y213", "type": "payperiod", "_rid": "NmJkAKiCbEcdAQMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcdAQMAAAAAAA==/", "_etag": "\"a400adc1-0000-0100-0000-687022780000\"", "_attachments": "attachments/", "_ts": 1752179320}, {"payPeriodId": "1060038997284794", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (2)", "startDate": "2025-01-04T00:00:00Z", "endDate": "2025-01-17T00:00:00Z", "submitByDate": "2025-01-16T00:00:00Z", "checkDate": "2025-01-17T00:00:00Z", "checkCount": 2, "id": "b422c6e3-5d7e-4d40-8fd4-60f364eab926", "companyId": "0084Y213", "type": "payperiod", "_rid": "NmJkAKiCbEceAQMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEceAQMAAAAAAA==/", "_etag": "\"a400afc1-0000-0100-0000-687022780000\"", "_attachments": "attachments/", "_ts": 1752179320}, {"payPeriodId": "1060039522614042", "status": "COMPLETED", "description": "Correction", "startDate": "2025-01-04T00:00:00Z", "endDate": "2025-01-17T00:00:00Z", "submitByDate": "2025-01-19T00:00:00Z", "checkDate": "2025-01-20T00:00:00Z", "checkCount": 1, "id": "45c594ba-6ded-4db9-b7e7-564e6b8daa1b", "companyId": "0084Y213", "type": "payperiod", "_rid": "NmJkAKiCbEcfAQMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcfAQMAAAAAAA==/", "_etag": "\"a400b0c1-0000-0100-0000-687022780000\"", "_attachments": "attachments/", "_ts": 1752179320}, {"payPeriodId": "1060039076026299", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (2)", "startDate": "2025-01-18T00:00:00Z", "endDate": "2025-01-31T00:00:00Z", "submitByDate": "2025-01-31T00:00:00Z", "checkDate": "2025-02-03T00:00:00Z", "checkCount": 1, "id": "514464b5-d2b4-4ce2-80b4-9df1afa0264f", "companyId": "0084Y213", "type": "payperiod", "_rid": "NmJkAKiCbEcgAQMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcgAQMAAAAAAA==/", "_etag": "\"a400b5c1-0000-0100-0000-687022780000\"", "_attachments": "attachments/", "_ts": 1752179320}, {"payPeriodId": "1060039153952705", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (2)", "startDate": "2025-02-01T00:00:00Z", "endDate": "2025-02-14T00:00:00Z", "submitByDate": "2025-02-13T00:00:00Z", "checkDate": "2025-02-14T00:00:00Z", "checkCount": 1, "id": "2d286c0b-1e80-4191-afcd-47a81c33477d", "companyId": "0084Y213", "type": "payperiod", "_rid": "NmJkAKiCbEchAQMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEchAQMAAAAAAA==/", "_etag": "\"a400bcc1-0000-0100-0000-687022780000\"", "_attachments": "attachments/", "_ts": 1752179320}, {"payPeriodId": "1060039224911145", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (2)", "startDate": "2025-02-15T00:00:00Z", "endDate": "2025-02-28T00:00:00Z", "submitByDate": "2025-02-28T00:00:00Z", "checkDate": "2025-03-03T00:00:00Z", "checkCount": 1, "id": "df55d779-1130-49a5-94f9-4e4a290bb547", "companyId": "0084Y213", "type": "payperiod", "_rid": "NmJkAKiCbEciAQMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEciAQMAAAAAAA==/", "_etag": "\"a400c1c1-0000-0100-0000-687022780000\"", "_attachments": "attachments/", "_ts": 1752179320}, {"payPeriodId": "1060039294398750", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (2)", "startDate": "2025-03-01T00:00:00Z", "endDate": "2025-03-14T00:00:00Z", "submitByDate": "2025-03-14T00:00:00Z", "checkDate": "2025-03-17T00:00:00Z", "checkCount": 2, "id": "081605e5-db0b-424d-b718-cb7dd90f2424", "companyId": "0084Y213", "type": "payperiod", "_rid": "NmJkAKiCbEcjAQMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcjAQMAAAAAAA==/", "_etag": "\"a400c6c1-0000-0100-0000-687022780000\"", "_attachments": "attachments/", "_ts": 1752179320}, {"payPeriodId": "1060039372877322", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (2)", "startDate": "2025-03-15T00:00:00Z", "endDate": "2025-03-28T00:00:00Z", "submitByDate": "2025-03-28T00:00:00Z", "checkDate": "2025-03-31T00:00:00Z", "checkCount": 2, "id": "f97d5601-2943-42b3-a7ea-a47e4c07769d", "companyId": "0084Y213", "type": "payperiod", "_rid": "NmJkAKiCbEckAQMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEckAQMAAAAAAA==/", "_etag": "\"a400c8c1-0000-0100-0000-687022780000\"", "_attachments": "attachments/", "_ts": 1752179320}, {"payPeriodId": "1060039454094184", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (2)", "startDate": "2025-03-29T00:00:00Z", "endDate": "2025-04-11T00:00:00Z", "submitByDate": "2025-04-11T00:00:00Z", "checkDate": "2025-04-14T00:00:00Z", "checkCount": 0, "id": "138b0c6d-1ff9-4eb1-9a06-6f0c7cbb523b", "companyId": "0084Y213", "type": "payperiod", "_rid": "NmJkAKiCbEclAQMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEclAQMAAAAAAA==/", "_etag": "\"a400c9c1-0000-0100-0000-687022790000\"", "_attachments": "attachments/", "_ts": 1752179321}, {"payPeriodId": "1060039520447132", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (2)", "startDate": "2025-04-12T00:00:00Z", "endDate": "2025-04-25T00:00:00Z", "submitByDate": "2025-04-25T00:00:00Z", "checkDate": "2025-04-28T00:00:00Z", "checkCount": 0, "id": "ccaca735-332e-48c8-a872-5ce55253e111", "companyId": "0084Y213", "type": "payperiod", "_rid": "NmJkAKiCbEcmAQMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcmAQMAAAAAAA==/", "_etag": "\"a400ccc1-0000-0100-0000-687022790000\"", "_attachments": "attachments/", "_ts": 1752179321}, {"payPeriodId": "1060039589001928", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (2)", "startDate": "2025-04-26T00:00:00Z", "endDate": "2025-05-09T00:00:00Z", "submitByDate": "2025-05-09T00:00:00Z", "checkDate": "2025-05-12T00:00:00Z", "checkCount": 0, "id": "e58d6bbd-c465-404f-a2ae-cf2f36352fca", "companyId": "0084Y213", "type": "payperiod", "_rid": "NmJkAKiCbEcnAQMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcnAQMAAAAAAA==/", "_etag": "\"a400cec1-0000-0100-0000-687022790000\"", "_attachments": "attachments/", "_ts": 1752179321}, {"payPeriodId": "1060039656725054", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (2)", "startDate": "2025-05-10T00:00:00Z", "endDate": "2025-05-23T00:00:00Z", "submitByDate": "2025-05-22T00:00:00Z", "checkDate": "2025-05-23T00:00:00Z", "checkCount": 0, "id": "e2aa46ca-9fb8-4a94-8b24-d766ea0616ab", "companyId": "0084Y213", "type": "payperiod", "_rid": "NmJkAKiCbEcoAQMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcoAQMAAAAAAA==/", "_etag": "\"a400d1c1-0000-0100-0000-687022790000\"", "_attachments": "attachments/", "_ts": 1752179321}, {"payPeriodId": "1060039720488363", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (2)", "startDate": "2025-05-24T00:00:00Z", "endDate": "2025-06-06T00:00:00Z", "submitByDate": "2025-06-06T00:00:00Z", "checkDate": "2025-06-09T00:00:00Z", "checkCount": 0, "id": "c16a4e90-01f5-43f0-9fa3-2af1098f2fd8", "companyId": "0084Y213", "type": "payperiod", "_rid": "NmJkAKiCbEcpAQMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcpAQMAAAAAAA==/", "_etag": "\"a400d2c1-0000-0100-0000-687022790000\"", "_attachments": "attachments/", "_ts": 1752179321}, {"payPeriodId": "1060039793600898", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (2)", "startDate": "2025-06-07T00:00:00Z", "endDate": "2025-06-20T00:00:00Z", "submitByDate": "2025-06-20T00:00:00Z", "checkDate": "2025-06-23T00:00:00Z", "checkCount": 0, "id": "22a08145-2c7f-4312-ae84-c38faabc5eab", "companyId": "0084Y213", "type": "payperiod", "_rid": "NmJkAKiCbEcqAQMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcqAQMAAAAAAA==/", "_etag": "\"a400d5c1-0000-0100-0000-687022790000\"", "_attachments": "attachments/", "_ts": 1752179321}, {"payPeriodId": "1060039862731087", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (2)", "startDate": "2025-06-21T00:00:00Z", "endDate": "2025-07-04T00:00:00Z", "submitByDate": "2025-07-03T00:00:00Z", "checkDate": "2025-07-07T00:00:00Z", "checkCount": 0, "id": "ac132891-3479-4d8d-adc5-98c4b3ba1e82", "companyId": "0084Y213", "type": "payperiod", "_rid": "NmJkAKiCbEcrAQMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcrAQMAAAAAAA==/", "_etag": "\"a400d7c1-0000-0100-0000-687022790000\"", "_attachments": "attachments/", "_ts": 1752179321}, {"payPeriodId": "1060039937214343", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (2)", "startDate": "2025-07-05T00:00:00Z", "endDate": "2025-07-18T00:00:00Z", "submitByDate": "2025-07-18T00:00:00Z", "checkDate": "2025-07-21T00:00:00Z", "checkCount": 0, "id": "595ec206-5148-4384-8956-034f10d1a1a6", "companyId": "0084Y213", "type": "payperiod", "_rid": "NmJkAKiCbEcsAQMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcsAQMAAAAAAA==/", "_etag": "\"a400dbc1-0000-0100-0000-687022790000\"", "_attachments": "attachments/", "_ts": 1752179321}, {"payPeriodId": "1060040009108058", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (2)", "startDate": "2025-07-19T00:00:00Z", "endDate": "2025-08-01T00:00:00Z", "submitByDate": "2025-08-01T00:00:00Z", "checkDate": "2025-08-04T00:00:00Z", "checkCount": 0, "id": "bf8d54f9-e6b6-489a-ab66-c8b430567752", "companyId": "0084Y213", "type": "payperiod", "_rid": "NmJkAKiCbEctAQMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEctAQMAAAAAAA==/", "_etag": "\"a400ddc1-0000-0100-0000-687022790000\"", "_attachments": "attachments/", "_ts": 1752179321}, {"payPeriodId": "1060040084236589", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (2)", "startDate": "2025-08-02T00:00:00Z", "endDate": "2025-08-15T00:00:00Z", "submitByDate": "2025-08-15T00:00:00Z", "checkDate": "2025-08-18T00:00:00Z", "checkCount": 0, "id": "a3312b45-58bc-4ab5-a614-d1d76aa25310", "companyId": "0084Y213", "type": "payperiod", "_rid": "NmJkAKiCbEcuAQMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcuAQMAAAAAAA==/", "_etag": "\"a400e1c1-0000-0100-0000-687022790000\"", "_attachments": "attachments/", "_ts": 1752179321}, {"payPeriodId": "1060040153789451", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (2)", "startDate": "2025-08-16T00:00:00Z", "endDate": "2025-08-29T00:00:00Z", "submitByDate": "2025-08-28T00:00:00Z", "checkDate": "2025-08-29T00:00:00Z", "checkCount": 0, "id": "66479c05-8a02-405d-a775-4b832794f063", "companyId": "0084Y213", "type": "payperiod", "_rid": "NmJkAKiCbEcvAQMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcvAQMAAAAAAA==/", "_etag": "\"a400e6c1-0000-0100-0000-687022790000\"", "_attachments": "attachments/", "_ts": 1752179321}, {"payPeriodId": "1060040228790292", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (2)", "startDate": "2025-08-30T00:00:00Z", "endDate": "2025-09-12T00:00:00Z", "submitByDate": "2025-09-12T00:00:00Z", "checkDate": "2025-09-15T00:00:00Z", "checkCount": 0, "id": "d37b48bc-6f9a-40f5-a931-1777c12d9da5", "companyId": "0084Y213", "type": "payperiod", "_rid": "NmJkAKiCbEcwAQMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcwAQMAAAAAAA==/", "_etag": "\"a400e8c1-0000-0100-0000-687022790000\"", "_attachments": "attachments/", "_ts": 1752179321}, {"payPeriodId": "1060040292500277", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (2)", "startDate": "2025-09-13T00:00:00Z", "endDate": "2025-09-26T00:00:00Z", "submitByDate": "2025-09-26T00:00:00Z", "checkDate": "2025-09-29T00:00:00Z", "checkCount": 0, "id": "329e6c36-7111-408a-bd0e-667c881d4171", "companyId": "0084Y213", "type": "payperiod", "_rid": "NmJkAKiCbEcxAQMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcxAQMAAAAAAA==/", "_etag": "\"a400e9c1-0000-0100-0000-687022790000\"", "_attachments": "attachments/", "_ts": 1752179321}, {"payPeriodId": "1060040366794812", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (2)", "startDate": "2025-09-27T00:00:00Z", "endDate": "2025-10-10T00:00:00Z", "submitByDate": "2025-10-09T00:00:00Z", "checkDate": "2025-10-10T00:00:00Z", "checkCount": 0, "id": "eb7693c7-69a2-46dd-ad54-abb85d7c030d", "companyId": "0084Y213", "type": "payperiod", "_rid": "NmJkAKiCbEcyAQMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcyAQMAAAAAAA==/", "_etag": "\"a400edc1-0000-0100-0000-6870227a0000\"", "_attachments": "attachments/", "_ts": 1752179322}, {"payPeriodId": "1060038926897493", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (2)", "startDate": "2024-12-21T00:00:00Z", "endDate": "2025-01-03T00:00:00Z", "submitByDate": "2025-01-03T00:00:00Z", "checkDate": "2025-01-06T00:00:00Z", "checkCount": 2, "id": "f73f3f4e-8369-4fe2-95b3-96a934f5f9bf", "companyId": "0084Y213", "type": "payperiod", "_rid": "NmJkAKiCbEc8AQMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc8AQMAAAAAAA==/", "_etag": "\"a4000fc2-0000-0100-0000-6870227a0000\"", "_attachments": "attachments/", "_ts": 1752179322}, {"payPeriodId": "1060038997284794", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (2)", "startDate": "2025-01-04T00:00:00Z", "endDate": "2025-01-17T00:00:00Z", "submitByDate": "2025-01-16T00:00:00Z", "checkDate": "2025-01-17T00:00:00Z", "checkCount": 2, "id": "7b12c64d-2590-43a8-a48f-04d218185fe5", "companyId": "0084Y213", "type": "payperiod", "_rid": "NmJkAKiCbEc9AQMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc9AQMAAAAAAA==/", "_etag": "\"a40012c2-0000-0100-0000-6870227a0000\"", "_attachments": "attachments/", "_ts": 1752179322}, {"payPeriodId": "1060039522614042", "status": "COMPLETED", "description": "Correction", "startDate": "2025-01-04T00:00:00Z", "endDate": "2025-01-17T00:00:00Z", "submitByDate": "2025-01-19T00:00:00Z", "checkDate": "2025-01-20T00:00:00Z", "checkCount": 1, "id": "08958891-64f3-4db8-8cbf-80128ad39211", "companyId": "0084Y213", "type": "payperiod", "_rid": "NmJkAKiCbEc+AQMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc+AQMAAAAAAA==/", "_etag": "\"a40015c2-0000-0100-0000-6870227b0000\"", "_attachments": "attachments/", "_ts": 1752179323}, {"payPeriodId": "1060039076026299", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (2)", "startDate": "2025-01-18T00:00:00Z", "endDate": "2025-01-31T00:00:00Z", "submitByDate": "2025-01-31T00:00:00Z", "checkDate": "2025-02-03T00:00:00Z", "checkCount": 1, "id": "30feaf0d-f07c-459e-8172-fbfca2e23dce", "companyId": "0084Y213", "type": "payperiod", "_rid": "NmJkAKiCbEc-AQMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc-AQMAAAAAAA==/", "_etag": "\"a40018c2-0000-0100-0000-6870227b0000\"", "_attachments": "attachments/", "_ts": 1752179323}, {"payPeriodId": "1060039153952705", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (2)", "startDate": "2025-02-01T00:00:00Z", "endDate": "2025-02-14T00:00:00Z", "submitByDate": "2025-02-13T00:00:00Z", "checkDate": "2025-02-14T00:00:00Z", "checkCount": 1, "id": "cba6f6f0-5a1c-448a-946b-fb8aa8ede24f", "companyId": "0084Y213", "type": "payperiod", "_rid": "NmJkAKiCbEdAAQMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdAAQMAAAAAAA==/", "_etag": "\"a4001cc2-0000-0100-0000-6870227b0000\"", "_attachments": "attachments/", "_ts": 1752179323}, {"payPeriodId": "1060039224911145", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (2)", "startDate": "2025-02-15T00:00:00Z", "endDate": "2025-02-28T00:00:00Z", "submitByDate": "2025-02-28T00:00:00Z", "checkDate": "2025-03-03T00:00:00Z", "checkCount": 1, "id": "5a7b2214-311d-4733-b38f-d07cee73f8dd", "companyId": "0084Y213", "type": "payperiod", "_rid": "NmJkAKiCbEdBAQMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdBAQMAAAAAAA==/", "_etag": "\"a4001fc2-0000-0100-0000-6870227b0000\"", "_attachments": "attachments/", "_ts": 1752179323}, {"payPeriodId": "1060039294398750", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (2)", "startDate": "2025-03-01T00:00:00Z", "endDate": "2025-03-14T00:00:00Z", "submitByDate": "2025-03-14T00:00:00Z", "checkDate": "2025-03-17T00:00:00Z", "checkCount": 2, "id": "6490ed4b-a4cb-4c38-9786-24b59b717489", "companyId": "0084Y213", "type": "payperiod", "_rid": "NmJkAKiCbEdCAQMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdCAQMAAAAAAA==/", "_etag": "\"a40021c2-0000-0100-0000-6870227b0000\"", "_attachments": "attachments/", "_ts": 1752179323}, {"payPeriodId": "1060039372877322", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (2)", "startDate": "2025-03-15T00:00:00Z", "endDate": "2025-03-28T00:00:00Z", "submitByDate": "2025-03-28T00:00:00Z", "checkDate": "2025-03-31T00:00:00Z", "checkCount": 2, "id": "ff2a99fb-78b8-422a-a4d6-71a98108fac6", "companyId": "0084Y213", "type": "payperiod", "_rid": "NmJkAKiCbEdDAQMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdDAQMAAAAAAA==/", "_etag": "\"a40023c2-0000-0100-0000-6870227b0000\"", "_attachments": "attachments/", "_ts": 1752179323}, {"payPeriodId": "1060039454094184", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (2)", "startDate": "2025-03-29T00:00:00Z", "endDate": "2025-04-11T00:00:00Z", "submitByDate": "2025-04-11T00:00:00Z", "checkDate": "2025-04-14T00:00:00Z", "checkCount": 2, "id": "47bca070-6510-468b-b429-3b8d5b673167", "companyId": "0084Y213", "type": "payperiod", "_rid": "NmJkAKiCbEdEAQMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdEAQMAAAAAAA==/", "_etag": "\"a40026c2-0000-0100-0000-6870227b0000\"", "_attachments": "attachments/", "_ts": 1752179323}, {"payPeriodId": "1060039520447132", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (2)", "startDate": "2025-04-12T00:00:00Z", "endDate": "2025-04-25T00:00:00Z", "submitByDate": "2025-04-25T00:00:00Z", "checkDate": "2025-04-28T00:00:00Z", "checkCount": 2, "id": "c25f3719-9063-4a50-902e-6443e1fe73a8", "companyId": "0084Y213", "type": "payperiod", "_rid": "NmJkAKiCbEdFAQMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdFAQMAAAAAAA==/", "_etag": "\"a40029c2-0000-0100-0000-6870227b0000\"", "_attachments": "attachments/", "_ts": 1752179323}, {"payPeriodId": "1060039589001928", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (2)", "startDate": "2025-04-26T00:00:00Z", "endDate": "2025-05-09T00:00:00Z", "submitByDate": "2025-05-09T00:00:00Z", "checkDate": "2025-05-12T00:00:00Z", "checkCount": 2, "id": "a136b5c6-4726-4aec-9fa0-7a2e86d8d139", "companyId": "0084Y213", "type": "payperiod", "_rid": "NmJkAKiCbEdGAQMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdGAQMAAAAAAA==/", "_etag": "\"a4002ac2-0000-0100-0000-6870227b0000\"", "_attachments": "attachments/", "_ts": 1752179323}, {"payPeriodId": "1060039656725054", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (2)", "startDate": "2025-05-10T00:00:00Z", "endDate": "2025-05-23T00:00:00Z", "submitByDate": "2025-05-22T00:00:00Z", "checkDate": "2025-05-23T00:00:00Z", "checkCount": 3, "id": "52cdb3a9-ec92-44be-a1b6-15f0ef4c6b9a", "companyId": "0084Y213", "type": "payperiod", "_rid": "NmJkAKiCbEdHAQMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdHAQMAAAAAAA==/", "_etag": "\"a4002ec2-0000-0100-0000-6870227b0000\"", "_attachments": "attachments/", "_ts": 1752179323}, {"payPeriodId": "1060039720488363", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (2)", "startDate": "2025-05-24T00:00:00Z", "endDate": "2025-06-06T00:00:00Z", "submitByDate": "2025-06-06T00:00:00Z", "checkDate": "2025-06-09T00:00:00Z", "checkCount": 2, "id": "653b8f64-0508-499c-865e-6e8c3d2506da", "companyId": "0084Y213", "type": "payperiod", "_rid": "NmJkAKiCbEdIAQMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdIAQMAAAAAAA==/", "_etag": "\"a40032c2-0000-0100-0000-6870227b0000\"", "_attachments": "attachments/", "_ts": 1752179323}, {"payPeriodId": "1060039793600898", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (2)", "startDate": "2025-06-07T00:00:00Z", "endDate": "2025-06-20T00:00:00Z", "submitByDate": "2025-06-20T00:00:00Z", "checkDate": "2025-06-23T00:00:00Z", "checkCount": 2, "id": "0007ff95-0ab6-4e38-bfbe-d15a7a2b0b29", "companyId": "0084Y213", "type": "payperiod", "_rid": "NmJkAKiCbEdJAQMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdJAQMAAAAAAA==/", "_etag": "\"a40035c2-0000-0100-0000-6870227b0000\"", "_attachments": "attachments/", "_ts": 1752179323}, {"payPeriodId": "1060039862731087", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (2)", "startDate": "2025-06-21T00:00:00Z", "endDate": "2025-07-04T00:00:00Z", "submitByDate": "2025-07-03T00:00:00Z", "checkDate": "2025-07-07T00:00:00Z", "checkCount": 2, "id": "84f41258-294d-45f7-8a82-277aa945e8c6", "companyId": "0084Y213", "type": "payperiod", "_rid": "NmJkAKiCbEdKAQMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdKAQMAAAAAAA==/", "_etag": "\"a40037c2-0000-0100-0000-6870227c0000\"", "_attachments": "attachments/", "_ts": 1752179324}, {"payPeriodId": "1060039937214343", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (2)", "startDate": "2025-07-05T00:00:00Z", "endDate": "2025-07-18T00:00:00Z", "submitByDate": "2025-07-18T00:00:00Z", "checkDate": "2025-07-21T00:00:00Z", "checkCount": 0, "id": "321f8a12-c133-4dcc-a324-e59823f72fcd", "companyId": "0084Y213", "type": "payperiod", "_rid": "NmJkAKiCbEdLAQMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdLAQMAAAAAAA==/", "_etag": "\"a4003dc2-0000-0100-0000-6870227c0000\"", "_attachments": "attachments/", "_ts": 1752179324}, {"payPeriodId": "1060040009108058", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (2)", "startDate": "2025-07-19T00:00:00Z", "endDate": "2025-08-01T00:00:00Z", "submitByDate": "2025-08-01T00:00:00Z", "checkDate": "2025-08-04T00:00:00Z", "checkCount": 0, "id": "b6bc7f48-0804-4374-a2f2-e2cac5a03d93", "companyId": "0084Y213", "type": "payperiod", "_rid": "NmJkAKiCbEdMAQMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdMAQMAAAAAAA==/", "_etag": "\"a40041c2-0000-0100-0000-6870227c0000\"", "_attachments": "attachments/", "_ts": 1752179324}, {"payPeriodId": "1060040084236589", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (2)", "startDate": "2025-08-02T00:00:00Z", "endDate": "2025-08-15T00:00:00Z", "submitByDate": "2025-08-15T00:00:00Z", "checkDate": "2025-08-18T00:00:00Z", "checkCount": 0, "id": "52b5688b-d26d-44a0-8a46-29b290b92e00", "companyId": "0084Y213", "type": "payperiod", "_rid": "NmJkAKiCbEdNAQMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdNAQMAAAAAAA==/", "_etag": "\"a40043c2-0000-0100-0000-6870227c0000\"", "_attachments": "attachments/", "_ts": 1752179324}, {"payPeriodId": "1060040153789451", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (2)", "startDate": "2025-08-16T00:00:00Z", "endDate": "2025-08-29T00:00:00Z", "submitByDate": "2025-08-28T00:00:00Z", "checkDate": "2025-08-29T00:00:00Z", "checkCount": 0, "id": "a114d577-dc26-4231-b4fd-a40bf7cd4c66", "companyId": "0084Y213", "type": "payperiod", "_rid": "NmJkAKiCbEdOAQMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdOAQMAAAAAAA==/", "_etag": "\"a40047c2-0000-0100-0000-6870227c0000\"", "_attachments": "attachments/", "_ts": 1752179324}, {"payPeriodId": "1060040228790292", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (2)", "startDate": "2025-08-30T00:00:00Z", "endDate": "2025-09-12T00:00:00Z", "submitByDate": "2025-09-12T00:00:00Z", "checkDate": "2025-09-15T00:00:00Z", "checkCount": 0, "id": "54d221be-999b-415e-beb9-5cba3ddab6e1", "companyId": "0084Y213", "type": "payperiod", "_rid": "NmJkAKiCbEdPAQMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdPAQMAAAAAAA==/", "_etag": "\"a40049c2-0000-0100-0000-6870227c0000\"", "_attachments": "attachments/", "_ts": 1752179324}, {"payPeriodId": "1060040292500277", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (2)", "startDate": "2025-09-13T00:00:00Z", "endDate": "2025-09-26T00:00:00Z", "submitByDate": "2025-09-26T00:00:00Z", "checkDate": "2025-09-29T00:00:00Z", "checkCount": 0, "id": "48f74afe-c941-44b3-a184-2b74bbe410c7", "companyId": "0084Y213", "type": "payperiod", "_rid": "NmJkAKiCbEdQAQMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdQAQMAAAAAAA==/", "_etag": "\"a4004ac2-0000-0100-0000-6870227c0000\"", "_attachments": "attachments/", "_ts": 1752179324}, {"payPeriodId": "1060040366794812", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (2)", "startDate": "2025-09-27T00:00:00Z", "endDate": "2025-10-10T00:00:00Z", "submitByDate": "2025-10-09T00:00:00Z", "checkDate": "2025-10-10T00:00:00Z", "checkCount": 0, "id": "33ffbf9a-d836-4a1d-b40f-e0ae19b1b439", "companyId": "0084Y213", "type": "payperiod", "_rid": "NmJkAKiCbEdRAQMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdRAQMAAAAAAA==/", "_etag": "\"a4004fc2-0000-0100-0000-6870227c0000\"", "_attachments": "attachments/", "_ts": 1752179324}], "links": [{"rel": "self", "href": "https://ca-payroll-ai-mock-sb-001-secure.nicebeach-8a7a52ab.eastus.azurecontainerapps.io/ca/companies/0084Y213/payperiods"}]}, "status_code": 200}