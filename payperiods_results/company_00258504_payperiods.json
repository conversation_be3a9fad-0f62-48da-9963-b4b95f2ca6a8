{"success": true, "company_id": "00258504", "data": {"metadata": {"contentItemCount": 46}, "content": [{"payPeriodId": "1040046243845952", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-01-01T00:00:00Z", "endDate": "2025-01-15T00:00:00Z", "submitByDate": "2025-01-13T00:00:00Z", "checkDate": "2025-01-15T00:00:00Z", "checkCount": 10, "id": "ddbf414e-6653-40f3-a181-c0931d6e4082", "companyId": "00258504", "type": "payperiod", "_rid": "NmJkAKiCbEeQlgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeQlgMAAAAAAA==/", "_etag": "\"a6006b90-0000-0100-0000-68702e9c0000\"", "_attachments": "attachments/", "_ts": 1752182428}, {"payPeriodId": "1040046243845953", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-01-16T00:00:00Z", "endDate": "2025-01-31T00:00:00Z", "submitByDate": "2025-01-29T00:00:00Z", "checkDate": "2025-01-31T00:00:00Z", "checkCount": 10, "id": "af28c8f6-8ed2-40ca-8ae2-ad7c50fc5169", "companyId": "00258504", "type": "payperiod", "_rid": "NmJkAKiCbEeRlgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeRlgMAAAAAAA==/", "_etag": "\"a6006d90-0000-0100-0000-68702e9c0000\"", "_attachments": "attachments/", "_ts": 1752182428}, {"payPeriodId": "1040047040028036", "status": "COMPLETED", "description": "Void", "startDate": "2025-01-26T00:00:00Z", "endDate": "2025-02-10T00:00:00Z", "submitByDate": "2025-02-13T00:00:00Z", "checkDate": "2025-02-14T00:00:00Z", "checkCount": 2, "id": "afa325e2-b3b4-41e6-a538-25aad6aeb5f6", "companyId": "00258504", "type": "payperiod", "_rid": "NmJkAKiCbEeSlgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeSlgMAAAAAAA==/", "_etag": "\"a6006e90-0000-0100-0000-68702e9c0000\"", "_attachments": "attachments/", "_ts": 1752182428}, {"payPeriodId": "1040047040028157", "status": "COMPLETED", "description": "payroll correction", "startDate": "2025-01-26T00:00:00Z", "endDate": "2025-02-10T00:00:00Z", "submitByDate": "2025-02-13T00:00:00Z", "checkDate": "2025-02-14T00:00:00Z", "checkCount": 2, "id": "f0fb626d-8716-474f-841a-1738a0bbb5b8", "companyId": "00258504", "type": "payperiod", "_rid": "NmJkAKiCbEeTlgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeTlgMAAAAAAA==/", "_etag": "\"a6007390-0000-0100-0000-68702e9c0000\"", "_attachments": "attachments/", "_ts": 1752182428}, {"payPeriodId": "1040046414233937", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-02-01T00:00:00Z", "endDate": "2025-02-15T00:00:00Z", "submitByDate": "2025-02-12T00:00:00Z", "checkDate": "2025-02-14T00:00:00Z", "checkCount": 10, "id": "cf2e58c3-95f8-4cf7-aa8a-c4910efa3a3f", "companyId": "00258504", "type": "payperiod", "_rid": "NmJkAKiCbEeUlgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeUlgMAAAAAAA==/", "_etag": "\"a6007590-0000-0100-0000-68702e9c0000\"", "_attachments": "attachments/", "_ts": 1752182428}, {"payPeriodId": "1040046414233938", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-02-16T00:00:00Z", "endDate": "2025-02-28T00:00:00Z", "submitByDate": "2025-02-26T00:00:00Z", "checkDate": "2025-02-28T00:00:00Z", "checkCount": 9, "id": "60045a01-d421-496f-834f-f24ca9c0e1da", "companyId": "00258504", "type": "payperiod", "_rid": "NmJkAKiCbEeVlgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeVlgMAAAAAAA==/", "_etag": "\"a6007790-0000-0100-0000-68702e9c0000\"", "_attachments": "attachments/", "_ts": 1752182428}, {"payPeriodId": "1040046609411015", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-03-01T00:00:00Z", "endDate": "2025-03-15T00:00:00Z", "submitByDate": "2025-03-12T00:00:00Z", "checkDate": "2025-03-14T00:00:00Z", "checkCount": 10, "id": "9b13a3b5-afea-4c8a-9bed-223d4ae2d480", "companyId": "00258504", "type": "payperiod", "_rid": "NmJkAKiCbEeWlgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeWlgMAAAAAAA==/", "_etag": "\"a6007a90-0000-0100-0000-68702e9c0000\"", "_attachments": "attachments/", "_ts": 1752182428}, {"payPeriodId": "1040046609411016", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-03-16T00:00:00Z", "endDate": "2025-03-31T00:00:00Z", "submitByDate": "2025-03-27T00:00:00Z", "checkDate": "2025-03-28T00:00:00Z", "checkCount": 10, "id": "93af706c-07cb-418d-88b2-168fd6100fbe", "companyId": "00258504", "type": "payperiod", "_rid": "NmJkAKiCbEeXlgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeXlgMAAAAAAA==/", "_etag": "\"a6007e90-0000-0100-0000-68702e9c0000\"", "_attachments": "attachments/", "_ts": 1752182428}, {"payPeriodId": "1040047174877223", "status": "COMPLETED", "description": "correction", "startDate": "2025-03-18T00:00:00Z", "endDate": "2025-03-18T00:00:00Z", "submitByDate": "2025-03-18T00:00:00Z", "checkDate": "2025-03-19T00:00:00Z", "checkCount": 1, "id": "2b885e2e-a32f-4198-a660-39634c875b51", "companyId": "00258504", "type": "payperiod", "_rid": "NmJkAKiCbEeYlgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeYlgMAAAAAAA==/", "_etag": "\"a6008490-0000-0100-0000-68702e9c0000\"", "_attachments": "attachments/", "_ts": 1752182428}, {"payPeriodId": "1040046780185699", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-04-01T00:00:00Z", "endDate": "2025-04-15T00:00:00Z", "submitByDate": "2025-04-13T00:00:00Z", "checkDate": "2025-04-14T00:00:00Z", "checkCount": 0, "id": "ed65182e-fd26-417c-843a-701e6230b226", "companyId": "00258504", "type": "payperiod", "_rid": "NmJkAKiCbEeZlgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeZlgMAAAAAAA==/", "_etag": "\"a6008e90-0000-0100-0000-68702e9c0000\"", "_attachments": "attachments/", "_ts": 1752182428}, {"payPeriodId": "1040046780185700", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-04-16T00:00:00Z", "endDate": "2025-04-30T00:00:00Z", "submitByDate": "2025-04-28T00:00:00Z", "checkDate": "2025-04-29T00:00:00Z", "checkCount": 0, "id": "8502844a-99cf-40c2-9736-3b5081ed558b", "companyId": "00258504", "type": "payperiod", "_rid": "NmJkAKiCbEealgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEealgMAAAAAAA==/", "_etag": "\"a6009690-0000-0100-0000-68702e9c0000\"", "_attachments": "attachments/", "_ts": 1752182428}, {"payPeriodId": "1040046931289050", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-05-01T00:00:00Z", "endDate": "2025-05-15T00:00:00Z", "submitByDate": "2025-05-13T00:00:00Z", "checkDate": "2025-05-15T00:00:00Z", "checkCount": 0, "id": "f916f090-26e1-457c-b94b-3b942f1af131", "companyId": "00258504", "type": "payperiod", "_rid": "NmJkAKiCbEeblgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeblgMAAAAAAA==/", "_etag": "\"a6009890-0000-0100-0000-68702e9c0000\"", "_attachments": "attachments/", "_ts": 1752182428}, {"payPeriodId": "1040046931289051", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-05-16T00:00:00Z", "endDate": "2025-05-31T00:00:00Z", "submitByDate": "2025-05-28T00:00:00Z", "checkDate": "2025-05-30T00:00:00Z", "checkCount": 0, "id": "72ef5dd1-7af8-4b64-8b70-1c84aecae828", "companyId": "00258504", "type": "payperiod", "_rid": "NmJkAKiCbEeclgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeclgMAAAAAAA==/", "_etag": "\"a6009f90-0000-0100-0000-68702e9d0000\"", "_attachments": "attachments/", "_ts": 1752182429}, {"payPeriodId": "1040047119591779", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-06-01T00:00:00Z", "endDate": "2025-06-15T00:00:00Z", "submitByDate": "2025-06-11T00:00:00Z", "checkDate": "2025-06-13T00:00:00Z", "checkCount": 0, "id": "e509dc10-e020-49b9-aa3f-48f93371d764", "companyId": "00258504", "type": "payperiod", "_rid": "NmJkAKiCbEedlgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEedlgMAAAAAAA==/", "_etag": "\"a600a290-0000-0100-0000-68702e9d0000\"", "_attachments": "attachments/", "_ts": 1752182429}, {"payPeriodId": "1040047119591780", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-06-16T00:00:00Z", "endDate": "2025-06-30T00:00:00Z", "submitByDate": "2025-06-26T00:00:00Z", "checkDate": "2025-06-30T00:00:00Z", "checkCount": 0, "id": "ab2885b4-8523-46df-9f93-5946c193bb2f", "companyId": "00258504", "type": "payperiod", "_rid": "NmJkAKiCbEeelgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeelgMAAAAAAA==/", "_etag": "\"a600a490-0000-0100-0000-68702e9d0000\"", "_attachments": "attachments/", "_ts": 1752182429}, {"payPeriodId": "1040047278799867", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-07-01T00:00:00Z", "endDate": "2025-07-15T00:00:00Z", "submitByDate": "2025-07-11T00:00:00Z", "checkDate": "2025-07-15T00:00:00Z", "checkCount": 0, "id": "5fde9726-4ef3-4f58-9007-496e8e0e5a75", "companyId": "00258504", "type": "payperiod", "_rid": "NmJkAKiCbEeflgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeflgMAAAAAAA==/", "_etag": "\"a600a690-0000-0100-0000-68702e9d0000\"", "_attachments": "attachments/", "_ts": 1752182429}, {"payPeriodId": "1040047278799868", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-07-16T00:00:00Z", "endDate": "2025-07-31T00:00:00Z", "submitByDate": "2025-07-29T00:00:00Z", "checkDate": "2025-07-31T00:00:00Z", "checkCount": 0, "id": "28f5d216-8d6e-4e8e-a43a-f42d529dd038", "companyId": "00258504", "type": "payperiod", "_rid": "NmJkAKiCbEeglgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeglgMAAAAAAA==/", "_etag": "\"a600a990-0000-0100-0000-68702e9d0000\"", "_attachments": "attachments/", "_ts": 1752182429}, {"payPeriodId": "1040047446490648", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-08-01T00:00:00Z", "endDate": "2025-08-15T00:00:00Z", "submitByDate": "2025-08-13T00:00:00Z", "checkDate": "2025-08-15T00:00:00Z", "checkCount": 0, "id": "8a3d350d-cdb8-4bfd-84dd-44f3fe64d8c9", "companyId": "00258504", "type": "payperiod", "_rid": "NmJkAKiCbEehlgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEehlgMAAAAAAA==/", "_etag": "\"a600aa90-0000-0100-0000-68702e9d0000\"", "_attachments": "attachments/", "_ts": 1752182429}, {"payPeriodId": "1040047446490649", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-08-16T00:00:00Z", "endDate": "2025-08-31T00:00:00Z", "submitByDate": "2025-08-27T00:00:00Z", "checkDate": "2025-08-29T00:00:00Z", "checkCount": 0, "id": "3ddb920e-9c3c-40db-b4c1-48dcade2c1fb", "companyId": "00258504", "type": "payperiod", "_rid": "NmJkAKiCbEeilgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeilgMAAAAAAA==/", "_etag": "\"a600ac90-0000-0100-0000-68702e9d0000\"", "_attachments": "attachments/", "_ts": 1752182429}, {"payPeriodId": "1040047634669797", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-09-01T00:00:00Z", "endDate": "2025-09-15T00:00:00Z", "submitByDate": "2025-09-11T00:00:00Z", "checkDate": "2025-09-15T00:00:00Z", "checkCount": 0, "id": "63d6004d-6749-47bd-b102-c0fe342b3399", "companyId": "00258504", "type": "payperiod", "_rid": "NmJkAKiCbEejlgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEejlgMAAAAAAA==/", "_etag": "\"a600b090-0000-0100-0000-68702e9d0000\"", "_attachments": "attachments/", "_ts": 1752182429}, {"payPeriodId": "1040047634669798", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-09-16T00:00:00Z", "endDate": "2025-09-30T00:00:00Z", "submitByDate": "2025-09-26T00:00:00Z", "checkDate": "2025-09-30T00:00:00Z", "checkCount": 0, "id": "bb89c7fc-d288-419f-9937-f1f32d2bdd51", "companyId": "00258504", "type": "payperiod", "_rid": "NmJkAKiCbEeklgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeklgMAAAAAAA==/", "_etag": "\"a600b490-0000-0100-0000-68702e9d0000\"", "_attachments": "attachments/", "_ts": 1752182429}, {"payPeriodId": "1040047789895977", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-10-01T00:00:00Z", "endDate": "2025-10-15T00:00:00Z", "submitByDate": "2025-10-13T00:00:00Z", "checkDate": "2025-10-15T00:00:00Z", "checkCount": 0, "id": "c2040a78-f334-4909-95e9-b6c863b5d5dc", "companyId": "00258504", "type": "payperiod", "_rid": "NmJkAKiCbEellgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEellgMAAAAAAA==/", "_etag": "\"a600bb90-0000-0100-0000-68702e9d0000\"", "_attachments": "attachments/", "_ts": 1752182429}, {"payPeriodId": "1040047789895978", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-10-16T00:00:00Z", "endDate": "2025-10-31T00:00:00Z", "submitByDate": "2025-10-29T00:00:00Z", "checkDate": "2025-10-31T00:00:00Z", "checkCount": 0, "id": "d9f981de-4887-4652-8b64-fb7c07f60597", "companyId": "00258504", "type": "payperiod", "_rid": "NmJkAKiCbEemlgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEemlgMAAAAAAA==/", "_etag": "\"a600bc90-0000-0100-0000-68702e9d0000\"", "_attachments": "attachments/", "_ts": 1752182429}, {"payPeriodId": "1040046243845952", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-01-01T00:00:00Z", "endDate": "2025-01-15T00:00:00Z", "submitByDate": "2025-01-13T00:00:00Z", "checkDate": "2025-01-15T00:00:00Z", "checkCount": 10, "id": "43d3f657-e1ad-4eef-a4b2-4c5cc45405be", "companyId": "00258504", "type": "payperiod", "_rid": "NmJkAKiCbEe7lgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEe7lgMAAAAAAA==/", "_etag": "\"a600ff90-0000-0100-0000-68702e9f0000\"", "_attachments": "attachments/", "_ts": 1752182431}, {"payPeriodId": "1040046243845953", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-01-16T00:00:00Z", "endDate": "2025-01-31T00:00:00Z", "submitByDate": "2025-01-29T00:00:00Z", "checkDate": "2025-01-31T00:00:00Z", "checkCount": 10, "id": "592cb244-85cd-47aa-a82c-13301af4068a", "companyId": "00258504", "type": "payperiod", "_rid": "NmJkAKiCbEe8lgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEe8lgMAAAAAAA==/", "_etag": "\"a6000391-0000-0100-0000-68702e9f0000\"", "_attachments": "attachments/", "_ts": 1752182431}, {"payPeriodId": "1040047040028036", "status": "COMPLETED", "description": "Void", "startDate": "2025-01-26T00:00:00Z", "endDate": "2025-02-10T00:00:00Z", "submitByDate": "2025-02-13T00:00:00Z", "checkDate": "2025-02-14T00:00:00Z", "checkCount": 2, "id": "d93d8dbf-2a56-4c31-a164-c05a754bd7c9", "companyId": "00258504", "type": "payperiod", "_rid": "NmJkAKiCbEe9lgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEe9lgMAAAAAAA==/", "_etag": "\"a6000491-0000-0100-0000-68702e9f0000\"", "_attachments": "attachments/", "_ts": 1752182431}, {"payPeriodId": "1040047040028157", "status": "COMPLETED", "description": "payroll correction", "startDate": "2025-01-26T00:00:00Z", "endDate": "2025-02-10T00:00:00Z", "submitByDate": "2025-02-13T00:00:00Z", "checkDate": "2025-02-14T00:00:00Z", "checkCount": 2, "id": "1638d18c-b443-4036-8156-35279129acfc", "companyId": "00258504", "type": "payperiod", "_rid": "NmJkAKiCbEe+lgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEe+lgMAAAAAAA==/", "_etag": "\"a6000991-0000-0100-0000-68702e9f0000\"", "_attachments": "attachments/", "_ts": 1752182431}, {"payPeriodId": "1040046414233937", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-02-01T00:00:00Z", "endDate": "2025-02-15T00:00:00Z", "submitByDate": "2025-02-12T00:00:00Z", "checkDate": "2025-02-14T00:00:00Z", "checkCount": 10, "id": "374e4670-c6d5-4e35-bf43-3da6c5d45384", "companyId": "00258504", "type": "payperiod", "_rid": "NmJkAKiCbEe-lgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEe-lgMAAAAAAA==/", "_etag": "\"a6000b91-0000-0100-0000-68702e9f0000\"", "_attachments": "attachments/", "_ts": 1752182431}, {"payPeriodId": "1040046414233938", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-02-16T00:00:00Z", "endDate": "2025-02-28T00:00:00Z", "submitByDate": "2025-02-26T00:00:00Z", "checkDate": "2025-02-28T00:00:00Z", "checkCount": 9, "id": "28336909-f9ee-4f85-94bf-a759dbc139e4", "companyId": "00258504", "type": "payperiod", "_rid": "NmJkAKiCbEfAlgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfAlgMAAAAAAA==/", "_etag": "\"a6001291-0000-0100-0000-68702e9f0000\"", "_attachments": "attachments/", "_ts": 1752182431}, {"payPeriodId": "1040046609411015", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-03-01T00:00:00Z", "endDate": "2025-03-15T00:00:00Z", "submitByDate": "2025-03-12T00:00:00Z", "checkDate": "2025-03-14T00:00:00Z", "checkCount": 10, "id": "1edeaa93-0a47-4178-ad43-6baaabbac92f", "companyId": "00258504", "type": "payperiod", "_rid": "NmJkAKiCbEfBlgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfBlgMAAAAAAA==/", "_etag": "\"a6001691-0000-0100-0000-68702ea00000\"", "_attachments": "attachments/", "_ts": 1752182432}, {"payPeriodId": "1040046609411016", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-03-16T00:00:00Z", "endDate": "2025-03-31T00:00:00Z", "submitByDate": "2025-03-27T00:00:00Z", "checkDate": "2025-03-28T00:00:00Z", "checkCount": 10, "id": "db478624-fb11-4d57-86f6-433cecfb003e", "companyId": "00258504", "type": "payperiod", "_rid": "NmJkAKiCbEfClgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfClgMAAAAAAA==/", "_etag": "\"a6001791-0000-0100-0000-68702ea00000\"", "_attachments": "attachments/", "_ts": 1752182432}, {"payPeriodId": "1040047174877223", "status": "COMPLETED", "description": "correction", "startDate": "2025-03-18T00:00:00Z", "endDate": "2025-03-18T00:00:00Z", "submitByDate": "2025-03-18T00:00:00Z", "checkDate": "2025-03-19T00:00:00Z", "checkCount": 1, "id": "0d8c8357-0a15-4612-8e34-97d4bff94a80", "companyId": "00258504", "type": "payperiod", "_rid": "NmJkAKiCbEfDlgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfDlgMAAAAAAA==/", "_etag": "\"a6001891-0000-0100-0000-68702ea00000\"", "_attachments": "attachments/", "_ts": 1752182432}, {"payPeriodId": "1040046780185699", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-04-01T00:00:00Z", "endDate": "2025-04-15T00:00:00Z", "submitByDate": "2025-04-13T00:00:00Z", "checkDate": "2025-04-14T00:00:00Z", "checkCount": 11, "id": "f48a79f6-91a4-4d9a-8c5b-9300a2218ca8", "companyId": "00258504", "type": "payperiod", "_rid": "NmJkAKiCbEfElgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfElgMAAAAAAA==/", "_etag": "\"a6001d91-0000-0100-0000-68702ea00000\"", "_attachments": "attachments/", "_ts": 1752182432}, {"payPeriodId": "1040046780185700", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-04-16T00:00:00Z", "endDate": "2025-04-30T00:00:00Z", "submitByDate": "2025-04-28T00:00:00Z", "checkDate": "2025-04-29T00:00:00Z", "checkCount": 11, "id": "64517abc-9dd5-498e-8b09-3410953bb495", "companyId": "00258504", "type": "payperiod", "_rid": "NmJkAKiCbEfFlgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfFlgMAAAAAAA==/", "_etag": "\"a6002091-0000-0100-0000-68702ea00000\"", "_attachments": "attachments/", "_ts": 1752182432}, {"payPeriodId": "1040046931289050", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-05-01T00:00:00Z", "endDate": "2025-05-15T00:00:00Z", "submitByDate": "2025-05-13T00:00:00Z", "checkDate": "2025-05-15T00:00:00Z", "checkCount": 10, "id": "e67f667a-7a1b-40c0-9d1c-ed4ff1c1a81d", "companyId": "00258504", "type": "payperiod", "_rid": "NmJkAKiCbEfGlgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfGlgMAAAAAAA==/", "_etag": "\"a6002291-0000-0100-0000-68702ea00000\"", "_attachments": "attachments/", "_ts": 1752182432}, {"payPeriodId": "1040046931289051", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-05-16T00:00:00Z", "endDate": "2025-05-31T00:00:00Z", "submitByDate": "2025-05-28T00:00:00Z", "checkDate": "2025-05-30T00:00:00Z", "checkCount": 10, "id": "daac90b2-657a-4881-b73d-c07aba135d05", "companyId": "00258504", "type": "payperiod", "_rid": "NmJkAKiCbEfHlgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfHlgMAAAAAAA==/", "_etag": "\"a6002791-0000-0100-0000-68702ea00000\"", "_attachments": "attachments/", "_ts": 1752182432}, {"payPeriodId": "1040047119591779", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-06-01T00:00:00Z", "endDate": "2025-06-15T00:00:00Z", "submitByDate": "2025-06-11T00:00:00Z", "checkDate": "2025-06-13T00:00:00Z", "checkCount": 11, "id": "ec7409eb-e3b6-4844-9c79-2d398eb6f21a", "companyId": "00258504", "type": "payperiod", "_rid": "NmJkAKiCbEfIlgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfIlgMAAAAAAA==/", "_etag": "\"a6002991-0000-0100-0000-68702ea00000\"", "_attachments": "attachments/", "_ts": 1752182432}, {"payPeriodId": "1040047119591780", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-06-16T00:00:00Z", "endDate": "2025-06-30T00:00:00Z", "submitByDate": "2025-06-26T00:00:00Z", "checkDate": "2025-06-30T00:00:00Z", "checkCount": 11, "id": "2fe73ddf-ddd1-4c72-85b0-c6c0b4645b91", "companyId": "00258504", "type": "payperiod", "_rid": "NmJkAKiCbEfJlgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfJlgMAAAAAAA==/", "_etag": "\"a6002d91-0000-0100-0000-68702ea00000\"", "_attachments": "attachments/", "_ts": 1752182432}, {"payPeriodId": "1040047278799867", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-07-01T00:00:00Z", "endDate": "2025-07-15T00:00:00Z", "submitByDate": "2025-07-11T00:00:00Z", "checkDate": "2025-07-15T00:00:00Z", "checkCount": 0, "id": "15239cf0-8be2-4bda-9072-24a1ec36b4f6", "companyId": "00258504", "type": "payperiod", "_rid": "NmJkAKiCbEfKlgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfKlgMAAAAAAA==/", "_etag": "\"a6003091-0000-0100-0000-68702ea00000\"", "_attachments": "attachments/", "_ts": 1752182432}, {"payPeriodId": "1040047278799868", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-07-16T00:00:00Z", "endDate": "2025-07-31T00:00:00Z", "submitByDate": "2025-07-29T00:00:00Z", "checkDate": "2025-07-31T00:00:00Z", "checkCount": 0, "id": "7553b8ea-4937-4845-a719-6b3fb1e6fae0", "companyId": "00258504", "type": "payperiod", "_rid": "NmJkAKiCbEfLlgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfLlgMAAAAAAA==/", "_etag": "\"a6003591-0000-0100-0000-68702ea00000\"", "_attachments": "attachments/", "_ts": 1752182432}, {"payPeriodId": "1040047446490648", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-08-01T00:00:00Z", "endDate": "2025-08-15T00:00:00Z", "submitByDate": "2025-08-13T00:00:00Z", "checkDate": "2025-08-15T00:00:00Z", "checkCount": 0, "id": "eaaccf8f-2c3f-4090-99b5-9cc732df2203", "companyId": "00258504", "type": "payperiod", "_rid": "NmJkAKiCbEfMlgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfMlgMAAAAAAA==/", "_etag": "\"a6003991-0000-0100-0000-68702ea00000\"", "_attachments": "attachments/", "_ts": 1752182432}, {"payPeriodId": "1040047446490649", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-08-16T00:00:00Z", "endDate": "2025-08-31T00:00:00Z", "submitByDate": "2025-08-27T00:00:00Z", "checkDate": "2025-08-29T00:00:00Z", "checkCount": 0, "id": "ae3001b1-f6f1-4ef6-b384-3e50d525f841", "companyId": "00258504", "type": "payperiod", "_rid": "NmJkAKiCbEfNlgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfNlgMAAAAAAA==/", "_etag": "\"a6003e91-0000-0100-0000-68702ea00000\"", "_attachments": "attachments/", "_ts": 1752182432}, {"payPeriodId": "1040047634669797", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-09-01T00:00:00Z", "endDate": "2025-09-15T00:00:00Z", "submitByDate": "2025-09-11T00:00:00Z", "checkDate": "2025-09-15T00:00:00Z", "checkCount": 0, "id": "219af163-b576-4617-8045-6bad43a03677", "companyId": "00258504", "type": "payperiod", "_rid": "NmJkAKiCbEfOlgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfOlgMAAAAAAA==/", "_etag": "\"a6003f91-0000-0100-0000-68702ea10000\"", "_attachments": "attachments/", "_ts": 1752182433}, {"payPeriodId": "1040047634669798", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-09-16T00:00:00Z", "endDate": "2025-09-30T00:00:00Z", "submitByDate": "2025-09-26T00:00:00Z", "checkDate": "2025-09-30T00:00:00Z", "checkCount": 0, "id": "dc010739-4294-42ad-9a1b-ccd381a33272", "companyId": "00258504", "type": "payperiod", "_rid": "NmJkAKiCbEfPlgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfPlgMAAAAAAA==/", "_etag": "\"a6004291-0000-0100-0000-68702ea10000\"", "_attachments": "attachments/", "_ts": 1752182433}, {"payPeriodId": "1040047789895977", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-10-01T00:00:00Z", "endDate": "2025-10-15T00:00:00Z", "submitByDate": "2025-10-13T00:00:00Z", "checkDate": "2025-10-15T00:00:00Z", "checkCount": 0, "id": "1fc987e0-8150-4cc9-8443-ade87a02e3cb", "companyId": "00258504", "type": "payperiod", "_rid": "NmJkAKiCbEfQlgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfQlgMAAAAAAA==/", "_etag": "\"a6004691-0000-0100-0000-68702ea10000\"", "_attachments": "attachments/", "_ts": 1752182433}, {"payPeriodId": "1040047789895978", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-10-16T00:00:00Z", "endDate": "2025-10-31T00:00:00Z", "submitByDate": "2025-10-29T00:00:00Z", "checkDate": "2025-10-31T00:00:00Z", "checkCount": 0, "id": "ec7ba932-43ea-400f-adbb-ef539c9e7fa7", "companyId": "00258504", "type": "payperiod", "_rid": "NmJkAKiCbEfRlgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfRlgMAAAAAAA==/", "_etag": "\"a6004a91-0000-0100-0000-68702ea10000\"", "_attachments": "attachments/", "_ts": 1752182433}], "links": [{"rel": "self", "href": "https://ca-payroll-ai-mock-sb-001-secure.nicebeach-8a7a52ab.eastus.azurecontainerapps.io/ca/companies/00258504/payperiods"}]}, "status_code": 200}