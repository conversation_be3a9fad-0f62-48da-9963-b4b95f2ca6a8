{"success": true, "company_id": "19015542", "data": {"metadata": {"contentItemCount": 148}, "content": [{"payPeriodId": "1140035058656571", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2024-12-28T00:00:00Z", "endDate": "2025-01-12T00:00:00Z", "submitByDate": "2025-01-15T00:00:00Z", "checkDate": "2025-01-17T00:00:00Z", "checkCount": 43, "id": "c5331b72-7f04-4365-bee0-7b54053840c7", "companyId": "19015542", "type": "payperiod", "_rid": "NmJkAKiCbEdhRgAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdhRgAAAAAAAA==/", "_etag": "\"9700bcf0-0000-0100-0000-686fd47d0000\"", "_attachments": "attachments/", "_ts": 1752159357}, {"payPeriodId": "1140036221760296", "status": "COMPLETED", "description": "Void", "startDate": "2024-12-28T00:00:00Z", "endDate": "2025-01-12T00:00:00Z", "submitByDate": "2025-01-16T00:00:00Z", "checkDate": "2025-01-17T00:00:00Z", "checkCount": 1, "id": "767730bb-c16e-4e1b-b7d3-6819fde642e1", "companyId": "19015542", "type": "payperiod", "_rid": "NmJkAKiCbEdiRgAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdiRgAAAAAAAA==/", "_etag": "\"9700bef0-0000-0100-0000-686fd47d0000\"", "_attachments": "attachments/", "_ts": 1752159357}, {"payPeriodId": "1140036222345602", "status": "COMPLETED", "description": "PAYCHEX FIX", "startDate": "2024-12-28T00:00:00Z", "endDate": "2025-01-12T00:00:00Z", "submitByDate": "2025-01-16T00:00:00Z", "checkDate": "2025-01-17T00:00:00Z", "checkCount": 1, "id": "fe1f02b6-1cb7-4283-ba7c-9472e8062cba", "companyId": "19015542", "type": "payperiod", "_rid": "NmJkAKiCbEdjRgAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdjRgAAAAAAAA==/", "_etag": "\"9700c2f0-0000-0100-0000-686fd47d0000\"", "_attachments": "attachments/", "_ts": 1752159357}, {"payPeriodId": "1140035117064141", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-01-11T00:00:00Z", "endDate": "2025-01-24T00:00:00Z", "submitByDate": "2025-01-29T00:00:00Z", "checkDate": "2025-01-31T00:00:00Z", "checkCount": 42, "id": "3074d82c-c5ca-4195-8501-3dac39a0d605", "companyId": "19015542", "type": "payperiod", "_rid": "NmJkAKiCbEdkRgAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdkRgAAAAAAAA==/", "_etag": "\"9700caf0-0000-0100-0000-686fd47d0000\"", "_attachments": "attachments/", "_ts": 1752159357}, {"payPeriodId": "1140036221760485", "status": "COMPLETED", "description": "Void", "startDate": "2025-01-11T00:00:00Z", "endDate": "2025-01-24T00:00:00Z", "submitByDate": "2025-01-30T00:00:00Z", "checkDate": "2025-01-31T00:00:00Z", "checkCount": 1, "id": "54fe2858-fd53-43d0-90b4-b65208d771bc", "companyId": "19015542", "type": "payperiod", "_rid": "NmJkAKiCbEdlRgAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdlRgAAAAAAAA==/", "_etag": "\"9700ccf0-0000-0100-0000-686fd47d0000\"", "_attachments": "attachments/", "_ts": 1752159357}, {"payPeriodId": "1140036222345842", "status": "COMPLETED", "description": "PAYCHEX FIX", "startDate": "2025-01-11T00:00:00Z", "endDate": "2025-01-24T00:00:00Z", "submitByDate": "2025-01-30T00:00:00Z", "checkDate": "2025-01-31T00:00:00Z", "checkCount": 1, "id": "7f8e9439-b264-4332-a29c-0bdd90f21eb3", "companyId": "19015542", "type": "payperiod", "_rid": "NmJkAKiCbEdmRgAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdmRgAAAAAAAA==/", "_etag": "\"9700d0f0-0000-0100-0000-686fd47d0000\"", "_attachments": "attachments/", "_ts": 1752159357}, {"payPeriodId": "1140035183597002", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-01-25T00:00:00Z", "endDate": "2025-02-07T00:00:00Z", "submitByDate": "2025-02-12T00:00:00Z", "checkDate": "2025-02-14T00:00:00Z", "checkCount": 41, "id": "96a14742-b0cc-47e1-97e2-205af00afde5", "companyId": "19015542", "type": "payperiod", "_rid": "NmJkAKiCbEdnRgAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdnRgAAAAAAAA==/", "_etag": "\"9700d9f0-0000-0100-0000-686fd47d0000\"", "_attachments": "attachments/", "_ts": 1752159357}, {"payPeriodId": "1140036221760334", "status": "COMPLETED", "description": "Void", "startDate": "2025-01-25T00:00:00Z", "endDate": "2025-02-07T00:00:00Z", "submitByDate": "2025-02-13T00:00:00Z", "checkDate": "2025-02-14T00:00:00Z", "checkCount": 1, "id": "9c0321b6-841b-46c8-bdd7-75bb51682147", "companyId": "19015542", "type": "payperiod", "_rid": "NmJkAKiCbEdoRgAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdoRgAAAAAAAA==/", "_etag": "\"9700e0f0-0000-0100-0000-686fd47d0000\"", "_attachments": "attachments/", "_ts": 1752159357}, {"payPeriodId": "1140036222345964", "status": "COMPLETED", "description": "PAYCHEX FIX", "startDate": "2025-01-25T00:00:00Z", "endDate": "2025-02-07T00:00:00Z", "submitByDate": "2025-02-13T00:00:00Z", "checkDate": "2025-02-14T00:00:00Z", "checkCount": 1, "id": "489e3bae-7a56-4bec-8d24-f01636c6e0c7", "companyId": "19015542", "type": "payperiod", "_rid": "NmJkAKiCbEdpRgAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdpRgAAAAAAAA==/", "_etag": "\"9700e6f0-0000-0100-0000-686fd47d0000\"", "_attachments": "attachments/", "_ts": 1752159357}, {"payPeriodId": "1140035237236324", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-02-08T00:00:00Z", "endDate": "2025-02-21T00:00:00Z", "submitByDate": "2025-02-26T00:00:00Z", "checkDate": "2025-02-28T00:00:00Z", "checkCount": 41, "id": "953b1495-b48b-4ba8-89c5-51950de7b0d7", "companyId": "19015542", "type": "payperiod", "_rid": "NmJkAKiCbEdqRgAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdqRgAAAAAAAA==/", "_etag": "\"9700e8f0-0000-0100-0000-686fd47d0000\"", "_attachments": "attachments/", "_ts": 1752159357}, {"payPeriodId": "1140036221760527", "status": "COMPLETED", "description": "Void", "startDate": "2025-02-08T00:00:00Z", "endDate": "2025-02-21T00:00:00Z", "submitByDate": "2025-02-27T00:00:00Z", "checkDate": "2025-02-28T00:00:00Z", "checkCount": 1, "id": "3c62237f-529d-4966-a214-18e3c9e6368f", "companyId": "19015542", "type": "payperiod", "_rid": "NmJkAKiCbEdrRgAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdrRgAAAAAAAA==/", "_etag": "\"9700edf0-0000-0100-0000-686fd47d0000\"", "_attachments": "attachments/", "_ts": 1752159357}, {"payPeriodId": "1140036222346058", "status": "COMPLETED", "description": "PAYCHEX FIX", "startDate": "2025-02-08T00:00:00Z", "endDate": "2025-02-21T00:00:00Z", "submitByDate": "2025-02-27T00:00:00Z", "checkDate": "2025-02-28T00:00:00Z", "checkCount": 1, "id": "74980934-4377-49d9-a51f-a140026d288c", "companyId": "19015542", "type": "payperiod", "_rid": "NmJkAKiCbEdsRgAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdsRgAAAAAAAA==/", "_etag": "\"9700f1f0-0000-0100-0000-686fd47d0000\"", "_attachments": "attachments/", "_ts": 1752159357}, {"payPeriodId": "1140035296441124", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-02-22T00:00:00Z", "endDate": "2025-03-07T00:00:00Z", "submitByDate": "2025-03-12T00:00:00Z", "checkDate": "2025-03-14T00:00:00Z", "checkCount": 42, "id": "342bf198-4a52-42d0-a07a-9a0444b9235e", "companyId": "19015542", "type": "payperiod", "_rid": "NmJkAKiCbEdtRgAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdtRgAAAAAAAA==/", "_etag": "\"9700f9f0-0000-0100-0000-686fd47d0000\"", "_attachments": "attachments/", "_ts": 1752159357}, {"payPeriodId": "1140036221760371", "status": "COMPLETED", "description": "Void", "startDate": "2025-02-22T00:00:00Z", "endDate": "2025-03-07T00:00:00Z", "submitByDate": "2025-03-13T00:00:00Z", "checkDate": "2025-03-14T00:00:00Z", "checkCount": 1, "id": "c1b0a257-255a-4dd5-ab40-d673a2f88663", "companyId": "19015542", "type": "payperiod", "_rid": "NmJkAKiCbEduRgAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEduRgAAAAAAAA==/", "_etag": "\"9700fef0-0000-0100-0000-686fd47d0000\"", "_attachments": "attachments/", "_ts": 1752159357}, {"payPeriodId": "1140036222346156", "status": "COMPLETED", "description": "PAYCHEX FIX", "startDate": "2025-02-22T00:00:00Z", "endDate": "2025-03-07T00:00:00Z", "submitByDate": "2025-03-13T00:00:00Z", "checkDate": "2025-03-14T00:00:00Z", "checkCount": 1, "id": "d36f2daf-21f9-4f1c-a396-67c28f6cc267", "companyId": "19015542", "type": "payperiod", "_rid": "NmJkAKiCbEdvRgAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdvRgAAAAAAAA==/", "_etag": "\"9700fff0-0000-0100-0000-686fd47e0000\"", "_attachments": "attachments/", "_ts": 1752159358}, {"payPeriodId": "1140035361804982", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-03-08T00:00:00Z", "endDate": "2025-03-21T00:00:00Z", "submitByDate": "2025-03-26T00:00:00Z", "checkDate": "2025-03-28T00:00:00Z", "checkCount": 42, "id": "236d91f5-dc50-42f4-abea-0c98eec8c90b", "companyId": "19015542", "type": "payperiod", "_rid": "NmJkAKiCbEdwRgAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdwRgAAAAAAAA==/", "_etag": "\"970002f1-0000-0100-0000-686fd47e0000\"", "_attachments": "attachments/", "_ts": 1752159358}, {"payPeriodId": "1140036221760565", "status": "COMPLETED", "description": "Void", "startDate": "2025-03-08T00:00:00Z", "endDate": "2025-03-21T00:00:00Z", "submitByDate": "2025-03-27T00:00:00Z", "checkDate": "2025-03-28T00:00:00Z", "checkCount": 1, "id": "3c89fb2f-fd35-4185-aae3-9448d18a0066", "companyId": "19015542", "type": "payperiod", "_rid": "NmJkAKiCbEdxRgAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdxRgAAAAAAAA==/", "_etag": "\"970003f1-0000-0100-0000-686fd47e0000\"", "_attachments": "attachments/", "_ts": 1752159358}, {"payPeriodId": "1140036222346331", "status": "COMPLETED", "description": "PAYCHEX FIX", "startDate": "2025-03-08T00:00:00Z", "endDate": "2025-03-21T00:00:00Z", "submitByDate": "2025-03-27T00:00:00Z", "checkDate": "2025-03-28T00:00:00Z", "checkCount": 1, "id": "77bf37be-5b13-4877-8064-17b2f44b5cd1", "companyId": "19015542", "type": "payperiod", "_rid": "NmJkAKiCbEdyRgAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdyRgAAAAAAAA==/", "_etag": "\"970006f1-0000-0100-0000-686fd47e0000\"", "_attachments": "attachments/", "_ts": 1752159358}, {"payPeriodId": "1140035445096121", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-03-22T00:00:00Z", "endDate": "2025-04-04T00:00:00Z", "submitByDate": "2025-04-09T00:00:00Z", "checkDate": "2025-04-11T00:00:00Z", "checkCount": 0, "id": "e21e5cc9-4655-4086-9559-3c9e171df984", "companyId": "19015542", "type": "payperiod", "_rid": "NmJkAKiCbEdzRgAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdzRgAAAAAAAA==/", "_etag": "\"97000af1-0000-0100-0000-686fd47e0000\"", "_attachments": "attachments/", "_ts": 1752159358}, {"payPeriodId": "1140036221760408", "status": "INITIAL", "description": "Void", "startDate": "2025-03-22T00:00:00Z", "endDate": "2025-04-04T00:00:00Z", "submitByDate": "2025-04-10T00:00:00Z", "checkDate": "2025-04-11T00:00:00Z", "checkCount": 0, "id": "29d89b6d-b7fd-4eef-88e5-b84ea9f7dff9", "companyId": "19015542", "type": "payperiod", "_rid": "NmJkAKiCbEd0RgAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEd0RgAAAAAAAA==/", "_etag": "\"97000ff1-0000-0100-0000-686fd47e0000\"", "_attachments": "attachments/", "_ts": 1752159358}, {"payPeriodId": "1140036222346436", "status": "INITIAL", "description": "PAYCHEX FIX", "startDate": "2025-03-22T00:00:00Z", "endDate": "2025-04-04T00:00:00Z", "submitByDate": "2025-04-10T00:00:00Z", "checkDate": "2025-04-11T00:00:00Z", "checkCount": 0, "id": "a060c600-edcd-43ee-b56f-69af2d18cb22", "companyId": "19015542", "type": "payperiod", "_rid": "NmJkAKiCbEd1RgAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEd1RgAAAAAAAA==/", "_etag": "\"970013f1-0000-0100-0000-686fd47e0000\"", "_attachments": "attachments/", "_ts": 1752159358}, {"payPeriodId": "1140035499971292", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-04-05T00:00:00Z", "endDate": "2025-04-18T00:00:00Z", "submitByDate": "2025-04-23T00:00:00Z", "checkDate": "2025-04-25T00:00:00Z", "checkCount": 0, "id": "6c880c0b-e5d1-4985-9951-7b2a624b6173", "companyId": "19015542", "type": "payperiod", "_rid": "NmJkAKiCbEd2RgAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEd2RgAAAAAAAA==/", "_etag": "\"970017f1-0000-0100-0000-686fd47e0000\"", "_attachments": "attachments/", "_ts": 1752159358}, {"payPeriodId": "1140036221760623", "status": "INITIAL", "description": "Void", "startDate": "2025-04-05T00:00:00Z", "endDate": "2025-04-18T00:00:00Z", "submitByDate": "2025-04-24T00:00:00Z", "checkDate": "2025-04-25T00:00:00Z", "checkCount": 0, "id": "8152cd54-922c-43c0-9f9c-af17ef452706", "companyId": "19015542", "type": "payperiod", "_rid": "NmJkAKiCbEd3RgAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEd3RgAAAAAAAA==/", "_etag": "\"97001cf1-0000-0100-0000-686fd47e0000\"", "_attachments": "attachments/", "_ts": 1752159358}, {"payPeriodId": "1140036222424559", "status": "INITIAL", "description": "PAYCHEX FIX", "startDate": "2025-04-05T00:00:00Z", "endDate": "2025-04-18T00:00:00Z", "submitByDate": "2025-04-24T00:00:00Z", "checkDate": "2025-04-25T00:00:00Z", "checkCount": 0, "id": "8d4cf777-fe03-447f-b237-55e8acadf031", "companyId": "19015542", "type": "payperiod", "_rid": "NmJkAKiCbEd4RgAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEd4RgAAAAAAAA==/", "_etag": "\"97001df1-0000-0100-0000-686fd47e0000\"", "_attachments": "attachments/", "_ts": 1752159358}, {"payPeriodId": "1140035553302281", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-04-19T00:00:00Z", "endDate": "2025-05-02T00:00:00Z", "submitByDate": "2025-05-07T00:00:00Z", "checkDate": "2025-05-09T00:00:00Z", "checkCount": 0, "id": "6a6826e8-e6c8-425f-a67d-32d8f753c431", "companyId": "19015542", "type": "payperiod", "_rid": "NmJkAKiCbEd5RgAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEd5RgAAAAAAAA==/", "_etag": "\"97001ff1-0000-0100-0000-686fd47e0000\"", "_attachments": "attachments/", "_ts": 1752159358}, {"payPeriodId": "1140035614685539", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-05-03T00:00:00Z", "endDate": "2025-05-16T00:00:00Z", "submitByDate": "2025-05-21T00:00:00Z", "checkDate": "2025-05-23T00:00:00Z", "checkCount": 0, "id": "895276c3-dc93-4a98-b588-67f4b89c8ac4", "companyId": "19015542", "type": "payperiod", "_rid": "NmJkAKiCbEd6RgAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEd6RgAAAAAAAA==/", "_etag": "\"970022f1-0000-0100-0000-686fd47e0000\"", "_attachments": "attachments/", "_ts": 1752159358}, {"payPeriodId": "1140035670435166", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-05-17T00:00:00Z", "endDate": "2025-05-30T00:00:00Z", "submitByDate": "2025-06-04T00:00:00Z", "checkDate": "2025-06-06T00:00:00Z", "checkCount": 0, "id": "80135057-2d34-4606-9163-916d0852d96f", "companyId": "19015542", "type": "payperiod", "_rid": "NmJkAKiCbEd7RgAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEd7RgAAAAAAAA==/", "_etag": "\"970026f1-0000-0100-0000-686fd47e0000\"", "_attachments": "attachments/", "_ts": 1752159358}, {"payPeriodId": "1140035730221705", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-05-31T00:00:00Z", "endDate": "2025-06-13T00:00:00Z", "submitByDate": "2025-06-18T00:00:00Z", "checkDate": "2025-06-20T00:00:00Z", "checkCount": 0, "id": "0d5b1947-8a04-4f8e-af69-85ccca9e8758", "companyId": "19015542", "type": "payperiod", "_rid": "NmJkAKiCbEd8RgAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEd8RgAAAAAAAA==/", "_etag": "\"97002bf1-0000-0100-0000-686fd47e0000\"", "_attachments": "attachments/", "_ts": 1752159358}, {"payPeriodId": "1140036163302083", "status": "INITIAL", "description": "Payroll", "startDate": "2025-05-31T00:00:00Z", "endDate": "2025-06-13T00:00:00Z", "submitByDate": "2025-06-19T00:00:00Z", "checkDate": "2025-06-20T00:00:00Z", "checkCount": 0, "id": "d48c4acd-333c-4d01-95c6-eff1b157dc7e", "companyId": "19015542", "type": "payperiod", "_rid": "NmJkAKiCbEd9RgAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEd9RgAAAAAAAA==/", "_etag": "\"970032f1-0000-0100-0000-686fd47f0000\"", "_attachments": "attachments/", "_ts": 1752159359}, {"payPeriodId": "1140035783987094", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-06-14T00:00:00Z", "endDate": "2025-06-27T00:00:00Z", "submitByDate": "2025-07-01T00:00:00Z", "checkDate": "2025-07-03T00:00:00Z", "checkCount": 0, "id": "68122f5e-ed48-477f-ab0c-c307d1b483cd", "companyId": "19015542", "type": "payperiod", "_rid": "NmJkAKiCbEd+RgAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEd+RgAAAAAAAA==/", "_etag": "\"970038f1-0000-0100-0000-686fd47f0000\"", "_attachments": "attachments/", "_ts": 1752159359}, {"payPeriodId": "1140035845489011", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-06-28T00:00:00Z", "endDate": "2025-07-11T00:00:00Z", "submitByDate": "2025-07-16T00:00:00Z", "checkDate": "2025-07-18T00:00:00Z", "checkCount": 0, "id": "a89ec730-38e3-4f7d-9ed3-aa624367393c", "companyId": "19015542", "type": "payperiod", "_rid": "NmJkAKiCbEd-RgAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEd-RgAAAAAAAA==/", "_etag": "\"97003df1-0000-0100-0000-686fd47f0000\"", "_attachments": "attachments/", "_ts": 1752159359}, {"payPeriodId": "1140035905101051", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-07-12T00:00:00Z", "endDate": "2025-07-25T00:00:00Z", "submitByDate": "2025-07-30T00:00:00Z", "checkDate": "2025-08-01T00:00:00Z", "checkCount": 0, "id": "1ca08d34-7610-44fe-b8ad-6fe43fff6b50", "companyId": "19015542", "type": "payperiod", "_rid": "NmJkAKiCbEeARgAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeARgAAAAAAAA==/", "_etag": "\"970040f1-0000-0100-0000-686fd47f0000\"", "_attachments": "attachments/", "_ts": 1752159359}, {"payPeriodId": "1140035966016207", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-07-26T00:00:00Z", "endDate": "2025-08-08T00:00:00Z", "submitByDate": "2025-08-13T00:00:00Z", "checkDate": "2025-08-15T00:00:00Z", "checkCount": 0, "id": "2c645fd8-fb4a-4f42-82d1-3d922a019b79", "companyId": "19015542", "type": "payperiod", "_rid": "NmJkAKiCbEeBRgAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeBRgAAAAAAAA==/", "_etag": "\"970046f1-0000-0100-0000-686fd47f0000\"", "_attachments": "attachments/", "_ts": 1752159359}, {"payPeriodId": "1140036029336500", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-08-09T00:00:00Z", "endDate": "2025-08-22T00:00:00Z", "submitByDate": "2025-08-27T00:00:00Z", "checkDate": "2025-08-29T00:00:00Z", "checkCount": 0, "id": "dd9b3fcd-14e0-49ca-a853-b016d846cbce", "companyId": "19015542", "type": "payperiod", "_rid": "NmJkAKiCbEeCRgAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeCRgAAAAAAAA==/", "_etag": "\"970048f1-0000-0100-0000-686fd47f0000\"", "_attachments": "attachments/", "_ts": 1752159359}, {"payPeriodId": "1140036082408790", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-08-23T00:00:00Z", "endDate": "2025-09-05T00:00:00Z", "submitByDate": "2025-09-10T00:00:00Z", "checkDate": "2025-09-12T00:00:00Z", "checkCount": 0, "id": "c825a70d-c21e-419f-a23d-ddb0db159002", "companyId": "19015542", "type": "payperiod", "_rid": "NmJkAKiCbEeDRgAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeDRgAAAAAAAA==/", "_etag": "\"97004cf1-0000-0100-0000-686fd47f0000\"", "_attachments": "attachments/", "_ts": 1752159359}, {"payPeriodId": "1140036144969842", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-09-06T00:00:00Z", "endDate": "2025-09-19T00:00:00Z", "submitByDate": "2025-09-24T00:00:00Z", "checkDate": "2025-09-26T00:00:00Z", "checkCount": 0, "id": "df8375f0-d935-4b96-a950-24518d59e3f3", "companyId": "19015542", "type": "payperiod", "_rid": "NmJkAKiCbEeERgAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeERgAAAAAAAA==/", "_etag": "\"97004ff1-0000-0100-0000-686fd47f0000\"", "_attachments": "attachments/", "_ts": 1752159359}, {"payPeriodId": "1140036200807826", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-09-20T00:00:00Z", "endDate": "2025-10-03T00:00:00Z", "submitByDate": "2025-10-08T00:00:00Z", "checkDate": "2025-10-10T00:00:00Z", "checkCount": 0, "id": "37b41e89-5cf6-4278-88e0-02922788d66c", "companyId": "19015542", "type": "payperiod", "_rid": "NmJkAKiCbEeFRgAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeFRgAAAAAAAA==/", "_etag": "\"970051f1-0000-0100-0000-686fd47f0000\"", "_attachments": "attachments/", "_ts": 1752159359}, {"payPeriodId": "1140035058656571", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2024-12-28T00:00:00Z", "endDate": "2025-01-12T00:00:00Z", "submitByDate": "2025-01-15T00:00:00Z", "checkDate": "2025-01-17T00:00:00Z", "checkCount": 43, "id": "59ca3ec7-a9cd-4eb5-b4b1-db80fade3797", "companyId": "19015542", "type": "payperiod", "_rid": "NmJkAKiCbEftRgAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEftRgAAAAAAAA==/", "_etag": "\"970048f3-0000-0100-0000-686fd4880000\"", "_attachments": "attachments/", "_ts": 1752159368}, {"payPeriodId": "1140036221760296", "status": "COMPLETED", "description": "Void", "startDate": "2024-12-28T00:00:00Z", "endDate": "2025-01-12T00:00:00Z", "submitByDate": "2025-01-16T00:00:00Z", "checkDate": "2025-01-17T00:00:00Z", "checkCount": 1, "id": "1f7247e5-41e9-48f8-a9b5-240576986991", "companyId": "19015542", "type": "payperiod", "_rid": "NmJkAKiCbEfuRgAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfuRgAAAAAAAA==/", "_etag": "\"97004af3-0000-0100-0000-686fd4880000\"", "_attachments": "attachments/", "_ts": 1752159368}, {"payPeriodId": "1140036222345602", "status": "COMPLETED", "description": "PAYCHEX FIX", "startDate": "2024-12-28T00:00:00Z", "endDate": "2025-01-12T00:00:00Z", "submitByDate": "2025-01-16T00:00:00Z", "checkDate": "2025-01-17T00:00:00Z", "checkCount": 1, "id": "1a706149-c48d-43df-9fe5-bc0a0d74d84d", "companyId": "19015542", "type": "payperiod", "_rid": "NmJkAKiCbEfvRgAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfvRgAAAAAAAA==/", "_etag": "\"97004ef3-0000-0100-0000-686fd4880000\"", "_attachments": "attachments/", "_ts": 1752159368}, {"payPeriodId": "1140035117064141", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-01-11T00:00:00Z", "endDate": "2025-01-24T00:00:00Z", "submitByDate": "2025-01-29T00:00:00Z", "checkDate": "2025-01-31T00:00:00Z", "checkCount": 42, "id": "2c463d82-61c5-4e17-a317-536bd0452f06", "companyId": "19015542", "type": "payperiod", "_rid": "NmJkAKiCbEfwRgAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfwRgAAAAAAAA==/", "_etag": "\"97004ff3-0000-0100-0000-686fd4880000\"", "_attachments": "attachments/", "_ts": 1752159368}, {"payPeriodId": "1140036221760485", "status": "COMPLETED", "description": "Void", "startDate": "2025-01-11T00:00:00Z", "endDate": "2025-01-24T00:00:00Z", "submitByDate": "2025-01-30T00:00:00Z", "checkDate": "2025-01-31T00:00:00Z", "checkCount": 1, "id": "d047aea9-a432-417a-83c7-baabf1a725ac", "companyId": "19015542", "type": "payperiod", "_rid": "NmJkAKiCbEfxRgAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfxRgAAAAAAAA==/", "_etag": "\"97005cf3-0000-0100-0000-686fd4880000\"", "_attachments": "attachments/", "_ts": 1752159368}, {"payPeriodId": "1140036222345842", "status": "COMPLETED", "description": "PAYCHEX FIX", "startDate": "2025-01-11T00:00:00Z", "endDate": "2025-01-24T00:00:00Z", "submitByDate": "2025-01-30T00:00:00Z", "checkDate": "2025-01-31T00:00:00Z", "checkCount": 1, "id": "d90e7020-f6c5-4232-b06d-7d919064915c", "companyId": "19015542", "type": "payperiod", "_rid": "NmJkAKiCbEfyRgAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfyRgAAAAAAAA==/", "_etag": "\"970060f3-0000-0100-0000-686fd4880000\"", "_attachments": "attachments/", "_ts": 1752159368}, {"payPeriodId": "1140035183597002", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-01-25T00:00:00Z", "endDate": "2025-02-07T00:00:00Z", "submitByDate": "2025-02-12T00:00:00Z", "checkDate": "2025-02-14T00:00:00Z", "checkCount": 41, "id": "09dc695b-b44c-4934-96a0-ff3710637d21", "companyId": "19015542", "type": "payperiod", "_rid": "NmJkAKiCbEfzRgAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfzRgAAAAAAAA==/", "_etag": "\"970062f3-0000-0100-0000-686fd4880000\"", "_attachments": "attachments/", "_ts": 1752159368}, {"payPeriodId": "1140036221760334", "status": "COMPLETED", "description": "Void", "startDate": "2025-01-25T00:00:00Z", "endDate": "2025-02-07T00:00:00Z", "submitByDate": "2025-02-13T00:00:00Z", "checkDate": "2025-02-14T00:00:00Z", "checkCount": 1, "id": "09924760-2f9b-4b60-9f30-59496d90f5d3", "companyId": "19015542", "type": "payperiod", "_rid": "NmJkAKiCbEf0RgAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf0RgAAAAAAAA==/", "_etag": "\"970064f3-0000-0100-0000-686fd4880000\"", "_attachments": "attachments/", "_ts": 1752159368}, {"payPeriodId": "1140036222345964", "status": "COMPLETED", "description": "PAYCHEX FIX", "startDate": "2025-01-25T00:00:00Z", "endDate": "2025-02-07T00:00:00Z", "submitByDate": "2025-02-13T00:00:00Z", "checkDate": "2025-02-14T00:00:00Z", "checkCount": 1, "id": "ac84502b-df38-4b86-bd93-f8b6e3ef64a5", "companyId": "19015542", "type": "payperiod", "_rid": "NmJkAKiCbEf1RgAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf1RgAAAAAAAA==/", "_etag": "\"970066f3-0000-0100-0000-686fd4880000\"", "_attachments": "attachments/", "_ts": 1752159368}, {"payPeriodId": "1140035237236324", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-02-08T00:00:00Z", "endDate": "2025-02-21T00:00:00Z", "submitByDate": "2025-02-26T00:00:00Z", "checkDate": "2025-02-28T00:00:00Z", "checkCount": 41, "id": "c533c690-8da4-46ee-b413-4c5df73f172a", "companyId": "19015542", "type": "payperiod", "_rid": "NmJkAKiCbEf2RgAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf2RgAAAAAAAA==/", "_etag": "\"97006cf3-0000-0100-0000-686fd4880000\"", "_attachments": "attachments/", "_ts": 1752159368}, {"payPeriodId": "1140036221760527", "status": "COMPLETED", "description": "Void", "startDate": "2025-02-08T00:00:00Z", "endDate": "2025-02-21T00:00:00Z", "submitByDate": "2025-02-27T00:00:00Z", "checkDate": "2025-02-28T00:00:00Z", "checkCount": 1, "id": "bc2a5fc5-ebf8-47ee-9f16-16635c86678c", "companyId": "19015542", "type": "payperiod", "_rid": "NmJkAKiCbEf3RgAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf3RgAAAAAAAA==/", "_etag": "\"970072f3-0000-0100-0000-686fd4880000\"", "_attachments": "attachments/", "_ts": 1752159368}, {"payPeriodId": "1140036222346058", "status": "COMPLETED", "description": "PAYCHEX FIX", "startDate": "2025-02-08T00:00:00Z", "endDate": "2025-02-21T00:00:00Z", "submitByDate": "2025-02-27T00:00:00Z", "checkDate": "2025-02-28T00:00:00Z", "checkCount": 1, "id": "011adf16-0d6c-46f1-a3ed-fc667b5033e3", "companyId": "19015542", "type": "payperiod", "_rid": "NmJkAKiCbEf4RgAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf4RgAAAAAAAA==/", "_etag": "\"970078f3-0000-0100-0000-686fd4880000\"", "_attachments": "attachments/", "_ts": 1752159368}, {"payPeriodId": "1140035296441124", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-02-22T00:00:00Z", "endDate": "2025-03-07T00:00:00Z", "submitByDate": "2025-03-12T00:00:00Z", "checkDate": "2025-03-14T00:00:00Z", "checkCount": 42, "id": "4d51e16f-738f-4a4c-8ea6-d777f47efac2", "companyId": "19015542", "type": "payperiod", "_rid": "NmJkAKiCbEf5RgAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf5RgAAAAAAAA==/", "_etag": "\"97007cf3-0000-0100-0000-686fd4880000\"", "_attachments": "attachments/", "_ts": 1752159368}, {"payPeriodId": "1140036221760371", "status": "COMPLETED", "description": "Void", "startDate": "2025-02-22T00:00:00Z", "endDate": "2025-03-07T00:00:00Z", "submitByDate": "2025-03-13T00:00:00Z", "checkDate": "2025-03-14T00:00:00Z", "checkCount": 1, "id": "89684efa-12de-4df7-adff-022284d37d84", "companyId": "19015542", "type": "payperiod", "_rid": "NmJkAKiCbEf6RgAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf6RgAAAAAAAA==/", "_etag": "\"97007df3-0000-0100-0000-686fd4890000\"", "_attachments": "attachments/", "_ts": 1752159369}, {"payPeriodId": "1140036222346156", "status": "COMPLETED", "description": "PAYCHEX FIX", "startDate": "2025-02-22T00:00:00Z", "endDate": "2025-03-07T00:00:00Z", "submitByDate": "2025-03-13T00:00:00Z", "checkDate": "2025-03-14T00:00:00Z", "checkCount": 1, "id": "ed97b603-2db3-470c-a2d7-52ae50a99af0", "companyId": "19015542", "type": "payperiod", "_rid": "NmJkAKiCbEf7RgAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf7RgAAAAAAAA==/", "_etag": "\"970080f3-0000-0100-0000-686fd4890000\"", "_attachments": "attachments/", "_ts": 1752159369}, {"payPeriodId": "1140035361804982", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-03-08T00:00:00Z", "endDate": "2025-03-21T00:00:00Z", "submitByDate": "2025-03-26T00:00:00Z", "checkDate": "2025-03-28T00:00:00Z", "checkCount": 42, "id": "9d7afa5b-fbc8-409e-8583-86030dccfba1", "companyId": "19015542", "type": "payperiod", "_rid": "NmJkAKiCbEf8RgAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf8RgAAAAAAAA==/", "_etag": "\"970087f3-0000-0100-0000-686fd4890000\"", "_attachments": "attachments/", "_ts": 1752159369}, {"payPeriodId": "1140036221760565", "status": "COMPLETED", "description": "Void", "startDate": "2025-03-08T00:00:00Z", "endDate": "2025-03-21T00:00:00Z", "submitByDate": "2025-03-27T00:00:00Z", "checkDate": "2025-03-28T00:00:00Z", "checkCount": 1, "id": "2f048eed-e837-48ab-8714-4e9df252fc3b", "companyId": "19015542", "type": "payperiod", "_rid": "NmJkAKiCbEf9RgAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf9RgAAAAAAAA==/", "_etag": "\"97008af3-0000-0100-0000-686fd4890000\"", "_attachments": "attachments/", "_ts": 1752159369}, {"payPeriodId": "1140036222346331", "status": "COMPLETED", "description": "PAYCHEX FIX", "startDate": "2025-03-08T00:00:00Z", "endDate": "2025-03-21T00:00:00Z", "submitByDate": "2025-03-27T00:00:00Z", "checkDate": "2025-03-28T00:00:00Z", "checkCount": 1, "id": "63dc97b1-e4ac-4801-9d8b-cda9078099eb", "companyId": "19015542", "type": "payperiod", "_rid": "NmJkAKiCbEf+RgAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf+RgAAAAAAAA==/", "_etag": "\"97008df3-0000-0100-0000-686fd4890000\"", "_attachments": "attachments/", "_ts": 1752159369}, {"payPeriodId": "1140035445096121", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-03-22T00:00:00Z", "endDate": "2025-04-04T00:00:00Z", "submitByDate": "2025-04-09T00:00:00Z", "checkDate": "2025-04-11T00:00:00Z", "checkCount": 41, "id": "7c2be118-3278-4250-8980-aca17559daa9", "companyId": "19015542", "type": "payperiod", "_rid": "NmJkAKiCbEf-RgAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf-RgAAAAAAAA==/", "_etag": "\"970093f3-0000-0100-0000-686fd4890000\"", "_attachments": "attachments/", "_ts": 1752159369}, {"payPeriodId": "1140036221760408", "status": "COMPLETED", "description": "Void", "startDate": "2025-03-22T00:00:00Z", "endDate": "2025-04-04T00:00:00Z", "submitByDate": "2025-04-10T00:00:00Z", "checkDate": "2025-04-11T00:00:00Z", "checkCount": 1, "id": "b9ffd35c-d1cb-48be-a18f-23b3cdce5234", "companyId": "19015542", "type": "payperiod", "_rid": "NmJkAKiCbEcARwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcARwAAAAAAAA==/", "_etag": "\"970099f3-0000-0100-0000-686fd4890000\"", "_attachments": "attachments/", "_ts": 1752159369}, {"payPeriodId": "1140036222346436", "status": "COMPLETED", "description": "PAYCHEX FIX", "startDate": "2025-03-22T00:00:00Z", "endDate": "2025-04-04T00:00:00Z", "submitByDate": "2025-04-10T00:00:00Z", "checkDate": "2025-04-11T00:00:00Z", "checkCount": 1, "id": "db626ddc-0d08-4b26-a617-486adc43ce8e", "companyId": "19015542", "type": "payperiod", "_rid": "NmJkAKiCbEcBRwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcBRwAAAAAAAA==/", "_etag": "\"97009ef3-0000-0100-0000-686fd4890000\"", "_attachments": "attachments/", "_ts": 1752159369}, {"payPeriodId": "1140035499971292", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-04-05T00:00:00Z", "endDate": "2025-04-18T00:00:00Z", "submitByDate": "2025-04-23T00:00:00Z", "checkDate": "2025-04-25T00:00:00Z", "checkCount": 46, "id": "02906684-a041-4260-8324-012099d291b3", "companyId": "19015542", "type": "payperiod", "_rid": "NmJkAKiCbEcCRwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcCRwAAAAAAAA==/", "_etag": "\"9700a3f3-0000-0100-0000-686fd4890000\"", "_attachments": "attachments/", "_ts": 1752159369}, {"payPeriodId": "1140036221760623", "status": "COMPLETED", "description": "Void", "startDate": "2025-04-05T00:00:00Z", "endDate": "2025-04-18T00:00:00Z", "submitByDate": "2025-04-24T00:00:00Z", "checkDate": "2025-04-25T00:00:00Z", "checkCount": 1, "id": "bb03197b-a064-443b-bbf9-09758a0cceea", "companyId": "19015542", "type": "payperiod", "_rid": "NmJkAKiCbEcDRwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcDRwAAAAAAAA==/", "_etag": "\"9700a7f3-0000-0100-0000-686fd4890000\"", "_attachments": "attachments/", "_ts": 1752159369}, {"payPeriodId": "1140036222424559", "status": "COMPLETED", "description": "PAYCHEX FIX", "startDate": "2025-04-05T00:00:00Z", "endDate": "2025-04-18T00:00:00Z", "submitByDate": "2025-04-24T00:00:00Z", "checkDate": "2025-04-25T00:00:00Z", "checkCount": 1, "id": "99a32cd3-c804-4a86-9b9e-b8d2c8d1b15c", "companyId": "19015542", "type": "payperiod", "_rid": "NmJkAKiCbEcERwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcERwAAAAAAAA==/", "_etag": "\"9700adf3-0000-0100-0000-686fd4890000\"", "_attachments": "attachments/", "_ts": 1752159369}, {"payPeriodId": "1140035553302281", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-04-19T00:00:00Z", "endDate": "2025-05-02T00:00:00Z", "submitByDate": "2025-05-07T00:00:00Z", "checkDate": "2025-05-09T00:00:00Z", "checkCount": 40, "id": "36a716c6-c19e-404e-8c19-a927bf8b0855", "companyId": "19015542", "type": "payperiod", "_rid": "NmJkAKiCbEcFRwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcFRwAAAAAAAA==/", "_etag": "\"9700b0f3-0000-0100-0000-686fd4890000\"", "_attachments": "attachments/", "_ts": 1752159369}, {"payPeriodId": "1140035614685539", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-05-03T00:00:00Z", "endDate": "2025-05-16T00:00:00Z", "submitByDate": "2025-05-21T00:00:00Z", "checkDate": "2025-05-23T00:00:00Z", "checkCount": 40, "id": "50a937fb-927a-4106-aa9a-1c0385e2b45c", "companyId": "19015542", "type": "payperiod", "_rid": "NmJkAKiCbEcGRwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcGRwAAAAAAAA==/", "_etag": "\"9700b2f3-0000-0100-0000-686fd4890000\"", "_attachments": "attachments/", "_ts": 1752159369}, {"payPeriodId": "1140035670435166", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-05-17T00:00:00Z", "endDate": "2025-05-30T00:00:00Z", "submitByDate": "2025-06-04T00:00:00Z", "checkDate": "2025-06-06T00:00:00Z", "checkCount": 41, "id": "72be719b-ad89-44f2-9768-62612439df35", "companyId": "19015542", "type": "payperiod", "_rid": "NmJkAKiCbEcHRwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcHRwAAAAAAAA==/", "_etag": "\"9700b3f3-0000-0100-0000-686fd4890000\"", "_attachments": "attachments/", "_ts": 1752159369}, {"payPeriodId": "1140035730221705", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-05-31T00:00:00Z", "endDate": "2025-06-13T00:00:00Z", "submitByDate": "2025-06-18T00:00:00Z", "checkDate": "2025-06-20T00:00:00Z", "checkCount": 39, "id": "144dec2b-1eb4-4b4e-b6eb-86527489059f", "companyId": "19015542", "type": "payperiod", "_rid": "NmJkAKiCbEcIRwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcIRwAAAAAAAA==/", "_etag": "\"9700b7f3-0000-0100-0000-686fd48a0000\"", "_attachments": "attachments/", "_ts": 1752159370}, {"payPeriodId": "1140036163302083", "status": "COMPLETED", "description": "Payroll", "startDate": "2025-05-31T00:00:00Z", "endDate": "2025-06-13T00:00:00Z", "submitByDate": "2025-06-19T00:00:00Z", "checkDate": "2025-06-20T00:00:00Z", "checkCount": 1, "id": "aba13cd3-47ba-491e-922e-7600a1df35bd", "companyId": "19015542", "type": "payperiod", "_rid": "NmJkAKiCbEcJRwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcJRwAAAAAAAA==/", "_etag": "\"9700baf3-0000-0100-0000-686fd48a0000\"", "_attachments": "attachments/", "_ts": 1752159370}, {"payPeriodId": "1140035783987094", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-06-14T00:00:00Z", "endDate": "2025-06-27T00:00:00Z", "submitByDate": "2025-07-01T00:00:00Z", "checkDate": "2025-07-03T00:00:00Z", "checkCount": 43, "id": "291e7b14-940a-467f-8bec-6e3b066bb379", "companyId": "19015542", "type": "payperiod", "_rid": "NmJkAKiCbEcKRwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcKRwAAAAAAAA==/", "_etag": "\"9700bcf3-0000-0100-0000-686fd48a0000\"", "_attachments": "attachments/", "_ts": 1752159370}, {"payPeriodId": "1140035845489011", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-06-28T00:00:00Z", "endDate": "2025-07-11T00:00:00Z", "submitByDate": "2025-07-16T00:00:00Z", "checkDate": "2025-07-18T00:00:00Z", "checkCount": 0, "id": "7d42264e-6ff2-4c4a-828b-ba582009fedc", "companyId": "19015542", "type": "payperiod", "_rid": "NmJkAKiCbEcLRwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcLRwAAAAAAAA==/", "_etag": "\"9700c2f3-0000-0100-0000-686fd48a0000\"", "_attachments": "attachments/", "_ts": 1752159370}, {"payPeriodId": "1140035905101051", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-07-12T00:00:00Z", "endDate": "2025-07-25T00:00:00Z", "submitByDate": "2025-07-30T00:00:00Z", "checkDate": "2025-08-01T00:00:00Z", "checkCount": 0, "id": "e6bc2f74-23ff-4f52-9f2d-d3770d463efe", "companyId": "19015542", "type": "payperiod", "_rid": "NmJkAKiCbEcMRwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcMRwAAAAAAAA==/", "_etag": "\"9700c4f3-0000-0100-0000-686fd48a0000\"", "_attachments": "attachments/", "_ts": 1752159370}, {"payPeriodId": "1140035966016207", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-07-26T00:00:00Z", "endDate": "2025-08-08T00:00:00Z", "submitByDate": "2025-08-13T00:00:00Z", "checkDate": "2025-08-15T00:00:00Z", "checkCount": 0, "id": "53c682bb-fe32-437f-8bfd-a78b681f3c5a", "companyId": "19015542", "type": "payperiod", "_rid": "NmJkAKiCbEcNRwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcNRwAAAAAAAA==/", "_etag": "\"9700c9f3-0000-0100-0000-686fd48a0000\"", "_attachments": "attachments/", "_ts": 1752159370}, {"payPeriodId": "1140036029336500", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-08-09T00:00:00Z", "endDate": "2025-08-22T00:00:00Z", "submitByDate": "2025-08-27T00:00:00Z", "checkDate": "2025-08-29T00:00:00Z", "checkCount": 0, "id": "1905fcc0-9cb7-4a53-93f1-2c406ad47164", "companyId": "19015542", "type": "payperiod", "_rid": "NmJkAKiCbEcORwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcORwAAAAAAAA==/", "_etag": "\"9700d1f3-0000-0100-0000-686fd48a0000\"", "_attachments": "attachments/", "_ts": 1752159370}, {"payPeriodId": "1140036082408790", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-08-23T00:00:00Z", "endDate": "2025-09-05T00:00:00Z", "submitByDate": "2025-09-10T00:00:00Z", "checkDate": "2025-09-12T00:00:00Z", "checkCount": 0, "id": "ee318fa8-186f-4434-8142-fc62d41403f4", "companyId": "19015542", "type": "payperiod", "_rid": "NmJkAKiCbEcPRwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcPRwAAAAAAAA==/", "_etag": "\"9700d5f3-0000-0100-0000-686fd48a0000\"", "_attachments": "attachments/", "_ts": 1752159370}, {"payPeriodId": "1140036144969842", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-09-06T00:00:00Z", "endDate": "2025-09-19T00:00:00Z", "submitByDate": "2025-09-24T00:00:00Z", "checkDate": "2025-09-26T00:00:00Z", "checkCount": 0, "id": "f4c46b4f-aeb0-4ee0-86d0-3b775e1ea5dd", "companyId": "19015542", "type": "payperiod", "_rid": "NmJkAKiCbEcQRwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcQRwAAAAAAAA==/", "_etag": "\"9700dbf3-0000-0100-0000-686fd48a0000\"", "_attachments": "attachments/", "_ts": 1752159370}, {"payPeriodId": "1140036200807826", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-09-20T00:00:00Z", "endDate": "2025-10-03T00:00:00Z", "submitByDate": "2025-10-08T00:00:00Z", "checkDate": "2025-10-10T00:00:00Z", "checkCount": 0, "id": "84528ae6-173a-4a39-8ad9-f8a21a0f7c83", "companyId": "19015542", "type": "payperiod", "_rid": "NmJkAKiCbEcRRwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcRRwAAAAAAAA==/", "_etag": "\"9700e5f3-0000-0100-0000-686fd48a0000\"", "_attachments": "attachments/", "_ts": 1752159370}, {"payPeriodId": "1140035058656571", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2024-12-28T00:00:00Z", "endDate": "2025-01-12T00:00:00Z", "submitByDate": "2025-01-15T00:00:00Z", "checkDate": "2025-01-17T00:00:00Z", "checkCount": 43, "id": "8943b69e-b15e-4660-980d-df3ca25e7082", "companyId": "19015542", "type": "payperiod", "_rid": "NmJkAKiCbEeOPwEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeOPwEAAAAAAA==/", "_etag": "\"9e003427-0000-0100-0000-686ffb180000\"", "_attachments": "attachments/", "_ts": 1752169240}, {"payPeriodId": "1140036221760296", "status": "COMPLETED", "description": "Void", "startDate": "2024-12-28T00:00:00Z", "endDate": "2025-01-12T00:00:00Z", "submitByDate": "2025-01-16T00:00:00Z", "checkDate": "2025-01-17T00:00:00Z", "checkCount": 1, "id": "45b22cf2-c272-424b-a1d9-bd9f32299c5d", "companyId": "19015542", "type": "payperiod", "_rid": "NmJkAKiCbEePPwEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEePPwEAAAAAAA==/", "_etag": "\"9e003927-0000-0100-0000-686ffb180000\"", "_attachments": "attachments/", "_ts": 1752169240}, {"payPeriodId": "1140036222345602", "status": "COMPLETED", "description": "PAYCHEX FIX", "startDate": "2024-12-28T00:00:00Z", "endDate": "2025-01-12T00:00:00Z", "submitByDate": "2025-01-16T00:00:00Z", "checkDate": "2025-01-17T00:00:00Z", "checkCount": 1, "id": "66687804-12de-42a9-82fb-4afa4abb4bb0", "companyId": "19015542", "type": "payperiod", "_rid": "NmJkAKiCbEeQPwEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeQPwEAAAAAAA==/", "_etag": "\"9e003a27-0000-0100-0000-686ffb180000\"", "_attachments": "attachments/", "_ts": 1752169240}, {"payPeriodId": "1140035117064141", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-01-11T00:00:00Z", "endDate": "2025-01-24T00:00:00Z", "submitByDate": "2025-01-29T00:00:00Z", "checkDate": "2025-01-31T00:00:00Z", "checkCount": 42, "id": "77dfad1b-0256-49d3-b6fb-79229eb2c9d2", "companyId": "19015542", "type": "payperiod", "_rid": "NmJkAKiCbEeRPwEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeRPwEAAAAAAA==/", "_etag": "\"9e003b27-0000-0100-0000-686ffb180000\"", "_attachments": "attachments/", "_ts": 1752169240}, {"payPeriodId": "1140036221760485", "status": "COMPLETED", "description": "Void", "startDate": "2025-01-11T00:00:00Z", "endDate": "2025-01-24T00:00:00Z", "submitByDate": "2025-01-30T00:00:00Z", "checkDate": "2025-01-31T00:00:00Z", "checkCount": 1, "id": "2eec7f3c-ec2b-46da-94da-79d196388fe6", "companyId": "19015542", "type": "payperiod", "_rid": "NmJkAKiCbEeSPwEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeSPwEAAAAAAA==/", "_etag": "\"9e003f27-0000-0100-0000-686ffb180000\"", "_attachments": "attachments/", "_ts": 1752169240}, {"payPeriodId": "1140036222345842", "status": "COMPLETED", "description": "PAYCHEX FIX", "startDate": "2025-01-11T00:00:00Z", "endDate": "2025-01-24T00:00:00Z", "submitByDate": "2025-01-30T00:00:00Z", "checkDate": "2025-01-31T00:00:00Z", "checkCount": 1, "id": "0e58b028-5bb2-411b-ac8d-be8761a4b4f7", "companyId": "19015542", "type": "payperiod", "_rid": "NmJkAKiCbEeTPwEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeTPwEAAAAAAA==/", "_etag": "\"9e004427-0000-0100-0000-686ffb180000\"", "_attachments": "attachments/", "_ts": 1752169240}, {"payPeriodId": "1140035183597002", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-01-25T00:00:00Z", "endDate": "2025-02-07T00:00:00Z", "submitByDate": "2025-02-12T00:00:00Z", "checkDate": "2025-02-14T00:00:00Z", "checkCount": 41, "id": "f93cbdda-9b42-4e3b-a82d-3a2420de31e3", "companyId": "19015542", "type": "payperiod", "_rid": "NmJkAKiCbEeUPwEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeUPwEAAAAAAA==/", "_etag": "\"9e004727-0000-0100-0000-686ffb180000\"", "_attachments": "attachments/", "_ts": 1752169240}, {"payPeriodId": "1140036221760334", "status": "COMPLETED", "description": "Void", "startDate": "2025-01-25T00:00:00Z", "endDate": "2025-02-07T00:00:00Z", "submitByDate": "2025-02-13T00:00:00Z", "checkDate": "2025-02-14T00:00:00Z", "checkCount": 1, "id": "44bbbee2-2343-4039-a446-088f6c100ede", "companyId": "19015542", "type": "payperiod", "_rid": "NmJkAKiCbEeVPwEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeVPwEAAAAAAA==/", "_etag": "\"9e004827-0000-0100-0000-686ffb180000\"", "_attachments": "attachments/", "_ts": 1752169240}, {"payPeriodId": "1140036222345964", "status": "COMPLETED", "description": "PAYCHEX FIX", "startDate": "2025-01-25T00:00:00Z", "endDate": "2025-02-07T00:00:00Z", "submitByDate": "2025-02-13T00:00:00Z", "checkDate": "2025-02-14T00:00:00Z", "checkCount": 1, "id": "d29e561c-e5e7-4e78-8674-4370d9760632", "companyId": "19015542", "type": "payperiod", "_rid": "NmJkAKiCbEeWPwEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeWPwEAAAAAAA==/", "_etag": "\"9e004f27-0000-0100-0000-686ffb180000\"", "_attachments": "attachments/", "_ts": 1752169240}, {"payPeriodId": "1140035237236324", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-02-08T00:00:00Z", "endDate": "2025-02-21T00:00:00Z", "submitByDate": "2025-02-26T00:00:00Z", "checkDate": "2025-02-28T00:00:00Z", "checkCount": 41, "id": "589f8a8f-bc05-4295-b292-e0725c7b09fb", "companyId": "19015542", "type": "payperiod", "_rid": "NmJkAKiCbEeXPwEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeXPwEAAAAAAA==/", "_etag": "\"9e005227-0000-0100-0000-686ffb180000\"", "_attachments": "attachments/", "_ts": 1752169240}, {"payPeriodId": "1140036221760527", "status": "COMPLETED", "description": "Void", "startDate": "2025-02-08T00:00:00Z", "endDate": "2025-02-21T00:00:00Z", "submitByDate": "2025-02-27T00:00:00Z", "checkDate": "2025-02-28T00:00:00Z", "checkCount": 1, "id": "0138401a-0dc0-43f2-90d3-7b3e39147d81", "companyId": "19015542", "type": "payperiod", "_rid": "NmJkAKiCbEeYPwEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeYPwEAAAAAAA==/", "_etag": "\"9e005527-0000-0100-0000-686ffb190000\"", "_attachments": "attachments/", "_ts": 1752169241}, {"payPeriodId": "1140036222346058", "status": "COMPLETED", "description": "PAYCHEX FIX", "startDate": "2025-02-08T00:00:00Z", "endDate": "2025-02-21T00:00:00Z", "submitByDate": "2025-02-27T00:00:00Z", "checkDate": "2025-02-28T00:00:00Z", "checkCount": 1, "id": "aee1ea12-4246-4773-8707-37a6507104fa", "companyId": "19015542", "type": "payperiod", "_rid": "NmJkAKiCbEeZPwEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeZPwEAAAAAAA==/", "_etag": "\"9e005827-0000-0100-0000-686ffb190000\"", "_attachments": "attachments/", "_ts": 1752169241}, {"payPeriodId": "1140035296441124", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-02-22T00:00:00Z", "endDate": "2025-03-07T00:00:00Z", "submitByDate": "2025-03-12T00:00:00Z", "checkDate": "2025-03-14T00:00:00Z", "checkCount": 42, "id": "a55ce24c-3476-47c0-8c88-2cc8a92e009d", "companyId": "19015542", "type": "payperiod", "_rid": "NmJkAKiCbEeaPwEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeaPwEAAAAAAA==/", "_etag": "\"9e005c27-0000-0100-0000-686ffb190000\"", "_attachments": "attachments/", "_ts": 1752169241}, {"payPeriodId": "1140036221760371", "status": "COMPLETED", "description": "Void", "startDate": "2025-02-22T00:00:00Z", "endDate": "2025-03-07T00:00:00Z", "submitByDate": "2025-03-13T00:00:00Z", "checkDate": "2025-03-14T00:00:00Z", "checkCount": 1, "id": "9b710434-4989-4246-b0e9-533114198e2d", "companyId": "19015542", "type": "payperiod", "_rid": "NmJkAKiCbEebPwEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEebPwEAAAAAAA==/", "_etag": "\"9e006227-0000-0100-0000-686ffb190000\"", "_attachments": "attachments/", "_ts": 1752169241}, {"payPeriodId": "1140036222346156", "status": "COMPLETED", "description": "PAYCHEX FIX", "startDate": "2025-02-22T00:00:00Z", "endDate": "2025-03-07T00:00:00Z", "submitByDate": "2025-03-13T00:00:00Z", "checkDate": "2025-03-14T00:00:00Z", "checkCount": 1, "id": "f1500011-2cef-471e-af12-99af409f5417", "companyId": "19015542", "type": "payperiod", "_rid": "NmJkAKiCbEecPwEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEecPwEAAAAAAA==/", "_etag": "\"9e006927-0000-0100-0000-686ffb190000\"", "_attachments": "attachments/", "_ts": 1752169241}, {"payPeriodId": "1140035361804982", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-03-08T00:00:00Z", "endDate": "2025-03-21T00:00:00Z", "submitByDate": "2025-03-26T00:00:00Z", "checkDate": "2025-03-28T00:00:00Z", "checkCount": 42, "id": "173f305d-a4ee-4e2d-b4e5-51f335c62316", "companyId": "19015542", "type": "payperiod", "_rid": "NmJkAKiCbEedPwEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEedPwEAAAAAAA==/", "_etag": "\"9e006c27-0000-0100-0000-686ffb190000\"", "_attachments": "attachments/", "_ts": 1752169241}, {"payPeriodId": "1140036221760565", "status": "COMPLETED", "description": "Void", "startDate": "2025-03-08T00:00:00Z", "endDate": "2025-03-21T00:00:00Z", "submitByDate": "2025-03-27T00:00:00Z", "checkDate": "2025-03-28T00:00:00Z", "checkCount": 1, "id": "c3d893a0-6ae9-4f26-918e-bda1f73b97b3", "companyId": "19015542", "type": "payperiod", "_rid": "NmJkAKiCbEeePwEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeePwEAAAAAAA==/", "_etag": "\"9e007327-0000-0100-0000-686ffb190000\"", "_attachments": "attachments/", "_ts": 1752169241}, {"payPeriodId": "1140036222346331", "status": "COMPLETED", "description": "PAYCHEX FIX", "startDate": "2025-03-08T00:00:00Z", "endDate": "2025-03-21T00:00:00Z", "submitByDate": "2025-03-27T00:00:00Z", "checkDate": "2025-03-28T00:00:00Z", "checkCount": 1, "id": "62b7fb0f-c959-4c24-9083-7a63b1e8b921", "companyId": "19015542", "type": "payperiod", "_rid": "NmJkAKiCbEefPwEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEefPwEAAAAAAA==/", "_etag": "\"9e007427-0000-0100-0000-686ffb190000\"", "_attachments": "attachments/", "_ts": 1752169241}, {"payPeriodId": "1140035445096121", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-03-22T00:00:00Z", "endDate": "2025-04-04T00:00:00Z", "submitByDate": "2025-04-09T00:00:00Z", "checkDate": "2025-04-11T00:00:00Z", "checkCount": 0, "id": "b7f7a35b-42b8-4ddf-968f-54f9a0dbb601", "companyId": "19015542", "type": "payperiod", "_rid": "NmJkAKiCbEegPwEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEegPwEAAAAAAA==/", "_etag": "\"9e007a27-0000-0100-0000-686ffb190000\"", "_attachments": "attachments/", "_ts": 1752169241}, {"payPeriodId": "1140036221760408", "status": "INITIAL", "description": "Void", "startDate": "2025-03-22T00:00:00Z", "endDate": "2025-04-04T00:00:00Z", "submitByDate": "2025-04-10T00:00:00Z", "checkDate": "2025-04-11T00:00:00Z", "checkCount": 0, "id": "d83df672-b10e-4cd9-bc9d-53e099c22d1c", "companyId": "19015542", "type": "payperiod", "_rid": "NmJkAKiCbEehPwEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEehPwEAAAAAAA==/", "_etag": "\"9e007c27-0000-0100-0000-686ffb190000\"", "_attachments": "attachments/", "_ts": 1752169241}, {"payPeriodId": "1140036222346436", "status": "INITIAL", "description": "PAYCHEX FIX", "startDate": "2025-03-22T00:00:00Z", "endDate": "2025-04-04T00:00:00Z", "submitByDate": "2025-04-10T00:00:00Z", "checkDate": "2025-04-11T00:00:00Z", "checkCount": 0, "id": "5f777dac-76da-4fed-9065-76c7726b5308", "companyId": "19015542", "type": "payperiod", "_rid": "NmJkAKiCbEeiPwEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeiPwEAAAAAAA==/", "_etag": "\"9e007e27-0000-0100-0000-686ffb190000\"", "_attachments": "attachments/", "_ts": 1752169241}, {"payPeriodId": "1140035499971292", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-04-05T00:00:00Z", "endDate": "2025-04-18T00:00:00Z", "submitByDate": "2025-04-23T00:00:00Z", "checkDate": "2025-04-25T00:00:00Z", "checkCount": 0, "id": "074be5c9-5dfe-4a7c-b5d6-3566d565c333", "companyId": "19015542", "type": "payperiod", "_rid": "NmJkAKiCbEejPwEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEejPwEAAAAAAA==/", "_etag": "\"9e008227-0000-0100-0000-686ffb190000\"", "_attachments": "attachments/", "_ts": 1752169241}, {"payPeriodId": "1140036221760623", "status": "INITIAL", "description": "Void", "startDate": "2025-04-05T00:00:00Z", "endDate": "2025-04-18T00:00:00Z", "submitByDate": "2025-04-24T00:00:00Z", "checkDate": "2025-04-25T00:00:00Z", "checkCount": 0, "id": "3c662d6a-444d-4838-a6a7-c1d285b43b76", "companyId": "19015542", "type": "payperiod", "_rid": "NmJkAKiCbEekPwEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEekPwEAAAAAAA==/", "_etag": "\"9e008327-0000-0100-0000-686ffb190000\"", "_attachments": "attachments/", "_ts": 1752169241}, {"payPeriodId": "1140036222424559", "status": "INITIAL", "description": "PAYCHEX FIX", "startDate": "2025-04-05T00:00:00Z", "endDate": "2025-04-18T00:00:00Z", "submitByDate": "2025-04-24T00:00:00Z", "checkDate": "2025-04-25T00:00:00Z", "checkCount": 0, "id": "41bd954c-2e4b-443e-9aea-bdbe67e5cac5", "companyId": "19015542", "type": "payperiod", "_rid": "NmJkAKiCbEelPwEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEelPwEAAAAAAA==/", "_etag": "\"9e008827-0000-0100-0000-686ffb1a0000\"", "_attachments": "attachments/", "_ts": 1752169242}, {"payPeriodId": "1140035553302281", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-04-19T00:00:00Z", "endDate": "2025-05-02T00:00:00Z", "submitByDate": "2025-05-07T00:00:00Z", "checkDate": "2025-05-09T00:00:00Z", "checkCount": 0, "id": "3d31d8f2-0b4c-406d-9464-9f08a49beac5", "companyId": "19015542", "type": "payperiod", "_rid": "NmJkAKiCbEemPwEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEemPwEAAAAAAA==/", "_etag": "\"9e008a27-0000-0100-0000-686ffb1a0000\"", "_attachments": "attachments/", "_ts": 1752169242}, {"payPeriodId": "1140035614685539", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-05-03T00:00:00Z", "endDate": "2025-05-16T00:00:00Z", "submitByDate": "2025-05-21T00:00:00Z", "checkDate": "2025-05-23T00:00:00Z", "checkCount": 0, "id": "e6c282e7-9c34-45e8-8f7b-66a235f0dbf7", "companyId": "19015542", "type": "payperiod", "_rid": "NmJkAKiCbEenPwEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEenPwEAAAAAAA==/", "_etag": "\"9e008c27-0000-0100-0000-686ffb1a0000\"", "_attachments": "attachments/", "_ts": 1752169242}, {"payPeriodId": "1140035670435166", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-05-17T00:00:00Z", "endDate": "2025-05-30T00:00:00Z", "submitByDate": "2025-06-04T00:00:00Z", "checkDate": "2025-06-06T00:00:00Z", "checkCount": 0, "id": "44bb8836-057e-44cf-a259-a37e389ded9c", "companyId": "19015542", "type": "payperiod", "_rid": "NmJkAKiCbEeoPwEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeoPwEAAAAAAA==/", "_etag": "\"9e008f27-0000-0100-0000-686ffb1a0000\"", "_attachments": "attachments/", "_ts": 1752169242}, {"payPeriodId": "1140035730221705", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-05-31T00:00:00Z", "endDate": "2025-06-13T00:00:00Z", "submitByDate": "2025-06-18T00:00:00Z", "checkDate": "2025-06-20T00:00:00Z", "checkCount": 0, "id": "9259cc54-e937-4593-ba47-e96a69a13ec0", "companyId": "19015542", "type": "payperiod", "_rid": "NmJkAKiCbEepPwEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEepPwEAAAAAAA==/", "_etag": "\"9e009327-0000-0100-0000-686ffb1a0000\"", "_attachments": "attachments/", "_ts": 1752169242}, {"payPeriodId": "1140036163302083", "status": "INITIAL", "description": "Payroll", "startDate": "2025-05-31T00:00:00Z", "endDate": "2025-06-13T00:00:00Z", "submitByDate": "2025-06-19T00:00:00Z", "checkDate": "2025-06-20T00:00:00Z", "checkCount": 0, "id": "7e29b6dc-129b-4712-917a-f2fc0bfd0025", "companyId": "19015542", "type": "payperiod", "_rid": "NmJkAKiCbEeqPwEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeqPwEAAAAAAA==/", "_etag": "\"9e009527-0000-0100-0000-686ffb1a0000\"", "_attachments": "attachments/", "_ts": 1752169242}, {"payPeriodId": "1140035783987094", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-06-14T00:00:00Z", "endDate": "2025-06-27T00:00:00Z", "submitByDate": "2025-07-01T00:00:00Z", "checkDate": "2025-07-03T00:00:00Z", "checkCount": 0, "id": "f5753131-d2bc-4e90-8d31-95d16f169e7b", "companyId": "19015542", "type": "payperiod", "_rid": "NmJkAKiCbEerPwEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEerPwEAAAAAAA==/", "_etag": "\"9e009c27-0000-0100-0000-686ffb1a0000\"", "_attachments": "attachments/", "_ts": 1752169242}, {"payPeriodId": "1140035845489011", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-06-28T00:00:00Z", "endDate": "2025-07-11T00:00:00Z", "submitByDate": "2025-07-16T00:00:00Z", "checkDate": "2025-07-18T00:00:00Z", "checkCount": 0, "id": "3a5831d7-a394-4eec-8f4e-a24bd0b05454", "companyId": "19015542", "type": "payperiod", "_rid": "NmJkAKiCbEesPwEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEesPwEAAAAAAA==/", "_etag": "\"9e00a127-0000-0100-0000-686ffb1a0000\"", "_attachments": "attachments/", "_ts": 1752169242}, {"payPeriodId": "1140035905101051", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-07-12T00:00:00Z", "endDate": "2025-07-25T00:00:00Z", "submitByDate": "2025-07-30T00:00:00Z", "checkDate": "2025-08-01T00:00:00Z", "checkCount": 0, "id": "1b0523f2-740b-4709-a5a2-965aab8f5cb7", "companyId": "19015542", "type": "payperiod", "_rid": "NmJkAKiCbEetPwEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEetPwEAAAAAAA==/", "_etag": "\"9e00af27-0000-0100-0000-686ffb1a0000\"", "_attachments": "attachments/", "_ts": 1752169242}, {"payPeriodId": "1140035966016207", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-07-26T00:00:00Z", "endDate": "2025-08-08T00:00:00Z", "submitByDate": "2025-08-13T00:00:00Z", "checkDate": "2025-08-15T00:00:00Z", "checkCount": 0, "id": "e299a34d-c109-4dc7-a27e-b6f0a0db9aa6", "companyId": "19015542", "type": "payperiod", "_rid": "NmJkAKiCbEeuPwEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeuPwEAAAAAAA==/", "_etag": "\"9e00b327-0000-0100-0000-686ffb1a0000\"", "_attachments": "attachments/", "_ts": 1752169242}, {"payPeriodId": "1140036029336500", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-08-09T00:00:00Z", "endDate": "2025-08-22T00:00:00Z", "submitByDate": "2025-08-27T00:00:00Z", "checkDate": "2025-08-29T00:00:00Z", "checkCount": 0, "id": "2c28e6c8-aade-4002-b581-e6c2b6538393", "companyId": "19015542", "type": "payperiod", "_rid": "NmJkAKiCbEevPwEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEevPwEAAAAAAA==/", "_etag": "\"9e00b627-0000-0100-0000-686ffb1a0000\"", "_attachments": "attachments/", "_ts": 1752169242}, {"payPeriodId": "1140036082408790", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-08-23T00:00:00Z", "endDate": "2025-09-05T00:00:00Z", "submitByDate": "2025-09-10T00:00:00Z", "checkDate": "2025-09-12T00:00:00Z", "checkCount": 0, "id": "5e0da52a-4e25-4b09-9f54-191825819313", "companyId": "19015542", "type": "payperiod", "_rid": "NmJkAKiCbEewPwEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEewPwEAAAAAAA==/", "_etag": "\"9e00ba27-0000-0100-0000-686ffb1a0000\"", "_attachments": "attachments/", "_ts": 1752169242}, {"payPeriodId": "1140036144969842", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-09-06T00:00:00Z", "endDate": "2025-09-19T00:00:00Z", "submitByDate": "2025-09-24T00:00:00Z", "checkDate": "2025-09-26T00:00:00Z", "checkCount": 0, "id": "a310db58-819b-4a12-ac5b-15aeb2ebbd2f", "companyId": "19015542", "type": "payperiod", "_rid": "NmJkAKiCbEexPwEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEexPwEAAAAAAA==/", "_etag": "\"9e00bc27-0000-0100-0000-686ffb1a0000\"", "_attachments": "attachments/", "_ts": 1752169242}, {"payPeriodId": "1140036200807826", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-09-20T00:00:00Z", "endDate": "2025-10-03T00:00:00Z", "submitByDate": "2025-10-08T00:00:00Z", "checkDate": "2025-10-10T00:00:00Z", "checkCount": 0, "id": "f7d1001e-65ee-4203-9f45-4e74614eb3d9", "companyId": "19015542", "type": "payperiod", "_rid": "NmJkAKiCbEeyPwEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeyPwEAAAAAAA==/", "_etag": "\"9e00be27-0000-0100-0000-686ffb1b0000\"", "_attachments": "attachments/", "_ts": 1752169243}, {"payPeriodId": "1140035058656571", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2024-12-28T00:00:00Z", "endDate": "2025-01-12T00:00:00Z", "submitByDate": "2025-01-15T00:00:00Z", "checkDate": "2025-01-17T00:00:00Z", "checkCount": 43, "id": "e6b19c36-a2b7-417d-a885-fa24a2f9cd51", "companyId": "19015542", "type": "payperiod", "_rid": "NmJkAKiCbEcaQAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcaQAEAAAAAAA==/", "_etag": "\"9e00b029-0000-0100-0000-686ffb240000\"", "_attachments": "attachments/", "_ts": 1752169252}, {"payPeriodId": "1140036221760296", "status": "COMPLETED", "description": "Void", "startDate": "2024-12-28T00:00:00Z", "endDate": "2025-01-12T00:00:00Z", "submitByDate": "2025-01-16T00:00:00Z", "checkDate": "2025-01-17T00:00:00Z", "checkCount": 1, "id": "070aa537-5083-4049-8222-22bb8472582b", "companyId": "19015542", "type": "payperiod", "_rid": "NmJkAKiCbEcbQAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcbQAEAAAAAAA==/", "_etag": "\"9e00b529-0000-0100-0000-686ffb240000\"", "_attachments": "attachments/", "_ts": 1752169252}, {"payPeriodId": "1140036222345602", "status": "COMPLETED", "description": "PAYCHEX FIX", "startDate": "2024-12-28T00:00:00Z", "endDate": "2025-01-12T00:00:00Z", "submitByDate": "2025-01-16T00:00:00Z", "checkDate": "2025-01-17T00:00:00Z", "checkCount": 1, "id": "f7373534-db72-4952-bb91-65651e6c9c55", "companyId": "19015542", "type": "payperiod", "_rid": "NmJkAKiCbEccQAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEccQAEAAAAAAA==/", "_etag": "\"9e00ba29-0000-0100-0000-686ffb240000\"", "_attachments": "attachments/", "_ts": 1752169252}, {"payPeriodId": "1140035117064141", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-01-11T00:00:00Z", "endDate": "2025-01-24T00:00:00Z", "submitByDate": "2025-01-29T00:00:00Z", "checkDate": "2025-01-31T00:00:00Z", "checkCount": 42, "id": "e080582a-cc35-473e-838b-c06da5bd89c9", "companyId": "19015542", "type": "payperiod", "_rid": "NmJkAKiCbEcdQAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcdQAEAAAAAAA==/", "_etag": "\"9e00c229-0000-0100-0000-686ffb240000\"", "_attachments": "attachments/", "_ts": 1752169252}, {"payPeriodId": "1140036221760485", "status": "COMPLETED", "description": "Void", "startDate": "2025-01-11T00:00:00Z", "endDate": "2025-01-24T00:00:00Z", "submitByDate": "2025-01-30T00:00:00Z", "checkDate": "2025-01-31T00:00:00Z", "checkCount": 1, "id": "00e06028-a646-4002-80e0-28363e59ebc3", "companyId": "19015542", "type": "payperiod", "_rid": "NmJkAKiCbEceQAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEceQAEAAAAAAA==/", "_etag": "\"9e00c429-0000-0100-0000-686ffb240000\"", "_attachments": "attachments/", "_ts": 1752169252}, {"payPeriodId": "1140036222345842", "status": "COMPLETED", "description": "PAYCHEX FIX", "startDate": "2025-01-11T00:00:00Z", "endDate": "2025-01-24T00:00:00Z", "submitByDate": "2025-01-30T00:00:00Z", "checkDate": "2025-01-31T00:00:00Z", "checkCount": 1, "id": "7eed3ec3-abf9-457c-9ad8-b07bcabe74a3", "companyId": "19015542", "type": "payperiod", "_rid": "NmJkAKiCbEcfQAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcfQAEAAAAAAA==/", "_etag": "\"9e00ce29-0000-0100-0000-686ffb240000\"", "_attachments": "attachments/", "_ts": 1752169252}, {"payPeriodId": "1140035183597002", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-01-25T00:00:00Z", "endDate": "2025-02-07T00:00:00Z", "submitByDate": "2025-02-12T00:00:00Z", "checkDate": "2025-02-14T00:00:00Z", "checkCount": 41, "id": "6bbe0344-1953-46f1-83cf-acb62e900606", "companyId": "19015542", "type": "payperiod", "_rid": "NmJkAKiCbEcgQAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcgQAEAAAAAAA==/", "_etag": "\"9e00d329-0000-0100-0000-686ffb240000\"", "_attachments": "attachments/", "_ts": 1752169252}, {"payPeriodId": "1140036221760334", "status": "COMPLETED", "description": "Void", "startDate": "2025-01-25T00:00:00Z", "endDate": "2025-02-07T00:00:00Z", "submitByDate": "2025-02-13T00:00:00Z", "checkDate": "2025-02-14T00:00:00Z", "checkCount": 1, "id": "f380e3f5-f25f-4788-9268-ba1c22ab79fd", "companyId": "19015542", "type": "payperiod", "_rid": "NmJkAKiCbEchQAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEchQAEAAAAAAA==/", "_etag": "\"9e00d629-0000-0100-0000-686ffb240000\"", "_attachments": "attachments/", "_ts": 1752169252}, {"payPeriodId": "1140036222345964", "status": "COMPLETED", "description": "PAYCHEX FIX", "startDate": "2025-01-25T00:00:00Z", "endDate": "2025-02-07T00:00:00Z", "submitByDate": "2025-02-13T00:00:00Z", "checkDate": "2025-02-14T00:00:00Z", "checkCount": 1, "id": "fe328673-da0a-4354-a06d-07228368c09e", "companyId": "19015542", "type": "payperiod", "_rid": "NmJkAKiCbEciQAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEciQAEAAAAAAA==/", "_etag": "\"9e00da29-0000-0100-0000-686ffb240000\"", "_attachments": "attachments/", "_ts": 1752169252}, {"payPeriodId": "1140035237236324", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-02-08T00:00:00Z", "endDate": "2025-02-21T00:00:00Z", "submitByDate": "2025-02-26T00:00:00Z", "checkDate": "2025-02-28T00:00:00Z", "checkCount": 41, "id": "e442b8a6-d2eb-4c7c-b2a1-dd25dca5d6aa", "companyId": "19015542", "type": "payperiod", "_rid": "NmJkAKiCbEcjQAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcjQAEAAAAAAA==/", "_etag": "\"9e00dd29-0000-0100-0000-686ffb240000\"", "_attachments": "attachments/", "_ts": 1752169252}, {"payPeriodId": "1140036221760527", "status": "COMPLETED", "description": "Void", "startDate": "2025-02-08T00:00:00Z", "endDate": "2025-02-21T00:00:00Z", "submitByDate": "2025-02-27T00:00:00Z", "checkDate": "2025-02-28T00:00:00Z", "checkCount": 1, "id": "6f7454fb-4a5a-4f4a-9256-d5b7a2e5d94e", "companyId": "19015542", "type": "payperiod", "_rid": "NmJkAKiCbEckQAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEckQAEAAAAAAA==/", "_etag": "\"9e00e129-0000-0100-0000-686ffb250000\"", "_attachments": "attachments/", "_ts": 1752169253}, {"payPeriodId": "1140036222346058", "status": "COMPLETED", "description": "PAYCHEX FIX", "startDate": "2025-02-08T00:00:00Z", "endDate": "2025-02-21T00:00:00Z", "submitByDate": "2025-02-27T00:00:00Z", "checkDate": "2025-02-28T00:00:00Z", "checkCount": 1, "id": "4303dee4-c6b0-4773-8b3f-aca5287bc254", "companyId": "19015542", "type": "payperiod", "_rid": "NmJkAKiCbEclQAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEclQAEAAAAAAA==/", "_etag": "\"9e00e429-0000-0100-0000-686ffb250000\"", "_attachments": "attachments/", "_ts": 1752169253}, {"payPeriodId": "1140035296441124", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-02-22T00:00:00Z", "endDate": "2025-03-07T00:00:00Z", "submitByDate": "2025-03-12T00:00:00Z", "checkDate": "2025-03-14T00:00:00Z", "checkCount": 42, "id": "1fd7a25e-fc5b-45c3-9ab1-8a12e589b5e6", "companyId": "19015542", "type": "payperiod", "_rid": "NmJkAKiCbEcmQAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcmQAEAAAAAAA==/", "_etag": "\"9e00e929-0000-0100-0000-686ffb250000\"", "_attachments": "attachments/", "_ts": 1752169253}, {"payPeriodId": "1140036221760371", "status": "COMPLETED", "description": "Void", "startDate": "2025-02-22T00:00:00Z", "endDate": "2025-03-07T00:00:00Z", "submitByDate": "2025-03-13T00:00:00Z", "checkDate": "2025-03-14T00:00:00Z", "checkCount": 1, "id": "df2f9050-4341-4e68-8b2e-5e9d2857b3db", "companyId": "19015542", "type": "payperiod", "_rid": "NmJkAKiCbEcnQAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcnQAEAAAAAAA==/", "_etag": "\"9e00ed29-0000-0100-0000-686ffb250000\"", "_attachments": "attachments/", "_ts": 1752169253}, {"payPeriodId": "1140036222346156", "status": "COMPLETED", "description": "PAYCHEX FIX", "startDate": "2025-02-22T00:00:00Z", "endDate": "2025-03-07T00:00:00Z", "submitByDate": "2025-03-13T00:00:00Z", "checkDate": "2025-03-14T00:00:00Z", "checkCount": 1, "id": "0b8ff3ee-2d77-4569-bda0-c2873b6c4b4e", "companyId": "19015542", "type": "payperiod", "_rid": "NmJkAKiCbEcoQAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcoQAEAAAAAAA==/", "_etag": "\"9e00f029-0000-0100-0000-686ffb250000\"", "_attachments": "attachments/", "_ts": 1752169253}, {"payPeriodId": "1140035361804982", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-03-08T00:00:00Z", "endDate": "2025-03-21T00:00:00Z", "submitByDate": "2025-03-26T00:00:00Z", "checkDate": "2025-03-28T00:00:00Z", "checkCount": 42, "id": "e16deb10-48a5-4efd-ad3b-c18dc03d644e", "companyId": "19015542", "type": "payperiod", "_rid": "NmJkAKiCbEcpQAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcpQAEAAAAAAA==/", "_etag": "\"9e00f329-0000-0100-0000-686ffb250000\"", "_attachments": "attachments/", "_ts": 1752169253}, {"payPeriodId": "1140036221760565", "status": "COMPLETED", "description": "Void", "startDate": "2025-03-08T00:00:00Z", "endDate": "2025-03-21T00:00:00Z", "submitByDate": "2025-03-27T00:00:00Z", "checkDate": "2025-03-28T00:00:00Z", "checkCount": 1, "id": "a7a15607-de6f-498d-be5b-3470acf12641", "companyId": "19015542", "type": "payperiod", "_rid": "NmJkAKiCbEcqQAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcqQAEAAAAAAA==/", "_etag": "\"9e00f629-0000-0100-0000-686ffb250000\"", "_attachments": "attachments/", "_ts": 1752169253}, {"payPeriodId": "1140036222346331", "status": "COMPLETED", "description": "PAYCHEX FIX", "startDate": "2025-03-08T00:00:00Z", "endDate": "2025-03-21T00:00:00Z", "submitByDate": "2025-03-27T00:00:00Z", "checkDate": "2025-03-28T00:00:00Z", "checkCount": 1, "id": "803fca39-ed23-49fe-a88e-753262f09276", "companyId": "19015542", "type": "payperiod", "_rid": "NmJkAKiCbEcrQAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcrQAEAAAAAAA==/", "_etag": "\"9e00f829-0000-0100-0000-686ffb250000\"", "_attachments": "attachments/", "_ts": 1752169253}, {"payPeriodId": "1140035445096121", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-03-22T00:00:00Z", "endDate": "2025-04-04T00:00:00Z", "submitByDate": "2025-04-09T00:00:00Z", "checkDate": "2025-04-11T00:00:00Z", "checkCount": 41, "id": "60e551ba-3916-48ff-8bcf-4fb55b08391e", "companyId": "19015542", "type": "payperiod", "_rid": "NmJkAKiCbEcsQAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcsQAEAAAAAAA==/", "_etag": "\"9e00fb29-0000-0100-0000-686ffb250000\"", "_attachments": "attachments/", "_ts": 1752169253}, {"payPeriodId": "1140036221760408", "status": "COMPLETED", "description": "Void", "startDate": "2025-03-22T00:00:00Z", "endDate": "2025-04-04T00:00:00Z", "submitByDate": "2025-04-10T00:00:00Z", "checkDate": "2025-04-11T00:00:00Z", "checkCount": 1, "id": "feb7e43d-2eb8-4ebe-bd4b-b66367e95f99", "companyId": "19015542", "type": "payperiod", "_rid": "NmJkAKiCbEctQAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEctQAEAAAAAAA==/", "_etag": "\"9e00ff29-0000-0100-0000-686ffb250000\"", "_attachments": "attachments/", "_ts": 1752169253}, {"payPeriodId": "1140036222346436", "status": "COMPLETED", "description": "PAYCHEX FIX", "startDate": "2025-03-22T00:00:00Z", "endDate": "2025-04-04T00:00:00Z", "submitByDate": "2025-04-10T00:00:00Z", "checkDate": "2025-04-11T00:00:00Z", "checkCount": 1, "id": "1b401312-a4ec-4722-be3a-26b0051b6d84", "companyId": "19015542", "type": "payperiod", "_rid": "NmJkAKiCbEcuQAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcuQAEAAAAAAA==/", "_etag": "\"9e00012a-0000-0100-0000-686ffb250000\"", "_attachments": "attachments/", "_ts": 1752169253}, {"payPeriodId": "1140035499971292", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-04-05T00:00:00Z", "endDate": "2025-04-18T00:00:00Z", "submitByDate": "2025-04-23T00:00:00Z", "checkDate": "2025-04-25T00:00:00Z", "checkCount": 46, "id": "675f91d4-d3d6-40ce-a8e6-97fcb1b7b647", "companyId": "19015542", "type": "payperiod", "_rid": "NmJkAKiCbEcvQAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcvQAEAAAAAAA==/", "_etag": "\"9e00082a-0000-0100-0000-686ffb250000\"", "_attachments": "attachments/", "_ts": 1752169253}, {"payPeriodId": "1140036221760623", "status": "COMPLETED", "description": "Void", "startDate": "2025-04-05T00:00:00Z", "endDate": "2025-04-18T00:00:00Z", "submitByDate": "2025-04-24T00:00:00Z", "checkDate": "2025-04-25T00:00:00Z", "checkCount": 1, "id": "a1155219-2616-4aa2-b151-49db44b189b0", "companyId": "19015542", "type": "payperiod", "_rid": "NmJkAKiCbEcwQAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcwQAEAAAAAAA==/", "_etag": "\"9e000d2a-0000-0100-0000-686ffb250000\"", "_attachments": "attachments/", "_ts": 1752169253}, {"payPeriodId": "1140036222424559", "status": "COMPLETED", "description": "PAYCHEX FIX", "startDate": "2025-04-05T00:00:00Z", "endDate": "2025-04-18T00:00:00Z", "submitByDate": "2025-04-24T00:00:00Z", "checkDate": "2025-04-25T00:00:00Z", "checkCount": 1, "id": "43b3e356-24ba-4a8b-994b-da12da4fe340", "companyId": "19015542", "type": "payperiod", "_rid": "NmJkAKiCbEcxQAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcxQAEAAAAAAA==/", "_etag": "\"9e00142a-0000-0100-0000-686ffb260000\"", "_attachments": "attachments/", "_ts": 1752169254}, {"payPeriodId": "1140035553302281", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-04-19T00:00:00Z", "endDate": "2025-05-02T00:00:00Z", "submitByDate": "2025-05-07T00:00:00Z", "checkDate": "2025-05-09T00:00:00Z", "checkCount": 40, "id": "76903b1e-c358-4c93-8ae9-e8dc3a005980", "companyId": "19015542", "type": "payperiod", "_rid": "NmJkAKiCbEcyQAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcyQAEAAAAAAA==/", "_etag": "\"9e001a2a-0000-0100-0000-686ffb260000\"", "_attachments": "attachments/", "_ts": 1752169254}, {"payPeriodId": "1140035614685539", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-05-03T00:00:00Z", "endDate": "2025-05-16T00:00:00Z", "submitByDate": "2025-05-21T00:00:00Z", "checkDate": "2025-05-23T00:00:00Z", "checkCount": 40, "id": "275388e7-5384-4655-810e-25d4890cc314", "companyId": "19015542", "type": "payperiod", "_rid": "NmJkAKiCbEczQAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEczQAEAAAAAAA==/", "_etag": "\"9e001c2a-0000-0100-0000-686ffb260000\"", "_attachments": "attachments/", "_ts": 1752169254}, {"payPeriodId": "1140035670435166", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-05-17T00:00:00Z", "endDate": "2025-05-30T00:00:00Z", "submitByDate": "2025-06-04T00:00:00Z", "checkDate": "2025-06-06T00:00:00Z", "checkCount": 41, "id": "04ec4147-5cc0-4321-95ff-1a06d69fd0a6", "companyId": "19015542", "type": "payperiod", "_rid": "NmJkAKiCbEc0QAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc0QAEAAAAAAA==/", "_etag": "\"9e00202a-0000-0100-0000-686ffb260000\"", "_attachments": "attachments/", "_ts": 1752169254}, {"payPeriodId": "1140035730221705", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-05-31T00:00:00Z", "endDate": "2025-06-13T00:00:00Z", "submitByDate": "2025-06-18T00:00:00Z", "checkDate": "2025-06-20T00:00:00Z", "checkCount": 39, "id": "73f47ffc-f7a7-4168-bd56-99864a72abbc", "companyId": "19015542", "type": "payperiod", "_rid": "NmJkAKiCbEc1QAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc1QAEAAAAAAA==/", "_etag": "\"9e00222a-0000-0100-0000-686ffb260000\"", "_attachments": "attachments/", "_ts": 1752169254}, {"payPeriodId": "1140036163302083", "status": "COMPLETED", "description": "Payroll", "startDate": "2025-05-31T00:00:00Z", "endDate": "2025-06-13T00:00:00Z", "submitByDate": "2025-06-19T00:00:00Z", "checkDate": "2025-06-20T00:00:00Z", "checkCount": 1, "id": "46dc2cc9-f334-44c8-bfb8-bb1325efbf87", "companyId": "19015542", "type": "payperiod", "_rid": "NmJkAKiCbEc2QAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc2QAEAAAAAAA==/", "_etag": "\"9e00232a-0000-0100-0000-686ffb260000\"", "_attachments": "attachments/", "_ts": 1752169254}, {"payPeriodId": "1140035783987094", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-06-14T00:00:00Z", "endDate": "2025-06-27T00:00:00Z", "submitByDate": "2025-07-01T00:00:00Z", "checkDate": "2025-07-03T00:00:00Z", "checkCount": 43, "id": "19d56b3a-d267-4dcf-a522-8113421bd401", "companyId": "19015542", "type": "payperiod", "_rid": "NmJkAKiCbEc3QAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc3QAEAAAAAAA==/", "_etag": "\"9e00262a-0000-0100-0000-686ffb260000\"", "_attachments": "attachments/", "_ts": 1752169254}, {"payPeriodId": "1140035845489011", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-06-28T00:00:00Z", "endDate": "2025-07-11T00:00:00Z", "submitByDate": "2025-07-16T00:00:00Z", "checkDate": "2025-07-18T00:00:00Z", "checkCount": 0, "id": "02c6cfaa-fe83-4f23-bfa9-219bc9bc0c9d", "companyId": "19015542", "type": "payperiod", "_rid": "NmJkAKiCbEc4QAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc4QAEAAAAAAA==/", "_etag": "\"9e002a2a-0000-0100-0000-686ffb260000\"", "_attachments": "attachments/", "_ts": 1752169254}, {"payPeriodId": "1140035905101051", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-07-12T00:00:00Z", "endDate": "2025-07-25T00:00:00Z", "submitByDate": "2025-07-30T00:00:00Z", "checkDate": "2025-08-01T00:00:00Z", "checkCount": 0, "id": "eb332148-8079-4166-92d4-61f114a53817", "companyId": "19015542", "type": "payperiod", "_rid": "NmJkAKiCbEc5QAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc5QAEAAAAAAA==/", "_etag": "\"9e002d2a-0000-0100-0000-686ffb260000\"", "_attachments": "attachments/", "_ts": 1752169254}, {"payPeriodId": "1140035966016207", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-07-26T00:00:00Z", "endDate": "2025-08-08T00:00:00Z", "submitByDate": "2025-08-13T00:00:00Z", "checkDate": "2025-08-15T00:00:00Z", "checkCount": 0, "id": "7bb51ebb-ae85-4dbe-be4c-3dae38e88c36", "companyId": "19015542", "type": "payperiod", "_rid": "NmJkAKiCbEc6QAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc6QAEAAAAAAA==/", "_etag": "\"9e00302a-0000-0100-0000-686ffb260000\"", "_attachments": "attachments/", "_ts": 1752169254}, {"payPeriodId": "1140036029336500", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-08-09T00:00:00Z", "endDate": "2025-08-22T00:00:00Z", "submitByDate": "2025-08-27T00:00:00Z", "checkDate": "2025-08-29T00:00:00Z", "checkCount": 0, "id": "9f33c668-1e50-4fa7-bb58-9e5b2658f153", "companyId": "19015542", "type": "payperiod", "_rid": "NmJkAKiCbEc7QAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc7QAEAAAAAAA==/", "_etag": "\"9e00322a-0000-0100-0000-686ffb260000\"", "_attachments": "attachments/", "_ts": 1752169254}, {"payPeriodId": "1140036082408790", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-08-23T00:00:00Z", "endDate": "2025-09-05T00:00:00Z", "submitByDate": "2025-09-10T00:00:00Z", "checkDate": "2025-09-12T00:00:00Z", "checkCount": 0, "id": "9bf1dfb3-f249-486b-990e-03f27b759b31", "companyId": "19015542", "type": "payperiod", "_rid": "NmJkAKiCbEc8QAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc8QAEAAAAAAA==/", "_etag": "\"9e00342a-0000-0100-0000-686ffb260000\"", "_attachments": "attachments/", "_ts": 1752169254}, {"payPeriodId": "1140036144969842", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-09-06T00:00:00Z", "endDate": "2025-09-19T00:00:00Z", "submitByDate": "2025-09-24T00:00:00Z", "checkDate": "2025-09-26T00:00:00Z", "checkCount": 0, "id": "7ef7051f-e1dd-448d-9671-af9328de6261", "companyId": "19015542", "type": "payperiod", "_rid": "NmJkAKiCbEc9QAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc9QAEAAAAAAA==/", "_etag": "\"9e00372a-0000-0100-0000-686ffb260000\"", "_attachments": "attachments/", "_ts": 1752169254}, {"payPeriodId": "1140036200807826", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-09-20T00:00:00Z", "endDate": "2025-10-03T00:00:00Z", "submitByDate": "2025-10-08T00:00:00Z", "checkDate": "2025-10-10T00:00:00Z", "checkCount": 0, "id": "048954cc-1ce0-4e99-8c93-662fc6dc3d11", "companyId": "19015542", "type": "payperiod", "_rid": "NmJkAKiCbEc+QAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc+QAEAAAAAAA==/", "_etag": "\"9e00392a-0000-0100-0000-686ffb270000\"", "_attachments": "attachments/", "_ts": 1752169255}], "links": [{"rel": "self", "href": "https://ca-payroll-ai-mock-sb-001-secure.nicebeach-8a7a52ab.eastus.azurecontainerapps.io/ca/companies/19015542/payperiods"}]}, "status_code": 200}