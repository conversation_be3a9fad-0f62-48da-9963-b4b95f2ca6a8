{"success": true, "company_id": "15031532", "data": {"metadata": {"contentItemCount": 80}, "content": [{"payPeriodId": "1060038933308061", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2024-12-28T00:00:00Z", "endDate": "2025-01-03T00:00:00Z", "submitByDate": "2024-12-31T00:00:00Z", "checkDate": "2025-01-03T00:00:00Z", "checkCount": 4, "id": "89cf1703-c7ad-4244-9100-8807b5e42f59", "companyId": "15031532", "type": "payperiod", "_rid": "NmJkAKiCbEcjlAQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcjlAQAAAAAAA==/", "_etag": "\"a900ca09-0000-0100-0000-687044580000\"", "_attachments": "attachments/", "_ts": 1752187992}, {"payPeriodId": "1060038969720868", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-01-04T00:00:00Z", "endDate": "2025-01-10T00:00:00Z", "submitByDate": "2025-01-08T00:00:00Z", "checkDate": "2025-01-10T00:00:00Z", "checkCount": 4, "id": "57dd19b0-bbb5-4bc9-9da0-d234dddd98d6", "companyId": "15031532", "type": "payperiod", "_rid": "NmJkAKiCbEcklAQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcklAQAAAAAAA==/", "_etag": "\"a900cf09-0000-0100-0000-687044580000\"", "_attachments": "attachments/", "_ts": 1752187992}, {"payPeriodId": "1060039005758740", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-01-11T00:00:00Z", "endDate": "2025-01-17T00:00:00Z", "submitByDate": "2025-01-15T00:00:00Z", "checkDate": "2025-01-17T00:00:00Z", "checkCount": 4, "id": "cb394c2b-1713-4b57-bf0b-e42df3d4a3b6", "companyId": "15031532", "type": "payperiod", "_rid": "NmJkAKiCbEcllAQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcllAQAAAAAAA==/", "_etag": "\"a900d009-0000-0100-0000-687044580000\"", "_attachments": "attachments/", "_ts": 1752187992}, {"payPeriodId": "1060039042204347", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-01-18T00:00:00Z", "endDate": "2025-01-24T00:00:00Z", "submitByDate": "2025-01-22T00:00:00Z", "checkDate": "2025-01-24T00:00:00Z", "checkCount": 4, "id": "d7dac1ae-7f28-470a-b961-20a18c4cf366", "companyId": "15031532", "type": "payperiod", "_rid": "NmJkAKiCbEcmlAQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcmlAQAAAAAAA==/", "_etag": "\"a900d409-0000-0100-0000-687044580000\"", "_attachments": "attachments/", "_ts": 1752187992}, {"payPeriodId": "1060039082227536", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-01-25T00:00:00Z", "endDate": "2025-01-31T00:00:00Z", "submitByDate": "2025-01-29T00:00:00Z", "checkDate": "2025-01-31T00:00:00Z", "checkCount": 4, "id": "e8bd999d-28c9-4f8a-a741-816d0d452fbf", "companyId": "15031532", "type": "payperiod", "_rid": "NmJkAKiCbEcnlAQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcnlAQAAAAAAA==/", "_etag": "\"a900d709-0000-0100-0000-687044580000\"", "_attachments": "attachments/", "_ts": 1752187992}, {"payPeriodId": "1060039122334302", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-02-01T00:00:00Z", "endDate": "2025-02-07T00:00:00Z", "submitByDate": "2025-02-05T00:00:00Z", "checkDate": "2025-02-07T00:00:00Z", "checkCount": 4, "id": "094f146e-c183-4f22-97b0-52f280505570", "companyId": "15031532", "type": "payperiod", "_rid": "NmJkAKiCbEcolAQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcolAQAAAAAAA==/", "_etag": "\"a900da09-0000-0100-0000-687044580000\"", "_attachments": "attachments/", "_ts": 1752187992}, {"payPeriodId": "1060039155195562", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-02-08T00:00:00Z", "endDate": "2025-02-14T00:00:00Z", "submitByDate": "2025-02-12T00:00:00Z", "checkDate": "2025-02-14T00:00:00Z", "checkCount": 4, "id": "7511da05-f8ab-4fae-ac4a-fc51a20bc180", "companyId": "15031532", "type": "payperiod", "_rid": "NmJkAKiCbEcplAQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcplAQAAAAAAA==/", "_etag": "\"a900db09-0000-0100-0000-687044580000\"", "_attachments": "attachments/", "_ts": 1752187992}, {"payPeriodId": "1060039191971326", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-02-15T00:00:00Z", "endDate": "2025-02-21T00:00:00Z", "submitByDate": "2025-02-19T00:00:00Z", "checkDate": "2025-02-21T00:00:00Z", "checkCount": 4, "id": "1c7abc34-48ca-4efb-a591-7d7d090b6912", "companyId": "15031532", "type": "payperiod", "_rid": "NmJkAKiCbEcqlAQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcqlAQAAAAAAA==/", "_etag": "\"a900dd09-0000-0100-0000-687044580000\"", "_attachments": "attachments/", "_ts": 1752187992}, {"payPeriodId": "1060039226766561", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-02-22T00:00:00Z", "endDate": "2025-02-28T00:00:00Z", "submitByDate": "2025-02-26T00:00:00Z", "checkDate": "2025-02-28T00:00:00Z", "checkCount": 4, "id": "c575cc0f-2aa0-475c-aab8-5d5bbae8768a", "companyId": "15031532", "type": "payperiod", "_rid": "NmJkAKiCbEcrlAQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcrlAQAAAAAAA==/", "_etag": "\"a900de09-0000-0100-0000-687044580000\"", "_attachments": "attachments/", "_ts": 1752187992}, {"payPeriodId": "1060039285923675", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-03-01T00:00:00Z", "endDate": "2025-03-07T00:00:00Z", "submitByDate": "2025-03-05T00:00:00Z", "checkDate": "2025-03-07T00:00:00Z", "checkCount": 4, "id": "67b9e425-1a33-4551-9ca6-c9c9e76611ce", "companyId": "15031532", "type": "payperiod", "_rid": "NmJkAKiCbEcslAQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcslAQAAAAAAA==/", "_etag": "\"a900e009-0000-0100-0000-687044590000\"", "_attachments": "attachments/", "_ts": 1752187993}, {"payPeriodId": "1060039330030164", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-03-08T00:00:00Z", "endDate": "2025-03-14T00:00:00Z", "submitByDate": "2025-03-12T00:00:00Z", "checkDate": "2025-03-14T00:00:00Z", "checkCount": 4, "id": "209b08d1-63b0-429c-beff-8ecc2c185761", "companyId": "15031532", "type": "payperiod", "_rid": "NmJkAKiCbEctlAQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEctlAQAAAAAAA==/", "_etag": "\"a900e209-0000-0100-0000-687044590000\"", "_attachments": "attachments/", "_ts": 1752187993}, {"payPeriodId": "1060039368697206", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-03-15T00:00:00Z", "endDate": "2025-03-21T00:00:00Z", "submitByDate": "2025-03-19T00:00:00Z", "checkDate": "2025-03-21T00:00:00Z", "checkCount": 4, "id": "6f007fe6-c657-4cce-9ddb-01df0f393167", "companyId": "15031532", "type": "payperiod", "_rid": "NmJkAKiCbEculAQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEculAQAAAAAAA==/", "_etag": "\"a900e409-0000-0100-0000-687044590000\"", "_attachments": "attachments/", "_ts": 1752187993}, {"payPeriodId": "1060039409940776", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-03-22T00:00:00Z", "endDate": "2025-03-28T00:00:00Z", "submitByDate": "2025-03-26T00:00:00Z", "checkDate": "2025-03-28T00:00:00Z", "checkCount": 4, "id": "3d35b970-43d8-4553-81ff-31647bd974b1", "companyId": "15031532", "type": "payperiod", "_rid": "NmJkAKiCbEcvlAQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcvlAQAAAAAAA==/", "_etag": "\"a900e609-0000-0100-0000-687044590000\"", "_attachments": "attachments/", "_ts": 1752187993}, {"payPeriodId": "1060039445628073", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-03-29T00:00:00Z", "endDate": "2025-04-04T00:00:00Z", "submitByDate": "2025-04-02T00:00:00Z", "checkDate": "2025-04-04T00:00:00Z", "checkCount": 0, "id": "05b696eb-aa49-47d7-855a-93151c2264a3", "companyId": "15031532", "type": "payperiod", "_rid": "NmJkAKiCbEcwlAQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcwlAQAAAAAAA==/", "_etag": "\"a900e809-0000-0100-0000-687044590000\"", "_attachments": "attachments/", "_ts": 1752187993}, {"payPeriodId": "1060039479118729", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-04-05T00:00:00Z", "endDate": "2025-04-11T00:00:00Z", "submitByDate": "2025-04-09T00:00:00Z", "checkDate": "2025-04-11T00:00:00Z", "checkCount": 0, "id": "f2d74a9f-690f-4526-ae64-0c98d989883c", "companyId": "15031532", "type": "payperiod", "_rid": "NmJkAKiCbEcxlAQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcxlAQAAAAAAA==/", "_etag": "\"a900e909-0000-0100-0000-687044590000\"", "_attachments": "attachments/", "_ts": 1752187993}, {"payPeriodId": "1060039515844780", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-04-12T00:00:00Z", "endDate": "2025-04-18T00:00:00Z", "submitByDate": "2025-04-16T00:00:00Z", "checkDate": "2025-04-18T00:00:00Z", "checkCount": 0, "id": "179dfe23-58da-430e-8639-e182291e53c4", "companyId": "15031532", "type": "payperiod", "_rid": "NmJkAKiCbEcylAQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcylAQAAAAAAA==/", "_etag": "\"a900eb09-0000-0100-0000-687044590000\"", "_attachments": "attachments/", "_ts": 1752187993}, {"payPeriodId": "1060039547932859", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-04-19T00:00:00Z", "endDate": "2025-04-25T00:00:00Z", "submitByDate": "2025-04-23T00:00:00Z", "checkDate": "2025-04-25T00:00:00Z", "checkCount": 0, "id": "02fbea5d-71fc-4d37-9782-f3046d4fbee7", "companyId": "15031532", "type": "payperiod", "_rid": "NmJkAKiCbEczlAQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEczlAQAAAAAAA==/", "_etag": "\"a900ec09-0000-0100-0000-687044590000\"", "_attachments": "attachments/", "_ts": 1752187993}, {"payPeriodId": "1060039584663064", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-04-26T00:00:00Z", "endDate": "2025-05-02T00:00:00Z", "submitByDate": "2025-04-30T00:00:00Z", "checkDate": "2025-05-02T00:00:00Z", "checkCount": 0, "id": "70c957b7-7cbf-4c0b-a682-2d1eec6ddb6c", "companyId": "15031532", "type": "payperiod", "_rid": "NmJkAKiCbEc0lAQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc0lAQAAAAAAA==/", "_etag": "\"a900ee09-0000-0100-0000-687044590000\"", "_attachments": "attachments/", "_ts": 1752187993}, {"payPeriodId": "1060039620233548", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-05-03T00:00:00Z", "endDate": "2025-05-09T00:00:00Z", "submitByDate": "2025-05-07T00:00:00Z", "checkDate": "2025-05-09T00:00:00Z", "checkCount": 0, "id": "2060c38a-b752-4166-9741-8b6753b87078", "companyId": "15031532", "type": "payperiod", "_rid": "NmJkAKiCbEc1lAQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc1lAQAAAAAAA==/", "_etag": "\"a900f209-0000-0100-0000-687044590000\"", "_attachments": "attachments/", "_ts": 1752187993}, {"payPeriodId": "1060039686692344", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-05-17T00:00:00Z", "endDate": "2025-05-23T00:00:00Z", "submitByDate": "2025-05-21T00:00:00Z", "checkDate": "2025-05-23T00:00:00Z", "checkCount": 0, "id": "529784c3-9b77-4f9c-8bfe-b410bb9a9412", "companyId": "15031532", "type": "payperiod", "_rid": "NmJkAKiCbEc2lAQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc2lAQAAAAAAA==/", "_etag": "\"a900f409-0000-0100-0000-687044590000\"", "_attachments": "attachments/", "_ts": 1752187993}, {"payPeriodId": "1060039724963935", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-05-24T00:00:00Z", "endDate": "2025-05-30T00:00:00Z", "submitByDate": "2025-05-28T00:00:00Z", "checkDate": "2025-05-30T00:00:00Z", "checkCount": 0, "id": "1642589c-7e31-4d9b-937d-6fa0a6d3be4f", "companyId": "15031532", "type": "payperiod", "_rid": "NmJkAKiCbEc3lAQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc3lAQAAAAAAA==/", "_etag": "\"a900f509-0000-0100-0000-687044590000\"", "_attachments": "attachments/", "_ts": 1752187993}, {"payPeriodId": "1060039727885391", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-05-31T00:00:00Z", "endDate": "2025-06-06T00:00:00Z", "submitByDate": "2025-06-04T00:00:00Z", "checkDate": "2025-06-06T00:00:00Z", "checkCount": 0, "id": "789b6935-a4da-46ac-b5c4-75292f1ab7e4", "companyId": "15031532", "type": "payperiod", "_rid": "NmJkAKiCbEc4lAQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc4lAQAAAAAAA==/", "_etag": "\"a900f609-0000-0100-0000-687044590000\"", "_attachments": "attachments/", "_ts": 1752187993}, {"payPeriodId": "1060039760116072", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-06-07T00:00:00Z", "endDate": "2025-06-13T00:00:00Z", "submitByDate": "2025-06-11T00:00:00Z", "checkDate": "2025-06-13T00:00:00Z", "checkCount": 0, "id": "c0428bda-0619-400e-96bf-5ce32b887930", "companyId": "15031532", "type": "payperiod", "_rid": "NmJkAKiCbEc5lAQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc5lAQAAAAAAA==/", "_etag": "\"a900f709-0000-0100-0000-6870445a0000\"", "_attachments": "attachments/", "_ts": 1752187994}, {"payPeriodId": "1060039795279203", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-06-14T00:00:00Z", "endDate": "2025-06-20T00:00:00Z", "submitByDate": "2025-06-18T00:00:00Z", "checkDate": "2025-06-20T00:00:00Z", "checkCount": 0, "id": "e9123bac-d387-4b38-bd9a-66b7aad6941e", "companyId": "15031532", "type": "payperiod", "_rid": "NmJkAKiCbEc6lAQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc6lAQAAAAAAA==/", "_etag": "\"a900fa09-0000-0100-0000-6870445a0000\"", "_attachments": "attachments/", "_ts": 1752187994}, {"payPeriodId": "1060039827732842", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-06-21T00:00:00Z", "endDate": "2025-06-27T00:00:00Z", "submitByDate": "2025-06-25T00:00:00Z", "checkDate": "2025-06-27T00:00:00Z", "checkCount": 0, "id": "cbc9b1af-3378-4cb1-a417-66acd3725d16", "companyId": "15031532", "type": "payperiod", "_rid": "NmJkAKiCbEc7lAQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc7lAQAAAAAAA==/", "_etag": "\"a900010a-0000-0100-0000-6870445a0000\"", "_attachments": "attachments/", "_ts": 1752187994}, {"payPeriodId": "1060039864787427", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-06-28T00:00:00Z", "endDate": "2025-07-04T00:00:00Z", "submitByDate": "2025-07-01T00:00:00Z", "checkDate": "2025-07-03T00:00:00Z", "checkCount": 0, "id": "c656c170-a63f-45ba-a781-99348455fbe6", "companyId": "15031532", "type": "payperiod", "_rid": "NmJkAKiCbEc8lAQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc8lAQAAAAAAA==/", "_etag": "\"a900030a-0000-0100-0000-6870445a0000\"", "_attachments": "attachments/", "_ts": 1752187994}, {"payPeriodId": "1060039904943648", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-07-05T00:00:00Z", "endDate": "2025-07-11T00:00:00Z", "submitByDate": "2025-07-09T00:00:00Z", "checkDate": "2025-07-11T00:00:00Z", "checkCount": 0, "id": "b457db8d-60c1-48b0-a8bf-d67ccaae6b83", "companyId": "15031532", "type": "payperiod", "_rid": "NmJkAKiCbEc9lAQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc9lAQAAAAAAA==/", "_etag": "\"a900050a-0000-0100-0000-6870445a0000\"", "_attachments": "attachments/", "_ts": 1752187994}, {"payPeriodId": "1060039943322494", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-07-12T00:00:00Z", "endDate": "2025-07-18T00:00:00Z", "submitByDate": "2025-07-16T00:00:00Z", "checkDate": "2025-07-18T00:00:00Z", "checkCount": 0, "id": "fd11b33a-71a5-402c-973b-1ebb831a8791", "companyId": "15031532", "type": "payperiod", "_rid": "NmJkAKiCbEc+lAQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc+lAQAAAAAAA==/", "_etag": "\"a900070a-0000-0100-0000-6870445a0000\"", "_attachments": "attachments/", "_ts": 1752187994}, {"payPeriodId": "1060039979225599", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-07-19T00:00:00Z", "endDate": "2025-07-25T00:00:00Z", "submitByDate": "2025-07-23T00:00:00Z", "checkDate": "2025-07-25T00:00:00Z", "checkCount": 0, "id": "4cd60145-bceb-42b2-a3f3-d8d226ba9341", "companyId": "15031532", "type": "payperiod", "_rid": "NmJkAKiCbEc-lAQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc-lAQAAAAAAA==/", "_etag": "\"a900080a-0000-0100-0000-6870445a0000\"", "_attachments": "attachments/", "_ts": 1752187994}, {"payPeriodId": "1060040014069262", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-07-26T00:00:00Z", "endDate": "2025-08-01T00:00:00Z", "submitByDate": "2025-07-30T00:00:00Z", "checkDate": "2025-08-01T00:00:00Z", "checkCount": 0, "id": "47343d2d-8fa1-4064-9041-c220ce208b62", "companyId": "15031532", "type": "payperiod", "_rid": "NmJkAKiCbEdAlAQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdAlAQAAAAAAA==/", "_etag": "\"a9000b0a-0000-0100-0000-6870445a0000\"", "_attachments": "attachments/", "_ts": 1752187994}, {"payPeriodId": "1060040055903632", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-08-02T00:00:00Z", "endDate": "2025-08-08T00:00:00Z", "submitByDate": "2025-08-06T00:00:00Z", "checkDate": "2025-08-08T00:00:00Z", "checkCount": 0, "id": "c9194351-2375-4d08-8489-472e5411d622", "companyId": "15031532", "type": "payperiod", "_rid": "NmJkAKiCbEdBlAQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdBlAQAAAAAAA==/", "_etag": "\"a9000d0a-0000-0100-0000-6870445a0000\"", "_attachments": "attachments/", "_ts": 1752187994}, {"payPeriodId": "1060040090004339", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-08-09T00:00:00Z", "endDate": "2025-08-15T00:00:00Z", "submitByDate": "2025-08-13T00:00:00Z", "checkDate": "2025-08-15T00:00:00Z", "checkCount": 0, "id": "bb900bad-cdb9-4428-8347-0899e65cc2c8", "companyId": "15031532", "type": "payperiod", "_rid": "NmJkAKiCbEdClAQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdClAQAAAAAAA==/", "_etag": "\"a9000f0a-0000-0100-0000-6870445a0000\"", "_attachments": "attachments/", "_ts": 1752187994}, {"payPeriodId": "1060040127397580", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-08-16T00:00:00Z", "endDate": "2025-08-22T00:00:00Z", "submitByDate": "2025-08-20T00:00:00Z", "checkDate": "2025-08-22T00:00:00Z", "checkCount": 0, "id": "b5d5afa3-5e34-46b3-a252-1d95aa162500", "companyId": "15031532", "type": "payperiod", "_rid": "NmJkAKiCbEdDlAQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdDlAQAAAAAAA==/", "_etag": "\"a900100a-0000-0100-0000-6870445a0000\"", "_attachments": "attachments/", "_ts": 1752187994}, {"payPeriodId": "1060040160351460", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-08-23T00:00:00Z", "endDate": "2025-08-29T00:00:00Z", "submitByDate": "2025-08-27T00:00:00Z", "checkDate": "2025-08-29T00:00:00Z", "checkCount": 0, "id": "2b7f61f9-a9e5-490e-9fee-0e957961bacf", "companyId": "15031532", "type": "payperiod", "_rid": "NmJkAKiCbEdElAQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdElAQAAAAAAA==/", "_etag": "\"a900130a-0000-0100-0000-6870445a0000\"", "_attachments": "attachments/", "_ts": 1752187994}, {"payPeriodId": "1060040196142616", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-08-30T00:00:00Z", "endDate": "2025-09-05T00:00:00Z", "submitByDate": "2025-09-03T00:00:00Z", "checkDate": "2025-09-05T00:00:00Z", "checkCount": 0, "id": "ece5f8c9-1882-44d0-9d07-f9262258149c", "companyId": "15031532", "type": "payperiod", "_rid": "NmJkAKiCbEdFlAQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdFlAQAAAAAAA==/", "_etag": "\"a900160a-0000-0100-0000-6870445a0000\"", "_attachments": "attachments/", "_ts": 1752187994}, {"payPeriodId": "1060040228953828", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-09-06T00:00:00Z", "endDate": "2025-09-12T00:00:00Z", "submitByDate": "2025-09-10T00:00:00Z", "checkDate": "2025-09-12T00:00:00Z", "checkCount": 0, "id": "3f6829a0-ad30-4a8f-a6b8-05371974c29e", "companyId": "15031532", "type": "payperiod", "_rid": "NmJkAKiCbEdGlAQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdGlAQAAAAAAA==/", "_etag": "\"a900180a-0000-0100-0000-6870445a0000\"", "_attachments": "attachments/", "_ts": 1752187994}, {"payPeriodId": "1060040264176530", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-09-13T00:00:00Z", "endDate": "2025-09-19T00:00:00Z", "submitByDate": "2025-09-17T00:00:00Z", "checkDate": "2025-09-19T00:00:00Z", "checkCount": 0, "id": "6ee53978-8259-4a40-a8b6-aa927fae6558", "companyId": "15031532", "type": "payperiod", "_rid": "NmJkAKiCbEdHlAQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdHlAQAAAAAAA==/", "_etag": "\"a900190a-0000-0100-0000-6870445b0000\"", "_attachments": "attachments/", "_ts": 1752187995}, {"payPeriodId": "1060040297299147", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-09-20T00:00:00Z", "endDate": "2025-09-26T00:00:00Z", "submitByDate": "2025-09-24T00:00:00Z", "checkDate": "2025-09-26T00:00:00Z", "checkCount": 0, "id": "d4262b64-2d7a-4d28-85f1-b8d2c63e350a", "companyId": "15031532", "type": "payperiod", "_rid": "NmJkAKiCbEdIlAQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdIlAQAAAAAAA==/", "_etag": "\"a9001a0a-0000-0100-0000-6870445b0000\"", "_attachments": "attachments/", "_ts": 1752187995}, {"payPeriodId": "1060040335252052", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-09-27T00:00:00Z", "endDate": "2025-10-03T00:00:00Z", "submitByDate": "2025-10-01T00:00:00Z", "checkDate": "2025-10-03T00:00:00Z", "checkCount": 0, "id": "9c1c6dcc-0fa7-49ba-9686-468406166804", "companyId": "15031532", "type": "payperiod", "_rid": "NmJkAKiCbEdJlAQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdJlAQAAAAAAA==/", "_etag": "\"a9001c0a-0000-0100-0000-6870445b0000\"", "_attachments": "attachments/", "_ts": 1752187995}, {"payPeriodId": "1060040369679211", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-10-04T00:00:00Z", "endDate": "2025-10-10T00:00:00Z", "submitByDate": "2025-10-08T00:00:00Z", "checkDate": "2025-10-10T00:00:00Z", "checkCount": 0, "id": "4b60518b-c188-4af6-b1fd-a8f1ae96399c", "companyId": "15031532", "type": "payperiod", "_rid": "NmJkAKiCbEdKlAQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdKlAQAAAAAAA==/", "_etag": "\"a9001e0a-0000-0100-0000-6870445b0000\"", "_attachments": "attachments/", "_ts": 1752187995}, {"payPeriodId": "1060038933308061", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2024-12-28T00:00:00Z", "endDate": "2025-01-03T00:00:00Z", "submitByDate": "2024-12-31T00:00:00Z", "checkDate": "2025-01-03T00:00:00Z", "checkCount": 4, "id": "67351eee-b3d3-4a57-91e4-b31bc538a789", "companyId": "15031532", "type": "payperiod", "_rid": "NmJkAKiCbEdQlAQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdQlAQAAAAAAA==/", "_etag": "\"a9002e0a-0000-0100-0000-6870445b0000\"", "_attachments": "attachments/", "_ts": 1752187995}, {"payPeriodId": "1060038969720868", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-01-04T00:00:00Z", "endDate": "2025-01-10T00:00:00Z", "submitByDate": "2025-01-08T00:00:00Z", "checkDate": "2025-01-10T00:00:00Z", "checkCount": 4, "id": "fddae4b2-1a8f-4b77-a880-0e804415162c", "companyId": "15031532", "type": "payperiod", "_rid": "NmJkAKiCbEdRlAQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdRlAQAAAAAAA==/", "_etag": "\"a900310a-0000-0100-0000-6870445b0000\"", "_attachments": "attachments/", "_ts": 1752187995}, {"payPeriodId": "1060039005758740", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-01-11T00:00:00Z", "endDate": "2025-01-17T00:00:00Z", "submitByDate": "2025-01-15T00:00:00Z", "checkDate": "2025-01-17T00:00:00Z", "checkCount": 4, "id": "30a470cb-abd1-4620-a9e5-b7a08caabe3c", "companyId": "15031532", "type": "payperiod", "_rid": "NmJkAKiCbEdSlAQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdSlAQAAAAAAA==/", "_etag": "\"a900320a-0000-0100-0000-6870445b0000\"", "_attachments": "attachments/", "_ts": 1752187995}, {"payPeriodId": "1060039042204347", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-01-18T00:00:00Z", "endDate": "2025-01-24T00:00:00Z", "submitByDate": "2025-01-22T00:00:00Z", "checkDate": "2025-01-24T00:00:00Z", "checkCount": 4, "id": "853dde00-f51e-4ca2-9f9e-b911dec6f7c8", "companyId": "15031532", "type": "payperiod", "_rid": "NmJkAKiCbEdTlAQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdTlAQAAAAAAA==/", "_etag": "\"a900350a-0000-0100-0000-6870445b0000\"", "_attachments": "attachments/", "_ts": 1752187995}, {"payPeriodId": "1060039082227536", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-01-25T00:00:00Z", "endDate": "2025-01-31T00:00:00Z", "submitByDate": "2025-01-29T00:00:00Z", "checkDate": "2025-01-31T00:00:00Z", "checkCount": 4, "id": "d43a9e23-5ad2-4f47-a8c0-325dd1889801", "companyId": "15031532", "type": "payperiod", "_rid": "NmJkAKiCbEdUlAQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdUlAQAAAAAAA==/", "_etag": "\"a900360a-0000-0100-0000-6870445c0000\"", "_attachments": "attachments/", "_ts": 1752187996}, {"payPeriodId": "1060039122334302", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-02-01T00:00:00Z", "endDate": "2025-02-07T00:00:00Z", "submitByDate": "2025-02-05T00:00:00Z", "checkDate": "2025-02-07T00:00:00Z", "checkCount": 4, "id": "0aa369e1-1090-4c2e-82a8-a304a0fe24ac", "companyId": "15031532", "type": "payperiod", "_rid": "NmJkAKiCbEdVlAQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdVlAQAAAAAAA==/", "_etag": "\"a900380a-0000-0100-0000-6870445c0000\"", "_attachments": "attachments/", "_ts": 1752187996}, {"payPeriodId": "1060039155195562", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-02-08T00:00:00Z", "endDate": "2025-02-14T00:00:00Z", "submitByDate": "2025-02-12T00:00:00Z", "checkDate": "2025-02-14T00:00:00Z", "checkCount": 4, "id": "80269b8f-4a5b-40be-988d-f0eee84c7375", "companyId": "15031532", "type": "payperiod", "_rid": "NmJkAKiCbEdWlAQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdWlAQAAAAAAA==/", "_etag": "\"a900390a-0000-0100-0000-6870445c0000\"", "_attachments": "attachments/", "_ts": 1752187996}, {"payPeriodId": "1060039191971326", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-02-15T00:00:00Z", "endDate": "2025-02-21T00:00:00Z", "submitByDate": "2025-02-19T00:00:00Z", "checkDate": "2025-02-21T00:00:00Z", "checkCount": 4, "id": "647ac749-761d-4a9d-8975-5f3bc206640b", "companyId": "15031532", "type": "payperiod", "_rid": "NmJkAKiCbEdXlAQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdXlAQAAAAAAA==/", "_etag": "\"a9003a0a-0000-0100-0000-6870445c0000\"", "_attachments": "attachments/", "_ts": 1752187996}, {"payPeriodId": "1060039226766561", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-02-22T00:00:00Z", "endDate": "2025-02-28T00:00:00Z", "submitByDate": "2025-02-26T00:00:00Z", "checkDate": "2025-02-28T00:00:00Z", "checkCount": 4, "id": "9d8fd72d-4854-4182-b077-4e307fea6327", "companyId": "15031532", "type": "payperiod", "_rid": "NmJkAKiCbEdYlAQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdYlAQAAAAAAA==/", "_etag": "\"a9003b0a-0000-0100-0000-6870445c0000\"", "_attachments": "attachments/", "_ts": 1752187996}, {"payPeriodId": "1060039285923675", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-03-01T00:00:00Z", "endDate": "2025-03-07T00:00:00Z", "submitByDate": "2025-03-05T00:00:00Z", "checkDate": "2025-03-07T00:00:00Z", "checkCount": 4, "id": "4387575d-5f34-44c2-84ce-4ead5a4621e0", "companyId": "15031532", "type": "payperiod", "_rid": "NmJkAKiCbEdZlAQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdZlAQAAAAAAA==/", "_etag": "\"a9003c0a-0000-0100-0000-6870445c0000\"", "_attachments": "attachments/", "_ts": 1752187996}, {"payPeriodId": "1060039330030164", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-03-08T00:00:00Z", "endDate": "2025-03-14T00:00:00Z", "submitByDate": "2025-03-12T00:00:00Z", "checkDate": "2025-03-14T00:00:00Z", "checkCount": 4, "id": "fee27013-bf08-4418-8c3e-45aa767394ad", "companyId": "15031532", "type": "payperiod", "_rid": "NmJkAKiCbEdalAQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdalAQAAAAAAA==/", "_etag": "\"a9003e0a-0000-0100-0000-6870445c0000\"", "_attachments": "attachments/", "_ts": 1752187996}, {"payPeriodId": "1060039368697206", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-03-15T00:00:00Z", "endDate": "2025-03-21T00:00:00Z", "submitByDate": "2025-03-19T00:00:00Z", "checkDate": "2025-03-21T00:00:00Z", "checkCount": 4, "id": "95b384ea-a5b9-4033-bcc6-05b78b817ec1", "companyId": "15031532", "type": "payperiod", "_rid": "NmJkAKiCbEdblAQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdblAQAAAAAAA==/", "_etag": "\"a900400a-0000-0100-0000-6870445c0000\"", "_attachments": "attachments/", "_ts": 1752187996}, {"payPeriodId": "1060039409940776", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-03-22T00:00:00Z", "endDate": "2025-03-28T00:00:00Z", "submitByDate": "2025-03-26T00:00:00Z", "checkDate": "2025-03-28T00:00:00Z", "checkCount": 4, "id": "ba0e6a51-854b-4b64-be3a-70cee651a861", "companyId": "15031532", "type": "payperiod", "_rid": "NmJkAKiCbEdclAQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdclAQAAAAAAA==/", "_etag": "\"a900420a-0000-0100-0000-6870445c0000\"", "_attachments": "attachments/", "_ts": 1752187996}, {"payPeriodId": "1060039445628073", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-03-29T00:00:00Z", "endDate": "2025-04-04T00:00:00Z", "submitByDate": "2025-04-02T00:00:00Z", "checkDate": "2025-04-04T00:00:00Z", "checkCount": 4, "id": "d8014034-df81-4bf3-96ba-35f4e1cf6278", "companyId": "15031532", "type": "payperiod", "_rid": "NmJkAKiCbEddlAQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEddlAQAAAAAAA==/", "_etag": "\"a900430a-0000-0100-0000-6870445c0000\"", "_attachments": "attachments/", "_ts": 1752187996}, {"payPeriodId": "1060039479118729", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-04-05T00:00:00Z", "endDate": "2025-04-11T00:00:00Z", "submitByDate": "2025-04-09T00:00:00Z", "checkDate": "2025-04-11T00:00:00Z", "checkCount": 4, "id": "990c308c-b26d-43eb-8223-5a4fe333e497", "companyId": "15031532", "type": "payperiod", "_rid": "NmJkAKiCbEdelAQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdelAQAAAAAAA==/", "_etag": "\"a900460a-0000-0100-0000-6870445c0000\"", "_attachments": "attachments/", "_ts": 1752187996}, {"payPeriodId": "1060039515844780", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-04-12T00:00:00Z", "endDate": "2025-04-18T00:00:00Z", "submitByDate": "2025-04-16T00:00:00Z", "checkDate": "2025-04-18T00:00:00Z", "checkCount": 4, "id": "efa1df64-6de7-418e-913d-489e2358f4d7", "companyId": "15031532", "type": "payperiod", "_rid": "NmJkAKiCbEdflAQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdflAQAAAAAAA==/", "_etag": "\"a9004c0a-0000-0100-0000-6870445c0000\"", "_attachments": "attachments/", "_ts": 1752187996}, {"payPeriodId": "1060039547932859", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-04-19T00:00:00Z", "endDate": "2025-04-25T00:00:00Z", "submitByDate": "2025-04-23T00:00:00Z", "checkDate": "2025-04-25T00:00:00Z", "checkCount": 4, "id": "bc29967f-3573-4b2c-b1d3-8682717023c3", "companyId": "15031532", "type": "payperiod", "_rid": "NmJkAKiCbEdglAQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdglAQAAAAAAA==/", "_etag": "\"a9004e0a-0000-0100-0000-6870445c0000\"", "_attachments": "attachments/", "_ts": 1752187996}, {"payPeriodId": "1060039584663064", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-04-26T00:00:00Z", "endDate": "2025-05-02T00:00:00Z", "submitByDate": "2025-04-30T00:00:00Z", "checkDate": "2025-05-02T00:00:00Z", "checkCount": 4, "id": "45db7aca-e630-4f3f-8199-77b89034bc4b", "companyId": "15031532", "type": "payperiod", "_rid": "NmJkAKiCbEdhlAQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdhlAQAAAAAAA==/", "_etag": "\"a900500a-0000-0100-0000-6870445c0000\"", "_attachments": "attachments/", "_ts": 1752187996}, {"payPeriodId": "1060039620233548", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-05-03T00:00:00Z", "endDate": "2025-05-09T00:00:00Z", "submitByDate": "2025-05-07T00:00:00Z", "checkDate": "2025-05-09T00:00:00Z", "checkCount": 4, "id": "75146732-0560-4f34-a295-f1fe2651e37a", "companyId": "15031532", "type": "payperiod", "_rid": "NmJkAKiCbEdilAQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdilAQAAAAAAA==/", "_etag": "\"a900520a-0000-0100-0000-6870445d0000\"", "_attachments": "attachments/", "_ts": 1752187997}, {"payPeriodId": "1060039686692344", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-05-17T00:00:00Z", "endDate": "2025-05-23T00:00:00Z", "submitByDate": "2025-05-21T00:00:00Z", "checkDate": "2025-05-23T00:00:00Z", "checkCount": 4, "id": "27e82da9-3571-443c-87a1-25178d799579", "companyId": "15031532", "type": "payperiod", "_rid": "NmJkAKiCbEdjlAQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdjlAQAAAAAAA==/", "_etag": "\"a900530a-0000-0100-0000-6870445d0000\"", "_attachments": "attachments/", "_ts": 1752187997}, {"payPeriodId": "1060039724963935", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-05-24T00:00:00Z", "endDate": "2025-05-30T00:00:00Z", "submitByDate": "2025-05-28T00:00:00Z", "checkDate": "2025-05-30T00:00:00Z", "checkCount": 4, "id": "21abb9ed-b82f-4704-b645-f7857f6bfd79", "companyId": "15031532", "type": "payperiod", "_rid": "NmJkAKiCbEdklAQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdklAQAAAAAAA==/", "_etag": "\"a900540a-0000-0100-0000-6870445d0000\"", "_attachments": "attachments/", "_ts": 1752187997}, {"payPeriodId": "1060039727885391", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-05-31T00:00:00Z", "endDate": "2025-06-06T00:00:00Z", "submitByDate": "2025-06-04T00:00:00Z", "checkDate": "2025-06-06T00:00:00Z", "checkCount": 4, "id": "a9b15e90-ff5d-4352-80bd-c1bb48d62cc3", "companyId": "15031532", "type": "payperiod", "_rid": "NmJkAKiCbEdllAQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdllAQAAAAAAA==/", "_etag": "\"a900570a-0000-0100-0000-6870445d0000\"", "_attachments": "attachments/", "_ts": 1752187997}, {"payPeriodId": "1060039760116072", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-06-07T00:00:00Z", "endDate": "2025-06-13T00:00:00Z", "submitByDate": "2025-06-11T00:00:00Z", "checkDate": "2025-06-13T00:00:00Z", "checkCount": 4, "id": "318dd456-e1cf-4c97-9da1-961929795847", "companyId": "15031532", "type": "payperiod", "_rid": "NmJkAKiCbEdmlAQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdmlAQAAAAAAA==/", "_etag": "\"a900580a-0000-0100-0000-6870445d0000\"", "_attachments": "attachments/", "_ts": 1752187997}, {"payPeriodId": "1060039795279203", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-06-14T00:00:00Z", "endDate": "2025-06-20T00:00:00Z", "submitByDate": "2025-06-18T00:00:00Z", "checkDate": "2025-06-20T00:00:00Z", "checkCount": 4, "id": "cf0c01dc-ca36-4a5c-a9b4-c86da7a31974", "companyId": "15031532", "type": "payperiod", "_rid": "NmJkAKiCbEdnlAQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdnlAQAAAAAAA==/", "_etag": "\"a9005b0a-0000-0100-0000-6870445d0000\"", "_attachments": "attachments/", "_ts": 1752187997}, {"payPeriodId": "1060039827732842", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-06-21T00:00:00Z", "endDate": "2025-06-27T00:00:00Z", "submitByDate": "2025-06-25T00:00:00Z", "checkDate": "2025-06-27T00:00:00Z", "checkCount": 4, "id": "2b4fe6b2-13bb-447f-948f-9af25d619cd6", "companyId": "15031532", "type": "payperiod", "_rid": "NmJkAKiCbEdolAQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdolAQAAAAAAA==/", "_etag": "\"a9005c0a-0000-0100-0000-6870445d0000\"", "_attachments": "attachments/", "_ts": 1752187997}, {"payPeriodId": "1060039864787427", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-06-28T00:00:00Z", "endDate": "2025-07-04T00:00:00Z", "submitByDate": "2025-07-01T00:00:00Z", "checkDate": "2025-07-03T00:00:00Z", "checkCount": 4, "id": "37954c7c-fc52-434f-aa6a-f110d6c3bef3", "companyId": "15031532", "type": "payperiod", "_rid": "NmJkAKiCbEdplAQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdplAQAAAAAAA==/", "_etag": "\"a9005e0a-0000-0100-0000-6870445d0000\"", "_attachments": "attachments/", "_ts": 1752187997}, {"payPeriodId": "1060039904943648", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-07-05T00:00:00Z", "endDate": "2025-07-11T00:00:00Z", "submitByDate": "2025-07-09T00:00:00Z", "checkDate": "2025-07-11T00:00:00Z", "checkCount": 0, "id": "b96148f9-8937-4a15-a536-83c7dad6949f", "companyId": "15031532", "type": "payperiod", "_rid": "NmJkAKiCbEdqlAQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdqlAQAAAAAAA==/", "_etag": "\"a900600a-0000-0100-0000-6870445d0000\"", "_attachments": "attachments/", "_ts": 1752187997}, {"payPeriodId": "1060039943322494", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-07-12T00:00:00Z", "endDate": "2025-07-18T00:00:00Z", "submitByDate": "2025-07-16T00:00:00Z", "checkDate": "2025-07-18T00:00:00Z", "checkCount": 0, "id": "87efe675-0911-4259-a189-baaf5076647f", "companyId": "15031532", "type": "payperiod", "_rid": "NmJkAKiCbEdrlAQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdrlAQAAAAAAA==/", "_etag": "\"a900610a-0000-0100-0000-6870445d0000\"", "_attachments": "attachments/", "_ts": 1752187997}, {"payPeriodId": "1060039979225599", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-07-19T00:00:00Z", "endDate": "2025-07-25T00:00:00Z", "submitByDate": "2025-07-23T00:00:00Z", "checkDate": "2025-07-25T00:00:00Z", "checkCount": 0, "id": "df11a437-5569-45b7-bb7f-c054e4278728", "companyId": "15031532", "type": "payperiod", "_rid": "NmJkAKiCbEdslAQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdslAQAAAAAAA==/", "_etag": "\"a900620a-0000-0100-0000-6870445d0000\"", "_attachments": "attachments/", "_ts": 1752187997}, {"payPeriodId": "1060040014069262", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-07-26T00:00:00Z", "endDate": "2025-08-01T00:00:00Z", "submitByDate": "2025-07-30T00:00:00Z", "checkDate": "2025-08-01T00:00:00Z", "checkCount": 0, "id": "b858aa48-1bed-4512-bb96-f55c7eb006da", "companyId": "15031532", "type": "payperiod", "_rid": "NmJkAKiCbEdtlAQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdtlAQAAAAAAA==/", "_etag": "\"a900640a-0000-0100-0000-6870445d0000\"", "_attachments": "attachments/", "_ts": 1752187997}, {"payPeriodId": "1060040055903632", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-08-02T00:00:00Z", "endDate": "2025-08-08T00:00:00Z", "submitByDate": "2025-08-06T00:00:00Z", "checkDate": "2025-08-08T00:00:00Z", "checkCount": 0, "id": "79d3f278-1a7b-46d1-bbbe-5984cf2d522d", "companyId": "15031532", "type": "payperiod", "_rid": "NmJkAKiCbEdulAQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdulAQAAAAAAA==/", "_etag": "\"a900650a-0000-0100-0000-6870445d0000\"", "_attachments": "attachments/", "_ts": 1752187997}, {"payPeriodId": "1060040090004339", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-08-09T00:00:00Z", "endDate": "2025-08-15T00:00:00Z", "submitByDate": "2025-08-13T00:00:00Z", "checkDate": "2025-08-15T00:00:00Z", "checkCount": 0, "id": "df5b1cd7-7aa0-4d1a-9586-975ace1dc663", "companyId": "15031532", "type": "payperiod", "_rid": "NmJkAKiCbEdvlAQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdvlAQAAAAAAA==/", "_etag": "\"a900660a-0000-0100-0000-6870445e0000\"", "_attachments": "attachments/", "_ts": 1752187998}, {"payPeriodId": "1060040127397580", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-08-16T00:00:00Z", "endDate": "2025-08-22T00:00:00Z", "submitByDate": "2025-08-20T00:00:00Z", "checkDate": "2025-08-22T00:00:00Z", "checkCount": 0, "id": "b51245f0-461e-4517-b745-fe0e33d005eb", "companyId": "15031532", "type": "payperiod", "_rid": "NmJkAKiCbEdwlAQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdwlAQAAAAAAA==/", "_etag": "\"a900670a-0000-0100-0000-6870445e0000\"", "_attachments": "attachments/", "_ts": 1752187998}, {"payPeriodId": "1060040160351460", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-08-23T00:00:00Z", "endDate": "2025-08-29T00:00:00Z", "submitByDate": "2025-08-27T00:00:00Z", "checkDate": "2025-08-29T00:00:00Z", "checkCount": 0, "id": "42c59faf-81d9-4f82-a9e1-1c2d9a5c9660", "companyId": "15031532", "type": "payperiod", "_rid": "NmJkAKiCbEdxlAQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdxlAQAAAAAAA==/", "_etag": "\"a900690a-0000-0100-0000-6870445e0000\"", "_attachments": "attachments/", "_ts": 1752187998}, {"payPeriodId": "1060040196142616", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-08-30T00:00:00Z", "endDate": "2025-09-05T00:00:00Z", "submitByDate": "2025-09-03T00:00:00Z", "checkDate": "2025-09-05T00:00:00Z", "checkCount": 0, "id": "fd82a93d-91a0-4054-b861-67850ec43e41", "companyId": "15031532", "type": "payperiod", "_rid": "NmJkAKiCbEdylAQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdylAQAAAAAAA==/", "_etag": "\"a9006a0a-0000-0100-0000-6870445e0000\"", "_attachments": "attachments/", "_ts": 1752187998}, {"payPeriodId": "1060040228953828", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-09-06T00:00:00Z", "endDate": "2025-09-12T00:00:00Z", "submitByDate": "2025-09-10T00:00:00Z", "checkDate": "2025-09-12T00:00:00Z", "checkCount": 0, "id": "b9a5ae12-c4c8-4ad0-9b82-981f580e7b63", "companyId": "15031532", "type": "payperiod", "_rid": "NmJkAKiCbEdzlAQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdzlAQAAAAAAA==/", "_etag": "\"a9006b0a-0000-0100-0000-6870445e0000\"", "_attachments": "attachments/", "_ts": 1752187998}, {"payPeriodId": "1060040264176530", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-09-13T00:00:00Z", "endDate": "2025-09-19T00:00:00Z", "submitByDate": "2025-09-17T00:00:00Z", "checkDate": "2025-09-19T00:00:00Z", "checkCount": 0, "id": "6c4cf86e-5eba-4177-b125-87bc15f2e17f", "companyId": "15031532", "type": "payperiod", "_rid": "NmJkAKiCbEd0lAQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEd0lAQAAAAAAA==/", "_etag": "\"a9006c0a-0000-0100-0000-6870445e0000\"", "_attachments": "attachments/", "_ts": 1752187998}, {"payPeriodId": "1060040297299147", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-09-20T00:00:00Z", "endDate": "2025-09-26T00:00:00Z", "submitByDate": "2025-09-24T00:00:00Z", "checkDate": "2025-09-26T00:00:00Z", "checkCount": 0, "id": "735b0a5a-7975-433b-a47d-68c630fb9c13", "companyId": "15031532", "type": "payperiod", "_rid": "NmJkAKiCbEd1lAQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEd1lAQAAAAAAA==/", "_etag": "\"a9006e0a-0000-0100-0000-6870445e0000\"", "_attachments": "attachments/", "_ts": 1752187998}, {"payPeriodId": "1060040335252052", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-09-27T00:00:00Z", "endDate": "2025-10-03T00:00:00Z", "submitByDate": "2025-10-01T00:00:00Z", "checkDate": "2025-10-03T00:00:00Z", "checkCount": 0, "id": "791f62dc-337e-4df7-9546-53f2c4d200f6", "companyId": "15031532", "type": "payperiod", "_rid": "NmJkAKiCbEd2lAQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEd2lAQAAAAAAA==/", "_etag": "\"a9006f0a-0000-0100-0000-6870445e0000\"", "_attachments": "attachments/", "_ts": 1752187998}, {"payPeriodId": "1060040369679211", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-10-04T00:00:00Z", "endDate": "2025-10-10T00:00:00Z", "submitByDate": "2025-10-08T00:00:00Z", "checkDate": "2025-10-10T00:00:00Z", "checkCount": 0, "id": "292a241f-cb89-4afb-a76e-d5d2bca997a4", "companyId": "15031532", "type": "payperiod", "_rid": "NmJkAKiCbEd3lAQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEd3lAQAAAAAAA==/", "_etag": "\"a900720a-0000-0100-0000-6870445e0000\"", "_attachments": "attachments/", "_ts": 1752187998}], "links": [{"rel": "self", "href": "https://ca-payroll-ai-mock-sb-001-secure.nicebeach-8a7a52ab.eastus.azurecontainerapps.io/ca/companies/15031532/payperiods"}]}, "status_code": 200}