{"success": true, "company_id": "0020P782", "data": {"metadata": {"contentItemCount": 20}, "content": [{"payPeriodId": "1140035058642094", "intervalCode": "MONTHLY", "status": "COMPLETED", "description": "Monthly Payroll (2)", "startDate": "2025-01-01T00:00:00Z", "endDate": "2025-01-31T00:00:00Z", "submitByDate": "2025-01-22T00:00:00Z", "checkDate": "2025-01-24T00:00:00Z", "checkCount": 5, "id": "2f43364d-f14f-4853-8f31-5f6aec637b25", "companyId": "0020P782", "type": "payperiod", "_rid": "NmJkAKiCbEf-SQUAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf-SQUAAAAAAA==/", "_etag": "\"aa001184-0000-0100-0000-687052b00000\"", "_attachments": "attachments/", "_ts": 1752191664}, {"payPeriodId": "1140035183546216", "intervalCode": "MONTHLY", "status": "COMPLETED", "description": "Monthly Payroll (2)", "startDate": "2025-02-01T00:00:00Z", "endDate": "2025-02-28T00:00:00Z", "submitByDate": "2025-02-21T00:00:00Z", "checkDate": "2025-02-25T00:00:00Z", "checkCount": 5, "id": "1979619a-dfcc-47da-9e8f-e0411b085123", "companyId": "0020P782", "type": "payperiod", "_rid": "NmJkAKiCbEcASgUAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcASgUAAAAAAA==/", "_etag": "\"aa001484-0000-0100-0000-687052b00000\"", "_attachments": "attachments/", "_ts": 1752191664}, {"payPeriodId": "1140035324843283", "intervalCode": "MONTHLY", "status": "COMPLETED", "description": "Monthly Payroll (2)", "startDate": "2025-03-01T00:00:00Z", "endDate": "2025-03-31T00:00:00Z", "submitByDate": "2025-03-21T00:00:00Z", "checkDate": "2025-03-25T00:00:00Z", "checkCount": 4, "id": "fd2c0ede-d3a2-4ab4-9031-66e834597001", "companyId": "0020P782", "type": "payperiod", "_rid": "NmJkAKiCbEcBSgUAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcBSgUAAAAAAA==/", "_etag": "\"aa001584-0000-0100-0000-687052b00000\"", "_attachments": "attachments/", "_ts": 1752191664}, {"payPeriodId": "1140035475439811", "intervalCode": "MONTHLY", "status": "INITIAL", "description": "Monthly Payroll (2)", "startDate": "2025-04-01T00:00:00Z", "endDate": "2025-04-30T00:00:00Z", "submitByDate": "2025-04-23T00:00:00Z", "checkDate": "2025-04-25T00:00:00Z", "checkCount": 0, "id": "8d124f1a-803d-4175-a74c-e108cf7b00f7", "companyId": "0020P782", "type": "payperiod", "_rid": "NmJkAKiCbEcCSgUAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcCSgUAAAAAAA==/", "_etag": "\"aa001984-0000-0100-0000-687052b00000\"", "_attachments": "attachments/", "_ts": 1752191664}, {"payPeriodId": "1140035588178619", "intervalCode": "MONTHLY", "status": "INITIAL", "description": "Monthly Payroll (2)", "startDate": "2025-05-01T00:00:00Z", "endDate": "2025-05-31T00:00:00Z", "submitByDate": "2025-05-21T00:00:00Z", "checkDate": "2025-05-23T00:00:00Z", "checkCount": 0, "id": "f1d0cd6e-db2e-4a07-af49-7dfc34ceb3c5", "companyId": "0020P782", "type": "payperiod", "_rid": "NmJkAKiCbEcDSgUAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcDSgUAAAAAAA==/", "_etag": "\"aa001b84-0000-0100-0000-687052b00000\"", "_attachments": "attachments/", "_ts": 1752191664}, {"payPeriodId": "1140035703532577", "intervalCode": "MONTHLY", "status": "INITIAL", "description": "Monthly Payroll (2)", "startDate": "2025-06-01T00:00:00Z", "endDate": "2025-06-30T00:00:00Z", "submitByDate": "2025-06-23T00:00:00Z", "checkDate": "2025-06-25T00:00:00Z", "checkCount": 0, "id": "7ced02d0-5f78-4a00-871b-7c6fb594f115", "companyId": "0020P782", "type": "payperiod", "_rid": "NmJkAKiCbEcESgUAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcESgUAAAAAAA==/", "_etag": "\"aa001d84-0000-0100-0000-687052b10000\"", "_attachments": "attachments/", "_ts": 1752191665}, {"payPeriodId": "1140035845457050", "intervalCode": "MONTHLY", "status": "INITIAL", "description": "Monthly Payroll (2)", "startDate": "2025-07-01T00:00:00Z", "endDate": "2025-07-31T00:00:00Z", "submitByDate": "2025-07-23T00:00:00Z", "checkDate": "2025-07-25T00:00:00Z", "checkCount": 0, "id": "3f151e0a-fb6a-4a48-887a-60d2f96f6158", "companyId": "0020P782", "type": "payperiod", "_rid": "NmJkAKiCbEcFSgUAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcFSgUAAAAAAA==/", "_etag": "\"aa001e84-0000-0100-0000-687052b10000\"", "_attachments": "attachments/", "_ts": 1752191665}, {"payPeriodId": "1140035965948352", "intervalCode": "MONTHLY", "status": "INITIAL", "description": "Monthly Payroll (2)", "startDate": "2025-08-01T00:00:00Z", "endDate": "2025-08-31T00:00:00Z", "submitByDate": "2025-08-21T00:00:00Z", "checkDate": "2025-08-25T00:00:00Z", "checkCount": 0, "id": "42e774e0-b705-4a96-9e97-abef1683d9d6", "companyId": "0020P782", "type": "payperiod", "_rid": "NmJkAKiCbEcGSgUAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcGSgUAAAAAAA==/", "_etag": "\"aa001f84-0000-0100-0000-687052b10000\"", "_attachments": "attachments/", "_ts": 1752191665}, {"payPeriodId": "1140036117668697", "intervalCode": "MONTHLY", "status": "INITIAL", "description": "Monthly Payroll (2)", "startDate": "2025-09-01T00:00:00Z", "endDate": "2025-09-30T00:00:00Z", "submitByDate": "2025-09-23T00:00:00Z", "checkDate": "2025-09-25T00:00:00Z", "checkCount": 0, "id": "229f5a76-234c-41b9-9f6f-e6fe09e697b1", "companyId": "0020P782", "type": "payperiod", "_rid": "NmJkAKiCbEcHSgUAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcHSgUAAAAAAA==/", "_etag": "\"aa002084-0000-0100-0000-687052b10000\"", "_attachments": "attachments/", "_ts": 1752191665}, {"payPeriodId": "1140036227437015", "intervalCode": "MONTHLY", "status": "INITIAL", "description": "Monthly Payroll (2)", "startDate": "2025-10-01T00:00:00Z", "endDate": "2025-10-31T00:00:00Z", "submitByDate": "2025-10-22T00:00:00Z", "checkDate": "2025-10-24T00:00:00Z", "checkCount": 0, "id": "271adaab-02e8-432c-b4b3-8eebb4b22682", "companyId": "0020P782", "type": "payperiod", "_rid": "NmJkAKiCbEcISgUAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcISgUAAAAAAA==/", "_etag": "\"aa002284-0000-0100-0000-687052b10000\"", "_attachments": "attachments/", "_ts": 1752191665}, {"payPeriodId": "1140035058642094", "intervalCode": "MONTHLY", "status": "COMPLETED", "description": "Monthly Payroll (2)", "startDate": "2025-01-01T00:00:00Z", "endDate": "2025-01-31T00:00:00Z", "submitByDate": "2025-01-22T00:00:00Z", "checkDate": "2025-01-24T00:00:00Z", "checkCount": 5, "id": "2279135e-7c10-4962-b9af-300d96c564ab", "companyId": "0020P782", "type": "payperiod", "_rid": "NmJkAKiCbEcMSgUAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcMSgUAAAAAAA==/", "_etag": "\"aa002a84-0000-0100-0000-687052b10000\"", "_attachments": "attachments/", "_ts": 1752191665}, {"payPeriodId": "1140035183546216", "intervalCode": "MONTHLY", "status": "COMPLETED", "description": "Monthly Payroll (2)", "startDate": "2025-02-01T00:00:00Z", "endDate": "2025-02-28T00:00:00Z", "submitByDate": "2025-02-21T00:00:00Z", "checkDate": "2025-02-25T00:00:00Z", "checkCount": 5, "id": "02e13da4-2f65-4552-968d-1aa8284d58d2", "companyId": "0020P782", "type": "payperiod", "_rid": "NmJkAKiCbEcNSgUAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcNSgUAAAAAAA==/", "_etag": "\"aa002d84-0000-0100-0000-687052b10000\"", "_attachments": "attachments/", "_ts": 1752191665}, {"payPeriodId": "1140035324843283", "intervalCode": "MONTHLY", "status": "COMPLETED", "description": "Monthly Payroll (2)", "startDate": "2025-03-01T00:00:00Z", "endDate": "2025-03-31T00:00:00Z", "submitByDate": "2025-03-21T00:00:00Z", "checkDate": "2025-03-25T00:00:00Z", "checkCount": 4, "id": "32215273-d085-472c-8dc8-0b5c37fc3c9b", "companyId": "0020P782", "type": "payperiod", "_rid": "NmJkAKiCbEcOSgUAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcOSgUAAAAAAA==/", "_etag": "\"aa002f84-0000-0100-0000-687052b10000\"", "_attachments": "attachments/", "_ts": 1752191665}, {"payPeriodId": "1140035475439811", "intervalCode": "MONTHLY", "status": "COMPLETED", "description": "Monthly Payroll (2)", "startDate": "2025-04-01T00:00:00Z", "endDate": "2025-04-30T00:00:00Z", "submitByDate": "2025-04-23T00:00:00Z", "checkDate": "2025-04-25T00:00:00Z", "checkCount": 4, "id": "850762a2-4276-49ed-9d69-3665d1fcb2f6", "companyId": "0020P782", "type": "payperiod", "_rid": "NmJkAKiCbEcPSgUAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcPSgUAAAAAAA==/", "_etag": "\"aa003084-0000-0100-0000-687052b10000\"", "_attachments": "attachments/", "_ts": 1752191665}, {"payPeriodId": "1140035588178619", "intervalCode": "MONTHLY", "status": "COMPLETED", "description": "Monthly Payroll (2)", "startDate": "2025-05-01T00:00:00Z", "endDate": "2025-05-31T00:00:00Z", "submitByDate": "2025-05-21T00:00:00Z", "checkDate": "2025-05-23T00:00:00Z", "checkCount": 4, "id": "dbad4ef8-d4b9-46f2-b3d0-d6bbbaa95296", "companyId": "0020P782", "type": "payperiod", "_rid": "NmJkAKiCbEcQSgUAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcQSgUAAAAAAA==/", "_etag": "\"aa003184-0000-0100-0000-687052b10000\"", "_attachments": "attachments/", "_ts": 1752191665}, {"payPeriodId": "1140035703532577", "intervalCode": "MONTHLY", "status": "COMPLETED", "description": "Monthly Payroll (2)", "startDate": "2025-06-01T00:00:00Z", "endDate": "2025-06-30T00:00:00Z", "submitByDate": "2025-06-23T00:00:00Z", "checkDate": "2025-06-25T00:00:00Z", "checkCount": 4, "id": "dd35851a-ca05-401c-b206-64c430735028", "companyId": "0020P782", "type": "payperiod", "_rid": "NmJkAKiCbEcRSgUAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcRSgUAAAAAAA==/", "_etag": "\"aa003484-0000-0100-0000-687052b20000\"", "_attachments": "attachments/", "_ts": 1752191666}, {"payPeriodId": "1140035845457050", "intervalCode": "MONTHLY", "status": "INITIAL", "description": "Monthly Payroll (2)", "startDate": "2025-07-01T00:00:00Z", "endDate": "2025-07-31T00:00:00Z", "submitByDate": "2025-07-23T00:00:00Z", "checkDate": "2025-07-25T00:00:00Z", "checkCount": 0, "id": "b3616116-2176-489b-a51b-98b3f6610d4d", "companyId": "0020P782", "type": "payperiod", "_rid": "NmJkAKiCbEcSSgUAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcSSgUAAAAAAA==/", "_etag": "\"aa003584-0000-0100-0000-687052b20000\"", "_attachments": "attachments/", "_ts": 1752191666}, {"payPeriodId": "1140035965948352", "intervalCode": "MONTHLY", "status": "INITIAL", "description": "Monthly Payroll (2)", "startDate": "2025-08-01T00:00:00Z", "endDate": "2025-08-31T00:00:00Z", "submitByDate": "2025-08-21T00:00:00Z", "checkDate": "2025-08-25T00:00:00Z", "checkCount": 0, "id": "ddcc8333-a0bd-4c73-be66-edd527aa7ae0", "companyId": "0020P782", "type": "payperiod", "_rid": "NmJkAKiCbEcTSgUAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcTSgUAAAAAAA==/", "_etag": "\"aa003684-0000-0100-0000-687052b20000\"", "_attachments": "attachments/", "_ts": 1752191666}, {"payPeriodId": "1140036117668697", "intervalCode": "MONTHLY", "status": "INITIAL", "description": "Monthly Payroll (2)", "startDate": "2025-09-01T00:00:00Z", "endDate": "2025-09-30T00:00:00Z", "submitByDate": "2025-09-23T00:00:00Z", "checkDate": "2025-09-25T00:00:00Z", "checkCount": 0, "id": "a2850b08-e476-4df4-8249-8fc801b418c3", "companyId": "0020P782", "type": "payperiod", "_rid": "NmJkAKiCbEcUSgUAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcUSgUAAAAAAA==/", "_etag": "\"aa003784-0000-0100-0000-687052b20000\"", "_attachments": "attachments/", "_ts": 1752191666}, {"payPeriodId": "1140036227437015", "intervalCode": "MONTHLY", "status": "INITIAL", "description": "Monthly Payroll (2)", "startDate": "2025-10-01T00:00:00Z", "endDate": "2025-10-31T00:00:00Z", "submitByDate": "2025-10-22T00:00:00Z", "checkDate": "2025-10-24T00:00:00Z", "checkCount": 0, "id": "cbe78d15-e883-4595-bc3a-b20aa2df0e2e", "companyId": "0020P782", "type": "payperiod", "_rid": "NmJkAKiCbEcVSgUAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcVSgUAAAAAAA==/", "_etag": "\"aa003984-0000-0100-0000-687052b20000\"", "_attachments": "attachments/", "_ts": 1752191666}], "links": [{"rel": "self", "href": "https://ca-payroll-ai-mock-sb-001-secure.nicebeach-8a7a52ab.eastus.azurecontainerapps.io/ca/companies/0020P782/payperiods"}]}, "status_code": 200}