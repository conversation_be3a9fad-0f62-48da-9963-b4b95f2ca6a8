{"success": true, "company_id": "00907228", "data": {"metadata": {"contentItemCount": 40}, "content": [{"payPeriodId": "1140034998133601", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2024-12-16T00:00:00Z", "endDate": "2025-01-01T00:00:00Z", "submitByDate": "2024-12-27T00:00:00Z", "checkDate": "2024-12-31T00:00:00Z", "checkCount": 0, "id": "3c6d9079-4396-45ad-a2a3-fd914281537c", "companyId": "00907228", "type": "payperiod", "_rid": "NmJkAKiCbEf2vQIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf2vQIAAAAAAA==/", "_etag": "\"a30070d7-0000-0100-0000-68701d060000\"", "_attachments": "attachments/", "_ts": 1752177926}, {"payPeriodId": "1140034998133602", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-01-02T00:00:00Z", "endDate": "2025-01-15T00:00:00Z", "submitByDate": "2025-01-13T00:00:00Z", "checkDate": "2025-01-15T00:00:00Z", "checkCount": 4, "id": "63ea4e18-ce22-4a33-8447-89e59bd21846", "companyId": "00907228", "type": "payperiod", "_rid": "NmJkAKiCbEf3vQIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf3vQIAAAAAAA==/", "_etag": "\"a30075d7-0000-0100-0000-68701d060000\"", "_attachments": "attachments/", "_ts": 1752177926}, {"payPeriodId": "1140035116413755", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-01-16T00:00:00Z", "endDate": "2025-02-01T00:00:00Z", "submitByDate": "2025-01-29T00:00:00Z", "checkDate": "2025-01-31T00:00:00Z", "checkCount": 4, "id": "b60ec3ae-a28c-4420-8d7b-4007415a3a89", "companyId": "00907228", "type": "payperiod", "_rid": "NmJkAKiCbEf4vQIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf4vQIAAAAAAA==/", "_etag": "\"a3007ad7-0000-0100-0000-68701d060000\"", "_attachments": "attachments/", "_ts": 1752177926}, {"payPeriodId": "1140035116413756", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-02-02T00:00:00Z", "endDate": "2025-02-15T00:00:00Z", "submitByDate": "2025-02-12T00:00:00Z", "checkDate": "2025-02-14T00:00:00Z", "checkCount": 4, "id": "fc877b59-8cfc-4e05-a3f8-028593ad08ce", "companyId": "00907228", "type": "payperiod", "_rid": "NmJkAKiCbEf5vQIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf5vQIAAAAAAA==/", "_etag": "\"a3007cd7-0000-0100-0000-68701d070000\"", "_attachments": "attachments/", "_ts": 1752177927}, {"payPeriodId": "1140035259579784", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-02-16T00:00:00Z", "endDate": "2025-03-01T00:00:00Z", "submitByDate": "2025-02-26T00:00:00Z", "checkDate": "2025-02-28T00:00:00Z", "checkCount": 4, "id": "9f4560e8-bbf0-4388-bee9-3b0d08c82873", "companyId": "00907228", "type": "payperiod", "_rid": "NmJkAKiCbEf6vQIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf6vQIAAAAAAA==/", "_etag": "\"a3007ed7-0000-0100-0000-68701d070000\"", "_attachments": "attachments/", "_ts": 1752177927}, {"payPeriodId": "1140035259579785", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-03-02T00:00:00Z", "endDate": "2025-03-15T00:00:00Z", "submitByDate": "2025-03-12T00:00:00Z", "checkDate": "2025-03-14T00:00:00Z", "checkCount": 4, "id": "24d0f4c2-f8c6-45d8-97ff-74346b06d5f4", "companyId": "00907228", "type": "payperiod", "_rid": "NmJkAKiCbEf7vQIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf7vQIAAAAAAA==/", "_etag": "\"a30082d7-0000-0100-0000-68701d070000\"", "_attachments": "attachments/", "_ts": 1752177927}, {"payPeriodId": "1140035413981173", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-03-16T00:00:00Z", "endDate": "2025-04-01T00:00:00Z", "submitByDate": "2025-03-28T00:00:00Z", "checkDate": "2025-04-01T00:00:00Z", "checkCount": 0, "id": "ace035a1-00d6-498f-9504-9a5d6fb30a09", "companyId": "00907228", "type": "payperiod", "_rid": "NmJkAKiCbEf8vQIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf8vQIAAAAAAA==/", "_etag": "\"a30086d7-0000-0100-0000-68701d070000\"", "_attachments": "attachments/", "_ts": 1752177927}, {"payPeriodId": "1140035413981174", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-04-02T00:00:00Z", "endDate": "2025-04-15T00:00:00Z", "submitByDate": "2025-04-11T00:00:00Z", "checkDate": "2025-04-15T00:00:00Z", "checkCount": 0, "id": "0c533e9f-dcaa-48f0-bfd8-82c1cc9925a7", "companyId": "00907228", "type": "payperiod", "_rid": "NmJkAKiCbEf9vQIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf9vQIAAAAAAA==/", "_etag": "\"a30089d7-0000-0100-0000-68701d070000\"", "_attachments": "attachments/", "_ts": 1752177927}, {"payPeriodId": "1140035533749212", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-04-16T00:00:00Z", "endDate": "2025-05-01T00:00:00Z", "submitByDate": "2025-04-29T00:00:00Z", "checkDate": "2025-05-01T00:00:00Z", "checkCount": 0, "id": "1fc32f87-efda-4f40-b4a4-c2fb9b29699d", "companyId": "00907228", "type": "payperiod", "_rid": "NmJkAKiCbEf+vQIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf+vQIAAAAAAA==/", "_etag": "\"a3008bd7-0000-0100-0000-68701d070000\"", "_attachments": "attachments/", "_ts": 1752177927}, {"payPeriodId": "1140035533749213", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-05-02T00:00:00Z", "endDate": "2025-05-15T00:00:00Z", "submitByDate": "2025-05-13T00:00:00Z", "checkDate": "2025-05-15T00:00:00Z", "checkCount": 0, "id": "54220e18-f5d6-402a-9b8e-37f819f3fbd7", "companyId": "00907228", "type": "payperiod", "_rid": "NmJkAKiCbEf-vQIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf-vQIAAAAAAA==/", "_etag": "\"a3008ed7-0000-0100-0000-68701d070000\"", "_attachments": "attachments/", "_ts": 1752177927}, {"payPeriodId": "1140035667502617", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-05-16T00:00:00Z", "endDate": "2025-06-01T00:00:00Z", "submitByDate": "2025-05-28T00:00:00Z", "checkDate": "2025-05-30T00:00:00Z", "checkCount": 0, "id": "f6226fc5-1d8c-4fb2-a3b5-1334bc853b7c", "companyId": "00907228", "type": "payperiod", "_rid": "NmJkAKiCbEcAvgIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcAvgIAAAAAAA==/", "_etag": "\"a30090d7-0000-0100-0000-68701d070000\"", "_attachments": "attachments/", "_ts": 1752177927}, {"payPeriodId": "1140035667502618", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-06-02T00:00:00Z", "endDate": "2025-06-15T00:00:00Z", "submitByDate": "2025-06-11T00:00:00Z", "checkDate": "2025-06-13T00:00:00Z", "checkCount": 0, "id": "5600dd57-cb3e-461d-904e-f9163d48f4d1", "companyId": "00907228", "type": "payperiod", "_rid": "NmJkAKiCbEcBvgIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcBvgIAAAAAAA==/", "_etag": "\"a30091d7-0000-0100-0000-68701d070000\"", "_attachments": "attachments/", "_ts": 1752177927}, {"payPeriodId": "1140035783906181", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-06-16T00:00:00Z", "endDate": "2025-07-01T00:00:00Z", "submitByDate": "2025-06-27T00:00:00Z", "checkDate": "2025-07-01T00:00:00Z", "checkCount": 0, "id": "18a2b6e1-72d0-4267-9deb-e09f177f461b", "companyId": "00907228", "type": "payperiod", "_rid": "NmJkAKiCbEcCvgIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcCvgIAAAAAAA==/", "_etag": "\"a30093d7-0000-0100-0000-68701d070000\"", "_attachments": "attachments/", "_ts": 1752177927}, {"payPeriodId": "1140035783906182", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-07-02T00:00:00Z", "endDate": "2025-07-15T00:00:00Z", "submitByDate": "2025-07-11T00:00:00Z", "checkDate": "2025-07-15T00:00:00Z", "checkCount": 0, "id": "38d51040-b2f5-4f1c-b5cc-2465a97d2609", "companyId": "00907228", "type": "payperiod", "_rid": "NmJkAKiCbEcDvgIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcDvgIAAAAAAA==/", "_etag": "\"a30095d7-0000-0100-0000-68701d070000\"", "_attachments": "attachments/", "_ts": 1752177927}, {"payPeriodId": "1140035904959246", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-07-16T00:00:00Z", "endDate": "2025-08-01T00:00:00Z", "submitByDate": "2025-07-30T00:00:00Z", "checkDate": "2025-08-01T00:00:00Z", "checkCount": 0, "id": "1990b2bd-01a5-40a0-97ee-cc84a43a4294", "companyId": "00907228", "type": "payperiod", "_rid": "NmJkAKiCbEcEvgIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcEvgIAAAAAAA==/", "_etag": "\"a30099d7-0000-0100-0000-68701d070000\"", "_attachments": "attachments/", "_ts": 1752177927}, {"payPeriodId": "1140035904959247", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-08-02T00:00:00Z", "endDate": "2025-08-15T00:00:00Z", "submitByDate": "2025-08-13T00:00:00Z", "checkDate": "2025-08-15T00:00:00Z", "checkCount": 0, "id": "b74c93f4-a4f2-4fd2-be44-9ecd4373bbe1", "companyId": "00907228", "type": "payperiod", "_rid": "NmJkAKiCbEcFvgIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcFvgIAAAAAAA==/", "_etag": "\"a3009cd7-0000-0100-0000-68701d070000\"", "_attachments": "attachments/", "_ts": 1752177927}, {"payPeriodId": "1140036027266428", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-08-16T00:00:00Z", "endDate": "2025-09-01T00:00:00Z", "submitByDate": "2025-08-27T00:00:00Z", "checkDate": "2025-08-29T00:00:00Z", "checkCount": 0, "id": "2e28572f-26fd-4ce6-9059-b5375eeffc4c", "companyId": "00907228", "type": "payperiod", "_rid": "NmJkAKiCbEcGvgIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcGvgIAAAAAAA==/", "_etag": "\"a300a0d7-0000-0100-0000-68701d080000\"", "_attachments": "attachments/", "_ts": 1752177928}, {"payPeriodId": "1140036027266429", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-09-02T00:00:00Z", "endDate": "2025-09-15T00:00:00Z", "submitByDate": "2025-09-11T00:00:00Z", "checkDate": "2025-09-15T00:00:00Z", "checkCount": 0, "id": "d5123c1b-323b-46f7-8d71-99abc7e2541e", "companyId": "00907228", "type": "payperiod", "_rid": "NmJkAKiCbEcHvgIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcHvgIAAAAAAA==/", "_etag": "\"a300a5d7-0000-0100-0000-68701d080000\"", "_attachments": "attachments/", "_ts": 1752177928}, {"payPeriodId": "1140036169899340", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-09-16T00:00:00Z", "endDate": "2025-10-01T00:00:00Z", "submitByDate": "2025-09-29T00:00:00Z", "checkDate": "2025-10-01T00:00:00Z", "checkCount": 0, "id": "f6834b98-ad41-4dec-a59e-89f76af8e5ba", "companyId": "00907228", "type": "payperiod", "_rid": "NmJkAKiCbEcIvgIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcIvgIAAAAAAA==/", "_etag": "\"a300aad7-0000-0100-0000-68701d080000\"", "_attachments": "attachments/", "_ts": 1752177928}, {"payPeriodId": "1140036169899341", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-10-02T00:00:00Z", "endDate": "2025-10-15T00:00:00Z", "submitByDate": "2025-10-13T00:00:00Z", "checkDate": "2025-10-15T00:00:00Z", "checkCount": 0, "id": "ae419f01-d61d-4114-b13f-1e90ad71938a", "companyId": "00907228", "type": "payperiod", "_rid": "NmJkAKiCbEcJvgIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcJvgIAAAAAAA==/", "_etag": "\"a300aed7-0000-0100-0000-68701d080000\"", "_attachments": "attachments/", "_ts": 1752177928}, {"payPeriodId": "1140034998133601", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2024-12-16T00:00:00Z", "endDate": "2025-01-01T00:00:00Z", "submitByDate": "2024-12-27T00:00:00Z", "checkDate": "2024-12-31T00:00:00Z", "checkCount": 4, "id": "e75a78fe-692d-4b5a-9034-2f973663cb76", "companyId": "00907228", "type": "payperiod", "_rid": "NmJkAKiCbEcUvgIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcUvgIAAAAAAA==/", "_etag": "\"a300ced7-0000-0100-0000-68701d090000\"", "_attachments": "attachments/", "_ts": 1752177929}, {"payPeriodId": "1140034998133602", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-01-02T00:00:00Z", "endDate": "2025-01-15T00:00:00Z", "submitByDate": "2025-01-13T00:00:00Z", "checkDate": "2025-01-15T00:00:00Z", "checkCount": 4, "id": "5e465fb7-5eee-4129-af1c-f0391a323299", "companyId": "00907228", "type": "payperiod", "_rid": "NmJkAKiCbEcVvgIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcVvgIAAAAAAA==/", "_etag": "\"a300cfd7-0000-0100-0000-68701d090000\"", "_attachments": "attachments/", "_ts": 1752177929}, {"payPeriodId": "1140035116413755", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-01-16T00:00:00Z", "endDate": "2025-02-01T00:00:00Z", "submitByDate": "2025-01-29T00:00:00Z", "checkDate": "2025-01-31T00:00:00Z", "checkCount": 4, "id": "b5112342-986c-404a-bbfc-21cdfd320b78", "companyId": "00907228", "type": "payperiod", "_rid": "NmJkAKiCbEcWvgIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcWvgIAAAAAAA==/", "_etag": "\"a300d1d7-0000-0100-0000-68701d090000\"", "_attachments": "attachments/", "_ts": 1752177929}, {"payPeriodId": "1140035116413756", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-02-02T00:00:00Z", "endDate": "2025-02-15T00:00:00Z", "submitByDate": "2025-02-12T00:00:00Z", "checkDate": "2025-02-14T00:00:00Z", "checkCount": 4, "id": "9c4706e3-1ed3-42c1-a149-216050e3f830", "companyId": "00907228", "type": "payperiod", "_rid": "NmJkAKiCbEcXvgIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcXvgIAAAAAAA==/", "_etag": "\"a300d6d7-0000-0100-0000-68701d090000\"", "_attachments": "attachments/", "_ts": 1752177929}, {"payPeriodId": "1140035259579784", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-02-16T00:00:00Z", "endDate": "2025-03-01T00:00:00Z", "submitByDate": "2025-02-26T00:00:00Z", "checkDate": "2025-02-28T00:00:00Z", "checkCount": 4, "id": "664c202b-1bc6-4d0f-9b4f-a29ca5a4112e", "companyId": "00907228", "type": "payperiod", "_rid": "NmJkAKiCbEcYvgIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcYvgIAAAAAAA==/", "_etag": "\"a300dbd7-0000-0100-0000-68701d090000\"", "_attachments": "attachments/", "_ts": 1752177929}, {"payPeriodId": "1140035259579785", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-03-02T00:00:00Z", "endDate": "2025-03-15T00:00:00Z", "submitByDate": "2025-03-12T00:00:00Z", "checkDate": "2025-03-14T00:00:00Z", "checkCount": 4, "id": "10c52889-62c5-4ec3-9c80-e19068198684", "companyId": "00907228", "type": "payperiod", "_rid": "NmJkAKiCbEcZvgIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcZvgIAAAAAAA==/", "_etag": "\"a300ddd7-0000-0100-0000-68701d090000\"", "_attachments": "attachments/", "_ts": 1752177929}, {"payPeriodId": "1140035413981173", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-03-16T00:00:00Z", "endDate": "2025-04-01T00:00:00Z", "submitByDate": "2025-03-28T00:00:00Z", "checkDate": "2025-04-01T00:00:00Z", "checkCount": 4, "id": "cae0affe-5913-4b42-8983-95991fbc735f", "companyId": "00907228", "type": "payperiod", "_rid": "NmJkAKiCbEcavgIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcavgIAAAAAAA==/", "_etag": "\"a300e0d7-0000-0100-0000-68701d090000\"", "_attachments": "attachments/", "_ts": 1752177929}, {"payPeriodId": "1140035413981174", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-04-02T00:00:00Z", "endDate": "2025-04-15T00:00:00Z", "submitByDate": "2025-04-11T00:00:00Z", "checkDate": "2025-04-15T00:00:00Z", "checkCount": 4, "id": "9d5dc69f-d25d-4540-9fda-c628ca6fa6df", "companyId": "00907228", "type": "payperiod", "_rid": "NmJkAKiCbEcbvgIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcbvgIAAAAAAA==/", "_etag": "\"a300e2d7-0000-0100-0000-68701d090000\"", "_attachments": "attachments/", "_ts": 1752177929}, {"payPeriodId": "1140035533749212", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-04-16T00:00:00Z", "endDate": "2025-05-01T00:00:00Z", "submitByDate": "2025-04-29T00:00:00Z", "checkDate": "2025-05-01T00:00:00Z", "checkCount": 4, "id": "f5e3782a-cf9e-4ac1-84d7-c7e579ae7a2b", "companyId": "00907228", "type": "payperiod", "_rid": "NmJkAKiCbEccvgIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEccvgIAAAAAAA==/", "_etag": "\"a300ead7-0000-0100-0000-68701d090000\"", "_attachments": "attachments/", "_ts": 1752177929}, {"payPeriodId": "1140035533749213", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-05-02T00:00:00Z", "endDate": "2025-05-15T00:00:00Z", "submitByDate": "2025-05-13T00:00:00Z", "checkDate": "2025-05-15T00:00:00Z", "checkCount": 4, "id": "e08d9ed6-9ffb-48e3-9344-617d6f5520f7", "companyId": "00907228", "type": "payperiod", "_rid": "NmJkAKiCbEcdvgIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcdvgIAAAAAAA==/", "_etag": "\"a300edd7-0000-0100-0000-68701d090000\"", "_attachments": "attachments/", "_ts": 1752177929}, {"payPeriodId": "1140035667502617", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-05-16T00:00:00Z", "endDate": "2025-06-01T00:00:00Z", "submitByDate": "2025-05-28T00:00:00Z", "checkDate": "2025-05-30T00:00:00Z", "checkCount": 4, "id": "b7bc2150-8ce2-4a21-9760-58b77ce24157", "companyId": "00907228", "type": "payperiod", "_rid": "NmJkAKiCbEcevgIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcevgIAAAAAAA==/", "_etag": "\"a300efd7-0000-0100-0000-68701d090000\"", "_attachments": "attachments/", "_ts": 1752177929}, {"payPeriodId": "1140035667502618", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-06-02T00:00:00Z", "endDate": "2025-06-15T00:00:00Z", "submitByDate": "2025-06-11T00:00:00Z", "checkDate": "2025-06-13T00:00:00Z", "checkCount": 4, "id": "2ee7e84e-05cc-4a2a-827d-284412882ff1", "companyId": "00907228", "type": "payperiod", "_rid": "NmJkAKiCbEcfvgIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcfvgIAAAAAAA==/", "_etag": "\"a300f2d7-0000-0100-0000-68701d0a0000\"", "_attachments": "attachments/", "_ts": 1752177930}, {"payPeriodId": "1140035783906181", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-06-16T00:00:00Z", "endDate": "2025-07-01T00:00:00Z", "submitByDate": "2025-06-27T00:00:00Z", "checkDate": "2025-07-01T00:00:00Z", "checkCount": 4, "id": "b4ffd22a-8f6c-4909-a244-7234e8059d4a", "companyId": "00907228", "type": "payperiod", "_rid": "NmJkAKiCbEcgvgIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcgvgIAAAAAAA==/", "_etag": "\"a300f5d7-0000-0100-0000-68701d0a0000\"", "_attachments": "attachments/", "_ts": 1752177930}, {"payPeriodId": "1140035783906182", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-07-02T00:00:00Z", "endDate": "2025-07-15T00:00:00Z", "submitByDate": "2025-07-11T00:00:00Z", "checkDate": "2025-07-15T00:00:00Z", "checkCount": 0, "id": "4e7f9242-4a84-4016-aaa0-a04a94f1d735", "companyId": "00907228", "type": "payperiod", "_rid": "NmJkAKiCbEchvgIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEchvgIAAAAAAA==/", "_etag": "\"a300f8d7-0000-0100-0000-68701d0a0000\"", "_attachments": "attachments/", "_ts": 1752177930}, {"payPeriodId": "1140035904959246", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-07-16T00:00:00Z", "endDate": "2025-08-01T00:00:00Z", "submitByDate": "2025-07-30T00:00:00Z", "checkDate": "2025-08-01T00:00:00Z", "checkCount": 0, "id": "6bda9782-2afa-4035-aad3-0f6991935259", "companyId": "00907228", "type": "payperiod", "_rid": "NmJkAKiCbEcivgIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcivgIAAAAAAA==/", "_etag": "\"a300fcd7-0000-0100-0000-68701d0a0000\"", "_attachments": "attachments/", "_ts": 1752177930}, {"payPeriodId": "1140035904959247", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-08-02T00:00:00Z", "endDate": "2025-08-15T00:00:00Z", "submitByDate": "2025-08-13T00:00:00Z", "checkDate": "2025-08-15T00:00:00Z", "checkCount": 0, "id": "9449d82f-688b-4c2a-b01f-bd743a1d0058", "companyId": "00907228", "type": "payperiod", "_rid": "NmJkAKiCbEcjvgIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcjvgIAAAAAAA==/", "_etag": "\"a30000d8-0000-0100-0000-68701d0a0000\"", "_attachments": "attachments/", "_ts": 1752177930}, {"payPeriodId": "1140036027266428", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-08-16T00:00:00Z", "endDate": "2025-09-01T00:00:00Z", "submitByDate": "2025-08-27T00:00:00Z", "checkDate": "2025-08-29T00:00:00Z", "checkCount": 0, "id": "22da72e7-3611-4661-9e34-4013cc4a9b74", "companyId": "00907228", "type": "payperiod", "_rid": "NmJkAKiCbEckvgIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEckvgIAAAAAAA==/", "_etag": "\"a30003d8-0000-0100-0000-68701d0a0000\"", "_attachments": "attachments/", "_ts": 1752177930}, {"payPeriodId": "1140036027266429", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-09-02T00:00:00Z", "endDate": "2025-09-15T00:00:00Z", "submitByDate": "2025-09-11T00:00:00Z", "checkDate": "2025-09-15T00:00:00Z", "checkCount": 0, "id": "f92e7cf3-ca95-4a08-827f-42ddb860a280", "companyId": "00907228", "type": "payperiod", "_rid": "NmJkAKiCbEclvgIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEclvgIAAAAAAA==/", "_etag": "\"a30008d8-0000-0100-0000-68701d0a0000\"", "_attachments": "attachments/", "_ts": 1752177930}, {"payPeriodId": "1140036169899340", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-09-16T00:00:00Z", "endDate": "2025-10-01T00:00:00Z", "submitByDate": "2025-09-29T00:00:00Z", "checkDate": "2025-10-01T00:00:00Z", "checkCount": 0, "id": "d70556d9-a8cc-487a-82fa-d2ce60772038", "companyId": "00907228", "type": "payperiod", "_rid": "NmJkAKiCbEcmvgIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcmvgIAAAAAAA==/", "_etag": "\"a3000bd8-0000-0100-0000-68701d0a0000\"", "_attachments": "attachments/", "_ts": 1752177930}, {"payPeriodId": "1140036169899341", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-10-02T00:00:00Z", "endDate": "2025-10-15T00:00:00Z", "submitByDate": "2025-10-13T00:00:00Z", "checkDate": "2025-10-15T00:00:00Z", "checkCount": 0, "id": "e8c5636b-6bd9-4bcf-941c-5f14bfc2dee4", "companyId": "00907228", "type": "payperiod", "_rid": "NmJkAKiCbEcnvgIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcnvgIAAAAAAA==/", "_etag": "\"a30010d8-0000-0100-0000-68701d0a0000\"", "_attachments": "attachments/", "_ts": 1752177930}], "links": [{"rel": "self", "href": "https://ca-payroll-ai-mock-sb-001-secure.nicebeach-8a7a52ab.eastus.azurecontainerapps.io/ca/companies/00907228/payperiods"}]}, "status_code": 200}