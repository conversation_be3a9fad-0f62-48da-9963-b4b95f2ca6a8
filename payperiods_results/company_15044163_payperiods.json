{"success": true, "company_id": "15044163", "data": {"metadata": {"contentItemCount": 38}, "content": [{"payPeriodId": "1060038969768702", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (3)", "startDate": "2025-01-01T00:00:00Z", "endDate": "2025-01-15T00:00:00Z", "submitByDate": "2025-01-29T00:00:00Z", "checkDate": "2025-01-31T00:00:00Z", "checkCount": 2, "id": "207591fd-d125-4922-8989-1447fdf2d227", "companyId": "15044163", "type": "payperiod", "_rid": "NmJkAKiCbEdx0AIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdx0AIAAAAAAA==/", "_etag": "\"a400ae11-0000-0100-0000-68701e830000\"", "_attachments": "attachments/", "_ts": 1752178307}, {"payPeriodId": "1060039112231086", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (3)", "startDate": "2025-01-16T00:00:00Z", "endDate": "2025-01-31T00:00:00Z", "submitByDate": "2025-02-12T00:00:00Z", "checkDate": "2025-02-14T00:00:00Z", "checkCount": 2, "id": "ee9a94a3-6cf5-48fd-92a9-30cc58386c27", "companyId": "15044163", "type": "payperiod", "_rid": "NmJkAKiCbEdy0AIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdy0AIAAAAAAA==/", "_etag": "\"a400b211-0000-0100-0000-68701e830000\"", "_attachments": "attachments/", "_ts": 1752178307}, {"payPeriodId": "1060039112231087", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (3)", "startDate": "2025-02-01T00:00:00Z", "endDate": "2025-02-15T00:00:00Z", "submitByDate": "2025-03-03T00:00:00Z", "checkDate": "2025-03-04T00:00:00Z", "checkCount": 2, "id": "313168f6-a197-4847-99d4-1f1328155537", "companyId": "15044163", "type": "payperiod", "_rid": "NmJkAKiCbEdz0AIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdz0AIAAAAAAA==/", "_etag": "\"a400b411-0000-0100-0000-68701e830000\"", "_attachments": "attachments/", "_ts": 1752178307}, {"payPeriodId": "1060039299723770", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (3)", "startDate": "2025-02-15T00:00:00Z", "endDate": "2025-02-28T00:00:00Z", "submitByDate": "2025-03-12T00:00:00Z", "checkDate": "2025-03-14T00:00:00Z", "checkCount": 2, "id": "a231ca69-0582-473f-b4c0-bcb93b0a73d4", "companyId": "15044163", "type": "payperiod", "_rid": "NmJkAKiCbEd00AIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEd00AIAAAAAAA==/", "_etag": "\"a400b811-0000-0100-0000-68701e830000\"", "_attachments": "attachments/", "_ts": 1752178307}, {"payPeriodId": "1060039299723771", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (3)", "startDate": "2025-03-01T00:00:00Z", "endDate": "2025-03-15T00:00:00Z", "submitByDate": "2025-03-28T00:00:00Z", "checkDate": "2025-04-01T00:00:00Z", "checkCount": 0, "id": "a47d6e8d-689c-432e-ae68-a87d99eed221", "companyId": "15044163", "type": "payperiod", "_rid": "NmJkAKiCbEd10AIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEd10AIAAAAAAA==/", "_etag": "\"a400b911-0000-0100-0000-68701e830000\"", "_attachments": "attachments/", "_ts": 1752178307}, {"payPeriodId": "1060039426282572", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (3)", "startDate": "2025-03-16T00:00:00Z", "endDate": "2025-03-31T00:00:00Z", "submitByDate": "2025-04-14T00:00:00Z", "checkDate": "2025-04-15T00:00:00Z", "checkCount": 0, "id": "973a3206-ce96-4065-b4f7-2c9920729fac", "companyId": "15044163", "type": "payperiod", "_rid": "NmJkAKiCbEd20AIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEd20AIAAAAAAA==/", "_etag": "\"a400bd11-0000-0100-0000-68701e830000\"", "_attachments": "attachments/", "_ts": 1752178307}, {"payPeriodId": "1060039426282573", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (3)", "startDate": "2025-04-01T00:00:00Z", "endDate": "2025-04-15T00:00:00Z", "submitByDate": "2025-04-29T00:00:00Z", "checkDate": "2025-05-01T00:00:00Z", "checkCount": 0, "id": "b74430ec-648f-48c0-8e50-3dca1d2b7c2d", "companyId": "15044163", "type": "payperiod", "_rid": "NmJkAKiCbEd30AIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEd30AIAAAAAAA==/", "_etag": "\"a400c111-0000-0100-0000-68701e840000\"", "_attachments": "attachments/", "_ts": 1752178308}, {"payPeriodId": "1060039580455400", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (3)", "startDate": "2025-04-16T00:00:00Z", "endDate": "2025-04-30T00:00:00Z", "submitByDate": "2025-05-14T00:00:00Z", "checkDate": "2025-05-15T00:00:00Z", "checkCount": 0, "id": "1373e55b-1889-4381-98b1-f31c4ba95cd9", "companyId": "15044163", "type": "payperiod", "_rid": "NmJkAKiCbEd40AIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEd40AIAAAAAAA==/", "_etag": "\"a400c411-0000-0100-0000-68701e840000\"", "_attachments": "attachments/", "_ts": 1752178308}, {"payPeriodId": "1060039580455401", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (3)", "startDate": "2025-05-01T00:00:00Z", "endDate": "2025-05-15T00:00:00Z", "submitByDate": "2025-06-01T00:00:00Z", "checkDate": "2025-06-02T00:00:00Z", "checkCount": 0, "id": "352f48b0-7702-4a4b-832e-3c82998547cf", "companyId": "15044163", "type": "payperiod", "_rid": "NmJkAKiCbEd50AIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEd50AIAAAAAAA==/", "_etag": "\"a400c711-0000-0100-0000-68701e840000\"", "_attachments": "attachments/", "_ts": 1752178308}, {"payPeriodId": "1060040262277115", "status": "INITIAL", "description": "Semi-monthly Payroll (3)", "startDate": "2025-05-16T00:00:00Z", "endDate": "2025-05-31T00:00:00Z", "submitByDate": "2025-06-12T00:00:00Z", "checkDate": "2025-06-16T00:00:00Z", "checkCount": 0, "id": "e008849d-28bc-4563-9a5f-6ba2dd3f6e46", "companyId": "15044163", "type": "payperiod", "_rid": "NmJkAKiCbEd60AIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEd60AIAAAAAAA==/", "_etag": "\"a400ca11-0000-0100-0000-68701e840000\"", "_attachments": "attachments/", "_ts": 1752178308}, {"payPeriodId": "1060039736181242", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (3)", "startDate": "2025-06-01T00:00:00Z", "endDate": "2025-06-15T00:00:00Z", "submitByDate": "2025-06-27T00:00:00Z", "checkDate": "2025-07-01T00:00:00Z", "checkCount": 0, "id": "6f31371e-6fd3-4f3b-8fa2-84f3f323b748", "companyId": "15044163", "type": "payperiod", "_rid": "NmJkAKiCbEd70AIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEd70AIAAAAAAA==/", "_etag": "\"a400cf11-0000-0100-0000-68701e840000\"", "_attachments": "attachments/", "_ts": 1752178308}, {"payPeriodId": "1060039873439265", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (3)", "startDate": "2025-06-16T00:00:00Z", "endDate": "2025-06-30T00:00:00Z", "submitByDate": "2025-07-14T00:00:00Z", "checkDate": "2025-07-16T00:00:00Z", "checkCount": 0, "id": "852f8d14-2608-4833-8415-18d73e39a194", "companyId": "15044163", "type": "payperiod", "_rid": "NmJkAKiCbEd80AIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEd80AIAAAAAAA==/", "_etag": "\"a400d211-0000-0100-0000-68701e840000\"", "_attachments": "attachments/", "_ts": 1752178308}, {"payPeriodId": "1060039873439266", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (3)", "startDate": "2025-07-01T00:00:00Z", "endDate": "2025-07-15T00:00:00Z", "submitByDate": "2025-07-30T00:00:00Z", "checkDate": "2025-08-01T00:00:00Z", "checkCount": 0, "id": "d0e01a44-b022-46db-9db1-84e41767efd7", "companyId": "15044163", "type": "payperiod", "_rid": "NmJkAKiCbEd90AIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEd90AIAAAAAAA==/", "_etag": "\"a400d311-0000-0100-0000-68701e840000\"", "_attachments": "attachments/", "_ts": 1752178308}, {"payPeriodId": "1060040042519369", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (3)", "startDate": "2025-07-16T00:00:00Z", "endDate": "2025-07-31T00:00:00Z", "submitByDate": "2025-08-13T00:00:00Z", "checkDate": "2025-08-15T00:00:00Z", "checkCount": 0, "id": "09cb7d04-33cf-4cab-88c3-a24d0c066ef3", "companyId": "15044163", "type": "payperiod", "_rid": "NmJkAKiCbEd+0AIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEd+0AIAAAAAAA==/", "_etag": "\"a400d411-0000-0100-0000-68701e840000\"", "_attachments": "attachments/", "_ts": 1752178308}, {"payPeriodId": "1060040042519370", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (3)", "startDate": "2025-08-01T00:00:00Z", "endDate": "2025-08-15T00:00:00Z", "submitByDate": "2025-08-27T00:00:00Z", "checkDate": "2025-08-29T00:00:00Z", "checkCount": 0, "id": "1ed5984a-febb-4adc-8bce-7569d50aa95b", "companyId": "15044163", "type": "payperiod", "_rid": "NmJkAKiCbEd-0AIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEd-0AIAAAAAAA==/", "_etag": "\"a400d611-0000-0100-0000-68701e840000\"", "_attachments": "attachments/", "_ts": 1752178308}, {"payPeriodId": "1060040193910111", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (3)", "startDate": "2025-08-16T00:00:00Z", "endDate": "2025-08-31T00:00:00Z", "submitByDate": "2025-09-12T00:00:00Z", "checkDate": "2025-09-16T00:00:00Z", "checkCount": 0, "id": "c7a86fb4-b649-4c40-ab32-c404692f8634", "companyId": "15044163", "type": "payperiod", "_rid": "NmJkAKiCbEeA0AIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeA0AIAAAAAAA==/", "_etag": "\"a400d711-0000-0100-0000-68701e840000\"", "_attachments": "attachments/", "_ts": 1752178308}, {"payPeriodId": "1060040193910112", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (3)", "startDate": "2025-09-01T00:00:00Z", "endDate": "2025-09-15T00:00:00Z", "submitByDate": "2025-09-29T00:00:00Z", "checkDate": "2025-10-01T00:00:00Z", "checkCount": 0, "id": "50ecca60-e295-46d5-a696-0a1f24724aa4", "companyId": "15044163", "type": "payperiod", "_rid": "NmJkAKiCbEeB0AIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeB0AIAAAAAAA==/", "_etag": "\"a400da11-0000-0100-0000-68701e840000\"", "_attachments": "attachments/", "_ts": 1752178308}, {"payPeriodId": "1060040330830627", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (3)", "startDate": "2025-09-16T00:00:00Z", "endDate": "2025-09-30T00:00:00Z", "submitByDate": "2025-10-14T00:00:00Z", "checkDate": "2025-10-16T00:00:00Z", "checkCount": 0, "id": "bcee1217-421d-4826-b101-50b76cfee4b0", "companyId": "15044163", "type": "payperiod", "_rid": "NmJkAKiCbEeC0AIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeC0AIAAAAAAA==/", "_etag": "\"a400db11-0000-0100-0000-68701e840000\"", "_attachments": "attachments/", "_ts": 1752178308}, {"payPeriodId": "1060040330830628", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (3)", "startDate": "2025-10-01T00:00:00Z", "endDate": "2025-10-15T00:00:00Z", "submitByDate": "2025-10-29T00:00:00Z", "checkDate": "2025-10-31T00:00:00Z", "checkCount": 0, "id": "c0aa4abd-2d72-42cf-9b71-c52e1c13068e", "companyId": "15044163", "type": "payperiod", "_rid": "NmJkAKiCbEeD0AIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeD0AIAAAAAAA==/", "_etag": "\"a400df11-0000-0100-0000-68701e850000\"", "_attachments": "attachments/", "_ts": 1752178309}, {"payPeriodId": "1060038969768702", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (3)", "startDate": "2025-01-01T00:00:00Z", "endDate": "2025-01-15T00:00:00Z", "submitByDate": "2025-01-29T00:00:00Z", "checkDate": "2025-01-31T00:00:00Z", "checkCount": 2, "id": "01ac2f66-c07a-46bd-bea2-18d145768650", "companyId": "15044163", "type": "payperiod", "_rid": "NmJkAKiCbEeI0AIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeI0AIAAAAAAA==/", "_etag": "\"a400ec11-0000-0100-0000-68701e850000\"", "_attachments": "attachments/", "_ts": 1752178309}, {"payPeriodId": "1060039112231086", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (3)", "startDate": "2025-01-16T00:00:00Z", "endDate": "2025-01-31T00:00:00Z", "submitByDate": "2025-02-12T00:00:00Z", "checkDate": "2025-02-14T00:00:00Z", "checkCount": 2, "id": "8936be38-c24b-4ad2-91f2-1991d873ad75", "companyId": "15044163", "type": "payperiod", "_rid": "NmJkAKiCbEeJ0AIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeJ0AIAAAAAAA==/", "_etag": "\"a400ee11-0000-0100-0000-68701e850000\"", "_attachments": "attachments/", "_ts": 1752178309}, {"payPeriodId": "1060039112231087", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (3)", "startDate": "2025-02-01T00:00:00Z", "endDate": "2025-02-15T00:00:00Z", "submitByDate": "2025-03-03T00:00:00Z", "checkDate": "2025-03-04T00:00:00Z", "checkCount": 2, "id": "79d2d342-e4fa-4e81-9847-f4c35ce25507", "companyId": "15044163", "type": "payperiod", "_rid": "NmJkAKiCbEeK0AIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeK0AIAAAAAAA==/", "_etag": "\"a400f311-0000-0100-0000-68701e850000\"", "_attachments": "attachments/", "_ts": 1752178309}, {"payPeriodId": "1060039299723770", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (3)", "startDate": "2025-02-15T00:00:00Z", "endDate": "2025-02-28T00:00:00Z", "submitByDate": "2025-03-12T00:00:00Z", "checkDate": "2025-03-14T00:00:00Z", "checkCount": 2, "id": "fd6fb290-ba50-4321-81d8-761fd27eb91f", "companyId": "15044163", "type": "payperiod", "_rid": "NmJkAKiCbEeL0AIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeL0AIAAAAAAA==/", "_etag": "\"a400fa11-0000-0100-0000-68701e850000\"", "_attachments": "attachments/", "_ts": 1752178309}, {"payPeriodId": "1060039299723771", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (3)", "startDate": "2025-03-01T00:00:00Z", "endDate": "2025-03-15T00:00:00Z", "submitByDate": "2025-03-28T00:00:00Z", "checkDate": "2025-04-01T00:00:00Z", "checkCount": 2, "id": "8b9b845f-d13a-49da-b2f9-dec9704290f7", "companyId": "15044163", "type": "payperiod", "_rid": "NmJkAKiCbEeM0AIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeM0AIAAAAAAA==/", "_etag": "\"a400fc11-0000-0100-0000-68701e850000\"", "_attachments": "attachments/", "_ts": 1752178309}, {"payPeriodId": "1060039426282572", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (3)", "startDate": "2025-03-16T00:00:00Z", "endDate": "2025-03-31T00:00:00Z", "submitByDate": "2025-04-14T00:00:00Z", "checkDate": "2025-04-15T00:00:00Z", "checkCount": 2, "id": "2b0621c8-5d98-463f-90f6-6e625a0e9455", "companyId": "15044163", "type": "payperiod", "_rid": "NmJkAKiCbEeN0AIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeN0AIAAAAAAA==/", "_etag": "\"a400fe11-0000-0100-0000-68701e850000\"", "_attachments": "attachments/", "_ts": 1752178309}, {"payPeriodId": "1060039426282573", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (3)", "startDate": "2025-04-01T00:00:00Z", "endDate": "2025-04-15T00:00:00Z", "submitByDate": "2025-04-29T00:00:00Z", "checkDate": "2025-05-01T00:00:00Z", "checkCount": 2, "id": "e481643d-7427-4717-bcbc-701a6151d01b", "companyId": "15044163", "type": "payperiod", "_rid": "NmJkAKiCbEeO0AIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeO0AIAAAAAAA==/", "_etag": "\"a4000212-0000-0100-0000-68701e850000\"", "_attachments": "attachments/", "_ts": 1752178309}, {"payPeriodId": "1060039580455400", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (3)", "startDate": "2025-04-16T00:00:00Z", "endDate": "2025-04-30T00:00:00Z", "submitByDate": "2025-05-14T00:00:00Z", "checkDate": "2025-05-15T00:00:00Z", "checkCount": 2, "id": "0de1cd92-1e02-4ffc-8360-10fe3fdd705f", "companyId": "15044163", "type": "payperiod", "_rid": "NmJkAKiCbEeP0AIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeP0AIAAAAAAA==/", "_etag": "\"a4000512-0000-0100-0000-68701e850000\"", "_attachments": "attachments/", "_ts": 1752178309}, {"payPeriodId": "1060039580455401", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (3)", "startDate": "2025-05-01T00:00:00Z", "endDate": "2025-05-15T00:00:00Z", "submitByDate": "2025-06-01T00:00:00Z", "checkDate": "2025-06-02T00:00:00Z", "checkCount": 2, "id": "93a3a301-04ce-44a7-8ef5-147daac185bf", "companyId": "15044163", "type": "payperiod", "_rid": "NmJkAKiCbEeQ0AIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeQ0AIAAAAAAA==/", "_etag": "\"a4000812-0000-0100-0000-68701e860000\"", "_attachments": "attachments/", "_ts": 1752178310}, {"payPeriodId": "1060040262277115", "status": "COMPLETED", "description": "Semi-monthly Payroll (3)", "startDate": "2025-05-16T00:00:00Z", "endDate": "2025-05-31T00:00:00Z", "submitByDate": "2025-06-12T00:00:00Z", "checkDate": "2025-06-16T00:00:00Z", "checkCount": 2, "id": "96418eb8-539b-48bd-918a-4a502958ec2c", "companyId": "15044163", "type": "payperiod", "_rid": "NmJkAKiCbEeR0AIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeR0AIAAAAAAA==/", "_etag": "\"a4000a12-0000-0100-0000-68701e860000\"", "_attachments": "attachments/", "_ts": 1752178310}, {"payPeriodId": "1060039736181242", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (3)", "startDate": "2025-06-01T00:00:00Z", "endDate": "2025-06-15T00:00:00Z", "submitByDate": "2025-06-27T00:00:00Z", "checkDate": "2025-07-01T00:00:00Z", "checkCount": 2, "id": "9739cee3-f34e-4c44-a4f7-7128a4dd3c6a", "companyId": "15044163", "type": "payperiod", "_rid": "NmJkAKiCbEeS0AIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeS0AIAAAAAAA==/", "_etag": "\"a4000c12-0000-0100-0000-68701e860000\"", "_attachments": "attachments/", "_ts": 1752178310}, {"payPeriodId": "1060039873439265", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (3)", "startDate": "2025-06-16T00:00:00Z", "endDate": "2025-06-30T00:00:00Z", "submitByDate": "2025-07-14T00:00:00Z", "checkDate": "2025-07-16T00:00:00Z", "checkCount": 0, "id": "1032c748-fdeb-42bb-8d03-4041a390904f", "companyId": "15044163", "type": "payperiod", "_rid": "NmJkAKiCbEeT0AIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeT0AIAAAAAAA==/", "_etag": "\"a4000f12-0000-0100-0000-68701e860000\"", "_attachments": "attachments/", "_ts": 1752178310}, {"payPeriodId": "1060039873439266", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (3)", "startDate": "2025-07-01T00:00:00Z", "endDate": "2025-07-15T00:00:00Z", "submitByDate": "2025-07-30T00:00:00Z", "checkDate": "2025-08-01T00:00:00Z", "checkCount": 0, "id": "2fa5fee0-3f1a-47b9-b114-9baf5ffc7179", "companyId": "15044163", "type": "payperiod", "_rid": "NmJkAKiCbEeU0AIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeU0AIAAAAAAA==/", "_etag": "\"a4001112-0000-0100-0000-68701e860000\"", "_attachments": "attachments/", "_ts": 1752178310}, {"payPeriodId": "1060040042519369", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (3)", "startDate": "2025-07-16T00:00:00Z", "endDate": "2025-07-31T00:00:00Z", "submitByDate": "2025-08-13T00:00:00Z", "checkDate": "2025-08-15T00:00:00Z", "checkCount": 0, "id": "767ae120-8e6d-4715-9534-4774b5da4312", "companyId": "15044163", "type": "payperiod", "_rid": "NmJkAKiCbEeV0AIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeV0AIAAAAAAA==/", "_etag": "\"a4001412-0000-0100-0000-68701e860000\"", "_attachments": "attachments/", "_ts": 1752178310}, {"payPeriodId": "1060040042519370", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (3)", "startDate": "2025-08-01T00:00:00Z", "endDate": "2025-08-15T00:00:00Z", "submitByDate": "2025-08-27T00:00:00Z", "checkDate": "2025-08-29T00:00:00Z", "checkCount": 0, "id": "d5eafe7b-e19e-4ce3-a28f-64e7c92ed86f", "companyId": "15044163", "type": "payperiod", "_rid": "NmJkAKiCbEeW0AIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeW0AIAAAAAAA==/", "_etag": "\"a4001512-0000-0100-0000-68701e860000\"", "_attachments": "attachments/", "_ts": 1752178310}, {"payPeriodId": "1060040193910111", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (3)", "startDate": "2025-08-16T00:00:00Z", "endDate": "2025-08-31T00:00:00Z", "submitByDate": "2025-09-12T00:00:00Z", "checkDate": "2025-09-16T00:00:00Z", "checkCount": 0, "id": "d396bc91-05c1-4867-893d-f09950759deb", "companyId": "15044163", "type": "payperiod", "_rid": "NmJkAKiCbEeX0AIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeX0AIAAAAAAA==/", "_etag": "\"a4001912-0000-0100-0000-68701e860000\"", "_attachments": "attachments/", "_ts": 1752178310}, {"payPeriodId": "1060040193910112", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (3)", "startDate": "2025-09-01T00:00:00Z", "endDate": "2025-09-15T00:00:00Z", "submitByDate": "2025-09-29T00:00:00Z", "checkDate": "2025-10-01T00:00:00Z", "checkCount": 0, "id": "51c0de17-3e41-4a48-910f-f48c82caa122", "companyId": "15044163", "type": "payperiod", "_rid": "NmJkAKiCbEeY0AIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeY0AIAAAAAAA==/", "_etag": "\"a4001d12-0000-0100-0000-68701e860000\"", "_attachments": "attachments/", "_ts": 1752178310}, {"payPeriodId": "1060040330830627", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (3)", "startDate": "2025-09-16T00:00:00Z", "endDate": "2025-09-30T00:00:00Z", "submitByDate": "2025-10-14T00:00:00Z", "checkDate": "2025-10-16T00:00:00Z", "checkCount": 0, "id": "b647d07e-0cf3-4c78-bf1c-5c646df2844a", "companyId": "15044163", "type": "payperiod", "_rid": "NmJkAKiCbEeZ0AIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeZ0AIAAAAAAA==/", "_etag": "\"a4001f12-0000-0100-0000-68701e860000\"", "_attachments": "attachments/", "_ts": 1752178310}, {"payPeriodId": "1060040330830628", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (3)", "startDate": "2025-10-01T00:00:00Z", "endDate": "2025-10-15T00:00:00Z", "submitByDate": "2025-10-29T00:00:00Z", "checkDate": "2025-10-31T00:00:00Z", "checkCount": 0, "id": "64e05d35-2d13-415e-b98b-0c0fc999cc39", "companyId": "15044163", "type": "payperiod", "_rid": "NmJkAKiCbEea0AIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEea0AIAAAAAAA==/", "_etag": "\"a4002112-0000-0100-0000-68701e860000\"", "_attachments": "attachments/", "_ts": 1752178310}], "links": [{"rel": "self", "href": "https://ca-payroll-ai-mock-sb-001-secure.nicebeach-8a7a52ab.eastus.azurecontainerapps.io/ca/companies/15044163/payperiods"}]}, "status_code": 200}