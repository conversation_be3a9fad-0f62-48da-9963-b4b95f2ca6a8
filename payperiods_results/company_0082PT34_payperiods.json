{"success": true, "company_id": "0082PT34", "data": {"metadata": {"contentItemCount": 40}, "content": [{"payPeriodId": "1060038969913447", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2024-12-30T00:00:00Z", "endDate": "2025-01-12T00:00:00Z", "submitByDate": "2025-01-13T00:00:00Z", "checkDate": "2025-01-15T00:00:00Z", "checkCount": 10, "id": "fcd536d3-3066-4bab-88f5-fb75ce2e08d2", "companyId": "0082PT34", "type": "payperiod", "_rid": "NmJkAKiCbEeO3gMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeO3gMAAAAAAA==/", "_etag": "\"a7009a44-0000-0100-0000-687034690000\"", "_attachments": "attachments/", "_ts": 1752183913}, {"payPeriodId": "1060039042344787", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-01-13T00:00:00Z", "endDate": "2025-01-26T00:00:00Z", "submitByDate": "2025-01-27T00:00:00Z", "checkDate": "2025-01-29T00:00:00Z", "checkCount": 11, "id": "a6812144-bd57-4f33-b256-bf2b15283827", "companyId": "0082PT34", "type": "payperiod", "_rid": "NmJkAKiCbEeP3gMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeP3gMAAAAAAA==/", "_etag": "\"a7009b44-0000-0100-0000-687034690000\"", "_attachments": "attachments/", "_ts": 1752183913}, {"payPeriodId": "1060039122502312", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-01-27T00:00:00Z", "endDate": "2025-02-09T00:00:00Z", "submitByDate": "2025-02-10T00:00:00Z", "checkDate": "2025-02-12T00:00:00Z", "checkCount": 15, "id": "fc6b5903-7e01-44e7-b350-b3d7645e2dfe", "companyId": "0082PT34", "type": "payperiod", "_rid": "NmJkAKiCbEeQ3gMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeQ3gMAAAAAAA==/", "_etag": "\"a7009e44-0000-0100-0000-687034690000\"", "_attachments": "attachments/", "_ts": 1752183913}, {"payPeriodId": "1060039192061857", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-02-10T00:00:00Z", "endDate": "2025-02-23T00:00:00Z", "submitByDate": "2025-02-24T00:00:00Z", "checkDate": "2025-02-26T00:00:00Z", "checkCount": 12, "id": "69ba1ec4-62a4-443d-877a-90d07fdd69c0", "companyId": "0082PT34", "type": "payperiod", "_rid": "NmJkAKiCbEeR3gMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeR3gMAAAAAAA==/", "_etag": "\"a700a144-0000-0100-0000-687034690000\"", "_attachments": "attachments/", "_ts": 1752183913}, {"payPeriodId": "1060039264515354", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-02-24T00:00:00Z", "endDate": "2025-03-09T00:00:00Z", "submitByDate": "2025-03-10T00:00:00Z", "checkDate": "2025-03-12T00:00:00Z", "checkCount": 14, "id": "619be7fc-960c-43d6-a539-ee5dc34449c0", "companyId": "0082PT34", "type": "payperiod", "_rid": "NmJkAKiCbEeS3gMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeS3gMAAAAAAA==/", "_etag": "\"a700a544-0000-0100-0000-687034690000\"", "_attachments": "attachments/", "_ts": 1752183913}, {"payPeriodId": "1060039339127065", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-03-10T00:00:00Z", "endDate": "2025-03-23T00:00:00Z", "submitByDate": "2025-03-24T00:00:00Z", "checkDate": "2025-03-26T00:00:00Z", "checkCount": 13, "id": "ce6e7dd3-5b2d-49e2-9697-ee485321056f", "companyId": "0082PT34", "type": "payperiod", "_rid": "NmJkAKiCbEeT3gMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeT3gMAAAAAAA==/", "_etag": "\"a700a744-0000-0100-0000-687034690000\"", "_attachments": "attachments/", "_ts": 1752183913}, {"payPeriodId": "1060039416791960", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-03-24T00:00:00Z", "endDate": "2025-04-06T00:00:00Z", "submitByDate": "2025-04-07T00:00:00Z", "checkDate": "2025-04-09T00:00:00Z", "checkCount": 0, "id": "27302260-d95f-4154-8965-b242c3f80388", "companyId": "0082PT34", "type": "payperiod", "_rid": "NmJkAKiCbEeU3gMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeU3gMAAAAAAA==/", "_etag": "\"a700ab44-0000-0100-0000-687034690000\"", "_attachments": "attachments/", "_ts": 1752183913}, {"payPeriodId": "1060039486917432", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-04-07T00:00:00Z", "endDate": "2025-04-20T00:00:00Z", "submitByDate": "2025-04-21T00:00:00Z", "checkDate": "2025-04-23T00:00:00Z", "checkCount": 0, "id": "054df66f-88c5-4fe3-a920-7dbbc493fb05", "companyId": "0082PT34", "type": "payperiod", "_rid": "NmJkAKiCbEeV3gMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeV3gMAAAAAAA==/", "_etag": "\"a700ae44-0000-0100-0000-687034690000\"", "_attachments": "attachments/", "_ts": 1752183913}, {"payPeriodId": "1060039553667430", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-04-21T00:00:00Z", "endDate": "2025-05-04T00:00:00Z", "submitByDate": "2025-05-05T00:00:00Z", "checkDate": "2025-05-07T00:00:00Z", "checkCount": 0, "id": "c178b735-9fc5-4157-8446-f7bf2b19d832", "companyId": "0082PT34", "type": "payperiod", "_rid": "NmJkAKiCbEeW3gMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeW3gMAAAAAAA==/", "_etag": "\"a700b144-0000-0100-0000-6870346a0000\"", "_attachments": "attachments/", "_ts": 1752183914}, {"payPeriodId": "1060039623101268", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-05-05T00:00:00Z", "endDate": "2025-05-18T00:00:00Z", "submitByDate": "2025-05-19T00:00:00Z", "checkDate": "2025-05-21T00:00:00Z", "checkCount": 0, "id": "ab53ffbb-276e-4dea-bdb4-8ee0ee47b289", "companyId": "0082PT34", "type": "payperiod", "_rid": "NmJkAKiCbEeX3gMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeX3gMAAAAAAA==/", "_etag": "\"a700b344-0000-0100-0000-6870346a0000\"", "_attachments": "attachments/", "_ts": 1752183914}, {"payPeriodId": "1060039689460303", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-05-19T00:00:00Z", "endDate": "2025-06-01T00:00:00Z", "submitByDate": "2025-06-02T00:00:00Z", "checkDate": "2025-06-04T00:00:00Z", "checkCount": 0, "id": "56ca97ee-450e-460b-85e4-2f3ad8a5b1d2", "companyId": "0082PT34", "type": "payperiod", "_rid": "NmJkAKiCbEeY3gMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeY3gMAAAAAAA==/", "_etag": "\"a700b644-0000-0100-0000-6870346a0000\"", "_attachments": "attachments/", "_ts": 1752183914}, {"payPeriodId": "1060039760323895", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-06-02T00:00:00Z", "endDate": "2025-06-15T00:00:00Z", "submitByDate": "2025-06-16T00:00:00Z", "checkDate": "2025-06-18T00:00:00Z", "checkCount": 0, "id": "b601f610-4484-4717-9677-1e200ae156d3", "companyId": "0082PT34", "type": "payperiod", "_rid": "NmJkAKiCbEeZ3gMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeZ3gMAAAAAAA==/", "_etag": "\"a700b744-0000-0100-0000-6870346a0000\"", "_attachments": "attachments/", "_ts": 1752183914}, {"payPeriodId": "1060039827851483", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-06-16T00:00:00Z", "endDate": "2025-06-29T00:00:00Z", "submitByDate": "2025-06-30T00:00:00Z", "checkDate": "2025-07-02T00:00:00Z", "checkCount": 0, "id": "95eb95eb-2c4b-43be-8da7-ac3dfd9f3487", "companyId": "0082PT34", "type": "payperiod", "_rid": "NmJkAKiCbEea3gMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEea3gMAAAAAAA==/", "_etag": "\"a700b844-0000-0100-0000-6870346a0000\"", "_attachments": "attachments/", "_ts": 1752183914}, {"payPeriodId": "1060039905189014", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-06-30T00:00:00Z", "endDate": "2025-07-13T00:00:00Z", "submitByDate": "2025-07-14T00:00:00Z", "checkDate": "2025-07-16T00:00:00Z", "checkCount": 0, "id": "4b648d0e-5dc2-412a-a8f1-22d87e98632a", "companyId": "0082PT34", "type": "payperiod", "_rid": "NmJkAKiCbEeb3gMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeb3gMAAAAAAA==/", "_etag": "\"a700b944-0000-0100-0000-6870346a0000\"", "_attachments": "attachments/", "_ts": 1752183914}, {"payPeriodId": "1060039979326925", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-07-14T00:00:00Z", "endDate": "2025-07-27T00:00:00Z", "submitByDate": "2025-07-28T00:00:00Z", "checkDate": "2025-07-30T00:00:00Z", "checkCount": 0, "id": "6bd16594-3a9b-4863-82d0-f40af74d2b62", "companyId": "0082PT34", "type": "payperiod", "_rid": "NmJkAKiCbEec3gMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEec3gMAAAAAAA==/", "_etag": "\"a700bc44-0000-0100-0000-6870346a0000\"", "_attachments": "attachments/", "_ts": 1752183914}, {"payPeriodId": "1060040056083994", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-07-28T00:00:00Z", "endDate": "2025-08-10T00:00:00Z", "submitByDate": "2025-08-11T00:00:00Z", "checkDate": "2025-08-13T00:00:00Z", "checkCount": 0, "id": "fcf5385e-cdd4-44c2-a228-ce23f0d604f1", "companyId": "0082PT34", "type": "payperiod", "_rid": "NmJkAKiCbEed3gMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEed3gMAAAAAAA==/", "_etag": "\"a700bd44-0000-0100-0000-6870346a0000\"", "_attachments": "attachments/", "_ts": 1752183914}, {"payPeriodId": "1060040127588060", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-08-11T00:00:00Z", "endDate": "2025-08-24T00:00:00Z", "submitByDate": "2025-08-25T00:00:00Z", "checkDate": "2025-08-27T00:00:00Z", "checkCount": 0, "id": "26c0a395-4b19-4205-a183-af973aa68534", "companyId": "0082PT34", "type": "payperiod", "_rid": "NmJkAKiCbEee3gMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEee3gMAAAAAAA==/", "_etag": "\"a700bf44-0000-0100-0000-6870346a0000\"", "_attachments": "attachments/", "_ts": 1752183914}, {"payPeriodId": "1060040196245190", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-08-25T00:00:00Z", "endDate": "2025-09-07T00:00:00Z", "submitByDate": "2025-09-08T00:00:00Z", "checkDate": "2025-09-10T00:00:00Z", "checkCount": 0, "id": "e25bee11-9446-4bae-8bdf-2c76e6ece9c3", "companyId": "0082PT34", "type": "payperiod", "_rid": "NmJkAKiCbEef3gMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEef3gMAAAAAAA==/", "_etag": "\"a700c144-0000-0100-0000-6870346a0000\"", "_attachments": "attachments/", "_ts": 1752183914}, {"payPeriodId": "1060040264288775", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-09-08T00:00:00Z", "endDate": "2025-09-21T00:00:00Z", "submitByDate": "2025-09-22T00:00:00Z", "checkDate": "2025-09-24T00:00:00Z", "checkCount": 0, "id": "8dc5b2d2-a172-4a57-953e-d19c3cc197b9", "companyId": "0082PT34", "type": "payperiod", "_rid": "NmJkAKiCbEeg3gMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeg3gMAAAAAAA==/", "_etag": "\"a700c244-0000-0100-0000-6870346a0000\"", "_attachments": "attachments/", "_ts": 1752183914}, {"payPeriodId": "1060040335408591", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-09-22T00:00:00Z", "endDate": "2025-10-05T00:00:00Z", "submitByDate": "2025-10-06T00:00:00Z", "checkDate": "2025-10-08T00:00:00Z", "checkCount": 0, "id": "89b4c085-b643-48cf-8dc4-01a603769228", "companyId": "0082PT34", "type": "payperiod", "_rid": "NmJkAKiCbEeh3gMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeh3gMAAAAAAA==/", "_etag": "\"a700c444-0000-0100-0000-6870346a0000\"", "_attachments": "attachments/", "_ts": 1752183914}, {"payPeriodId": "1060038969913447", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2024-12-30T00:00:00Z", "endDate": "2025-01-12T00:00:00Z", "submitByDate": "2025-01-13T00:00:00Z", "checkDate": "2025-01-15T00:00:00Z", "checkCount": 10, "id": "b082c1ec-f2df-4a72-b7d8-7869809ca494", "companyId": "0082PT34", "type": "payperiod", "_rid": "NmJkAKiCbEew3gMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEew3gMAAAAAAA==/", "_etag": "\"a700de44-0000-0100-0000-6870346c0000\"", "_attachments": "attachments/", "_ts": 1752183916}, {"payPeriodId": "1060039042344787", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-01-13T00:00:00Z", "endDate": "2025-01-26T00:00:00Z", "submitByDate": "2025-01-27T00:00:00Z", "checkDate": "2025-01-29T00:00:00Z", "checkCount": 11, "id": "ea51c56b-c82e-41e9-850b-910aa15382c5", "companyId": "0082PT34", "type": "payperiod", "_rid": "NmJkAKiCbEex3gMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEex3gMAAAAAAA==/", "_etag": "\"a700e244-0000-0100-0000-6870346c0000\"", "_attachments": "attachments/", "_ts": 1752183916}, {"payPeriodId": "1060039122502312", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-01-27T00:00:00Z", "endDate": "2025-02-09T00:00:00Z", "submitByDate": "2025-02-10T00:00:00Z", "checkDate": "2025-02-12T00:00:00Z", "checkCount": 15, "id": "3604ba1a-b3bb-4f34-8440-1c16c87c6895", "companyId": "0082PT34", "type": "payperiod", "_rid": "NmJkAKiCbEey3gMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEey3gMAAAAAAA==/", "_etag": "\"a700e344-0000-0100-0000-6870346c0000\"", "_attachments": "attachments/", "_ts": 1752183916}, {"payPeriodId": "1060039192061857", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-02-10T00:00:00Z", "endDate": "2025-02-23T00:00:00Z", "submitByDate": "2025-02-24T00:00:00Z", "checkDate": "2025-02-26T00:00:00Z", "checkCount": 12, "id": "2b49078f-336a-4d2e-a287-98f67c5bc4a9", "companyId": "0082PT34", "type": "payperiod", "_rid": "NmJkAKiCbEez3gMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEez3gMAAAAAAA==/", "_etag": "\"a700e444-0000-0100-0000-6870346c0000\"", "_attachments": "attachments/", "_ts": 1752183916}, {"payPeriodId": "1060039264515354", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-02-24T00:00:00Z", "endDate": "2025-03-09T00:00:00Z", "submitByDate": "2025-03-10T00:00:00Z", "checkDate": "2025-03-12T00:00:00Z", "checkCount": 14, "id": "0c6f700f-d03f-4c5e-b5d4-0f14bd1c1bba", "companyId": "0082PT34", "type": "payperiod", "_rid": "NmJkAKiCbEe03gMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEe03gMAAAAAAA==/", "_etag": "\"a700e844-0000-0100-0000-6870346c0000\"", "_attachments": "attachments/", "_ts": 1752183916}, {"payPeriodId": "1060039339127065", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-03-10T00:00:00Z", "endDate": "2025-03-23T00:00:00Z", "submitByDate": "2025-03-24T00:00:00Z", "checkDate": "2025-03-26T00:00:00Z", "checkCount": 13, "id": "e4c717ac-8318-493c-8737-56f408d86b05", "companyId": "0082PT34", "type": "payperiod", "_rid": "NmJkAKiCbEe13gMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEe13gMAAAAAAA==/", "_etag": "\"a700ea44-0000-0100-0000-6870346c0000\"", "_attachments": "attachments/", "_ts": 1752183916}, {"payPeriodId": "1060039416791960", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-03-24T00:00:00Z", "endDate": "2025-04-06T00:00:00Z", "submitByDate": "2025-04-07T00:00:00Z", "checkDate": "2025-04-09T00:00:00Z", "checkCount": 12, "id": "2b7f66d4-179c-4ca6-8c25-e92e541decdf", "companyId": "0082PT34", "type": "payperiod", "_rid": "NmJkAKiCbEe23gMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEe23gMAAAAAAA==/", "_etag": "\"a700ec44-0000-0100-0000-6870346c0000\"", "_attachments": "attachments/", "_ts": 1752183916}, {"payPeriodId": "1060039486917432", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-04-07T00:00:00Z", "endDate": "2025-04-20T00:00:00Z", "submitByDate": "2025-04-21T00:00:00Z", "checkDate": "2025-04-23T00:00:00Z", "checkCount": 12, "id": "2bfd288c-7dfc-4017-999a-735cea7570b9", "companyId": "0082PT34", "type": "payperiod", "_rid": "NmJkAKiCbEe33gMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEe33gMAAAAAAA==/", "_etag": "\"a700ee44-0000-0100-0000-6870346c0000\"", "_attachments": "attachments/", "_ts": 1752183916}, {"payPeriodId": "1060039553667430", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-04-21T00:00:00Z", "endDate": "2025-05-04T00:00:00Z", "submitByDate": "2025-05-05T00:00:00Z", "checkDate": "2025-05-07T00:00:00Z", "checkCount": 12, "id": "b8003385-3296-4835-82c5-b2ae20477b15", "companyId": "0082PT34", "type": "payperiod", "_rid": "NmJkAKiCbEe43gMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEe43gMAAAAAAA==/", "_etag": "\"a700f044-0000-0100-0000-6870346c0000\"", "_attachments": "attachments/", "_ts": 1752183916}, {"payPeriodId": "1060039623101268", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-05-05T00:00:00Z", "endDate": "2025-05-18T00:00:00Z", "submitByDate": "2025-05-19T00:00:00Z", "checkDate": "2025-05-21T00:00:00Z", "checkCount": 11, "id": "f47c4729-87c1-4f62-8c9b-6e7979e3262f", "companyId": "0082PT34", "type": "payperiod", "_rid": "NmJkAKiCbEe53gMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEe53gMAAAAAAA==/", "_etag": "\"a700f244-0000-0100-0000-6870346c0000\"", "_attachments": "attachments/", "_ts": 1752183916}, {"payPeriodId": "1060039689460303", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-05-19T00:00:00Z", "endDate": "2025-06-01T00:00:00Z", "submitByDate": "2025-06-02T00:00:00Z", "checkDate": "2025-06-04T00:00:00Z", "checkCount": 10, "id": "7802c2b7-dfb8-497d-ae49-7847f63c84bd", "companyId": "0082PT34", "type": "payperiod", "_rid": "NmJkAKiCbEe63gMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEe63gMAAAAAAA==/", "_etag": "\"a700f444-0000-0100-0000-6870346c0000\"", "_attachments": "attachments/", "_ts": 1752183916}, {"payPeriodId": "1060039760323895", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-06-02T00:00:00Z", "endDate": "2025-06-15T00:00:00Z", "submitByDate": "2025-06-16T00:00:00Z", "checkDate": "2025-06-18T00:00:00Z", "checkCount": 11, "id": "b301dad1-df53-475d-b8c1-8378a8be0a3e", "companyId": "0082PT34", "type": "payperiod", "_rid": "NmJkAKiCbEe73gMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEe73gMAAAAAAA==/", "_etag": "\"a700f544-0000-0100-0000-6870346d0000\"", "_attachments": "attachments/", "_ts": 1752183917}, {"payPeriodId": "1060039827851483", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-06-16T00:00:00Z", "endDate": "2025-06-29T00:00:00Z", "submitByDate": "2025-06-30T00:00:00Z", "checkDate": "2025-07-02T00:00:00Z", "checkCount": 12, "id": "6d444550-f89b-4980-8da7-e6cf8e7eb422", "companyId": "0082PT34", "type": "payperiod", "_rid": "NmJkAKiCbEe83gMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEe83gMAAAAAAA==/", "_etag": "\"a700f644-0000-0100-0000-6870346d0000\"", "_attachments": "attachments/", "_ts": 1752183917}, {"payPeriodId": "1060039905189014", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-06-30T00:00:00Z", "endDate": "2025-07-13T00:00:00Z", "submitByDate": "2025-07-14T00:00:00Z", "checkDate": "2025-07-16T00:00:00Z", "checkCount": 0, "id": "eeb7e359-5ad7-470e-abb9-0864407aaf8f", "companyId": "0082PT34", "type": "payperiod", "_rid": "NmJkAKiCbEe93gMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEe93gMAAAAAAA==/", "_etag": "\"a700f944-0000-0100-0000-6870346d0000\"", "_attachments": "attachments/", "_ts": 1752183917}, {"payPeriodId": "1060039979326925", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-07-14T00:00:00Z", "endDate": "2025-07-27T00:00:00Z", "submitByDate": "2025-07-28T00:00:00Z", "checkDate": "2025-07-30T00:00:00Z", "checkCount": 0, "id": "c42b01a6-97d9-4ee4-b39f-2d50b696e922", "companyId": "0082PT34", "type": "payperiod", "_rid": "NmJkAKiCbEe+3gMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEe+3gMAAAAAAA==/", "_etag": "\"a700fa44-0000-0100-0000-6870346d0000\"", "_attachments": "attachments/", "_ts": 1752183917}, {"payPeriodId": "1060040056083994", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-07-28T00:00:00Z", "endDate": "2025-08-10T00:00:00Z", "submitByDate": "2025-08-11T00:00:00Z", "checkDate": "2025-08-13T00:00:00Z", "checkCount": 0, "id": "f20b8314-44f8-4dd8-9246-1c19dc2d0d99", "companyId": "0082PT34", "type": "payperiod", "_rid": "NmJkAKiCbEe-3gMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEe-3gMAAAAAAA==/", "_etag": "\"a700fe44-0000-0100-0000-6870346d0000\"", "_attachments": "attachments/", "_ts": 1752183917}, {"payPeriodId": "1060040127588060", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-08-11T00:00:00Z", "endDate": "2025-08-24T00:00:00Z", "submitByDate": "2025-08-25T00:00:00Z", "checkDate": "2025-08-27T00:00:00Z", "checkCount": 0, "id": "c0df83b9-9672-461c-bf44-abc05f65ff5e", "companyId": "0082PT34", "type": "payperiod", "_rid": "NmJkAKiCbEfA3gMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfA3gMAAAAAAA==/", "_etag": "\"a700ff44-0000-0100-0000-6870346d0000\"", "_attachments": "attachments/", "_ts": 1752183917}, {"payPeriodId": "1060040196245190", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-08-25T00:00:00Z", "endDate": "2025-09-07T00:00:00Z", "submitByDate": "2025-09-08T00:00:00Z", "checkDate": "2025-09-10T00:00:00Z", "checkCount": 0, "id": "0d5c449c-56a2-4134-9b0d-02c4a4dfb4b7", "companyId": "0082PT34", "type": "payperiod", "_rid": "NmJkAKiCbEfB3gMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfB3gMAAAAAAA==/", "_etag": "\"a7000045-0000-0100-0000-6870346d0000\"", "_attachments": "attachments/", "_ts": 1752183917}, {"payPeriodId": "1060040264288775", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-09-08T00:00:00Z", "endDate": "2025-09-21T00:00:00Z", "submitByDate": "2025-09-22T00:00:00Z", "checkDate": "2025-09-24T00:00:00Z", "checkCount": 0, "id": "33d6ccc7-b16e-470a-8dd8-10d809eb82f1", "companyId": "0082PT34", "type": "payperiod", "_rid": "NmJkAKiCbEfC3gMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfC3gMAAAAAAA==/", "_etag": "\"a7000145-0000-0100-0000-6870346d0000\"", "_attachments": "attachments/", "_ts": 1752183917}, {"payPeriodId": "1060040335408591", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-09-22T00:00:00Z", "endDate": "2025-10-05T00:00:00Z", "submitByDate": "2025-10-06T00:00:00Z", "checkDate": "2025-10-08T00:00:00Z", "checkCount": 0, "id": "6cb80e6a-4b27-46d1-b094-a6b85a480d8b", "companyId": "0082PT34", "type": "payperiod", "_rid": "NmJkAKiCbEfD3gMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfD3gMAAAAAAA==/", "_etag": "\"a7000245-0000-0100-0000-6870346d0000\"", "_attachments": "attachments/", "_ts": 1752183917}], "links": [{"rel": "self", "href": "https://ca-payroll-ai-mock-sb-001-secure.nicebeach-8a7a52ab.eastus.azurecontainerapps.io/ca/companies/0082PT34/payperiods"}]}, "status_code": 200}