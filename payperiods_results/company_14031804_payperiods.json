{"success": true, "company_id": "14031804", "data": {"metadata": {"contentItemCount": 164}, "content": [{"payPeriodId": "1050102839108267", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2024-12-26T00:00:00Z", "endDate": "2025-01-01T00:00:00Z", "submitByDate": "2024-12-31T00:00:00Z", "checkDate": "2025-01-03T00:00:00Z", "checkCount": 5, "id": "53258f9e-f69f-4f45-8aab-6de26f1a8c9e", "companyId": "14031804", "type": "payperiod", "_rid": "NmJkAKiCbEfYPwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfYPwAAAAAAAA==/", "_etag": "\"97000dd7-0000-0100-0000-686fd4000000\"", "_attachments": "attachments/", "_ts": 1752159232}, {"payPeriodId": "1050103030766385", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-01-02T00:00:00Z", "endDate": "2025-01-08T00:00:00Z", "submitByDate": "2025-01-08T00:00:00Z", "checkDate": "2025-01-10T00:00:00Z", "checkCount": 4, "id": "0ce537eb-8038-469b-b1b1-9685ab8bd13f", "companyId": "14031804", "type": "payperiod", "_rid": "NmJkAKiCbEfZPwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfZPwAAAAAAAA==/", "_etag": "\"97000fd7-0000-0100-0000-686fd4000000\"", "_attachments": "attachments/", "_ts": 1752159232}, {"payPeriodId": "1050103283023104", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-01-09T00:00:00Z", "endDate": "2025-01-15T00:00:00Z", "submitByDate": "2025-01-15T00:00:00Z", "checkDate": "2025-01-17T00:00:00Z", "checkCount": 4, "id": "1cee0bc3-5c9e-4b84-8519-20d42caff1d2", "companyId": "14031804", "type": "payperiod", "_rid": "NmJkAKiCbEfaPwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfaPwAAAAAAAA==/", "_etag": "\"970011d7-0000-0100-0000-686fd4000000\"", "_attachments": "attachments/", "_ts": 1752159232}, {"payPeriodId": "1050103532735129", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-01-16T00:00:00Z", "endDate": "2025-01-22T00:00:00Z", "submitByDate": "2025-01-22T00:00:00Z", "checkDate": "2025-01-24T00:00:00Z", "checkCount": 5, "id": "12c5fd07-f2a1-49e7-b410-5689c12599b3", "companyId": "14031804", "type": "payperiod", "_rid": "NmJkAKiCbEfbPwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfbPwAAAAAAAA==/", "_etag": "\"970013d7-0000-0100-0000-686fd4000000\"", "_attachments": "attachments/", "_ts": 1752159232}, {"payPeriodId": "1050103728370352", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-01-23T00:00:00Z", "endDate": "2025-01-29T00:00:00Z", "submitByDate": "2025-01-29T00:00:00Z", "checkDate": "2025-01-31T00:00:00Z", "checkCount": 5, "id": "b8f8c5fb-0652-46a6-843e-0cefaada8043", "companyId": "14031804", "type": "payperiod", "_rid": "NmJkAKiCbEfcPwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfcPwAAAAAAAA==/", "_etag": "\"970016d7-0000-0100-0000-686fd4000000\"", "_attachments": "attachments/", "_ts": 1752159232}, {"payPeriodId": "1050103970698115", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-01-30T00:00:00Z", "endDate": "2025-02-05T00:00:00Z", "submitByDate": "2025-02-05T00:00:00Z", "checkDate": "2025-02-07T00:00:00Z", "checkCount": 5, "id": "29878d2c-340c-467c-92e6-38c81f0c7461", "companyId": "14031804", "type": "payperiod", "_rid": "NmJkAKiCbEfdPwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfdPwAAAAAAAA==/", "_etag": "\"970019d7-0000-0100-0000-686fd4000000\"", "_attachments": "attachments/", "_ts": 1752159232}, {"payPeriodId": "1050104217972602", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-02-06T00:00:00Z", "endDate": "2025-02-12T00:00:00Z", "submitByDate": "2025-02-12T00:00:00Z", "checkDate": "2025-02-14T00:00:00Z", "checkCount": 4, "id": "7c2e707e-9a64-4cc8-b6cc-b656ef059437", "companyId": "14031804", "type": "payperiod", "_rid": "NmJkAKiCbEfePwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfePwAAAAAAAA==/", "_etag": "\"97001cd7-0000-0100-0000-686fd4000000\"", "_attachments": "attachments/", "_ts": 1752159232}, {"payPeriodId": "1050104444235861", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-02-13T00:00:00Z", "endDate": "2025-02-19T00:00:00Z", "submitByDate": "2025-02-19T00:00:00Z", "checkDate": "2025-02-21T00:00:00Z", "checkCount": 5, "id": "db3e61e3-3da3-453b-a234-d0a2a9b50733", "companyId": "14031804", "type": "payperiod", "_rid": "NmJkAKiCbEffPwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEffPwAAAAAAAA==/", "_etag": "\"970020d7-0000-0100-0000-686fd4000000\"", "_attachments": "attachments/", "_ts": 1752159232}, {"payPeriodId": "1050104696920917", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-02-20T00:00:00Z", "endDate": "2025-02-26T00:00:00Z", "submitByDate": "2025-02-26T00:00:00Z", "checkDate": "2025-02-28T00:00:00Z", "checkCount": 4, "id": "de8cf8fc-27b7-4a38-8603-a67ff402bac6", "companyId": "14031804", "type": "payperiod", "_rid": "NmJkAKiCbEfgPwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfgPwAAAAAAAA==/", "_etag": "\"970025d7-0000-0100-0000-686fd4010000\"", "_attachments": "attachments/", "_ts": 1752159233}, {"payPeriodId": "1050104914037753", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-02-27T00:00:00Z", "endDate": "2025-03-05T00:00:00Z", "submitByDate": "2025-03-05T00:00:00Z", "checkDate": "2025-03-07T00:00:00Z", "checkCount": 5, "id": "5750f58b-53bd-42a5-99ed-307234592319", "companyId": "14031804", "type": "payperiod", "_rid": "NmJkAKiCbEfhPwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfhPwAAAAAAAA==/", "_etag": "\"970028d7-0000-0100-0000-686fd4010000\"", "_attachments": "attachments/", "_ts": 1752159233}, {"payPeriodId": "1050105278385171", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-03-06T00:00:00Z", "endDate": "2025-03-12T00:00:00Z", "submitByDate": "2025-03-12T00:00:00Z", "checkDate": "2025-03-14T00:00:00Z", "checkCount": 5, "id": "2e9c335e-c19e-4a21-85bc-462579caf7dc", "companyId": "14031804", "type": "payperiod", "_rid": "NmJkAKiCbEfiPwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfiPwAAAAAAAA==/", "_etag": "\"97002cd7-0000-0100-0000-686fd4010000\"", "_attachments": "attachments/", "_ts": 1752159233}, {"payPeriodId": "1050105547300066", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-03-13T00:00:00Z", "endDate": "2025-03-19T00:00:00Z", "submitByDate": "2025-03-19T00:00:00Z", "checkDate": "2025-03-21T00:00:00Z", "checkCount": 5, "id": "6704a96b-7a95-4965-9b46-04c64b54cf06", "companyId": "14031804", "type": "payperiod", "_rid": "NmJkAKiCbEfjPwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfjPwAAAAAAAA==/", "_etag": "\"97002ed7-0000-0100-0000-686fd4010000\"", "_attachments": "attachments/", "_ts": 1752159233}, {"payPeriodId": "1050105813421713", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-03-20T00:00:00Z", "endDate": "2025-03-26T00:00:00Z", "submitByDate": "2025-03-26T00:00:00Z", "checkDate": "2025-03-28T00:00:00Z", "checkCount": 4, "id": "e9bc273c-d487-4e53-ac06-d55cdb09052d", "companyId": "14031804", "type": "payperiod", "_rid": "NmJkAKiCbEfkPwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfkPwAAAAAAAA==/", "_etag": "\"970033d7-0000-0100-0000-686fd4010000\"", "_attachments": "attachments/", "_ts": 1752159233}, {"payPeriodId": "1050106053757879", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-03-27T00:00:00Z", "endDate": "2025-04-02T00:00:00Z", "submitByDate": "2025-04-02T00:00:00Z", "checkDate": "2025-04-04T00:00:00Z", "checkCount": 0, "id": "c3d0d98c-d84f-45ee-9863-a7a69e65a514", "companyId": "14031804", "type": "payperiod", "_rid": "NmJkAKiCbEflPwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEflPwAAAAAAAA==/", "_etag": "\"97003bd7-0000-0100-0000-686fd4010000\"", "_attachments": "attachments/", "_ts": 1752159233}, {"payPeriodId": "1050106296460761", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-04-03T00:00:00Z", "endDate": "2025-04-09T00:00:00Z", "submitByDate": "2025-04-09T00:00:00Z", "checkDate": "2025-04-11T00:00:00Z", "checkCount": 0, "id": "c59a8486-946c-42fe-be55-7b1521cae91b", "companyId": "14031804", "type": "payperiod", "_rid": "NmJkAKiCbEfmPwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfmPwAAAAAAAA==/", "_etag": "\"97003dd7-0000-0100-0000-686fd4010000\"", "_attachments": "attachments/", "_ts": 1752159233}, {"payPeriodId": "1050106538396344", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-04-10T00:00:00Z", "endDate": "2025-04-16T00:00:00Z", "submitByDate": "2025-04-16T00:00:00Z", "checkDate": "2025-04-18T00:00:00Z", "checkCount": 0, "id": "ece3d75e-9a8d-4ae2-a4df-a05f00043c9f", "companyId": "14031804", "type": "payperiod", "_rid": "NmJkAKiCbEfnPwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfnPwAAAAAAAA==/", "_etag": "\"970041d7-0000-0100-0000-686fd4010000\"", "_attachments": "attachments/", "_ts": 1752159233}, {"payPeriodId": "1050106792492273", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-04-17T00:00:00Z", "endDate": "2025-04-23T00:00:00Z", "submitByDate": "2025-04-23T00:00:00Z", "checkDate": "2025-04-25T00:00:00Z", "checkCount": 0, "id": "e4da4e86-983f-4273-bb37-d067a3af9c9f", "companyId": "14031804", "type": "payperiod", "_rid": "NmJkAKiCbEfoPwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfoPwAAAAAAAA==/", "_etag": "\"970046d7-0000-0100-0000-686fd4010000\"", "_attachments": "attachments/", "_ts": 1752159233}, {"payPeriodId": "1050107010957908", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-04-24T00:00:00Z", "endDate": "2025-04-30T00:00:00Z", "submitByDate": "2025-04-30T00:00:00Z", "checkDate": "2025-05-02T00:00:00Z", "checkCount": 0, "id": "058aa66c-c907-4fb9-9531-21a3c86577ce", "companyId": "14031804", "type": "payperiod", "_rid": "NmJkAKiCbEfpPwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfpPwAAAAAAAA==/", "_etag": "\"970047d7-0000-0100-0000-686fd4010000\"", "_attachments": "attachments/", "_ts": 1752159233}, {"payPeriodId": "1050107264249568", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-05-01T00:00:00Z", "endDate": "2025-05-07T00:00:00Z", "submitByDate": "2025-05-07T00:00:00Z", "checkDate": "2025-05-09T00:00:00Z", "checkCount": 0, "id": "e5a0499c-b89c-4a43-8a6f-ab0d8650070f", "companyId": "14031804", "type": "payperiod", "_rid": "NmJkAKiCbEfqPwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfqPwAAAAAAAA==/", "_etag": "\"970049d7-0000-0100-0000-686fd4010000\"", "_attachments": "attachments/", "_ts": 1752159233}, {"payPeriodId": "1050107499552422", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-05-08T00:00:00Z", "endDate": "2025-05-14T00:00:00Z", "submitByDate": "2025-05-14T00:00:00Z", "checkDate": "2025-05-16T00:00:00Z", "checkCount": 0, "id": "c8bc6712-8acf-4477-a164-ac7821d0d8a5", "companyId": "14031804", "type": "payperiod", "_rid": "NmJkAKiCbEfrPwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfrPwAAAAAAAA==/", "_etag": "\"970050d7-0000-0100-0000-686fd4010000\"", "_attachments": "attachments/", "_ts": 1752159233}, {"payPeriodId": "1050107744943448", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-05-15T00:00:00Z", "endDate": "2025-05-21T00:00:00Z", "submitByDate": "2025-05-21T00:00:00Z", "checkDate": "2025-05-23T00:00:00Z", "checkCount": 0, "id": "260f2bfd-6786-4e78-ba95-699fc0bd7968", "companyId": "14031804", "type": "payperiod", "_rid": "NmJkAKiCbEfsPwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfsPwAAAAAAAA==/", "_etag": "\"970052d7-0000-0100-0000-686fd4010000\"", "_attachments": "attachments/", "_ts": 1752159233}, {"payPeriodId": "1050107973594397", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-05-22T00:00:00Z", "endDate": "2025-05-28T00:00:00Z", "submitByDate": "2025-05-28T00:00:00Z", "checkDate": "2025-05-30T00:00:00Z", "checkCount": 0, "id": "1aaa720c-9a73-4510-87fe-deb148e06a62", "companyId": "14031804", "type": "payperiod", "_rid": "NmJkAKiCbEftPwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEftPwAAAAAAAA==/", "_etag": "\"970053d7-0000-0100-0000-686fd4010000\"", "_attachments": "attachments/", "_ts": 1752159233}, {"payPeriodId": "1050108217960652", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-05-29T00:00:00Z", "endDate": "2025-06-04T00:00:00Z", "submitByDate": "2025-06-04T00:00:00Z", "checkDate": "2025-06-06T00:00:00Z", "checkCount": 0, "id": "65cd34b8-51be-4974-b25b-0358cc0c3f8e", "companyId": "14031804", "type": "payperiod", "_rid": "NmJkAKiCbEfuPwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfuPwAAAAAAAA==/", "_etag": "\"970056d7-0000-0100-0000-686fd4020000\"", "_attachments": "attachments/", "_ts": 1752159234}, {"payPeriodId": "1050108342826199", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-06-05T00:00:00Z", "endDate": "2025-06-11T00:00:00Z", "submitByDate": "2025-06-11T00:00:00Z", "checkDate": "2025-06-13T00:00:00Z", "checkCount": 0, "id": "98bd6a5f-a2fb-4d70-a625-6087584b82ff", "companyId": "14031804", "type": "payperiod", "_rid": "NmJkAKiCbEfvPwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfvPwAAAAAAAA==/", "_etag": "\"970058d7-0000-0100-0000-686fd4020000\"", "_attachments": "attachments/", "_ts": 1752159234}, {"payPeriodId": "1050108613880129", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-06-12T00:00:00Z", "endDate": "2025-06-18T00:00:00Z", "submitByDate": "2025-06-18T00:00:00Z", "checkDate": "2025-06-20T00:00:00Z", "checkCount": 0, "id": "20372dcc-2b01-4fd3-96df-acbf39f1fe1a", "companyId": "14031804", "type": "payperiod", "_rid": "NmJkAKiCbEfwPwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfwPwAAAAAAAA==/", "_etag": "\"970059d7-0000-0100-0000-686fd4020000\"", "_attachments": "attachments/", "_ts": 1752159234}, {"payPeriodId": "1050108816287584", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-06-19T00:00:00Z", "endDate": "2025-06-25T00:00:00Z", "submitByDate": "2025-06-25T00:00:00Z", "checkDate": "2025-06-27T00:00:00Z", "checkCount": 0, "id": "4138f755-b5f3-4abd-8897-907f40282ac4", "companyId": "14031804", "type": "payperiod", "_rid": "NmJkAKiCbEfxPwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfxPwAAAAAAAA==/", "_etag": "\"97005cd7-0000-0100-0000-686fd4020000\"", "_attachments": "attachments/", "_ts": 1752159234}, {"payPeriodId": "1050109058328496", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-06-26T00:00:00Z", "endDate": "2025-07-02T00:00:00Z", "submitByDate": "2025-07-01T00:00:00Z", "checkDate": "2025-07-03T00:00:00Z", "checkCount": 0, "id": "c64f9cf5-58f4-4ce0-b8b6-357fe4e70764", "companyId": "14031804", "type": "payperiod", "_rid": "NmJkAKiCbEfyPwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfyPwAAAAAAAA==/", "_etag": "\"97005ed7-0000-0100-0000-686fd4020000\"", "_attachments": "attachments/", "_ts": 1752159234}, {"payPeriodId": "1050109301165776", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-07-03T00:00:00Z", "endDate": "2025-07-09T00:00:00Z", "submitByDate": "2025-07-09T00:00:00Z", "checkDate": "2025-07-11T00:00:00Z", "checkCount": 0, "id": "5b7a0284-d93a-4134-9062-f4f14264af86", "companyId": "14031804", "type": "payperiod", "_rid": "NmJkAKiCbEfzPwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfzPwAAAAAAAA==/", "_etag": "\"970060d7-0000-0100-0000-686fd4020000\"", "_attachments": "attachments/", "_ts": 1752159234}, {"payPeriodId": "1050109564360781", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-07-10T00:00:00Z", "endDate": "2025-07-16T00:00:00Z", "submitByDate": "2025-07-16T00:00:00Z", "checkDate": "2025-07-18T00:00:00Z", "checkCount": 0, "id": "9af65587-1803-40c8-8985-15f1d2be1990", "companyId": "14031804", "type": "payperiod", "_rid": "NmJkAKiCbEf0PwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf0PwAAAAAAAA==/", "_etag": "\"970065d7-0000-0100-0000-686fd4020000\"", "_attachments": "attachments/", "_ts": 1752159234}, {"payPeriodId": "1050109795669035", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-07-17T00:00:00Z", "endDate": "2025-07-23T00:00:00Z", "submitByDate": "2025-07-23T00:00:00Z", "checkDate": "2025-07-25T00:00:00Z", "checkCount": 0, "id": "c373cde0-f27e-42bb-8422-0538b321377c", "companyId": "14031804", "type": "payperiod", "_rid": "NmJkAKiCbEf1PwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf1PwAAAAAAAA==/", "_etag": "\"97006ad7-0000-0100-0000-686fd4020000\"", "_attachments": "attachments/", "_ts": 1752159234}, {"payPeriodId": "1050110021451888", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-07-24T00:00:00Z", "endDate": "2025-07-30T00:00:00Z", "submitByDate": "2025-07-30T00:00:00Z", "checkDate": "2025-08-01T00:00:00Z", "checkCount": 0, "id": "bcf1ce8d-12f0-4851-ba07-848e37196344", "companyId": "14031804", "type": "payperiod", "_rid": "NmJkAKiCbEf2PwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf2PwAAAAAAAA==/", "_etag": "\"970070d7-0000-0100-0000-686fd4020000\"", "_attachments": "attachments/", "_ts": 1752159234}, {"payPeriodId": "1050110276266457", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-07-31T00:00:00Z", "endDate": "2025-08-06T00:00:00Z", "submitByDate": "2025-08-06T00:00:00Z", "checkDate": "2025-08-08T00:00:00Z", "checkCount": 0, "id": "034662db-d265-438a-a1b0-5c5c25962423", "companyId": "14031804", "type": "payperiod", "_rid": "NmJkAKiCbEf3PwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf3PwAAAAAAAA==/", "_etag": "\"970073d7-0000-0100-0000-686fd4020000\"", "_attachments": "attachments/", "_ts": 1752159234}, {"payPeriodId": "1050110517120720", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-08-07T00:00:00Z", "endDate": "2025-08-13T00:00:00Z", "submitByDate": "2025-08-13T00:00:00Z", "checkDate": "2025-08-15T00:00:00Z", "checkCount": 0, "id": "5bf90f30-0d8a-45ce-9c4c-006e7b5f7494", "companyId": "14031804", "type": "payperiod", "_rid": "NmJkAKiCbEf4PwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf4PwAAAAAAAA==/", "_etag": "\"970077d7-0000-0100-0000-686fd4020000\"", "_attachments": "attachments/", "_ts": 1752159234}, {"payPeriodId": "1050110771547801", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-08-14T00:00:00Z", "endDate": "2025-08-20T00:00:00Z", "submitByDate": "2025-08-20T00:00:00Z", "checkDate": "2025-08-22T00:00:00Z", "checkCount": 0, "id": "8d976f73-37ac-4410-9e07-b4129b4ef4cd", "companyId": "14031804", "type": "payperiod", "_rid": "NmJkAKiCbEf5PwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf5PwAAAAAAAA==/", "_etag": "\"97007ad7-0000-0100-0000-686fd4020000\"", "_attachments": "attachments/", "_ts": 1752159234}, {"payPeriodId": "1050111005803658", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-08-21T00:00:00Z", "endDate": "2025-08-27T00:00:00Z", "submitByDate": "2025-08-27T00:00:00Z", "checkDate": "2025-08-29T00:00:00Z", "checkCount": 0, "id": "5948da3a-ff33-40ca-8d21-36ad32fbc133", "companyId": "14031804", "type": "payperiod", "_rid": "NmJkAKiCbEf6PwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf6PwAAAAAAAA==/", "_etag": "\"97007ed7-0000-0100-0000-686fd4020000\"", "_attachments": "attachments/", "_ts": 1752159234}, {"payPeriodId": "1050111334676357", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-08-28T00:00:00Z", "endDate": "2025-09-03T00:00:00Z", "submitByDate": "2025-09-03T00:00:00Z", "checkDate": "2025-09-05T00:00:00Z", "checkCount": 0, "id": "1700894e-bbe5-4742-aed3-9995425355e0", "companyId": "14031804", "type": "payperiod", "_rid": "NmJkAKiCbEf7PwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf7PwAAAAAAAA==/", "_etag": "\"970082d7-0000-0100-0000-686fd4020000\"", "_attachments": "attachments/", "_ts": 1752159234}, {"payPeriodId": "1050111563390561", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-09-04T00:00:00Z", "endDate": "2025-09-10T00:00:00Z", "submitByDate": "2025-09-10T00:00:00Z", "checkDate": "2025-09-12T00:00:00Z", "checkCount": 0, "id": "56b214a7-08ac-454d-9752-6c126d7e8bfd", "companyId": "14031804", "type": "payperiod", "_rid": "NmJkAKiCbEf8PwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf8PwAAAAAAAA==/", "_etag": "\"970086d7-0000-0100-0000-686fd4020000\"", "_attachments": "attachments/", "_ts": 1752159234}, {"payPeriodId": "1050111873092806", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-09-11T00:00:00Z", "endDate": "2025-09-17T00:00:00Z", "submitByDate": "2025-09-17T00:00:00Z", "checkDate": "2025-09-19T00:00:00Z", "checkCount": 0, "id": "8d50b16d-c188-40a7-a647-798f12c51c86", "companyId": "14031804", "type": "payperiod", "_rid": "NmJkAKiCbEf9PwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf9PwAAAAAAAA==/", "_etag": "\"97008dd7-0000-0100-0000-686fd4030000\"", "_attachments": "attachments/", "_ts": 1752159235}, {"payPeriodId": "1050112017682575", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-09-18T00:00:00Z", "endDate": "2025-09-24T00:00:00Z", "submitByDate": "2025-09-24T00:00:00Z", "checkDate": "2025-09-26T00:00:00Z", "checkCount": 0, "id": "961c5d0f-2849-411b-bb62-568e47<PERSON>da8", "companyId": "14031804", "type": "payperiod", "_rid": "NmJkAKiCbEf+PwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf+PwAAAAAAAA==/", "_etag": "\"970095d7-0000-0100-0000-686fd4030000\"", "_attachments": "attachments/", "_ts": 1752159235}, {"payPeriodId": "1050112323675123", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-09-25T00:00:00Z", "endDate": "2025-10-01T00:00:00Z", "submitByDate": "2025-10-01T00:00:00Z", "checkDate": "2025-10-03T00:00:00Z", "checkCount": 0, "id": "5531e6dd-613e-4154-bb31-1ce65e843059", "companyId": "14031804", "type": "payperiod", "_rid": "NmJkAKiCbEf-PwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf-PwAAAAAAAA==/", "_etag": "\"97009bd7-0000-0100-0000-686fd4030000\"", "_attachments": "attachments/", "_ts": 1752159235}, {"payPeriodId": "1050112391938507", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-10-02T00:00:00Z", "endDate": "2025-10-08T00:00:00Z", "submitByDate": "2025-10-08T00:00:00Z", "checkDate": "2025-10-10T00:00:00Z", "checkCount": 0, "id": "29551053-b8da-4357-8163-fac6808bd5f4", "companyId": "14031804", "type": "payperiod", "_rid": "NmJkAKiCbEcAQAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcAQAAAAAAAAA==/", "_etag": "\"9700a2d7-0000-0100-0000-686fd4030000\"", "_attachments": "attachments/", "_ts": 1752159235}, {"payPeriodId": "1050102839108267", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2024-12-26T00:00:00Z", "endDate": "2025-01-01T00:00:00Z", "submitByDate": "2024-12-31T00:00:00Z", "checkDate": "2025-01-03T00:00:00Z", "checkCount": 5, "id": "0e50cafa-1db6-4d62-991b-c8849eb66194", "companyId": "14031804", "type": "payperiod", "_rid": "NmJkAKiCbEcbQAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcbQAAAAAAAAA==/", "_etag": "\"970016d8-0000-0100-0000-686fd4050000\"", "_attachments": "attachments/", "_ts": 1752159237}, {"payPeriodId": "1050103030766385", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-01-02T00:00:00Z", "endDate": "2025-01-08T00:00:00Z", "submitByDate": "2025-01-08T00:00:00Z", "checkDate": "2025-01-10T00:00:00Z", "checkCount": 4, "id": "55b739b0-0254-4dc2-9ef2-6bf78bc5c1e8", "companyId": "14031804", "type": "payperiod", "_rid": "NmJkAKiCbEccQAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEccQAAAAAAAAA==/", "_etag": "\"970019d8-0000-0100-0000-686fd4050000\"", "_attachments": "attachments/", "_ts": 1752159237}, {"payPeriodId": "1050103283023104", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-01-09T00:00:00Z", "endDate": "2025-01-15T00:00:00Z", "submitByDate": "2025-01-15T00:00:00Z", "checkDate": "2025-01-17T00:00:00Z", "checkCount": 4, "id": "30f9b456-1209-4b06-b377-80cca0879c4d", "companyId": "14031804", "type": "payperiod", "_rid": "NmJkAKiCbEcdQAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcdQAAAAAAAAA==/", "_etag": "\"97001dd8-0000-0100-0000-686fd4050000\"", "_attachments": "attachments/", "_ts": 1752159237}, {"payPeriodId": "1050103532735129", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-01-16T00:00:00Z", "endDate": "2025-01-22T00:00:00Z", "submitByDate": "2025-01-22T00:00:00Z", "checkDate": "2025-01-24T00:00:00Z", "checkCount": 5, "id": "2799b532-2288-4587-b19f-2620c7e8e638", "companyId": "14031804", "type": "payperiod", "_rid": "NmJkAKiCbEceQAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEceQAAAAAAAAA==/", "_etag": "\"970021d8-0000-0100-0000-686fd4050000\"", "_attachments": "attachments/", "_ts": 1752159237}, {"payPeriodId": "1050103728370352", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-01-23T00:00:00Z", "endDate": "2025-01-29T00:00:00Z", "submitByDate": "2025-01-29T00:00:00Z", "checkDate": "2025-01-31T00:00:00Z", "checkCount": 5, "id": "7824f9e3-fd57-48f5-828c-9c12e448e153", "companyId": "14031804", "type": "payperiod", "_rid": "NmJkAKiCbEcfQAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcfQAAAAAAAAA==/", "_etag": "\"970023d8-0000-0100-0000-686fd4050000\"", "_attachments": "attachments/", "_ts": 1752159237}, {"payPeriodId": "1050103970698115", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-01-30T00:00:00Z", "endDate": "2025-02-05T00:00:00Z", "submitByDate": "2025-02-05T00:00:00Z", "checkDate": "2025-02-07T00:00:00Z", "checkCount": 5, "id": "e033039f-36df-4e0f-9a1a-1925b4a266bd", "companyId": "14031804", "type": "payperiod", "_rid": "NmJkAKiCbEcgQAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcgQAAAAAAAAA==/", "_etag": "\"970027d8-0000-0100-0000-686fd4050000\"", "_attachments": "attachments/", "_ts": 1752159237}, {"payPeriodId": "1050104217972602", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-02-06T00:00:00Z", "endDate": "2025-02-12T00:00:00Z", "submitByDate": "2025-02-12T00:00:00Z", "checkDate": "2025-02-14T00:00:00Z", "checkCount": 4, "id": "c058a7a8-3217-44e3-931e-24767d69d505", "companyId": "14031804", "type": "payperiod", "_rid": "NmJkAKiCbEchQAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEchQAAAAAAAAA==/", "_etag": "\"97002ad8-0000-0100-0000-686fd4050000\"", "_attachments": "attachments/", "_ts": 1752159237}, {"payPeriodId": "1050104444235861", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-02-13T00:00:00Z", "endDate": "2025-02-19T00:00:00Z", "submitByDate": "2025-02-19T00:00:00Z", "checkDate": "2025-02-21T00:00:00Z", "checkCount": 5, "id": "38c58516-b60d-44b6-8514-c7ebc03f4129", "companyId": "14031804", "type": "payperiod", "_rid": "NmJkAKiCbEciQAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEciQAAAAAAAAA==/", "_etag": "\"97002fd8-0000-0100-0000-686fd4050000\"", "_attachments": "attachments/", "_ts": 1752159237}, {"payPeriodId": "1050104696920917", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-02-20T00:00:00Z", "endDate": "2025-02-26T00:00:00Z", "submitByDate": "2025-02-26T00:00:00Z", "checkDate": "2025-02-28T00:00:00Z", "checkCount": 4, "id": "983bcb46-da59-489b-92a6-880eeeac822c", "companyId": "14031804", "type": "payperiod", "_rid": "NmJkAKiCbEcjQAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcjQAAAAAAAAA==/", "_etag": "\"970034d8-0000-0100-0000-686fd4050000\"", "_attachments": "attachments/", "_ts": 1752159237}, {"payPeriodId": "1050104914037753", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-02-27T00:00:00Z", "endDate": "2025-03-05T00:00:00Z", "submitByDate": "2025-03-05T00:00:00Z", "checkDate": "2025-03-07T00:00:00Z", "checkCount": 5, "id": "57f5e46d-dda3-438c-9e34-8e8490cccc06", "companyId": "14031804", "type": "payperiod", "_rid": "NmJkAKiCbEckQAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEckQAAAAAAAAA==/", "_etag": "\"970036d8-0000-0100-0000-686fd4050000\"", "_attachments": "attachments/", "_ts": 1752159237}, {"payPeriodId": "1050105278385171", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-03-06T00:00:00Z", "endDate": "2025-03-12T00:00:00Z", "submitByDate": "2025-03-12T00:00:00Z", "checkDate": "2025-03-14T00:00:00Z", "checkCount": 5, "id": "8ed30de8-3b18-4664-9d3b-38b4cf01a31f", "companyId": "14031804", "type": "payperiod", "_rid": "NmJkAKiCbEclQAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEclQAAAAAAAAA==/", "_etag": "\"970038d8-0000-0100-0000-686fd4060000\"", "_attachments": "attachments/", "_ts": 1752159238}, {"payPeriodId": "1050105547300066", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-03-13T00:00:00Z", "endDate": "2025-03-19T00:00:00Z", "submitByDate": "2025-03-19T00:00:00Z", "checkDate": "2025-03-21T00:00:00Z", "checkCount": 5, "id": "a388dc4d-ba51-44ac-801c-4678ed538aa2", "companyId": "14031804", "type": "payperiod", "_rid": "NmJkAKiCbEcmQAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcmQAAAAAAAAA==/", "_etag": "\"97003ad8-0000-0100-0000-686fd4060000\"", "_attachments": "attachments/", "_ts": 1752159238}, {"payPeriodId": "1050105813421713", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-03-20T00:00:00Z", "endDate": "2025-03-26T00:00:00Z", "submitByDate": "2025-03-26T00:00:00Z", "checkDate": "2025-03-28T00:00:00Z", "checkCount": 4, "id": "bfb739d1-dd94-4927-8262-2131cbb93a7e", "companyId": "14031804", "type": "payperiod", "_rid": "NmJkAKiCbEcnQAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcnQAAAAAAAAA==/", "_etag": "\"970041d8-0000-0100-0000-686fd4060000\"", "_attachments": "attachments/", "_ts": 1752159238}, {"payPeriodId": "1050106053757879", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-03-27T00:00:00Z", "endDate": "2025-04-02T00:00:00Z", "submitByDate": "2025-04-02T00:00:00Z", "checkDate": "2025-04-04T00:00:00Z", "checkCount": 5, "id": "93846c23-5fcf-4385-a4b9-ffad7c4d6835", "companyId": "14031804", "type": "payperiod", "_rid": "NmJkAKiCbEcoQAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcoQAAAAAAAAA==/", "_etag": "\"970042d8-0000-0100-0000-686fd4060000\"", "_attachments": "attachments/", "_ts": 1752159238}, {"payPeriodId": "1050106296460761", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-04-03T00:00:00Z", "endDate": "2025-04-09T00:00:00Z", "submitByDate": "2025-04-09T00:00:00Z", "checkDate": "2025-04-11T00:00:00Z", "checkCount": 5, "id": "727eb4b3-f78e-450a-885e-cdd54997a4db", "companyId": "14031804", "type": "payperiod", "_rid": "NmJkAKiCbEcpQAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcpQAAAAAAAAA==/", "_etag": "\"970043d8-0000-0100-0000-686fd4060000\"", "_attachments": "attachments/", "_ts": 1752159238}, {"payPeriodId": "1050106538396344", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-04-10T00:00:00Z", "endDate": "2025-04-16T00:00:00Z", "submitByDate": "2025-04-16T00:00:00Z", "checkDate": "2025-04-18T00:00:00Z", "checkCount": 5, "id": "72b9113f-fdb9-4009-9bbc-a5f2613e93ff", "companyId": "14031804", "type": "payperiod", "_rid": "NmJkAKiCbEcqQAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcqQAAAAAAAAA==/", "_etag": "\"970044d8-0000-0100-0000-686fd4060000\"", "_attachments": "attachments/", "_ts": 1752159238}, {"payPeriodId": "1050106792492273", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-04-17T00:00:00Z", "endDate": "2025-04-23T00:00:00Z", "submitByDate": "2025-04-23T00:00:00Z", "checkDate": "2025-04-25T00:00:00Z", "checkCount": 4, "id": "1d9de18a-203f-4835-b477-657a891c3741", "companyId": "14031804", "type": "payperiod", "_rid": "NmJkAKiCbEcrQAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcrQAAAAAAAAA==/", "_etag": "\"970048d8-0000-0100-0000-686fd4060000\"", "_attachments": "attachments/", "_ts": 1752159238}, {"payPeriodId": "1050107010957908", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-04-24T00:00:00Z", "endDate": "2025-04-30T00:00:00Z", "submitByDate": "2025-04-30T00:00:00Z", "checkDate": "2025-05-02T00:00:00Z", "checkCount": 4, "id": "d2844d59-901f-435f-b51c-045a491739af", "companyId": "14031804", "type": "payperiod", "_rid": "NmJkAKiCbEcsQAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcsQAAAAAAAAA==/", "_etag": "\"97004bd8-0000-0100-0000-686fd4060000\"", "_attachments": "attachments/", "_ts": 1752159238}, {"payPeriodId": "1050107264249568", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-05-01T00:00:00Z", "endDate": "2025-05-07T00:00:00Z", "submitByDate": "2025-05-07T00:00:00Z", "checkDate": "2025-05-09T00:00:00Z", "checkCount": 5, "id": "e5e3b07b-67c1-4847-aeea-0767d809bf5f", "companyId": "14031804", "type": "payperiod", "_rid": "NmJkAKiCbEctQAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEctQAAAAAAAAA==/", "_etag": "\"97004ed8-0000-0100-0000-686fd4060000\"", "_attachments": "attachments/", "_ts": 1752159238}, {"payPeriodId": "1050107499552422", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-05-08T00:00:00Z", "endDate": "2025-05-14T00:00:00Z", "submitByDate": "2025-05-14T00:00:00Z", "checkDate": "2025-05-16T00:00:00Z", "checkCount": 5, "id": "23b147ad-ea19-4fa0-9be6-216bbed1b967", "companyId": "14031804", "type": "payperiod", "_rid": "NmJkAKiCbEcuQAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcuQAAAAAAAAA==/", "_etag": "\"970056d8-0000-0100-0000-686fd4060000\"", "_attachments": "attachments/", "_ts": 1752159238}, {"payPeriodId": "1050107744943448", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-05-15T00:00:00Z", "endDate": "2025-05-21T00:00:00Z", "submitByDate": "2025-05-21T00:00:00Z", "checkDate": "2025-05-23T00:00:00Z", "checkCount": 5, "id": "702b3b49-f750-43eb-8f9d-9e994bc4bd11", "companyId": "14031804", "type": "payperiod", "_rid": "NmJkAKiCbEcvQAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcvQAAAAAAAAA==/", "_etag": "\"970059d8-0000-0100-0000-686fd4060000\"", "_attachments": "attachments/", "_ts": 1752159238}, {"payPeriodId": "1050107973594397", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-05-22T00:00:00Z", "endDate": "2025-05-28T00:00:00Z", "submitByDate": "2025-05-28T00:00:00Z", "checkDate": "2025-05-30T00:00:00Z", "checkCount": 4, "id": "47dd7a99-3e2b-494d-bf61-c3cc2e1d87ab", "companyId": "14031804", "type": "payperiod", "_rid": "NmJkAKiCbEcwQAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcwQAAAAAAAAA==/", "_etag": "\"97005ed8-0000-0100-0000-686fd4060000\"", "_attachments": "attachments/", "_ts": 1752159238}, {"payPeriodId": "1050108217960652", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-05-29T00:00:00Z", "endDate": "2025-06-04T00:00:00Z", "submitByDate": "2025-06-04T00:00:00Z", "checkDate": "2025-06-06T00:00:00Z", "checkCount": 5, "id": "f738b47d-6596-4be6-9ea4-de43cb761087", "companyId": "14031804", "type": "payperiod", "_rid": "NmJkAKiCbEcxQAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcxQAAAAAAAAA==/", "_etag": "\"970063d8-0000-0100-0000-686fd4060000\"", "_attachments": "attachments/", "_ts": 1752159238}, {"payPeriodId": "1050108342826199", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-06-05T00:00:00Z", "endDate": "2025-06-11T00:00:00Z", "submitByDate": "2025-06-11T00:00:00Z", "checkDate": "2025-06-13T00:00:00Z", "checkCount": 4, "id": "19b68620-426f-4f34-86f6-d0aadac183cb", "companyId": "14031804", "type": "payperiod", "_rid": "NmJkAKiCbEcyQAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcyQAAAAAAAAA==/", "_etag": "\"970068d8-0000-0100-0000-686fd4060000\"", "_attachments": "attachments/", "_ts": 1752159238}, {"payPeriodId": "1050108613880129", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-06-12T00:00:00Z", "endDate": "2025-06-18T00:00:00Z", "submitByDate": "2025-06-18T00:00:00Z", "checkDate": "2025-06-20T00:00:00Z", "checkCount": 5, "id": "6077332c-3bc7-40ef-b649-753f4f531f9b", "companyId": "14031804", "type": "payperiod", "_rid": "NmJkAKiCbEczQAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEczQAAAAAAAAA==/", "_etag": "\"97006ad8-0000-0100-0000-686fd4070000\"", "_attachments": "attachments/", "_ts": 1752159239}, {"payPeriodId": "1050108816287584", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-06-19T00:00:00Z", "endDate": "2025-06-25T00:00:00Z", "submitByDate": "2025-06-25T00:00:00Z", "checkDate": "2025-06-27T00:00:00Z", "checkCount": 5, "id": "5a017aea-4a38-45ef-811e-f4bad86de50e", "companyId": "14031804", "type": "payperiod", "_rid": "NmJkAKiCbEc0QAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc0QAAAAAAAAA==/", "_etag": "\"97006cd8-0000-0100-0000-686fd4070000\"", "_attachments": "attachments/", "_ts": 1752159239}, {"payPeriodId": "1050109058328496", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-06-26T00:00:00Z", "endDate": "2025-07-02T00:00:00Z", "submitByDate": "2025-07-01T00:00:00Z", "checkDate": "2025-07-03T00:00:00Z", "checkCount": 5, "id": "5f4ca215-ecb1-4f9b-acc7-b6c3c0cc3171", "companyId": "14031804", "type": "payperiod", "_rid": "NmJkAKiCbEc1QAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc1QAAAAAAAAA==/", "_etag": "\"97006fd8-0000-0100-0000-686fd4070000\"", "_attachments": "attachments/", "_ts": 1752159239}, {"payPeriodId": "1050109301165776", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-07-03T00:00:00Z", "endDate": "2025-07-09T00:00:00Z", "submitByDate": "2025-07-09T00:00:00Z", "checkDate": "2025-07-11T00:00:00Z", "checkCount": 0, "id": "37912304-2bd9-401c-afc6-fe3e371024e4", "companyId": "14031804", "type": "payperiod", "_rid": "NmJkAKiCbEc2QAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc2QAAAAAAAAA==/", "_etag": "\"970073d8-0000-0100-0000-686fd4070000\"", "_attachments": "attachments/", "_ts": 1752159239}, {"payPeriodId": "1050109564360781", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-07-10T00:00:00Z", "endDate": "2025-07-16T00:00:00Z", "submitByDate": "2025-07-16T00:00:00Z", "checkDate": "2025-07-18T00:00:00Z", "checkCount": 0, "id": "49a6afe1-b958-4fcd-8cf2-fcb298b5c891", "companyId": "14031804", "type": "payperiod", "_rid": "NmJkAKiCbEc3QAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc3QAAAAAAAAA==/", "_etag": "\"970078d8-0000-0100-0000-686fd4070000\"", "_attachments": "attachments/", "_ts": 1752159239}, {"payPeriodId": "1050109795669035", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-07-17T00:00:00Z", "endDate": "2025-07-23T00:00:00Z", "submitByDate": "2025-07-23T00:00:00Z", "checkDate": "2025-07-25T00:00:00Z", "checkCount": 0, "id": "0bc6ff20-25b3-47ff-9ce6-af4af6c80ba9", "companyId": "14031804", "type": "payperiod", "_rid": "NmJkAKiCbEc4QAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc4QAAAAAAAAA==/", "_etag": "\"97007dd8-0000-0100-0000-686fd4070000\"", "_attachments": "attachments/", "_ts": 1752159239}, {"payPeriodId": "1050110021451888", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-07-24T00:00:00Z", "endDate": "2025-07-30T00:00:00Z", "submitByDate": "2025-07-30T00:00:00Z", "checkDate": "2025-08-01T00:00:00Z", "checkCount": 0, "id": "878c2013-3be8-4173-bb24-275206bf0671", "companyId": "14031804", "type": "payperiod", "_rid": "NmJkAKiCbEc5QAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc5QAAAAAAAAA==/", "_etag": "\"970082d8-0000-0100-0000-686fd4070000\"", "_attachments": "attachments/", "_ts": 1752159239}, {"payPeriodId": "1050110276266457", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-07-31T00:00:00Z", "endDate": "2025-08-06T00:00:00Z", "submitByDate": "2025-08-06T00:00:00Z", "checkDate": "2025-08-08T00:00:00Z", "checkCount": 0, "id": "1d8045ca-068a-45f8-afed-07bc147ec696", "companyId": "14031804", "type": "payperiod", "_rid": "NmJkAKiCbEc6QAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc6QAAAAAAAAA==/", "_etag": "\"970085d8-0000-0100-0000-686fd4070000\"", "_attachments": "attachments/", "_ts": 1752159239}, {"payPeriodId": "1050110517120720", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-08-07T00:00:00Z", "endDate": "2025-08-13T00:00:00Z", "submitByDate": "2025-08-13T00:00:00Z", "checkDate": "2025-08-15T00:00:00Z", "checkCount": 0, "id": "e4769c76-365a-453b-9bcc-3dde93e81037", "companyId": "14031804", "type": "payperiod", "_rid": "NmJkAKiCbEc7QAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc7QAAAAAAAAA==/", "_etag": "\"970086d8-0000-0100-0000-686fd4070000\"", "_attachments": "attachments/", "_ts": 1752159239}, {"payPeriodId": "1050110771547801", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-08-14T00:00:00Z", "endDate": "2025-08-20T00:00:00Z", "submitByDate": "2025-08-20T00:00:00Z", "checkDate": "2025-08-22T00:00:00Z", "checkCount": 0, "id": "d6f20ac4-5478-49e0-b3b4-8a1e1e56bdbd", "companyId": "14031804", "type": "payperiod", "_rid": "NmJkAKiCbEc8QAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc8QAAAAAAAAA==/", "_etag": "\"97008ad8-0000-0100-0000-686fd4070000\"", "_attachments": "attachments/", "_ts": 1752159239}, {"payPeriodId": "1050111005803658", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-08-21T00:00:00Z", "endDate": "2025-08-27T00:00:00Z", "submitByDate": "2025-08-27T00:00:00Z", "checkDate": "2025-08-29T00:00:00Z", "checkCount": 0, "id": "fd9e0da2-5fcb-45bb-84f1-cdd89080<PERSON>de", "companyId": "14031804", "type": "payperiod", "_rid": "NmJkAKiCbEc9QAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc9QAAAAAAAAA==/", "_etag": "\"97008dd8-0000-0100-0000-686fd4070000\"", "_attachments": "attachments/", "_ts": 1752159239}, {"payPeriodId": "1050111334676357", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-08-28T00:00:00Z", "endDate": "2025-09-03T00:00:00Z", "submitByDate": "2025-09-03T00:00:00Z", "checkDate": "2025-09-05T00:00:00Z", "checkCount": 0, "id": "f7bb1313-fa5d-4750-9f09-d0bc5eb39fb2", "companyId": "14031804", "type": "payperiod", "_rid": "NmJkAKiCbEc+QAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc+QAAAAAAAAA==/", "_etag": "\"970092d8-0000-0100-0000-686fd4070000\"", "_attachments": "attachments/", "_ts": 1752159239}, {"payPeriodId": "1050111563390561", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-09-04T00:00:00Z", "endDate": "2025-09-10T00:00:00Z", "submitByDate": "2025-09-10T00:00:00Z", "checkDate": "2025-09-12T00:00:00Z", "checkCount": 0, "id": "2f4d8698-761b-4d1a-867d-903f5409f612", "companyId": "14031804", "type": "payperiod", "_rid": "NmJkAKiCbEc-QAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc-QAAAAAAAAA==/", "_etag": "\"970094d8-0000-0100-0000-686fd4070000\"", "_attachments": "attachments/", "_ts": 1752159239}, {"payPeriodId": "1050111873092806", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-09-11T00:00:00Z", "endDate": "2025-09-17T00:00:00Z", "submitByDate": "2025-09-17T00:00:00Z", "checkDate": "2025-09-19T00:00:00Z", "checkCount": 0, "id": "6bb4a7d3-f885-4e1d-9d41-5398fd7869af", "companyId": "14031804", "type": "payperiod", "_rid": "NmJkAKiCbEdAQAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdAQAAAAAAAAA==/", "_etag": "\"970098d8-0000-0100-0000-686fd4070000\"", "_attachments": "attachments/", "_ts": 1752159239}, {"payPeriodId": "1050112017682575", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-09-18T00:00:00Z", "endDate": "2025-09-24T00:00:00Z", "submitByDate": "2025-09-24T00:00:00Z", "checkDate": "2025-09-26T00:00:00Z", "checkCount": 0, "id": "9dc9751e-ea46-4927-8f56-84170d1cd832", "companyId": "14031804", "type": "payperiod", "_rid": "NmJkAKiCbEdBQAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdBQAAAAAAAAA==/", "_etag": "\"97009ad8-0000-0100-0000-686fd4080000\"", "_attachments": "attachments/", "_ts": 1752159240}, {"payPeriodId": "1050112323675123", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-09-25T00:00:00Z", "endDate": "2025-10-01T00:00:00Z", "submitByDate": "2025-10-01T00:00:00Z", "checkDate": "2025-10-03T00:00:00Z", "checkCount": 0, "id": "1c729211-0e7a-42dc-b0d6-408652c91366", "companyId": "14031804", "type": "payperiod", "_rid": "NmJkAKiCbEdCQAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdCQAAAAAAAAA==/", "_etag": "\"97009ed8-0000-0100-0000-686fd4080000\"", "_attachments": "attachments/", "_ts": 1752159240}, {"payPeriodId": "1050112391938507", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-10-02T00:00:00Z", "endDate": "2025-10-08T00:00:00Z", "submitByDate": "2025-10-08T00:00:00Z", "checkDate": "2025-10-10T00:00:00Z", "checkCount": 0, "id": "80f38557-83ca-44e3-80ed-199c3188c08d", "companyId": "14031804", "type": "payperiod", "_rid": "NmJkAKiCbEdDQAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdDQAAAAAAAAA==/", "_etag": "\"97009fd8-0000-0100-0000-686fd4080000\"", "_attachments": "attachments/", "_ts": 1752159240}, {"payPeriodId": "1050102839108267", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2024-12-26T00:00:00Z", "endDate": "2025-01-01T00:00:00Z", "submitByDate": "2024-12-31T00:00:00Z", "checkDate": "2025-01-03T00:00:00Z", "checkCount": 5, "id": "89abb88e-5fc6-4164-bc56-bbda66e34f8a", "companyId": "14031804", "type": "payperiod", "_rid": "NmJkAKiCbEcFOQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcFOQEAAAAAAA==/", "_etag": "\"9e00c90f-0000-0100-0000-686ffa910000\"", "_attachments": "attachments/", "_ts": 1752169105}, {"payPeriodId": "1050103030766385", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-01-02T00:00:00Z", "endDate": "2025-01-08T00:00:00Z", "submitByDate": "2025-01-08T00:00:00Z", "checkDate": "2025-01-10T00:00:00Z", "checkCount": 4, "id": "9653e38c-ff5c-462b-9d09-b92007ed5367", "companyId": "14031804", "type": "payperiod", "_rid": "NmJkAKiCbEcGOQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcGOQEAAAAAAA==/", "_etag": "\"9e00cc0f-0000-0100-0000-686ffa910000\"", "_attachments": "attachments/", "_ts": 1752169105}, {"payPeriodId": "1050103283023104", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-01-09T00:00:00Z", "endDate": "2025-01-15T00:00:00Z", "submitByDate": "2025-01-15T00:00:00Z", "checkDate": "2025-01-17T00:00:00Z", "checkCount": 4, "id": "3d2cadc9-5a5c-4452-91b8-d1a327770dba", "companyId": "14031804", "type": "payperiod", "_rid": "NmJkAKiCbEcHOQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcHOQEAAAAAAA==/", "_etag": "\"9e00d20f-0000-0100-0000-686ffa910000\"", "_attachments": "attachments/", "_ts": 1752169105}, {"payPeriodId": "1050103532735129", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-01-16T00:00:00Z", "endDate": "2025-01-22T00:00:00Z", "submitByDate": "2025-01-22T00:00:00Z", "checkDate": "2025-01-24T00:00:00Z", "checkCount": 5, "id": "5cf86e31-b7f0-4738-9a6a-301fcc71be27", "companyId": "14031804", "type": "payperiod", "_rid": "NmJkAKiCbEcIOQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcIOQEAAAAAAA==/", "_etag": "\"9e00d80f-0000-0100-0000-686ffa910000\"", "_attachments": "attachments/", "_ts": 1752169105}, {"payPeriodId": "1050103728370352", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-01-23T00:00:00Z", "endDate": "2025-01-29T00:00:00Z", "submitByDate": "2025-01-29T00:00:00Z", "checkDate": "2025-01-31T00:00:00Z", "checkCount": 5, "id": "300a8cbb-84d3-480c-bfcb-64618cfd1aa4", "companyId": "14031804", "type": "payperiod", "_rid": "NmJkAKiCbEcJOQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcJOQEAAAAAAA==/", "_etag": "\"9e00dd0f-0000-0100-0000-686ffa910000\"", "_attachments": "attachments/", "_ts": 1752169105}, {"payPeriodId": "1050103970698115", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-01-30T00:00:00Z", "endDate": "2025-02-05T00:00:00Z", "submitByDate": "2025-02-05T00:00:00Z", "checkDate": "2025-02-07T00:00:00Z", "checkCount": 5, "id": "5b6e447a-1580-457e-bd95-765a881440a5", "companyId": "14031804", "type": "payperiod", "_rid": "NmJkAKiCbEcKOQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcKOQEAAAAAAA==/", "_etag": "\"9e00e10f-0000-0100-0000-686ffa910000\"", "_attachments": "attachments/", "_ts": 1752169105}, {"payPeriodId": "1050104217972602", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-02-06T00:00:00Z", "endDate": "2025-02-12T00:00:00Z", "submitByDate": "2025-02-12T00:00:00Z", "checkDate": "2025-02-14T00:00:00Z", "checkCount": 4, "id": "77847ce4-9381-448a-9228-0c29cc5e385f", "companyId": "14031804", "type": "payperiod", "_rid": "NmJkAKiCbEcLOQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcLOQEAAAAAAA==/", "_etag": "\"9e00e20f-0000-0100-0000-686ffa910000\"", "_attachments": "attachments/", "_ts": 1752169105}, {"payPeriodId": "1050104444235861", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-02-13T00:00:00Z", "endDate": "2025-02-19T00:00:00Z", "submitByDate": "2025-02-19T00:00:00Z", "checkDate": "2025-02-21T00:00:00Z", "checkCount": 5, "id": "72d82c88-2f9a-441a-ada8-5bcc5c098325", "companyId": "14031804", "type": "payperiod", "_rid": "NmJkAKiCbEcMOQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcMOQEAAAAAAA==/", "_etag": "\"9e00e70f-0000-0100-0000-686ffa910000\"", "_attachments": "attachments/", "_ts": 1752169105}, {"payPeriodId": "1050104696920917", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-02-20T00:00:00Z", "endDate": "2025-02-26T00:00:00Z", "submitByDate": "2025-02-26T00:00:00Z", "checkDate": "2025-02-28T00:00:00Z", "checkCount": 4, "id": "f540f5a1-ce89-43bc-aa82-5aee353b032f", "companyId": "14031804", "type": "payperiod", "_rid": "NmJkAKiCbEcNOQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcNOQEAAAAAAA==/", "_etag": "\"9e00ea0f-0000-0100-0000-686ffa920000\"", "_attachments": "attachments/", "_ts": 1752169106}, {"payPeriodId": "1050104914037753", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-02-27T00:00:00Z", "endDate": "2025-03-05T00:00:00Z", "submitByDate": "2025-03-05T00:00:00Z", "checkDate": "2025-03-07T00:00:00Z", "checkCount": 5, "id": "81766aa4-4a93-49a4-9bfa-03abe6181c9d", "companyId": "14031804", "type": "payperiod", "_rid": "NmJkAKiCbEcOOQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcOOQEAAAAAAA==/", "_etag": "\"9e00ef0f-0000-0100-0000-686ffa920000\"", "_attachments": "attachments/", "_ts": 1752169106}, {"payPeriodId": "1050105278385171", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-03-06T00:00:00Z", "endDate": "2025-03-12T00:00:00Z", "submitByDate": "2025-03-12T00:00:00Z", "checkDate": "2025-03-14T00:00:00Z", "checkCount": 5, "id": "1a32eda4-3d0f-4429-ade8-c576c88d73c0", "companyId": "14031804", "type": "payperiod", "_rid": "NmJkAKiCbEcPOQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcPOQEAAAAAAA==/", "_etag": "\"9e00f50f-0000-0100-0000-686ffa920000\"", "_attachments": "attachments/", "_ts": 1752169106}, {"payPeriodId": "1050105547300066", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-03-13T00:00:00Z", "endDate": "2025-03-19T00:00:00Z", "submitByDate": "2025-03-19T00:00:00Z", "checkDate": "2025-03-21T00:00:00Z", "checkCount": 5, "id": "ccc59d90-6baf-4264-9d67-65309899de61", "companyId": "14031804", "type": "payperiod", "_rid": "NmJkAKiCbEcQOQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcQOQEAAAAAAA==/", "_etag": "\"9e00f80f-0000-0100-0000-686ffa920000\"", "_attachments": "attachments/", "_ts": 1752169106}, {"payPeriodId": "1050105813421713", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-03-20T00:00:00Z", "endDate": "2025-03-26T00:00:00Z", "submitByDate": "2025-03-26T00:00:00Z", "checkDate": "2025-03-28T00:00:00Z", "checkCount": 4, "id": "6470b7e4-6523-43c7-8e4c-88035ec20638", "companyId": "14031804", "type": "payperiod", "_rid": "NmJkAKiCbEcROQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcROQEAAAAAAA==/", "_etag": "\"9e00fb0f-0000-0100-0000-686ffa920000\"", "_attachments": "attachments/", "_ts": 1752169106}, {"payPeriodId": "1050106053757879", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-03-27T00:00:00Z", "endDate": "2025-04-02T00:00:00Z", "submitByDate": "2025-04-02T00:00:00Z", "checkDate": "2025-04-04T00:00:00Z", "checkCount": 0, "id": "b6da63ef-ae53-477b-a870-d695998f185b", "companyId": "14031804", "type": "payperiod", "_rid": "NmJkAKiCbEcSOQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcSOQEAAAAAAA==/", "_etag": "\"9e000010-0000-0100-0000-686ffa920000\"", "_attachments": "attachments/", "_ts": 1752169106}, {"payPeriodId": "1050106296460761", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-04-03T00:00:00Z", "endDate": "2025-04-09T00:00:00Z", "submitByDate": "2025-04-09T00:00:00Z", "checkDate": "2025-04-11T00:00:00Z", "checkCount": 0, "id": "b9797165-b1ed-4a2c-b2ad-2ceacdb3f1da", "companyId": "14031804", "type": "payperiod", "_rid": "NmJkAKiCbEcTOQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcTOQEAAAAAAA==/", "_etag": "\"9e000410-0000-0100-0000-686ffa920000\"", "_attachments": "attachments/", "_ts": 1752169106}, {"payPeriodId": "1050106538396344", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-04-10T00:00:00Z", "endDate": "2025-04-16T00:00:00Z", "submitByDate": "2025-04-16T00:00:00Z", "checkDate": "2025-04-18T00:00:00Z", "checkCount": 0, "id": "a86741b1-4086-4793-b955-994731e5d093", "companyId": "14031804", "type": "payperiod", "_rid": "NmJkAKiCbEcUOQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcUOQEAAAAAAA==/", "_etag": "\"9e000b10-0000-0100-0000-686ffa920000\"", "_attachments": "attachments/", "_ts": 1752169106}, {"payPeriodId": "1050106792492273", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-04-17T00:00:00Z", "endDate": "2025-04-23T00:00:00Z", "submitByDate": "2025-04-23T00:00:00Z", "checkDate": "2025-04-25T00:00:00Z", "checkCount": 0, "id": "11a1e135-428f-41e7-b0d0-5e3bd2d5ea0e", "companyId": "14031804", "type": "payperiod", "_rid": "NmJkAKiCbEcVOQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcVOQEAAAAAAA==/", "_etag": "\"9e000e10-0000-0100-0000-686ffa920000\"", "_attachments": "attachments/", "_ts": 1752169106}, {"payPeriodId": "1050107010957908", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-04-24T00:00:00Z", "endDate": "2025-04-30T00:00:00Z", "submitByDate": "2025-04-30T00:00:00Z", "checkDate": "2025-05-02T00:00:00Z", "checkCount": 0, "id": "98250710-a874-43d3-a65c-e3b5f64c094e", "companyId": "14031804", "type": "payperiod", "_rid": "NmJkAKiCbEcWOQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcWOQEAAAAAAA==/", "_etag": "\"9e001710-0000-0100-0000-686ffa920000\"", "_attachments": "attachments/", "_ts": 1752169106}, {"payPeriodId": "1050107264249568", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-05-01T00:00:00Z", "endDate": "2025-05-07T00:00:00Z", "submitByDate": "2025-05-07T00:00:00Z", "checkDate": "2025-05-09T00:00:00Z", "checkCount": 0, "id": "2280e2da-865f-4cf3-a0ae-52e22c3a7aec", "companyId": "14031804", "type": "payperiod", "_rid": "NmJkAKiCbEcXOQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcXOQEAAAAAAA==/", "_etag": "\"9e001d10-0000-0100-0000-686ffa920000\"", "_attachments": "attachments/", "_ts": 1752169106}, {"payPeriodId": "1050107499552422", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-05-08T00:00:00Z", "endDate": "2025-05-14T00:00:00Z", "submitByDate": "2025-05-14T00:00:00Z", "checkDate": "2025-05-16T00:00:00Z", "checkCount": 0, "id": "b08e13f0-1e9b-4ab8-b8df-56ef0710c48d", "companyId": "14031804", "type": "payperiod", "_rid": "NmJkAKiCbEcYOQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcYOQEAAAAAAA==/", "_etag": "\"9e002010-0000-0100-0000-686ffa920000\"", "_attachments": "attachments/", "_ts": 1752169106}, {"payPeriodId": "1050107744943448", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-05-15T00:00:00Z", "endDate": "2025-05-21T00:00:00Z", "submitByDate": "2025-05-21T00:00:00Z", "checkDate": "2025-05-23T00:00:00Z", "checkCount": 0, "id": "ef622dfe-a231-4e59-8819-306afb3cbb16", "companyId": "14031804", "type": "payperiod", "_rid": "NmJkAKiCbEcZOQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcZOQEAAAAAAA==/", "_etag": "\"9e002410-0000-0100-0000-686ffa930000\"", "_attachments": "attachments/", "_ts": 1752169107}, {"payPeriodId": "1050107973594397", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-05-22T00:00:00Z", "endDate": "2025-05-28T00:00:00Z", "submitByDate": "2025-05-28T00:00:00Z", "checkDate": "2025-05-30T00:00:00Z", "checkCount": 0, "id": "c36b3713-2c69-40d2-aa67-beb490ad0725", "companyId": "14031804", "type": "payperiod", "_rid": "NmJkAKiCbEcaOQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcaOQEAAAAAAA==/", "_etag": "\"9e002810-0000-0100-0000-686ffa930000\"", "_attachments": "attachments/", "_ts": 1752169107}, {"payPeriodId": "1050108217960652", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-05-29T00:00:00Z", "endDate": "2025-06-04T00:00:00Z", "submitByDate": "2025-06-04T00:00:00Z", "checkDate": "2025-06-06T00:00:00Z", "checkCount": 0, "id": "d4c37856-2ebf-4539-a06a-3740ca3e6ad0", "companyId": "14031804", "type": "payperiod", "_rid": "NmJkAKiCbEcbOQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcbOQEAAAAAAA==/", "_etag": "\"9e002c10-0000-0100-0000-686ffa930000\"", "_attachments": "attachments/", "_ts": 1752169107}, {"payPeriodId": "1050108342826199", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-06-05T00:00:00Z", "endDate": "2025-06-11T00:00:00Z", "submitByDate": "2025-06-11T00:00:00Z", "checkDate": "2025-06-13T00:00:00Z", "checkCount": 0, "id": "0328a09e-6af4-4232-8ad4-2dde07c2e19d", "companyId": "14031804", "type": "payperiod", "_rid": "NmJkAKiCbEccOQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEccOQEAAAAAAA==/", "_etag": "\"9e002e10-0000-0100-0000-686ffa930000\"", "_attachments": "attachments/", "_ts": 1752169107}, {"payPeriodId": "1050108613880129", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-06-12T00:00:00Z", "endDate": "2025-06-18T00:00:00Z", "submitByDate": "2025-06-18T00:00:00Z", "checkDate": "2025-06-20T00:00:00Z", "checkCount": 0, "id": "66c3ab23-8661-4e89-b4ed-c024ff3c2e6a", "companyId": "14031804", "type": "payperiod", "_rid": "NmJkAKiCbEcdOQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcdOQEAAAAAAA==/", "_etag": "\"9e003210-0000-0100-0000-686ffa930000\"", "_attachments": "attachments/", "_ts": 1752169107}, {"payPeriodId": "1050108816287584", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-06-19T00:00:00Z", "endDate": "2025-06-25T00:00:00Z", "submitByDate": "2025-06-25T00:00:00Z", "checkDate": "2025-06-27T00:00:00Z", "checkCount": 0, "id": "9a795ee0-86b6-45bd-a76e-50f454c54690", "companyId": "14031804", "type": "payperiod", "_rid": "NmJkAKiCbEceOQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEceOQEAAAAAAA==/", "_etag": "\"9e003410-0000-0100-0000-686ffa930000\"", "_attachments": "attachments/", "_ts": 1752169107}, {"payPeriodId": "1050109058328496", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-06-26T00:00:00Z", "endDate": "2025-07-02T00:00:00Z", "submitByDate": "2025-07-01T00:00:00Z", "checkDate": "2025-07-03T00:00:00Z", "checkCount": 0, "id": "e1bcb250-7c5a-4532-98cc-9fe3be3206aa", "companyId": "14031804", "type": "payperiod", "_rid": "NmJkAKiCbEcfOQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcfOQEAAAAAAA==/", "_etag": "\"9e003610-0000-0100-0000-686ffa930000\"", "_attachments": "attachments/", "_ts": 1752169107}, {"payPeriodId": "1050109301165776", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-07-03T00:00:00Z", "endDate": "2025-07-09T00:00:00Z", "submitByDate": "2025-07-09T00:00:00Z", "checkDate": "2025-07-11T00:00:00Z", "checkCount": 0, "id": "e394b9b9-7539-4030-a40c-42c8a4d34c85", "companyId": "14031804", "type": "payperiod", "_rid": "NmJkAKiCbEcgOQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcgOQEAAAAAAA==/", "_etag": "\"9e003a10-0000-0100-0000-686ffa930000\"", "_attachments": "attachments/", "_ts": 1752169107}, {"payPeriodId": "1050109564360781", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-07-10T00:00:00Z", "endDate": "2025-07-16T00:00:00Z", "submitByDate": "2025-07-16T00:00:00Z", "checkDate": "2025-07-18T00:00:00Z", "checkCount": 0, "id": "2c92a89f-f8ec-4dd5-81ba-76900e0fe842", "companyId": "14031804", "type": "payperiod", "_rid": "NmJkAKiCbEchOQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEchOQEAAAAAAA==/", "_etag": "\"9e004010-0000-0100-0000-686ffa930000\"", "_attachments": "attachments/", "_ts": 1752169107}, {"payPeriodId": "1050109795669035", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-07-17T00:00:00Z", "endDate": "2025-07-23T00:00:00Z", "submitByDate": "2025-07-23T00:00:00Z", "checkDate": "2025-07-25T00:00:00Z", "checkCount": 0, "id": "238b6ed3-70c8-48f5-ac8a-ebdf51438be5", "companyId": "14031804", "type": "payperiod", "_rid": "NmJkAKiCbEciOQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEciOQEAAAAAAA==/", "_etag": "\"9e004410-0000-0100-0000-686ffa930000\"", "_attachments": "attachments/", "_ts": 1752169107}, {"payPeriodId": "1050110021451888", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-07-24T00:00:00Z", "endDate": "2025-07-30T00:00:00Z", "submitByDate": "2025-07-30T00:00:00Z", "checkDate": "2025-08-01T00:00:00Z", "checkCount": 0, "id": "8c87f08d-4864-4742-a531-56963db9140d", "companyId": "14031804", "type": "payperiod", "_rid": "NmJkAKiCbEcjOQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcjOQEAAAAAAA==/", "_etag": "\"9e004610-0000-0100-0000-686ffa930000\"", "_attachments": "attachments/", "_ts": 1752169107}, {"payPeriodId": "1050110276266457", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-07-31T00:00:00Z", "endDate": "2025-08-06T00:00:00Z", "submitByDate": "2025-08-06T00:00:00Z", "checkDate": "2025-08-08T00:00:00Z", "checkCount": 0, "id": "30da95cf-10f3-4695-ba9a-d6da9fd57a56", "companyId": "14031804", "type": "payperiod", "_rid": "NmJkAKiCbEckOQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEckOQEAAAAAAA==/", "_etag": "\"9e004a10-0000-0100-0000-686ffa930000\"", "_attachments": "attachments/", "_ts": 1752169107}, {"payPeriodId": "1050110517120720", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-08-07T00:00:00Z", "endDate": "2025-08-13T00:00:00Z", "submitByDate": "2025-08-13T00:00:00Z", "checkDate": "2025-08-15T00:00:00Z", "checkCount": 0, "id": "a0d13b18-e45d-4ade-9f8d-1085623aec3d", "companyId": "14031804", "type": "payperiod", "_rid": "NmJkAKiCbEclOQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEclOQEAAAAAAA==/", "_etag": "\"9e004f10-0000-0100-0000-686ffa930000\"", "_attachments": "attachments/", "_ts": 1752169107}, {"payPeriodId": "1050110771547801", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-08-14T00:00:00Z", "endDate": "2025-08-20T00:00:00Z", "submitByDate": "2025-08-20T00:00:00Z", "checkDate": "2025-08-22T00:00:00Z", "checkCount": 0, "id": "9de21e72-b3f8-49e8-8d5e-ddd0c1e79f8a", "companyId": "14031804", "type": "payperiod", "_rid": "NmJkAKiCbEcmOQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcmOQEAAAAAAA==/", "_etag": "\"9e005210-0000-0100-0000-686ffa940000\"", "_attachments": "attachments/", "_ts": 1752169108}, {"payPeriodId": "1050111005803658", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-08-21T00:00:00Z", "endDate": "2025-08-27T00:00:00Z", "submitByDate": "2025-08-27T00:00:00Z", "checkDate": "2025-08-29T00:00:00Z", "checkCount": 0, "id": "fc2b7188-c85c-4c59-a425-441cfe21e9fc", "companyId": "14031804", "type": "payperiod", "_rid": "NmJkAKiCbEcnOQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcnOQEAAAAAAA==/", "_etag": "\"9e005910-0000-0100-0000-686ffa940000\"", "_attachments": "attachments/", "_ts": 1752169108}, {"payPeriodId": "1050111334676357", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-08-28T00:00:00Z", "endDate": "2025-09-03T00:00:00Z", "submitByDate": "2025-09-03T00:00:00Z", "checkDate": "2025-09-05T00:00:00Z", "checkCount": 0, "id": "fd1ee12e-bf3d-4343-bfe7-713c89a6076b", "companyId": "14031804", "type": "payperiod", "_rid": "NmJkAKiCbEcoOQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcoOQEAAAAAAA==/", "_etag": "\"9e005b10-0000-0100-0000-686ffa940000\"", "_attachments": "attachments/", "_ts": 1752169108}, {"payPeriodId": "1050111563390561", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-09-04T00:00:00Z", "endDate": "2025-09-10T00:00:00Z", "submitByDate": "2025-09-10T00:00:00Z", "checkDate": "2025-09-12T00:00:00Z", "checkCount": 0, "id": "8c641f1b-4b74-4955-8204-097784942f15", "companyId": "14031804", "type": "payperiod", "_rid": "NmJkAKiCbEcpOQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcpOQEAAAAAAA==/", "_etag": "\"9e005c10-0000-0100-0000-686ffa940000\"", "_attachments": "attachments/", "_ts": 1752169108}, {"payPeriodId": "1050111873092806", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-09-11T00:00:00Z", "endDate": "2025-09-17T00:00:00Z", "submitByDate": "2025-09-17T00:00:00Z", "checkDate": "2025-09-19T00:00:00Z", "checkCount": 0, "id": "ac41038b-13f3-4e71-bf51-0c3bff51de13", "companyId": "14031804", "type": "payperiod", "_rid": "NmJkAKiCbEcqOQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcqOQEAAAAAAA==/", "_etag": "\"9e005f10-0000-0100-0000-686ffa940000\"", "_attachments": "attachments/", "_ts": 1752169108}, {"payPeriodId": "1050112017682575", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-09-18T00:00:00Z", "endDate": "2025-09-24T00:00:00Z", "submitByDate": "2025-09-24T00:00:00Z", "checkDate": "2025-09-26T00:00:00Z", "checkCount": 0, "id": "5fa07618-7140-49fd-9580-c85ac94a778b", "companyId": "14031804", "type": "payperiod", "_rid": "NmJkAKiCbEcrOQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcrOQEAAAAAAA==/", "_etag": "\"9e006210-0000-0100-0000-686ffa940000\"", "_attachments": "attachments/", "_ts": 1752169108}, {"payPeriodId": "1050112323675123", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-09-25T00:00:00Z", "endDate": "2025-10-01T00:00:00Z", "submitByDate": "2025-10-01T00:00:00Z", "checkDate": "2025-10-03T00:00:00Z", "checkCount": 0, "id": "fa8637e8-fc76-4543-9a38-c9c5fd990c13", "companyId": "14031804", "type": "payperiod", "_rid": "NmJkAKiCbEcsOQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcsOQEAAAAAAA==/", "_etag": "\"9e006410-0000-0100-0000-686ffa940000\"", "_attachments": "attachments/", "_ts": 1752169108}, {"payPeriodId": "1050112391938507", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-10-02T00:00:00Z", "endDate": "2025-10-08T00:00:00Z", "submitByDate": "2025-10-08T00:00:00Z", "checkDate": "2025-10-10T00:00:00Z", "checkCount": 0, "id": "d06e20ff-0067-48eb-badd-e2ce053fedca", "companyId": "14031804", "type": "payperiod", "_rid": "NmJkAKiCbEctOQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEctOQEAAAAAAA==/", "_etag": "\"9e006710-0000-0100-0000-686ffa940000\"", "_attachments": "attachments/", "_ts": 1752169108}, {"payPeriodId": "1050102839108267", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2024-12-26T00:00:00Z", "endDate": "2025-01-01T00:00:00Z", "submitByDate": "2024-12-31T00:00:00Z", "checkDate": "2025-01-03T00:00:00Z", "checkCount": 5, "id": "f3cedea8-68dd-4893-bd24-b17e5db480e2", "companyId": "14031804", "type": "payperiod", "_rid": "NmJkAKiCbEdIOQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdIOQEAAAAAAA==/", "_etag": "\"9e00b510-0000-0100-0000-686ffa960000\"", "_attachments": "attachments/", "_ts": 1752169110}, {"payPeriodId": "1050103030766385", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-01-02T00:00:00Z", "endDate": "2025-01-08T00:00:00Z", "submitByDate": "2025-01-08T00:00:00Z", "checkDate": "2025-01-10T00:00:00Z", "checkCount": 4, "id": "41b12085-12cf-4624-8062-b319922ec6ac", "companyId": "14031804", "type": "payperiod", "_rid": "NmJkAKiCbEdJOQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdJOQEAAAAAAA==/", "_etag": "\"9e00b810-0000-0100-0000-686ffa970000\"", "_attachments": "attachments/", "_ts": 1752169111}, {"payPeriodId": "1050103283023104", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-01-09T00:00:00Z", "endDate": "2025-01-15T00:00:00Z", "submitByDate": "2025-01-15T00:00:00Z", "checkDate": "2025-01-17T00:00:00Z", "checkCount": 4, "id": "f4fbe120-9ed1-4c62-91e0-58a291357a6b", "companyId": "14031804", "type": "payperiod", "_rid": "NmJkAKiCbEdKOQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdKOQEAAAAAAA==/", "_etag": "\"9e00b910-0000-0100-0000-686ffa970000\"", "_attachments": "attachments/", "_ts": 1752169111}, {"payPeriodId": "1050103532735129", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-01-16T00:00:00Z", "endDate": "2025-01-22T00:00:00Z", "submitByDate": "2025-01-22T00:00:00Z", "checkDate": "2025-01-24T00:00:00Z", "checkCount": 5, "id": "feabee36-0ddd-470c-89fe-f3fe871e51b2", "companyId": "14031804", "type": "payperiod", "_rid": "NmJkAKiCbEdLOQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdLOQEAAAAAAA==/", "_etag": "\"9e00bb10-0000-0100-0000-686ffa970000\"", "_attachments": "attachments/", "_ts": 1752169111}, {"payPeriodId": "1050103728370352", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-01-23T00:00:00Z", "endDate": "2025-01-29T00:00:00Z", "submitByDate": "2025-01-29T00:00:00Z", "checkDate": "2025-01-31T00:00:00Z", "checkCount": 5, "id": "fe5d6c52-d5af-4888-8602-49cff6009598", "companyId": "14031804", "type": "payperiod", "_rid": "NmJkAKiCbEdMOQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdMOQEAAAAAAA==/", "_etag": "\"9e00be10-0000-0100-0000-686ffa970000\"", "_attachments": "attachments/", "_ts": 1752169111}, {"payPeriodId": "1050103970698115", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-01-30T00:00:00Z", "endDate": "2025-02-05T00:00:00Z", "submitByDate": "2025-02-05T00:00:00Z", "checkDate": "2025-02-07T00:00:00Z", "checkCount": 5, "id": "c48418d4-f4aa-412f-8aa1-15e81443a020", "companyId": "14031804", "type": "payperiod", "_rid": "NmJkAKiCbEdNOQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdNOQEAAAAAAA==/", "_etag": "\"9e00c010-0000-0100-0000-686ffa970000\"", "_attachments": "attachments/", "_ts": 1752169111}, {"payPeriodId": "1050104217972602", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-02-06T00:00:00Z", "endDate": "2025-02-12T00:00:00Z", "submitByDate": "2025-02-12T00:00:00Z", "checkDate": "2025-02-14T00:00:00Z", "checkCount": 4, "id": "e254e5c2-f316-4269-926e-b7d81f81936f", "companyId": "14031804", "type": "payperiod", "_rid": "NmJkAKiCbEdOOQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdOOQEAAAAAAA==/", "_etag": "\"9e00c810-0000-0100-0000-686ffa970000\"", "_attachments": "attachments/", "_ts": 1752169111}, {"payPeriodId": "1050104444235861", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-02-13T00:00:00Z", "endDate": "2025-02-19T00:00:00Z", "submitByDate": "2025-02-19T00:00:00Z", "checkDate": "2025-02-21T00:00:00Z", "checkCount": 5, "id": "4bacfb2b-6101-429f-96fc-f67b2171b763", "companyId": "14031804", "type": "payperiod", "_rid": "NmJkAKiCbEdPOQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdPOQEAAAAAAA==/", "_etag": "\"9e00ca10-0000-0100-0000-686ffa970000\"", "_attachments": "attachments/", "_ts": 1752169111}, {"payPeriodId": "1050104696920917", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-02-20T00:00:00Z", "endDate": "2025-02-26T00:00:00Z", "submitByDate": "2025-02-26T00:00:00Z", "checkDate": "2025-02-28T00:00:00Z", "checkCount": 4, "id": "1d449156-d345-4898-883b-e8d1a4966034", "companyId": "14031804", "type": "payperiod", "_rid": "NmJkAKiCbEdQOQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdQOQEAAAAAAA==/", "_etag": "\"9e00ce10-0000-0100-0000-686ffa970000\"", "_attachments": "attachments/", "_ts": 1752169111}, {"payPeriodId": "1050104914037753", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-02-27T00:00:00Z", "endDate": "2025-03-05T00:00:00Z", "submitByDate": "2025-03-05T00:00:00Z", "checkDate": "2025-03-07T00:00:00Z", "checkCount": 5, "id": "f127e773-b1d6-4127-b83a-bfd158fa394c", "companyId": "14031804", "type": "payperiod", "_rid": "NmJkAKiCbEdROQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdROQEAAAAAAA==/", "_etag": "\"9e00cf10-0000-0100-0000-686ffa970000\"", "_attachments": "attachments/", "_ts": 1752169111}, {"payPeriodId": "1050105278385171", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-03-06T00:00:00Z", "endDate": "2025-03-12T00:00:00Z", "submitByDate": "2025-03-12T00:00:00Z", "checkDate": "2025-03-14T00:00:00Z", "checkCount": 5, "id": "b4beee25-89b6-4b36-acf8-de7c61036d6f", "companyId": "14031804", "type": "payperiod", "_rid": "NmJkAKiCbEdSOQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdSOQEAAAAAAA==/", "_etag": "\"9e00d510-0000-0100-0000-686ffa970000\"", "_attachments": "attachments/", "_ts": 1752169111}, {"payPeriodId": "1050105547300066", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-03-13T00:00:00Z", "endDate": "2025-03-19T00:00:00Z", "submitByDate": "2025-03-19T00:00:00Z", "checkDate": "2025-03-21T00:00:00Z", "checkCount": 5, "id": "414794f5-b295-4a81-8f74-e6c4fb0a0b9e", "companyId": "14031804", "type": "payperiod", "_rid": "NmJkAKiCbEdTOQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdTOQEAAAAAAA==/", "_etag": "\"9e00d810-0000-0100-0000-686ffa970000\"", "_attachments": "attachments/", "_ts": 1752169111}, {"payPeriodId": "1050105813421713", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-03-20T00:00:00Z", "endDate": "2025-03-26T00:00:00Z", "submitByDate": "2025-03-26T00:00:00Z", "checkDate": "2025-03-28T00:00:00Z", "checkCount": 4, "id": "2205e423-9a64-4e8b-afe9-c25b5acdd154", "companyId": "14031804", "type": "payperiod", "_rid": "NmJkAKiCbEdUOQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdUOQEAAAAAAA==/", "_etag": "\"9e00dc10-0000-0100-0000-686ffa970000\"", "_attachments": "attachments/", "_ts": 1752169111}, {"payPeriodId": "1050106053757879", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-03-27T00:00:00Z", "endDate": "2025-04-02T00:00:00Z", "submitByDate": "2025-04-02T00:00:00Z", "checkDate": "2025-04-04T00:00:00Z", "checkCount": 5, "id": "568aa3ef-4d3c-41ea-9266-94d7f58e7565", "companyId": "14031804", "type": "payperiod", "_rid": "NmJkAKiCbEdVOQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdVOQEAAAAAAA==/", "_etag": "\"9e00de10-0000-0100-0000-686ffa970000\"", "_attachments": "attachments/", "_ts": 1752169111}, {"payPeriodId": "1050106296460761", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-04-03T00:00:00Z", "endDate": "2025-04-09T00:00:00Z", "submitByDate": "2025-04-09T00:00:00Z", "checkDate": "2025-04-11T00:00:00Z", "checkCount": 5, "id": "5a28f4bb-27f5-49ca-b7ee-0c76181a4688", "companyId": "14031804", "type": "payperiod", "_rid": "NmJkAKiCbEdWOQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdWOQEAAAAAAA==/", "_etag": "\"9e00e210-0000-0100-0000-686ffa980000\"", "_attachments": "attachments/", "_ts": 1752169112}, {"payPeriodId": "1050106538396344", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-04-10T00:00:00Z", "endDate": "2025-04-16T00:00:00Z", "submitByDate": "2025-04-16T00:00:00Z", "checkDate": "2025-04-18T00:00:00Z", "checkCount": 5, "id": "6c646c73-5be4-4ca6-81ae-41d60a7f96b0", "companyId": "14031804", "type": "payperiod", "_rid": "NmJkAKiCbEdXOQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdXOQEAAAAAAA==/", "_etag": "\"9e00e510-0000-0100-0000-686ffa980000\"", "_attachments": "attachments/", "_ts": 1752169112}, {"payPeriodId": "1050106792492273", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-04-17T00:00:00Z", "endDate": "2025-04-23T00:00:00Z", "submitByDate": "2025-04-23T00:00:00Z", "checkDate": "2025-04-25T00:00:00Z", "checkCount": 4, "id": "69816779-4f57-417a-a191-7c06613e4a22", "companyId": "14031804", "type": "payperiod", "_rid": "NmJkAKiCbEdYOQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdYOQEAAAAAAA==/", "_etag": "\"9e00ec10-0000-0100-0000-686ffa980000\"", "_attachments": "attachments/", "_ts": 1752169112}, {"payPeriodId": "1050107010957908", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-04-24T00:00:00Z", "endDate": "2025-04-30T00:00:00Z", "submitByDate": "2025-04-30T00:00:00Z", "checkDate": "2025-05-02T00:00:00Z", "checkCount": 4, "id": "0ab89d76-7f7a-4b56-84e8-5fc5d21dd183", "companyId": "14031804", "type": "payperiod", "_rid": "NmJkAKiCbEdZOQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdZOQEAAAAAAA==/", "_etag": "\"9e00f010-0000-0100-0000-686ffa980000\"", "_attachments": "attachments/", "_ts": 1752169112}, {"payPeriodId": "1050107264249568", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-05-01T00:00:00Z", "endDate": "2025-05-07T00:00:00Z", "submitByDate": "2025-05-07T00:00:00Z", "checkDate": "2025-05-09T00:00:00Z", "checkCount": 5, "id": "3fc9a932-a095-4a5f-b48d-d45e645d1512", "companyId": "14031804", "type": "payperiod", "_rid": "NmJkAKiCbEdaOQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdaOQEAAAAAAA==/", "_etag": "\"9e00f210-0000-0100-0000-686ffa980000\"", "_attachments": "attachments/", "_ts": 1752169112}, {"payPeriodId": "1050107499552422", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-05-08T00:00:00Z", "endDate": "2025-05-14T00:00:00Z", "submitByDate": "2025-05-14T00:00:00Z", "checkDate": "2025-05-16T00:00:00Z", "checkCount": 5, "id": "1b225ecd-2718-4120-a69d-99d7f9bc267c", "companyId": "14031804", "type": "payperiod", "_rid": "NmJkAKiCbEdbOQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdbOQEAAAAAAA==/", "_etag": "\"9e00f410-0000-0100-0000-686ffa980000\"", "_attachments": "attachments/", "_ts": 1752169112}, {"payPeriodId": "1050107744943448", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-05-15T00:00:00Z", "endDate": "2025-05-21T00:00:00Z", "submitByDate": "2025-05-21T00:00:00Z", "checkDate": "2025-05-23T00:00:00Z", "checkCount": 5, "id": "6316e39b-d80a-47e4-b450-4c44215ab2da", "companyId": "14031804", "type": "payperiod", "_rid": "NmJkAKiCbEdcOQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdcOQEAAAAAAA==/", "_etag": "\"9e00f610-0000-0100-0000-686ffa980000\"", "_attachments": "attachments/", "_ts": 1752169112}, {"payPeriodId": "1050107973594397", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-05-22T00:00:00Z", "endDate": "2025-05-28T00:00:00Z", "submitByDate": "2025-05-28T00:00:00Z", "checkDate": "2025-05-30T00:00:00Z", "checkCount": 4, "id": "604931ef-2f61-4967-9b80-dd344b0a0eba", "companyId": "14031804", "type": "payperiod", "_rid": "NmJkAKiCbEddOQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEddOQEAAAAAAA==/", "_etag": "\"9e00fb10-0000-0100-0000-686ffa980000\"", "_attachments": "attachments/", "_ts": 1752169112}, {"payPeriodId": "1050108217960652", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-05-29T00:00:00Z", "endDate": "2025-06-04T00:00:00Z", "submitByDate": "2025-06-04T00:00:00Z", "checkDate": "2025-06-06T00:00:00Z", "checkCount": 5, "id": "759f578c-1cad-48b9-a850-e2469027f5ed", "companyId": "14031804", "type": "payperiod", "_rid": "NmJkAKiCbEdeOQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdeOQEAAAAAAA==/", "_etag": "\"9e000411-0000-0100-0000-686ffa980000\"", "_attachments": "attachments/", "_ts": 1752169112}, {"payPeriodId": "1050108342826199", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-06-05T00:00:00Z", "endDate": "2025-06-11T00:00:00Z", "submitByDate": "2025-06-11T00:00:00Z", "checkDate": "2025-06-13T00:00:00Z", "checkCount": 4, "id": "e93b5dd2-20db-40b4-a7d2-b1be1adeacc3", "companyId": "14031804", "type": "payperiod", "_rid": "NmJkAKiCbEdfOQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdfOQEAAAAAAA==/", "_etag": "\"9e000611-0000-0100-0000-686ffa980000\"", "_attachments": "attachments/", "_ts": 1752169112}, {"payPeriodId": "1050108613880129", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-06-12T00:00:00Z", "endDate": "2025-06-18T00:00:00Z", "submitByDate": "2025-06-18T00:00:00Z", "checkDate": "2025-06-20T00:00:00Z", "checkCount": 5, "id": "039b5541-6d2e-4987-a8ea-a214bd6b62a1", "companyId": "14031804", "type": "payperiod", "_rid": "NmJkAKiCbEdgOQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdgOQEAAAAAAA==/", "_etag": "\"9e000911-0000-0100-0000-686ffa980000\"", "_attachments": "attachments/", "_ts": 1752169112}, {"payPeriodId": "1050108816287584", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-06-19T00:00:00Z", "endDate": "2025-06-25T00:00:00Z", "submitByDate": "2025-06-25T00:00:00Z", "checkDate": "2025-06-27T00:00:00Z", "checkCount": 5, "id": "f4d4b416-c2b6-4367-85ed-f9e23f6652fa", "companyId": "14031804", "type": "payperiod", "_rid": "NmJkAKiCbEdhOQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdhOQEAAAAAAA==/", "_etag": "\"9e000d11-0000-0100-0000-686ffa980000\"", "_attachments": "attachments/", "_ts": 1752169112}, {"payPeriodId": "1050109058328496", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-06-26T00:00:00Z", "endDate": "2025-07-02T00:00:00Z", "submitByDate": "2025-07-01T00:00:00Z", "checkDate": "2025-07-03T00:00:00Z", "checkCount": 5, "id": "1d2abe99-f2d3-439f-9145-120bf414f671", "companyId": "14031804", "type": "payperiod", "_rid": "NmJkAKiCbEdiOQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdiOQEAAAAAAA==/", "_etag": "\"9e000f11-0000-0100-0000-686ffa980000\"", "_attachments": "attachments/", "_ts": 1752169112}, {"payPeriodId": "1050109301165776", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-07-03T00:00:00Z", "endDate": "2025-07-09T00:00:00Z", "submitByDate": "2025-07-09T00:00:00Z", "checkDate": "2025-07-11T00:00:00Z", "checkCount": 0, "id": "a85b7df6-2852-4c23-9fdf-781d19f5fe1e", "companyId": "14031804", "type": "payperiod", "_rid": "NmJkAKiCbEdjOQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdjOQEAAAAAAA==/", "_etag": "\"9e001111-0000-0100-0000-686ffa990000\"", "_attachments": "attachments/", "_ts": 1752169113}, {"payPeriodId": "1050109564360781", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-07-10T00:00:00Z", "endDate": "2025-07-16T00:00:00Z", "submitByDate": "2025-07-16T00:00:00Z", "checkDate": "2025-07-18T00:00:00Z", "checkCount": 0, "id": "2b5d075f-bae6-4ee6-b50b-756bcf448368", "companyId": "14031804", "type": "payperiod", "_rid": "NmJkAKiCbEdkOQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdkOQEAAAAAAA==/", "_etag": "\"9e001411-0000-0100-0000-686ffa990000\"", "_attachments": "attachments/", "_ts": 1752169113}, {"payPeriodId": "1050109795669035", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-07-17T00:00:00Z", "endDate": "2025-07-23T00:00:00Z", "submitByDate": "2025-07-23T00:00:00Z", "checkDate": "2025-07-25T00:00:00Z", "checkCount": 0, "id": "fd46f1e5-50db-45fe-8f28-924aeeab0986", "companyId": "14031804", "type": "payperiod", "_rid": "NmJkAKiCbEdlOQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdlOQEAAAAAAA==/", "_etag": "\"9e001511-0000-0100-0000-686ffa990000\"", "_attachments": "attachments/", "_ts": 1752169113}, {"payPeriodId": "1050110021451888", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-07-24T00:00:00Z", "endDate": "2025-07-30T00:00:00Z", "submitByDate": "2025-07-30T00:00:00Z", "checkDate": "2025-08-01T00:00:00Z", "checkCount": 0, "id": "caf98d50-2b5f-496a-b4e2-3ce1645ef808", "companyId": "14031804", "type": "payperiod", "_rid": "NmJkAKiCbEdmOQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdmOQEAAAAAAA==/", "_etag": "\"9e001811-0000-0100-0000-686ffa990000\"", "_attachments": "attachments/", "_ts": 1752169113}, {"payPeriodId": "1050110276266457", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-07-31T00:00:00Z", "endDate": "2025-08-06T00:00:00Z", "submitByDate": "2025-08-06T00:00:00Z", "checkDate": "2025-08-08T00:00:00Z", "checkCount": 0, "id": "5f21d883-9f7c-47d8-b453-f15cdfb48c69", "companyId": "14031804", "type": "payperiod", "_rid": "NmJkAKiCbEdnOQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdnOQEAAAAAAA==/", "_etag": "\"9e001a11-0000-0100-0000-686ffa990000\"", "_attachments": "attachments/", "_ts": 1752169113}, {"payPeriodId": "1050110517120720", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-08-07T00:00:00Z", "endDate": "2025-08-13T00:00:00Z", "submitByDate": "2025-08-13T00:00:00Z", "checkDate": "2025-08-15T00:00:00Z", "checkCount": 0, "id": "0b02641e-db1e-40c3-8c95-4e32c80e4b77", "companyId": "14031804", "type": "payperiod", "_rid": "NmJkAKiCbEdoOQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdoOQEAAAAAAA==/", "_etag": "\"9e001d11-0000-0100-0000-686ffa990000\"", "_attachments": "attachments/", "_ts": 1752169113}, {"payPeriodId": "1050110771547801", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-08-14T00:00:00Z", "endDate": "2025-08-20T00:00:00Z", "submitByDate": "2025-08-20T00:00:00Z", "checkDate": "2025-08-22T00:00:00Z", "checkCount": 0, "id": "412b0fdd-6aad-43c9-9c99-5e9381febb34", "companyId": "14031804", "type": "payperiod", "_rid": "NmJkAKiCbEdpOQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdpOQEAAAAAAA==/", "_etag": "\"9e001f11-0000-0100-0000-686ffa990000\"", "_attachments": "attachments/", "_ts": 1752169113}, {"payPeriodId": "1050111005803658", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-08-21T00:00:00Z", "endDate": "2025-08-27T00:00:00Z", "submitByDate": "2025-08-27T00:00:00Z", "checkDate": "2025-08-29T00:00:00Z", "checkCount": 0, "id": "e1ff5412-11fd-4d90-8b78-719da9600e20", "companyId": "14031804", "type": "payperiod", "_rid": "NmJkAKiCbEdqOQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdqOQEAAAAAAA==/", "_etag": "\"9e002211-0000-0100-0000-686ffa990000\"", "_attachments": "attachments/", "_ts": 1752169113}, {"payPeriodId": "1050111334676357", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-08-28T00:00:00Z", "endDate": "2025-09-03T00:00:00Z", "submitByDate": "2025-09-03T00:00:00Z", "checkDate": "2025-09-05T00:00:00Z", "checkCount": 0, "id": "ff9bf6e8-200a-4fa4-a78b-6533de5a2b76", "companyId": "14031804", "type": "payperiod", "_rid": "NmJkAKiCbEdrOQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdrOQEAAAAAAA==/", "_etag": "\"9e002611-0000-0100-0000-686ffa990000\"", "_attachments": "attachments/", "_ts": 1752169113}, {"payPeriodId": "1050111563390561", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-09-04T00:00:00Z", "endDate": "2025-09-10T00:00:00Z", "submitByDate": "2025-09-10T00:00:00Z", "checkDate": "2025-09-12T00:00:00Z", "checkCount": 0, "id": "5af2ac9a-cc2d-426b-a7d6-728eae2ead7d", "companyId": "14031804", "type": "payperiod", "_rid": "NmJkAKiCbEdsOQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdsOQEAAAAAAA==/", "_etag": "\"9e002711-0000-0100-0000-686ffa990000\"", "_attachments": "attachments/", "_ts": 1752169113}, {"payPeriodId": "1050111873092806", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-09-11T00:00:00Z", "endDate": "2025-09-17T00:00:00Z", "submitByDate": "2025-09-17T00:00:00Z", "checkDate": "2025-09-19T00:00:00Z", "checkCount": 0, "id": "ab5a8734-dde3-48b3-8008-6e3ad798f620", "companyId": "14031804", "type": "payperiod", "_rid": "NmJkAKiCbEdtOQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdtOQEAAAAAAA==/", "_etag": "\"9e002b11-0000-0100-0000-686ffa990000\"", "_attachments": "attachments/", "_ts": 1752169113}, {"payPeriodId": "1050112017682575", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-09-18T00:00:00Z", "endDate": "2025-09-24T00:00:00Z", "submitByDate": "2025-09-24T00:00:00Z", "checkDate": "2025-09-26T00:00:00Z", "checkCount": 0, "id": "33a223f1-07f1-49b7-b561-63e3496a50d6", "companyId": "14031804", "type": "payperiod", "_rid": "NmJkAKiCbEduOQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEduOQEAAAAAAA==/", "_etag": "\"9e002f11-0000-0100-0000-686ffa990000\"", "_attachments": "attachments/", "_ts": 1752169113}, {"payPeriodId": "1050112323675123", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-09-25T00:00:00Z", "endDate": "2025-10-01T00:00:00Z", "submitByDate": "2025-10-01T00:00:00Z", "checkDate": "2025-10-03T00:00:00Z", "checkCount": 0, "id": "8b82d29c-1bc2-4fc1-b6cb-85d282757cdf", "companyId": "14031804", "type": "payperiod", "_rid": "NmJkAKiCbEdvOQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdvOQEAAAAAAA==/", "_etag": "\"9e003111-0000-0100-0000-686ffa990000\"", "_attachments": "attachments/", "_ts": 1752169113}, {"payPeriodId": "1050112391938507", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-10-02T00:00:00Z", "endDate": "2025-10-08T00:00:00Z", "submitByDate": "2025-10-08T00:00:00Z", "checkDate": "2025-10-10T00:00:00Z", "checkCount": 0, "id": "319ec1a4-8e20-4991-a289-e26c567b58bf", "companyId": "14031804", "type": "payperiod", "_rid": "NmJkAKiCbEdwOQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdwOQEAAAAAAA==/", "_etag": "\"9e003211-0000-0100-0000-686ffa9a0000\"", "_attachments": "attachments/", "_ts": 1752169114}], "links": [{"rel": "self", "href": "https://ca-payroll-ai-mock-sb-001-secure.nicebeach-8a7a52ab.eastus.azurecontainerapps.io/ca/companies/14031804/payperiods"}]}, "status_code": 200}