{"success": true, "company_id": "12018698", "data": {"metadata": {"contentItemCount": 160}, "content": [{"payPeriodId": "1030069029932861", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2024-12-29T00:00:00Z", "endDate": "2025-01-04T00:00:00Z", "submitByDate": "2025-01-07T00:00:00Z", "checkDate": "2025-01-09T00:00:00Z", "checkCount": 5, "id": "091d1d88-5134-417b-9bdd-669587c0dad6", "companyId": "12018698", "type": "payperiod", "_rid": "NmJkAKiCbEewHQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEewHQAAAAAAAA==/", "_etag": "\"97000b63-0000-0100-0000-686fd17b0000\"", "_attachments": "attachments/", "_ts": 1752158587}, {"payPeriodId": "1030069156088165", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-01-05T00:00:00Z", "endDate": "2025-01-11T00:00:00Z", "submitByDate": "2025-01-14T00:00:00Z", "checkDate": "2025-01-16T00:00:00Z", "checkCount": 6, "id": "aa1d4a34-1c61-499a-a12d-32c8bb70bdfd", "companyId": "12018698", "type": "payperiod", "_rid": "NmJkAKiCbEexHQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEexHQAAAAAAAA==/", "_etag": "\"97000f63-0000-0100-0000-686fd17c0000\"", "_attachments": "attachments/", "_ts": 1752158588}, {"payPeriodId": "1030069286625805", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-01-12T00:00:00Z", "endDate": "2025-01-18T00:00:00Z", "submitByDate": "2025-01-21T00:00:00Z", "checkDate": "2025-01-23T00:00:00Z", "checkCount": 6, "id": "5156c011-36df-4415-972b-73d82dd7b50e", "companyId": "12018698", "type": "payperiod", "_rid": "NmJkAKiCbEeyHQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeyHQAAAAAAAA==/", "_etag": "\"97001363-0000-0100-0000-686fd17c0000\"", "_attachments": "attachments/", "_ts": 1752158588}, {"payPeriodId": "1030069438118528", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-01-19T00:00:00Z", "endDate": "2025-01-25T00:00:00Z", "submitByDate": "2025-01-28T00:00:00Z", "checkDate": "2025-01-30T00:00:00Z", "checkCount": 7, "id": "c9015237-f6bb-4130-be5d-86b19d25e1d1", "companyId": "12018698", "type": "payperiod", "_rid": "NmJkAKiCbEezHQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEezHQAAAAAAAA==/", "_etag": "\"97001663-0000-0100-0000-686fd17c0000\"", "_attachments": "attachments/", "_ts": 1752158588}, {"payPeriodId": "1030069586597516", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-01-26T00:00:00Z", "endDate": "2025-02-01T00:00:00Z", "submitByDate": "2025-02-04T00:00:00Z", "checkDate": "2025-02-06T00:00:00Z", "checkCount": 7, "id": "b577ebec-feed-4c27-a766-a063fefaece8", "companyId": "12018698", "type": "payperiod", "_rid": "NmJkAKiCbEe0HQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEe0HQAAAAAAAA==/", "_etag": "\"97001963-0000-0100-0000-686fd17c0000\"", "_attachments": "attachments/", "_ts": 1752158588}, {"payPeriodId": "1030069722900078", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-02-02T00:00:00Z", "endDate": "2025-02-08T00:00:00Z", "submitByDate": "2025-02-11T00:00:00Z", "checkDate": "2025-02-13T00:00:00Z", "checkCount": 6, "id": "d21f9a69-9855-4f3c-ae83-2ab6f78b5ef7", "companyId": "12018698", "type": "payperiod", "_rid": "NmJkAKiCbEe1HQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEe1HQAAAAAAAA==/", "_etag": "\"97001c63-0000-0100-0000-686fd17c0000\"", "_attachments": "attachments/", "_ts": 1752158588}, {"payPeriodId": "1030069825730631", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-02-09T00:00:00Z", "endDate": "2025-02-15T00:00:00Z", "submitByDate": "2025-02-18T00:00:00Z", "checkDate": "2025-02-20T00:00:00Z", "checkCount": 7, "id": "17eae807-e235-40a2-89c1-ecbe8a77d9d2", "companyId": "12018698", "type": "payperiod", "_rid": "NmJkAKiCbEe2HQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEe2HQAAAAAAAA==/", "_etag": "\"97002063-0000-0100-0000-686fd17c0000\"", "_attachments": "attachments/", "_ts": 1752158588}, {"payPeriodId": "1030069981753573", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-02-16T00:00:00Z", "endDate": "2025-02-22T00:00:00Z", "submitByDate": "2025-02-25T00:00:00Z", "checkDate": "2025-02-27T00:00:00Z", "checkCount": 7, "id": "426ec6c0-9030-4811-b631-78b92d507f14", "companyId": "12018698", "type": "payperiod", "_rid": "NmJkAKiCbEe3HQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEe3HQAAAAAAAA==/", "_etag": "\"97002363-0000-0100-0000-686fd17c0000\"", "_attachments": "attachments/", "_ts": 1752158588}, {"payPeriodId": "1030070091941341", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-02-23T00:00:00Z", "endDate": "2025-03-01T00:00:00Z", "submitByDate": "2025-03-04T00:00:00Z", "checkDate": "2025-03-06T00:00:00Z", "checkCount": 7, "id": "d669c2be-76de-4ed2-9827-82f8f7d6c760", "companyId": "12018698", "type": "payperiod", "_rid": "NmJkAKiCbEe4HQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEe4HQAAAAAAAA==/", "_etag": "\"97002563-0000-0100-0000-686fd17c0000\"", "_attachments": "attachments/", "_ts": 1752158588}, {"payPeriodId": "1030070255413473", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-03-01T00:00:00Z", "endDate": "2025-03-08T00:00:00Z", "submitByDate": "2025-03-11T00:00:00Z", "checkDate": "2025-03-13T00:00:00Z", "checkCount": 7, "id": "ee5f650b-9142-483b-9a62-df91e6d369fb", "companyId": "12018698", "type": "payperiod", "_rid": "NmJkAKiCbEe5HQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEe5HQAAAAAAAA==/", "_etag": "\"97002663-0000-0100-0000-686fd17c0000\"", "_attachments": "attachments/", "_ts": 1752158588}, {"payPeriodId": "1030070397578847", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-03-09T00:00:00Z", "endDate": "2025-03-15T00:00:00Z", "submitByDate": "2025-03-18T00:00:00Z", "checkDate": "2025-03-20T00:00:00Z", "checkCount": 7, "id": "6b713305-82b6-443e-a54b-fb3b252f8067", "companyId": "12018698", "type": "payperiod", "_rid": "NmJkAKiCbEe6HQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEe6HQAAAAAAAA==/", "_etag": "\"97002b63-0000-0100-0000-686fd17c0000\"", "_attachments": "attachments/", "_ts": 1752158588}, {"payPeriodId": "1030070568165032", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-03-16T00:00:00Z", "endDate": "2025-03-22T00:00:00Z", "submitByDate": "2025-03-25T00:00:00Z", "checkDate": "2025-03-27T00:00:00Z", "checkCount": 7, "id": "49c69ed1-65f2-4f0c-924a-b18c84d37cae", "companyId": "12018698", "type": "payperiod", "_rid": "NmJkAKiCbEe7HQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEe7HQAAAAAAAA==/", "_etag": "\"97002f63-0000-0100-0000-686fd17c0000\"", "_attachments": "attachments/", "_ts": 1752158588}, {"payPeriodId": "1030070696433136", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-03-23T00:00:00Z", "endDate": "2025-03-29T00:00:00Z", "submitByDate": "2025-04-01T00:00:00Z", "checkDate": "2025-04-03T00:00:00Z", "checkCount": 0, "id": "2ddf0575-4341-4e82-81c4-3fcec9de338f", "companyId": "12018698", "type": "payperiod", "_rid": "NmJkAKiCbEe8HQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEe8HQAAAAAAAA==/", "_etag": "\"97003263-0000-0100-0000-686fd17c0000\"", "_attachments": "attachments/", "_ts": 1752158588}, {"payPeriodId": "1030070798560373", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-03-30T00:00:00Z", "endDate": "2025-04-05T00:00:00Z", "submitByDate": "2025-04-08T00:00:00Z", "checkDate": "2025-04-10T00:00:00Z", "checkCount": 0, "id": "7c0430a5-e5a5-4c32-8fb0-7a7a4fa85027", "companyId": "12018698", "type": "payperiod", "_rid": "NmJkAKiCbEe9HQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEe9HQAAAAAAAA==/", "_etag": "\"97003763-0000-0100-0000-686fd17c0000\"", "_attachments": "attachments/", "_ts": 1752158588}, {"payPeriodId": "1030070991696304", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-04-06T00:00:00Z", "endDate": "2025-04-12T00:00:00Z", "submitByDate": "2025-04-15T00:00:00Z", "checkDate": "2025-04-17T00:00:00Z", "checkCount": 0, "id": "d5fdac08-22bd-4582-b91c-c24d24ff1cf7", "companyId": "12018698", "type": "payperiod", "_rid": "NmJkAKiCbEe+HQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEe+HQAAAAAAAA==/", "_etag": "\"97003a63-0000-0100-0000-686fd17c0000\"", "_attachments": "attachments/", "_ts": 1752158588}, {"payPeriodId": "1030071111367978", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-04-13T00:00:00Z", "endDate": "2025-04-19T00:00:00Z", "submitByDate": "2025-04-22T00:00:00Z", "checkDate": "2025-04-24T00:00:00Z", "checkCount": 0, "id": "22c014e0-74e9-41d3-af97-5442b1f87ae5", "companyId": "12018698", "type": "payperiod", "_rid": "NmJkAKiCbEe-HQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEe-HQAAAAAAAA==/", "_etag": "\"97003d63-0000-0100-0000-686fd17d0000\"", "_attachments": "attachments/", "_ts": 1752158589}, {"payPeriodId": "1030071243704419", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-04-20T00:00:00Z", "endDate": "2025-04-26T00:00:00Z", "submitByDate": "2025-04-29T00:00:00Z", "checkDate": "2025-05-01T00:00:00Z", "checkCount": 0, "id": "a52aa94a-ca42-40c2-9a27-b76d569255c2", "companyId": "12018698", "type": "payperiod", "_rid": "NmJkAKiCbEfAHQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfAHQAAAAAAAA==/", "_etag": "\"97004063-0000-0100-0000-686fd17d0000\"", "_attachments": "attachments/", "_ts": 1752158589}, {"payPeriodId": "1030071384105993", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-04-27T00:00:00Z", "endDate": "2025-05-03T00:00:00Z", "submitByDate": "2025-05-06T00:00:00Z", "checkDate": "2025-05-08T00:00:00Z", "checkCount": 0, "id": "7231f4cd-e921-432d-8707-c718a8166048", "companyId": "12018698", "type": "payperiod", "_rid": "NmJkAKiCbEfBHQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfBHQAAAAAAAA==/", "_etag": "\"97004163-0000-0100-0000-686fd17d0000\"", "_attachments": "attachments/", "_ts": 1752158589}, {"payPeriodId": "1030071548056901", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-05-04T00:00:00Z", "endDate": "2025-05-10T00:00:00Z", "submitByDate": "2025-05-13T00:00:00Z", "checkDate": "2025-05-15T00:00:00Z", "checkCount": 0, "id": "ec42109f-d861-49d8-b0d0-27bdd755acdc", "companyId": "12018698", "type": "payperiod", "_rid": "NmJkAKiCbEfCHQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfCHQAAAAAAAA==/", "_etag": "\"97004463-0000-0100-0000-686fd17d0000\"", "_attachments": "attachments/", "_ts": 1752158589}, {"payPeriodId": "1030071657175338", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-05-11T00:00:00Z", "endDate": "2025-05-17T00:00:00Z", "submitByDate": "2025-05-20T00:00:00Z", "checkDate": "2025-05-22T00:00:00Z", "checkCount": 0, "id": "c0739368-22d0-42f2-b578-61b5e8d07877", "companyId": "12018698", "type": "payperiod", "_rid": "NmJkAKiCbEfDHQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfDHQAAAAAAAA==/", "_etag": "\"97004663-0000-0100-0000-686fd17d0000\"", "_attachments": "attachments/", "_ts": 1752158589}, {"payPeriodId": "1030071779116550", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-05-18T00:00:00Z", "endDate": "2025-05-24T00:00:00Z", "submitByDate": "2025-05-27T00:00:00Z", "checkDate": "2025-05-29T00:00:00Z", "checkCount": 0, "id": "3a6e857b-25c3-4d84-bf47-b44f9190d7f8", "companyId": "12018698", "type": "payperiod", "_rid": "NmJkAKiCbEfEHQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfEHQAAAAAAAA==/", "_etag": "\"97004963-0000-0100-0000-686fd17d0000\"", "_attachments": "attachments/", "_ts": 1752158589}, {"payPeriodId": "1030071915075191", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-05-25T00:00:00Z", "endDate": "2025-05-31T00:00:00Z", "submitByDate": "2025-06-03T00:00:00Z", "checkDate": "2025-06-05T00:00:00Z", "checkCount": 0, "id": "d6a7493e-7272-4d43-8f65-d5b2c1a1b831", "companyId": "12018698", "type": "payperiod", "_rid": "NmJkAKiCbEfFHQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfFHQAAAAAAAA==/", "_etag": "\"97004a63-0000-0100-0000-686fd17d0000\"", "_attachments": "attachments/", "_ts": 1752158589}, {"payPeriodId": "1030072053383496", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-06-01T00:00:00Z", "endDate": "2025-06-07T00:00:00Z", "submitByDate": "2025-06-10T00:00:00Z", "checkDate": "2025-06-12T00:00:00Z", "checkCount": 0, "id": "8c72726e-b5de-4410-8bab-4764634a5596", "companyId": "12018698", "type": "payperiod", "_rid": "NmJkAKiCbEfGHQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfGHQAAAAAAAA==/", "_etag": "\"97005063-0000-0100-0000-686fd17d0000\"", "_attachments": "attachments/", "_ts": 1752158589}, {"payPeriodId": "1030072211921610", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-06-08T00:00:00Z", "endDate": "2025-06-14T00:00:00Z", "submitByDate": "2025-06-19T00:00:00Z", "checkDate": "2025-06-20T00:00:00Z", "checkCount": 0, "id": "72e512c7-6323-49b3-962c-dc3f07222dff", "companyId": "12018698", "type": "payperiod", "_rid": "NmJkAKiCbEfHHQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfHHQAAAAAAAA==/", "_etag": "\"97005463-0000-0100-0000-686fd17d0000\"", "_attachments": "attachments/", "_ts": 1752158589}, {"payPeriodId": "1030072332618629", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-06-15T00:00:00Z", "endDate": "2025-06-21T00:00:00Z", "submitByDate": "2025-06-24T00:00:00Z", "checkDate": "2025-06-26T00:00:00Z", "checkCount": 0, "id": "052da9f5-2d42-4adc-b37d-0618c609f69a", "companyId": "12018698", "type": "payperiod", "_rid": "NmJkAKiCbEfIHQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfIHQAAAAAAAA==/", "_etag": "\"97005863-0000-0100-0000-686fd17d0000\"", "_attachments": "attachments/", "_ts": 1752158589}, {"payPeriodId": "1030072481049989", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-06-22T00:00:00Z", "endDate": "2025-06-28T00:00:00Z", "submitByDate": "2025-07-01T00:00:00Z", "checkDate": "2025-07-03T00:00:00Z", "checkCount": 0, "id": "17bdd7c9-bcf2-46f7-b63e-ee275adfbf4e", "companyId": "12018698", "type": "payperiod", "_rid": "NmJkAKiCbEfJHQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfJHQAAAAAAAA==/", "_etag": "\"97005f63-0000-0100-0000-686fd17d0000\"", "_attachments": "attachments/", "_ts": 1752158589}, {"payPeriodId": "1030072614763068", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-06-29T00:00:00Z", "endDate": "2025-07-05T00:00:00Z", "submitByDate": "2025-07-08T00:00:00Z", "checkDate": "2025-07-10T00:00:00Z", "checkCount": 0, "id": "f29143dd-9e94-4a9a-97f7-cf141ee0f913", "companyId": "12018698", "type": "payperiod", "_rid": "NmJkAKiCbEfKHQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfKHQAAAAAAAA==/", "_etag": "\"97006163-0000-0100-0000-686fd17d0000\"", "_attachments": "attachments/", "_ts": 1752158589}, {"payPeriodId": "1030072741765400", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-07-06T00:00:00Z", "endDate": "2025-07-12T00:00:00Z", "submitByDate": "2025-07-15T00:00:00Z", "checkDate": "2025-07-17T00:00:00Z", "checkCount": 0, "id": "1fca8e37-3ae1-4099-905b-3d40ed6589b2", "companyId": "12018698", "type": "payperiod", "_rid": "NmJkAKiCbEfLHQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfLHQAAAAAAAA==/", "_etag": "\"97006363-0000-0100-0000-686fd17d0000\"", "_attachments": "attachments/", "_ts": 1752158589}, {"payPeriodId": "1030072894706317", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-07-13T00:00:00Z", "endDate": "2025-07-19T00:00:00Z", "submitByDate": "2025-07-22T00:00:00Z", "checkDate": "2025-07-24T00:00:00Z", "checkCount": 0, "id": "d7997ecf-acb1-4703-87b5-ffe4418af40b", "companyId": "12018698", "type": "payperiod", "_rid": "NmJkAKiCbEfMHQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfMHQAAAAAAAA==/", "_etag": "\"97006763-0000-0100-0000-686fd17d0000\"", "_attachments": "attachments/", "_ts": 1752158589}, {"payPeriodId": "1030073007360331", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-07-20T00:00:00Z", "endDate": "2025-07-26T00:00:00Z", "submitByDate": "2025-07-29T00:00:00Z", "checkDate": "2025-07-31T00:00:00Z", "checkCount": 0, "id": "57657d6b-91f1-4d5f-b5b8-64826d9ba30a", "companyId": "12018698", "type": "payperiod", "_rid": "NmJkAKiCbEfNHQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfNHQAAAAAAAA==/", "_etag": "\"97006a63-0000-0100-0000-686fd17e0000\"", "_attachments": "attachments/", "_ts": 1752158590}, {"payPeriodId": "1030073126476088", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-07-27T00:00:00Z", "endDate": "2025-08-02T00:00:00Z", "submitByDate": "2025-08-05T00:00:00Z", "checkDate": "2025-08-07T00:00:00Z", "checkCount": 0, "id": "c2fa66a3-988c-4af5-bad3-32e4735dec86", "companyId": "12018698", "type": "payperiod", "_rid": "NmJkAKiCbEfOHQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfOHQAAAAAAAA==/", "_etag": "\"97006d63-0000-0100-0000-686fd17e0000\"", "_attachments": "attachments/", "_ts": 1752158590}, {"payPeriodId": "1030073279784328", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-08-03T00:00:00Z", "endDate": "2025-08-09T00:00:00Z", "submitByDate": "2025-08-12T00:00:00Z", "checkDate": "2025-08-14T00:00:00Z", "checkCount": 0, "id": "22d2bf59-c2f8-410e-95a3-fdc66ea9dbff", "companyId": "12018698", "type": "payperiod", "_rid": "NmJkAKiCbEfPHQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfPHQAAAAAAAA==/", "_etag": "\"97007263-0000-0100-0000-686fd17e0000\"", "_attachments": "attachments/", "_ts": 1752158590}, {"payPeriodId": "1030073421073373", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-08-10T00:00:00Z", "endDate": "2025-08-16T00:00:00Z", "submitByDate": "2025-08-19T00:00:00Z", "checkDate": "2025-08-21T00:00:00Z", "checkCount": 0, "id": "329d86cb-7680-4a4a-8d7a-4ab0a1f4a2bc", "companyId": "12018698", "type": "payperiod", "_rid": "NmJkAKiCbEfQHQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfQHQAAAAAAAA==/", "_etag": "\"97007463-0000-0100-0000-686fd17e0000\"", "_attachments": "attachments/", "_ts": 1752158590}, {"payPeriodId": "1030073555123836", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-08-17T00:00:00Z", "endDate": "2025-08-23T00:00:00Z", "submitByDate": "2025-08-26T00:00:00Z", "checkDate": "2025-08-28T00:00:00Z", "checkCount": 0, "id": "5fa7016d-8c2d-4537-ba9a-783a5d2eeea1", "companyId": "12018698", "type": "payperiod", "_rid": "NmJkAKiCbEfRHQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfRHQAAAAAAAA==/", "_etag": "\"97007763-0000-0100-0000-686fd17e0000\"", "_attachments": "attachments/", "_ts": 1752158590}, {"payPeriodId": "1030073717648310", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-08-24T00:00:00Z", "endDate": "2025-08-30T00:00:00Z", "submitByDate": "2025-09-02T00:00:00Z", "checkDate": "2025-09-04T00:00:00Z", "checkCount": 0, "id": "d95ecd41-dc29-4834-afec-36b648d48cec", "companyId": "12018698", "type": "payperiod", "_rid": "NmJkAKiCbEfSHQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfSHQAAAAAAAA==/", "_etag": "\"97007c63-0000-0100-0000-686fd17e0000\"", "_attachments": "attachments/", "_ts": 1752158590}, {"payPeriodId": "1030073858016362", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-08-31T00:00:00Z", "endDate": "2025-09-06T00:00:00Z", "submitByDate": "2025-09-09T00:00:00Z", "checkDate": "2025-09-11T00:00:00Z", "checkCount": 0, "id": "f663f86e-0c8a-452c-9a03-1fc6404c7595", "companyId": "12018698", "type": "payperiod", "_rid": "NmJkAKiCbEfTHQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfTHQAAAAAAAA==/", "_etag": "\"97008263-0000-0100-0000-686fd17e0000\"", "_attachments": "attachments/", "_ts": 1752158590}, {"payPeriodId": "1030073986451466", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-09-07T00:00:00Z", "endDate": "2025-09-13T00:00:00Z", "submitByDate": "2025-09-16T00:00:00Z", "checkDate": "2025-09-18T00:00:00Z", "checkCount": 0, "id": "bed8b967-8411-42ca-bfda-f365ea57600a", "companyId": "12018698", "type": "payperiod", "_rid": "NmJkAKiCbEfUHQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfUHQAAAAAAAA==/", "_etag": "\"97008663-0000-0100-0000-686fd17e0000\"", "_attachments": "attachments/", "_ts": 1752158590}, {"payPeriodId": "1030074138105803", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-09-14T00:00:00Z", "endDate": "2025-09-20T00:00:00Z", "submitByDate": "2025-09-23T00:00:00Z", "checkDate": "2025-09-25T00:00:00Z", "checkCount": 0, "id": "4d44b3a9-90f7-485d-92ce-1ace2c5c65b9", "companyId": "12018698", "type": "payperiod", "_rid": "NmJkAKiCbEfVHQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfVHQAAAAAAAA==/", "_etag": "\"97008763-0000-0100-0000-686fd17e0000\"", "_attachments": "attachments/", "_ts": 1752158590}, {"payPeriodId": "1030074264005751", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-09-21T00:00:00Z", "endDate": "2025-09-27T00:00:00Z", "submitByDate": "2025-09-30T00:00:00Z", "checkDate": "2025-10-02T00:00:00Z", "checkCount": 0, "id": "e73efb9d-c4f4-4b27-88e1-87bc82765455", "companyId": "12018698", "type": "payperiod", "_rid": "NmJkAKiCbEfWHQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfWHQAAAAAAAA==/", "_etag": "\"97009163-0000-0100-0000-686fd17e0000\"", "_attachments": "attachments/", "_ts": 1752158590}, {"payPeriodId": "1030074450668925", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-09-28T00:00:00Z", "endDate": "2025-10-04T00:00:00Z", "submitByDate": "2025-10-07T00:00:00Z", "checkDate": "2025-10-09T00:00:00Z", "checkCount": 0, "id": "ff02a252-45f8-4c0e-a7ae-f958b5b00dfe", "companyId": "12018698", "type": "payperiod", "_rid": "NmJkAKiCbEfXHQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfXHQAAAAAAAA==/", "_etag": "\"97009463-0000-0100-0000-686fd17e0000\"", "_attachments": "attachments/", "_ts": 1752158590}, {"payPeriodId": "1030069029932861", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2024-12-29T00:00:00Z", "endDate": "2025-01-04T00:00:00Z", "submitByDate": "2025-01-07T00:00:00Z", "checkDate": "2025-01-09T00:00:00Z", "checkCount": 5, "id": "7879e68d-75bc-44e7-a17e-b3bd0e16a6d5", "companyId": "12018698", "type": "payperiod", "_rid": "NmJkAKiCbEf0HQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf0HQAAAAAAAA==/", "_etag": "\"9700fd63-0000-0100-0000-686fd1800000\"", "_attachments": "attachments/", "_ts": 1752158592}, {"payPeriodId": "1030069156088165", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-01-05T00:00:00Z", "endDate": "2025-01-11T00:00:00Z", "submitByDate": "2025-01-14T00:00:00Z", "checkDate": "2025-01-16T00:00:00Z", "checkCount": 6, "id": "fd514ead-fc37-4ba0-9369-44a30f594fd6", "companyId": "12018698", "type": "payperiod", "_rid": "NmJkAKiCbEf1HQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf1HQAAAAAAAA==/", "_etag": "\"97000064-0000-0100-0000-686fd1800000\"", "_attachments": "attachments/", "_ts": 1752158592}, {"payPeriodId": "1030069286625805", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-01-12T00:00:00Z", "endDate": "2025-01-18T00:00:00Z", "submitByDate": "2025-01-21T00:00:00Z", "checkDate": "2025-01-23T00:00:00Z", "checkCount": 6, "id": "26973868-4eee-48bc-affe-76cc8c9f6ef4", "companyId": "12018698", "type": "payperiod", "_rid": "NmJkAKiCbEf2HQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf2HQAAAAAAAA==/", "_etag": "\"97000564-0000-0100-0000-686fd1810000\"", "_attachments": "attachments/", "_ts": 1752158593}, {"payPeriodId": "1030069438118528", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-01-19T00:00:00Z", "endDate": "2025-01-25T00:00:00Z", "submitByDate": "2025-01-28T00:00:00Z", "checkDate": "2025-01-30T00:00:00Z", "checkCount": 7, "id": "605c4bd3-708e-4056-a864-509e0324f4a1", "companyId": "12018698", "type": "payperiod", "_rid": "NmJkAKiCbEf3HQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf3HQAAAAAAAA==/", "_etag": "\"97000864-0000-0100-0000-686fd1810000\"", "_attachments": "attachments/", "_ts": 1752158593}, {"payPeriodId": "1030069586597516", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-01-26T00:00:00Z", "endDate": "2025-02-01T00:00:00Z", "submitByDate": "2025-02-04T00:00:00Z", "checkDate": "2025-02-06T00:00:00Z", "checkCount": 7, "id": "9dcfb66c-24d5-4187-bf18-762d9068795d", "companyId": "12018698", "type": "payperiod", "_rid": "NmJkAKiCbEf4HQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf4HQAAAAAAAA==/", "_etag": "\"97001164-0000-0100-0000-686fd1810000\"", "_attachments": "attachments/", "_ts": 1752158593}, {"payPeriodId": "1030069722900078", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-02-02T00:00:00Z", "endDate": "2025-02-08T00:00:00Z", "submitByDate": "2025-02-11T00:00:00Z", "checkDate": "2025-02-13T00:00:00Z", "checkCount": 6, "id": "ba2adf06-bee6-4765-a50b-dc89337e1d80", "companyId": "12018698", "type": "payperiod", "_rid": "NmJkAKiCbEf5HQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf5HQAAAAAAAA==/", "_etag": "\"97001764-0000-0100-0000-686fd1810000\"", "_attachments": "attachments/", "_ts": 1752158593}, {"payPeriodId": "1030069825730631", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-02-09T00:00:00Z", "endDate": "2025-02-15T00:00:00Z", "submitByDate": "2025-02-18T00:00:00Z", "checkDate": "2025-02-20T00:00:00Z", "checkCount": 7, "id": "902f04c0-640e-498e-b751-f55cc2de88df", "companyId": "12018698", "type": "payperiod", "_rid": "NmJkAKiCbEf6HQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf6HQAAAAAAAA==/", "_etag": "\"97001864-0000-0100-0000-686fd1810000\"", "_attachments": "attachments/", "_ts": 1752158593}, {"payPeriodId": "1030069981753573", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-02-16T00:00:00Z", "endDate": "2025-02-22T00:00:00Z", "submitByDate": "2025-02-25T00:00:00Z", "checkDate": "2025-02-27T00:00:00Z", "checkCount": 7, "id": "ef6fe157-1e97-4c1e-a6b1-14a5b8588a02", "companyId": "12018698", "type": "payperiod", "_rid": "NmJkAKiCbEf7HQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf7HQAAAAAAAA==/", "_etag": "\"97001f64-0000-0100-0000-686fd1810000\"", "_attachments": "attachments/", "_ts": 1752158593}, {"payPeriodId": "1030070091941341", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-02-23T00:00:00Z", "endDate": "2025-03-01T00:00:00Z", "submitByDate": "2025-03-04T00:00:00Z", "checkDate": "2025-03-06T00:00:00Z", "checkCount": 7, "id": "e2aa6858-93e0-496b-ab4d-c029901e453d", "companyId": "12018698", "type": "payperiod", "_rid": "NmJkAKiCbEf8HQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf8HQAAAAAAAA==/", "_etag": "\"97002364-0000-0100-0000-686fd1810000\"", "_attachments": "attachments/", "_ts": 1752158593}, {"payPeriodId": "1030070255413473", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-03-01T00:00:00Z", "endDate": "2025-03-08T00:00:00Z", "submitByDate": "2025-03-11T00:00:00Z", "checkDate": "2025-03-13T00:00:00Z", "checkCount": 7, "id": "195d9139-0e6e-4de1-b0f0-6035fd3fcf3f", "companyId": "12018698", "type": "payperiod", "_rid": "NmJkAKiCbEf9HQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf9HQAAAAAAAA==/", "_etag": "\"97002664-0000-0100-0000-686fd1810000\"", "_attachments": "attachments/", "_ts": 1752158593}, {"payPeriodId": "1030070397578847", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-03-09T00:00:00Z", "endDate": "2025-03-15T00:00:00Z", "submitByDate": "2025-03-18T00:00:00Z", "checkDate": "2025-03-20T00:00:00Z", "checkCount": 7, "id": "bad4d50f-8aa3-4133-ace7-85e32126abfd", "companyId": "12018698", "type": "payperiod", "_rid": "NmJkAKiCbEf+HQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf+HQAAAAAAAA==/", "_etag": "\"97002a64-0000-0100-0000-686fd1810000\"", "_attachments": "attachments/", "_ts": 1752158593}, {"payPeriodId": "1030070568165032", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-03-16T00:00:00Z", "endDate": "2025-03-22T00:00:00Z", "submitByDate": "2025-03-25T00:00:00Z", "checkDate": "2025-03-27T00:00:00Z", "checkCount": 7, "id": "513b66f1-cfe0-41f8-873b-ce7d540b8949", "companyId": "12018698", "type": "payperiod", "_rid": "NmJkAKiCbEf-HQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf-HQAAAAAAAA==/", "_etag": "\"97002c64-0000-0100-0000-686fd1810000\"", "_attachments": "attachments/", "_ts": 1752158593}, {"payPeriodId": "1030070696433136", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-03-23T00:00:00Z", "endDate": "2025-03-29T00:00:00Z", "submitByDate": "2025-04-01T00:00:00Z", "checkDate": "2025-04-03T00:00:00Z", "checkCount": 7, "id": "11323788-2e4e-4c98-be2f-6b2cbfcb132d", "companyId": "12018698", "type": "payperiod", "_rid": "NmJkAKiCbEcAHgAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcAHgAAAAAAAA==/", "_etag": "\"97002e64-0000-0100-0000-686fd1810000\"", "_attachments": "attachments/", "_ts": 1752158593}, {"payPeriodId": "1030070798560373", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-03-30T00:00:00Z", "endDate": "2025-04-05T00:00:00Z", "submitByDate": "2025-04-08T00:00:00Z", "checkDate": "2025-04-10T00:00:00Z", "checkCount": 7, "id": "cbfda15b-79e4-48a3-961d-4e751984b7b5", "companyId": "12018698", "type": "payperiod", "_rid": "NmJkAKiCbEcBHgAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcBHgAAAAAAAA==/", "_etag": "\"97003064-0000-0100-0000-686fd1810000\"", "_attachments": "attachments/", "_ts": 1752158593}, {"payPeriodId": "1030070991696304", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-04-06T00:00:00Z", "endDate": "2025-04-12T00:00:00Z", "submitByDate": "2025-04-15T00:00:00Z", "checkDate": "2025-04-17T00:00:00Z", "checkCount": 7, "id": "c95e8af2-630b-4b9c-a297-737a499ba168", "companyId": "12018698", "type": "payperiod", "_rid": "NmJkAKiCbEcCHgAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcCHgAAAAAAAA==/", "_etag": "\"97003464-0000-0100-0000-686fd1810000\"", "_attachments": "attachments/", "_ts": 1752158593}, {"payPeriodId": "1030071111367978", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-04-13T00:00:00Z", "endDate": "2025-04-19T00:00:00Z", "submitByDate": "2025-04-22T00:00:00Z", "checkDate": "2025-04-24T00:00:00Z", "checkCount": 6, "id": "b34f59ba-59c3-4715-b7ac-6aa2cdd0e08a", "companyId": "12018698", "type": "payperiod", "_rid": "NmJkAKiCbEcDHgAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcDHgAAAAAAAA==/", "_etag": "\"97003864-0000-0100-0000-686fd1810000\"", "_attachments": "attachments/", "_ts": 1752158593}, {"payPeriodId": "1030071243704419", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-04-20T00:00:00Z", "endDate": "2025-04-26T00:00:00Z", "submitByDate": "2025-04-29T00:00:00Z", "checkDate": "2025-05-01T00:00:00Z", "checkCount": 6, "id": "7e82fa97-bfa1-49d8-8199-b20935979f95", "companyId": "12018698", "type": "payperiod", "_rid": "NmJkAKiCbEcEHgAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcEHgAAAAAAAA==/", "_etag": "\"97003a64-0000-0100-0000-686fd1820000\"", "_attachments": "attachments/", "_ts": 1752158594}, {"payPeriodId": "1030071384105993", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-04-27T00:00:00Z", "endDate": "2025-05-03T00:00:00Z", "submitByDate": "2025-05-06T00:00:00Z", "checkDate": "2025-05-08T00:00:00Z", "checkCount": 7, "id": "81a66334-4bf8-4fb1-9cf4-45fd7333eda7", "companyId": "12018698", "type": "payperiod", "_rid": "NmJkAKiCbEcFHgAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcFHgAAAAAAAA==/", "_etag": "\"97003c64-0000-0100-0000-686fd1820000\"", "_attachments": "attachments/", "_ts": 1752158594}, {"payPeriodId": "1030071548056901", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-05-04T00:00:00Z", "endDate": "2025-05-10T00:00:00Z", "submitByDate": "2025-05-13T00:00:00Z", "checkDate": "2025-05-15T00:00:00Z", "checkCount": 7, "id": "731131ee-d7d4-4405-a6b9-8ddbb0c39b27", "companyId": "12018698", "type": "payperiod", "_rid": "NmJkAKiCbEcGHgAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcGHgAAAAAAAA==/", "_etag": "\"97004064-0000-0100-0000-686fd1820000\"", "_attachments": "attachments/", "_ts": 1752158594}, {"payPeriodId": "1030071657175338", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-05-11T00:00:00Z", "endDate": "2025-05-17T00:00:00Z", "submitByDate": "2025-05-20T00:00:00Z", "checkDate": "2025-05-22T00:00:00Z", "checkCount": 6, "id": "c1757896-0d7c-4db5-9daf-a2725b06c526", "companyId": "12018698", "type": "payperiod", "_rid": "NmJkAKiCbEcHHgAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcHHgAAAAAAAA==/", "_etag": "\"97004364-0000-0100-0000-686fd1820000\"", "_attachments": "attachments/", "_ts": 1752158594}, {"payPeriodId": "1030071779116550", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-05-18T00:00:00Z", "endDate": "2025-05-24T00:00:00Z", "submitByDate": "2025-05-27T00:00:00Z", "checkDate": "2025-05-29T00:00:00Z", "checkCount": 7, "id": "2bf09457-18f9-473c-ac20-9ef0157692a8", "companyId": "12018698", "type": "payperiod", "_rid": "NmJkAKiCbEcIHgAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcIHgAAAAAAAA==/", "_etag": "\"97004864-0000-0100-0000-686fd1820000\"", "_attachments": "attachments/", "_ts": 1752158594}, {"payPeriodId": "1030071915075191", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-05-25T00:00:00Z", "endDate": "2025-05-31T00:00:00Z", "submitByDate": "2025-06-03T00:00:00Z", "checkDate": "2025-06-05T00:00:00Z", "checkCount": 6, "id": "45cd27d4-91d1-44cc-a8cc-ce35de75e11a", "companyId": "12018698", "type": "payperiod", "_rid": "NmJkAKiCbEcJHgAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcJHgAAAAAAAA==/", "_etag": "\"97004d64-0000-0100-0000-686fd1820000\"", "_attachments": "attachments/", "_ts": 1752158594}, {"payPeriodId": "1030072053383496", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-06-01T00:00:00Z", "endDate": "2025-06-07T00:00:00Z", "submitByDate": "2025-06-10T00:00:00Z", "checkDate": "2025-06-12T00:00:00Z", "checkCount": 6, "id": "3d97016b-4bfb-46d7-a36b-d92f1778de1c", "companyId": "12018698", "type": "payperiod", "_rid": "NmJkAKiCbEcKHgAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcKHgAAAAAAAA==/", "_etag": "\"97004f64-0000-0100-0000-686fd1820000\"", "_attachments": "attachments/", "_ts": 1752158594}, {"payPeriodId": "1030072211921610", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-06-08T00:00:00Z", "endDate": "2025-06-14T00:00:00Z", "submitByDate": "2025-06-19T00:00:00Z", "checkDate": "2025-06-20T00:00:00Z", "checkCount": 7, "id": "38f7f035-332a-4c94-91b1-5092fa5a96c7", "companyId": "12018698", "type": "payperiod", "_rid": "NmJkAKiCbEcLHgAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcLHgAAAAAAAA==/", "_etag": "\"97005264-0000-0100-0000-686fd1820000\"", "_attachments": "attachments/", "_ts": 1752158594}, {"payPeriodId": "1030072332618629", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-06-15T00:00:00Z", "endDate": "2025-06-21T00:00:00Z", "submitByDate": "2025-06-24T00:00:00Z", "checkDate": "2025-06-26T00:00:00Z", "checkCount": 6, "id": "92cea857-3771-4d65-8266-dd566098b195", "companyId": "12018698", "type": "payperiod", "_rid": "NmJkAKiCbEcMHgAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcMHgAAAAAAAA==/", "_etag": "\"97005464-0000-0100-0000-686fd1820000\"", "_attachments": "attachments/", "_ts": 1752158594}, {"payPeriodId": "1030072481049989", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-06-22T00:00:00Z", "endDate": "2025-06-28T00:00:00Z", "submitByDate": "2025-07-01T00:00:00Z", "checkDate": "2025-07-03T00:00:00Z", "checkCount": 6, "id": "fdaff063-4513-4576-8ee8-694e4be3d902", "companyId": "12018698", "type": "payperiod", "_rid": "NmJkAKiCbEcNHgAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcNHgAAAAAAAA==/", "_etag": "\"97005764-0000-0100-0000-686fd1820000\"", "_attachments": "attachments/", "_ts": 1752158594}, {"payPeriodId": "1030072614763068", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-06-29T00:00:00Z", "endDate": "2025-07-05T00:00:00Z", "submitByDate": "2025-07-08T00:00:00Z", "checkDate": "2025-07-10T00:00:00Z", "checkCount": 0, "id": "fa664d14-fe54-44af-990a-17cf5418c988", "companyId": "12018698", "type": "payperiod", "_rid": "NmJkAKiCbEcOHgAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcOHgAAAAAAAA==/", "_etag": "\"97005a64-0000-0100-0000-686fd1820000\"", "_attachments": "attachments/", "_ts": 1752158594}, {"payPeriodId": "1030072741765400", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-07-06T00:00:00Z", "endDate": "2025-07-12T00:00:00Z", "submitByDate": "2025-07-15T00:00:00Z", "checkDate": "2025-07-17T00:00:00Z", "checkCount": 0, "id": "1ce37455-8591-4450-8170-786f2331172f", "companyId": "12018698", "type": "payperiod", "_rid": "NmJkAKiCbEcPHgAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcPHgAAAAAAAA==/", "_etag": "\"97005c64-0000-0100-0000-686fd1820000\"", "_attachments": "attachments/", "_ts": 1752158594}, {"payPeriodId": "1030072894706317", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-07-13T00:00:00Z", "endDate": "2025-07-19T00:00:00Z", "submitByDate": "2025-07-22T00:00:00Z", "checkDate": "2025-07-24T00:00:00Z", "checkCount": 0, "id": "0303169a-2134-4a68-a19e-2b1c987e6609", "companyId": "12018698", "type": "payperiod", "_rid": "NmJkAKiCbEcQHgAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcQHgAAAAAAAA==/", "_etag": "\"97005d64-0000-0100-0000-686fd1820000\"", "_attachments": "attachments/", "_ts": 1752158594}, {"payPeriodId": "1030073007360331", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-07-20T00:00:00Z", "endDate": "2025-07-26T00:00:00Z", "submitByDate": "2025-07-29T00:00:00Z", "checkDate": "2025-07-31T00:00:00Z", "checkCount": 0, "id": "e13ef2c4-d7ea-4ceb-bf23-c3c75aa61442", "companyId": "12018698", "type": "payperiod", "_rid": "NmJkAKiCbEcRHgAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcRHgAAAAAAAA==/", "_etag": "\"97006264-0000-0100-0000-686fd1820000\"", "_attachments": "attachments/", "_ts": 1752158594}, {"payPeriodId": "1030073126476088", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-07-27T00:00:00Z", "endDate": "2025-08-02T00:00:00Z", "submitByDate": "2025-08-05T00:00:00Z", "checkDate": "2025-08-07T00:00:00Z", "checkCount": 0, "id": "6c8cbd34-65cb-45c2-a095-0aa86362e06d", "companyId": "12018698", "type": "payperiod", "_rid": "NmJkAKiCbEcSHgAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcSHgAAAAAAAA==/", "_etag": "\"97006764-0000-0100-0000-686fd1830000\"", "_attachments": "attachments/", "_ts": 1752158595}, {"payPeriodId": "1030073279784328", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-08-03T00:00:00Z", "endDate": "2025-08-09T00:00:00Z", "submitByDate": "2025-08-12T00:00:00Z", "checkDate": "2025-08-14T00:00:00Z", "checkCount": 0, "id": "0b4858e5-3d96-4bc0-8ee6-0c5726a91da8", "companyId": "12018698", "type": "payperiod", "_rid": "NmJkAKiCbEcTHgAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcTHgAAAAAAAA==/", "_etag": "\"97006a64-0000-0100-0000-686fd1830000\"", "_attachments": "attachments/", "_ts": 1752158595}, {"payPeriodId": "1030073421073373", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-08-10T00:00:00Z", "endDate": "2025-08-16T00:00:00Z", "submitByDate": "2025-08-19T00:00:00Z", "checkDate": "2025-08-21T00:00:00Z", "checkCount": 0, "id": "fa83ec7e-f55b-4854-bbeb-f1f7f5cb9d1c", "companyId": "12018698", "type": "payperiod", "_rid": "NmJkAKiCbEcUHgAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcUHgAAAAAAAA==/", "_etag": "\"97006f64-0000-0100-0000-686fd1830000\"", "_attachments": "attachments/", "_ts": 1752158595}, {"payPeriodId": "1030073555123836", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-08-17T00:00:00Z", "endDate": "2025-08-23T00:00:00Z", "submitByDate": "2025-08-26T00:00:00Z", "checkDate": "2025-08-28T00:00:00Z", "checkCount": 0, "id": "5c7afea5-8947-4950-b5c7-bd4677c18471", "companyId": "12018698", "type": "payperiod", "_rid": "NmJkAKiCbEcVHgAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcVHgAAAAAAAA==/", "_etag": "\"97007264-0000-0100-0000-686fd1830000\"", "_attachments": "attachments/", "_ts": 1752158595}, {"payPeriodId": "1030073717648310", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-08-24T00:00:00Z", "endDate": "2025-08-30T00:00:00Z", "submitByDate": "2025-09-02T00:00:00Z", "checkDate": "2025-09-04T00:00:00Z", "checkCount": 0, "id": "0a6bc29b-fcb6-4e42-ad40-fe7f6bc56459", "companyId": "12018698", "type": "payperiod", "_rid": "NmJkAKiCbEcWHgAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcWHgAAAAAAAA==/", "_etag": "\"97007564-0000-0100-0000-686fd1830000\"", "_attachments": "attachments/", "_ts": 1752158595}, {"payPeriodId": "1030073858016362", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-08-31T00:00:00Z", "endDate": "2025-09-06T00:00:00Z", "submitByDate": "2025-09-09T00:00:00Z", "checkDate": "2025-09-11T00:00:00Z", "checkCount": 0, "id": "6806d50e-3f01-4660-bac8-c869a255aa52", "companyId": "12018698", "type": "payperiod", "_rid": "NmJkAKiCbEcXHgAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcXHgAAAAAAAA==/", "_etag": "\"97007764-0000-0100-0000-686fd1830000\"", "_attachments": "attachments/", "_ts": 1752158595}, {"payPeriodId": "1030073986451466", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-09-07T00:00:00Z", "endDate": "2025-09-13T00:00:00Z", "submitByDate": "2025-09-16T00:00:00Z", "checkDate": "2025-09-18T00:00:00Z", "checkCount": 0, "id": "dba614c4-7972-4c41-a070-7f4d4df870e0", "companyId": "12018698", "type": "payperiod", "_rid": "NmJkAKiCbEcYHgAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcYHgAAAAAAAA==/", "_etag": "\"97007a64-0000-0100-0000-686fd1830000\"", "_attachments": "attachments/", "_ts": 1752158595}, {"payPeriodId": "1030074138105803", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-09-14T00:00:00Z", "endDate": "2025-09-20T00:00:00Z", "submitByDate": "2025-09-23T00:00:00Z", "checkDate": "2025-09-25T00:00:00Z", "checkCount": 0, "id": "86601ba3-c30a-4427-9980-db360fa85011", "companyId": "12018698", "type": "payperiod", "_rid": "NmJkAKiCbEcZHgAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcZHgAAAAAAAA==/", "_etag": "\"97007d64-0000-0100-0000-686fd1830000\"", "_attachments": "attachments/", "_ts": 1752158595}, {"payPeriodId": "1030074264005751", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-09-21T00:00:00Z", "endDate": "2025-09-27T00:00:00Z", "submitByDate": "2025-09-30T00:00:00Z", "checkDate": "2025-10-02T00:00:00Z", "checkCount": 0, "id": "34e28ce2-c2f2-4498-b717-0fd138bf3b2a", "companyId": "12018698", "type": "payperiod", "_rid": "NmJkAKiCbEcaHgAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcaHgAAAAAAAA==/", "_etag": "\"97008164-0000-0100-0000-686fd1830000\"", "_attachments": "attachments/", "_ts": 1752158595}, {"payPeriodId": "1030074450668925", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-09-28T00:00:00Z", "endDate": "2025-10-04T00:00:00Z", "submitByDate": "2025-10-07T00:00:00Z", "checkDate": "2025-10-09T00:00:00Z", "checkCount": 0, "id": "7af70759-cc2b-497f-bd09-5a9c8ee32235", "companyId": "12018698", "type": "payperiod", "_rid": "NmJkAKiCbEcbHgAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcbHgAAAAAAAA==/", "_etag": "\"97008364-0000-0100-0000-686fd1830000\"", "_attachments": "attachments/", "_ts": 1752158595}, {"payPeriodId": "1030069029932861", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2024-12-29T00:00:00Z", "endDate": "2025-01-04T00:00:00Z", "submitByDate": "2025-01-07T00:00:00Z", "checkDate": "2025-01-09T00:00:00Z", "checkCount": 5, "id": "f92f39ad-0240-4608-b009-b5737a8f6c1a", "companyId": "12018698", "type": "payperiod", "_rid": "NmJkAKiCbEfdFgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfdFgEAAAAAAA==/", "_etag": "\"9d00699f-0000-0100-0000-686ff7d60000\"", "_attachments": "attachments/", "_ts": 1752168406}, {"payPeriodId": "1030069156088165", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-01-05T00:00:00Z", "endDate": "2025-01-11T00:00:00Z", "submitByDate": "2025-01-14T00:00:00Z", "checkDate": "2025-01-16T00:00:00Z", "checkCount": 6, "id": "8e5775b8-fe74-4e86-bde4-a5c022a74867", "companyId": "12018698", "type": "payperiod", "_rid": "NmJkAKiCbEfeFgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfeFgEAAAAAAA==/", "_etag": "\"9d006c9f-0000-0100-0000-686ff7d60000\"", "_attachments": "attachments/", "_ts": 1752168406}, {"payPeriodId": "1030069286625805", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-01-12T00:00:00Z", "endDate": "2025-01-18T00:00:00Z", "submitByDate": "2025-01-21T00:00:00Z", "checkDate": "2025-01-23T00:00:00Z", "checkCount": 6, "id": "ae3553a8-8595-4119-884f-7b3d85058636", "companyId": "12018698", "type": "payperiod", "_rid": "NmJkAKiCbEffFgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEffFgEAAAAAAA==/", "_etag": "\"9d006d9f-0000-0100-0000-686ff7d60000\"", "_attachments": "attachments/", "_ts": 1752168406}, {"payPeriodId": "1030069438118528", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-01-19T00:00:00Z", "endDate": "2025-01-25T00:00:00Z", "submitByDate": "2025-01-28T00:00:00Z", "checkDate": "2025-01-30T00:00:00Z", "checkCount": 7, "id": "d80734e3-7a24-4a26-bde8-fd9d376d14f4", "companyId": "12018698", "type": "payperiod", "_rid": "NmJkAKiCbEfgFgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfgFgEAAAAAAA==/", "_etag": "\"9d00739f-0000-0100-0000-686ff7d70000\"", "_attachments": "attachments/", "_ts": 1752168407}, {"payPeriodId": "1030069586597516", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-01-26T00:00:00Z", "endDate": "2025-02-01T00:00:00Z", "submitByDate": "2025-02-04T00:00:00Z", "checkDate": "2025-02-06T00:00:00Z", "checkCount": 7, "id": "f6a01b83-42ef-43b1-8445-1af2315c4913", "companyId": "12018698", "type": "payperiod", "_rid": "NmJkAKiCbEfhFgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfhFgEAAAAAAA==/", "_etag": "\"9d00749f-0000-0100-0000-686ff7d70000\"", "_attachments": "attachments/", "_ts": 1752168407}, {"payPeriodId": "1030069722900078", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-02-02T00:00:00Z", "endDate": "2025-02-08T00:00:00Z", "submitByDate": "2025-02-11T00:00:00Z", "checkDate": "2025-02-13T00:00:00Z", "checkCount": 6, "id": "19706593-e148-4045-af97-43204c27e2e9", "companyId": "12018698", "type": "payperiod", "_rid": "NmJkAKiCbEfiFgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfiFgEAAAAAAA==/", "_etag": "\"9d00769f-0000-0100-0000-686ff7d70000\"", "_attachments": "attachments/", "_ts": 1752168407}, {"payPeriodId": "1030069825730631", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-02-09T00:00:00Z", "endDate": "2025-02-15T00:00:00Z", "submitByDate": "2025-02-18T00:00:00Z", "checkDate": "2025-02-20T00:00:00Z", "checkCount": 7, "id": "e6cfa43d-8657-4ee5-ba35-a66bb116f3fa", "companyId": "12018698", "type": "payperiod", "_rid": "NmJkAKiCbEfjFgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfjFgEAAAAAAA==/", "_etag": "\"9d00779f-0000-0100-0000-686ff7d70000\"", "_attachments": "attachments/", "_ts": 1752168407}, {"payPeriodId": "1030069981753573", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-02-16T00:00:00Z", "endDate": "2025-02-22T00:00:00Z", "submitByDate": "2025-02-25T00:00:00Z", "checkDate": "2025-02-27T00:00:00Z", "checkCount": 7, "id": "71cae335-d738-47d6-aa98-5896c34524f9", "companyId": "12018698", "type": "payperiod", "_rid": "NmJkAKiCbEfkFgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfkFgEAAAAAAA==/", "_etag": "\"9d007a9f-0000-0100-0000-686ff7d70000\"", "_attachments": "attachments/", "_ts": 1752168407}, {"payPeriodId": "1030070091941341", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-02-23T00:00:00Z", "endDate": "2025-03-01T00:00:00Z", "submitByDate": "2025-03-04T00:00:00Z", "checkDate": "2025-03-06T00:00:00Z", "checkCount": 7, "id": "82f52dcc-6e1b-41f9-922b-d5bce666144e", "companyId": "12018698", "type": "payperiod", "_rid": "NmJkAKiCbEflFgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEflFgEAAAAAAA==/", "_etag": "\"9d007e9f-0000-0100-0000-686ff7d70000\"", "_attachments": "attachments/", "_ts": 1752168407}, {"payPeriodId": "1030070255413473", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-03-01T00:00:00Z", "endDate": "2025-03-08T00:00:00Z", "submitByDate": "2025-03-11T00:00:00Z", "checkDate": "2025-03-13T00:00:00Z", "checkCount": 7, "id": "2e87beff-e551-4eba-b36c-0b0c1967551e", "companyId": "12018698", "type": "payperiod", "_rid": "NmJkAKiCbEfmFgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfmFgEAAAAAAA==/", "_etag": "\"9d00819f-0000-0100-0000-686ff7d70000\"", "_attachments": "attachments/", "_ts": 1752168407}, {"payPeriodId": "1030070397578847", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-03-09T00:00:00Z", "endDate": "2025-03-15T00:00:00Z", "submitByDate": "2025-03-18T00:00:00Z", "checkDate": "2025-03-20T00:00:00Z", "checkCount": 7, "id": "6221e5ef-ce3e-446d-8b84-c9fb99d15b75", "companyId": "12018698", "type": "payperiod", "_rid": "NmJkAKiCbEfnFgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfnFgEAAAAAAA==/", "_etag": "\"9d00849f-0000-0100-0000-686ff7d70000\"", "_attachments": "attachments/", "_ts": 1752168407}, {"payPeriodId": "1030070568165032", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-03-16T00:00:00Z", "endDate": "2025-03-22T00:00:00Z", "submitByDate": "2025-03-25T00:00:00Z", "checkDate": "2025-03-27T00:00:00Z", "checkCount": 7, "id": "01da239a-bed0-4302-8756-7f6dea948ad1", "companyId": "12018698", "type": "payperiod", "_rid": "NmJkAKiCbEfoFgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfoFgEAAAAAAA==/", "_etag": "\"9d00869f-0000-0100-0000-686ff7d70000\"", "_attachments": "attachments/", "_ts": 1752168407}, {"payPeriodId": "1030070696433136", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-03-23T00:00:00Z", "endDate": "2025-03-29T00:00:00Z", "submitByDate": "2025-04-01T00:00:00Z", "checkDate": "2025-04-03T00:00:00Z", "checkCount": 0, "id": "58eb8eb3-d5f1-4599-8e1c-8cb742ec423a", "companyId": "12018698", "type": "payperiod", "_rid": "NmJkAKiCbEfpFgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfpFgEAAAAAAA==/", "_etag": "\"9d00889f-0000-0100-0000-686ff7d70000\"", "_attachments": "attachments/", "_ts": 1752168407}, {"payPeriodId": "1030070798560373", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-03-30T00:00:00Z", "endDate": "2025-04-05T00:00:00Z", "submitByDate": "2025-04-08T00:00:00Z", "checkDate": "2025-04-10T00:00:00Z", "checkCount": 0, "id": "efcaf01f-d751-4de6-9487-ce4a7ac3d0c1", "companyId": "12018698", "type": "payperiod", "_rid": "NmJkAKiCbEfqFgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfqFgEAAAAAAA==/", "_etag": "\"9d00899f-0000-0100-0000-686ff7d70000\"", "_attachments": "attachments/", "_ts": 1752168407}, {"payPeriodId": "1030070991696304", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-04-06T00:00:00Z", "endDate": "2025-04-12T00:00:00Z", "submitByDate": "2025-04-15T00:00:00Z", "checkDate": "2025-04-17T00:00:00Z", "checkCount": 0, "id": "df2e532b-35ab-4b4a-84c5-1cb0fc53c5b2", "companyId": "12018698", "type": "payperiod", "_rid": "NmJkAKiCbEfrFgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfrFgEAAAAAAA==/", "_etag": "\"9d008d9f-0000-0100-0000-686ff7d70000\"", "_attachments": "attachments/", "_ts": 1752168407}, {"payPeriodId": "1030071111367978", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-04-13T00:00:00Z", "endDate": "2025-04-19T00:00:00Z", "submitByDate": "2025-04-22T00:00:00Z", "checkDate": "2025-04-24T00:00:00Z", "checkCount": 0, "id": "11a9b179-0cfc-484f-85b1-2dc6d4c5c7a8", "companyId": "12018698", "type": "payperiod", "_rid": "NmJkAKiCbEfsFgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfsFgEAAAAAAA==/", "_etag": "\"9d00919f-0000-0100-0000-686ff7d70000\"", "_attachments": "attachments/", "_ts": 1752168407}, {"payPeriodId": "1030071243704419", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-04-20T00:00:00Z", "endDate": "2025-04-26T00:00:00Z", "submitByDate": "2025-04-29T00:00:00Z", "checkDate": "2025-05-01T00:00:00Z", "checkCount": 0, "id": "d30d0ff3-56bc-4264-92f9-16134d20c921", "companyId": "12018698", "type": "payperiod", "_rid": "NmJkAKiCbEftFgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEftFgEAAAAAAA==/", "_etag": "\"9d00949f-0000-0100-0000-686ff7d80000\"", "_attachments": "attachments/", "_ts": 1752168408}, {"payPeriodId": "1030071384105993", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-04-27T00:00:00Z", "endDate": "2025-05-03T00:00:00Z", "submitByDate": "2025-05-06T00:00:00Z", "checkDate": "2025-05-08T00:00:00Z", "checkCount": 0, "id": "5ba4cd41-932b-4e3c-9cec-a14f384882af", "companyId": "12018698", "type": "payperiod", "_rid": "NmJkAKiCbEfuFgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfuFgEAAAAAAA==/", "_etag": "\"9d00989f-0000-0100-0000-686ff7d80000\"", "_attachments": "attachments/", "_ts": 1752168408}, {"payPeriodId": "1030071548056901", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-05-04T00:00:00Z", "endDate": "2025-05-10T00:00:00Z", "submitByDate": "2025-05-13T00:00:00Z", "checkDate": "2025-05-15T00:00:00Z", "checkCount": 0, "id": "aa796991-7f93-4209-ba97-67164f2a45eb", "companyId": "12018698", "type": "payperiod", "_rid": "NmJkAKiCbEfvFgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfvFgEAAAAAAA==/", "_etag": "\"9d009e9f-0000-0100-0000-686ff7d80000\"", "_attachments": "attachments/", "_ts": 1752168408}, {"payPeriodId": "1030071657175338", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-05-11T00:00:00Z", "endDate": "2025-05-17T00:00:00Z", "submitByDate": "2025-05-20T00:00:00Z", "checkDate": "2025-05-22T00:00:00Z", "checkCount": 0, "id": "1a5b7454-8b83-43e3-91d4-8072fe6964be", "companyId": "12018698", "type": "payperiod", "_rid": "NmJkAKiCbEfwFgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfwFgEAAAAAAA==/", "_etag": "\"9d00a29f-0000-0100-0000-686ff7d80000\"", "_attachments": "attachments/", "_ts": 1752168408}, {"payPeriodId": "1030071779116550", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-05-18T00:00:00Z", "endDate": "2025-05-24T00:00:00Z", "submitByDate": "2025-05-27T00:00:00Z", "checkDate": "2025-05-29T00:00:00Z", "checkCount": 0, "id": "8ddb5627-acfd-4008-8dcb-84611e11439d", "companyId": "12018698", "type": "payperiod", "_rid": "NmJkAKiCbEfxFgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfxFgEAAAAAAA==/", "_etag": "\"9d00a59f-0000-0100-0000-686ff7d80000\"", "_attachments": "attachments/", "_ts": 1752168408}, {"payPeriodId": "1030071915075191", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-05-25T00:00:00Z", "endDate": "2025-05-31T00:00:00Z", "submitByDate": "2025-06-03T00:00:00Z", "checkDate": "2025-06-05T00:00:00Z", "checkCount": 0, "id": "b47ef1c5-6a1d-4929-a71a-ba53d744748e", "companyId": "12018698", "type": "payperiod", "_rid": "NmJkAKiCbEfyFgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfyFgEAAAAAAA==/", "_etag": "\"9d00a89f-0000-0100-0000-686ff7d80000\"", "_attachments": "attachments/", "_ts": 1752168408}, {"payPeriodId": "1030072053383496", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-06-01T00:00:00Z", "endDate": "2025-06-07T00:00:00Z", "submitByDate": "2025-06-10T00:00:00Z", "checkDate": "2025-06-12T00:00:00Z", "checkCount": 0, "id": "69d33f24-af3f-4ebd-b18a-7988688bd107", "companyId": "12018698", "type": "payperiod", "_rid": "NmJkAKiCbEfzFgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfzFgEAAAAAAA==/", "_etag": "\"9d00a99f-0000-0100-0000-686ff7d80000\"", "_attachments": "attachments/", "_ts": 1752168408}, {"payPeriodId": "1030072211921610", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-06-08T00:00:00Z", "endDate": "2025-06-14T00:00:00Z", "submitByDate": "2025-06-19T00:00:00Z", "checkDate": "2025-06-20T00:00:00Z", "checkCount": 0, "id": "363d8bf8-0c54-4695-90a8-e39ccfcf27d0", "companyId": "12018698", "type": "payperiod", "_rid": "NmJkAKiCbEf0FgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf0FgEAAAAAAA==/", "_etag": "\"9d00ab9f-0000-0100-0000-686ff7d80000\"", "_attachments": "attachments/", "_ts": 1752168408}, {"payPeriodId": "1030072332618629", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-06-15T00:00:00Z", "endDate": "2025-06-21T00:00:00Z", "submitByDate": "2025-06-24T00:00:00Z", "checkDate": "2025-06-26T00:00:00Z", "checkCount": 0, "id": "ff1dc0c1-2c0a-4189-9695-18578004289e", "companyId": "12018698", "type": "payperiod", "_rid": "NmJkAKiCbEf1FgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf1FgEAAAAAAA==/", "_etag": "\"9d00ae9f-0000-0100-0000-686ff7d80000\"", "_attachments": "attachments/", "_ts": 1752168408}, {"payPeriodId": "1030072481049989", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-06-22T00:00:00Z", "endDate": "2025-06-28T00:00:00Z", "submitByDate": "2025-07-01T00:00:00Z", "checkDate": "2025-07-03T00:00:00Z", "checkCount": 0, "id": "60215e0a-0950-4071-b548-a792e751d7a8", "companyId": "12018698", "type": "payperiod", "_rid": "NmJkAKiCbEf2FgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf2FgEAAAAAAA==/", "_etag": "\"9d00b29f-0000-0100-0000-686ff7d80000\"", "_attachments": "attachments/", "_ts": 1752168408}, {"payPeriodId": "1030072614763068", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-06-29T00:00:00Z", "endDate": "2025-07-05T00:00:00Z", "submitByDate": "2025-07-08T00:00:00Z", "checkDate": "2025-07-10T00:00:00Z", "checkCount": 0, "id": "904d3a73-09ca-41ad-8930-df224f9ab220", "companyId": "12018698", "type": "payperiod", "_rid": "NmJkAKiCbEf3FgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf3FgEAAAAAAA==/", "_etag": "\"9d00b59f-0000-0100-0000-686ff7d80000\"", "_attachments": "attachments/", "_ts": 1752168408}, {"payPeriodId": "1030072741765400", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-07-06T00:00:00Z", "endDate": "2025-07-12T00:00:00Z", "submitByDate": "2025-07-15T00:00:00Z", "checkDate": "2025-07-17T00:00:00Z", "checkCount": 0, "id": "f0a5390e-522d-4207-bc51-d10df539891c", "companyId": "12018698", "type": "payperiod", "_rid": "NmJkAKiCbEf4FgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf4FgEAAAAAAA==/", "_etag": "\"9d00b89f-0000-0100-0000-686ff7d80000\"", "_attachments": "attachments/", "_ts": 1752168408}, {"payPeriodId": "1030072894706317", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-07-13T00:00:00Z", "endDate": "2025-07-19T00:00:00Z", "submitByDate": "2025-07-22T00:00:00Z", "checkDate": "2025-07-24T00:00:00Z", "checkCount": 0, "id": "3f1ac083-e27e-4b0b-ab77-5b6c6074c6f8", "companyId": "12018698", "type": "payperiod", "_rid": "NmJkAKiCbEf5FgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf5FgEAAAAAAA==/", "_etag": "\"9d00ba9f-0000-0100-0000-686ff7d80000\"", "_attachments": "attachments/", "_ts": 1752168408}, {"payPeriodId": "1030073007360331", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-07-20T00:00:00Z", "endDate": "2025-07-26T00:00:00Z", "submitByDate": "2025-07-29T00:00:00Z", "checkDate": "2025-07-31T00:00:00Z", "checkCount": 0, "id": "c3b82fc8-45c1-4554-bd91-56a1d9b4b30c", "companyId": "12018698", "type": "payperiod", "_rid": "NmJkAKiCbEf6FgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf6FgEAAAAAAA==/", "_etag": "\"9d00be9f-0000-0100-0000-686ff7d90000\"", "_attachments": "attachments/", "_ts": 1752168409}, {"payPeriodId": "1030073126476088", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-07-27T00:00:00Z", "endDate": "2025-08-02T00:00:00Z", "submitByDate": "2025-08-05T00:00:00Z", "checkDate": "2025-08-07T00:00:00Z", "checkCount": 0, "id": "f5d3895f-7ed1-4eea-b01d-c86ec20eee5b", "companyId": "12018698", "type": "payperiod", "_rid": "NmJkAKiCbEf7FgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf7FgEAAAAAAA==/", "_etag": "\"9d00bf9f-0000-0100-0000-686ff7d90000\"", "_attachments": "attachments/", "_ts": 1752168409}, {"payPeriodId": "1030073279784328", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-08-03T00:00:00Z", "endDate": "2025-08-09T00:00:00Z", "submitByDate": "2025-08-12T00:00:00Z", "checkDate": "2025-08-14T00:00:00Z", "checkCount": 0, "id": "4309a739-3995-48e3-9a46-60ac0d2ff1d3", "companyId": "12018698", "type": "payperiod", "_rid": "NmJkAKiCbEf8FgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf8FgEAAAAAAA==/", "_etag": "\"9d00c49f-0000-0100-0000-686ff7d90000\"", "_attachments": "attachments/", "_ts": 1752168409}, {"payPeriodId": "1030073421073373", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-08-10T00:00:00Z", "endDate": "2025-08-16T00:00:00Z", "submitByDate": "2025-08-19T00:00:00Z", "checkDate": "2025-08-21T00:00:00Z", "checkCount": 0, "id": "e2d4403c-db35-4b06-979f-77153b56c513", "companyId": "12018698", "type": "payperiod", "_rid": "NmJkAKiCbEf9FgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf9FgEAAAAAAA==/", "_etag": "\"9d00c59f-0000-0100-0000-686ff7d90000\"", "_attachments": "attachments/", "_ts": 1752168409}, {"payPeriodId": "1030073555123836", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-08-17T00:00:00Z", "endDate": "2025-08-23T00:00:00Z", "submitByDate": "2025-08-26T00:00:00Z", "checkDate": "2025-08-28T00:00:00Z", "checkCount": 0, "id": "8b6edf18-053e-4104-8693-c2740e99404b", "companyId": "12018698", "type": "payperiod", "_rid": "NmJkAKiCbEf+FgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf+FgEAAAAAAA==/", "_etag": "\"9d00c79f-0000-0100-0000-686ff7d90000\"", "_attachments": "attachments/", "_ts": 1752168409}, {"payPeriodId": "1030073717648310", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-08-24T00:00:00Z", "endDate": "2025-08-30T00:00:00Z", "submitByDate": "2025-09-02T00:00:00Z", "checkDate": "2025-09-04T00:00:00Z", "checkCount": 0, "id": "2e643673-bd5b-4caa-bfe2-ac65c<PERSON><PERSON><PERSON>", "companyId": "12018698", "type": "payperiod", "_rid": "NmJkAKiCbEf-FgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf-FgEAAAAAAA==/", "_etag": "\"9d00ca9f-0000-0100-0000-686ff7d90000\"", "_attachments": "attachments/", "_ts": 1752168409}, {"payPeriodId": "1030073858016362", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-08-31T00:00:00Z", "endDate": "2025-09-06T00:00:00Z", "submitByDate": "2025-09-09T00:00:00Z", "checkDate": "2025-09-11T00:00:00Z", "checkCount": 0, "id": "c6abae4d-7321-4a70-b8bc-f4be9987437b", "companyId": "12018698", "type": "payperiod", "_rid": "NmJkAKiCbEcAFwEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcAFwEAAAAAAA==/", "_etag": "\"9d00cb9f-0000-0100-0000-686ff7d90000\"", "_attachments": "attachments/", "_ts": 1752168409}, {"payPeriodId": "1030073986451466", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-09-07T00:00:00Z", "endDate": "2025-09-13T00:00:00Z", "submitByDate": "2025-09-16T00:00:00Z", "checkDate": "2025-09-18T00:00:00Z", "checkCount": 0, "id": "3522e300-9324-44cd-8ea0-2bf3ac670172", "companyId": "12018698", "type": "payperiod", "_rid": "NmJkAKiCbEcBFwEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcBFwEAAAAAAA==/", "_etag": "\"9d00ce9f-0000-0100-0000-686ff7d90000\"", "_attachments": "attachments/", "_ts": 1752168409}, {"payPeriodId": "1030074138105803", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-09-14T00:00:00Z", "endDate": "2025-09-20T00:00:00Z", "submitByDate": "2025-09-23T00:00:00Z", "checkDate": "2025-09-25T00:00:00Z", "checkCount": 0, "id": "1f06762b-5adb-454e-ac2c-091ff7802844", "companyId": "12018698", "type": "payperiod", "_rid": "NmJkAKiCbEcCFwEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcCFwEAAAAAAA==/", "_etag": "\"9d00d09f-0000-0100-0000-686ff7d90000\"", "_attachments": "attachments/", "_ts": 1752168409}, {"payPeriodId": "1030074264005751", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-09-21T00:00:00Z", "endDate": "2025-09-27T00:00:00Z", "submitByDate": "2025-09-30T00:00:00Z", "checkDate": "2025-10-02T00:00:00Z", "checkCount": 0, "id": "a586b5b9-426c-44f4-91a3-a5682b39e49e", "companyId": "12018698", "type": "payperiod", "_rid": "NmJkAKiCbEcDFwEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcDFwEAAAAAAA==/", "_etag": "\"9d00d39f-0000-0100-0000-686ff7d90000\"", "_attachments": "attachments/", "_ts": 1752168409}, {"payPeriodId": "1030074450668925", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-09-28T00:00:00Z", "endDate": "2025-10-04T00:00:00Z", "submitByDate": "2025-10-07T00:00:00Z", "checkDate": "2025-10-09T00:00:00Z", "checkCount": 0, "id": "3d0b3a79-9e96-442c-a4d7-ee2fad75502b", "companyId": "12018698", "type": "payperiod", "_rid": "NmJkAKiCbEcEFwEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcEFwEAAAAAAA==/", "_etag": "\"9d00d69f-0000-0100-0000-686ff7d90000\"", "_attachments": "attachments/", "_ts": 1752168409}, {"payPeriodId": "1030069029932861", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2024-12-29T00:00:00Z", "endDate": "2025-01-04T00:00:00Z", "submitByDate": "2025-01-07T00:00:00Z", "checkDate": "2025-01-09T00:00:00Z", "checkCount": 5, "id": "e69de15a-d772-49ea-ae82-5df95503d01a", "companyId": "12018698", "type": "payperiod", "_rid": "NmJkAKiCbEchFwEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEchFwEAAAAAAA==/", "_etag": "\"9d0024a0-0000-0100-0000-686ff7dc0000\"", "_attachments": "attachments/", "_ts": 1752168412}, {"payPeriodId": "1030069156088165", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-01-05T00:00:00Z", "endDate": "2025-01-11T00:00:00Z", "submitByDate": "2025-01-14T00:00:00Z", "checkDate": "2025-01-16T00:00:00Z", "checkCount": 6, "id": "4e597b17-a1c3-4475-a48d-1a50707b74cd", "companyId": "12018698", "type": "payperiod", "_rid": "NmJkAKiCbEciFwEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEciFwEAAAAAAA==/", "_etag": "\"9d0028a0-0000-0100-0000-686ff7dc0000\"", "_attachments": "attachments/", "_ts": 1752168412}, {"payPeriodId": "1030069286625805", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-01-12T00:00:00Z", "endDate": "2025-01-18T00:00:00Z", "submitByDate": "2025-01-21T00:00:00Z", "checkDate": "2025-01-23T00:00:00Z", "checkCount": 6, "id": "cfe04e31-8a32-4bd3-8943-1a92be4c2833", "companyId": "12018698", "type": "payperiod", "_rid": "NmJkAKiCbEcjFwEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcjFwEAAAAAAA==/", "_etag": "\"9d0029a0-0000-0100-0000-686ff7dc0000\"", "_attachments": "attachments/", "_ts": 1752168412}, {"payPeriodId": "1030069438118528", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-01-19T00:00:00Z", "endDate": "2025-01-25T00:00:00Z", "submitByDate": "2025-01-28T00:00:00Z", "checkDate": "2025-01-30T00:00:00Z", "checkCount": 7, "id": "4b71538e-bf28-4243-96fa-594dd3422b43", "companyId": "12018698", "type": "payperiod", "_rid": "NmJkAKiCbEckFwEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEckFwEAAAAAAA==/", "_etag": "\"9d002da0-0000-0100-0000-686ff7dc0000\"", "_attachments": "attachments/", "_ts": 1752168412}, {"payPeriodId": "1030069586597516", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-01-26T00:00:00Z", "endDate": "2025-02-01T00:00:00Z", "submitByDate": "2025-02-04T00:00:00Z", "checkDate": "2025-02-06T00:00:00Z", "checkCount": 7, "id": "c646d79b-3888-43e2-a366-97f1c820b655", "companyId": "12018698", "type": "payperiod", "_rid": "NmJkAKiCbEclFwEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEclFwEAAAAAAA==/", "_etag": "\"9d0030a0-0000-0100-0000-686ff7dc0000\"", "_attachments": "attachments/", "_ts": 1752168412}, {"payPeriodId": "1030069722900078", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-02-02T00:00:00Z", "endDate": "2025-02-08T00:00:00Z", "submitByDate": "2025-02-11T00:00:00Z", "checkDate": "2025-02-13T00:00:00Z", "checkCount": 6, "id": "67f0b3e8-e1f4-478f-b964-840390060b27", "companyId": "12018698", "type": "payperiod", "_rid": "NmJkAKiCbEcmFwEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcmFwEAAAAAAA==/", "_etag": "\"9d0033a0-0000-0100-0000-686ff7dc0000\"", "_attachments": "attachments/", "_ts": 1752168412}, {"payPeriodId": "1030069825730631", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-02-09T00:00:00Z", "endDate": "2025-02-15T00:00:00Z", "submitByDate": "2025-02-18T00:00:00Z", "checkDate": "2025-02-20T00:00:00Z", "checkCount": 7, "id": "8f31eb31-64e5-4c6f-882e-91f242a2b73c", "companyId": "12018698", "type": "payperiod", "_rid": "NmJkAKiCbEcnFwEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcnFwEAAAAAAA==/", "_etag": "\"9d0035a0-0000-0100-0000-686ff7dc0000\"", "_attachments": "attachments/", "_ts": 1752168412}, {"payPeriodId": "1030069981753573", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-02-16T00:00:00Z", "endDate": "2025-02-22T00:00:00Z", "submitByDate": "2025-02-25T00:00:00Z", "checkDate": "2025-02-27T00:00:00Z", "checkCount": 7, "id": "6f61e687-f4ac-49d1-9438-a1a8c9921b68", "companyId": "12018698", "type": "payperiod", "_rid": "NmJkAKiCbEcoFwEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcoFwEAAAAAAA==/", "_etag": "\"9d003aa0-0000-0100-0000-686ff7dc0000\"", "_attachments": "attachments/", "_ts": 1752168412}, {"payPeriodId": "1030070091941341", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-02-23T00:00:00Z", "endDate": "2025-03-01T00:00:00Z", "submitByDate": "2025-03-04T00:00:00Z", "checkDate": "2025-03-06T00:00:00Z", "checkCount": 7, "id": "851fabf1-6968-4395-b078-23e27d9575ae", "companyId": "12018698", "type": "payperiod", "_rid": "NmJkAKiCbEcpFwEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcpFwEAAAAAAA==/", "_etag": "\"9d003ca0-0000-0100-0000-686ff7dc0000\"", "_attachments": "attachments/", "_ts": 1752168412}, {"payPeriodId": "1030070255413473", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-03-01T00:00:00Z", "endDate": "2025-03-08T00:00:00Z", "submitByDate": "2025-03-11T00:00:00Z", "checkDate": "2025-03-13T00:00:00Z", "checkCount": 7, "id": "07928746-ffdf-474f-ae49-3d1bec9b2221", "companyId": "12018698", "type": "payperiod", "_rid": "NmJkAKiCbEcqFwEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcqFwEAAAAAAA==/", "_etag": "\"9d003ea0-0000-0100-0000-686ff7dc0000\"", "_attachments": "attachments/", "_ts": 1752168412}, {"payPeriodId": "1030070397578847", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-03-09T00:00:00Z", "endDate": "2025-03-15T00:00:00Z", "submitByDate": "2025-03-18T00:00:00Z", "checkDate": "2025-03-20T00:00:00Z", "checkCount": 7, "id": "b4a671b1-7b8a-4722-9499-c8d6e73c6632", "companyId": "12018698", "type": "payperiod", "_rid": "NmJkAKiCbEcrFwEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcrFwEAAAAAAA==/", "_etag": "\"9d0042a0-0000-0100-0000-686ff7dc0000\"", "_attachments": "attachments/", "_ts": 1752168412}, {"payPeriodId": "1030070568165032", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-03-16T00:00:00Z", "endDate": "2025-03-22T00:00:00Z", "submitByDate": "2025-03-25T00:00:00Z", "checkDate": "2025-03-27T00:00:00Z", "checkCount": 7, "id": "18471255-b18e-4e32-a2bc-4fa4350eeca6", "companyId": "12018698", "type": "payperiod", "_rid": "NmJkAKiCbEcsFwEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcsFwEAAAAAAA==/", "_etag": "\"9d0049a0-0000-0100-0000-686ff7dd0000\"", "_attachments": "attachments/", "_ts": 1752168413}, {"payPeriodId": "1030070696433136", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-03-23T00:00:00Z", "endDate": "2025-03-29T00:00:00Z", "submitByDate": "2025-04-01T00:00:00Z", "checkDate": "2025-04-03T00:00:00Z", "checkCount": 7, "id": "e80602f0-22e0-4606-8f1f-d86b7600fcdc", "companyId": "12018698", "type": "payperiod", "_rid": "NmJkAKiCbEctFwEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEctFwEAAAAAAA==/", "_etag": "\"9d004ea0-0000-0100-0000-686ff7dd0000\"", "_attachments": "attachments/", "_ts": 1752168413}, {"payPeriodId": "1030070798560373", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-03-30T00:00:00Z", "endDate": "2025-04-05T00:00:00Z", "submitByDate": "2025-04-08T00:00:00Z", "checkDate": "2025-04-10T00:00:00Z", "checkCount": 7, "id": "6980db88-d1ad-4b9d-b316-7fcfbe49baef", "companyId": "12018698", "type": "payperiod", "_rid": "NmJkAKiCbEcuFwEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcuFwEAAAAAAA==/", "_etag": "\"9d0051a0-0000-0100-0000-686ff7dd0000\"", "_attachments": "attachments/", "_ts": 1752168413}, {"payPeriodId": "1030070991696304", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-04-06T00:00:00Z", "endDate": "2025-04-12T00:00:00Z", "submitByDate": "2025-04-15T00:00:00Z", "checkDate": "2025-04-17T00:00:00Z", "checkCount": 7, "id": "f244e1a3-99e2-4e88-8c1f-fe4102866494", "companyId": "12018698", "type": "payperiod", "_rid": "NmJkAKiCbEcvFwEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcvFwEAAAAAAA==/", "_etag": "\"9d0054a0-0000-0100-0000-686ff7dd0000\"", "_attachments": "attachments/", "_ts": 1752168413}, {"payPeriodId": "1030071111367978", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-04-13T00:00:00Z", "endDate": "2025-04-19T00:00:00Z", "submitByDate": "2025-04-22T00:00:00Z", "checkDate": "2025-04-24T00:00:00Z", "checkCount": 6, "id": "8e186aed-bd1f-4780-975a-beb399001fff", "companyId": "12018698", "type": "payperiod", "_rid": "NmJkAKiCbEcwFwEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcwFwEAAAAAAA==/", "_etag": "\"9d0055a0-0000-0100-0000-686ff7dd0000\"", "_attachments": "attachments/", "_ts": 1752168413}, {"payPeriodId": "1030071243704419", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-04-20T00:00:00Z", "endDate": "2025-04-26T00:00:00Z", "submitByDate": "2025-04-29T00:00:00Z", "checkDate": "2025-05-01T00:00:00Z", "checkCount": 6, "id": "6863f178-82bf-4e2b-8606-943870f8ac9f", "companyId": "12018698", "type": "payperiod", "_rid": "NmJkAKiCbEcxFwEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcxFwEAAAAAAA==/", "_etag": "\"9d0056a0-0000-0100-0000-686ff7dd0000\"", "_attachments": "attachments/", "_ts": 1752168413}, {"payPeriodId": "1030071384105993", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-04-27T00:00:00Z", "endDate": "2025-05-03T00:00:00Z", "submitByDate": "2025-05-06T00:00:00Z", "checkDate": "2025-05-08T00:00:00Z", "checkCount": 7, "id": "a44c517e-c561-4965-ae96-513c5f0a93ab", "companyId": "12018698", "type": "payperiod", "_rid": "NmJkAKiCbEcyFwEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcyFwEAAAAAAA==/", "_etag": "\"9d0058a0-0000-0100-0000-686ff7dd0000\"", "_attachments": "attachments/", "_ts": 1752168413}, {"payPeriodId": "1030071548056901", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-05-04T00:00:00Z", "endDate": "2025-05-10T00:00:00Z", "submitByDate": "2025-05-13T00:00:00Z", "checkDate": "2025-05-15T00:00:00Z", "checkCount": 7, "id": "59aafc2f-92a3-4fa3-97f3-54c4ba8b3612", "companyId": "12018698", "type": "payperiod", "_rid": "NmJkAKiCbEczFwEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEczFwEAAAAAAA==/", "_etag": "\"9d005aa0-0000-0100-0000-686ff7dd0000\"", "_attachments": "attachments/", "_ts": 1752168413}, {"payPeriodId": "1030071657175338", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-05-11T00:00:00Z", "endDate": "2025-05-17T00:00:00Z", "submitByDate": "2025-05-20T00:00:00Z", "checkDate": "2025-05-22T00:00:00Z", "checkCount": 6, "id": "f6aba15a-c10f-4654-ba0a-3bb6d5c53b9c", "companyId": "12018698", "type": "payperiod", "_rid": "NmJkAKiCbEc0FwEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc0FwEAAAAAAA==/", "_etag": "\"9d005ca0-0000-0100-0000-686ff7dd0000\"", "_attachments": "attachments/", "_ts": 1752168413}, {"payPeriodId": "1030071779116550", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-05-18T00:00:00Z", "endDate": "2025-05-24T00:00:00Z", "submitByDate": "2025-05-27T00:00:00Z", "checkDate": "2025-05-29T00:00:00Z", "checkCount": 7, "id": "b7b830f6-bf8f-425f-90e6-3214cfc600ed", "companyId": "12018698", "type": "payperiod", "_rid": "NmJkAKiCbEc1FwEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc1FwEAAAAAAA==/", "_etag": "\"9d0061a0-0000-0100-0000-686ff7dd0000\"", "_attachments": "attachments/", "_ts": 1752168413}, {"payPeriodId": "1030071915075191", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-05-25T00:00:00Z", "endDate": "2025-05-31T00:00:00Z", "submitByDate": "2025-06-03T00:00:00Z", "checkDate": "2025-06-05T00:00:00Z", "checkCount": 6, "id": "453888fa-7daa-4ee7-830e-bbba478ac613", "companyId": "12018698", "type": "payperiod", "_rid": "NmJkAKiCbEc2FwEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc2FwEAAAAAAA==/", "_etag": "\"9d0065a0-0000-0100-0000-686ff7dd0000\"", "_attachments": "attachments/", "_ts": 1752168413}, {"payPeriodId": "1030072053383496", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-06-01T00:00:00Z", "endDate": "2025-06-07T00:00:00Z", "submitByDate": "2025-06-10T00:00:00Z", "checkDate": "2025-06-12T00:00:00Z", "checkCount": 6, "id": "1a5305be-1358-4924-990e-54f0cca6cc2a", "companyId": "12018698", "type": "payperiod", "_rid": "NmJkAKiCbEc3FwEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc3FwEAAAAAAA==/", "_etag": "\"9d006ba0-0000-0100-0000-686ff7dd0000\"", "_attachments": "attachments/", "_ts": 1752168413}, {"payPeriodId": "1030072211921610", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-06-08T00:00:00Z", "endDate": "2025-06-14T00:00:00Z", "submitByDate": "2025-06-19T00:00:00Z", "checkDate": "2025-06-20T00:00:00Z", "checkCount": 7, "id": "d019e118-6af7-4b0e-85fc-f01b7bb45610", "companyId": "12018698", "type": "payperiod", "_rid": "NmJkAKiCbEc4FwEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc4FwEAAAAAAA==/", "_etag": "\"9d006da0-0000-0100-0000-686ff7dd0000\"", "_attachments": "attachments/", "_ts": 1752168413}, {"payPeriodId": "1030072332618629", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-06-15T00:00:00Z", "endDate": "2025-06-21T00:00:00Z", "submitByDate": "2025-06-24T00:00:00Z", "checkDate": "2025-06-26T00:00:00Z", "checkCount": 6, "id": "dd8ed1e4-826f-4d4c-9016-03f8319f1137", "companyId": "12018698", "type": "payperiod", "_rid": "NmJkAKiCbEc5FwEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc5FwEAAAAAAA==/", "_etag": "\"9d0070a0-0000-0100-0000-686ff7de0000\"", "_attachments": "attachments/", "_ts": 1752168414}, {"payPeriodId": "1030072481049989", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-06-22T00:00:00Z", "endDate": "2025-06-28T00:00:00Z", "submitByDate": "2025-07-01T00:00:00Z", "checkDate": "2025-07-03T00:00:00Z", "checkCount": 6, "id": "b5e685a3-6dce-47b5-9118-3600e97c7d2e", "companyId": "12018698", "type": "payperiod", "_rid": "NmJkAKiCbEc6FwEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc6FwEAAAAAAA==/", "_etag": "\"9d0074a0-0000-0100-0000-686ff7de0000\"", "_attachments": "attachments/", "_ts": 1752168414}, {"payPeriodId": "1030072614763068", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-06-29T00:00:00Z", "endDate": "2025-07-05T00:00:00Z", "submitByDate": "2025-07-08T00:00:00Z", "checkDate": "2025-07-10T00:00:00Z", "checkCount": 0, "id": "926faadd-c3c3-4d5a-ae38-8c7de454ba89", "companyId": "12018698", "type": "payperiod", "_rid": "NmJkAKiCbEc7FwEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc7FwEAAAAAAA==/", "_etag": "\"9d0079a0-0000-0100-0000-686ff7de0000\"", "_attachments": "attachments/", "_ts": 1752168414}, {"payPeriodId": "1030072741765400", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-07-06T00:00:00Z", "endDate": "2025-07-12T00:00:00Z", "submitByDate": "2025-07-15T00:00:00Z", "checkDate": "2025-07-17T00:00:00Z", "checkCount": 0, "id": "48726b4e-5f7d-4b5f-b5dd-9dee50e4aca0", "companyId": "12018698", "type": "payperiod", "_rid": "NmJkAKiCbEc8FwEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc8FwEAAAAAAA==/", "_etag": "\"9d007ca0-0000-0100-0000-686ff7de0000\"", "_attachments": "attachments/", "_ts": 1752168414}, {"payPeriodId": "1030072894706317", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-07-13T00:00:00Z", "endDate": "2025-07-19T00:00:00Z", "submitByDate": "2025-07-22T00:00:00Z", "checkDate": "2025-07-24T00:00:00Z", "checkCount": 0, "id": "4d6fae7d-892a-4ae0-8107-4b734a7b926a", "companyId": "12018698", "type": "payperiod", "_rid": "NmJkAKiCbEc9FwEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc9FwEAAAAAAA==/", "_etag": "\"9d0081a0-0000-0100-0000-686ff7de0000\"", "_attachments": "attachments/", "_ts": 1752168414}, {"payPeriodId": "1030073007360331", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-07-20T00:00:00Z", "endDate": "2025-07-26T00:00:00Z", "submitByDate": "2025-07-29T00:00:00Z", "checkDate": "2025-07-31T00:00:00Z", "checkCount": 0, "id": "8f6c73fd-a7a4-4a21-93e7-df3a7e2715c6", "companyId": "12018698", "type": "payperiod", "_rid": "NmJkAKiCbEc+FwEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc+FwEAAAAAAA==/", "_etag": "\"9d0085a0-0000-0100-0000-686ff7de0000\"", "_attachments": "attachments/", "_ts": 1752168414}, {"payPeriodId": "1030073126476088", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-07-27T00:00:00Z", "endDate": "2025-08-02T00:00:00Z", "submitByDate": "2025-08-05T00:00:00Z", "checkDate": "2025-08-07T00:00:00Z", "checkCount": 0, "id": "54254a81-e64a-4dac-8b3f-4c2f1ad94319", "companyId": "12018698", "type": "payperiod", "_rid": "NmJkAKiCbEc-FwEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc-FwEAAAAAAA==/", "_etag": "\"9d0088a0-0000-0100-0000-686ff7de0000\"", "_attachments": "attachments/", "_ts": 1752168414}, {"payPeriodId": "1030073279784328", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-08-03T00:00:00Z", "endDate": "2025-08-09T00:00:00Z", "submitByDate": "2025-08-12T00:00:00Z", "checkDate": "2025-08-14T00:00:00Z", "checkCount": 0, "id": "b671150a-03d1-42e4-97c7-3f75e0090c11", "companyId": "12018698", "type": "payperiod", "_rid": "NmJkAKiCbEdAFwEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdAFwEAAAAAAA==/", "_etag": "\"9d008ca0-0000-0100-0000-686ff7de0000\"", "_attachments": "attachments/", "_ts": 1752168414}, {"payPeriodId": "1030073421073373", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-08-10T00:00:00Z", "endDate": "2025-08-16T00:00:00Z", "submitByDate": "2025-08-19T00:00:00Z", "checkDate": "2025-08-21T00:00:00Z", "checkCount": 0, "id": "e0c35dfe-f55a-4da0-9cc1-291daf7a93b5", "companyId": "12018698", "type": "payperiod", "_rid": "NmJkAKiCbEdBFwEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdBFwEAAAAAAA==/", "_etag": "\"9d008ea0-0000-0100-0000-686ff7de0000\"", "_attachments": "attachments/", "_ts": 1752168414}, {"payPeriodId": "1030073555123836", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-08-17T00:00:00Z", "endDate": "2025-08-23T00:00:00Z", "submitByDate": "2025-08-26T00:00:00Z", "checkDate": "2025-08-28T00:00:00Z", "checkCount": 0, "id": "5b04a8aa-1e9d-4501-a674-1194c02b2dea", "companyId": "12018698", "type": "payperiod", "_rid": "NmJkAKiCbEdCFwEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdCFwEAAAAAAA==/", "_etag": "\"9d0092a0-0000-0100-0000-686ff7de0000\"", "_attachments": "attachments/", "_ts": 1752168414}, {"payPeriodId": "1030073717648310", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-08-24T00:00:00Z", "endDate": "2025-08-30T00:00:00Z", "submitByDate": "2025-09-02T00:00:00Z", "checkDate": "2025-09-04T00:00:00Z", "checkCount": 0, "id": "2ab874f9-8db8-4126-ad25-ee584dd385b0", "companyId": "12018698", "type": "payperiod", "_rid": "NmJkAKiCbEdDFwEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdDFwEAAAAAAA==/", "_etag": "\"9d0096a0-0000-0100-0000-686ff7de0000\"", "_attachments": "attachments/", "_ts": 1752168414}, {"payPeriodId": "1030073858016362", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-08-31T00:00:00Z", "endDate": "2025-09-06T00:00:00Z", "submitByDate": "2025-09-09T00:00:00Z", "checkDate": "2025-09-11T00:00:00Z", "checkCount": 0, "id": "e673bf01-a5de-4a88-9ed4-7dbc09f9fd2b", "companyId": "12018698", "type": "payperiod", "_rid": "NmJkAKiCbEdEFwEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdEFwEAAAAAAA==/", "_etag": "\"9d009aa0-0000-0100-0000-686ff7de0000\"", "_attachments": "attachments/", "_ts": 1752168414}, {"payPeriodId": "1030073986451466", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-09-07T00:00:00Z", "endDate": "2025-09-13T00:00:00Z", "submitByDate": "2025-09-16T00:00:00Z", "checkDate": "2025-09-18T00:00:00Z", "checkCount": 0, "id": "2538b6d2-7e6e-418b-b4f4-5a704de799e1", "companyId": "12018698", "type": "payperiod", "_rid": "NmJkAKiCbEdFFwEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdFFwEAAAAAAA==/", "_etag": "\"9d009ba0-0000-0100-0000-686ff7de0000\"", "_attachments": "attachments/", "_ts": 1752168414}, {"payPeriodId": "1030074138105803", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-09-14T00:00:00Z", "endDate": "2025-09-20T00:00:00Z", "submitByDate": "2025-09-23T00:00:00Z", "checkDate": "2025-09-25T00:00:00Z", "checkCount": 0, "id": "3d03f102-cce8-429a-9e30-fc20c921777e", "companyId": "12018698", "type": "payperiod", "_rid": "NmJkAKiCbEdGFwEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdGFwEAAAAAAA==/", "_etag": "\"9d009ca0-0000-0100-0000-686ff7df0000\"", "_attachments": "attachments/", "_ts": 1752168415}, {"payPeriodId": "1030074264005751", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-09-21T00:00:00Z", "endDate": "2025-09-27T00:00:00Z", "submitByDate": "2025-09-30T00:00:00Z", "checkDate": "2025-10-02T00:00:00Z", "checkCount": 0, "id": "d0e1a577-673b-4626-b1a9-0efe4f138e5d", "companyId": "12018698", "type": "payperiod", "_rid": "NmJkAKiCbEdHFwEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdHFwEAAAAAAA==/", "_etag": "\"9d009ea0-0000-0100-0000-686ff7df0000\"", "_attachments": "attachments/", "_ts": 1752168415}, {"payPeriodId": "1030074450668925", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-09-28T00:00:00Z", "endDate": "2025-10-04T00:00:00Z", "submitByDate": "2025-10-07T00:00:00Z", "checkDate": "2025-10-09T00:00:00Z", "checkCount": 0, "id": "e2694c76-9aa0-4a28-b0e8-ceb7cf877bc0", "companyId": "12018698", "type": "payperiod", "_rid": "NmJkAKiCbEdIFwEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdIFwEAAAAAAA==/", "_etag": "\"9d00a0a0-0000-0100-0000-686ff7df0000\"", "_attachments": "attachments/", "_ts": 1752168415}], "links": [{"rel": "self", "href": "https://ca-payroll-ai-mock-sb-001-secure.nicebeach-8a7a52ab.eastus.azurecontainerapps.io/ca/companies/12018698/payperiods"}]}, "status_code": 200}