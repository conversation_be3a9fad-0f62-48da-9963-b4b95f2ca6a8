{"success": true, "company_id": "00472465", "data": {"metadata": {"contentItemCount": 42}, "content": [{"payPeriodId": "1050103134820108", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (2)", "startDate": "2024-12-27T00:00:00Z", "endDate": "2025-01-13T00:00:00Z", "submitByDate": "2025-01-13T00:00:00Z", "checkDate": "2025-01-14T00:00:00Z", "checkCount": 3, "id": "805e64b7-2f96-4467-a4c3-5abdebb72fcd", "companyId": "00472465", "type": "payperiod", "_rid": "NmJkAKiCbEc3-wIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc3-wIAAAAAAA==/", "_etag": "\"a400f0bb-0000-0100-0000-687022510000\"", "_attachments": "attachments/", "_ts": 1752179281}, {"payPeriodId": "1050103134820109", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (2)", "startDate": "2025-01-14T00:00:00Z", "endDate": "2025-01-29T00:00:00Z", "submitByDate": "2025-01-28T00:00:00Z", "checkDate": "2025-01-29T00:00:00Z", "checkCount": 3, "id": "5e2b1b65-ba8d-437c-ae23-a0a575eae83b", "companyId": "00472465", "type": "payperiod", "_rid": "NmJkAKiCbEc4-wIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc4-wIAAAAAAA==/", "_etag": "\"a400f2bb-0000-0100-0000-687022510000\"", "_attachments": "attachments/", "_ts": 1752179281}, {"payPeriodId": "1050104321320734", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (2)", "startDate": "2025-01-30T00:00:00Z", "endDate": "2025-02-13T00:00:00Z", "submitByDate": "2025-02-13T00:00:00Z", "checkDate": "2025-02-14T00:00:00Z", "checkCount": 3, "id": "e12818cb-ee78-4781-8d1f-b472b054a46f", "companyId": "00472465", "type": "payperiod", "_rid": "NmJkAKiCbEc5-wIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc5-wIAAAAAAA==/", "_etag": "\"a400f5bb-0000-0100-0000-687022510000\"", "_attachments": "attachments/", "_ts": 1752179281}, {"payPeriodId": "1050104321320735", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (2)", "startDate": "2025-02-14T00:00:00Z", "endDate": "2025-02-27T00:00:00Z", "submitByDate": "2025-02-27T00:00:00Z", "checkDate": "2025-02-28T00:00:00Z", "checkCount": 3, "id": "71a1b5a9-4ce5-4a56-85b6-198d92182996", "companyId": "00472465", "type": "payperiod", "_rid": "NmJkAKiCbEc6-wIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc6-wIAAAAAAA==/", "_etag": "\"a400fabb-0000-0100-0000-687022510000\"", "_attachments": "attachments/", "_ts": 1752179281}, {"payPeriodId": "1050105278376224", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (2)", "startDate": "2025-02-28T00:00:00Z", "endDate": "2025-03-15T00:00:00Z", "submitByDate": "2025-03-16T00:00:00Z", "checkDate": "2025-03-17T00:00:00Z", "checkCount": 3, "id": "ef1b63e6-ae77-4f86-88d0-b00cdfdd821e", "companyId": "00472465", "type": "payperiod", "_rid": "NmJkAKiCbEc7-wIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc7-wIAAAAAAA==/", "_etag": "\"a400fcbb-0000-0100-0000-687022510000\"", "_attachments": "attachments/", "_ts": 1752179281}, {"payPeriodId": "1050109207686180", "status": "COMPLETED", "description": "Semi-monthly Payroll (2)", "startDate": "2025-03-16T00:00:00Z", "endDate": "2025-03-30T00:00:00Z", "submitByDate": "2025-04-01T00:00:00Z", "checkDate": "2025-03-31T00:00:00Z", "checkCount": 3, "id": "7e035a37-3eeb-40db-b0a2-cf3b505ecc02", "companyId": "00472465", "type": "payperiod", "_rid": "NmJkAKiCbEc8-wIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc8-wIAAAAAAA==/", "_etag": "\"a400fdbb-0000-0100-0000-687022510000\"", "_attachments": "attachments/", "_ts": 1752179281}, {"payPeriodId": "1050106291874606", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (2)", "startDate": "2025-04-01T00:00:00Z", "endDate": "2025-04-15T00:00:00Z", "submitByDate": "2025-04-14T00:00:00Z", "checkDate": "2025-04-15T00:00:00Z", "checkCount": 0, "id": "85c059a1-dbbb-462b-9cb6-8f6721d2c1c8", "companyId": "00472465", "type": "payperiod", "_rid": "NmJkAKiCbEc9-wIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc9-wIAAAAAAA==/", "_etag": "\"a400febb-0000-0100-0000-687022510000\"", "_attachments": "attachments/", "_ts": 1752179281}, {"payPeriodId": "1050109791423548", "status": "INITIAL", "description": "Payroll correction", "startDate": "2025-04-01T00:00:00Z", "endDate": "2025-04-15T00:00:00Z", "submitByDate": "2025-04-15T00:00:00Z", "checkDate": "2025-04-16T00:00:00Z", "checkCount": 0, "id": "2f750307-7a35-4fb3-8cb3-a7e0e9286d3b", "companyId": "00472465", "type": "payperiod", "_rid": "NmJkAKiCbEc+-wIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc+-wIAAAAAAA==/", "_etag": "\"a40001bc-0000-0100-0000-687022510000\"", "_attachments": "attachments/", "_ts": 1752179281}, {"payPeriodId": "1050106291874607", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (2)", "startDate": "2025-04-16T00:00:00Z", "endDate": "2025-04-28T00:00:00Z", "submitByDate": "2025-04-27T00:00:00Z", "checkDate": "2025-04-28T00:00:00Z", "checkCount": 0, "id": "4075923b-00f9-499a-aa44-15f134ee68a9", "companyId": "00472465", "type": "payperiod", "_rid": "NmJkAKiCbEc--wIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc--wIAAAAAAA==/", "_etag": "\"a40002bc-0000-0100-0000-687022510000\"", "_attachments": "attachments/", "_ts": 1752179281}, {"payPeriodId": "1050107499373791", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (2)", "startDate": "2025-04-29T00:00:00Z", "endDate": "2025-05-13T00:00:00Z", "submitByDate": "2025-05-12T00:00:00Z", "checkDate": "2025-05-13T00:00:00Z", "checkCount": 0, "id": "a48b4a1a-a817-48e6-a46d-f7c9aa9fe66c", "companyId": "00472465", "type": "payperiod", "_rid": "NmJkAKiCbEdA-wIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdA-wIAAAAAAA==/", "_etag": "\"a4000abc-0000-0100-0000-687022520000\"", "_attachments": "attachments/", "_ts": 1752179282}, {"payPeriodId": "1050107499373792", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (2)", "startDate": "2025-05-14T00:00:00Z", "endDate": "2025-05-22T00:00:00Z", "submitByDate": "2025-05-21T00:00:00Z", "checkDate": "2025-05-22T00:00:00Z", "checkCount": 0, "id": "d6a31df1-380a-4b12-bcf1-a959726d2dd8", "companyId": "00472465", "type": "payperiod", "_rid": "NmJkAKiCbEdB-wIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdB-wIAAAAAAA==/", "_etag": "\"a4000bbc-0000-0100-0000-687022520000\"", "_attachments": "attachments/", "_ts": 1752179282}, {"payPeriodId": "1050108448741308", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (2)", "startDate": "2025-05-23T00:00:00Z", "endDate": "2025-06-11T00:00:00Z", "submitByDate": "2025-06-10T00:00:00Z", "checkDate": "2025-06-11T00:00:00Z", "checkCount": 0, "id": "a8eceb7e-d973-430b-980c-3fdf16dbd25b", "companyId": "00472465", "type": "payperiod", "_rid": "NmJkAKiCbEdC-wIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdC-wIAAAAAAA==/", "_etag": "\"a4000cbc-0000-0100-0000-687022520000\"", "_attachments": "attachments/", "_ts": 1752179282}, {"payPeriodId": "1050108448741309", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (2)", "startDate": "2025-06-11T00:00:00Z", "endDate": "2025-06-26T00:00:00Z", "submitByDate": "2025-06-25T00:00:00Z", "checkDate": "2025-06-26T00:00:00Z", "checkCount": 0, "id": "add4d149-d490-4412-bf09-de0e4b449dee", "companyId": "00472465", "type": "payperiod", "_rid": "NmJkAKiCbEdD-wIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdD-wIAAAAAAA==/", "_etag": "\"a4000fbc-0000-0100-0000-687022520000\"", "_attachments": "attachments/", "_ts": 1752179282}, {"payPeriodId": "1050109420268757", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (2)", "startDate": "2025-07-01T00:00:00Z", "endDate": "2025-07-15T00:00:00Z", "submitByDate": "2025-07-16T00:00:00Z", "checkDate": "2025-07-18T00:00:00Z", "checkCount": 0, "id": "4e593215-d992-438b-af2c-325e85b16ce7", "companyId": "00472465", "type": "payperiod", "_rid": "NmJkAKiCbEdE-wIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdE-wIAAAAAAA==/", "_etag": "\"a40013bc-0000-0100-0000-687022520000\"", "_attachments": "attachments/", "_ts": 1752179282}, {"payPeriodId": "1050109420268758", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (2)", "startDate": "2025-07-16T00:00:00Z", "endDate": "2025-07-31T00:00:00Z", "submitByDate": "2025-07-30T00:00:00Z", "checkDate": "2025-08-01T00:00:00Z", "checkCount": 0, "id": "a1485299-0970-46fd-ae66-d02c9535362c", "companyId": "00472465", "type": "payperiod", "_rid": "NmJkAKiCbEdF-wIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdF-wIAAAAAAA==/", "_etag": "\"a40014bc-0000-0100-0000-687022520000\"", "_attachments": "attachments/", "_ts": 1752179282}, {"payPeriodId": "1050110631865854", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (2)", "startDate": "2025-08-01T00:00:00Z", "endDate": "2025-08-15T00:00:00Z", "submitByDate": "2025-08-14T00:00:00Z", "checkDate": "2025-08-18T00:00:00Z", "checkCount": 0, "id": "53bb2125-f4be-43e2-b233-847182d8a563", "companyId": "00472465", "type": "payperiod", "_rid": "NmJkAKiCbEdG-wIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdG-wIAAAAAAA==/", "_etag": "\"a40017bc-0000-0100-0000-687022520000\"", "_attachments": "attachments/", "_ts": 1752179282}, {"payPeriodId": "1050110631865855", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (2)", "startDate": "2025-08-16T00:00:00Z", "endDate": "2025-08-31T00:00:00Z", "submitByDate": "2025-08-29T00:00:00Z", "checkDate": "2025-09-03T00:00:00Z", "checkCount": 0, "id": "92d54dea-fb7c-4686-9588-8f1297c4e906", "companyId": "00472465", "type": "payperiod", "_rid": "NmJkAKiCbEdH-wIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdH-wIAAAAAAA==/", "_etag": "\"a40018bc-0000-0100-0000-687022520000\"", "_attachments": "attachments/", "_ts": 1752179282}, {"payPeriodId": "1050111632103714", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (2)", "startDate": "2025-09-01T00:00:00Z", "endDate": "2025-09-15T00:00:00Z", "submitByDate": "2025-09-16T00:00:00Z", "checkDate": "2025-09-18T00:00:00Z", "checkCount": 0, "id": "5031c598-37ac-4142-8041-0c4e1f5f95a3", "companyId": "00472465", "type": "payperiod", "_rid": "NmJkAKiCbEdI-wIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdI-wIAAAAAAA==/", "_etag": "\"a4001abc-0000-0100-0000-687022520000\"", "_attachments": "attachments/", "_ts": 1752179282}, {"payPeriodId": "1050111632103715", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (2)", "startDate": "2025-09-16T00:00:00Z", "endDate": "2025-09-30T00:00:00Z", "submitByDate": "2025-10-01T00:00:00Z", "checkDate": "2025-10-03T00:00:00Z", "checkCount": 0, "id": "8627d59d-b584-4ac7-a68e-2af7ac993f43", "companyId": "00472465", "type": "payperiod", "_rid": "NmJkAKiCbEdJ-wIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdJ-wIAAAAAAA==/", "_etag": "\"a4001bbc-0000-0100-0000-687022520000\"", "_attachments": "attachments/", "_ts": 1752179282}, {"payPeriodId": "1050112637225030", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (2)", "startDate": "2025-10-01T00:00:00Z", "endDate": "2025-10-15T00:00:00Z", "submitByDate": "2025-10-15T00:00:00Z", "checkDate": "2025-10-17T00:00:00Z", "checkCount": 0, "id": "09c1a1c7-c155-48b0-a6d0-056dcfb3570e", "companyId": "00472465", "type": "payperiod", "_rid": "NmJkAKiCbEdK-wIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdK-wIAAAAAAA==/", "_etag": "\"a4001fbc-0000-0100-0000-687022520000\"", "_attachments": "attachments/", "_ts": 1752179282}, {"payPeriodId": "1050112637225031", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (2)", "startDate": "2025-10-16T00:00:00Z", "endDate": "2025-10-31T00:00:00Z", "submitByDate": "2025-10-30T00:00:00Z", "checkDate": "2025-11-03T00:00:00Z", "checkCount": 0, "id": "1e936376-8a55-4753-9f28-338bec1d4b30", "companyId": "00472465", "type": "payperiod", "_rid": "NmJkAKiCbEdL-wIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdL-wIAAAAAAA==/", "_etag": "\"a40020bc-0000-0100-0000-687022520000\"", "_attachments": "attachments/", "_ts": 1752179282}, {"payPeriodId": "1050103134820108", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (2)", "startDate": "2024-12-27T00:00:00Z", "endDate": "2025-01-13T00:00:00Z", "submitByDate": "2025-01-13T00:00:00Z", "checkDate": "2025-01-14T00:00:00Z", "checkCount": 3, "id": "1b77d046-7557-449f-a863-81c41dedb40c", "companyId": "00472465", "type": "payperiod", "_rid": "NmJkAKiCbEdT-wIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdT-wIAAAAAAA==/", "_etag": "\"a4003abc-0000-0100-0000-687022530000\"", "_attachments": "attachments/", "_ts": 1752179283}, {"payPeriodId": "1050103134820109", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (2)", "startDate": "2025-01-14T00:00:00Z", "endDate": "2025-01-29T00:00:00Z", "submitByDate": "2025-01-28T00:00:00Z", "checkDate": "2025-01-29T00:00:00Z", "checkCount": 3, "id": "2d6ba31e-a686-415d-8947-006a8be9cf30", "companyId": "00472465", "type": "payperiod", "_rid": "NmJkAKiCbEdU-wIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdU-wIAAAAAAA==/", "_etag": "\"a4003cbc-0000-0100-0000-687022530000\"", "_attachments": "attachments/", "_ts": 1752179283}, {"payPeriodId": "1050104321320734", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (2)", "startDate": "2025-01-30T00:00:00Z", "endDate": "2025-02-13T00:00:00Z", "submitByDate": "2025-02-13T00:00:00Z", "checkDate": "2025-02-14T00:00:00Z", "checkCount": 3, "id": "31152d9c-3040-4325-89a1-7bd6d1522a85", "companyId": "00472465", "type": "payperiod", "_rid": "NmJkAKiCbEdV-wIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdV-wIAAAAAAA==/", "_etag": "\"a40042bc-0000-0100-0000-687022530000\"", "_attachments": "attachments/", "_ts": 1752179283}, {"payPeriodId": "1050104321320735", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (2)", "startDate": "2025-02-14T00:00:00Z", "endDate": "2025-02-27T00:00:00Z", "submitByDate": "2025-02-27T00:00:00Z", "checkDate": "2025-02-28T00:00:00Z", "checkCount": 3, "id": "feb3fc67-4f35-4515-abf4-37f92b0ac3f9", "companyId": "00472465", "type": "payperiod", "_rid": "NmJkAKiCbEdW-wIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdW-wIAAAAAAA==/", "_etag": "\"a40046bc-0000-0100-0000-687022530000\"", "_attachments": "attachments/", "_ts": 1752179283}, {"payPeriodId": "1050105278376224", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (2)", "startDate": "2025-02-28T00:00:00Z", "endDate": "2025-03-15T00:00:00Z", "submitByDate": "2025-03-16T00:00:00Z", "checkDate": "2025-03-17T00:00:00Z", "checkCount": 3, "id": "1bf79803-ec6c-4561-a4b9-4e42e73917d7", "companyId": "00472465", "type": "payperiod", "_rid": "NmJkAKiCbEdX-wIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdX-wIAAAAAAA==/", "_etag": "\"a4004abc-0000-0100-0000-687022530000\"", "_attachments": "attachments/", "_ts": 1752179283}, {"payPeriodId": "1050109207686180", "status": "COMPLETED", "description": "Semi-monthly Payroll (2)", "startDate": "2025-03-16T00:00:00Z", "endDate": "2025-03-30T00:00:00Z", "submitByDate": "2025-04-01T00:00:00Z", "checkDate": "2025-03-31T00:00:00Z", "checkCount": 3, "id": "13420d08-1dcd-46c2-b66b-2e04b0a87029", "companyId": "00472465", "type": "payperiod", "_rid": "NmJkAKiCbEdY-wIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdY-wIAAAAAAA==/", "_etag": "\"a4004dbc-0000-0100-0000-687022530000\"", "_attachments": "attachments/", "_ts": 1752179283}, {"payPeriodId": "1050106291874606", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (2)", "startDate": "2025-04-01T00:00:00Z", "endDate": "2025-04-15T00:00:00Z", "submitByDate": "2025-04-14T00:00:00Z", "checkDate": "2025-04-15T00:00:00Z", "checkCount": 3, "id": "e291bac3-b1c1-4dff-bfd2-b0d196fe6289", "companyId": "00472465", "type": "payperiod", "_rid": "NmJkAKiCbEdZ-wIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdZ-wIAAAAAAA==/", "_etag": "\"a40050bc-0000-0100-0000-687022540000\"", "_attachments": "attachments/", "_ts": 1752179284}, {"payPeriodId": "1050109791423548", "status": "COMPLETED", "description": "Payroll correction", "startDate": "2025-04-01T00:00:00Z", "endDate": "2025-04-15T00:00:00Z", "submitByDate": "2025-04-15T00:00:00Z", "checkDate": "2025-04-16T00:00:00Z", "checkCount": 3, "id": "07343288-8749-4a94-a85f-f0b475ae4754", "companyId": "00472465", "type": "payperiod", "_rid": "NmJkAKiCbEda-wIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEda-wIAAAAAAA==/", "_etag": "\"a40054bc-0000-0100-0000-687022540000\"", "_attachments": "attachments/", "_ts": 1752179284}, {"payPeriodId": "1050106291874607", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (2)", "startDate": "2025-04-16T00:00:00Z", "endDate": "2025-04-28T00:00:00Z", "submitByDate": "2025-04-27T00:00:00Z", "checkDate": "2025-04-28T00:00:00Z", "checkCount": 3, "id": "dc658c10-dece-457f-8ad5-0f98fedb804d", "companyId": "00472465", "type": "payperiod", "_rid": "NmJkAKiCbEdb-wIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdb-wIAAAAAAA==/", "_etag": "\"a40055bc-0000-0100-0000-687022540000\"", "_attachments": "attachments/", "_ts": 1752179284}, {"payPeriodId": "1050107499373791", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (2)", "startDate": "2025-04-29T00:00:00Z", "endDate": "2025-05-13T00:00:00Z", "submitByDate": "2025-05-12T00:00:00Z", "checkDate": "2025-05-13T00:00:00Z", "checkCount": 3, "id": "d123be3e-6098-40c2-a0c1-2b245e8956c8", "companyId": "00472465", "type": "payperiod", "_rid": "NmJkAKiCbEdc-wIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdc-wIAAAAAAA==/", "_etag": "\"a4005abc-0000-0100-0000-687022540000\"", "_attachments": "attachments/", "_ts": 1752179284}, {"payPeriodId": "1050107499373792", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (2)", "startDate": "2025-05-14T00:00:00Z", "endDate": "2025-05-22T00:00:00Z", "submitByDate": "2025-05-21T00:00:00Z", "checkDate": "2025-05-22T00:00:00Z", "checkCount": 3, "id": "b1699bff-53b1-4f07-a89d-e1e0b4dd877b", "companyId": "00472465", "type": "payperiod", "_rid": "NmJkAKiCbEdd-wIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdd-wIAAAAAAA==/", "_etag": "\"a4005ebc-0000-0100-0000-687022540000\"", "_attachments": "attachments/", "_ts": 1752179284}, {"payPeriodId": "1050108448741308", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (2)", "startDate": "2025-05-23T00:00:00Z", "endDate": "2025-06-11T00:00:00Z", "submitByDate": "2025-06-10T00:00:00Z", "checkDate": "2025-06-11T00:00:00Z", "checkCount": 3, "id": "9867080a-e5f4-41d6-bb60-a8c42e90bb72", "companyId": "00472465", "type": "payperiod", "_rid": "NmJkAKiCbEde-wIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEde-wIAAAAAAA==/", "_etag": "\"a40060bc-0000-0100-0000-687022540000\"", "_attachments": "attachments/", "_ts": 1752179284}, {"payPeriodId": "1050108448741309", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (2)", "startDate": "2025-06-11T00:00:00Z", "endDate": "2025-06-26T00:00:00Z", "submitByDate": "2025-06-25T00:00:00Z", "checkDate": "2025-06-26T00:00:00Z", "checkCount": 3, "id": "d6cdbb0c-4f0a-407b-9f32-45c2d282306a", "companyId": "00472465", "type": "payperiod", "_rid": "NmJkAKiCbEdf-wIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdf-wIAAAAAAA==/", "_etag": "\"a40065bc-0000-0100-0000-687022540000\"", "_attachments": "attachments/", "_ts": 1752179284}, {"payPeriodId": "1050109420268757", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (2)", "startDate": "2025-07-01T00:00:00Z", "endDate": "2025-07-15T00:00:00Z", "submitByDate": "2025-07-16T00:00:00Z", "checkDate": "2025-07-18T00:00:00Z", "checkCount": 0, "id": "e2f39a3c-f644-4b13-a962-343b3e5bc25f", "companyId": "00472465", "type": "payperiod", "_rid": "NmJkAKiCbEdg-wIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdg-wIAAAAAAA==/", "_etag": "\"a40069bc-0000-0100-0000-687022540000\"", "_attachments": "attachments/", "_ts": 1752179284}, {"payPeriodId": "1050109420268758", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (2)", "startDate": "2025-07-16T00:00:00Z", "endDate": "2025-07-31T00:00:00Z", "submitByDate": "2025-07-30T00:00:00Z", "checkDate": "2025-08-01T00:00:00Z", "checkCount": 0, "id": "e721667b-ad07-49e0-9eb1-836c8fa04c6e", "companyId": "00472465", "type": "payperiod", "_rid": "NmJkAKiCbEdh-wIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdh-wIAAAAAAA==/", "_etag": "\"a4006cbc-0000-0100-0000-687022540000\"", "_attachments": "attachments/", "_ts": 1752179284}, {"payPeriodId": "1050110631865854", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (2)", "startDate": "2025-08-01T00:00:00Z", "endDate": "2025-08-15T00:00:00Z", "submitByDate": "2025-08-14T00:00:00Z", "checkDate": "2025-08-18T00:00:00Z", "checkCount": 0, "id": "71159d1a-f788-4fcc-846c-9855f1640df8", "companyId": "00472465", "type": "payperiod", "_rid": "NmJkAKiCbEdi-wIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdi-wIAAAAAAA==/", "_etag": "\"a40070bc-0000-0100-0000-687022540000\"", "_attachments": "attachments/", "_ts": 1752179284}, {"payPeriodId": "1050110631865855", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (2)", "startDate": "2025-08-16T00:00:00Z", "endDate": "2025-08-31T00:00:00Z", "submitByDate": "2025-08-29T00:00:00Z", "checkDate": "2025-09-03T00:00:00Z", "checkCount": 0, "id": "75b8e4ca-e55b-438d-a82d-2a7434544253", "companyId": "00472465", "type": "payperiod", "_rid": "NmJkAKiCbEdj-wIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdj-wIAAAAAAA==/", "_etag": "\"a40074bc-0000-0100-0000-687022540000\"", "_attachments": "attachments/", "_ts": 1752179284}, {"payPeriodId": "1050111632103714", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (2)", "startDate": "2025-09-01T00:00:00Z", "endDate": "2025-09-15T00:00:00Z", "submitByDate": "2025-09-16T00:00:00Z", "checkDate": "2025-09-18T00:00:00Z", "checkCount": 0, "id": "3e681b54-7f93-47e0-9419-db17a1aca276", "companyId": "00472465", "type": "payperiod", "_rid": "NmJkAKiCbEdk-wIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdk-wIAAAAAAA==/", "_etag": "\"a40078bc-0000-0100-0000-687022540000\"", "_attachments": "attachments/", "_ts": 1752179284}, {"payPeriodId": "1050111632103715", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (2)", "startDate": "2025-09-16T00:00:00Z", "endDate": "2025-09-30T00:00:00Z", "submitByDate": "2025-10-01T00:00:00Z", "checkDate": "2025-10-03T00:00:00Z", "checkCount": 0, "id": "1da11fa2-f178-4050-8798-ab216e3d75c4", "companyId": "00472465", "type": "payperiod", "_rid": "NmJkAKiCbEdl-wIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdl-wIAAAAAAA==/", "_etag": "\"a4007bbc-0000-0100-0000-687022550000\"", "_attachments": "attachments/", "_ts": 1752179285}, {"payPeriodId": "1050112637225030", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (2)", "startDate": "2025-10-01T00:00:00Z", "endDate": "2025-10-15T00:00:00Z", "submitByDate": "2025-10-15T00:00:00Z", "checkDate": "2025-10-17T00:00:00Z", "checkCount": 0, "id": "ad3f71b5-ebe8-4a26-b6cb-74146967ab00", "companyId": "00472465", "type": "payperiod", "_rid": "NmJkAKiCbEdm-wIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdm-wIAAAAAAA==/", "_etag": "\"a4007fbc-0000-0100-0000-687022550000\"", "_attachments": "attachments/", "_ts": 1752179285}, {"payPeriodId": "1050112637225031", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (2)", "startDate": "2025-10-16T00:00:00Z", "endDate": "2025-10-31T00:00:00Z", "submitByDate": "2025-10-30T00:00:00Z", "checkDate": "2025-11-03T00:00:00Z", "checkCount": 0, "id": "2a74046f-8457-4089-834d-06bb38cafadb", "companyId": "00472465", "type": "payperiod", "_rid": "NmJkAKiCbEdn-wIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdn-wIAAAAAAA==/", "_etag": "\"a40082bc-0000-0100-0000-687022550000\"", "_attachments": "attachments/", "_ts": 1752179285}], "links": [{"rel": "self", "href": "https://ca-payroll-ai-mock-sb-001-secure.nicebeach-8a7a52ab.eastus.azurecontainerapps.io/ca/companies/00472465/payperiods"}]}, "status_code": 200}