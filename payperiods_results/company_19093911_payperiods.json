{"success": true, "company_id": "19093911", "data": {"metadata": {"contentItemCount": 48}, "content": [{"payPeriodId": "1050103135132991", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-01-01T00:00:00Z", "endDate": "2025-01-15T00:00:00Z", "submitByDate": "2025-01-13T00:00:00Z", "checkDate": "2025-01-15T00:00:00Z", "checkCount": 9, "id": "025ec82b-3e8d-402d-a83c-54a125fe4256", "companyId": "19093911", "type": "payperiod", "_rid": "NmJkAKiCbEftCwMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEftCwMAAAAAAA==/", "_etag": "\"a40040e2-0000-0100-0000-687023580000\"", "_attachments": "attachments/", "_ts": 1752179544}, {"payPeriodId": "1050103135132992", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-01-16T00:00:00Z", "endDate": "2025-01-31T00:00:00Z", "submitByDate": "2025-01-29T00:00:00Z", "checkDate": "2025-01-31T00:00:00Z", "checkCount": 9, "id": "bff65e58-ac69-4a2f-b622-be7663e4bc11", "companyId": "19093911", "type": "payperiod", "_rid": "NmJkAKiCbEfuCwMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfuCwMAAAAAAA==/", "_etag": "\"a40045e2-0000-0100-0000-687023580000\"", "_attachments": "attachments/", "_ts": 1752179544}, {"payPeriodId": "1050104321518868", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-02-01T00:00:00Z", "endDate": "2025-02-15T00:00:00Z", "submitByDate": "2025-02-12T00:00:00Z", "checkDate": "2025-02-14T00:00:00Z", "checkCount": 10, "id": "9c74334f-65eb-47b4-806a-a6c11bfc748c", "companyId": "19093911", "type": "payperiod", "_rid": "NmJkAKiCbEfvCwMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfvCwMAAAAAAA==/", "_etag": "\"a40048e2-0000-0100-0000-687023580000\"", "_attachments": "attachments/", "_ts": 1752179544}, {"payPeriodId": "1050104321518869", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-02-16T00:00:00Z", "endDate": "2025-02-28T00:00:00Z", "submitByDate": "2025-02-26T00:00:00Z", "checkDate": "2025-02-28T00:00:00Z", "checkCount": 10, "id": "16209962-3cad-4c3b-8e4f-89a46a996624", "companyId": "19093911", "type": "payperiod", "_rid": "NmJkAKiCbEfwCwMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfwCwMAAAAAAA==/", "_etag": "\"a4004ae2-0000-0100-0000-687023580000\"", "_attachments": "attachments/", "_ts": 1752179544}, {"payPeriodId": "1050105278732644", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-03-01T00:00:00Z", "endDate": "2025-03-15T00:00:00Z", "submitByDate": "2025-03-12T00:00:00Z", "checkDate": "2025-03-14T00:00:00Z", "checkCount": 10, "id": "532f30ec-83c3-4775-b327-ba9fc459d9ef", "companyId": "19093911", "type": "payperiod", "_rid": "NmJkAKiCbEfxCwMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfxCwMAAAAAAA==/", "_etag": "\"a4004ce2-0000-0100-0000-687023580000\"", "_attachments": "attachments/", "_ts": 1752179544}, {"payPeriodId": "1050105278732645", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-03-16T00:00:00Z", "endDate": "2025-03-31T00:00:00Z", "submitByDate": "2025-03-28T00:00:00Z", "checkDate": "2025-04-01T00:00:00Z", "checkCount": 0, "id": "4068cf0f-ec44-46bf-9008-b9af39f07960", "companyId": "19093911", "type": "payperiod", "_rid": "NmJkAKiCbEfyCwMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfyCwMAAAAAAA==/", "_etag": "\"a40050e2-0000-0100-0000-687023580000\"", "_attachments": "attachments/", "_ts": 1752179544}, {"payPeriodId": "1050106296839409", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-04-01T00:00:00Z", "endDate": "2025-04-15T00:00:00Z", "submitByDate": "2025-04-11T00:00:00Z", "checkDate": "2025-04-15T00:00:00Z", "checkCount": 0, "id": "dc0ced0f-2046-4bbc-96cd-e3055e758732", "companyId": "19093911", "type": "payperiod", "_rid": "NmJkAKiCbEfzCwMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfzCwMAAAAAAA==/", "_etag": "\"a40055e2-0000-0100-0000-687023580000\"", "_attachments": "attachments/", "_ts": 1752179544}, {"payPeriodId": "1050110373876055", "status": "INITIAL", "description": ".", "startDate": "2025-04-07T00:00:00Z", "endDate": "2025-04-27T00:00:00Z", "submitByDate": "2025-04-30T00:00:00Z", "checkDate": "2025-05-01T00:00:00Z", "checkCount": 0, "id": "3b4167f1-1882-4a33-88a2-6b011d8de984", "companyId": "19093911", "type": "payperiod", "_rid": "NmJkAKiCbEf0CwMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf0CwMAAAAAAA==/", "_etag": "\"a40057e2-0000-0100-0000-687023590000\"", "_attachments": "attachments/", "_ts": 1752179545}, {"payPeriodId": "1050106296839410", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-04-16T00:00:00Z", "endDate": "2025-04-30T00:00:00Z", "submitByDate": "2025-04-29T00:00:00Z", "checkDate": "2025-05-01T00:00:00Z", "checkCount": 0, "id": "d080eb08-6dc2-4543-80e6-afc1b84f9ced", "companyId": "19093911", "type": "payperiod", "_rid": "NmJkAKiCbEf1CwMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf1CwMAAAAAAA==/", "_etag": "\"a40059e2-0000-0100-0000-687023590000\"", "_attachments": "attachments/", "_ts": 1752179545}, {"payPeriodId": "1050110252441239", "status": "INITIAL", "description": "Void", "startDate": "2025-04-16T00:00:00Z", "endDate": "2025-04-30T00:00:00Z", "submitByDate": "2025-04-30T00:00:00Z", "checkDate": "2025-05-01T00:00:00Z", "checkCount": 0, "id": "b7584526-5f88-472b-b844-125705ec8c83", "companyId": "19093911", "type": "payperiod", "_rid": "NmJkAKiCbEf2CwMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf2CwMAAAAAAA==/", "_etag": "\"a4005ee2-0000-0100-0000-687023590000\"", "_attachments": "attachments/", "_ts": 1752179545}, {"payPeriodId": "1050110252441397", "status": "INITIAL", "description": "<PERSON> and <PERSON>", "startDate": "2025-04-16T00:00:00Z", "endDate": "2025-04-30T00:00:00Z", "submitByDate": "2025-04-30T00:00:00Z", "checkDate": "2025-05-01T00:00:00Z", "checkCount": 0, "id": "4ebd3f7a-400f-4c27-8503-cd0e7cfe57cb", "companyId": "19093911", "type": "payperiod", "_rid": "NmJkAKiCbEf3CwMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf3CwMAAAAAAA==/", "_etag": "\"a40060e2-0000-0100-0000-687023590000\"", "_attachments": "attachments/", "_ts": 1752179545}, {"payPeriodId": "1050107264558986", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-05-01T00:00:00Z", "endDate": "2025-05-15T00:00:00Z", "submitByDate": "2025-05-13T00:00:00Z", "checkDate": "2025-05-15T00:00:00Z", "checkCount": 0, "id": "a722bf38-d37f-4370-883b-d50cc23b5624", "companyId": "19093911", "type": "payperiod", "_rid": "NmJkAKiCbEf4CwMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf4CwMAAAAAAA==/", "_etag": "\"a40062e2-0000-0100-0000-687023590000\"", "_attachments": "attachments/", "_ts": 1752179545}, {"payPeriodId": "1050107264558987", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-05-16T00:00:00Z", "endDate": "2025-05-31T00:00:00Z", "submitByDate": "2025-05-28T00:00:00Z", "checkDate": "2025-05-30T00:00:00Z", "checkCount": 0, "id": "14e62978-e438-4291-8470-35ffd586ede7", "companyId": "19093911", "type": "payperiod", "_rid": "NmJkAKiCbEf5CwMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf5CwMAAAAAAA==/", "_etag": "\"a40064e2-0000-0100-0000-687023590000\"", "_attachments": "attachments/", "_ts": 1752179545}, {"payPeriodId": "1050108448991703", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-06-01T00:00:00Z", "endDate": "2025-06-15T00:00:00Z", "submitByDate": "2025-06-11T00:00:00Z", "checkDate": "2025-06-13T00:00:00Z", "checkCount": 0, "id": "abfea2d9-ae8a-4626-9798-2deb663239e5", "companyId": "19093911", "type": "payperiod", "_rid": "NmJkAKiCbEf6CwMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf6CwMAAAAAAA==/", "_etag": "\"a40067e2-0000-0100-0000-687023590000\"", "_attachments": "attachments/", "_ts": 1752179545}, {"payPeriodId": "1050112126660586", "status": "INITIAL", "description": "Bonus", "startDate": "2025-06-01T00:00:00Z", "endDate": "2025-06-15T00:00:00Z", "submitByDate": "2025-06-22T00:00:00Z", "checkDate": "2025-06-23T00:00:00Z", "checkCount": 0, "id": "bd16ac5c-ba61-413f-bc9d-b3c08105881f", "companyId": "19093911", "type": "payperiod", "_rid": "NmJkAKiCbEf7CwMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf7CwMAAAAAAA==/", "_etag": "\"a4006ae2-0000-0100-0000-687023590000\"", "_attachments": "attachments/", "_ts": 1752179545}, {"payPeriodId": "1050108448991704", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-06-16T00:00:00Z", "endDate": "2025-06-30T00:00:00Z", "submitByDate": "2025-06-27T00:00:00Z", "checkDate": "2025-07-01T00:00:00Z", "checkCount": 0, "id": "0cb1fff5-f5a0-4ca0-8fb9-1db35c09f786", "companyId": "19093911", "type": "payperiod", "_rid": "NmJkAKiCbEf8CwMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf8CwMAAAAAAA==/", "_etag": "\"a4006ee2-0000-0100-0000-687023590000\"", "_attachments": "attachments/", "_ts": 1752179545}, {"payPeriodId": "1050109420543527", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-07-01T00:00:00Z", "endDate": "2025-07-15T00:00:00Z", "submitByDate": "2025-07-11T00:00:00Z", "checkDate": "2025-07-15T00:00:00Z", "checkCount": 0, "id": "f5d756d9-efc2-4718-baab-b1bc6d6765b4", "companyId": "19093911", "type": "payperiod", "_rid": "NmJkAKiCbEf9CwMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf9CwMAAAAAAA==/", "_etag": "\"a40074e2-0000-0100-0000-687023590000\"", "_attachments": "attachments/", "_ts": 1752179545}, {"payPeriodId": "1050109420543528", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-07-16T00:00:00Z", "endDate": "2025-07-31T00:00:00Z", "submitByDate": "2025-07-30T00:00:00Z", "checkDate": "2025-08-01T00:00:00Z", "checkCount": 0, "id": "a9f66deb-05eb-4348-ac17-ea3b3ddd7c53", "companyId": "19093911", "type": "payperiod", "_rid": "NmJkAKiCbEf+CwMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf+CwMAAAAAAA==/", "_etag": "\"a40078e2-0000-0100-0000-687023590000\"", "_attachments": "attachments/", "_ts": 1752179545}, {"payPeriodId": "1050110398048168", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-08-01T00:00:00Z", "endDate": "2025-08-15T00:00:00Z", "submitByDate": "2025-08-13T00:00:00Z", "checkDate": "2025-08-15T00:00:00Z", "checkCount": 0, "id": "0a73ab67-a18f-455a-9d1b-6906c8b7ef08", "companyId": "19093911", "type": "payperiod", "_rid": "NmJkAKiCbEf-CwMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf-CwMAAAAAAA==/", "_etag": "\"a4007ae2-0000-0100-0000-687023590000\"", "_attachments": "attachments/", "_ts": 1752179545}, {"payPeriodId": "1050110398048169", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-08-16T00:00:00Z", "endDate": "2025-08-31T00:00:00Z", "submitByDate": "2025-08-27T00:00:00Z", "checkDate": "2025-08-29T00:00:00Z", "checkCount": 0, "id": "c5d6591b-9d76-4767-938b-2b85df7aef58", "companyId": "19093911", "type": "payperiod", "_rid": "NmJkAKiCbEcADAMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcADAMAAAAAAA==/", "_etag": "\"a4007ee2-0000-0100-0000-687023590000\"", "_attachments": "attachments/", "_ts": 1752179545}, {"payPeriodId": "1050111632449137", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-09-01T00:00:00Z", "endDate": "2025-09-15T00:00:00Z", "submitByDate": "2025-09-11T00:00:00Z", "checkDate": "2025-09-15T00:00:00Z", "checkCount": 0, "id": "6a903831-a889-48c2-b15e-86dd666a77c4", "companyId": "19093911", "type": "payperiod", "_rid": "NmJkAKiCbEcBDAMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcBDAMAAAAAAA==/", "_etag": "\"a40082e2-0000-0100-0000-6870235a0000\"", "_attachments": "attachments/", "_ts": 1752179546}, {"payPeriodId": "1050111632449138", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-09-16T00:00:00Z", "endDate": "2025-09-30T00:00:00Z", "submitByDate": "2025-09-29T00:00:00Z", "checkDate": "2025-10-01T00:00:00Z", "checkCount": 0, "id": "e67e9380-b0e9-4b54-a8b8-b4fbd94a310e", "companyId": "19093911", "type": "payperiod", "_rid": "NmJkAKiCbEcCDAMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcCDAMAAAAAAA==/", "_etag": "\"a40084e2-0000-0100-0000-6870235a0000\"", "_attachments": "attachments/", "_ts": 1752179546}, {"payPeriodId": "1050112637584647", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-10-01T00:00:00Z", "endDate": "2025-10-15T00:00:00Z", "submitByDate": "2025-10-13T00:00:00Z", "checkDate": "2025-10-15T00:00:00Z", "checkCount": 0, "id": "583a4fde-9d1e-4120-a87d-a656f2793ef9", "companyId": "19093911", "type": "payperiod", "_rid": "NmJkAKiCbEcDDAMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcDDAMAAAAAAA==/", "_etag": "\"a40086e2-0000-0100-0000-6870235a0000\"", "_attachments": "attachments/", "_ts": 1752179546}, {"payPeriodId": "1050112637584648", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-10-16T00:00:00Z", "endDate": "2025-10-31T00:00:00Z", "submitByDate": "2025-10-29T00:00:00Z", "checkDate": "2025-10-31T00:00:00Z", "checkCount": 0, "id": "c0b27334-b03b-408d-a441-0ef5b04d48c3", "companyId": "19093911", "type": "payperiod", "_rid": "NmJkAKiCbEcEDAMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcEDAMAAAAAAA==/", "_etag": "\"a4008ee2-0000-0100-0000-6870235a0000\"", "_attachments": "attachments/", "_ts": 1752179546}, {"payPeriodId": "1050103135132991", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-01-01T00:00:00Z", "endDate": "2025-01-15T00:00:00Z", "submitByDate": "2025-01-13T00:00:00Z", "checkDate": "2025-01-15T00:00:00Z", "checkCount": 9, "id": "026310b8-111b-4d06-bfbb-3ac16a939bdc", "companyId": "19093911", "type": "payperiod", "_rid": "NmJkAKiCbEchDAMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEchDAMAAAAAAA==/", "_etag": "\"a400f6e2-0000-0100-0000-6870235c0000\"", "_attachments": "attachments/", "_ts": 1752179548}, {"payPeriodId": "1050103135132992", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-01-16T00:00:00Z", "endDate": "2025-01-31T00:00:00Z", "submitByDate": "2025-01-29T00:00:00Z", "checkDate": "2025-01-31T00:00:00Z", "checkCount": 9, "id": "c4e7af23-e5f9-4044-a33d-539b2a18a358", "companyId": "19093911", "type": "payperiod", "_rid": "NmJkAKiCbEciDAMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEciDAMAAAAAAA==/", "_etag": "\"a400fbe2-0000-0100-0000-6870235c0000\"", "_attachments": "attachments/", "_ts": 1752179548}, {"payPeriodId": "1050104321518868", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-02-01T00:00:00Z", "endDate": "2025-02-15T00:00:00Z", "submitByDate": "2025-02-12T00:00:00Z", "checkDate": "2025-02-14T00:00:00Z", "checkCount": 10, "id": "cdb30bd7-723d-41fb-9c9a-236827142f31", "companyId": "19093911", "type": "payperiod", "_rid": "NmJkAKiCbEcjDAMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcjDAMAAAAAAA==/", "_etag": "\"a400fde2-0000-0100-0000-6870235c0000\"", "_attachments": "attachments/", "_ts": 1752179548}, {"payPeriodId": "1050104321518869", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-02-16T00:00:00Z", "endDate": "2025-02-28T00:00:00Z", "submitByDate": "2025-02-26T00:00:00Z", "checkDate": "2025-02-28T00:00:00Z", "checkCount": 10, "id": "7e8d1a6e-221d-4b83-9b81-59900a7a26a3", "companyId": "19093911", "type": "payperiod", "_rid": "NmJkAKiCbEckDAMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEckDAMAAAAAAA==/", "_etag": "\"a400fee2-0000-0100-0000-6870235c0000\"", "_attachments": "attachments/", "_ts": 1752179548}, {"payPeriodId": "1050105278732644", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-03-01T00:00:00Z", "endDate": "2025-03-15T00:00:00Z", "submitByDate": "2025-03-12T00:00:00Z", "checkDate": "2025-03-14T00:00:00Z", "checkCount": 10, "id": "3d3ca6b7-c1fc-40a3-bf23-0c4be5bcb118", "companyId": "19093911", "type": "payperiod", "_rid": "NmJkAKiCbEclDAMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEclDAMAAAAAAA==/", "_etag": "\"a40001e3-0000-0100-0000-6870235c0000\"", "_attachments": "attachments/", "_ts": 1752179548}, {"payPeriodId": "1050105278732645", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-03-16T00:00:00Z", "endDate": "2025-03-31T00:00:00Z", "submitByDate": "2025-03-28T00:00:00Z", "checkDate": "2025-04-01T00:00:00Z", "checkCount": 10, "id": "79b095b3-8829-44db-a8ee-f2ea800f361d", "companyId": "19093911", "type": "payperiod", "_rid": "NmJkAKiCbEcmDAMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcmDAMAAAAAAA==/", "_etag": "\"a40005e3-0000-0100-0000-6870235c0000\"", "_attachments": "attachments/", "_ts": 1752179548}, {"payPeriodId": "1050106296839409", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-04-01T00:00:00Z", "endDate": "2025-04-15T00:00:00Z", "submitByDate": "2025-04-11T00:00:00Z", "checkDate": "2025-04-15T00:00:00Z", "checkCount": 10, "id": "c77713c3-bf28-411a-b7f8-402b61b49c27", "companyId": "19093911", "type": "payperiod", "_rid": "NmJkAKiCbEcnDAMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcnDAMAAAAAAA==/", "_etag": "\"a4000ae3-0000-0100-0000-6870235d0000\"", "_attachments": "attachments/", "_ts": 1752179549}, {"payPeriodId": "1050110373876055", "status": "COMPLETED", "description": ".", "startDate": "2025-04-07T00:00:00Z", "endDate": "2025-04-27T00:00:00Z", "submitByDate": "2025-04-30T00:00:00Z", "checkDate": "2025-05-01T00:00:00Z", "checkCount": 1, "id": "4d4177ca-f6f5-4bb4-a971-95e2b80d8f87", "companyId": "19093911", "type": "payperiod", "_rid": "NmJkAKiCbEcoDAMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcoDAMAAAAAAA==/", "_etag": "\"************************************\"", "_attachments": "attachments/", "_ts": 1752179549}, {"payPeriodId": "1050106296839410", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-04-16T00:00:00Z", "endDate": "2025-04-30T00:00:00Z", "submitByDate": "2025-04-29T00:00:00Z", "checkDate": "2025-05-01T00:00:00Z", "checkCount": 9, "id": "8b2b1b83-b3e7-441f-a561-d3fcf5b43869", "companyId": "19093911", "type": "payperiod", "_rid": "NmJkAKiCbEcpDAMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcpDAMAAAAAAA==/", "_etag": "\"a40011e3-0000-0100-0000-6870235d0000\"", "_attachments": "attachments/", "_ts": 1752179549}, {"payPeriodId": "1050110252441239", "status": "COMPLETED", "description": "Void", "startDate": "2025-04-16T00:00:00Z", "endDate": "2025-04-30T00:00:00Z", "submitByDate": "2025-04-30T00:00:00Z", "checkDate": "2025-05-01T00:00:00Z", "checkCount": 2, "id": "4fb66402-3139-42d9-a5d8-e78bfa676d0c", "companyId": "19093911", "type": "payperiod", "_rid": "NmJkAKiCbEcqDAMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcqDAMAAAAAAA==/", "_etag": "\"a40014e3-0000-0100-0000-6870235d0000\"", "_attachments": "attachments/", "_ts": 1752179549}, {"payPeriodId": "1050110252441397", "status": "COMPLETED", "description": "<PERSON> and <PERSON>", "startDate": "2025-04-16T00:00:00Z", "endDate": "2025-04-30T00:00:00Z", "submitByDate": "2025-04-30T00:00:00Z", "checkDate": "2025-05-01T00:00:00Z", "checkCount": 2, "id": "d0f14b1f-48ef-4b2e-b9fe-95061d7b410a", "companyId": "19093911", "type": "payperiod", "_rid": "NmJkAKiCbEcrDAMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcrDAMAAAAAAA==/", "_etag": "\"a40016e3-0000-0100-0000-6870235d0000\"", "_attachments": "attachments/", "_ts": 1752179549}, {"payPeriodId": "1050107264558986", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-05-01T00:00:00Z", "endDate": "2025-05-15T00:00:00Z", "submitByDate": "2025-05-13T00:00:00Z", "checkDate": "2025-05-15T00:00:00Z", "checkCount": 10, "id": "2a757ed4-e6f0-4b7a-b82e-6dacfe01fb2b", "companyId": "19093911", "type": "payperiod", "_rid": "NmJkAKiCbEcsDAMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcsDAMAAAAAAA==/", "_etag": "\"a40017e3-0000-0100-0000-6870235d0000\"", "_attachments": "attachments/", "_ts": 1752179549}, {"payPeriodId": "1050107264558987", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-05-16T00:00:00Z", "endDate": "2025-05-31T00:00:00Z", "submitByDate": "2025-05-28T00:00:00Z", "checkDate": "2025-05-30T00:00:00Z", "checkCount": 10, "id": "d8e97f04-4b68-4c39-8ff9-07a463ec76e7", "companyId": "19093911", "type": "payperiod", "_rid": "NmJkAKiCbEctDAMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEctDAMAAAAAAA==/", "_etag": "\"a40018e3-0000-0100-0000-6870235d0000\"", "_attachments": "attachments/", "_ts": 1752179549}, {"payPeriodId": "1050108448991703", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-06-01T00:00:00Z", "endDate": "2025-06-15T00:00:00Z", "submitByDate": "2025-06-11T00:00:00Z", "checkDate": "2025-06-13T00:00:00Z", "checkCount": 10, "id": "5c8bc7ae-1671-4aec-82d6-213066729ef9", "companyId": "19093911", "type": "payperiod", "_rid": "NmJkAKiCbEcuDAMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcuDAMAAAAAAA==/", "_etag": "\"a4001fe3-0000-0100-0000-6870235d0000\"", "_attachments": "attachments/", "_ts": 1752179549}, {"payPeriodId": "1050112126660586", "status": "COMPLETED", "description": "Bonus", "startDate": "2025-06-01T00:00:00Z", "endDate": "2025-06-15T00:00:00Z", "submitByDate": "2025-06-22T00:00:00Z", "checkDate": "2025-06-23T00:00:00Z", "checkCount": 2, "id": "acbc411a-0b16-43bf-8317-c932c9493740", "companyId": "19093911", "type": "payperiod", "_rid": "NmJkAKiCbEcvDAMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcvDAMAAAAAAA==/", "_etag": "\"a40022e3-0000-0100-0000-6870235d0000\"", "_attachments": "attachments/", "_ts": 1752179549}, {"payPeriodId": "1050108448991704", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-06-16T00:00:00Z", "endDate": "2025-06-30T00:00:00Z", "submitByDate": "2025-06-27T00:00:00Z", "checkDate": "2025-07-01T00:00:00Z", "checkCount": 10, "id": "cf09bbb5-7cdc-4e76-96d9-6facd48e4dda", "companyId": "19093911", "type": "payperiod", "_rid": "NmJkAKiCbEcwDAMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcwDAMAAAAAAA==/", "_etag": "\"a40025e3-0000-0100-0000-6870235d0000\"", "_attachments": "attachments/", "_ts": 1752179549}, {"payPeriodId": "1050109420543527", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-07-01T00:00:00Z", "endDate": "2025-07-15T00:00:00Z", "submitByDate": "2025-07-11T00:00:00Z", "checkDate": "2025-07-15T00:00:00Z", "checkCount": 0, "id": "b46ddbe8-d330-4558-b38e-c781f851ea23", "companyId": "19093911", "type": "payperiod", "_rid": "NmJkAKiCbEcxDAMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcxDAMAAAAAAA==/", "_etag": "\"a4002de3-0000-0100-0000-6870235d0000\"", "_attachments": "attachments/", "_ts": 1752179549}, {"payPeriodId": "1050109420543528", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-07-16T00:00:00Z", "endDate": "2025-07-31T00:00:00Z", "submitByDate": "2025-07-30T00:00:00Z", "checkDate": "2025-08-01T00:00:00Z", "checkCount": 0, "id": "51616e8d-5fdc-44d8-8ddc-1d4dada43075", "companyId": "19093911", "type": "payperiod", "_rid": "NmJkAKiCbEcyDAMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcyDAMAAAAAAA==/", "_etag": "\"a40033e3-0000-0100-0000-6870235d0000\"", "_attachments": "attachments/", "_ts": 1752179549}, {"payPeriodId": "1050110398048168", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-08-01T00:00:00Z", "endDate": "2025-08-15T00:00:00Z", "submitByDate": "2025-08-13T00:00:00Z", "checkDate": "2025-08-15T00:00:00Z", "checkCount": 0, "id": "26bcad97-fa24-4d93-ba80-4f8c54dc5661", "companyId": "19093911", "type": "payperiod", "_rid": "NmJkAKiCbEczDAMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEczDAMAAAAAAA==/", "_etag": "\"a40036e3-0000-0100-0000-6870235e0000\"", "_attachments": "attachments/", "_ts": 1752179550}, {"payPeriodId": "1050110398048169", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-08-16T00:00:00Z", "endDate": "2025-08-31T00:00:00Z", "submitByDate": "2025-08-27T00:00:00Z", "checkDate": "2025-08-29T00:00:00Z", "checkCount": 0, "id": "ca3ded8e-8b94-44b7-a493-90ab54d8e34e", "companyId": "19093911", "type": "payperiod", "_rid": "NmJkAKiCbEc0DAMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc0DAMAAAAAAA==/", "_etag": "\"a4003ae3-0000-0100-0000-6870235e0000\"", "_attachments": "attachments/", "_ts": 1752179550}, {"payPeriodId": "1050111632449137", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-09-01T00:00:00Z", "endDate": "2025-09-15T00:00:00Z", "submitByDate": "2025-09-11T00:00:00Z", "checkDate": "2025-09-15T00:00:00Z", "checkCount": 0, "id": "362159c2-f175-477f-8ffe-a228d2acb745", "companyId": "19093911", "type": "payperiod", "_rid": "NmJkAKiCbEc1DAMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc1DAMAAAAAAA==/", "_etag": "\"a4003ce3-0000-0100-0000-6870235e0000\"", "_attachments": "attachments/", "_ts": 1752179550}, {"payPeriodId": "1050111632449138", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-09-16T00:00:00Z", "endDate": "2025-09-30T00:00:00Z", "submitByDate": "2025-09-29T00:00:00Z", "checkDate": "2025-10-01T00:00:00Z", "checkCount": 0, "id": "ae4ff4bf-f4f4-42cd-bdec-4f632729d94d", "companyId": "19093911", "type": "payperiod", "_rid": "NmJkAKiCbEc2DAMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc2DAMAAAAAAA==/", "_etag": "\"a40040e3-0000-0100-0000-6870235e0000\"", "_attachments": "attachments/", "_ts": 1752179550}, {"payPeriodId": "1050112637584647", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-10-01T00:00:00Z", "endDate": "2025-10-15T00:00:00Z", "submitByDate": "2025-10-13T00:00:00Z", "checkDate": "2025-10-15T00:00:00Z", "checkCount": 0, "id": "34156635-5610-499c-9ac0-654ea4122dd5", "companyId": "19093911", "type": "payperiod", "_rid": "NmJkAKiCbEc3DAMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc3DAMAAAAAAA==/", "_etag": "\"a40043e3-0000-0100-0000-6870235e0000\"", "_attachments": "attachments/", "_ts": 1752179550}, {"payPeriodId": "1050112637584648", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-10-16T00:00:00Z", "endDate": "2025-10-31T00:00:00Z", "submitByDate": "2025-10-29T00:00:00Z", "checkDate": "2025-10-31T00:00:00Z", "checkCount": 0, "id": "798ce329-bb72-4432-b93b-3253b25a97ad", "companyId": "19093911", "type": "payperiod", "_rid": "NmJkAKiCbEc4DAMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc4DAMAAAAAAA==/", "_etag": "\"a4004be3-0000-0100-0000-6870235e0000\"", "_attachments": "attachments/", "_ts": 1752179550}], "links": [{"rel": "self", "href": "https://ca-payroll-ai-mock-sb-001-secure.nicebeach-8a7a52ab.eastus.azurecontainerapps.io/ca/companies/19093911/payperiods"}]}, "status_code": 200}