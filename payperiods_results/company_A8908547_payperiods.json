{"success": true, "company_id": "A8908547", "data": {"metadata": {"contentItemCount": 40}, "content": [{"payPeriodId": "1060038969743055", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-01-01T00:00:00Z", "endDate": "2025-01-15T00:00:00Z", "submitByDate": "2025-01-13T00:00:00Z", "checkDate": "2025-01-15T00:00:00Z", "checkCount": 2, "id": "4800845d-cf76-47bd-9363-828bf335e2a0", "companyId": "A8908547", "type": "payperiod", "_rid": "NmJkAKiCbEc0GAMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc0GAMAAAAAAA==/", "_etag": "\"a500240a-0000-0100-0000-687024570000\"", "_attachments": "attachments/", "_ts": 1752179799}, {"payPeriodId": "1060038969743056", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-01-16T00:00:00Z", "endDate": "2025-01-31T00:00:00Z", "submitByDate": "2025-01-29T00:00:00Z", "checkDate": "2025-01-31T00:00:00Z", "checkCount": 2, "id": "0609b988-5a80-4720-b765-951b6ced4146", "companyId": "A8908547", "type": "payperiod", "_rid": "NmJkAKiCbEc1GAMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc1GAMAAAAAAA==/", "_etag": "\"a5002b0a-0000-0100-0000-687024570000\"", "_attachments": "attachments/", "_ts": 1752179799}, {"payPeriodId": "1060039122307225", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-02-01T00:00:00Z", "endDate": "2025-02-15T00:00:00Z", "submitByDate": "2025-02-14T00:00:00Z", "checkDate": "2025-02-18T00:00:00Z", "checkCount": 2, "id": "aa66b371-402c-47fa-9a92-0c8d333a74a3", "companyId": "A8908547", "type": "payperiod", "_rid": "NmJkAKiCbEc2GAMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc2GAMAAAAAAA==/", "_etag": "\"a5002e0a-0000-0100-0000-687024570000\"", "_attachments": "attachments/", "_ts": 1752179799}, {"payPeriodId": "1060039122307226", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-02-16T00:00:00Z", "endDate": "2025-02-28T00:00:00Z", "submitByDate": "2025-02-26T00:00:00Z", "checkDate": "2025-02-28T00:00:00Z", "checkCount": 2, "id": "bb8d7beb-0ba2-4848-810c-95dcb8b4eb8b", "companyId": "A8908547", "type": "payperiod", "_rid": "NmJkAKiCbEc3GAMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc3GAMAAAAAAA==/", "_etag": "\"a500300a-0000-0100-0000-687024570000\"", "_attachments": "attachments/", "_ts": 1752179799}, {"payPeriodId": "1060039252842185", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-03-01T00:00:00Z", "endDate": "2025-03-15T00:00:00Z", "submitByDate": "2025-03-13T00:00:00Z", "checkDate": "2025-03-17T00:00:00Z", "checkCount": 2, "id": "2cc2225e-ed71-4581-9d62-b0891189e356", "companyId": "A8908547", "type": "payperiod", "_rid": "NmJkAKiCbEc4GAMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc4GAMAAAAAAA==/", "_etag": "\"a500310a-0000-0100-0000-687024570000\"", "_attachments": "attachments/", "_ts": 1752179799}, {"payPeriodId": "1060039252842186", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-03-16T00:00:00Z", "endDate": "2025-03-31T00:00:00Z", "submitByDate": "2025-03-27T00:00:00Z", "checkDate": "2025-03-31T00:00:00Z", "checkCount": 2, "id": "b2b7256f-e64a-473a-b6c8-8ced47e8cbf5", "companyId": "A8908547", "type": "payperiod", "_rid": "NmJkAKiCbEc5GAMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc5GAMAAAAAAA==/", "_etag": "\"a500330a-0000-0100-0000-687024570000\"", "_attachments": "attachments/", "_ts": 1752179799}, {"payPeriodId": "1060039454653323", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-04-01T00:00:00Z", "endDate": "2025-04-15T00:00:00Z", "submitByDate": "2025-04-11T00:00:00Z", "checkDate": "2025-04-15T00:00:00Z", "checkCount": 0, "id": "0a786c28-55a0-49a3-b6e9-16df4690cec0", "companyId": "A8908547", "type": "payperiod", "_rid": "NmJkAKiCbEc6GAMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc6GAMAAAAAAA==/", "_etag": "\"a500390a-0000-0100-0000-687024570000\"", "_attachments": "attachments/", "_ts": 1752179799}, {"payPeriodId": "1060039454653324", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-04-16T00:00:00Z", "endDate": "2025-04-30T00:00:00Z", "submitByDate": "2025-04-28T00:00:00Z", "checkDate": "2025-04-30T00:00:00Z", "checkCount": 0, "id": "4e716b94-79ce-41dd-8aa4-3fc628ac4609", "companyId": "A8908547", "type": "payperiod", "_rid": "NmJkAKiCbEc7GAMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc7GAMAAAAAAA==/", "_etag": "\"a500480a-0000-0100-0000-687024580000\"", "_attachments": "attachments/", "_ts": 1752179800}, {"payPeriodId": "1060039591269077", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-05-01T00:00:00Z", "endDate": "2025-05-15T00:00:00Z", "submitByDate": "2025-05-21T00:00:00Z", "checkDate": "2025-05-22T00:00:00Z", "checkCount": 0, "id": "f38991bf-a1c0-4530-9b75-1bc0b431f4c8", "companyId": "A8908547", "type": "payperiod", "_rid": "NmJkAKiCbEc8GAMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc8GAMAAAAAAA==/", "_etag": "\"a5004b0a-0000-0100-0000-687024580000\"", "_attachments": "attachments/", "_ts": 1752179800}, {"payPeriodId": "1060039591269078", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-05-16T00:00:00Z", "endDate": "2025-05-31T00:00:00Z", "submitByDate": "2025-05-29T00:00:00Z", "checkDate": "2025-06-02T00:00:00Z", "checkCount": 0, "id": "8c76fa2c-8869-4367-9e5b-624c49b5256c", "companyId": "A8908547", "type": "payperiod", "_rid": "NmJkAKiCbEc9GAMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc9GAMAAAAAAA==/", "_etag": "\"a500500a-0000-0100-0000-687024580000\"", "_attachments": "attachments/", "_ts": 1752179800}, {"payPeriodId": "1060039760128313", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-06-01T00:00:00Z", "endDate": "2025-06-15T00:00:00Z", "submitByDate": "2025-06-12T00:00:00Z", "checkDate": "2025-06-16T00:00:00Z", "checkCount": 0, "id": "10bf8d05-080a-4a2c-8ea2-412d0de599a7", "companyId": "A8908547", "type": "payperiod", "_rid": "NmJkAKiCbEc+GAMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc+GAMAAAAAAA==/", "_etag": "\"a500530a-0000-0100-0000-687024580000\"", "_attachments": "attachments/", "_ts": 1752179800}, {"payPeriodId": "1060039760128314", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-06-16T00:00:00Z", "endDate": "2025-06-30T00:00:00Z", "submitByDate": "2025-06-26T00:00:00Z", "checkDate": "2025-06-30T00:00:00Z", "checkCount": 0, "id": "5fdba5f0-8dab-4646-a529-303806d8fe5e", "companyId": "A8908547", "type": "payperiod", "_rid": "NmJkAKiCbEc-GAMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc-GAMAAAAAAA==/", "_etag": "\"a500570a-0000-0100-0000-687024580000\"", "_attachments": "attachments/", "_ts": 1752179800}, {"payPeriodId": "1060039904937586", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-07-01T00:00:00Z", "endDate": "2025-07-15T00:00:00Z", "submitByDate": "2025-07-11T00:00:00Z", "checkDate": "2025-07-15T00:00:00Z", "checkCount": 0, "id": "f7c0e21d-fc4c-462c-9327-67f600689495", "companyId": "A8908547", "type": "payperiod", "_rid": "NmJkAKiCbEdAGAMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdAGAMAAAAAAA==/", "_etag": "\"a500580a-0000-0100-0000-687024580000\"", "_attachments": "attachments/", "_ts": 1752179800}, {"payPeriodId": "1060039904937587", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-07-16T00:00:00Z", "endDate": "2025-07-31T00:00:00Z", "submitByDate": "2025-07-29T00:00:00Z", "checkDate": "2025-07-31T00:00:00Z", "checkCount": 0, "id": "e21ed2b3-009d-43cb-839a-57604913637a", "companyId": "A8908547", "type": "payperiod", "_rid": "NmJkAKiCbEdBGAMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdBGAMAAAAAAA==/", "_etag": "\"a5005c0a-0000-0100-0000-687024580000\"", "_attachments": "attachments/", "_ts": 1752179800}, {"payPeriodId": "1060040055911229", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-08-01T00:00:00Z", "endDate": "2025-08-15T00:00:00Z", "submitByDate": "2025-08-13T00:00:00Z", "checkDate": "2025-08-15T00:00:00Z", "checkCount": 0, "id": "489285d7-8636-4e25-a011-3c1c258d0a9c", "companyId": "A8908547", "type": "payperiod", "_rid": "NmJkAKiCbEdCGAMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdCGAMAAAAAAA==/", "_etag": "\"a5005f0a-0000-0100-0000-687024580000\"", "_attachments": "attachments/", "_ts": 1752179800}, {"payPeriodId": "1060040055911230", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-08-16T00:00:00Z", "endDate": "2025-08-31T00:00:00Z", "submitByDate": "2025-08-28T00:00:00Z", "checkDate": "2025-09-02T00:00:00Z", "checkCount": 0, "id": "fc9cb927-4b1a-467d-9c8d-123a9d46cd69", "companyId": "A8908547", "type": "payperiod", "_rid": "NmJkAKiCbEdDGAMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdDGAMAAAAAAA==/", "_etag": "\"a500610a-0000-0100-0000-687024580000\"", "_attachments": "attachments/", "_ts": 1752179800}, {"payPeriodId": "1060040179852120", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-09-01T00:00:00Z", "endDate": "2025-09-15T00:00:00Z", "submitByDate": "2025-09-11T00:00:00Z", "checkDate": "2025-09-15T00:00:00Z", "checkCount": 0, "id": "922cff08-6d87-4ab6-ae47-49ab65ca02e8", "companyId": "A8908547", "type": "payperiod", "_rid": "NmJkAKiCbEdEGAMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdEGAMAAAAAAA==/", "_etag": "\"a500640a-0000-0100-0000-687024580000\"", "_attachments": "attachments/", "_ts": 1752179800}, {"payPeriodId": "1060040179852121", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-09-16T00:00:00Z", "endDate": "2025-09-30T00:00:00Z", "submitByDate": "2025-09-26T00:00:00Z", "checkDate": "2025-09-30T00:00:00Z", "checkCount": 0, "id": "406baf73-d65b-473e-a9a5-92b6b52150ef", "companyId": "A8908547", "type": "payperiod", "_rid": "NmJkAKiCbEdFGAMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdFGAMAAAAAAA==/", "_etag": "\"a500660a-0000-0100-0000-687024590000\"", "_attachments": "attachments/", "_ts": 1752179801}, {"payPeriodId": "1060040369705533", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-10-01T00:00:00Z", "endDate": "2025-10-15T00:00:00Z", "submitByDate": "2025-10-13T00:00:00Z", "checkDate": "2025-10-15T00:00:00Z", "checkCount": 0, "id": "43447c7a-4fc5-49c2-ab1b-d060bfb013fc", "companyId": "A8908547", "type": "payperiod", "_rid": "NmJkAKiCbEdGGAMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdGGAMAAAAAAA==/", "_etag": "\"a5006c0a-0000-0100-0000-687024590000\"", "_attachments": "attachments/", "_ts": 1752179801}, {"payPeriodId": "1060040369705534", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-10-16T00:00:00Z", "endDate": "2025-10-31T00:00:00Z", "submitByDate": "2025-10-29T00:00:00Z", "checkDate": "2025-10-31T00:00:00Z", "checkCount": 0, "id": "2d022698-4928-4d82-9f21-9e1d5e9b16ac", "companyId": "A8908547", "type": "payperiod", "_rid": "NmJkAKiCbEdHGAMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdHGAMAAAAAAA==/", "_etag": "\"a5006d0a-0000-0100-0000-687024590000\"", "_attachments": "attachments/", "_ts": 1752179801}, {"payPeriodId": "1060038969743055", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-01-01T00:00:00Z", "endDate": "2025-01-15T00:00:00Z", "submitByDate": "2025-01-13T00:00:00Z", "checkDate": "2025-01-15T00:00:00Z", "checkCount": 2, "id": "2f9ece13-c67a-4caf-a32b-437f88643d54", "companyId": "A8908547", "type": "payperiod", "_rid": "NmJkAKiCbEdMGAMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdMGAMAAAAAAA==/", "_etag": "\"a500820a-0000-0100-0000-687024590000\"", "_attachments": "attachments/", "_ts": 1752179801}, {"payPeriodId": "1060038969743056", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-01-16T00:00:00Z", "endDate": "2025-01-31T00:00:00Z", "submitByDate": "2025-01-29T00:00:00Z", "checkDate": "2025-01-31T00:00:00Z", "checkCount": 2, "id": "cbccbfc7-dd0f-4199-8d8f-dae978d74c60", "companyId": "A8908547", "type": "payperiod", "_rid": "NmJkAKiCbEdNGAMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdNGAMAAAAAAA==/", "_etag": "\"a500830a-0000-0100-0000-687024590000\"", "_attachments": "attachments/", "_ts": 1752179801}, {"payPeriodId": "1060039122307225", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-02-01T00:00:00Z", "endDate": "2025-02-15T00:00:00Z", "submitByDate": "2025-02-14T00:00:00Z", "checkDate": "2025-02-18T00:00:00Z", "checkCount": 2, "id": "6640ff93-3626-4b0a-8171-a06266c8e3dc", "companyId": "A8908547", "type": "payperiod", "_rid": "NmJkAKiCbEdOGAMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdOGAMAAAAAAA==/", "_etag": "\"a500860a-0000-0100-0000-687024590000\"", "_attachments": "attachments/", "_ts": 1752179801}, {"payPeriodId": "1060039122307226", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-02-16T00:00:00Z", "endDate": "2025-02-28T00:00:00Z", "submitByDate": "2025-02-26T00:00:00Z", "checkDate": "2025-02-28T00:00:00Z", "checkCount": 2, "id": "904b79e3-fee9-43d3-990b-350edfc368dd", "companyId": "A8908547", "type": "payperiod", "_rid": "NmJkAKiCbEdPGAMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdPGAMAAAAAAA==/", "_etag": "\"a500890a-0000-0100-0000-687024590000\"", "_attachments": "attachments/", "_ts": 1752179801}, {"payPeriodId": "1060039252842185", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-03-01T00:00:00Z", "endDate": "2025-03-15T00:00:00Z", "submitByDate": "2025-03-13T00:00:00Z", "checkDate": "2025-03-17T00:00:00Z", "checkCount": 2, "id": "82cd37c3-7b48-405b-8791-84536b4b8d23", "companyId": "A8908547", "type": "payperiod", "_rid": "NmJkAKiCbEdQGAMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdQGAMAAAAAAA==/", "_etag": "\"a5008b0a-0000-0100-0000-687024590000\"", "_attachments": "attachments/", "_ts": 1752179801}, {"payPeriodId": "1060039252842186", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-03-16T00:00:00Z", "endDate": "2025-03-31T00:00:00Z", "submitByDate": "2025-03-27T00:00:00Z", "checkDate": "2025-03-31T00:00:00Z", "checkCount": 2, "id": "21e6aa5d-74b3-4e35-b733-85c97ace9165", "companyId": "A8908547", "type": "payperiod", "_rid": "NmJkAKiCbEdRGAMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdRGAMAAAAAAA==/", "_etag": "\"a5008e0a-0000-0100-0000-687024590000\"", "_attachments": "attachments/", "_ts": 1752179801}, {"payPeriodId": "1060039454653323", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-04-01T00:00:00Z", "endDate": "2025-04-15T00:00:00Z", "submitByDate": "2025-04-11T00:00:00Z", "checkDate": "2025-04-15T00:00:00Z", "checkCount": 2, "id": "8f31156c-1b6d-4851-951d-f5797933670a", "companyId": "A8908547", "type": "payperiod", "_rid": "NmJkAKiCbEdSGAMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdSGAMAAAAAAA==/", "_etag": "\"a5008f0a-0000-0100-0000-6870245a0000\"", "_attachments": "attachments/", "_ts": 1752179802}, {"payPeriodId": "1060039454653324", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-04-16T00:00:00Z", "endDate": "2025-04-30T00:00:00Z", "submitByDate": "2025-04-28T00:00:00Z", "checkDate": "2025-04-30T00:00:00Z", "checkCount": 2, "id": "0e4d73cd-cf57-471f-822f-685b7ff4f932", "companyId": "A8908547", "type": "payperiod", "_rid": "NmJkAKiCbEdTGAMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdTGAMAAAAAAA==/", "_etag": "\"a500910a-0000-0100-0000-6870245a0000\"", "_attachments": "attachments/", "_ts": 1752179802}, {"payPeriodId": "1060039591269077", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-05-01T00:00:00Z", "endDate": "2025-05-15T00:00:00Z", "submitByDate": "2025-05-21T00:00:00Z", "checkDate": "2025-05-22T00:00:00Z", "checkCount": 1, "id": "61e63161-0e38-47fe-90c8-4ec4ce4ca753", "companyId": "A8908547", "type": "payperiod", "_rid": "NmJkAKiCbEdUGAMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdUGAMAAAAAAA==/", "_etag": "\"a500930a-0000-0100-0000-6870245a0000\"", "_attachments": "attachments/", "_ts": 1752179802}, {"payPeriodId": "1060039591269078", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-05-16T00:00:00Z", "endDate": "2025-05-31T00:00:00Z", "submitByDate": "2025-05-29T00:00:00Z", "checkDate": "2025-06-02T00:00:00Z", "checkCount": 2, "id": "e4261b7d-d46c-40cb-9cdb-3d7bbef9d311", "companyId": "A8908547", "type": "payperiod", "_rid": "NmJkAKiCbEdVGAMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdVGAMAAAAAAA==/", "_etag": "\"a500960a-0000-0100-0000-6870245a0000\"", "_attachments": "attachments/", "_ts": 1752179802}, {"payPeriodId": "1060039760128313", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-06-01T00:00:00Z", "endDate": "2025-06-15T00:00:00Z", "submitByDate": "2025-06-12T00:00:00Z", "checkDate": "2025-06-16T00:00:00Z", "checkCount": 2, "id": "1cf30ce8-989b-487b-98db-dd240227d259", "companyId": "A8908547", "type": "payperiod", "_rid": "NmJkAKiCbEdWGAMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdWGAMAAAAAAA==/", "_etag": "\"a500980a-0000-0100-0000-6870245a0000\"", "_attachments": "attachments/", "_ts": 1752179802}, {"payPeriodId": "1060039760128314", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-06-16T00:00:00Z", "endDate": "2025-06-30T00:00:00Z", "submitByDate": "2025-06-26T00:00:00Z", "checkDate": "2025-06-30T00:00:00Z", "checkCount": 2, "id": "13310a23-6563-4948-8076-d5f88d81cd00", "companyId": "A8908547", "type": "payperiod", "_rid": "NmJkAKiCbEdXGAMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdXGAMAAAAAAA==/", "_etag": "\"a5009b0a-0000-0100-0000-6870245a0000\"", "_attachments": "attachments/", "_ts": 1752179802}, {"payPeriodId": "1060039904937586", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-07-01T00:00:00Z", "endDate": "2025-07-15T00:00:00Z", "submitByDate": "2025-07-11T00:00:00Z", "checkDate": "2025-07-15T00:00:00Z", "checkCount": 0, "id": "86c1e868-12c9-43b6-b9f6-90d4db1dfd62", "companyId": "A8908547", "type": "payperiod", "_rid": "NmJkAKiCbEdYGAMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdYGAMAAAAAAA==/", "_etag": "\"a5009f0a-0000-0100-0000-6870245a0000\"", "_attachments": "attachments/", "_ts": 1752179802}, {"payPeriodId": "1060039904937587", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-07-16T00:00:00Z", "endDate": "2025-07-31T00:00:00Z", "submitByDate": "2025-07-29T00:00:00Z", "checkDate": "2025-07-31T00:00:00Z", "checkCount": 0, "id": "d6fbe6d7-c858-4388-a6f4-464ec7018bd0", "companyId": "A8908547", "type": "payperiod", "_rid": "NmJkAKiCbEdZGAMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdZGAMAAAAAAA==/", "_etag": "\"a500a10a-0000-0100-0000-6870245a0000\"", "_attachments": "attachments/", "_ts": 1752179802}, {"payPeriodId": "1060040055911229", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-08-01T00:00:00Z", "endDate": "2025-08-15T00:00:00Z", "submitByDate": "2025-08-13T00:00:00Z", "checkDate": "2025-08-15T00:00:00Z", "checkCount": 0, "id": "c4f45713-992a-4f16-a43a-ab2ece4c7ed3", "companyId": "A8908547", "type": "payperiod", "_rid": "NmJkAKiCbEdaGAMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdaGAMAAAAAAA==/", "_etag": "\"a500a50a-0000-0100-0000-6870245a0000\"", "_attachments": "attachments/", "_ts": 1752179802}, {"payPeriodId": "1060040055911230", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-08-16T00:00:00Z", "endDate": "2025-08-31T00:00:00Z", "submitByDate": "2025-08-28T00:00:00Z", "checkDate": "2025-09-02T00:00:00Z", "checkCount": 0, "id": "b0646968-4121-4d50-baa8-ca68e407ba4a", "companyId": "A8908547", "type": "payperiod", "_rid": "NmJkAKiCbEdbGAMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdbGAMAAAAAAA==/", "_etag": "\"a500a80a-0000-0100-0000-6870245a0000\"", "_attachments": "attachments/", "_ts": 1752179802}, {"payPeriodId": "1060040179852120", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-09-01T00:00:00Z", "endDate": "2025-09-15T00:00:00Z", "submitByDate": "2025-09-11T00:00:00Z", "checkDate": "2025-09-15T00:00:00Z", "checkCount": 0, "id": "0d5f60f7-df02-4ef2-953f-9b79bece7981", "companyId": "A8908547", "type": "payperiod", "_rid": "NmJkAKiCbEdcGAMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdcGAMAAAAAAA==/", "_etag": "\"a500ab0a-0000-0100-0000-6870245a0000\"", "_attachments": "attachments/", "_ts": 1752179802}, {"payPeriodId": "1060040179852121", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-09-16T00:00:00Z", "endDate": "2025-09-30T00:00:00Z", "submitByDate": "2025-09-26T00:00:00Z", "checkDate": "2025-09-30T00:00:00Z", "checkCount": 0, "id": "357330a4-074c-4336-ad6d-8dda55feec04", "companyId": "A8908547", "type": "payperiod", "_rid": "NmJkAKiCbEddGAMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEddGAMAAAAAAA==/", "_etag": "\"a500ac0a-0000-0100-0000-6870245a0000\"", "_attachments": "attachments/", "_ts": 1752179802}, {"payPeriodId": "1060040369705533", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-10-01T00:00:00Z", "endDate": "2025-10-15T00:00:00Z", "submitByDate": "2025-10-13T00:00:00Z", "checkDate": "2025-10-15T00:00:00Z", "checkCount": 0, "id": "d0682c10-5613-43b4-ae92-4e9f8af57d01", "companyId": "A8908547", "type": "payperiod", "_rid": "NmJkAKiCbEdeGAMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdeGAMAAAAAAA==/", "_etag": "\"a500ae0a-0000-0100-0000-6870245b0000\"", "_attachments": "attachments/", "_ts": 1752179803}, {"payPeriodId": "1060040369705534", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-10-16T00:00:00Z", "endDate": "2025-10-31T00:00:00Z", "submitByDate": "2025-10-29T00:00:00Z", "checkDate": "2025-10-31T00:00:00Z", "checkCount": 0, "id": "e88a20a6-1668-4612-b1e3-c7cf8de58b51", "companyId": "A8908547", "type": "payperiod", "_rid": "NmJkAKiCbEdfGAMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdfGAMAAAAAAA==/", "_etag": "\"a500b00a-0000-0100-0000-6870245b0000\"", "_attachments": "attachments/", "_ts": 1752179803}], "links": [{"rel": "self", "href": "https://ca-payroll-ai-mock-sb-001-secure.nicebeach-8a7a52ab.eastus.azurecontainerapps.io/ca/companies/A8908547/payperiods"}]}, "status_code": 200}