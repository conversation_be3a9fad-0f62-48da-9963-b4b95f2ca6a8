{"success": true, "company_id": "15066642", "data": {"metadata": {"contentItemCount": 32}, "content": [{"payPeriodId": "1060039122367863", "intervalCode": "MONTHLY", "status": "COMPLETED", "description": "Monthly Payroll (3)", "startDate": "2025-02-01T00:00:00Z", "endDate": "2025-02-28T00:00:00Z", "submitByDate": "2025-02-18T00:00:00Z", "checkDate": "2025-02-20T00:00:00Z", "checkCount": 1, "id": "cd0b5fbb-da39-492d-9fd5-9f94f7df50fa", "companyId": "15066642", "type": "payperiod", "_rid": "NmJkAKiCbEeFCQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeFCQAAAAAAAA==/", "_etag": "\"97000819-0000-0100-0000-686fcffc0000\"", "_attachments": "attachments/", "_ts": 1752158204}, {"payPeriodId": "1060039299723013", "intervalCode": "MONTHLY", "status": "COMPLETED", "description": "Monthly Payroll (3)", "startDate": "2025-03-01T00:00:00Z", "endDate": "2025-03-31T00:00:00Z", "submitByDate": "2025-03-18T00:00:00Z", "checkDate": "2025-03-20T00:00:00Z", "checkCount": 1, "id": "2e2cb3e5-1292-4196-8278-66a5818ec6dc", "companyId": "15066642", "type": "payperiod", "_rid": "NmJkAKiCbEeGCQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeGCQAAAAAAAA==/", "_etag": "\"97000a19-0000-0100-0000-686fcffc0000\"", "_attachments": "attachments/", "_ts": 1752158204}, {"payPeriodId": "1060039454699124", "intervalCode": "MONTHLY", "status": "INITIAL", "description": "Monthly Payroll (3)", "startDate": "2025-04-01T00:00:00Z", "endDate": "2025-04-30T00:00:00Z", "submitByDate": "2025-04-16T00:00:00Z", "checkDate": "2025-04-18T00:00:00Z", "checkCount": 0, "id": "6bb02a58-f04c-4579-a2a2-1e36160bc678", "companyId": "15066642", "type": "payperiod", "_rid": "NmJkAKiCbEeHCQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeHCQAAAAAAAA==/", "_etag": "\"97000c19-0000-0100-0000-686fcffc0000\"", "_attachments": "attachments/", "_ts": 1752158204}, {"payPeriodId": "1060039591307594", "intervalCode": "MONTHLY", "status": "INITIAL", "description": "Monthly Payroll (3)", "startDate": "2025-05-01T00:00:00Z", "endDate": "2025-05-31T00:00:00Z", "submitByDate": "2025-05-16T00:00:00Z", "checkDate": "2025-05-20T00:00:00Z", "checkCount": 0, "id": "6d21a22d-5302-4c12-a145-c5bc9249173f", "companyId": "15066642", "type": "payperiod", "_rid": "NmJkAKiCbEeICQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeICQAAAAAAAA==/", "_etag": "\"97000d19-0000-0100-0000-686fcffc0000\"", "_attachments": "attachments/", "_ts": 1752158204}, {"payPeriodId": "1060039727898437", "intervalCode": "MONTHLY", "status": "INITIAL", "description": "Monthly Payroll (3)", "startDate": "2025-06-01T00:00:00Z", "endDate": "2025-06-30T00:00:00Z", "submitByDate": "2025-06-18T00:00:00Z", "checkDate": "2025-06-20T00:00:00Z", "checkCount": 0, "id": "70db5260-d186-4b6e-aa35-bfb27b031cc7", "companyId": "15066642", "type": "payperiod", "_rid": "NmJkAKiCbEeJCQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeJCQAAAAAAAA==/", "_etag": "\"97001119-0000-0100-0000-686fcffc0000\"", "_attachments": "attachments/", "_ts": 1752158204}, {"payPeriodId": "1060039905008218", "intervalCode": "MONTHLY", "status": "INITIAL", "description": "Monthly Payroll (3)", "startDate": "2025-07-01T00:00:00Z", "endDate": "2025-07-31T00:00:00Z", "submitByDate": "2025-07-16T00:00:00Z", "checkDate": "2025-07-18T00:00:00Z", "checkCount": 0, "id": "4e191c40-5d05-4cf8-a27b-0c4db66a0cbf", "companyId": "15066642", "type": "payperiod", "_rid": "NmJkAKiCbEeKCQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeKCQAAAAAAAA==/", "_etag": "\"97001819-0000-0100-0000-686fcffd0000\"", "_attachments": "attachments/", "_ts": 1752158205}, {"payPeriodId": "1060040055928747", "intervalCode": "MONTHLY", "status": "INITIAL", "description": "Monthly Payroll (3)", "startDate": "2025-08-01T00:00:00Z", "endDate": "2025-08-31T00:00:00Z", "submitByDate": "2025-08-18T00:00:00Z", "checkDate": "2025-08-20T00:00:00Z", "checkCount": 0, "id": "dae7bb3e-606e-4338-9c98-b6074158bc8c", "companyId": "15066642", "type": "payperiod", "_rid": "NmJkAKiCbEeLCQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeLCQAAAAAAAA==/", "_etag": "\"97001c19-0000-0100-0000-686fcffd0000\"", "_attachments": "attachments/", "_ts": 1752158205}, {"payPeriodId": "1060040229038347", "intervalCode": "MONTHLY", "status": "INITIAL", "description": "Monthly Payroll (3)", "startDate": "2025-09-01T00:00:00Z", "endDate": "2025-09-30T00:00:00Z", "submitByDate": "2025-09-17T00:00:00Z", "checkDate": "2025-09-19T00:00:00Z", "checkCount": 0, "id": "989aa496-7135-412f-9478-455a9fbcd862", "companyId": "15066642", "type": "payperiod", "_rid": "NmJkAKiCbEeMCQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeMCQAAAAAAAA==/", "_etag": "\"97001e19-0000-0100-0000-686fcffd0000\"", "_attachments": "attachments/", "_ts": 1752158205}, {"payPeriodId": "1060039122367863", "intervalCode": "MONTHLY", "status": "COMPLETED", "description": "Monthly Payroll (3)", "startDate": "2025-02-01T00:00:00Z", "endDate": "2025-02-28T00:00:00Z", "submitByDate": "2025-02-18T00:00:00Z", "checkDate": "2025-02-20T00:00:00Z", "checkCount": 1, "id": "74180a9a-23d5-493d-8bf7-d1418d20ab5e", "companyId": "15066642", "type": "payperiod", "_rid": "NmJkAKiCbEeNCQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeNCQAAAAAAAA==/", "_etag": "\"97002519-0000-0100-0000-686fcffd0000\"", "_attachments": "attachments/", "_ts": 1752158205}, {"payPeriodId": "1060039299723013", "intervalCode": "MONTHLY", "status": "COMPLETED", "description": "Monthly Payroll (3)", "startDate": "2025-03-01T00:00:00Z", "endDate": "2025-03-31T00:00:00Z", "submitByDate": "2025-03-18T00:00:00Z", "checkDate": "2025-03-20T00:00:00Z", "checkCount": 1, "id": "5cc2ab47-089d-4374-a953-9110e546fb56", "companyId": "15066642", "type": "payperiod", "_rid": "NmJkAKiCbEeOCQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeOCQAAAAAAAA==/", "_etag": "\"97002719-0000-0100-0000-686fcffd0000\"", "_attachments": "attachments/", "_ts": 1752158205}, {"payPeriodId": "1060039454699124", "intervalCode": "MONTHLY", "status": "COMPLETED", "description": "Monthly Payroll (3)", "startDate": "2025-04-01T00:00:00Z", "endDate": "2025-04-30T00:00:00Z", "submitByDate": "2025-04-16T00:00:00Z", "checkDate": "2025-04-18T00:00:00Z", "checkCount": 1, "id": "1ce07e78-69a4-45dd-b298-fa49b5fe1e3f", "companyId": "15066642", "type": "payperiod", "_rid": "NmJkAKiCbEePCQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEePCQAAAAAAAA==/", "_etag": "\"97002a19-0000-0100-0000-686fcffd0000\"", "_attachments": "attachments/", "_ts": 1752158205}, {"payPeriodId": "1060039591307594", "intervalCode": "MONTHLY", "status": "COMPLETED", "description": "Monthly Payroll (3)", "startDate": "2025-05-01T00:00:00Z", "endDate": "2025-05-31T00:00:00Z", "submitByDate": "2025-05-16T00:00:00Z", "checkDate": "2025-05-20T00:00:00Z", "checkCount": 1, "id": "4f89fa60-8800-4cb9-89d5-b84286d4275f", "companyId": "15066642", "type": "payperiod", "_rid": "NmJkAKiCbEeQCQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeQCQAAAAAAAA==/", "_etag": "\"97002c19-0000-0100-0000-686fcffd0000\"", "_attachments": "attachments/", "_ts": 1752158205}, {"payPeriodId": "1060039727898437", "intervalCode": "MONTHLY", "status": "COMPLETED", "description": "Monthly Payroll (3)", "startDate": "2025-06-01T00:00:00Z", "endDate": "2025-06-30T00:00:00Z", "submitByDate": "2025-06-18T00:00:00Z", "checkDate": "2025-06-20T00:00:00Z", "checkCount": 1, "id": "872ff5b3-b048-4ba8-8aea-fe446a521a78", "companyId": "15066642", "type": "payperiod", "_rid": "NmJkAKiCbEeRCQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeRCQAAAAAAAA==/", "_etag": "\"97002d19-0000-0100-0000-686fcffd0000\"", "_attachments": "attachments/", "_ts": 1752158205}, {"payPeriodId": "1060039905008218", "intervalCode": "MONTHLY", "status": "INITIAL", "description": "Monthly Payroll (3)", "startDate": "2025-07-01T00:00:00Z", "endDate": "2025-07-31T00:00:00Z", "submitByDate": "2025-07-16T00:00:00Z", "checkDate": "2025-07-18T00:00:00Z", "checkCount": 0, "id": "4ed147aa-47ec-430b-b232-f61c2fa38d9f", "companyId": "15066642", "type": "payperiod", "_rid": "NmJkAKiCbEeSCQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeSCQAAAAAAAA==/", "_etag": "\"97002e19-0000-0100-0000-686fcffd0000\"", "_attachments": "attachments/", "_ts": 1752158205}, {"payPeriodId": "1060040055928747", "intervalCode": "MONTHLY", "status": "INITIAL", "description": "Monthly Payroll (3)", "startDate": "2025-08-01T00:00:00Z", "endDate": "2025-08-31T00:00:00Z", "submitByDate": "2025-08-18T00:00:00Z", "checkDate": "2025-08-20T00:00:00Z", "checkCount": 0, "id": "1ebbd972-bcab-4cc7-b6c1-abb7b4aee2a8", "companyId": "15066642", "type": "payperiod", "_rid": "NmJkAKiCbEeTCQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeTCQAAAAAAAA==/", "_etag": "\"97003819-0000-0100-0000-686fcffd0000\"", "_attachments": "attachments/", "_ts": 1752158205}, {"payPeriodId": "1060040229038347", "intervalCode": "MONTHLY", "status": "INITIAL", "description": "Monthly Payroll (3)", "startDate": "2025-09-01T00:00:00Z", "endDate": "2025-09-30T00:00:00Z", "submitByDate": "2025-09-17T00:00:00Z", "checkDate": "2025-09-19T00:00:00Z", "checkCount": 0, "id": "35f62ea7-538a-476e-852d-a14659eb71ad", "companyId": "15066642", "type": "payperiod", "_rid": "NmJkAKiCbEeUCQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeUCQAAAAAAAA==/", "_etag": "\"97003c19-0000-0100-0000-686fcffd0000\"", "_attachments": "attachments/", "_ts": 1752158205}, {"payPeriodId": "1060039122367863", "intervalCode": "MONTHLY", "status": "COMPLETED", "description": "Monthly Payroll (3)", "startDate": "2025-02-01T00:00:00Z", "endDate": "2025-02-28T00:00:00Z", "submitByDate": "2025-02-18T00:00:00Z", "checkDate": "2025-02-20T00:00:00Z", "checkCount": 1, "id": "dc1633aa-4c44-44bc-8f9a-7a4dedb5d234", "companyId": "15066642", "type": "payperiod", "_rid": "NmJkAKiCbEeyAgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeyAgEAAAAAAA==/", "_etag": "\"9d00ed5c-0000-0100-0000-686ff6390000\"", "_attachments": "attachments/", "_ts": 1752167993}, {"payPeriodId": "1060039299723013", "intervalCode": "MONTHLY", "status": "COMPLETED", "description": "Monthly Payroll (3)", "startDate": "2025-03-01T00:00:00Z", "endDate": "2025-03-31T00:00:00Z", "submitByDate": "2025-03-18T00:00:00Z", "checkDate": "2025-03-20T00:00:00Z", "checkCount": 1, "id": "0c75d9c3-db96-4859-9b7d-58b66b281727", "companyId": "15066642", "type": "payperiod", "_rid": "NmJkAKiCbEezAgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEezAgEAAAAAAA==/", "_etag": "\"9d00f25c-0000-0100-0000-686ff6390000\"", "_attachments": "attachments/", "_ts": 1752167993}, {"payPeriodId": "1060039454699124", "intervalCode": "MONTHLY", "status": "INITIAL", "description": "Monthly Payroll (3)", "startDate": "2025-04-01T00:00:00Z", "endDate": "2025-04-30T00:00:00Z", "submitByDate": "2025-04-16T00:00:00Z", "checkDate": "2025-04-18T00:00:00Z", "checkCount": 0, "id": "99128112-77e6-49d5-a5bb-7c6beb4153f5", "companyId": "15066642", "type": "payperiod", "_rid": "NmJkAKiCbEe0AgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEe0AgEAAAAAAA==/", "_etag": "\"9d00fa5c-0000-0100-0000-686ff6390000\"", "_attachments": "attachments/", "_ts": 1752167993}, {"payPeriodId": "1060039591307594", "intervalCode": "MONTHLY", "status": "INITIAL", "description": "Monthly Payroll (3)", "startDate": "2025-05-01T00:00:00Z", "endDate": "2025-05-31T00:00:00Z", "submitByDate": "2025-05-16T00:00:00Z", "checkDate": "2025-05-20T00:00:00Z", "checkCount": 0, "id": "7548a9b6-ff60-4ab4-b181-24f886eba495", "companyId": "15066642", "type": "payperiod", "_rid": "NmJkAKiCbEe1AgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEe1AgEAAAAAAA==/", "_etag": "\"9d00ff5c-0000-0100-0000-686ff6390000\"", "_attachments": "attachments/", "_ts": 1752167993}, {"payPeriodId": "1060039727898437", "intervalCode": "MONTHLY", "status": "INITIAL", "description": "Monthly Payroll (3)", "startDate": "2025-06-01T00:00:00Z", "endDate": "2025-06-30T00:00:00Z", "submitByDate": "2025-06-18T00:00:00Z", "checkDate": "2025-06-20T00:00:00Z", "checkCount": 0, "id": "4b47be25-3202-4440-91b2-a7b84dfdfe2e", "companyId": "15066642", "type": "payperiod", "_rid": "NmJkAKiCbEe2AgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEe2AgEAAAAAAA==/", "_etag": "\"9d00035d-0000-0100-0000-686ff6390000\"", "_attachments": "attachments/", "_ts": 1752167993}, {"payPeriodId": "1060039905008218", "intervalCode": "MONTHLY", "status": "INITIAL", "description": "Monthly Payroll (3)", "startDate": "2025-07-01T00:00:00Z", "endDate": "2025-07-31T00:00:00Z", "submitByDate": "2025-07-16T00:00:00Z", "checkDate": "2025-07-18T00:00:00Z", "checkCount": 0, "id": "cdd8ed13-e684-4fb2-be4b-9c1601efec18", "companyId": "15066642", "type": "payperiod", "_rid": "NmJkAKiCbEe3AgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEe3AgEAAAAAAA==/", "_etag": "\"9d00065d-0000-0100-0000-686ff6390000\"", "_attachments": "attachments/", "_ts": 1752167993}, {"payPeriodId": "1060040055928747", "intervalCode": "MONTHLY", "status": "INITIAL", "description": "Monthly Payroll (3)", "startDate": "2025-08-01T00:00:00Z", "endDate": "2025-08-31T00:00:00Z", "submitByDate": "2025-08-18T00:00:00Z", "checkDate": "2025-08-20T00:00:00Z", "checkCount": 0, "id": "fbde9bae-7c7c-4b6e-bd5b-0c9525b76e35", "companyId": "15066642", "type": "payperiod", "_rid": "NmJkAKiCbEe4AgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEe4AgEAAAAAAA==/", "_etag": "\"9d000d5d-0000-0100-0000-686ff6390000\"", "_attachments": "attachments/", "_ts": 1752167993}, {"payPeriodId": "1060040229038347", "intervalCode": "MONTHLY", "status": "INITIAL", "description": "Monthly Payroll (3)", "startDate": "2025-09-01T00:00:00Z", "endDate": "2025-09-30T00:00:00Z", "submitByDate": "2025-09-17T00:00:00Z", "checkDate": "2025-09-19T00:00:00Z", "checkCount": 0, "id": "507a9415-7d0f-46f6-9408-86de41f6dead", "companyId": "15066642", "type": "payperiod", "_rid": "NmJkAKiCbEe5AgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEe5AgEAAAAAAA==/", "_etag": "\"9d00125d-0000-0100-0000-686ff6390000\"", "_attachments": "attachments/", "_ts": 1752167993}, {"payPeriodId": "1060039122367863", "intervalCode": "MONTHLY", "status": "COMPLETED", "description": "Monthly Payroll (3)", "startDate": "2025-02-01T00:00:00Z", "endDate": "2025-02-28T00:00:00Z", "submitByDate": "2025-02-18T00:00:00Z", "checkDate": "2025-02-20T00:00:00Z", "checkCount": 1, "id": "7427a3d1-50cb-4823-b07e-ce9a24d0c075", "companyId": "15066642", "type": "payperiod", "_rid": "NmJkAKiCbEe6AgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEe6AgEAAAAAAA==/", "_etag": "\"9d00185d-0000-0100-0000-686ff6390000\"", "_attachments": "attachments/", "_ts": 1752167993}, {"payPeriodId": "1060039299723013", "intervalCode": "MONTHLY", "status": "COMPLETED", "description": "Monthly Payroll (3)", "startDate": "2025-03-01T00:00:00Z", "endDate": "2025-03-31T00:00:00Z", "submitByDate": "2025-03-18T00:00:00Z", "checkDate": "2025-03-20T00:00:00Z", "checkCount": 1, "id": "a6113f3f-0305-4c8f-9559-6e913fd552db", "companyId": "15066642", "type": "payperiod", "_rid": "NmJkAKiCbEe7AgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEe7AgEAAAAAAA==/", "_etag": "\"9d001d5d-0000-0100-0000-686ff6390000\"", "_attachments": "attachments/", "_ts": 1752167993}, {"payPeriodId": "1060039454699124", "intervalCode": "MONTHLY", "status": "COMPLETED", "description": "Monthly Payroll (3)", "startDate": "2025-04-01T00:00:00Z", "endDate": "2025-04-30T00:00:00Z", "submitByDate": "2025-04-16T00:00:00Z", "checkDate": "2025-04-18T00:00:00Z", "checkCount": 1, "id": "ad144364-4a1e-4277-ac9a-cd0a5a880006", "companyId": "15066642", "type": "payperiod", "_rid": "NmJkAKiCbEe8AgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEe8AgEAAAAAAA==/", "_etag": "\"9d00215d-0000-0100-0000-686ff6390000\"", "_attachments": "attachments/", "_ts": 1752167993}, {"payPeriodId": "1060039591307594", "intervalCode": "MONTHLY", "status": "COMPLETED", "description": "Monthly Payroll (3)", "startDate": "2025-05-01T00:00:00Z", "endDate": "2025-05-31T00:00:00Z", "submitByDate": "2025-05-16T00:00:00Z", "checkDate": "2025-05-20T00:00:00Z", "checkCount": 1, "id": "300eb3a5-6b49-45cd-a252-84b729da806f", "companyId": "15066642", "type": "payperiod", "_rid": "NmJkAKiCbEe9AgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEe9AgEAAAAAAA==/", "_etag": "\"9d00265d-0000-0100-0000-686ff63a0000\"", "_attachments": "attachments/", "_ts": 1752167994}, {"payPeriodId": "1060039727898437", "intervalCode": "MONTHLY", "status": "COMPLETED", "description": "Monthly Payroll (3)", "startDate": "2025-06-01T00:00:00Z", "endDate": "2025-06-30T00:00:00Z", "submitByDate": "2025-06-18T00:00:00Z", "checkDate": "2025-06-20T00:00:00Z", "checkCount": 1, "id": "cbc9eaa9-1edf-421d-90f0-0a65e0a698d1", "companyId": "15066642", "type": "payperiod", "_rid": "NmJkAKiCbEe+AgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEe+AgEAAAAAAA==/", "_etag": "\"9d00315d-0000-0100-0000-686ff63a0000\"", "_attachments": "attachments/", "_ts": 1752167994}, {"payPeriodId": "1060039905008218", "intervalCode": "MONTHLY", "status": "INITIAL", "description": "Monthly Payroll (3)", "startDate": "2025-07-01T00:00:00Z", "endDate": "2025-07-31T00:00:00Z", "submitByDate": "2025-07-16T00:00:00Z", "checkDate": "2025-07-18T00:00:00Z", "checkCount": 0, "id": "e8eededd-81e8-41a5-a1a7-a2a29f87bba3", "companyId": "15066642", "type": "payperiod", "_rid": "NmJkAKiCbEe-AgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEe-AgEAAAAAAA==/", "_etag": "\"9d00335d-0000-0100-0000-686ff63a0000\"", "_attachments": "attachments/", "_ts": 1752167994}, {"payPeriodId": "1060040055928747", "intervalCode": "MONTHLY", "status": "INITIAL", "description": "Monthly Payroll (3)", "startDate": "2025-08-01T00:00:00Z", "endDate": "2025-08-31T00:00:00Z", "submitByDate": "2025-08-18T00:00:00Z", "checkDate": "2025-08-20T00:00:00Z", "checkCount": 0, "id": "a494fe2e-a103-40b1-a007-e87463d17bc1", "companyId": "15066642", "type": "payperiod", "_rid": "NmJkAKiCbEfAAgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfAAgEAAAAAAA==/", "_etag": "\"9d00405d-0000-0100-0000-686ff63a0000\"", "_attachments": "attachments/", "_ts": 1752167994}, {"payPeriodId": "1060040229038347", "intervalCode": "MONTHLY", "status": "INITIAL", "description": "Monthly Payroll (3)", "startDate": "2025-09-01T00:00:00Z", "endDate": "2025-09-30T00:00:00Z", "submitByDate": "2025-09-17T00:00:00Z", "checkDate": "2025-09-19T00:00:00Z", "checkCount": 0, "id": "8bdb5ba2-2a6b-46df-8787-d6652e551e6b", "companyId": "15066642", "type": "payperiod", "_rid": "NmJkAKiCbEfBAgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfBAgEAAAAAAA==/", "_etag": "\"9d00485d-0000-0100-0000-686ff63a0000\"", "_attachments": "attachments/", "_ts": 1752167994}], "links": [{"rel": "self", "href": "https://ca-payroll-ai-mock-sb-001-secure.nicebeach-8a7a52ab.eastus.azurecontainerapps.io/ca/companies/15066642/payperiods"}]}, "status_code": 200}