{"success": true, "company_id": "70137182", "data": {"metadata": {"contentItemCount": 42}, "content": [{"payPeriodId": "1090066032918486", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2024-12-23T00:00:00Z", "endDate": "2025-01-05T00:00:00Z", "submitByDate": "2025-01-08T00:00:00Z", "checkDate": "2025-01-10T00:00:00Z", "checkCount": 5, "id": "6f9b9467-7344-4cc8-b173-f717da7cf0ae", "companyId": "70137182", "type": "payperiod", "_rid": "NmJkAKiCbEfs8AIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfs8AIAAAAAAA==/", "_etag": "\"a4007c8c-0000-0100-0000-687021270000\"", "_attachments": "attachments/", "_ts": 1752178983}, {"payPeriodId": "1090066327565634", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-01-06T00:00:00Z", "endDate": "2025-01-19T00:00:00Z", "submitByDate": "2025-01-22T00:00:00Z", "checkDate": "2025-01-24T00:00:00Z", "checkCount": 5, "id": "6e65a9e7-2ab7-4280-80e1-0dcfd3fbdefb", "companyId": "70137182", "type": "payperiod", "_rid": "NmJkAKiCbEft8AIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEft8AIAAAAAAA==/", "_etag": "\"a4007e8c-0000-0100-0000-687021270000\"", "_attachments": "attachments/", "_ts": 1752178983}, {"payPeriodId": "1090066619415673", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-01-20T00:00:00Z", "endDate": "2025-02-02T00:00:00Z", "submitByDate": "2025-02-05T00:00:00Z", "checkDate": "2025-02-07T00:00:00Z", "checkCount": 5, "id": "bc08f7fc-908f-4587-be38-aaa1b3afb665", "companyId": "70137182", "type": "payperiod", "_rid": "NmJkAKiCbEfu8AIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfu8AIAAAAAAA==/", "_etag": "\"a400858c-0000-0100-0000-687021270000\"", "_attachments": "attachments/", "_ts": 1752178983}, {"payPeriodId": "1090066916637900", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-02-03T00:00:00Z", "endDate": "2025-02-16T00:00:00Z", "submitByDate": "2025-02-19T00:00:00Z", "checkDate": "2025-02-21T00:00:00Z", "checkCount": 5, "id": "646b8fdf-4024-43b5-ac7b-cb3ae138f6c8", "companyId": "70137182", "type": "payperiod", "_rid": "NmJkAKiCbEfv8AIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfv8AIAAAAAAA==/", "_etag": "\"a400898c-0000-0100-0000-687021270000\"", "_attachments": "attachments/", "_ts": 1752178983}, {"payPeriodId": "1090067218370466", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-02-17T00:00:00Z", "endDate": "2025-03-02T00:00:00Z", "submitByDate": "2025-03-05T00:00:00Z", "checkDate": "2025-03-07T00:00:00Z", "checkCount": 5, "id": "b57ceafb-d406-4ea6-87e2-0699ffef9aa9", "companyId": "70137182", "type": "payperiod", "_rid": "NmJkAKiCbEfw8AIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfw8AIAAAAAAA==/", "_etag": "\"a4008f8c-0000-0100-0000-687021270000\"", "_attachments": "attachments/", "_ts": 1752178983}, {"payPeriodId": "1090067521589355", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-03-03T00:00:00Z", "endDate": "2025-03-16T00:00:00Z", "submitByDate": "2025-03-19T00:00:00Z", "checkDate": "2025-03-21T00:00:00Z", "checkCount": 4, "id": "31e65fe8-1c9d-4b5c-a8b9-db2f34c37515", "companyId": "70137182", "type": "payperiod", "_rid": "NmJkAKiCbEfx8AIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfx8AIAAAAAAA==/", "_etag": "\"a400978c-0000-0100-0000-687021280000\"", "_attachments": "attachments/", "_ts": 1752178984}, {"payPeriodId": "1090067858904397", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-03-16T00:00:00Z", "endDate": "2025-03-29T00:00:00Z", "submitByDate": "2025-04-02T00:00:00Z", "checkDate": "2025-04-04T00:00:00Z", "checkCount": 0, "id": "97aac454-31b4-4587-9847-a7736411186a", "companyId": "70137182", "type": "payperiod", "_rid": "NmJkAKiCbEfy8AIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfy8AIAAAAAAA==/", "_etag": "\"a4009b8c-0000-0100-0000-687021280000\"", "_attachments": "attachments/", "_ts": 1752178984}, {"payPeriodId": "1090068159868672", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-03-31T00:00:00Z", "endDate": "2025-04-13T00:00:00Z", "submitByDate": "2025-04-16T00:00:00Z", "checkDate": "2025-04-18T00:00:00Z", "checkCount": 0, "id": "e0e4d4ac-956f-4d34-96ea-04856e17c426", "companyId": "70137182", "type": "payperiod", "_rid": "NmJkAKiCbEfz8AIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfz8AIAAAAAAA==/", "_etag": "\"a4009e8c-0000-0100-0000-687021280000\"", "_attachments": "attachments/", "_ts": 1752178984}, {"payPeriodId": "1090068472749070", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-04-14T00:00:00Z", "endDate": "2025-04-27T00:00:00Z", "submitByDate": "2025-04-30T00:00:00Z", "checkDate": "2025-05-02T00:00:00Z", "checkCount": 0, "id": "b482ffa3-c6cf-4a2d-998b-3b623c9d9b0c", "companyId": "70137182", "type": "payperiod", "_rid": "NmJkAKiCbEf08AIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf08AIAAAAAAA==/", "_etag": "\"a400a28c-0000-0100-0000-687021280000\"", "_attachments": "attachments/", "_ts": 1752178984}, {"payPeriodId": "1090068767892050", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-04-28T00:00:00Z", "endDate": "2025-05-11T00:00:00Z", "submitByDate": "2025-05-14T00:00:00Z", "checkDate": "2025-05-16T00:00:00Z", "checkCount": 0, "id": "3bddb596-593e-4ceb-80d7-4d29f7f19b63", "companyId": "70137182", "type": "payperiod", "_rid": "NmJkAKiCbEf18AIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf18AIAAAAAAA==/", "_etag": "\"a400a98c-0000-0100-0000-687021280000\"", "_attachments": "attachments/", "_ts": 1752178984}, {"payPeriodId": "1090069068198760", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-05-12T00:00:00Z", "endDate": "2025-05-25T00:00:00Z", "submitByDate": "2025-05-28T00:00:00Z", "checkDate": "2025-05-30T00:00:00Z", "checkCount": 0, "id": "c798cfba-9fc2-44bf-978a-d79a4438c946", "companyId": "70137182", "type": "payperiod", "_rid": "NmJkAKiCbEf28AIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf28AIAAAAAAA==/", "_etag": "\"a400ad8c-0000-0100-0000-687021280000\"", "_attachments": "attachments/", "_ts": 1752178984}, {"payPeriodId": "1090069372889754", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-05-26T00:00:00Z", "endDate": "2025-06-08T00:00:00Z", "submitByDate": "2025-06-11T00:00:00Z", "checkDate": "2025-06-13T00:00:00Z", "checkCount": 0, "id": "f5b51cc1-978d-48f5-acd3-fee9be16eb9b", "companyId": "70137182", "type": "payperiod", "_rid": "NmJkAKiCbEf38AIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf38AIAAAAAAA==/", "_etag": "\"a400b18c-0000-0100-0000-687021280000\"", "_attachments": "attachments/", "_ts": 1752178984}, {"payPeriodId": "1090069676497003", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-06-09T00:00:00Z", "endDate": "2025-06-22T00:00:00Z", "submitByDate": "2025-06-25T00:00:00Z", "checkDate": "2025-06-27T00:00:00Z", "checkCount": 0, "id": "363a64ad-ada3-4bb6-ac4b-88177acda543", "companyId": "70137182", "type": "payperiod", "_rid": "NmJkAKiCbEf48AIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf48AIAAAAAAA==/", "_etag": "\"a400b48c-0000-0100-0000-687021280000\"", "_attachments": "attachments/", "_ts": 1752178984}, {"payPeriodId": "1090069981444036", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-06-23T00:00:00Z", "endDate": "2025-07-06T00:00:00Z", "submitByDate": "2025-07-09T00:00:00Z", "checkDate": "2025-07-11T00:00:00Z", "checkCount": 0, "id": "fdcb7597-5427-423c-ae61-7adf924ca0ca", "companyId": "70137182", "type": "payperiod", "_rid": "NmJkAKiCbEf58AIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf58AIAAAAAAA==/", "_etag": "\"a400b88c-0000-0100-0000-687021280000\"", "_attachments": "attachments/", "_ts": 1752178984}, {"payPeriodId": "1090070288676198", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-07-07T00:00:00Z", "endDate": "2025-07-20T00:00:00Z", "submitByDate": "2025-07-23T00:00:00Z", "checkDate": "2025-07-25T00:00:00Z", "checkCount": 0, "id": "46042274-14d5-4b82-9f07-c8bda83527b7", "companyId": "70137182", "type": "payperiod", "_rid": "NmJkAKiCbEf68AIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf68AIAAAAAAA==/", "_etag": "\"a400bc8c-0000-0100-0000-687021280000\"", "_attachments": "attachments/", "_ts": 1752178984}, {"payPeriodId": "1090070592018802", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-07-21T00:00:00Z", "endDate": "2025-08-03T00:00:00Z", "submitByDate": "2025-08-06T00:00:00Z", "checkDate": "2025-08-08T00:00:00Z", "checkCount": 0, "id": "87e96a57-12e8-4a31-b5a1-6e96fa0ddf85", "companyId": "70137182", "type": "payperiod", "_rid": "NmJkAKiCbEf78AIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf78AIAAAAAAA==/", "_etag": "\"a400bd8c-0000-0100-0000-687021280000\"", "_attachments": "attachments/", "_ts": 1752178984}, {"payPeriodId": "1090070902147189", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-08-04T00:00:00Z", "endDate": "2025-08-17T00:00:00Z", "submitByDate": "2025-08-20T00:00:00Z", "checkDate": "2025-08-22T00:00:00Z", "checkCount": 0, "id": "b80578eb-f48d-4b27-8a14-5b2da17edff2", "companyId": "70137182", "type": "payperiod", "_rid": "NmJkAKiCbEf88AIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf88AIAAAAAAA==/", "_etag": "\"a400be8c-0000-0100-0000-687021280000\"", "_attachments": "attachments/", "_ts": 1752178984}, {"payPeriodId": "1090071221969150", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-08-18T00:00:00Z", "endDate": "2025-08-31T00:00:00Z", "submitByDate": "2025-09-03T00:00:00Z", "checkDate": "2025-09-05T00:00:00Z", "checkCount": 0, "id": "be662b25-3235-47a1-8961-cb242efbb72d", "companyId": "70137182", "type": "payperiod", "_rid": "NmJkAKiCbEf98AIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf98AIAAAAAAA==/", "_etag": "\"a400c08c-0000-0100-0000-687021290000\"", "_attachments": "attachments/", "_ts": 1752178985}, {"payPeriodId": "1090071527028849", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-09-01T00:00:00Z", "endDate": "2025-09-14T00:00:00Z", "submitByDate": "2025-09-17T00:00:00Z", "checkDate": "2025-09-19T00:00:00Z", "checkCount": 0, "id": "e942206c-27fd-4a51-a47f-77802a97c08c", "companyId": "70137182", "type": "payperiod", "_rid": "NmJkAKiCbEf+8AIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf+8AIAAAAAAA==/", "_etag": "\"a400c78c-0000-0100-0000-687021290000\"", "_attachments": "attachments/", "_ts": 1752178985}, {"payPeriodId": "1090071838406463", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-09-15T00:00:00Z", "endDate": "2025-09-28T00:00:00Z", "submitByDate": "2025-10-01T00:00:00Z", "checkDate": "2025-10-03T00:00:00Z", "checkCount": 0, "id": "5a65d907-a466-49ac-8ff9-c18561e6087c", "companyId": "70137182", "type": "payperiod", "_rid": "NmJkAKiCbEf-8AIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf-8AIAAAAAAA==/", "_etag": "\"a400c98c-0000-0100-0000-687021290000\"", "_attachments": "attachments/", "_ts": 1752178985}, {"payPeriodId": "1090072149908002", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-09-29T00:00:00Z", "endDate": "2025-10-12T00:00:00Z", "submitByDate": "2025-10-15T00:00:00Z", "checkDate": "2025-10-17T00:00:00Z", "checkCount": 0, "id": "b933ac08-89fd-4338-b3ae-6c9de44fdc41", "companyId": "70137182", "type": "payperiod", "_rid": "NmJkAKiCbEcA8QIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcA8QIAAAAAAA==/", "_etag": "\"a400cf8c-0000-0100-0000-687021290000\"", "_attachments": "attachments/", "_ts": 1752178985}, {"payPeriodId": "1090066032918486", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2024-12-23T00:00:00Z", "endDate": "2025-01-05T00:00:00Z", "submitByDate": "2025-01-08T00:00:00Z", "checkDate": "2025-01-10T00:00:00Z", "checkCount": 5, "id": "6e06ecc2-4d5e-49f1-995f-a65669cbbacc", "companyId": "70137182", "type": "payperiod", "_rid": "NmJkAKiCbEcN8QIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcN8QIAAAAAAA==/", "_etag": "\"a400228d-0000-0100-0000-6870212a0000\"", "_attachments": "attachments/", "_ts": 1752178986}, {"payPeriodId": "1090066327565634", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-01-06T00:00:00Z", "endDate": "2025-01-19T00:00:00Z", "submitByDate": "2025-01-22T00:00:00Z", "checkDate": "2025-01-24T00:00:00Z", "checkCount": 5, "id": "d0d5bdfe-59ea-46ee-a333-292b9ad639f4", "companyId": "70137182", "type": "payperiod", "_rid": "NmJkAKiCbEcO8QIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcO8QIAAAAAAA==/", "_etag": "\"a400248d-0000-0100-0000-6870212a0000\"", "_attachments": "attachments/", "_ts": 1752178986}, {"payPeriodId": "1090066619415673", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-01-20T00:00:00Z", "endDate": "2025-02-02T00:00:00Z", "submitByDate": "2025-02-05T00:00:00Z", "checkDate": "2025-02-07T00:00:00Z", "checkCount": 5, "id": "1748afd1-7460-4843-9cfb-102a2230a40e", "companyId": "70137182", "type": "payperiod", "_rid": "NmJkAKiCbEcP8QIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcP8QIAAAAAAA==/", "_etag": "\"a400298d-0000-0100-0000-6870212a0000\"", "_attachments": "attachments/", "_ts": 1752178986}, {"payPeriodId": "1090066916637900", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-02-03T00:00:00Z", "endDate": "2025-02-16T00:00:00Z", "submitByDate": "2025-02-19T00:00:00Z", "checkDate": "2025-02-21T00:00:00Z", "checkCount": 5, "id": "0e1b5d56-3f27-43f6-9857-f9f933f55418", "companyId": "70137182", "type": "payperiod", "_rid": "NmJkAKiCbEcQ8QIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcQ8QIAAAAAAA==/", "_etag": "\"a4002e8d-0000-0100-0000-6870212a0000\"", "_attachments": "attachments/", "_ts": 1752178986}, {"payPeriodId": "1090067218370466", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-02-17T00:00:00Z", "endDate": "2025-03-02T00:00:00Z", "submitByDate": "2025-03-05T00:00:00Z", "checkDate": "2025-03-07T00:00:00Z", "checkCount": 5, "id": "e0c97c6e-f3ac-4b8e-87be-a3626d04e932", "companyId": "70137182", "type": "payperiod", "_rid": "NmJkAKiCbEcR8QIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcR8QIAAAAAAA==/", "_etag": "\"a400308d-0000-0100-0000-6870212a0000\"", "_attachments": "attachments/", "_ts": 1752178986}, {"payPeriodId": "1090067521589355", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-03-03T00:00:00Z", "endDate": "2025-03-16T00:00:00Z", "submitByDate": "2025-03-19T00:00:00Z", "checkDate": "2025-03-21T00:00:00Z", "checkCount": 4, "id": "cd84e359-2650-4724-90cc-2940d3c1cf6e", "companyId": "70137182", "type": "payperiod", "_rid": "NmJkAKiCbEcS8QIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcS8QIAAAAAAA==/", "_etag": "\"a400318d-0000-0100-0000-6870212a0000\"", "_attachments": "attachments/", "_ts": 1752178986}, {"payPeriodId": "1090067858904397", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-03-16T00:00:00Z", "endDate": "2025-03-29T00:00:00Z", "submitByDate": "2025-04-02T00:00:00Z", "checkDate": "2025-04-04T00:00:00Z", "checkCount": 5, "id": "43b43457-38ea-4758-8bf5-9b4dc4f50d7f", "companyId": "70137182", "type": "payperiod", "_rid": "NmJkAKiCbEcT8QIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcT8QIAAAAAAA==/", "_etag": "\"a400338d-0000-0100-0000-6870212a0000\"", "_attachments": "attachments/", "_ts": 1752178986}, {"payPeriodId": "1090068159868672", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-03-31T00:00:00Z", "endDate": "2025-04-13T00:00:00Z", "submitByDate": "2025-04-16T00:00:00Z", "checkDate": "2025-04-18T00:00:00Z", "checkCount": 5, "id": "5e060f04-65ab-4e12-a45b-611ef9f3a63a", "companyId": "70137182", "type": "payperiod", "_rid": "NmJkAKiCbEcU8QIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcU8QIAAAAAAA==/", "_etag": "\"a4003b8d-0000-0100-0000-6870212a0000\"", "_attachments": "attachments/", "_ts": 1752178986}, {"payPeriodId": "1090068472749070", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-04-14T00:00:00Z", "endDate": "2025-04-27T00:00:00Z", "submitByDate": "2025-04-30T00:00:00Z", "checkDate": "2025-05-02T00:00:00Z", "checkCount": 5, "id": "d1ac59a8-c78a-4bd2-9d75-21fe9e19ad33", "companyId": "70137182", "type": "payperiod", "_rid": "NmJkAKiCbEcV8QIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcV8QIAAAAAAA==/", "_etag": "\"a4003f8d-0000-0100-0000-6870212a0000\"", "_attachments": "attachments/", "_ts": 1752178986}, {"payPeriodId": "1090068767892050", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-04-28T00:00:00Z", "endDate": "2025-05-11T00:00:00Z", "submitByDate": "2025-05-14T00:00:00Z", "checkDate": "2025-05-16T00:00:00Z", "checkCount": 5, "id": "b6569e2c-886b-4f08-addd-dd4cdecfcfeb", "companyId": "70137182", "type": "payperiod", "_rid": "NmJkAKiCbEcW8QIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcW8QIAAAAAAA==/", "_etag": "\"a400458d-0000-0100-0000-6870212b0000\"", "_attachments": "attachments/", "_ts": 1752178987}, {"payPeriodId": "1090069068198760", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-05-12T00:00:00Z", "endDate": "2025-05-25T00:00:00Z", "submitByDate": "2025-05-28T00:00:00Z", "checkDate": "2025-05-30T00:00:00Z", "checkCount": 5, "id": "e062395a-fbd4-4674-b2f1-37c4217b4728", "companyId": "70137182", "type": "payperiod", "_rid": "NmJkAKiCbEcX8QIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcX8QIAAAAAAA==/", "_etag": "\"a400478d-0000-0100-0000-6870212b0000\"", "_attachments": "attachments/", "_ts": 1752178987}, {"payPeriodId": "1090069372889754", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-05-26T00:00:00Z", "endDate": "2025-06-08T00:00:00Z", "submitByDate": "2025-06-11T00:00:00Z", "checkDate": "2025-06-13T00:00:00Z", "checkCount": 5, "id": "6f2b527a-46cd-4105-9db7-1305bc5d38f2", "companyId": "70137182", "type": "payperiod", "_rid": "NmJkAKiCbEcY8QIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcY8QIAAAAAAA==/", "_etag": "\"a400488d-0000-0100-0000-6870212b0000\"", "_attachments": "attachments/", "_ts": 1752178987}, {"payPeriodId": "1090069676497003", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-06-09T00:00:00Z", "endDate": "2025-06-22T00:00:00Z", "submitByDate": "2025-06-25T00:00:00Z", "checkDate": "2025-06-27T00:00:00Z", "checkCount": 5, "id": "4697f8d5-db97-4e24-a4a0-678d158c0baa", "companyId": "70137182", "type": "payperiod", "_rid": "NmJkAKiCbEcZ8QIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcZ8QIAAAAAAA==/", "_etag": "\"a4004b8d-0000-0100-0000-6870212b0000\"", "_attachments": "attachments/", "_ts": 1752178987}, {"payPeriodId": "1090069981444036", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-06-23T00:00:00Z", "endDate": "2025-07-06T00:00:00Z", "submitByDate": "2025-07-09T00:00:00Z", "checkDate": "2025-07-11T00:00:00Z", "checkCount": 0, "id": "07769e24-aee7-4631-a064-48d1c686e35d", "companyId": "70137182", "type": "payperiod", "_rid": "NmJkAKiCbEca8QIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEca8QIAAAAAAA==/", "_etag": "\"a400508d-0000-0100-0000-6870212b0000\"", "_attachments": "attachments/", "_ts": 1752178987}, {"payPeriodId": "1090070288676198", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-07-07T00:00:00Z", "endDate": "2025-07-20T00:00:00Z", "submitByDate": "2025-07-23T00:00:00Z", "checkDate": "2025-07-25T00:00:00Z", "checkCount": 0, "id": "183edd6a-3da9-4adf-b2ad-627e9d523e62", "companyId": "70137182", "type": "payperiod", "_rid": "NmJkAKiCbEcb8QIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcb8QIAAAAAAA==/", "_etag": "\"a400578d-0000-0100-0000-6870212b0000\"", "_attachments": "attachments/", "_ts": 1752178987}, {"payPeriodId": "1090070592018802", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-07-21T00:00:00Z", "endDate": "2025-08-03T00:00:00Z", "submitByDate": "2025-08-06T00:00:00Z", "checkDate": "2025-08-08T00:00:00Z", "checkCount": 0, "id": "ea146565-4722-44c2-927a-6b8e40f93d1f", "companyId": "70137182", "type": "payperiod", "_rid": "NmJkAKiCbEcc8QIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcc8QIAAAAAAA==/", "_etag": "\"a400588d-0000-0100-0000-6870212b0000\"", "_attachments": "attachments/", "_ts": 1752178987}, {"payPeriodId": "1090070902147189", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-08-04T00:00:00Z", "endDate": "2025-08-17T00:00:00Z", "submitByDate": "2025-08-20T00:00:00Z", "checkDate": "2025-08-22T00:00:00Z", "checkCount": 0, "id": "93f54d10-7253-42c2-8114-dc2c2d5b50c4", "companyId": "70137182", "type": "payperiod", "_rid": "NmJkAKiCbEcd8QIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcd8QIAAAAAAA==/", "_etag": "\"a4005b8d-0000-0100-0000-6870212b0000\"", "_attachments": "attachments/", "_ts": 1752178987}, {"payPeriodId": "1090071221969150", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-08-18T00:00:00Z", "endDate": "2025-08-31T00:00:00Z", "submitByDate": "2025-09-03T00:00:00Z", "checkDate": "2025-09-05T00:00:00Z", "checkCount": 0, "id": "94e7039d-b8d9-4b2f-a0cf-ace7a56af3a5", "companyId": "70137182", "type": "payperiod", "_rid": "NmJkAKiCbEce8QIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEce8QIAAAAAAA==/", "_etag": "\"a400608d-0000-0100-0000-6870212b0000\"", "_attachments": "attachments/", "_ts": 1752178987}, {"payPeriodId": "1090071527028849", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-09-01T00:00:00Z", "endDate": "2025-09-14T00:00:00Z", "submitByDate": "2025-09-17T00:00:00Z", "checkDate": "2025-09-19T00:00:00Z", "checkCount": 0, "id": "7199cef5-1823-4e04-9861-b4866849aeed", "companyId": "70137182", "type": "payperiod", "_rid": "NmJkAKiCbEcf8QIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcf8QIAAAAAAA==/", "_etag": "\"a4006d8d-0000-0100-0000-6870212b0000\"", "_attachments": "attachments/", "_ts": 1752178987}, {"payPeriodId": "1090071838406463", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-09-15T00:00:00Z", "endDate": "2025-09-28T00:00:00Z", "submitByDate": "2025-10-01T00:00:00Z", "checkDate": "2025-10-03T00:00:00Z", "checkCount": 0, "id": "d19a88b1-be78-4e74-a3b9-3b7a3a648102", "companyId": "70137182", "type": "payperiod", "_rid": "NmJkAKiCbEcg8QIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcg8QIAAAAAAA==/", "_etag": "\"a400758d-0000-0100-0000-6870212b0000\"", "_attachments": "attachments/", "_ts": 1752178987}, {"payPeriodId": "1090072149908002", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-09-29T00:00:00Z", "endDate": "2025-10-12T00:00:00Z", "submitByDate": "2025-10-15T00:00:00Z", "checkDate": "2025-10-17T00:00:00Z", "checkCount": 0, "id": "507d8d6d-19d8-4073-9c1a-f5a3c50c2df5", "companyId": "70137182", "type": "payperiod", "_rid": "NmJkAKiCbEch8QIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEch8QIAAAAAAA==/", "_etag": "\"a400798d-0000-0100-0000-6870212b0000\"", "_attachments": "attachments/", "_ts": 1752178987}], "links": [{"rel": "self", "href": "https://ca-payroll-ai-mock-sb-001-secure.nicebeach-8a7a52ab.eastus.azurecontainerapps.io/ca/companies/70137182/payperiods"}]}, "status_code": 200}