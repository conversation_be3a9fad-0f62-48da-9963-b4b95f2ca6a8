{"success": true, "company_id": "70159951", "data": {"metadata": {"contentItemCount": 42}, "content": [{"payPeriodId": "1050102657854044", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2024-12-19T00:00:00Z", "endDate": "2025-01-01T00:00:00Z", "submitByDate": "2024-12-31T00:00:00Z", "checkDate": "2025-01-03T00:00:00Z", "checkCount": 2, "id": "a2b44c0b-b7ae-436f-9e4a-110d93dc9cda", "companyId": "70159951", "type": "payperiod", "_rid": "NmJkAKiCbEdRKgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdRKgMAAAAAAA==/", "_etag": "\"a5003d3e-0000-0100-0000-687025cb0000\"", "_attachments": "attachments/", "_ts": 1752180171}, {"payPeriodId": "1050103135073450", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-01-02T00:00:00Z", "endDate": "2025-01-15T00:00:00Z", "submitByDate": "2025-01-15T00:00:00Z", "checkDate": "2025-01-17T00:00:00Z", "checkCount": 2, "id": "1fa6c41a-b46a-4ee8-93bf-48e763a2865b", "companyId": "70159951", "type": "payperiod", "_rid": "NmJkAKiCbEdSKgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdSKgMAAAAAAA==/", "_etag": "\"a500403e-0000-0100-0000-687025cb0000\"", "_attachments": "attachments/", "_ts": 1752180171}, {"payPeriodId": "1050103609611519", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-01-16T00:00:00Z", "endDate": "2025-01-29T00:00:00Z", "submitByDate": "2025-01-29T00:00:00Z", "checkDate": "2025-01-31T00:00:00Z", "checkCount": 2, "id": "e4aaeb89-80cd-4fd0-bc13-701f24bcf9d9", "companyId": "70159951", "type": "payperiod", "_rid": "NmJkAKiCbEdTKgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdTKgMAAAAAAA==/", "_etag": "\"a500443e-0000-0100-0000-687025cb0000\"", "_attachments": "attachments/", "_ts": 1752180171}, {"payPeriodId": "1050104093073930", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-01-30T00:00:00Z", "endDate": "2025-02-12T00:00:00Z", "submitByDate": "2025-02-12T00:00:00Z", "checkDate": "2025-02-14T00:00:00Z", "checkCount": 2, "id": "0fd67ca6-3676-4171-a050-3603ddbe020d", "companyId": "70159951", "type": "payperiod", "_rid": "NmJkAKiCbEdUKgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdUKgMAAAAAAA==/", "_etag": "\"a5004a3e-0000-0100-0000-687025cb0000\"", "_attachments": "attachments/", "_ts": 1752180171}, {"payPeriodId": "1050104568756011", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-02-13T00:00:00Z", "endDate": "2025-02-26T00:00:00Z", "submitByDate": "2025-02-26T00:00:00Z", "checkDate": "2025-02-28T00:00:00Z", "checkCount": 2, "id": "f2e3d20c-c1b6-4a06-a86d-b169c9669827", "companyId": "70159951", "type": "payperiod", "_rid": "NmJkAKiCbEdVKgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdVKgMAAAAAAA==/", "_etag": "\"a5004e3e-0000-0100-0000-687025cb0000\"", "_attachments": "attachments/", "_ts": 1752180171}, {"payPeriodId": "1050105037074713", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-02-27T00:00:00Z", "endDate": "2025-03-12T00:00:00Z", "submitByDate": "2025-03-12T00:00:00Z", "checkDate": "2025-03-14T00:00:00Z", "checkCount": 2, "id": "05a06b26-f681-4f74-9ee4-9d53b5f5dd5d", "companyId": "70159951", "type": "payperiod", "_rid": "NmJkAKiCbEdWKgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdWKgMAAAAAAA==/", "_etag": "\"a500523e-0000-0100-0000-687025cb0000\"", "_attachments": "attachments/", "_ts": 1752180171}, {"payPeriodId": "1050105547467167", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-03-13T00:00:00Z", "endDate": "2025-03-26T00:00:00Z", "submitByDate": "2025-03-26T00:00:00Z", "checkDate": "2025-03-28T00:00:00Z", "checkCount": 2, "id": "f8883a20-e8e0-43eb-9520-88d19a9b1f87", "companyId": "70159951", "type": "payperiod", "_rid": "NmJkAKiCbEdXKgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdXKgMAAAAAAA==/", "_etag": "\"a500603e-0000-0100-0000-687025cb0000\"", "_attachments": "attachments/", "_ts": 1752180171}, {"payPeriodId": "1050106053904880", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-03-27T00:00:00Z", "endDate": "2025-04-09T00:00:00Z", "submitByDate": "2025-04-09T00:00:00Z", "checkDate": "2025-04-11T00:00:00Z", "checkCount": 0, "id": "0e1c70b8-642a-47a9-9358-f63702db3650", "companyId": "70159951", "type": "payperiod", "_rid": "NmJkAKiCbEdYKgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdYKgMAAAAAAA==/", "_etag": "\"a500663e-0000-0100-0000-687025cb0000\"", "_attachments": "attachments/", "_ts": 1752180171}, {"payPeriodId": "1050106539910531", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-04-10T00:00:00Z", "endDate": "2025-04-23T00:00:00Z", "submitByDate": "2025-04-23T00:00:00Z", "checkDate": "2025-04-25T00:00:00Z", "checkCount": 0, "id": "64ed5927-86e1-4081-9ced-01fb01c77c85", "companyId": "70159951", "type": "payperiod", "_rid": "NmJkAKiCbEdZKgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdZKgMAAAAAAA==/", "_etag": "\"a5006c3e-0000-0100-0000-687025cb0000\"", "_attachments": "attachments/", "_ts": 1752180171}, {"payPeriodId": "1050107011148194", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-04-24T00:00:00Z", "endDate": "2025-05-07T00:00:00Z", "submitByDate": "2025-05-07T00:00:00Z", "checkDate": "2025-05-09T00:00:00Z", "checkCount": 0, "id": "8f4d0184-86a9-4f1e-b230-e5c388f99cb6", "companyId": "70159951", "type": "payperiod", "_rid": "NmJkAKiCbEdaKgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdaKgMAAAAAAA==/", "_etag": "\"a5006e3e-0000-0100-0000-687025cb0000\"", "_attachments": "attachments/", "_ts": 1752180171}, {"payPeriodId": "1050107499693462", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-05-08T00:00:00Z", "endDate": "2025-05-21T00:00:00Z", "submitByDate": "2025-05-21T00:00:00Z", "checkDate": "2025-05-23T00:00:00Z", "checkCount": 0, "id": "6d34568b-7b22-4790-9eda-f1ac5b91713b", "companyId": "70159951", "type": "payperiod", "_rid": "NmJkAKiCbEdbKgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdbKgMAAAAAAA==/", "_etag": "\"a500743e-0000-0100-0000-687025cc0000\"", "_attachments": "attachments/", "_ts": 1752180172}, {"payPeriodId": "1050107973852108", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-05-22T00:00:00Z", "endDate": "2025-06-04T00:00:00Z", "submitByDate": "2025-06-04T00:00:00Z", "checkDate": "2025-06-06T00:00:00Z", "checkCount": 0, "id": "182b3443-ae97-4840-895e-8c00932cd1cd", "companyId": "70159951", "type": "payperiod", "_rid": "NmJkAKiCbEdcKgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdcKgMAAAAAAA==/", "_etag": "\"a5007b3e-0000-0100-0000-687025cc0000\"", "_attachments": "attachments/", "_ts": 1752180172}, {"payPeriodId": "1050108448980262", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-06-05T00:00:00Z", "endDate": "2025-06-18T00:00:00Z", "submitByDate": "2025-06-18T00:00:00Z", "checkDate": "2025-06-20T00:00:00Z", "checkCount": 0, "id": "aaffd8c5-de24-4020-939e-7236b0369d32", "companyId": "70159951", "type": "payperiod", "_rid": "NmJkAKiCbEddKgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEddKgMAAAAAAA==/", "_etag": "\"a500833e-0000-0100-0000-687025cc0000\"", "_attachments": "attachments/", "_ts": 1752180172}, {"payPeriodId": "1050108918911017", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-06-19T00:00:00Z", "endDate": "2025-07-02T00:00:00Z", "submitByDate": "2025-07-01T00:00:00Z", "checkDate": "2025-07-03T00:00:00Z", "checkCount": 0, "id": "0cf2584e-c4bd-47ab-b469-cbc2b77b17c5", "companyId": "70159951", "type": "payperiod", "_rid": "NmJkAKiCbEdeKgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdeKgMAAAAAAA==/", "_etag": "\"a500843e-0000-0100-0000-687025cc0000\"", "_attachments": "attachments/", "_ts": 1752180172}, {"payPeriodId": "1050109420461272", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-07-03T00:00:00Z", "endDate": "2025-07-16T00:00:00Z", "submitByDate": "2025-07-16T00:00:00Z", "checkDate": "2025-07-18T00:00:00Z", "checkCount": 0, "id": "ec252c20-6c0d-4f2e-9e66-1b93883af1a5", "companyId": "70159951", "type": "payperiod", "_rid": "NmJkAKiCbEdfKgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdfKgMAAAAAAA==/", "_etag": "\"a500883e-0000-0100-0000-687025cc0000\"", "_attachments": "attachments/", "_ts": 1752180172}, {"payPeriodId": "1050109909696974", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-07-17T00:00:00Z", "endDate": "2025-07-30T00:00:00Z", "submitByDate": "2025-07-30T00:00:00Z", "checkDate": "2025-08-01T00:00:00Z", "checkCount": 0, "id": "0e24b4c7-f1b8-41de-bca1-5f3bad56379a", "companyId": "70159951", "type": "payperiod", "_rid": "NmJkAKiCbEdgKgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdgKgMAAAAAAA==/", "_etag": "\"a5008c3e-0000-0100-0000-687025cc0000\"", "_attachments": "attachments/", "_ts": 1752180172}, {"payPeriodId": "1050110397991234", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-07-31T00:00:00Z", "endDate": "2025-08-13T00:00:00Z", "submitByDate": "2025-08-13T00:00:00Z", "checkDate": "2025-08-15T00:00:00Z", "checkCount": 0, "id": "a806904f-8a35-4fd1-9cf3-2486ba1688af", "companyId": "70159951", "type": "payperiod", "_rid": "NmJkAKiCbEdhKgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdhKgMAAAAAAA==/", "_etag": "\"a500903e-0000-0100-0000-687025cc0000\"", "_attachments": "attachments/", "_ts": 1752180172}, {"payPeriodId": "1050110890362971", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-08-14T00:00:00Z", "endDate": "2025-08-27T00:00:00Z", "submitByDate": "2025-08-27T00:00:00Z", "checkDate": "2025-08-29T00:00:00Z", "checkCount": 0, "id": "29a92ac9-810b-4c9d-b101-5afe49386da8", "companyId": "70159951", "type": "payperiod", "_rid": "NmJkAKiCbEdiKgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdiKgMAAAAAAA==/", "_etag": "\"a500963e-0000-0100-0000-687025cc0000\"", "_attachments": "attachments/", "_ts": 1752180172}, {"payPeriodId": "1050111389044346", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-08-28T00:00:00Z", "endDate": "2025-09-10T00:00:00Z", "submitByDate": "2025-09-10T00:00:00Z", "checkDate": "2025-09-12T00:00:00Z", "checkCount": 0, "id": "fc6dabd1-86dc-4021-a165-4c1c553749d4", "companyId": "70159951", "type": "payperiod", "_rid": "NmJkAKiCbEdjKgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdjKgMAAAAAAA==/", "_etag": "\"a5009c3e-0000-0100-0000-687025cc0000\"", "_attachments": "attachments/", "_ts": 1752180172}, {"payPeriodId": "1050111895155162", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-09-11T00:00:00Z", "endDate": "2025-09-24T00:00:00Z", "submitByDate": "2025-09-24T00:00:00Z", "checkDate": "2025-09-26T00:00:00Z", "checkCount": 0, "id": "6fc0850b-7989-42f7-ac01-24bb40f6d47e", "companyId": "70159951", "type": "payperiod", "_rid": "NmJkAKiCbEdkKgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdkKgMAAAAAAA==/", "_etag": "\"a5009d3e-0000-0100-0000-687025cc0000\"", "_attachments": "attachments/", "_ts": 1752180172}, {"payPeriodId": "1050112397706810", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-09-25T00:00:00Z", "endDate": "2025-10-08T00:00:00Z", "submitByDate": "2025-10-08T00:00:00Z", "checkDate": "2025-10-10T00:00:00Z", "checkCount": 0, "id": "0b197ea0-b6b4-42ab-abf0-29c4cb098a0e", "companyId": "70159951", "type": "payperiod", "_rid": "NmJkAKiCbEdlKgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdlKgMAAAAAAA==/", "_etag": "\"a500a23e-0000-0100-0000-687025cc0000\"", "_attachments": "attachments/", "_ts": 1752180172}, {"payPeriodId": "1050102657854044", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2024-12-19T00:00:00Z", "endDate": "2025-01-01T00:00:00Z", "submitByDate": "2024-12-31T00:00:00Z", "checkDate": "2025-01-03T00:00:00Z", "checkCount": 2, "id": "042af97d-eae2-41a0-a38a-896e467e8727", "companyId": "70159951", "type": "payperiod", "_rid": "NmJkAKiCbEdsKgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdsKgMAAAAAAA==/", "_etag": "\"a500c13e-0000-0100-0000-687025cd0000\"", "_attachments": "attachments/", "_ts": 1752180173}, {"payPeriodId": "1050103135073450", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-01-02T00:00:00Z", "endDate": "2025-01-15T00:00:00Z", "submitByDate": "2025-01-15T00:00:00Z", "checkDate": "2025-01-17T00:00:00Z", "checkCount": 2, "id": "a7b3e9ba-3efa-4689-b721-c013b75d9dc5", "companyId": "70159951", "type": "payperiod", "_rid": "NmJkAKiCbEdtKgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdtKgMAAAAAAA==/", "_etag": "\"a500c83e-0000-0100-0000-687025cd0000\"", "_attachments": "attachments/", "_ts": 1752180173}, {"payPeriodId": "1050103609611519", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-01-16T00:00:00Z", "endDate": "2025-01-29T00:00:00Z", "submitByDate": "2025-01-29T00:00:00Z", "checkDate": "2025-01-31T00:00:00Z", "checkCount": 2, "id": "3acbe6bb-2600-4b55-9571-2d8792894860", "companyId": "70159951", "type": "payperiod", "_rid": "NmJkAKiCbEduKgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEduKgMAAAAAAA==/", "_etag": "\"a500ca3e-0000-0100-0000-687025cd0000\"", "_attachments": "attachments/", "_ts": 1752180173}, {"payPeriodId": "1050104093073930", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-01-30T00:00:00Z", "endDate": "2025-02-12T00:00:00Z", "submitByDate": "2025-02-12T00:00:00Z", "checkDate": "2025-02-14T00:00:00Z", "checkCount": 2, "id": "dbfabb25-030e-4437-a822-3f979bfff303", "companyId": "70159951", "type": "payperiod", "_rid": "NmJkAKiCbEdvKgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdvKgMAAAAAAA==/", "_etag": "\"a500cc3e-0000-0100-0000-687025cd0000\"", "_attachments": "attachments/", "_ts": 1752180173}, {"payPeriodId": "1050104568756011", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-02-13T00:00:00Z", "endDate": "2025-02-26T00:00:00Z", "submitByDate": "2025-02-26T00:00:00Z", "checkDate": "2025-02-28T00:00:00Z", "checkCount": 2, "id": "dbbc8711-0dd9-40ca-8264-2ff9198a9271", "companyId": "70159951", "type": "payperiod", "_rid": "NmJkAKiCbEdwKgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdwKgMAAAAAAA==/", "_etag": "\"a500cf3e-0000-0100-0000-687025cd0000\"", "_attachments": "attachments/", "_ts": 1752180173}, {"payPeriodId": "1050105037074713", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-02-27T00:00:00Z", "endDate": "2025-03-12T00:00:00Z", "submitByDate": "2025-03-12T00:00:00Z", "checkDate": "2025-03-14T00:00:00Z", "checkCount": 2, "id": "7d034e37-90f6-462d-9344-77ad5046b74e", "companyId": "70159951", "type": "payperiod", "_rid": "NmJkAKiCbEdxKgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdxKgMAAAAAAA==/", "_etag": "\"a500d33e-0000-0100-0000-687025cd0000\"", "_attachments": "attachments/", "_ts": 1752180173}, {"payPeriodId": "1050105547467167", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-03-13T00:00:00Z", "endDate": "2025-03-26T00:00:00Z", "submitByDate": "2025-03-26T00:00:00Z", "checkDate": "2025-03-28T00:00:00Z", "checkCount": 2, "id": "c80c1275-de4d-4d89-883e-671f8ed18638", "companyId": "70159951", "type": "payperiod", "_rid": "NmJkAKiCbEdyKgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdyKgMAAAAAAA==/", "_etag": "\"a500d53e-0000-0100-0000-687025cd0000\"", "_attachments": "attachments/", "_ts": 1752180173}, {"payPeriodId": "1050106053904880", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-03-27T00:00:00Z", "endDate": "2025-04-09T00:00:00Z", "submitByDate": "2025-04-09T00:00:00Z", "checkDate": "2025-04-11T00:00:00Z", "checkCount": 2, "id": "36715dde-1960-4455-a8f4-b50cd8ac53b8", "companyId": "70159951", "type": "payperiod", "_rid": "NmJkAKiCbEdzKgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdzKgMAAAAAAA==/", "_etag": "\"a500d73e-0000-0100-0000-687025cd0000\"", "_attachments": "attachments/", "_ts": 1752180173}, {"payPeriodId": "1050106539910531", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-04-10T00:00:00Z", "endDate": "2025-04-23T00:00:00Z", "submitByDate": "2025-04-23T00:00:00Z", "checkDate": "2025-04-25T00:00:00Z", "checkCount": 2, "id": "edb097e7-031c-4f4a-a715-2e1519d37328", "companyId": "70159951", "type": "payperiod", "_rid": "NmJkAKiCbEd0KgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEd0KgMAAAAAAA==/", "_etag": "\"a500da3e-0000-0100-0000-687025cd0000\"", "_attachments": "attachments/", "_ts": 1752180173}, {"payPeriodId": "1050107011148194", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-04-24T00:00:00Z", "endDate": "2025-05-07T00:00:00Z", "submitByDate": "2025-05-07T00:00:00Z", "checkDate": "2025-05-09T00:00:00Z", "checkCount": 2, "id": "d5ccced3-7778-4730-841d-04d7fb5940ef", "companyId": "70159951", "type": "payperiod", "_rid": "NmJkAKiCbEd1KgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEd1KgMAAAAAAA==/", "_etag": "\"a500dd3e-0000-0100-0000-687025ce0000\"", "_attachments": "attachments/", "_ts": 1752180174}, {"payPeriodId": "1050107499693462", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-05-08T00:00:00Z", "endDate": "2025-05-21T00:00:00Z", "submitByDate": "2025-05-21T00:00:00Z", "checkDate": "2025-05-23T00:00:00Z", "checkCount": 2, "id": "13537d5a-c827-4193-896d-73a530a70893", "companyId": "70159951", "type": "payperiod", "_rid": "NmJkAKiCbEd2KgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEd2KgMAAAAAAA==/", "_etag": "\"a500df3e-0000-0100-0000-687025ce0000\"", "_attachments": "attachments/", "_ts": 1752180174}, {"payPeriodId": "1050107973852108", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-05-22T00:00:00Z", "endDate": "2025-06-04T00:00:00Z", "submitByDate": "2025-06-04T00:00:00Z", "checkDate": "2025-06-06T00:00:00Z", "checkCount": 2, "id": "0601d3f5-ee69-4aa5-9ef3-185b0d538fe3", "companyId": "70159951", "type": "payperiod", "_rid": "NmJkAKiCbEd3KgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEd3KgMAAAAAAA==/", "_etag": "\"a500e13e-0000-0100-0000-687025ce0000\"", "_attachments": "attachments/", "_ts": 1752180174}, {"payPeriodId": "1050108448980262", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-06-05T00:00:00Z", "endDate": "2025-06-18T00:00:00Z", "submitByDate": "2025-06-18T00:00:00Z", "checkDate": "2025-06-20T00:00:00Z", "checkCount": 2, "id": "6657cc5c-4fc8-4868-b398-b80300869ef9", "companyId": "70159951", "type": "payperiod", "_rid": "NmJkAKiCbEd4KgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEd4KgMAAAAAAA==/", "_etag": "\"a500e23e-0000-0100-0000-687025ce0000\"", "_attachments": "attachments/", "_ts": 1752180174}, {"payPeriodId": "1050108918911017", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-06-19T00:00:00Z", "endDate": "2025-07-02T00:00:00Z", "submitByDate": "2025-07-01T00:00:00Z", "checkDate": "2025-07-03T00:00:00Z", "checkCount": 2, "id": "1f80fa66-acfb-4095-8d58-40245b30355f", "companyId": "70159951", "type": "payperiod", "_rid": "NmJkAKiCbEd5KgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEd5KgMAAAAAAA==/", "_etag": "\"a500e83e-0000-0100-0000-687025ce0000\"", "_attachments": "attachments/", "_ts": 1752180174}, {"payPeriodId": "1050109420461272", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-07-03T00:00:00Z", "endDate": "2025-07-16T00:00:00Z", "submitByDate": "2025-07-16T00:00:00Z", "checkDate": "2025-07-18T00:00:00Z", "checkCount": 0, "id": "1b91f850-247d-4947-8095-9783122d301c", "companyId": "70159951", "type": "payperiod", "_rid": "NmJkAKiCbEd6KgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEd6KgMAAAAAAA==/", "_etag": "\"a500eb3e-0000-0100-0000-687025ce0000\"", "_attachments": "attachments/", "_ts": 1752180174}, {"payPeriodId": "1050109909696974", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-07-17T00:00:00Z", "endDate": "2025-07-30T00:00:00Z", "submitByDate": "2025-07-30T00:00:00Z", "checkDate": "2025-08-01T00:00:00Z", "checkCount": 0, "id": "d3e5646d-9d41-4dfe-883b-f423024fe3d7", "companyId": "70159951", "type": "payperiod", "_rid": "NmJkAKiCbEd7KgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEd7KgMAAAAAAA==/", "_etag": "\"a500ec3e-0000-0100-0000-687025ce0000\"", "_attachments": "attachments/", "_ts": 1752180174}, {"payPeriodId": "1050110397991234", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-07-31T00:00:00Z", "endDate": "2025-08-13T00:00:00Z", "submitByDate": "2025-08-13T00:00:00Z", "checkDate": "2025-08-15T00:00:00Z", "checkCount": 0, "id": "81d7854d-c12e-4dd4-a45f-b664b1d49c7e", "companyId": "70159951", "type": "payperiod", "_rid": "NmJkAKiCbEd8KgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEd8KgMAAAAAAA==/", "_etag": "\"a500ed3e-0000-0100-0000-687025ce0000\"", "_attachments": "attachments/", "_ts": 1752180174}, {"payPeriodId": "1050110890362971", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-08-14T00:00:00Z", "endDate": "2025-08-27T00:00:00Z", "submitByDate": "2025-08-27T00:00:00Z", "checkDate": "2025-08-29T00:00:00Z", "checkCount": 0, "id": "197bfe7f-b01c-42a2-af0a-b4819f41248b", "companyId": "70159951", "type": "payperiod", "_rid": "NmJkAKiCbEd9KgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEd9KgMAAAAAAA==/", "_etag": "\"a500f03e-0000-0100-0000-687025ce0000\"", "_attachments": "attachments/", "_ts": 1752180174}, {"payPeriodId": "1050111389044346", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-08-28T00:00:00Z", "endDate": "2025-09-10T00:00:00Z", "submitByDate": "2025-09-10T00:00:00Z", "checkDate": "2025-09-12T00:00:00Z", "checkCount": 0, "id": "76397f41-e4ad-4413-b498-5093fed0cf04", "companyId": "70159951", "type": "payperiod", "_rid": "NmJkAKiCbEd+KgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEd+KgMAAAAAAA==/", "_etag": "\"a500f33e-0000-0100-0000-687025ce0000\"", "_attachments": "attachments/", "_ts": 1752180174}, {"payPeriodId": "1050111895155162", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-09-11T00:00:00Z", "endDate": "2025-09-24T00:00:00Z", "submitByDate": "2025-09-24T00:00:00Z", "checkDate": "2025-09-26T00:00:00Z", "checkCount": 0, "id": "98357c9c-53f8-42cb-9218-777a94a90d0e", "companyId": "70159951", "type": "payperiod", "_rid": "NmJkAKiCbEd-KgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEd-KgMAAAAAAA==/", "_etag": "\"a500f53e-0000-0100-0000-687025ce0000\"", "_attachments": "attachments/", "_ts": 1752180174}, {"payPeriodId": "1050112397706810", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-09-25T00:00:00Z", "endDate": "2025-10-08T00:00:00Z", "submitByDate": "2025-10-08T00:00:00Z", "checkDate": "2025-10-10T00:00:00Z", "checkCount": 0, "id": "6e178c3a-df11-4b3c-b364-ce925d9985e1", "companyId": "70159951", "type": "payperiod", "_rid": "NmJkAKiCbEeAKgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeAKgMAAAAAAA==/", "_etag": "\"a500f93e-0000-0100-0000-687025ce0000\"", "_attachments": "attachments/", "_ts": 1752180174}], "links": [{"rel": "self", "href": "https://ca-payroll-ai-mock-sb-001-secure.nicebeach-8a7a52ab.eastus.azurecontainerapps.io/ca/companies/70159951/payperiods"}]}, "status_code": 200}