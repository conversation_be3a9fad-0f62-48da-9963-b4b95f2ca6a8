{"success": true, "company_id": "70214111", "data": {"metadata": {"contentItemCount": 32}, "content": [{"payPeriodId": "1030072300286283", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "1st PR", "startDate": "2025-03-01T00:00:00Z", "endDate": "2025-03-14T00:00:00Z", "submitByDate": "2025-03-14T00:00:00Z", "checkDate": "2025-03-18T00:00:00Z", "checkCount": 1, "id": "3c944e68-4532-4ad2-aec1-d95e03b403e2", "companyId": "70214111", "type": "payperiod", "_rid": "NmJkAKiCbEdkDgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdkDgMAAAAAAA==/", "_etag": "\"a4001fea-0000-0100-0000-6870238b0000\"", "_attachments": "attachments/", "_ts": 1752179595}, {"payPeriodId": "1030072300286286", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "2nd PR", "startDate": "2025-03-15T00:00:00Z", "endDate": "2025-03-28T00:00:00Z", "submitByDate": "2025-03-28T00:00:00Z", "checkDate": "2025-04-01T00:00:00Z", "checkCount": 0, "id": "0ab2a2b1-8541-48c5-856a-b979ae2a9edf", "companyId": "70214111", "type": "payperiod", "_rid": "NmJkAKiCbEdlDgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdlDgMAAAAAAA==/", "_etag": "\"a40020ea-0000-0100-0000-6870238b0000\"", "_attachments": "attachments/", "_ts": 1752179595}, {"payPeriodId": "1030072300286289", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-03-29T00:00:00Z", "endDate": "2025-04-11T00:00:00Z", "submitByDate": "2025-04-11T00:00:00Z", "checkDate": "2025-04-15T00:00:00Z", "checkCount": 0, "id": "8c0fcd27-e929-4f7f-96ad-cc7f17b77e07", "companyId": "70214111", "type": "payperiod", "_rid": "NmJkAKiCbEdmDgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdmDgMAAAAAAA==/", "_etag": "\"a40022ea-0000-0100-0000-6870238b0000\"", "_attachments": "attachments/", "_ts": 1752179595}, {"payPeriodId": "1030072300286292", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-04-12T00:00:00Z", "endDate": "2025-04-25T00:00:00Z", "submitByDate": "2025-04-25T00:00:00Z", "checkDate": "2025-04-29T00:00:00Z", "checkCount": 0, "id": "41f55b51-62bc-44d5-abb5-1a28682d9c4b", "companyId": "70214111", "type": "payperiod", "_rid": "NmJkAKiCbEdnDgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdnDgMAAAAAAA==/", "_etag": "\"a40023ea-0000-0100-0000-6870238b0000\"", "_attachments": "attachments/", "_ts": 1752179595}, {"payPeriodId": "1030072300286295", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-04-26T00:00:00Z", "endDate": "2025-05-09T00:00:00Z", "submitByDate": "2025-05-09T00:00:00Z", "checkDate": "2025-05-13T00:00:00Z", "checkCount": 0, "id": "235809ff-2670-430f-8577-ef6606fa4440", "companyId": "70214111", "type": "payperiod", "_rid": "NmJkAKiCbEdoDgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdoDgMAAAAAAA==/", "_etag": "\"a40027ea-0000-0100-0000-6870238b0000\"", "_attachments": "attachments/", "_ts": 1752179595}, {"payPeriodId": "1030072300286298", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-05-10T00:00:00Z", "endDate": "2025-05-23T00:00:00Z", "submitByDate": "2025-05-22T00:00:00Z", "checkDate": "2025-05-27T00:00:00Z", "checkCount": 0, "id": "873f6879-55f9-42e5-ae6c-30b969f1c0d5", "companyId": "70214111", "type": "payperiod", "_rid": "NmJkAKiCbEdpDgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdpDgMAAAAAAA==/", "_etag": "\"a4002aea-0000-0100-0000-6870238b0000\"", "_attachments": "attachments/", "_ts": 1752179595}, {"payPeriodId": "1030072300286301", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-05-24T00:00:00Z", "endDate": "2025-06-06T00:00:00Z", "submitByDate": "2025-06-06T00:00:00Z", "checkDate": "2025-06-10T00:00:00Z", "checkCount": 0, "id": "d297804e-4fb8-454c-8673-b648e55e7f03", "companyId": "70214111", "type": "payperiod", "_rid": "NmJkAKiCbEdqDgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdqDgMAAAAAAA==/", "_etag": "\"a4002bea-0000-0100-0000-6870238b0000\"", "_attachments": "attachments/", "_ts": 1752179595}, {"payPeriodId": "1030072353523392", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-06-07T00:00:00Z", "endDate": "2025-06-20T00:00:00Z", "submitByDate": "2025-06-20T00:00:00Z", "checkDate": "2025-06-24T00:00:00Z", "checkCount": 0, "id": "7fc79c1e-42b9-42c4-abd3-afa7f28ffb9d", "companyId": "70214111", "type": "payperiod", "_rid": "NmJkAKiCbEdrDgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdrDgMAAAAAAA==/", "_etag": "\"a4002eea-0000-0100-0000-6870238b0000\"", "_attachments": "attachments/", "_ts": 1752179595}, {"payPeriodId": "1030072553405835", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-06-21T00:00:00Z", "endDate": "2025-07-04T00:00:00Z", "submitByDate": "2025-07-03T00:00:00Z", "checkDate": "2025-07-08T00:00:00Z", "checkCount": 0, "id": "64e75e3d-a393-4f14-97e0-7bfd6f30f792", "companyId": "70214111", "type": "payperiod", "_rid": "NmJkAKiCbEdsDgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdsDgMAAAAAAA==/", "_etag": "\"a40032ea-0000-0100-0000-6870238b0000\"", "_attachments": "attachments/", "_ts": 1752179595}, {"payPeriodId": "1030072818675728", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-07-05T00:00:00Z", "endDate": "2025-07-18T00:00:00Z", "submitByDate": "2025-07-18T00:00:00Z", "checkDate": "2025-07-22T00:00:00Z", "checkCount": 0, "id": "222bfca7-3077-4ab5-a4c4-37c0d0b66234", "companyId": "70214111", "type": "payperiod", "_rid": "NmJkAKiCbEdtDgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdtDgMAAAAAAA==/", "_etag": "\"a40036ea-0000-0100-0000-6870238c0000\"", "_attachments": "attachments/", "_ts": 1752179596}, {"payPeriodId": "1030073096071971", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-07-19T00:00:00Z", "endDate": "2025-08-01T00:00:00Z", "submitByDate": "2025-08-01T00:00:00Z", "checkDate": "2025-08-05T00:00:00Z", "checkCount": 0, "id": "0def2320-48cf-4ada-a11b-65eeaf65167c", "companyId": "70214111", "type": "payperiod", "_rid": "NmJkAKiCbEduDgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEduDgMAAAAAAA==/", "_etag": "\"a4003bea-0000-0100-0000-6870238c0000\"", "_attachments": "attachments/", "_ts": 1752179596}, {"payPeriodId": "1030073376060402", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-08-02T00:00:00Z", "endDate": "2025-08-15T00:00:00Z", "submitByDate": "2025-08-15T00:00:00Z", "checkDate": "2025-08-19T00:00:00Z", "checkCount": 0, "id": "c7913ebc-baca-4ce7-b9fe-a86ad9f7cd76", "companyId": "70214111", "type": "payperiod", "_rid": "NmJkAKiCbEdvDgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdvDgMAAAAAAA==/", "_etag": "\"a4003dea-0000-0100-0000-6870238c0000\"", "_attachments": "attachments/", "_ts": 1752179596}, {"payPeriodId": "1030073648212586", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-08-16T00:00:00Z", "endDate": "2025-08-29T00:00:00Z", "submitByDate": "2025-08-28T00:00:00Z", "checkDate": "2025-09-02T00:00:00Z", "checkCount": 0, "id": "d3a784b5-0678-4304-838c-fd7b0496e4b1", "companyId": "70214111", "type": "payperiod", "_rid": "NmJkAKiCbEdwDgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdwDgMAAAAAAA==/", "_etag": "\"a40040ea-0000-0100-0000-6870238c0000\"", "_attachments": "attachments/", "_ts": 1752179596}, {"payPeriodId": "1030073936797096", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-08-30T00:00:00Z", "endDate": "2025-09-12T00:00:00Z", "submitByDate": "2025-09-12T00:00:00Z", "checkDate": "2025-09-16T00:00:00Z", "checkCount": 0, "id": "db805342-1938-4fc3-81c5-350e469762de", "companyId": "70214111", "type": "payperiod", "_rid": "NmJkAKiCbEdxDgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdxDgMAAAAAAA==/", "_etag": "\"a40046ea-0000-0100-0000-6870238c0000\"", "_attachments": "attachments/", "_ts": 1752179596}, {"payPeriodId": "1030074219296912", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-09-13T00:00:00Z", "endDate": "2025-09-26T00:00:00Z", "submitByDate": "2025-09-26T00:00:00Z", "checkDate": "2025-09-30T00:00:00Z", "checkCount": 0, "id": "cba4856a-0eb4-490e-a3ed-7430e406c4c9", "companyId": "70214111", "type": "payperiod", "_rid": "NmJkAKiCbEdyDgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdyDgMAAAAAAA==/", "_etag": "\"a4004dea-0000-0100-0000-6870238c0000\"", "_attachments": "attachments/", "_ts": 1752179596}, {"payPeriodId": "1030074500239081", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-09-27T00:00:00Z", "endDate": "2025-10-10T00:00:00Z", "submitByDate": "2025-10-10T00:00:00Z", "checkDate": "2025-10-14T00:00:00Z", "checkCount": 0, "id": "c88e9269-dc70-4c27-8f25-d1a40bff115f", "companyId": "70214111", "type": "payperiod", "_rid": "NmJkAKiCbEdzDgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdzDgMAAAAAAA==/", "_etag": "\"a40050ea-0000-0100-0000-6870238c0000\"", "_attachments": "attachments/", "_ts": 1752179596}, {"payPeriodId": "1030072300286283", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "1st PR", "startDate": "2025-03-01T00:00:00Z", "endDate": "2025-03-14T00:00:00Z", "submitByDate": "2025-03-14T00:00:00Z", "checkDate": "2025-03-18T00:00:00Z", "checkCount": 1, "id": "d23bbc93-1275-4eed-84a2-3f1fda5a7997", "companyId": "70214111", "type": "payperiod", "_rid": "NmJkAKiCbEd1DgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEd1DgMAAAAAAA==/", "_etag": "\"a40059ea-0000-0100-0000-6870238c0000\"", "_attachments": "attachments/", "_ts": 1752179596}, {"payPeriodId": "1030072300286286", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "2nd PR", "startDate": "2025-03-15T00:00:00Z", "endDate": "2025-03-28T00:00:00Z", "submitByDate": "2025-03-28T00:00:00Z", "checkDate": "2025-04-01T00:00:00Z", "checkCount": 1, "id": "a5f1000e-c552-410b-92e6-1adb0404cf71", "companyId": "70214111", "type": "payperiod", "_rid": "NmJkAKiCbEd2DgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEd2DgMAAAAAAA==/", "_etag": "\"a4005aea-0000-0100-0000-6870238c0000\"", "_attachments": "attachments/", "_ts": 1752179596}, {"payPeriodId": "1030072300286289", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-03-29T00:00:00Z", "endDate": "2025-04-11T00:00:00Z", "submitByDate": "2025-04-11T00:00:00Z", "checkDate": "2025-04-15T00:00:00Z", "checkCount": 1, "id": "4f0c1cfa-fc44-40e9-b22d-6c8a53b7aad5", "companyId": "70214111", "type": "payperiod", "_rid": "NmJkAKiCbEd3DgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEd3DgMAAAAAAA==/", "_etag": "\"a4005fea-0000-0100-0000-6870238c0000\"", "_attachments": "attachments/", "_ts": 1752179596}, {"payPeriodId": "1030072300286292", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-04-12T00:00:00Z", "endDate": "2025-04-25T00:00:00Z", "submitByDate": "2025-04-25T00:00:00Z", "checkDate": "2025-04-29T00:00:00Z", "checkCount": 1, "id": "30508061-d08e-4bc0-a35f-4a971f8f9bb0", "companyId": "70214111", "type": "payperiod", "_rid": "NmJkAKiCbEd4DgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEd4DgMAAAAAAA==/", "_etag": "\"a40063ea-0000-0100-0000-6870238c0000\"", "_attachments": "attachments/", "_ts": 1752179596}, {"payPeriodId": "1030072300286295", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-04-26T00:00:00Z", "endDate": "2025-05-09T00:00:00Z", "submitByDate": "2025-05-09T00:00:00Z", "checkDate": "2025-05-13T00:00:00Z", "checkCount": 1, "id": "aafee742-3df4-4af9-b4f6-e89af5dfa3b6", "companyId": "70214111", "type": "payperiod", "_rid": "NmJkAKiCbEd5DgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEd5DgMAAAAAAA==/", "_etag": "\"a40066ea-0000-0100-0000-6870238d0000\"", "_attachments": "attachments/", "_ts": 1752179597}, {"payPeriodId": "1030072300286298", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-05-10T00:00:00Z", "endDate": "2025-05-23T00:00:00Z", "submitByDate": "2025-05-22T00:00:00Z", "checkDate": "2025-05-27T00:00:00Z", "checkCount": 1, "id": "ab227d5b-13af-49b1-a2fa-f7b97d0f8a38", "companyId": "70214111", "type": "payperiod", "_rid": "NmJkAKiCbEd6DgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEd6DgMAAAAAAA==/", "_etag": "\"a4006aea-0000-0100-0000-6870238d0000\"", "_attachments": "attachments/", "_ts": 1752179597}, {"payPeriodId": "1030072300286301", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-05-24T00:00:00Z", "endDate": "2025-06-06T00:00:00Z", "submitByDate": "2025-06-06T00:00:00Z", "checkDate": "2025-06-10T00:00:00Z", "checkCount": 1, "id": "36207182-c00d-4fea-b491-d9b09c3df888", "companyId": "70214111", "type": "payperiod", "_rid": "NmJkAKiCbEd7DgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEd7DgMAAAAAAA==/", "_etag": "\"a4006fea-0000-0100-0000-6870238d0000\"", "_attachments": "attachments/", "_ts": 1752179597}, {"payPeriodId": "1030072353523392", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-06-07T00:00:00Z", "endDate": "2025-06-20T00:00:00Z", "submitByDate": "2025-06-20T00:00:00Z", "checkDate": "2025-06-24T00:00:00Z", "checkCount": 1, "id": "eb748c8e-5d9d-4c3a-adfe-3b8d9348ab86", "companyId": "70214111", "type": "payperiod", "_rid": "NmJkAKiCbEd8DgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEd8DgMAAAAAAA==/", "_etag": "\"a40073ea-0000-0100-0000-6870238d0000\"", "_attachments": "attachments/", "_ts": 1752179597}, {"payPeriodId": "1030072553405835", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-06-21T00:00:00Z", "endDate": "2025-07-04T00:00:00Z", "submitByDate": "2025-07-03T00:00:00Z", "checkDate": "2025-07-08T00:00:00Z", "checkCount": 0, "id": "cf1602dc-9a0b-48e2-9dc1-81802d1e8a7e", "companyId": "70214111", "type": "payperiod", "_rid": "NmJkAKiCbEd9DgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEd9DgMAAAAAAA==/", "_etag": "\"a40075ea-0000-0100-0000-6870238d0000\"", "_attachments": "attachments/", "_ts": 1752179597}, {"payPeriodId": "1030072818675728", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-07-05T00:00:00Z", "endDate": "2025-07-18T00:00:00Z", "submitByDate": "2025-07-18T00:00:00Z", "checkDate": "2025-07-22T00:00:00Z", "checkCount": 0, "id": "411d3a41-7b1c-4c6b-bdbf-605279392513", "companyId": "70214111", "type": "payperiod", "_rid": "NmJkAKiCbEd+DgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEd+DgMAAAAAAA==/", "_etag": "\"a4007aea-0000-0100-0000-6870238d0000\"", "_attachments": "attachments/", "_ts": 1752179597}, {"payPeriodId": "1030073096071971", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-07-19T00:00:00Z", "endDate": "2025-08-01T00:00:00Z", "submitByDate": "2025-08-01T00:00:00Z", "checkDate": "2025-08-05T00:00:00Z", "checkCount": 0, "id": "7d971797-a068-4326-b49f-3e4c05eafd13", "companyId": "70214111", "type": "payperiod", "_rid": "NmJkAKiCbEd-DgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEd-DgMAAAAAAA==/", "_etag": "\"a4007dea-0000-0100-0000-6870238d0000\"", "_attachments": "attachments/", "_ts": 1752179597}, {"payPeriodId": "1030073376060402", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-08-02T00:00:00Z", "endDate": "2025-08-15T00:00:00Z", "submitByDate": "2025-08-15T00:00:00Z", "checkDate": "2025-08-19T00:00:00Z", "checkCount": 0, "id": "061dc04b-e3b5-4c9e-8a2a-14ff4c585e37", "companyId": "70214111", "type": "payperiod", "_rid": "NmJkAKiCbEeADgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeADgMAAAAAAA==/", "_etag": "\"a40083ea-0000-0100-0000-6870238d0000\"", "_attachments": "attachments/", "_ts": 1752179597}, {"payPeriodId": "1030073648212586", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-08-16T00:00:00Z", "endDate": "2025-08-29T00:00:00Z", "submitByDate": "2025-08-28T00:00:00Z", "checkDate": "2025-09-02T00:00:00Z", "checkCount": 0, "id": "a87ba4ab-b9c7-4877-988c-496bda5cf1b4", "companyId": "70214111", "type": "payperiod", "_rid": "NmJkAKiCbEeBDgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeBDgMAAAAAAA==/", "_etag": "\"a40089ea-0000-0100-0000-6870238d0000\"", "_attachments": "attachments/", "_ts": 1752179597}, {"payPeriodId": "1030073936797096", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-08-30T00:00:00Z", "endDate": "2025-09-12T00:00:00Z", "submitByDate": "2025-09-12T00:00:00Z", "checkDate": "2025-09-16T00:00:00Z", "checkCount": 0, "id": "680cd5cd-5c58-48fa-bd3a-ba776a36dcf2", "companyId": "70214111", "type": "payperiod", "_rid": "NmJkAKiCbEeCDgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeCDgMAAAAAAA==/", "_etag": "\"a4008cea-0000-0100-0000-6870238d0000\"", "_attachments": "attachments/", "_ts": 1752179597}, {"payPeriodId": "1030074219296912", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-09-13T00:00:00Z", "endDate": "2025-09-26T00:00:00Z", "submitByDate": "2025-09-26T00:00:00Z", "checkDate": "2025-09-30T00:00:00Z", "checkCount": 0, "id": "adf2b374-a7dd-411c-b20a-76637947e3c0", "companyId": "70214111", "type": "payperiod", "_rid": "NmJkAKiCbEeDDgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeDDgMAAAAAAA==/", "_etag": "\"a4008dea-0000-0100-0000-6870238d0000\"", "_attachments": "attachments/", "_ts": 1752179597}, {"payPeriodId": "1030074500239081", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-09-27T00:00:00Z", "endDate": "2025-10-10T00:00:00Z", "submitByDate": "2025-10-10T00:00:00Z", "checkDate": "2025-10-14T00:00:00Z", "checkCount": 0, "id": "6dc4e57a-1003-42a1-83b6-8a78d7356a12", "companyId": "70214111", "type": "payperiod", "_rid": "NmJkAKiCbEeEDgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeEDgMAAAAAAA==/", "_etag": "\"a4008fea-0000-0100-0000-6870238d0000\"", "_attachments": "attachments/", "_ts": 1752179597}], "links": [{"rel": "self", "href": "https://ca-payroll-ai-mock-sb-001-secure.nicebeach-8a7a52ab.eastus.azurecontainerapps.io/ca/companies/70214111/payperiods"}]}, "status_code": 200}