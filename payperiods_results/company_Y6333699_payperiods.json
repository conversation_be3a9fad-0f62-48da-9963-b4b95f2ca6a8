{"success": true, "company_id": "Y6333699", "data": {"metadata": {"contentItemCount": 36}, "content": [{"payPeriodId": "1030069263880448", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-01-06T00:00:00Z", "endDate": "2025-01-19T00:00:00Z", "submitByDate": "2025-01-16T00:00:00Z", "checkDate": "2025-01-17T00:00:00Z", "checkCount": 1, "id": "aee6c188-f3e2-4b97-bc9c-a2322b58d56b", "companyId": "Y6333699", "type": "payperiod", "_rid": "NmJkAKiCbEfLFwMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfLFwMAAAAAAA==/", "_etag": "\"a5000109-0000-0100-0000-6870244e0000\"", "_attachments": "attachments/", "_ts": 1752179790}, {"payPeriodId": "1030069808004342", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-02-03T00:00:00Z", "endDate": "2025-02-16T00:00:00Z", "submitByDate": "2025-02-13T00:00:00Z", "checkDate": "2025-02-14T00:00:00Z", "checkCount": 1, "id": "dcad9ca5-82ab-429b-9f9b-c7898c3c81ba", "companyId": "Y6333699", "type": "payperiod", "_rid": "NmJkAKiCbEfMFwMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfMFwMAAAAAAA==/", "_etag": "\"a5000309-0000-0100-0000-6870244e0000\"", "_attachments": "attachments/", "_ts": 1752179790}, {"payPeriodId": "1030071950854096", "status": "COMPLETED", "description": "payroll", "startDate": "2025-02-17T00:00:00Z", "endDate": "2025-03-02T00:00:00Z", "submitByDate": "2025-02-27T00:00:00Z", "checkDate": "2025-02-28T00:00:00Z", "checkCount": 2, "id": "5a6dd23e-f29e-44e3-9431-3bc4decd8fa9", "companyId": "Y6333699", "type": "payperiod", "_rid": "NmJkAKiCbEfNFwMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfNFwMAAAAAAA==/", "_etag": "\"a5000709-0000-0100-0000-6870244e0000\"", "_attachments": "attachments/", "_ts": 1752179790}, {"payPeriodId": "1030070356923562", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-03-03T00:00:00Z", "endDate": "2025-03-16T00:00:00Z", "submitByDate": "2025-03-14T00:00:00Z", "checkDate": "2025-03-17T00:00:00Z", "checkCount": 2, "id": "9ad28b30-7ce5-429a-a93c-8d93adfd4b93", "companyId": "Y6333699", "type": "payperiod", "_rid": "NmJkAKiCbEfOFwMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfOFwMAAAAAAA==/", "_etag": "\"a5000909-0000-0100-0000-6870244f0000\"", "_attachments": "attachments/", "_ts": 1752179791}, {"payPeriodId": "1030070589964271", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-03-17T00:00:00Z", "endDate": "2025-03-30T00:00:00Z", "submitByDate": "2025-03-28T00:00:00Z", "checkDate": "2025-03-31T00:00:00Z", "checkCount": 2, "id": "7b2225f3-f064-4214-a8e5-c3848d98e8c3", "companyId": "Y6333699", "type": "payperiod", "_rid": "NmJkAKiCbEfPFwMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfPFwMAAAAAAA==/", "_etag": "\"a5000b09-0000-0100-0000-6870244f0000\"", "_attachments": "attachments/", "_ts": 1752179791}, {"payPeriodId": "1030070924580522", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-03-31T00:00:00Z", "endDate": "2025-04-13T00:00:00Z", "submitByDate": "2025-04-11T00:00:00Z", "checkDate": "2025-04-14T00:00:00Z", "checkCount": 0, "id": "0185119a-cbcc-4c64-8800-313ef337e9a5", "companyId": "Y6333699", "type": "payperiod", "_rid": "NmJkAKiCbEfQFwMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfQFwMAAAAAAA==/", "_etag": "\"a5000e09-0000-0100-0000-6870244f0000\"", "_attachments": "attachments/", "_ts": 1752179791}, {"payPeriodId": "1030071113453986", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-04-14T00:00:00Z", "endDate": "2025-04-27T00:00:00Z", "submitByDate": "2025-04-25T00:00:00Z", "checkDate": "2025-04-28T00:00:00Z", "checkCount": 0, "id": "5bb3c974-6104-4d8d-83b5-003ae9de7b89", "companyId": "Y6333699", "type": "payperiod", "_rid": "NmJkAKiCbEfRFwMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfRFwMAAAAAAA==/", "_etag": "\"a5001009-0000-0100-0000-6870244f0000\"", "_attachments": "attachments/", "_ts": 1752179791}, {"payPeriodId": "1030071478126819", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-04-28T00:00:00Z", "endDate": "2025-05-11T00:00:00Z", "submitByDate": "2025-05-09T00:00:00Z", "checkDate": "2025-05-12T00:00:00Z", "checkCount": 0, "id": "70b25d64-0e92-4656-be42-333392770b49", "companyId": "Y6333699", "type": "payperiod", "_rid": "NmJkAKiCbEfSFwMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfSFwMAAAAAAA==/", "_etag": "\"a5001109-0000-0100-0000-6870244f0000\"", "_attachments": "attachments/", "_ts": 1752179791}, {"payPeriodId": "1030071669785090", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-05-12T00:00:00Z", "endDate": "2025-05-25T00:00:00Z", "submitByDate": "2025-05-22T00:00:00Z", "checkDate": "2025-05-23T00:00:00Z", "checkCount": 0, "id": "b4ed5755-4c07-42ed-8e40-5370d4d65054", "companyId": "Y6333699", "type": "payperiod", "_rid": "NmJkAKiCbEfTFwMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfTFwMAAAAAAA==/", "_etag": "\"a5001209-0000-0100-0000-6870244f0000\"", "_attachments": "attachments/", "_ts": 1752179791}, {"payPeriodId": "1030072021699427", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-05-26T00:00:00Z", "endDate": "2025-06-08T00:00:00Z", "submitByDate": "2025-06-06T00:00:00Z", "checkDate": "2025-06-09T00:00:00Z", "checkCount": 0, "id": "d89f922b-3640-418a-beb8-83e33870dccf", "companyId": "Y6333699", "type": "payperiod", "_rid": "NmJkAKiCbEfUFwMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfUFwMAAAAAAA==/", "_etag": "\"a5001409-0000-0100-0000-6870244f0000\"", "_attachments": "attachments/", "_ts": 1752179791}, {"payPeriodId": "1030072245187854", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-06-09T00:00:00Z", "endDate": "2025-06-22T00:00:00Z", "submitByDate": "2025-06-20T00:00:00Z", "checkDate": "2025-06-23T00:00:00Z", "checkCount": 0, "id": "79127057-c33f-4744-b8af-b86079712c60", "companyId": "Y6333699", "type": "payperiod", "_rid": "NmJkAKiCbEfVFwMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfVFwMAAAAAAA==/", "_etag": "\"a5001709-0000-0100-0000-6870244f0000\"", "_attachments": "attachments/", "_ts": 1752179791}, {"payPeriodId": "1030072768615034", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-07-07T00:00:00Z", "endDate": "2025-07-20T00:00:00Z", "submitByDate": "2025-07-18T00:00:00Z", "checkDate": "2025-07-21T00:00:00Z", "checkCount": 0, "id": "0c69e234-a946-4da7-9b31-bec6997484d1", "companyId": "Y6333699", "type": "payperiod", "_rid": "NmJkAKiCbEfWFwMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfWFwMAAAAAAA==/", "_etag": "\"a5001909-0000-0100-0000-6870244f0000\"", "_attachments": "attachments/", "_ts": 1752179791}, {"payPeriodId": "1030073013740445", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-07-21T00:00:00Z", "endDate": "2025-08-03T00:00:00Z", "submitByDate": "2025-08-01T00:00:00Z", "checkDate": "2025-08-04T00:00:00Z", "checkCount": 0, "id": "b6197aa0-db79-497e-bc47-ea07a3271d49", "companyId": "Y6333699", "type": "payperiod", "_rid": "NmJkAKiCbEfXFwMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfXFwMAAAAAAA==/", "_etag": "\"a5001d09-0000-0100-0000-6870244f0000\"", "_attachments": "attachments/", "_ts": 1752179791}, {"payPeriodId": "1030073303931996", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-08-04T00:00:00Z", "endDate": "2025-08-17T00:00:00Z", "submitByDate": "2025-08-15T00:00:00Z", "checkDate": "2025-08-18T00:00:00Z", "checkCount": 0, "id": "a2e8d1c2-9997-461b-981d-87cddb91cbd5", "companyId": "Y6333699", "type": "payperiod", "_rid": "NmJkAKiCbEfYFwMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfYFwMAAAAAAA==/", "_etag": "\"a5002009-0000-0100-0000-6870244f0000\"", "_attachments": "attachments/", "_ts": 1752179791}, {"payPeriodId": "1030073545495942", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-08-18T00:00:00Z", "endDate": "2025-08-31T00:00:00Z", "submitByDate": "2025-08-28T00:00:00Z", "checkDate": "2025-08-29T00:00:00Z", "checkCount": 0, "id": "3f77893f-8466-4774-b384-9154a99cc3b1", "companyId": "Y6333699", "type": "payperiod", "_rid": "NmJkAKiCbEfZFwMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfZFwMAAAAAAA==/", "_etag": "\"a5002409-0000-0100-0000-6870244f0000\"", "_attachments": "attachments/", "_ts": 1752179791}, {"payPeriodId": "1030073862523559", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-09-01T00:00:00Z", "endDate": "2025-09-14T00:00:00Z", "submitByDate": "2025-09-12T00:00:00Z", "checkDate": "2025-09-15T00:00:00Z", "checkCount": 0, "id": "c470835b-f60c-462d-999f-57c129d36627", "companyId": "Y6333699", "type": "payperiod", "_rid": "NmJkAKiCbEfaFwMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfaFwMAAAAAAA==/", "_etag": "\"a5002609-0000-0100-0000-6870244f0000\"", "_attachments": "attachments/", "_ts": 1752179791}, {"payPeriodId": "1030074127532466", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-09-15T00:00:00Z", "endDate": "2025-09-28T00:00:00Z", "submitByDate": "2025-09-26T00:00:00Z", "checkDate": "2025-09-29T00:00:00Z", "checkCount": 0, "id": "5499150c-4a61-4c41-88c9-6a7ec00ad18d", "companyId": "Y6333699", "type": "payperiod", "_rid": "NmJkAKiCbEfbFwMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfbFwMAAAAAAA==/", "_etag": "\"a5002809-0000-0100-0000-687024500000\"", "_attachments": "attachments/", "_ts": 1752179792}, {"payPeriodId": "1030074500613148", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-09-29T00:00:00Z", "endDate": "2025-10-12T00:00:00Z", "submitByDate": "2025-10-09T00:00:00Z", "checkDate": "2025-10-10T00:00:00Z", "checkCount": 0, "id": "d237ae94-5d1d-4575-86f1-646f7da81846", "companyId": "Y6333699", "type": "payperiod", "_rid": "NmJkAKiCbEfcFwMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfcFwMAAAAAAA==/", "_etag": "\"a5002a09-0000-0100-0000-687024500000\"", "_attachments": "attachments/", "_ts": 1752179792}, {"payPeriodId": "1030069263880448", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-01-06T00:00:00Z", "endDate": "2025-01-19T00:00:00Z", "submitByDate": "2025-01-16T00:00:00Z", "checkDate": "2025-01-17T00:00:00Z", "checkCount": 1, "id": "d7126652-2208-4222-84ba-ecb4901c2437", "companyId": "Y6333699", "type": "payperiod", "_rid": "NmJkAKiCbEfiFwMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfiFwMAAAAAAA==/", "_etag": "\"a5003c09-0000-0100-0000-687024500000\"", "_attachments": "attachments/", "_ts": 1752179792}, {"payPeriodId": "1030069808004342", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-02-03T00:00:00Z", "endDate": "2025-02-16T00:00:00Z", "submitByDate": "2025-02-13T00:00:00Z", "checkDate": "2025-02-14T00:00:00Z", "checkCount": 1, "id": "55b3cdfc-6657-41b4-a67c-9d3ea563de64", "companyId": "Y6333699", "type": "payperiod", "_rid": "NmJkAKiCbEfjFwMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfjFwMAAAAAAA==/", "_etag": "\"a5003f09-0000-0100-0000-687024500000\"", "_attachments": "attachments/", "_ts": 1752179792}, {"payPeriodId": "1030071950854096", "status": "COMPLETED", "description": "payroll", "startDate": "2025-02-17T00:00:00Z", "endDate": "2025-03-02T00:00:00Z", "submitByDate": "2025-02-27T00:00:00Z", "checkDate": "2025-02-28T00:00:00Z", "checkCount": 2, "id": "1f5fd1e6-b6aa-4d19-930f-271001023c3f", "companyId": "Y6333699", "type": "payperiod", "_rid": "NmJkAKiCbEfkFwMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfkFwMAAAAAAA==/", "_etag": "\"a5004309-0000-0100-0000-687024500000\"", "_attachments": "attachments/", "_ts": 1752179792}, {"payPeriodId": "1030070356923562", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-03-03T00:00:00Z", "endDate": "2025-03-16T00:00:00Z", "submitByDate": "2025-03-14T00:00:00Z", "checkDate": "2025-03-17T00:00:00Z", "checkCount": 2, "id": "976c51f1-08f4-4249-b84a-d78540e7bcaa", "companyId": "Y6333699", "type": "payperiod", "_rid": "NmJkAKiCbEflFwMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEflFwMAAAAAAA==/", "_etag": "\"a5004509-0000-0100-0000-687024500000\"", "_attachments": "attachments/", "_ts": 1752179792}, {"payPeriodId": "1030070589964271", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-03-17T00:00:00Z", "endDate": "2025-03-30T00:00:00Z", "submitByDate": "2025-03-28T00:00:00Z", "checkDate": "2025-03-31T00:00:00Z", "checkCount": 2, "id": "43459811-dff5-4657-a8ac-23b372749508", "companyId": "Y6333699", "type": "payperiod", "_rid": "NmJkAKiCbEfmFwMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfmFwMAAAAAAA==/", "_etag": "\"a5004809-0000-0100-0000-687024500000\"", "_attachments": "attachments/", "_ts": 1752179792}, {"payPeriodId": "1030070924580522", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-03-31T00:00:00Z", "endDate": "2025-04-13T00:00:00Z", "submitByDate": "2025-04-11T00:00:00Z", "checkDate": "2025-04-14T00:00:00Z", "checkCount": 2, "id": "1a8e8afe-739a-4007-ae95-d46045e3b5ad", "companyId": "Y6333699", "type": "payperiod", "_rid": "NmJkAKiCbEfnFwMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfnFwMAAAAAAA==/", "_etag": "\"a5004a09-0000-0100-0000-687024510000\"", "_attachments": "attachments/", "_ts": 1752179793}, {"payPeriodId": "1030071113453986", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-04-14T00:00:00Z", "endDate": "2025-04-27T00:00:00Z", "submitByDate": "2025-04-25T00:00:00Z", "checkDate": "2025-04-28T00:00:00Z", "checkCount": 2, "id": "9196fdff-5a67-40d8-b884-c8c399e06f64", "companyId": "Y6333699", "type": "payperiod", "_rid": "NmJkAKiCbEfoFwMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfoFwMAAAAAAA==/", "_etag": "\"a5004b09-0000-0100-0000-687024510000\"", "_attachments": "attachments/", "_ts": 1752179793}, {"payPeriodId": "1030071478126819", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-04-28T00:00:00Z", "endDate": "2025-05-11T00:00:00Z", "submitByDate": "2025-05-09T00:00:00Z", "checkDate": "2025-05-12T00:00:00Z", "checkCount": 2, "id": "b1f8e705-6f70-48e0-811d-ecee829be4a8", "companyId": "Y6333699", "type": "payperiod", "_rid": "NmJkAKiCbEfpFwMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfpFwMAAAAAAA==/", "_etag": "\"a5004e09-0000-0100-0000-687024510000\"", "_attachments": "attachments/", "_ts": 1752179793}, {"payPeriodId": "1030071669785090", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-05-12T00:00:00Z", "endDate": "2025-05-25T00:00:00Z", "submitByDate": "2025-05-22T00:00:00Z", "checkDate": "2025-05-23T00:00:00Z", "checkCount": 2, "id": "e2e6cbfe-7b05-44f8-b201-a423aeb348f0", "companyId": "Y6333699", "type": "payperiod", "_rid": "NmJkAKiCbEfqFwMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfqFwMAAAAAAA==/", "_etag": "\"a5005109-0000-0100-0000-687024510000\"", "_attachments": "attachments/", "_ts": 1752179793}, {"payPeriodId": "1030072021699427", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-05-26T00:00:00Z", "endDate": "2025-06-08T00:00:00Z", "submitByDate": "2025-06-06T00:00:00Z", "checkDate": "2025-06-09T00:00:00Z", "checkCount": 2, "id": "ca308b93-92f1-4770-8954-2b315ffce669", "companyId": "Y6333699", "type": "payperiod", "_rid": "NmJkAKiCbEfrFwMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfrFwMAAAAAAA==/", "_etag": "\"a5005409-0000-0100-0000-687024510000\"", "_attachments": "attachments/", "_ts": 1752179793}, {"payPeriodId": "1030072245187854", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-06-09T00:00:00Z", "endDate": "2025-06-22T00:00:00Z", "submitByDate": "2025-06-20T00:00:00Z", "checkDate": "2025-06-23T00:00:00Z", "checkCount": 1, "id": "6b04c61f-d66e-45b1-9a7c-3f8463dbc82c", "companyId": "Y6333699", "type": "payperiod", "_rid": "NmJkAKiCbEfsFwMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfsFwMAAAAAAA==/", "_etag": "\"a5005609-0000-0100-0000-687024510000\"", "_attachments": "attachments/", "_ts": 1752179793}, {"payPeriodId": "1030072768615034", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-07-07T00:00:00Z", "endDate": "2025-07-20T00:00:00Z", "submitByDate": "2025-07-18T00:00:00Z", "checkDate": "2025-07-21T00:00:00Z", "checkCount": 0, "id": "a3428347-506e-4efd-a9f9-962fce392513", "companyId": "Y6333699", "type": "payperiod", "_rid": "NmJkAKiCbEftFwMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEftFwMAAAAAAA==/", "_etag": "\"a5005909-0000-0100-0000-687024510000\"", "_attachments": "attachments/", "_ts": 1752179793}, {"payPeriodId": "1030073013740445", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-07-21T00:00:00Z", "endDate": "2025-08-03T00:00:00Z", "submitByDate": "2025-08-01T00:00:00Z", "checkDate": "2025-08-04T00:00:00Z", "checkCount": 0, "id": "aa126b87-e55d-40e8-a44e-983218203f30", "companyId": "Y6333699", "type": "payperiod", "_rid": "NmJkAKiCbEfuFwMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfuFwMAAAAAAA==/", "_etag": "\"a5005c09-0000-0100-0000-687024510000\"", "_attachments": "attachments/", "_ts": 1752179793}, {"payPeriodId": "1030073303931996", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-08-04T00:00:00Z", "endDate": "2025-08-17T00:00:00Z", "submitByDate": "2025-08-15T00:00:00Z", "checkDate": "2025-08-18T00:00:00Z", "checkCount": 0, "id": "8962c056-492b-452b-8186-89facd8c36c2", "companyId": "Y6333699", "type": "payperiod", "_rid": "NmJkAKiCbEfvFwMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfvFwMAAAAAAA==/", "_etag": "\"a5006009-0000-0100-0000-687024510000\"", "_attachments": "attachments/", "_ts": 1752179793}, {"payPeriodId": "1030073545495942", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-08-18T00:00:00Z", "endDate": "2025-08-31T00:00:00Z", "submitByDate": "2025-08-28T00:00:00Z", "checkDate": "2025-08-29T00:00:00Z", "checkCount": 0, "id": "29954b5b-f1d0-4c3c-a9e4-c89c328e0b98", "companyId": "Y6333699", "type": "payperiod", "_rid": "NmJkAKiCbEfwFwMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfwFwMAAAAAAA==/", "_etag": "\"a5006309-0000-0100-0000-687024510000\"", "_attachments": "attachments/", "_ts": 1752179793}, {"payPeriodId": "1030073862523559", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-09-01T00:00:00Z", "endDate": "2025-09-14T00:00:00Z", "submitByDate": "2025-09-12T00:00:00Z", "checkDate": "2025-09-15T00:00:00Z", "checkCount": 0, "id": "43aabf97-f76d-4489-885d-d0e74fe23c18", "companyId": "Y6333699", "type": "payperiod", "_rid": "NmJkAKiCbEfxFwMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfxFwMAAAAAAA==/", "_etag": "\"a5006409-0000-0100-0000-687024510000\"", "_attachments": "attachments/", "_ts": 1752179793}, {"payPeriodId": "1030074127532466", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-09-15T00:00:00Z", "endDate": "2025-09-28T00:00:00Z", "submitByDate": "2025-09-26T00:00:00Z", "checkDate": "2025-09-29T00:00:00Z", "checkCount": 0, "id": "1529b2ea-751f-44ba-ab01-7c8d91ece5a9", "companyId": "Y6333699", "type": "payperiod", "_rid": "NmJkAKiCbEfyFwMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfyFwMAAAAAAA==/", "_etag": "\"a5006709-0000-0100-0000-687024510000\"", "_attachments": "attachments/", "_ts": 1752179793}, {"payPeriodId": "1030074500613148", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-09-29T00:00:00Z", "endDate": "2025-10-12T00:00:00Z", "submitByDate": "2025-10-09T00:00:00Z", "checkDate": "2025-10-10T00:00:00Z", "checkCount": 0, "id": "6f92f231-43ad-48f7-a482-484463481761", "companyId": "Y6333699", "type": "payperiod", "_rid": "NmJkAKiCbEfzFwMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfzFwMAAAAAAA==/", "_etag": "\"a5006a09-0000-0100-0000-687024510000\"", "_attachments": "attachments/", "_ts": 1752179793}], "links": [{"rel": "self", "href": "https://ca-payroll-ai-mock-sb-001-secure.nicebeach-8a7a52ab.eastus.azurecontainerapps.io/ca/companies/Y6333699/payperiods"}]}, "status_code": 200}