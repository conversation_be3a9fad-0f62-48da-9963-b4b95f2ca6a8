{"success": true, "company_id": "19008831", "data": {"metadata": {"contentItemCount": 50}, "content": [{"payPeriodId": "1140035183594218", "intervalCode": "MONTHLY", "status": "COMPLETED", "description": "Monthly Payroll (1)", "startDate": "2025-02-01T00:00:00Z", "endDate": "2025-02-28T00:00:00Z", "submitByDate": "2025-02-27T00:00:00Z", "checkDate": "2025-02-28T00:00:00Z", "checkCount": 6, "id": "6922aae9-c80f-43fe-9c1c-50d4e57d1722", "companyId": "19008831", "type": "payperiod", "_rid": "NmJkAKiCbEei1wAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEei1wAAAAAAAA==/", "_etag": "\"9a00bb2b-0000-0100-0000-686fe0600000\"", "_attachments": "attachments/", "_ts": 1752162400}, {"payPeriodId": "1140035324869338", "intervalCode": "MONTHLY", "status": "COMPLETED", "description": "Monthly Payroll (1)", "startDate": "2025-03-01T00:00:00Z", "endDate": "2025-03-31T00:00:00Z", "submitByDate": "2025-03-30T00:00:00Z", "checkDate": "2025-03-31T00:00:00Z", "checkCount": 6, "id": "3e4ff8ed-3b92-4a65-b0e5-2e667bac3333", "companyId": "19008831", "type": "payperiod", "_rid": "NmJkAKiCbEej1wAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEej1wAAAAAAAA==/", "_etag": "\"9a00c12b-0000-0100-0000-686fe0600000\"", "_attachments": "attachments/", "_ts": 1752162400}, {"payPeriodId": "1140035936799677", "status": "INITIAL", "description": "Payroll", "startDate": "2025-04-01T00:00:00Z", "endDate": "2025-04-30T00:00:00Z", "submitByDate": "2025-04-29T00:00:00Z", "checkDate": "2025-04-30T00:00:00Z", "checkCount": 0, "id": "97bdcc49-a244-4190-810f-dd120e762366", "companyId": "19008831", "type": "payperiod", "_rid": "NmJkAKiCbEek1wAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEek1wAAAAAAAA==/", "_etag": "\"9a00c62b-0000-0100-0000-686fe0600000\"", "_attachments": "attachments/", "_ts": 1752162400}, {"payPeriodId": "1140035588191389", "intervalCode": "MONTHLY", "status": "INITIAL", "description": "Monthly Payroll (1)", "startDate": "2025-05-01T00:00:00Z", "endDate": "2025-05-31T00:00:00Z", "submitByDate": "2025-05-30T00:00:00Z", "checkDate": "2025-05-31T00:00:00Z", "checkCount": 0, "id": "fb5e0dae-5f99-4867-acac-a3b28015b247", "companyId": "19008831", "type": "payperiod", "_rid": "NmJkAKiCbEel1wAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEel1wAAAAAAAA==/", "_etag": "\"9a00c82b-0000-0100-0000-686fe0600000\"", "_attachments": "attachments/", "_ts": 1752162400}, {"payPeriodId": "1140035703550964", "intervalCode": "MONTHLY", "status": "INITIAL", "description": "Monthly Payroll (1)", "startDate": "2025-06-01T00:00:00Z", "endDate": "2025-06-30T00:00:00Z", "submitByDate": "2025-06-29T00:00:00Z", "checkDate": "2025-06-30T00:00:00Z", "checkCount": 0, "id": "9409258a-d893-4f1f-96b0-90815c1aee90", "companyId": "19008831", "type": "payperiod", "_rid": "NmJkAKiCbEem1wAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEem1wAAAAAAAA==/", "_etag": "\"9a00d02b-0000-0100-0000-686fe0600000\"", "_attachments": "attachments/", "_ts": 1752162400}, {"payPeriodId": "1140035845469368", "intervalCode": "MONTHLY", "status": "INITIAL", "description": "Monthly Payroll (1)", "startDate": "2025-07-01T00:00:00Z", "endDate": "2025-07-31T00:00:00Z", "submitByDate": "2025-07-30T00:00:00Z", "checkDate": "2025-07-31T00:00:00Z", "checkCount": 0, "id": "e80ef9bd-4b8b-43ef-9f61-8f7118586d42", "companyId": "19008831", "type": "payperiod", "_rid": "NmJkAKiCbEen1wAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEen1wAAAAAAAA==/", "_etag": "\"9a00d12b-0000-0100-0000-686fe0600000\"", "_attachments": "attachments/", "_ts": 1752162400}, {"payPeriodId": "1140035965999862", "intervalCode": "MONTHLY", "status": "INITIAL", "description": "Monthly Payroll (1)", "startDate": "2025-08-01T00:00:00Z", "endDate": "2025-08-31T00:00:00Z", "submitByDate": "2025-07-30T00:00:00Z", "checkDate": "2025-08-01T00:00:00Z", "checkCount": 0, "id": "78f04940-1141-444c-9ecd-b0d651c1c3ed", "companyId": "19008831", "type": "payperiod", "_rid": "NmJkAKiCbEeo1wAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeo1wAAAAAAAA==/", "_etag": "\"9a00d22b-0000-0100-0000-686fe0600000\"", "_attachments": "attachments/", "_ts": 1752162400}, {"payPeriodId": "1140036117687213", "intervalCode": "MONTHLY", "status": "INITIAL", "description": "Monthly Payroll (1)", "startDate": "2025-09-01T00:00:00Z", "endDate": "2025-09-30T00:00:00Z", "submitByDate": "2025-08-27T00:00:00Z", "checkDate": "2025-08-29T00:00:00Z", "checkCount": 0, "id": "f99a6483-fcd0-4ce4-b738-f6510f0e376d", "companyId": "19008831", "type": "payperiod", "_rid": "NmJkAKiCbEep1wAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEep1wAAAAAAAA==/", "_etag": "\"9a00d42b-0000-0100-0000-686fe0600000\"", "_attachments": "attachments/", "_ts": 1752162400}, {"payPeriodId": "1140035183594218", "intervalCode": "MONTHLY", "status": "COMPLETED", "description": "Monthly Payroll (1)", "startDate": "2025-02-01T00:00:00Z", "endDate": "2025-02-28T00:00:00Z", "submitByDate": "2025-02-27T00:00:00Z", "checkDate": "2025-02-28T00:00:00Z", "checkCount": 6, "id": "cc656f60-ac65-4300-9ab2-64fa01682da2", "companyId": "19008831", "type": "payperiod", "_rid": "NmJkAKiCbEey1wAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEey1wAAAAAAAA==/", "_etag": "\"9a00fb2b-0000-0100-0000-686fe0610000\"", "_attachments": "attachments/", "_ts": 1752162401}, {"payPeriodId": "1140035324869338", "intervalCode": "MONTHLY", "status": "COMPLETED", "description": "Monthly Payroll (1)", "startDate": "2025-03-01T00:00:00Z", "endDate": "2025-03-31T00:00:00Z", "submitByDate": "2025-03-30T00:00:00Z", "checkDate": "2025-03-31T00:00:00Z", "checkCount": 6, "id": "53161df9-3fb9-4361-8880-bdac6142ab8b", "companyId": "19008831", "type": "payperiod", "_rid": "NmJkAKiCbEez1wAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEez1wAAAAAAAA==/", "_etag": "\"9a00fe2b-0000-0100-0000-686fe0610000\"", "_attachments": "attachments/", "_ts": 1752162401}, {"payPeriodId": "1140035936799677", "status": "COMPLETED", "description": "Payroll", "startDate": "2025-04-01T00:00:00Z", "endDate": "2025-04-30T00:00:00Z", "submitByDate": "2025-04-29T00:00:00Z", "checkDate": "2025-04-30T00:00:00Z", "checkCount": 6, "id": "4b607c9b-ad96-4cad-91b4-6926b234f57e", "companyId": "19008831", "type": "payperiod", "_rid": "NmJkAKiCbEe01wAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEe01wAAAAAAAA==/", "_etag": "\"9a00ff2b-0000-0100-0000-686fe0610000\"", "_attachments": "attachments/", "_ts": 1752162401}, {"payPeriodId": "1140035588191389", "intervalCode": "MONTHLY", "status": "COMPLETED", "description": "Monthly Payroll (1)", "startDate": "2025-05-01T00:00:00Z", "endDate": "2025-05-31T00:00:00Z", "submitByDate": "2025-05-30T00:00:00Z", "checkDate": "2025-05-31T00:00:00Z", "checkCount": 6, "id": "b8c06f64-2271-467f-bdc1-96f8533af096", "companyId": "19008831", "type": "payperiod", "_rid": "NmJkAKiCbEe11wAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEe11wAAAAAAAA==/", "_etag": "\"9a00022c-0000-0100-0000-686fe0610000\"", "_attachments": "attachments/", "_ts": 1752162401}, {"payPeriodId": "1140035703550964", "intervalCode": "MONTHLY", "status": "INITIAL", "description": "Monthly Payroll (1)", "startDate": "2025-06-01T00:00:00Z", "endDate": "2025-06-30T00:00:00Z", "submitByDate": "2025-06-29T00:00:00Z", "checkDate": "2025-06-30T00:00:00Z", "checkCount": 0, "id": "bf7e52e6-a2fc-46e5-992e-e4874e1d21cf", "companyId": "19008831", "type": "payperiod", "_rid": "NmJkAKiCbEe21wAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEe21wAAAAAAAA==/", "_etag": "\"9a00042c-0000-0100-0000-686fe0610000\"", "_attachments": "attachments/", "_ts": 1752162401}, {"payPeriodId": "1140035845469368", "intervalCode": "MONTHLY", "status": "INITIAL", "description": "Monthly Payroll (1)", "startDate": "2025-07-01T00:00:00Z", "endDate": "2025-07-31T00:00:00Z", "submitByDate": "2025-07-30T00:00:00Z", "checkDate": "2025-07-31T00:00:00Z", "checkCount": 0, "id": "946490df-7320-4e9d-be01-9a2ca40cc370", "companyId": "19008831", "type": "payperiod", "_rid": "NmJkAKiCbEe31wAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEe31wAAAAAAAA==/", "_etag": "\"9a00052c-0000-0100-0000-686fe0610000\"", "_attachments": "attachments/", "_ts": 1752162401}, {"payPeriodId": "1140035965999862", "intervalCode": "MONTHLY", "status": "INITIAL", "description": "Monthly Payroll (1)", "startDate": "2025-08-01T00:00:00Z", "endDate": "2025-08-31T00:00:00Z", "submitByDate": "2025-07-30T00:00:00Z", "checkDate": "2025-08-01T00:00:00Z", "checkCount": 0, "id": "369b5e66-651c-4012-b4e7-0adb540976e6", "companyId": "19008831", "type": "payperiod", "_rid": "NmJkAKiCbEe41wAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEe41wAAAAAAAA==/", "_etag": "\"9a00062c-0000-0100-0000-686fe0620000\"", "_attachments": "attachments/", "_ts": 1752162402}, {"payPeriodId": "1140036117687213", "intervalCode": "MONTHLY", "status": "INITIAL", "description": "Monthly Payroll (1)", "startDate": "2025-09-01T00:00:00Z", "endDate": "2025-09-30T00:00:00Z", "submitByDate": "2025-08-27T00:00:00Z", "checkDate": "2025-08-29T00:00:00Z", "checkCount": 0, "id": "7a47dc9f-90e8-4f1b-9575-ddbd44af61d6", "companyId": "19008831", "type": "payperiod", "_rid": "NmJkAKiCbEe51wAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEe51wAAAAAAAA==/", "_etag": "\"9a00082c-0000-0100-0000-686fe0620000\"", "_attachments": "attachments/", "_ts": 1752162402}, {"payPeriodId": "1140035183594218", "intervalCode": "MONTHLY", "status": "COMPLETED", "description": "Monthly Payroll (1)", "startDate": "2025-02-01T00:00:00Z", "endDate": "2025-02-28T00:00:00Z", "submitByDate": "2025-02-27T00:00:00Z", "checkDate": "2025-02-28T00:00:00Z", "checkCount": 6, "id": "4be79e8b-828c-42ba-89a2-c4cde62768dc", "companyId": "19008831", "type": "payperiod", "_rid": "NmJkAKiCbEfP0AEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfP0AEAAAAAAA==/", "_etag": "\"a0005648-0000-0100-0000-687007350000\"", "_attachments": "attachments/", "_ts": 1752172341}, {"payPeriodId": "1140035324869338", "intervalCode": "MONTHLY", "status": "COMPLETED", "description": "Monthly Payroll (1)", "startDate": "2025-03-01T00:00:00Z", "endDate": "2025-03-31T00:00:00Z", "submitByDate": "2025-03-30T00:00:00Z", "checkDate": "2025-03-31T00:00:00Z", "checkCount": 6, "id": "9b3e506f-7772-4d79-a7ab-56a3c0c55f6a", "companyId": "19008831", "type": "payperiod", "_rid": "NmJkAKiCbEfQ0AEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfQ0AEAAAAAAA==/", "_etag": "\"a0005948-0000-0100-0000-687007350000\"", "_attachments": "attachments/", "_ts": 1752172341}, {"payPeriodId": "1140035936799677", "status": "INITIAL", "description": "Payroll", "startDate": "2025-04-01T00:00:00Z", "endDate": "2025-04-30T00:00:00Z", "submitByDate": "2025-04-29T00:00:00Z", "checkDate": "2025-04-30T00:00:00Z", "checkCount": 0, "id": "6a9d09c1-2996-45e2-ac56-837cc8086086", "companyId": "19008831", "type": "payperiod", "_rid": "NmJkAKiCbEfR0AEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfR0AEAAAAAAA==/", "_etag": "\"a0005e48-0000-0100-0000-687007350000\"", "_attachments": "attachments/", "_ts": 1752172341}, {"payPeriodId": "1140035588191389", "intervalCode": "MONTHLY", "status": "INITIAL", "description": "Monthly Payroll (1)", "startDate": "2025-05-01T00:00:00Z", "endDate": "2025-05-31T00:00:00Z", "submitByDate": "2025-05-30T00:00:00Z", "checkDate": "2025-05-31T00:00:00Z", "checkCount": 0, "id": "c7848f9a-98cb-4aa6-be6f-e908d5b2b085", "companyId": "19008831", "type": "payperiod", "_rid": "NmJkAKiCbEfS0AEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfS0AEAAAAAAA==/", "_etag": "\"a0006348-0000-0100-0000-687007350000\"", "_attachments": "attachments/", "_ts": 1752172341}, {"payPeriodId": "1140035703550964", "intervalCode": "MONTHLY", "status": "INITIAL", "description": "Monthly Payroll (1)", "startDate": "2025-06-01T00:00:00Z", "endDate": "2025-06-30T00:00:00Z", "submitByDate": "2025-06-29T00:00:00Z", "checkDate": "2025-06-30T00:00:00Z", "checkCount": 0, "id": "9b73ac27-33e9-4fb2-ab79-7cb0e1994254", "companyId": "19008831", "type": "payperiod", "_rid": "NmJkAKiCbEfT0AEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfT0AEAAAAAAA==/", "_etag": "\"a0006648-0000-0100-0000-687007350000\"", "_attachments": "attachments/", "_ts": 1752172341}, {"payPeriodId": "1140035845469368", "intervalCode": "MONTHLY", "status": "INITIAL", "description": "Monthly Payroll (1)", "startDate": "2025-07-01T00:00:00Z", "endDate": "2025-07-31T00:00:00Z", "submitByDate": "2025-07-30T00:00:00Z", "checkDate": "2025-07-31T00:00:00Z", "checkCount": 0, "id": "592f302c-4229-42c0-8b02-7060d5c4d296", "companyId": "19008831", "type": "payperiod", "_rid": "NmJkAKiCbEfU0AEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfU0AEAAAAAAA==/", "_etag": "\"a0006a48-0000-0100-0000-687007350000\"", "_attachments": "attachments/", "_ts": 1752172341}, {"payPeriodId": "1140035965999862", "intervalCode": "MONTHLY", "status": "INITIAL", "description": "Monthly Payroll (1)", "startDate": "2025-08-01T00:00:00Z", "endDate": "2025-08-31T00:00:00Z", "submitByDate": "2025-07-30T00:00:00Z", "checkDate": "2025-08-01T00:00:00Z", "checkCount": 0, "id": "237493cd-2c12-4dc5-a8b8-ad38fcafabd3", "companyId": "19008831", "type": "payperiod", "_rid": "NmJkAKiCbEfV0AEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfV0AEAAAAAAA==/", "_etag": "\"a0006c48-0000-0100-0000-687007350000\"", "_attachments": "attachments/", "_ts": 1752172341}, {"payPeriodId": "1140036117687213", "intervalCode": "MONTHLY", "status": "INITIAL", "description": "Monthly Payroll (1)", "startDate": "2025-09-01T00:00:00Z", "endDate": "2025-09-30T00:00:00Z", "submitByDate": "2025-08-27T00:00:00Z", "checkDate": "2025-08-29T00:00:00Z", "checkCount": 0, "id": "6390a342-2d01-4605-8c74-00aa7f1fca8b", "companyId": "19008831", "type": "payperiod", "_rid": "NmJkAKiCbEfW0AEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfW0AEAAAAAAA==/", "_etag": "\"a0006f48-0000-0100-0000-687007350000\"", "_attachments": "attachments/", "_ts": 1752172341}, {"payPeriodId": "1140035183594218", "intervalCode": "MONTHLY", "status": "COMPLETED", "description": "Monthly Payroll (1)", "startDate": "2025-02-01T00:00:00Z", "endDate": "2025-02-28T00:00:00Z", "submitByDate": "2025-02-27T00:00:00Z", "checkDate": "2025-02-28T00:00:00Z", "checkCount": 6, "id": "e20c60f2-e8dc-4a9e-aa41-d3365e509157", "companyId": "19008831", "type": "payperiod", "_rid": "NmJkAKiCbEff0AEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEff0AEAAAAAAA==/", "_etag": "\"a0008748-0000-0100-0000-687007360000\"", "_attachments": "attachments/", "_ts": 1752172342}, {"payPeriodId": "1140035324869338", "intervalCode": "MONTHLY", "status": "COMPLETED", "description": "Monthly Payroll (1)", "startDate": "2025-03-01T00:00:00Z", "endDate": "2025-03-31T00:00:00Z", "submitByDate": "2025-03-30T00:00:00Z", "checkDate": "2025-03-31T00:00:00Z", "checkCount": 6, "id": "d8025866-8300-46ec-a4a8-f7e4f163c668", "companyId": "19008831", "type": "payperiod", "_rid": "NmJkAKiCbEfg0AEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfg0AEAAAAAAA==/", "_etag": "\"a0008f48-0000-0100-0000-687007360000\"", "_attachments": "attachments/", "_ts": 1752172342}, {"payPeriodId": "1140035936799677", "status": "COMPLETED", "description": "Payroll", "startDate": "2025-04-01T00:00:00Z", "endDate": "2025-04-30T00:00:00Z", "submitByDate": "2025-04-29T00:00:00Z", "checkDate": "2025-04-30T00:00:00Z", "checkCount": 6, "id": "64f97eb3-3242-4287-9368-888320b59c59", "companyId": "19008831", "type": "payperiod", "_rid": "NmJkAKiCbEfh0AEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfh0AEAAAAAAA==/", "_etag": "\"a0009548-0000-0100-0000-687007360000\"", "_attachments": "attachments/", "_ts": 1752172342}, {"payPeriodId": "1140035588191389", "intervalCode": "MONTHLY", "status": "COMPLETED", "description": "Monthly Payroll (1)", "startDate": "2025-05-01T00:00:00Z", "endDate": "2025-05-31T00:00:00Z", "submitByDate": "2025-05-30T00:00:00Z", "checkDate": "2025-05-31T00:00:00Z", "checkCount": 6, "id": "acde4fb4-6633-444f-b9f3-33df17edb081", "companyId": "19008831", "type": "payperiod", "_rid": "NmJkAKiCbEfi0AEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfi0AEAAAAAAA==/", "_etag": "\"a0009848-0000-0100-0000-687007360000\"", "_attachments": "attachments/", "_ts": 1752172342}, {"payPeriodId": "1140035703550964", "intervalCode": "MONTHLY", "status": "INITIAL", "description": "Monthly Payroll (1)", "startDate": "2025-06-01T00:00:00Z", "endDate": "2025-06-30T00:00:00Z", "submitByDate": "2025-06-29T00:00:00Z", "checkDate": "2025-06-30T00:00:00Z", "checkCount": 0, "id": "41b0c944-2f3d-46f2-8002-8783acff58a1", "companyId": "19008831", "type": "payperiod", "_rid": "NmJkAKiCbEfj0AEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfj0AEAAAAAAA==/", "_etag": "\"a0009c48-0000-0100-0000-687007370000\"", "_attachments": "attachments/", "_ts": 1752172343}, {"payPeriodId": "1140035845469368", "intervalCode": "MONTHLY", "status": "INITIAL", "description": "Monthly Payroll (1)", "startDate": "2025-07-01T00:00:00Z", "endDate": "2025-07-31T00:00:00Z", "submitByDate": "2025-07-30T00:00:00Z", "checkDate": "2025-07-31T00:00:00Z", "checkCount": 0, "id": "a4c69c2b-d2ed-42aa-ba09-fa241808c338", "companyId": "19008831", "type": "payperiod", "_rid": "NmJkAKiCbEfk0AEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfk0AEAAAAAAA==/", "_etag": "\"a0009f48-0000-0100-0000-687007370000\"", "_attachments": "attachments/", "_ts": 1752172343}, {"payPeriodId": "1140035965999862", "intervalCode": "MONTHLY", "status": "INITIAL", "description": "Monthly Payroll (1)", "startDate": "2025-08-01T00:00:00Z", "endDate": "2025-08-31T00:00:00Z", "submitByDate": "2025-07-30T00:00:00Z", "checkDate": "2025-08-01T00:00:00Z", "checkCount": 0, "id": "38c18f28-9ae9-45bd-a4b3-c22abee2d91c", "companyId": "19008831", "type": "payperiod", "_rid": "NmJkAKiCbEfl0AEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfl0AEAAAAAAA==/", "_etag": "\"a000a348-0000-0100-0000-687007370000\"", "_attachments": "attachments/", "_ts": 1752172343}, {"payPeriodId": "1140036117687213", "intervalCode": "MONTHLY", "status": "INITIAL", "description": "Monthly Payroll (1)", "startDate": "2025-09-01T00:00:00Z", "endDate": "2025-09-30T00:00:00Z", "submitByDate": "2025-08-27T00:00:00Z", "checkDate": "2025-08-29T00:00:00Z", "checkCount": 0, "id": "dac8ec0b-a7e7-445d-b814-f6384e950724", "companyId": "19008831", "type": "payperiod", "_rid": "NmJkAKiCbEfm0AEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfm0AEAAAAAAA==/", "_etag": "\"a000a548-0000-0100-0000-687007370000\"", "_attachments": "attachments/", "_ts": 1752172343}, {"payPeriodId": "1140035183594218", "intervalCode": "MONTHLY", "status": "COMPLETED", "description": "Monthly Payroll (1)", "startDate": "2025-02-01T00:00:00Z", "endDate": "2025-02-28T00:00:00Z", "submitByDate": "2025-02-27T00:00:00Z", "checkDate": "2025-02-28T00:00:00Z", "checkCount": 6, "id": "8fd94893-0115-4ce3-a94c-52d0ad0792e5", "companyId": "19008831", "type": "payperiod", "_rid": "NmJkAKiCbEeBLQQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeBLQQAAAAAAA==/", "_etag": "\"a800681d-0000-0100-0000-68703ac50000\"", "_attachments": "attachments/", "_ts": 1752185541}, {"payPeriodId": "1140035324869338", "intervalCode": "MONTHLY", "status": "COMPLETED", "description": "Monthly Payroll (1)", "startDate": "2025-03-01T00:00:00Z", "endDate": "2025-03-31T00:00:00Z", "submitByDate": "2025-03-30T00:00:00Z", "checkDate": "2025-03-31T00:00:00Z", "checkCount": 6, "id": "28cef50f-aea3-4650-9d98-bf4603db2ca4", "companyId": "19008831", "type": "payperiod", "_rid": "NmJkAKiCbEeCLQQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeCLQQAAAAAAA==/", "_etag": "\"a8006a1d-0000-0100-0000-68703ac50000\"", "_attachments": "attachments/", "_ts": 1752185541}, {"payPeriodId": "1140035936799677", "status": "INITIAL", "description": "Payroll", "startDate": "2025-04-01T00:00:00Z", "endDate": "2025-04-30T00:00:00Z", "submitByDate": "2025-04-29T00:00:00Z", "checkDate": "2025-04-30T00:00:00Z", "checkCount": 0, "id": "d20a2818-404d-41af-92ff-e7f99ae9cdbe", "companyId": "19008831", "type": "payperiod", "_rid": "NmJkAKiCbEeDLQQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeDLQQAAAAAAA==/", "_etag": "\"a8006d1d-0000-0100-0000-68703ac50000\"", "_attachments": "attachments/", "_ts": 1752185541}, {"payPeriodId": "1140035588191389", "intervalCode": "MONTHLY", "status": "INITIAL", "description": "Monthly Payroll (1)", "startDate": "2025-05-01T00:00:00Z", "endDate": "2025-05-31T00:00:00Z", "submitByDate": "2025-05-30T00:00:00Z", "checkDate": "2025-05-31T00:00:00Z", "checkCount": 0, "id": "beb7458c-0732-49ea-a062-e88a37dc2046", "companyId": "19008831", "type": "payperiod", "_rid": "NmJkAKiCbEeELQQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeELQQAAAAAAA==/", "_etag": "\"a8006f1d-0000-0100-0000-68703ac50000\"", "_attachments": "attachments/", "_ts": 1752185541}, {"payPeriodId": "1140035703550964", "intervalCode": "MONTHLY", "status": "INITIAL", "description": "Monthly Payroll (1)", "startDate": "2025-06-01T00:00:00Z", "endDate": "2025-06-30T00:00:00Z", "submitByDate": "2025-06-29T00:00:00Z", "checkDate": "2025-06-30T00:00:00Z", "checkCount": 0, "id": "ca4c5400-7a81-4d50-b149-9765d00e2b75", "companyId": "19008831", "type": "payperiod", "_rid": "NmJkAKiCbEeFLQQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeFLQQAAAAAAA==/", "_etag": "\"a800701d-0000-0100-0000-68703ac60000\"", "_attachments": "attachments/", "_ts": 1752185542}, {"payPeriodId": "1140035845469368", "intervalCode": "MONTHLY", "status": "INITIAL", "description": "Monthly Payroll (1)", "startDate": "2025-07-01T00:00:00Z", "endDate": "2025-07-31T00:00:00Z", "submitByDate": "2025-07-30T00:00:00Z", "checkDate": "2025-07-31T00:00:00Z", "checkCount": 0, "id": "c6f87df0-9b7d-4822-9140-6903c53cbb7f", "companyId": "19008831", "type": "payperiod", "_rid": "NmJkAKiCbEeGLQQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeGLQQAAAAAAA==/", "_etag": "\"a800721d-0000-0100-0000-68703ac60000\"", "_attachments": "attachments/", "_ts": 1752185542}, {"payPeriodId": "1140035965999862", "intervalCode": "MONTHLY", "status": "INITIAL", "description": "Monthly Payroll (1)", "startDate": "2025-08-01T00:00:00Z", "endDate": "2025-08-31T00:00:00Z", "submitByDate": "2025-07-30T00:00:00Z", "checkDate": "2025-08-01T00:00:00Z", "checkCount": 0, "id": "35ae084e-c5e5-4e04-9e77-8932a46cbe10", "companyId": "19008831", "type": "payperiod", "_rid": "NmJkAKiCbEeHLQQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeHLQQAAAAAAA==/", "_etag": "\"a800741d-0000-0100-0000-68703ac60000\"", "_attachments": "attachments/", "_ts": 1752185542}, {"payPeriodId": "1140036117687213", "intervalCode": "MONTHLY", "status": "INITIAL", "description": "Monthly Payroll (1)", "startDate": "2025-09-01T00:00:00Z", "endDate": "2025-09-30T00:00:00Z", "submitByDate": "2025-08-27T00:00:00Z", "checkDate": "2025-08-29T00:00:00Z", "checkCount": 0, "id": "d5d12114-09af-44ed-a316-81e13a287566", "companyId": "19008831", "type": "payperiod", "_rid": "NmJkAKiCbEeILQQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeILQQAAAAAAA==/", "_etag": "\"a800761d-0000-0100-0000-68703ac60000\"", "_attachments": "attachments/", "_ts": 1752185542}, {"payPeriodId": "1140036227488671", "intervalCode": "MONTHLY", "status": "INITIAL", "description": "Monthly Payroll (1)", "startDate": "2025-10-01T00:00:00Z", "endDate": "2025-10-31T00:00:00Z", "submitByDate": "2025-09-29T00:00:00Z", "checkDate": "2025-10-01T00:00:00Z", "checkCount": 0, "id": "d457378a-1e98-497c-8d60-3fe991529f87", "companyId": "19008831", "type": "payperiod", "_rid": "NmJkAKiCbEeJLQQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeJLQQAAAAAAA==/", "_etag": "\"a8007b1d-0000-0100-0000-68703ac60000\"", "_attachments": "attachments/", "_ts": 1752185542}, {"payPeriodId": "1140035183594218", "intervalCode": "MONTHLY", "status": "COMPLETED", "description": "Monthly Payroll (1)", "startDate": "2025-02-01T00:00:00Z", "endDate": "2025-02-28T00:00:00Z", "submitByDate": "2025-02-27T00:00:00Z", "checkDate": "2025-02-28T00:00:00Z", "checkCount": 6, "id": "263e73d6-a077-4599-b938-56df128f788b", "companyId": "19008831", "type": "payperiod", "_rid": "NmJkAKiCbEeSLQQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeSLQQAAAAAAA==/", "_etag": "\"a8008c1d-0000-0100-0000-68703ac70000\"", "_attachments": "attachments/", "_ts": 1752185543}, {"payPeriodId": "1140035324869338", "intervalCode": "MONTHLY", "status": "COMPLETED", "description": "Monthly Payroll (1)", "startDate": "2025-03-01T00:00:00Z", "endDate": "2025-03-31T00:00:00Z", "submitByDate": "2025-03-30T00:00:00Z", "checkDate": "2025-03-31T00:00:00Z", "checkCount": 6, "id": "e6fffbd3-b0c9-48d6-8cb4-327fed1649e5", "companyId": "19008831", "type": "payperiod", "_rid": "NmJkAKiCbEeTLQQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeTLQQAAAAAAA==/", "_etag": "\"a8008d1d-0000-0100-0000-68703ac70000\"", "_attachments": "attachments/", "_ts": 1752185543}, {"payPeriodId": "1140035936799677", "status": "COMPLETED", "description": "Payroll", "startDate": "2025-04-01T00:00:00Z", "endDate": "2025-04-30T00:00:00Z", "submitByDate": "2025-04-29T00:00:00Z", "checkDate": "2025-04-30T00:00:00Z", "checkCount": 6, "id": "b6766128-d2f8-457b-897b-cd02fae75ff1", "companyId": "19008831", "type": "payperiod", "_rid": "NmJkAKiCbEeULQQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeULQQAAAAAAA==/", "_etag": "\"a8008f1d-0000-0100-0000-68703ac70000\"", "_attachments": "attachments/", "_ts": 1752185543}, {"payPeriodId": "1140035588191389", "intervalCode": "MONTHLY", "status": "COMPLETED", "description": "Monthly Payroll (1)", "startDate": "2025-05-01T00:00:00Z", "endDate": "2025-05-31T00:00:00Z", "submitByDate": "2025-05-30T00:00:00Z", "checkDate": "2025-05-31T00:00:00Z", "checkCount": 6, "id": "211e7927-9b5b-42b9-9471-3a0904e4e36f", "companyId": "19008831", "type": "payperiod", "_rid": "NmJkAKiCbEeVLQQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeVLQQAAAAAAA==/", "_etag": "\"a800931d-0000-0100-0000-68703ac70000\"", "_attachments": "attachments/", "_ts": 1752185543}, {"payPeriodId": "1140035703550964", "intervalCode": "MONTHLY", "status": "INITIAL", "description": "Monthly Payroll (1)", "startDate": "2025-06-01T00:00:00Z", "endDate": "2025-06-30T00:00:00Z", "submitByDate": "2025-06-29T00:00:00Z", "checkDate": "2025-06-30T00:00:00Z", "checkCount": 0, "id": "b87f26a0-3953-47d1-a80b-a3bc03744646", "companyId": "19008831", "type": "payperiod", "_rid": "NmJkAKiCbEeWLQQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeWLQQAAAAAAA==/", "_etag": "\"a800941d-0000-0100-0000-68703ac70000\"", "_attachments": "attachments/", "_ts": 1752185543}, {"payPeriodId": "1140035845469368", "intervalCode": "MONTHLY", "status": "INITIAL", "description": "Monthly Payroll (1)", "startDate": "2025-07-01T00:00:00Z", "endDate": "2025-07-31T00:00:00Z", "submitByDate": "2025-07-30T00:00:00Z", "checkDate": "2025-07-31T00:00:00Z", "checkCount": 0, "id": "cc29f4b3-d1f2-43ff-87bd-26edf8e9b0c7", "companyId": "19008831", "type": "payperiod", "_rid": "NmJkAKiCbEeXLQQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeXLQQAAAAAAA==/", "_etag": "\"a800971d-0000-0100-0000-68703ac70000\"", "_attachments": "attachments/", "_ts": 1752185543}, {"payPeriodId": "1140035965999862", "intervalCode": "MONTHLY", "status": "INITIAL", "description": "Monthly Payroll (1)", "startDate": "2025-08-01T00:00:00Z", "endDate": "2025-08-31T00:00:00Z", "submitByDate": "2025-07-30T00:00:00Z", "checkDate": "2025-08-01T00:00:00Z", "checkCount": 0, "id": "c129e052-2c13-4910-bdd1-30ecaecd6b51", "companyId": "19008831", "type": "payperiod", "_rid": "NmJkAKiCbEeYLQQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeYLQQAAAAAAA==/", "_etag": "\"a800981d-0000-0100-0000-68703ac70000\"", "_attachments": "attachments/", "_ts": 1752185543}, {"payPeriodId": "1140036117687213", "intervalCode": "MONTHLY", "status": "INITIAL", "description": "Monthly Payroll (1)", "startDate": "2025-09-01T00:00:00Z", "endDate": "2025-09-30T00:00:00Z", "submitByDate": "2025-08-27T00:00:00Z", "checkDate": "2025-08-29T00:00:00Z", "checkCount": 0, "id": "479bd0d1-d418-4577-b7d4-77d4ea8c8b26", "companyId": "19008831", "type": "payperiod", "_rid": "NmJkAKiCbEeZLQQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeZLQQAAAAAAA==/", "_etag": "\"a800991d-0000-0100-0000-68703ac70000\"", "_attachments": "attachments/", "_ts": 1752185543}, {"payPeriodId": "1140036227488671", "intervalCode": "MONTHLY", "status": "INITIAL", "description": "Monthly Payroll (1)", "startDate": "2025-10-01T00:00:00Z", "endDate": "2025-10-31T00:00:00Z", "submitByDate": "2025-09-29T00:00:00Z", "checkDate": "2025-10-01T00:00:00Z", "checkCount": 0, "id": "ee3a0e74-303a-4298-b172-11be4c79cbc2", "companyId": "19008831", "type": "payperiod", "_rid": "NmJkAKiCbEeaLQQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeaLQQAAAAAAA==/", "_etag": "\"a8009f1d-0000-0100-0000-68703ac70000\"", "_attachments": "attachments/", "_ts": 1752185543}], "links": [{"rel": "self", "href": "https://ca-payroll-ai-mock-sb-001-secure.nicebeach-8a7a52ab.eastus.azurecontainerapps.io/ca/companies/19008831/payperiods"}]}, "status_code": 200}