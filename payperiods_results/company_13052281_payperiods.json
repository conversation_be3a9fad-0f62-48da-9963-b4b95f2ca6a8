{"success": true, "company_id": "13052281", "data": {"metadata": {"contentItemCount": 40}, "content": [{"payPeriodId": "1040046183035247", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (2)", "startDate": "2024-12-28T00:00:00Z", "endDate": "2025-01-13T00:00:00Z", "submitByDate": "2025-01-13T00:00:00Z", "checkDate": "2025-01-15T00:00:00Z", "checkCount": 2, "id": "42c615d1-a252-4f6c-9b0a-8f9968e0376a", "companyId": "13052281", "type": "payperiod", "_rid": "NmJkAKiCbEd4HgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEd4HgMAAAAAAA==/", "_etag": "\"a500161c-0000-0100-0000-687024d80000\"", "_attachments": "attachments/", "_ts": 1752179928}, {"payPeriodId": "1040046183035248", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (2)", "startDate": "2025-01-14T00:00:00Z", "endDate": "2025-01-27T00:00:00Z", "submitByDate": "2025-01-29T00:00:00Z", "checkDate": "2025-01-31T00:00:00Z", "checkCount": 2, "id": "9edd35f5-2c4c-4037-a268-c0f8ba1bfb22", "companyId": "13052281", "type": "payperiod", "_rid": "NmJkAKiCbEd5HgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEd5HgMAAAAAAA==/", "_etag": "\"a500191c-0000-0100-0000-687024d80000\"", "_attachments": "attachments/", "_ts": 1752179928}, {"payPeriodId": "1040046372669503", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (2)", "startDate": "2025-01-28T00:00:00Z", "endDate": "2025-02-13T00:00:00Z", "submitByDate": "2025-02-12T00:00:00Z", "checkDate": "2025-02-14T00:00:00Z", "checkCount": 2, "id": "111e0f9f-0339-470e-aac9-e8cc94a5740c", "companyId": "13052281", "type": "payperiod", "_rid": "NmJkAKiCbEd6HgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEd6HgMAAAAAAA==/", "_etag": "\"a5001c1c-0000-0100-0000-687024d80000\"", "_attachments": "attachments/", "_ts": 1752179928}, {"payPeriodId": "1040046372669504", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (2)", "startDate": "2025-02-14T00:00:00Z", "endDate": "2025-02-27T00:00:00Z", "submitByDate": "2025-02-26T00:00:00Z", "checkDate": "2025-02-28T00:00:00Z", "checkCount": 2, "id": "4e46d59f-d631-42ad-8344-31842d723099", "companyId": "13052281", "type": "payperiod", "_rid": "NmJkAKiCbEd7HgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEd7HgMAAAAAAA==/", "_etag": "\"a5001e1c-0000-0100-0000-687024d80000\"", "_attachments": "attachments/", "_ts": 1752179928}, {"payPeriodId": "1040046569149097", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (2)", "startDate": "2025-02-28T00:00:00Z", "endDate": "2025-03-13T00:00:00Z", "submitByDate": "2025-03-12T00:00:00Z", "checkDate": "2025-03-14T00:00:00Z", "checkCount": 2, "id": "200a4858-0b2e-4361-8591-cb066711a5be", "companyId": "13052281", "type": "payperiod", "_rid": "NmJkAKiCbEd8HgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEd8HgMAAAAAAA==/", "_etag": "\"a500211c-0000-0100-0000-687024d80000\"", "_attachments": "attachments/", "_ts": 1752179928}, {"payPeriodId": "1040046569149098", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (2)", "startDate": "2025-03-14T00:00:00Z", "endDate": "2025-03-27T00:00:00Z", "submitByDate": "2025-03-27T00:00:00Z", "checkDate": "2025-03-31T00:00:00Z", "checkCount": 2, "id": "4dd19935-7f43-46aa-bbb5-ca8e53db2f12", "companyId": "13052281", "type": "payperiod", "_rid": "NmJkAKiCbEd9HgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEd9HgMAAAAAAA==/", "_etag": "\"a500241c-0000-0100-0000-687024d80000\"", "_attachments": "attachments/", "_ts": 1752179928}, {"payPeriodId": "1040046780276019", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (2)", "startDate": "2025-03-31T00:00:00Z", "endDate": "2025-04-13T00:00:00Z", "submitByDate": "2025-04-11T00:00:00Z", "checkDate": "2025-04-15T00:00:00Z", "checkCount": 0, "id": "dcb1148c-37b3-4397-9a53-4d6616eacdac", "companyId": "13052281", "type": "payperiod", "_rid": "NmJkAKiCbEd+HgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEd+HgMAAAAAAA==/", "_etag": "\"a500271c-0000-0100-0000-687024d80000\"", "_attachments": "attachments/", "_ts": 1752179928}, {"payPeriodId": "1040046780276020", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (2)", "startDate": "2025-04-14T00:00:00Z", "endDate": "2025-04-29T00:00:00Z", "submitByDate": "2025-04-28T00:00:00Z", "checkDate": "2025-04-30T00:00:00Z", "checkCount": 0, "id": "1ee81d26-ac20-48b3-a101-246717a143ca", "companyId": "13052281", "type": "payperiod", "_rid": "NmJkAKiCbEd-HgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEd-HgMAAAAAAA==/", "_etag": "\"a5002c1c-0000-0100-0000-687024d80000\"", "_attachments": "attachments/", "_ts": 1752179928}, {"payPeriodId": "1040046903097418", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (2)", "startDate": "2025-04-28T00:00:00Z", "endDate": "2025-05-13T00:00:00Z", "submitByDate": "2025-05-13T00:00:00Z", "checkDate": "2025-05-15T00:00:00Z", "checkCount": 0, "id": "6efceced-6ece-4d93-b53b-63e533bdc86b", "companyId": "13052281", "type": "payperiod", "_rid": "NmJkAKiCbEeAHgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeAHgMAAAAAAA==/", "_etag": "\"a5002f1c-0000-0100-0000-687024d80000\"", "_attachments": "attachments/", "_ts": 1752179928}, {"payPeriodId": "1040046903097419", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (2)", "startDate": "2025-05-14T00:00:00Z", "endDate": "2025-05-27T00:00:00Z", "submitByDate": "2025-05-28T00:00:00Z", "checkDate": "2025-05-30T00:00:00Z", "checkCount": 0, "id": "54cf6553-8850-4b36-9cc2-bcc3a8482bd0", "companyId": "13052281", "type": "payperiod", "_rid": "NmJkAKiCbEeBHgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeBHgMAAAAAAA==/", "_etag": "\"a500381c-0000-0100-0000-687024d80000\"", "_attachments": "attachments/", "_ts": 1752179928}, {"payPeriodId": "1040047119632922", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (2)", "startDate": "2025-05-28T00:00:00Z", "endDate": "2025-06-13T00:00:00Z", "submitByDate": "2025-06-11T00:00:00Z", "checkDate": "2025-06-13T00:00:00Z", "checkCount": 0, "id": "a6e5020a-96a8-4e6b-a30a-a5f1bb80b9f1", "companyId": "13052281", "type": "payperiod", "_rid": "NmJkAKiCbEeCHgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeCHgMAAAAAAA==/", "_etag": "\"a500421c-0000-0100-0000-687024d80000\"", "_attachments": "attachments/", "_ts": 1752179928}, {"payPeriodId": "1040047119632923", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (2)", "startDate": "2025-06-14T00:00:00Z", "endDate": "2025-06-27T00:00:00Z", "submitByDate": "2025-06-26T00:00:00Z", "checkDate": "2025-06-30T00:00:00Z", "checkCount": 0, "id": "e8206730-bf1e-4488-972e-dc3851204f46", "companyId": "13052281", "type": "payperiod", "_rid": "NmJkAKiCbEeDHgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeDHgMAAAAAAA==/", "_etag": "\"a500451c-0000-0100-0000-687024d80000\"", "_attachments": "attachments/", "_ts": 1752179928}, {"payPeriodId": "1040047217972970", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (2)", "startDate": "2025-06-28T00:00:00Z", "endDate": "2025-07-13T00:00:00Z", "submitByDate": "2025-07-11T00:00:00Z", "checkDate": "2025-07-15T00:00:00Z", "checkCount": 0, "id": "c81e36c2-9dbc-4838-a461-c51d821c111d", "companyId": "13052281", "type": "payperiod", "_rid": "NmJkAKiCbEeEHgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeEHgMAAAAAAA==/", "_etag": "\"a500481c-0000-0100-0000-687024d80000\"", "_attachments": "attachments/", "_ts": 1752179928}, {"payPeriodId": "1040047217972971", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (2)", "startDate": "2025-07-14T00:00:00Z", "endDate": "2025-07-27T00:00:00Z", "submitByDate": "2025-07-29T00:00:00Z", "checkDate": "2025-07-31T00:00:00Z", "checkCount": 0, "id": "c4b7d3a0-085c-4cae-ae5f-42ea6846b2b3", "companyId": "13052281", "type": "payperiod", "_rid": "NmJkAKiCbEeFHgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeFHgMAAAAAAA==/", "_etag": "\"a5004a1c-0000-0100-0000-687024d90000\"", "_attachments": "attachments/", "_ts": 1752179929}, {"payPeriodId": "1040047397043527", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (2)", "startDate": "2025-07-28T00:00:00Z", "endDate": "2025-08-13T00:00:00Z", "submitByDate": "2025-08-13T00:00:00Z", "checkDate": "2025-08-15T00:00:00Z", "checkCount": 0, "id": "c0a7a2ff-6bcc-4175-946b-805d8f3ab8f4", "companyId": "13052281", "type": "payperiod", "_rid": "NmJkAKiCbEeGHgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeGHgMAAAAAAA==/", "_etag": "\"a5004d1c-0000-0100-0000-687024d90000\"", "_attachments": "attachments/", "_ts": 1752179929}, {"payPeriodId": "1040047397043528", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (2)", "startDate": "2025-08-14T00:00:00Z", "endDate": "2025-08-27T00:00:00Z", "submitByDate": "2025-08-27T00:00:00Z", "checkDate": "2025-08-29T00:00:00Z", "checkCount": 0, "id": "b85a765b-d2e9-4b4e-a431-efb88fdd75b1", "companyId": "13052281", "type": "payperiod", "_rid": "NmJkAKiCbEeHHgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeHHgMAAAAAAA==/", "_etag": "\"a5004f1c-0000-0100-0000-687024d90000\"", "_attachments": "attachments/", "_ts": 1752179929}, {"payPeriodId": "1040047563149376", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (2)", "startDate": "2025-08-28T00:00:00Z", "endDate": "2025-09-13T00:00:00Z", "submitByDate": "2025-09-11T00:00:00Z", "checkDate": "2025-09-15T00:00:00Z", "checkCount": 0, "id": "c850aa68-af12-4de1-af40-a00ba1cd2167", "companyId": "13052281", "type": "payperiod", "_rid": "NmJkAKiCbEeIHgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeIHgMAAAAAAA==/", "_etag": "\"a500531c-0000-0100-0000-687024d90000\"", "_attachments": "attachments/", "_ts": 1752179929}, {"payPeriodId": "1040047563149377", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (2)", "startDate": "2025-09-14T00:00:00Z", "endDate": "2025-09-27T00:00:00Z", "submitByDate": "2025-09-26T00:00:00Z", "checkDate": "2025-09-30T00:00:00Z", "checkCount": 0, "id": "42201bc0-a860-4111-a2e1-fccbc3e636f7", "companyId": "13052281", "type": "payperiod", "_rid": "NmJkAKiCbEeJHgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeJHgMAAAAAAA==/", "_etag": "\"a500561c-0000-0100-0000-687024d90000\"", "_attachments": "attachments/", "_ts": 1752179929}, {"payPeriodId": "1040047731384718", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (2)", "startDate": "2025-09-28T00:00:00Z", "endDate": "2025-10-13T00:00:00Z", "submitByDate": "2025-10-13T00:00:00Z", "checkDate": "2025-10-15T00:00:00Z", "checkCount": 0, "id": "5ea0e5ce-ffb4-4ad5-9583-8ef709dc0a27", "companyId": "13052281", "type": "payperiod", "_rid": "NmJkAKiCbEeKHgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeKHgMAAAAAAA==/", "_etag": "\"a5005b1c-0000-0100-0000-687024d90000\"", "_attachments": "attachments/", "_ts": 1752179929}, {"payPeriodId": "1040047731384719", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (2)", "startDate": "2025-10-14T00:00:00Z", "endDate": "2025-10-27T00:00:00Z", "submitByDate": "2025-10-29T00:00:00Z", "checkDate": "2025-10-31T00:00:00Z", "checkCount": 0, "id": "c8becd56-084c-41ed-b0a5-e578dde4b981", "companyId": "13052281", "type": "payperiod", "_rid": "NmJkAKiCbEeLHgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeLHgMAAAAAAA==/", "_etag": "\"a500601c-0000-0100-0000-687024d90000\"", "_attachments": "attachments/", "_ts": 1752179929}, {"payPeriodId": "1040046183035247", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (2)", "startDate": "2024-12-28T00:00:00Z", "endDate": "2025-01-13T00:00:00Z", "submitByDate": "2025-01-13T00:00:00Z", "checkDate": "2025-01-15T00:00:00Z", "checkCount": 2, "id": "4b4afdca-ad5f-475f-b668-80886b8f2318", "companyId": "13052281", "type": "payperiod", "_rid": "NmJkAKiCbEeVHgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeVHgMAAAAAAA==/", "_etag": "\"a500841c-0000-0100-0000-687024da0000\"", "_attachments": "attachments/", "_ts": 1752179930}, {"payPeriodId": "1040046183035248", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (2)", "startDate": "2025-01-14T00:00:00Z", "endDate": "2025-01-27T00:00:00Z", "submitByDate": "2025-01-29T00:00:00Z", "checkDate": "2025-01-31T00:00:00Z", "checkCount": 2, "id": "0248c8ef-3080-46c8-9fc8-5848cb1a11f5", "companyId": "13052281", "type": "payperiod", "_rid": "NmJkAKiCbEeWHgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeWHgMAAAAAAA==/", "_etag": "\"a5008b1c-0000-0100-0000-687024da0000\"", "_attachments": "attachments/", "_ts": 1752179930}, {"payPeriodId": "1040046372669503", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (2)", "startDate": "2025-01-28T00:00:00Z", "endDate": "2025-02-13T00:00:00Z", "submitByDate": "2025-02-12T00:00:00Z", "checkDate": "2025-02-14T00:00:00Z", "checkCount": 2, "id": "e388e594-fcd6-4342-b9b7-6304c4a26de5", "companyId": "13052281", "type": "payperiod", "_rid": "NmJkAKiCbEeXHgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeXHgMAAAAAAA==/", "_etag": "\"a5008f1c-0000-0100-0000-687024da0000\"", "_attachments": "attachments/", "_ts": 1752179930}, {"payPeriodId": "1040046372669504", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (2)", "startDate": "2025-02-14T00:00:00Z", "endDate": "2025-02-27T00:00:00Z", "submitByDate": "2025-02-26T00:00:00Z", "checkDate": "2025-02-28T00:00:00Z", "checkCount": 2, "id": "63ee1f94-6900-4f60-ad61-b6167bc34d94", "companyId": "13052281", "type": "payperiod", "_rid": "NmJkAKiCbEeYHgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeYHgMAAAAAAA==/", "_etag": "\"a500901c-0000-0100-0000-687024da0000\"", "_attachments": "attachments/", "_ts": 1752179930}, {"payPeriodId": "1040046569149097", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (2)", "startDate": "2025-02-28T00:00:00Z", "endDate": "2025-03-13T00:00:00Z", "submitByDate": "2025-03-12T00:00:00Z", "checkDate": "2025-03-14T00:00:00Z", "checkCount": 2, "id": "d8ae53c8-5f55-4a71-bd2d-ab54079573e4", "companyId": "13052281", "type": "payperiod", "_rid": "NmJkAKiCbEeZHgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeZHgMAAAAAAA==/", "_etag": "\"a500931c-0000-0100-0000-687024da0000\"", "_attachments": "attachments/", "_ts": 1752179930}, {"payPeriodId": "1040046569149098", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (2)", "startDate": "2025-03-14T00:00:00Z", "endDate": "2025-03-27T00:00:00Z", "submitByDate": "2025-03-27T00:00:00Z", "checkDate": "2025-03-31T00:00:00Z", "checkCount": 2, "id": "cfa0523a-98bc-44ab-9f4b-aa78f036a05a", "companyId": "13052281", "type": "payperiod", "_rid": "NmJkAKiCbEeaHgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeaHgMAAAAAAA==/", "_etag": "\"a5009b1c-0000-0100-0000-687024da0000\"", "_attachments": "attachments/", "_ts": 1752179930}, {"payPeriodId": "1040046780276019", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (2)", "startDate": "2025-03-31T00:00:00Z", "endDate": "2025-04-13T00:00:00Z", "submitByDate": "2025-04-11T00:00:00Z", "checkDate": "2025-04-15T00:00:00Z", "checkCount": 2, "id": "1108c89b-dde6-4128-83a3-ca2b52161d8d", "companyId": "13052281", "type": "payperiod", "_rid": "NmJkAKiCbEebHgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEebHgMAAAAAAA==/", "_etag": "\"a5009e1c-0000-0100-0000-687024da0000\"", "_attachments": "attachments/", "_ts": 1752179930}, {"payPeriodId": "1040046780276020", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (2)", "startDate": "2025-04-14T00:00:00Z", "endDate": "2025-04-29T00:00:00Z", "submitByDate": "2025-04-28T00:00:00Z", "checkDate": "2025-04-30T00:00:00Z", "checkCount": 2, "id": "a827538f-37aa-4633-bd24-740c00682920", "companyId": "13052281", "type": "payperiod", "_rid": "NmJkAKiCbEecHgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEecHgMAAAAAAA==/", "_etag": "\"a500a21c-0000-0100-0000-687024da0000\"", "_attachments": "attachments/", "_ts": 1752179930}, {"payPeriodId": "1040046903097418", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (2)", "startDate": "2025-04-28T00:00:00Z", "endDate": "2025-05-13T00:00:00Z", "submitByDate": "2025-05-13T00:00:00Z", "checkDate": "2025-05-15T00:00:00Z", "checkCount": 2, "id": "409dfcd2-ae91-460e-aa0c-0eb986d85d79", "companyId": "13052281", "type": "payperiod", "_rid": "NmJkAKiCbEedHgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEedHgMAAAAAAA==/", "_etag": "\"a500a51c-0000-0100-0000-687024da0000\"", "_attachments": "attachments/", "_ts": 1752179930}, {"payPeriodId": "1040046903097419", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (2)", "startDate": "2025-05-14T00:00:00Z", "endDate": "2025-05-27T00:00:00Z", "submitByDate": "2025-05-28T00:00:00Z", "checkDate": "2025-05-30T00:00:00Z", "checkCount": 2, "id": "55a31d88-c007-4597-9632-41bd1a5496e9", "companyId": "13052281", "type": "payperiod", "_rid": "NmJkAKiCbEeeHgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeeHgMAAAAAAA==/", "_etag": "\"a500a91c-0000-0100-0000-687024db0000\"", "_attachments": "attachments/", "_ts": 1752179931}, {"payPeriodId": "1040047119632922", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (2)", "startDate": "2025-05-28T00:00:00Z", "endDate": "2025-06-13T00:00:00Z", "submitByDate": "2025-06-11T00:00:00Z", "checkDate": "2025-06-13T00:00:00Z", "checkCount": 2, "id": "f7625be9-3397-40e5-a583-7d85baa05140", "companyId": "13052281", "type": "payperiod", "_rid": "NmJkAKiCbEefHgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEefHgMAAAAAAA==/", "_etag": "\"a500ac1c-0000-0100-0000-687024db0000\"", "_attachments": "attachments/", "_ts": 1752179931}, {"payPeriodId": "1040047119632923", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (2)", "startDate": "2025-06-14T00:00:00Z", "endDate": "2025-06-27T00:00:00Z", "submitByDate": "2025-06-26T00:00:00Z", "checkDate": "2025-06-30T00:00:00Z", "checkCount": 2, "id": "859c7e4c-b17b-45d7-a2e5-be81ebfdc868", "companyId": "13052281", "type": "payperiod", "_rid": "NmJkAKiCbEegHgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEegHgMAAAAAAA==/", "_etag": "\"a500b01c-0000-0100-0000-687024db0000\"", "_attachments": "attachments/", "_ts": 1752179931}, {"payPeriodId": "1040047217972970", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (2)", "startDate": "2025-06-28T00:00:00Z", "endDate": "2025-07-13T00:00:00Z", "submitByDate": "2025-07-11T00:00:00Z", "checkDate": "2025-07-15T00:00:00Z", "checkCount": 0, "id": "4d3c51b3-9337-4fad-bfac-5880ca94dbf5", "companyId": "13052281", "type": "payperiod", "_rid": "NmJkAKiCbEehHgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEehHgMAAAAAAA==/", "_etag": "\"a500b31c-0000-0100-0000-687024db0000\"", "_attachments": "attachments/", "_ts": 1752179931}, {"payPeriodId": "1040047217972971", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (2)", "startDate": "2025-07-14T00:00:00Z", "endDate": "2025-07-27T00:00:00Z", "submitByDate": "2025-07-29T00:00:00Z", "checkDate": "2025-07-31T00:00:00Z", "checkCount": 0, "id": "ba12d029-6aa1-4371-8749-e78385e1d2c4", "companyId": "13052281", "type": "payperiod", "_rid": "NmJkAKiCbEeiHgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeiHgMAAAAAAA==/", "_etag": "\"a500b51c-0000-0100-0000-687024db0000\"", "_attachments": "attachments/", "_ts": 1752179931}, {"payPeriodId": "1040047397043527", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (2)", "startDate": "2025-07-28T00:00:00Z", "endDate": "2025-08-13T00:00:00Z", "submitByDate": "2025-08-13T00:00:00Z", "checkDate": "2025-08-15T00:00:00Z", "checkCount": 0, "id": "5113af4b-2951-4393-a00a-a26b05b0955d", "companyId": "13052281", "type": "payperiod", "_rid": "NmJkAKiCbEejHgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEejHgMAAAAAAA==/", "_etag": "\"a500b91c-0000-0100-0000-687024db0000\"", "_attachments": "attachments/", "_ts": 1752179931}, {"payPeriodId": "1040047397043528", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (2)", "startDate": "2025-08-14T00:00:00Z", "endDate": "2025-08-27T00:00:00Z", "submitByDate": "2025-08-27T00:00:00Z", "checkDate": "2025-08-29T00:00:00Z", "checkCount": 0, "id": "e6c3d835-e61f-4a92-9b87-35dd58857f87", "companyId": "13052281", "type": "payperiod", "_rid": "NmJkAKiCbEekHgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEekHgMAAAAAAA==/", "_etag": "\"a500ba1c-0000-0100-0000-687024db0000\"", "_attachments": "attachments/", "_ts": 1752179931}, {"payPeriodId": "1040047563149376", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (2)", "startDate": "2025-08-28T00:00:00Z", "endDate": "2025-09-13T00:00:00Z", "submitByDate": "2025-09-11T00:00:00Z", "checkDate": "2025-09-15T00:00:00Z", "checkCount": 0, "id": "0cdaf009-8521-4660-a44a-8180b72fd904", "companyId": "13052281", "type": "payperiod", "_rid": "NmJkAKiCbEelHgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEelHgMAAAAAAA==/", "_etag": "\"a500bd1c-0000-0100-0000-687024db0000\"", "_attachments": "attachments/", "_ts": 1752179931}, {"payPeriodId": "1040047563149377", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (2)", "startDate": "2025-09-14T00:00:00Z", "endDate": "2025-09-27T00:00:00Z", "submitByDate": "2025-09-26T00:00:00Z", "checkDate": "2025-09-30T00:00:00Z", "checkCount": 0, "id": "9c982458-430c-495c-90be-3882c520da6f", "companyId": "13052281", "type": "payperiod", "_rid": "NmJkAKiCbEemHgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEemHgMAAAAAAA==/", "_etag": "\"a500c41c-0000-0100-0000-687024db0000\"", "_attachments": "attachments/", "_ts": 1752179931}, {"payPeriodId": "1040047731384718", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (2)", "startDate": "2025-09-28T00:00:00Z", "endDate": "2025-10-13T00:00:00Z", "submitByDate": "2025-10-13T00:00:00Z", "checkDate": "2025-10-15T00:00:00Z", "checkCount": 0, "id": "70143bff-c0d7-4857-900d-179479b3f4b6", "companyId": "13052281", "type": "payperiod", "_rid": "NmJkAKiCbEenHgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEenHgMAAAAAAA==/", "_etag": "\"a500c71c-0000-0100-0000-687024db0000\"", "_attachments": "attachments/", "_ts": 1752179931}, {"payPeriodId": "1040047731384719", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (2)", "startDate": "2025-10-14T00:00:00Z", "endDate": "2025-10-27T00:00:00Z", "submitByDate": "2025-10-29T00:00:00Z", "checkDate": "2025-10-31T00:00:00Z", "checkCount": 0, "id": "4abcf8a9-4a15-4902-8f45-9df0a35d166a", "companyId": "13052281", "type": "payperiod", "_rid": "NmJkAKiCbEeoHgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeoHgMAAAAAAA==/", "_etag": "\"a500ca1c-0000-0100-0000-687024db0000\"", "_attachments": "attachments/", "_ts": 1752179931}], "links": [{"rel": "self", "href": "https://ca-payroll-ai-mock-sb-001-secure.nicebeach-8a7a52ab.eastus.azurecontainerapps.io/ca/companies/13052281/payperiods"}]}, "status_code": 200}