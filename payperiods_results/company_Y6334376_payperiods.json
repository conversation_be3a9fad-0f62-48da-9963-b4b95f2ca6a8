{"success": true, "company_id": "Y6334376", "data": {"metadata": {"contentItemCount": 20}, "content": [{"payPeriodId": "1020050187258553", "intervalCode": "MONTHLY", "status": "COMPLETED", "description": "Monthly Payroll (1)", "startDate": "2025-01-01T00:00:00Z", "endDate": "2025-01-31T00:00:00Z", "submitByDate": "2025-01-29T00:00:00Z", "checkDate": "2025-01-31T00:00:00Z", "checkCount": 2, "id": "145e509f-feac-4d72-83f8-64e11d25f917", "companyId": "Y6334376", "type": "payperiod", "_rid": "NmJkAKiCbEca1QQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEca1QQAAAAAAA==/", "_etag": "\"a900a797-0000-0100-0000-6870494b0000\"", "_attachments": "attachments/", "_ts": 1752189259}, {"payPeriodId": "1020050443567147", "intervalCode": "MONTHLY", "status": "COMPLETED", "description": "Monthly Payroll (1)", "startDate": "2025-02-01T00:00:00Z", "endDate": "2025-02-28T00:00:00Z", "submitByDate": "2025-02-26T00:00:00Z", "checkDate": "2025-02-28T00:00:00Z", "checkCount": 2, "id": "79573d45-35a4-475b-9d02-31031709413f", "companyId": "Y6334376", "type": "payperiod", "_rid": "NmJkAKiCbEcb1QQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcb1QQAAAAAAA==/", "_etag": "\"a900a997-0000-0100-0000-6870494b0000\"", "_attachments": "attachments/", "_ts": 1752189259}, {"payPeriodId": "1020050743524696", "intervalCode": "MONTHLY", "status": "COMPLETED", "description": "Monthly Payroll (1)", "startDate": "2025-03-01T00:00:00Z", "endDate": "2025-03-31T00:00:00Z", "submitByDate": "2025-03-27T00:00:00Z", "checkDate": "2025-03-31T00:00:00Z", "checkCount": 2, "id": "cda9476e-a80e-4e64-9660-f024ef0ec1d1", "companyId": "Y6334376", "type": "payperiod", "_rid": "NmJkAKiCbEcc1QQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcc1QQAAAAAAA==/", "_etag": "\"a900ac97-0000-0100-0000-6870494b0000\"", "_attachments": "attachments/", "_ts": 1752189259}, {"payPeriodId": "1020051000203620", "intervalCode": "MONTHLY", "status": "INITIAL", "description": "Monthly Payroll (1)", "startDate": "2025-04-01T00:00:00Z", "endDate": "2025-04-30T00:00:00Z", "submitByDate": "2025-04-28T00:00:00Z", "checkDate": "2025-04-30T00:00:00Z", "checkCount": 0, "id": "06c2a6b9-d07b-4891-b7dc-aef2d126a3ac", "companyId": "Y6334376", "type": "payperiod", "_rid": "NmJkAKiCbEcd1QQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcd1QQAAAAAAA==/", "_etag": "\"a900ad97-0000-0100-0000-6870494c0000\"", "_attachments": "attachments/", "_ts": 1752189260}, {"payPeriodId": "1020051232390789", "intervalCode": "MONTHLY", "status": "INITIAL", "description": "Monthly Payroll (1)", "startDate": "2025-05-01T00:00:00Z", "endDate": "2025-05-31T00:00:00Z", "submitByDate": "2025-05-29T00:00:00Z", "checkDate": "2025-05-31T00:00:00Z", "checkCount": 0, "id": "287f5625-16d5-474d-9cc3-d6cc6cccb4aa", "companyId": "Y6334376", "type": "payperiod", "_rid": "NmJkAKiCbEce1QQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEce1QQAAAAAAA==/", "_etag": "\"a900b097-0000-0100-0000-6870494c0000\"", "_attachments": "attachments/", "_ts": 1752189260}, {"payPeriodId": "1020051513524617", "intervalCode": "MONTHLY", "status": "INITIAL", "description": "Monthly Payroll (1)", "startDate": "2025-06-01T00:00:00Z", "endDate": "2025-06-30T00:00:00Z", "submitByDate": "2025-06-26T00:00:00Z", "checkDate": "2025-06-30T00:00:00Z", "checkCount": 0, "id": "265bc3af-2088-4775-a57e-ede0176f0355", "companyId": "Y6334376", "type": "payperiod", "_rid": "NmJkAKiCbEcf1QQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcf1QQAAAAAAA==/", "_etag": "\"a900b197-0000-0100-0000-6870494c0000\"", "_attachments": "attachments/", "_ts": 1752189260}, {"payPeriodId": "1020051750680265", "intervalCode": "MONTHLY", "status": "INITIAL", "description": "Monthly Payroll (1)", "startDate": "2025-07-01T00:00:00Z", "endDate": "2025-07-31T00:00:00Z", "submitByDate": "2025-07-29T00:00:00Z", "checkDate": "2025-07-31T00:00:00Z", "checkCount": 0, "id": "4ae73d4e-8b31-4e20-9bdf-ce89a5179aaf", "companyId": "Y6334376", "type": "payperiod", "_rid": "NmJkAKiCbEcg1QQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcg1QQAAAAAAA==/", "_etag": "\"a900b297-0000-0100-0000-6870494c0000\"", "_attachments": "attachments/", "_ts": 1752189260}, {"payPeriodId": "1020052001765159", "intervalCode": "MONTHLY", "status": "INITIAL", "description": "Monthly Payroll (1)", "startDate": "2025-08-01T00:00:00Z", "endDate": "2025-08-31T00:00:00Z", "submitByDate": "2025-08-28T00:00:00Z", "checkDate": "2025-08-31T00:00:00Z", "checkCount": 0, "id": "d3ded46f-01ae-449c-b929-7127ef52ca15", "companyId": "Y6334376", "type": "payperiod", "_rid": "NmJkAKiCbEch1QQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEch1QQAAAAAAA==/", "_etag": "\"a900b497-0000-0100-0000-6870494c0000\"", "_attachments": "attachments/", "_ts": 1752189260}, {"payPeriodId": "1020052298037277", "intervalCode": "MONTHLY", "status": "INITIAL", "description": "Monthly Payroll (1)", "startDate": "2025-09-01T00:00:00Z", "endDate": "2025-09-30T00:00:00Z", "submitByDate": "2025-09-26T00:00:00Z", "checkDate": "2025-09-30T00:00:00Z", "checkCount": 0, "id": "b69ec9fd-c57f-4b62-9341-cf326b8aab3a", "companyId": "Y6334376", "type": "payperiod", "_rid": "NmJkAKiCbEci1QQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEci1QQAAAAAAA==/", "_etag": "\"a900b797-0000-0100-0000-6870494c0000\"", "_attachments": "attachments/", "_ts": 1752189260}, {"payPeriodId": "1020052539531109", "intervalCode": "MONTHLY", "status": "INITIAL", "description": "Monthly Payroll (1)", "startDate": "2025-10-01T00:00:00Z", "endDate": "2025-10-31T00:00:00Z", "submitByDate": "2025-10-29T00:00:00Z", "checkDate": "2025-10-31T00:00:00Z", "checkCount": 0, "id": "566bdf98-7006-4d02-910c-c99b1d6d1f0b", "companyId": "Y6334376", "type": "payperiod", "_rid": "NmJkAKiCbEcj1QQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcj1QQAAAAAAA==/", "_etag": "\"a900b897-0000-0100-0000-6870494c0000\"", "_attachments": "attachments/", "_ts": 1752189260}, {"payPeriodId": "1020050187258553", "intervalCode": "MONTHLY", "status": "COMPLETED", "description": "Monthly Payroll (1)", "startDate": "2025-01-01T00:00:00Z", "endDate": "2025-01-31T00:00:00Z", "submitByDate": "2025-01-29T00:00:00Z", "checkDate": "2025-01-31T00:00:00Z", "checkCount": 2, "id": "791ad2e6-dcbf-4612-9cb2-11ec94490103", "companyId": "Y6334376", "type": "payperiod", "_rid": "NmJkAKiCbEco1QQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEco1QQAAAAAAA==/", "_etag": "\"a900c397-0000-0100-0000-6870494c0000\"", "_attachments": "attachments/", "_ts": 1752189260}, {"payPeriodId": "1020050443567147", "intervalCode": "MONTHLY", "status": "COMPLETED", "description": "Monthly Payroll (1)", "startDate": "2025-02-01T00:00:00Z", "endDate": "2025-02-28T00:00:00Z", "submitByDate": "2025-02-26T00:00:00Z", "checkDate": "2025-02-28T00:00:00Z", "checkCount": 2, "id": "fce3bb4f-b8be-4d79-a231-3a27f5c047ee", "companyId": "Y6334376", "type": "payperiod", "_rid": "NmJkAKiCbEcp1QQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcp1QQAAAAAAA==/", "_etag": "\"a900c497-0000-0100-0000-6870494c0000\"", "_attachments": "attachments/", "_ts": 1752189260}, {"payPeriodId": "1020050743524696", "intervalCode": "MONTHLY", "status": "COMPLETED", "description": "Monthly Payroll (1)", "startDate": "2025-03-01T00:00:00Z", "endDate": "2025-03-31T00:00:00Z", "submitByDate": "2025-03-27T00:00:00Z", "checkDate": "2025-03-31T00:00:00Z", "checkCount": 2, "id": "84ba417f-7306-4e47-848c-94cc7fa97ab4", "companyId": "Y6334376", "type": "payperiod", "_rid": "NmJkAKiCbEcq1QQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcq1QQAAAAAAA==/", "_etag": "\"a900ca97-0000-0100-0000-6870494c0000\"", "_attachments": "attachments/", "_ts": 1752189260}, {"payPeriodId": "1020051000203620", "intervalCode": "MONTHLY", "status": "COMPLETED", "description": "Monthly Payroll (1)", "startDate": "2025-04-01T00:00:00Z", "endDate": "2025-04-30T00:00:00Z", "submitByDate": "2025-04-28T00:00:00Z", "checkDate": "2025-04-30T00:00:00Z", "checkCount": 2, "id": "22f61243-87b3-4142-a768-75452f4fed61", "companyId": "Y6334376", "type": "payperiod", "_rid": "NmJkAKiCbEcr1QQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcr1QQAAAAAAA==/", "_etag": "\"a900cf97-0000-0100-0000-6870494d0000\"", "_attachments": "attachments/", "_ts": 1752189261}, {"payPeriodId": "1020051232390789", "intervalCode": "MONTHLY", "status": "COMPLETED", "description": "Monthly Payroll (1)", "startDate": "2025-05-01T00:00:00Z", "endDate": "2025-05-31T00:00:00Z", "submitByDate": "2025-05-29T00:00:00Z", "checkDate": "2025-05-31T00:00:00Z", "checkCount": 2, "id": "5acc7a4d-dd71-4d1a-8977-2e4d81f2cddd", "companyId": "Y6334376", "type": "payperiod", "_rid": "NmJkAKiCbEcs1QQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcs1QQAAAAAAA==/", "_etag": "\"a900d097-0000-0100-0000-6870494d0000\"", "_attachments": "attachments/", "_ts": 1752189261}, {"payPeriodId": "1020051513524617", "intervalCode": "MONTHLY", "status": "COMPLETED", "description": "Monthly Payroll (1)", "startDate": "2025-06-01T00:00:00Z", "endDate": "2025-06-30T00:00:00Z", "submitByDate": "2025-06-26T00:00:00Z", "checkDate": "2025-06-30T00:00:00Z", "checkCount": 2, "id": "f52c6875-cefd-4a9e-880d-1bfc59039c72", "companyId": "Y6334376", "type": "payperiod", "_rid": "NmJkAKiCbEct1QQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEct1QQAAAAAAA==/", "_etag": "\"a900d297-0000-0100-0000-6870494d0000\"", "_attachments": "attachments/", "_ts": 1752189261}, {"payPeriodId": "1020051750680265", "intervalCode": "MONTHLY", "status": "INITIAL", "description": "Monthly Payroll (1)", "startDate": "2025-07-01T00:00:00Z", "endDate": "2025-07-31T00:00:00Z", "submitByDate": "2025-07-29T00:00:00Z", "checkDate": "2025-07-31T00:00:00Z", "checkCount": 0, "id": "f8380920-a922-468a-bf11-50f5204bc7f3", "companyId": "Y6334376", "type": "payperiod", "_rid": "NmJkAKiCbEcu1QQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcu1QQAAAAAAA==/", "_etag": "\"a900d497-0000-0100-0000-6870494d0000\"", "_attachments": "attachments/", "_ts": 1752189261}, {"payPeriodId": "1020052001765159", "intervalCode": "MONTHLY", "status": "INITIAL", "description": "Monthly Payroll (1)", "startDate": "2025-08-01T00:00:00Z", "endDate": "2025-08-31T00:00:00Z", "submitByDate": "2025-08-28T00:00:00Z", "checkDate": "2025-08-31T00:00:00Z", "checkCount": 0, "id": "342a3da6-f84a-4e8b-abdb-2bcd2cb20c2a", "companyId": "Y6334376", "type": "payperiod", "_rid": "NmJkAKiCbEcv1QQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcv1QQAAAAAAA==/", "_etag": "\"a900d697-0000-0100-0000-6870494d0000\"", "_attachments": "attachments/", "_ts": 1752189261}, {"payPeriodId": "1020052298037277", "intervalCode": "MONTHLY", "status": "INITIAL", "description": "Monthly Payroll (1)", "startDate": "2025-09-01T00:00:00Z", "endDate": "2025-09-30T00:00:00Z", "submitByDate": "2025-09-26T00:00:00Z", "checkDate": "2025-09-30T00:00:00Z", "checkCount": 0, "id": "38c86c28-6604-4268-8520-6a689e2add59", "companyId": "Y6334376", "type": "payperiod", "_rid": "NmJkAKiCbEcw1QQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcw1QQAAAAAAA==/", "_etag": "\"a900d897-0000-0100-0000-6870494d0000\"", "_attachments": "attachments/", "_ts": 1752189261}, {"payPeriodId": "1020052539531109", "intervalCode": "MONTHLY", "status": "INITIAL", "description": "Monthly Payroll (1)", "startDate": "2025-10-01T00:00:00Z", "endDate": "2025-10-31T00:00:00Z", "submitByDate": "2025-10-29T00:00:00Z", "checkDate": "2025-10-31T00:00:00Z", "checkCount": 0, "id": "5d8a0b27-d35d-4369-a4fb-8e876081004e", "companyId": "Y6334376", "type": "payperiod", "_rid": "NmJkAKiCbEcx1QQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcx1QQAAAAAAA==/", "_etag": "\"a900da97-0000-0100-0000-6870494d0000\"", "_attachments": "attachments/", "_ts": 1752189261}], "links": [{"rel": "self", "href": "https://ca-payroll-ai-mock-sb-001-secure.nicebeach-8a7a52ab.eastus.azurecontainerapps.io/ca/companies/Y6334376/payperiods"}]}, "status_code": 200}