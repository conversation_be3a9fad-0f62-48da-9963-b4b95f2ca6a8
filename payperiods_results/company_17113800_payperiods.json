{"success": true, "company_id": "17113800", "data": {"metadata": {"contentItemCount": 20}, "content": [{"payPeriodId": "1080039085445098", "intervalCode": "MONTHLY", "status": "COMPLETED", "description": "Monthly Payroll (1)", "startDate": "2025-01-01T00:00:00Z", "endDate": "2025-01-31T00:00:00Z", "submitByDate": "2025-01-23T00:00:00Z", "checkDate": "2025-01-27T00:00:00Z", "checkCount": 1, "id": "3503745e-c150-4066-a0ce-a96e58ffd2ee", "companyId": "17113800", "type": "payperiod", "_rid": "NmJkAKiCbEfinAQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfinAQAAAAAAA==/", "_etag": "\"a900ad18-0000-0100-0000-687045040000\"", "_attachments": "attachments/", "_ts": 1752188164}, {"payPeriodId": "1080039281051460", "intervalCode": "MONTHLY", "status": "COMPLETED", "description": "Monthly Payroll (1)", "startDate": "2025-02-01T00:00:00Z", "endDate": "2025-02-28T00:00:00Z", "submitByDate": "2025-02-25T00:00:00Z", "checkDate": "2025-02-27T00:00:00Z", "checkCount": 1, "id": "da21060a-13d6-4466-9c2a-728fce76ec9e", "companyId": "17113800", "type": "payperiod", "_rid": "NmJkAKiCbEfjnAQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfjnAQAAAAAAA==/", "_etag": "\"a900af18-0000-0100-0000-687045040000\"", "_attachments": "attachments/", "_ts": 1752188164}, {"payPeriodId": "1080039510250020", "intervalCode": "MONTHLY", "status": "COMPLETED", "description": "Monthly Payroll (1)", "startDate": "2025-03-01T00:00:00Z", "endDate": "2025-03-31T00:00:00Z", "submitByDate": "2025-03-25T00:00:00Z", "checkDate": "2025-03-27T00:00:00Z", "checkCount": 1, "id": "35359a0a-4ce0-4beb-9186-51ec55cb628f", "companyId": "17113800", "type": "payperiod", "_rid": "NmJkAKiCbEfknAQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfknAQAAAAAAA==/", "_etag": "\"a900b118-0000-0100-0000-687045040000\"", "_attachments": "attachments/", "_ts": 1752188164}, {"payPeriodId": "1080039711652158", "intervalCode": "MONTHLY", "status": "INITIAL", "description": "Monthly Payroll (1)", "startDate": "2025-04-01T00:00:00Z", "endDate": "2025-04-30T00:00:00Z", "submitByDate": "2025-04-23T00:00:00Z", "checkDate": "2025-04-25T00:00:00Z", "checkCount": 0, "id": "90565ab2-350b-4c01-ac1a-558b51301efd", "companyId": "17113800", "type": "payperiod", "_rid": "NmJkAKiCbEflnAQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEflnAQAAAAAAA==/", "_etag": "\"a900b218-0000-0100-0000-687045040000\"", "_attachments": "attachments/", "_ts": 1752188164}, {"payPeriodId": "1080039899307314", "intervalCode": "MONTHLY", "status": "INITIAL", "description": "Monthly Payroll (1)", "startDate": "2025-05-01T00:00:00Z", "endDate": "2025-05-31T00:00:00Z", "submitByDate": "2025-05-22T00:00:00Z", "checkDate": "2025-05-27T00:00:00Z", "checkCount": 0, "id": "3635c938-5d6b-4a87-b577-f512cc38072b", "companyId": "17113800", "type": "payperiod", "_rid": "NmJkAKiCbEfmnAQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfmnAQAAAAAAA==/", "_etag": "\"a900b418-0000-0100-0000-687045050000\"", "_attachments": "attachments/", "_ts": 1752188165}, {"payPeriodId": "1080040085669877", "intervalCode": "MONTHLY", "status": "INITIAL", "description": "Monthly Payroll (1)", "startDate": "2025-06-01T00:00:00Z", "endDate": "2025-06-30T00:00:00Z", "submitByDate": "2025-06-25T00:00:00Z", "checkDate": "2025-06-27T00:00:00Z", "checkCount": 0, "id": "0eb546b9-50b1-4595-a737-d00f3b03cbd5", "companyId": "17113800", "type": "payperiod", "_rid": "NmJkAKiCbEfnnAQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfnnAQAAAAAAA==/", "_etag": "\"a900b518-0000-0100-0000-687045050000\"", "_attachments": "attachments/", "_ts": 1752188165}, {"payPeriodId": "1080040325272425", "intervalCode": "MONTHLY", "status": "INITIAL", "description": "Monthly Payroll (1)", "startDate": "2025-07-01T00:00:00Z", "endDate": "2025-07-31T00:00:00Z", "submitByDate": "2025-07-23T00:00:00Z", "checkDate": "2025-07-25T00:00:00Z", "checkCount": 0, "id": "bc83324d-4156-4435-99c4-3e7d48538268", "companyId": "17113800", "type": "payperiod", "_rid": "NmJkAKiCbEfonAQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfonAQAAAAAAA==/", "_etag": "\"a900b718-0000-0100-0000-687045050000\"", "_attachments": "attachments/", "_ts": 1752188165}, {"payPeriodId": "1080040539229458", "intervalCode": "MONTHLY", "status": "INITIAL", "description": "Monthly Payroll (1)", "startDate": "2025-08-01T00:00:00Z", "endDate": "2025-08-31T00:00:00Z", "submitByDate": "2025-08-25T00:00:00Z", "checkDate": "2025-08-27T00:00:00Z", "checkCount": 0, "id": "519defc5-c83a-4589-882e-c4a301ecdbfc", "companyId": "17113800", "type": "payperiod", "_rid": "NmJkAKiCbEfpnAQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfpnAQAAAAAAA==/", "_etag": "\"a900b918-0000-0100-0000-687045050000\"", "_attachments": "attachments/", "_ts": 1752188165}, {"payPeriodId": "1080040773345754", "intervalCode": "MONTHLY", "status": "INITIAL", "description": "Monthly Payroll (1)", "startDate": "2025-09-01T00:00:00Z", "endDate": "2025-09-30T00:00:00Z", "submitByDate": "2025-09-24T00:00:00Z", "checkDate": "2025-09-26T00:00:00Z", "checkCount": 0, "id": "d1c7cb37-3548-47bc-8b54-d9f6873a8219", "companyId": "17113800", "type": "payperiod", "_rid": "NmJkAKiCbEfqnAQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfqnAQAAAAAAA==/", "_etag": "\"a900bb18-0000-0100-0000-687045050000\"", "_attachments": "attachments/", "_ts": 1752188165}, {"payPeriodId": "1080040962092962", "intervalCode": "MONTHLY", "status": "INITIAL", "description": "Monthly Payroll (1)", "startDate": "2025-10-01T00:00:00Z", "endDate": "2025-10-31T00:00:00Z", "submitByDate": "2025-10-23T00:00:00Z", "checkDate": "2025-10-27T00:00:00Z", "checkCount": 0, "id": "4cd2aa3c-2395-48d6-9e9c-945e19173cbf", "companyId": "17113800", "type": "payperiod", "_rid": "NmJkAKiCbEfrnAQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfrnAQAAAAAAA==/", "_etag": "\"a900bc18-0000-0100-0000-687045050000\"", "_attachments": "attachments/", "_ts": 1752188165}, {"payPeriodId": "1080039085445098", "intervalCode": "MONTHLY", "status": "COMPLETED", "description": "Monthly Payroll (1)", "startDate": "2025-01-01T00:00:00Z", "endDate": "2025-01-31T00:00:00Z", "submitByDate": "2025-01-23T00:00:00Z", "checkDate": "2025-01-27T00:00:00Z", "checkCount": 1, "id": "68d435a5-7297-4c0d-8b03-aec12d1030af", "companyId": "17113800", "type": "payperiod", "_rid": "NmJkAKiCbEfunAQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfunAQAAAAAAA==/", "_etag": "\"a900c218-0000-0100-0000-687045050000\"", "_attachments": "attachments/", "_ts": 1752188165}, {"payPeriodId": "1080039281051460", "intervalCode": "MONTHLY", "status": "COMPLETED", "description": "Monthly Payroll (1)", "startDate": "2025-02-01T00:00:00Z", "endDate": "2025-02-28T00:00:00Z", "submitByDate": "2025-02-25T00:00:00Z", "checkDate": "2025-02-27T00:00:00Z", "checkCount": 1, "id": "bb5495b4-7d06-401c-b85c-52b44c70160c", "companyId": "17113800", "type": "payperiod", "_rid": "NmJkAKiCbEfvnAQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfvnAQAAAAAAA==/", "_etag": "\"a900c318-0000-0100-0000-687045050000\"", "_attachments": "attachments/", "_ts": 1752188165}, {"payPeriodId": "1080039510250020", "intervalCode": "MONTHLY", "status": "COMPLETED", "description": "Monthly Payroll (1)", "startDate": "2025-03-01T00:00:00Z", "endDate": "2025-03-31T00:00:00Z", "submitByDate": "2025-03-25T00:00:00Z", "checkDate": "2025-03-27T00:00:00Z", "checkCount": 1, "id": "c0a7e8ba-6c94-44eb-8b15-8762c45c6c5a", "companyId": "17113800", "type": "payperiod", "_rid": "NmJkAKiCbEfwnAQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfwnAQAAAAAAA==/", "_etag": "\"a900c418-0000-0100-0000-687045050000\"", "_attachments": "attachments/", "_ts": 1752188165}, {"payPeriodId": "1080039711652158", "intervalCode": "MONTHLY", "status": "COMPLETED", "description": "Monthly Payroll (1)", "startDate": "2025-04-01T00:00:00Z", "endDate": "2025-04-30T00:00:00Z", "submitByDate": "2025-04-23T00:00:00Z", "checkDate": "2025-04-25T00:00:00Z", "checkCount": 1, "id": "ecf02373-42b5-4508-b555-df2a43846944", "companyId": "17113800", "type": "payperiod", "_rid": "NmJkAKiCbEfxnAQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfxnAQAAAAAAA==/", "_etag": "\"a900c518-0000-0100-0000-687045050000\"", "_attachments": "attachments/", "_ts": 1752188165}, {"payPeriodId": "1080039899307314", "intervalCode": "MONTHLY", "status": "COMPLETED", "description": "Monthly Payroll (1)", "startDate": "2025-05-01T00:00:00Z", "endDate": "2025-05-31T00:00:00Z", "submitByDate": "2025-05-22T00:00:00Z", "checkDate": "2025-05-27T00:00:00Z", "checkCount": 1, "id": "5febc0ca-ab14-480b-b550-a9a04f9ea370", "companyId": "17113800", "type": "payperiod", "_rid": "NmJkAKiCbEfynAQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfynAQAAAAAAA==/", "_etag": "\"a900c718-0000-0100-0000-687045050000\"", "_attachments": "attachments/", "_ts": 1752188165}, {"payPeriodId": "1080040085669877", "intervalCode": "MONTHLY", "status": "COMPLETED", "description": "Monthly Payroll (1)", "startDate": "2025-06-01T00:00:00Z", "endDate": "2025-06-30T00:00:00Z", "submitByDate": "2025-06-25T00:00:00Z", "checkDate": "2025-06-27T00:00:00Z", "checkCount": 1, "id": "50c83fc4-a895-4bf9-a73c-63b9efaec310", "companyId": "17113800", "type": "payperiod", "_rid": "NmJkAKiCbEfznAQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfznAQAAAAAAA==/", "_etag": "\"a900c918-0000-0100-0000-687045060000\"", "_attachments": "attachments/", "_ts": 1752188166}, {"payPeriodId": "1080040325272425", "intervalCode": "MONTHLY", "status": "INITIAL", "description": "Monthly Payroll (1)", "startDate": "2025-07-01T00:00:00Z", "endDate": "2025-07-31T00:00:00Z", "submitByDate": "2025-07-23T00:00:00Z", "checkDate": "2025-07-25T00:00:00Z", "checkCount": 0, "id": "9ce80d6f-9f0a-448c-adbd-3de2213aab11", "companyId": "17113800", "type": "payperiod", "_rid": "NmJkAKiCbEf0nAQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf0nAQAAAAAAA==/", "_etag": "\"a900ca18-0000-0100-0000-687045060000\"", "_attachments": "attachments/", "_ts": 1752188166}, {"payPeriodId": "1080040539229458", "intervalCode": "MONTHLY", "status": "INITIAL", "description": "Monthly Payroll (1)", "startDate": "2025-08-01T00:00:00Z", "endDate": "2025-08-31T00:00:00Z", "submitByDate": "2025-08-25T00:00:00Z", "checkDate": "2025-08-27T00:00:00Z", "checkCount": 0, "id": "45a8c72a-f580-4911-903e-e2d25f11b7e4", "companyId": "17113800", "type": "payperiod", "_rid": "NmJkAKiCbEf1nAQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf1nAQAAAAAAA==/", "_etag": "\"a900cb18-0000-0100-0000-687045060000\"", "_attachments": "attachments/", "_ts": 1752188166}, {"payPeriodId": "1080040773345754", "intervalCode": "MONTHLY", "status": "INITIAL", "description": "Monthly Payroll (1)", "startDate": "2025-09-01T00:00:00Z", "endDate": "2025-09-30T00:00:00Z", "submitByDate": "2025-09-24T00:00:00Z", "checkDate": "2025-09-26T00:00:00Z", "checkCount": 0, "id": "609149ab-e9d2-4f6e-aaed-b9b457b0f009", "companyId": "17113800", "type": "payperiod", "_rid": "NmJkAKiCbEf2nAQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf2nAQAAAAAAA==/", "_etag": "\"a900cd18-0000-0100-0000-687045060000\"", "_attachments": "attachments/", "_ts": 1752188166}, {"payPeriodId": "1080040962092962", "intervalCode": "MONTHLY", "status": "INITIAL", "description": "Monthly Payroll (1)", "startDate": "2025-10-01T00:00:00Z", "endDate": "2025-10-31T00:00:00Z", "submitByDate": "2025-10-23T00:00:00Z", "checkDate": "2025-10-27T00:00:00Z", "checkCount": 0, "id": "b4240ace-ad23-4fa0-9ce0-a4608960a80b", "companyId": "17113800", "type": "payperiod", "_rid": "NmJkAKiCbEf3nAQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf3nAQAAAAAAA==/", "_etag": "\"a900d118-0000-0100-0000-687045060000\"", "_attachments": "attachments/", "_ts": 1752188166}], "links": [{"rel": "self", "href": "https://ca-payroll-ai-mock-sb-001-secure.nicebeach-8a7a52ab.eastus.azurecontainerapps.io/ca/companies/17113800/payperiods"}]}, "status_code": 200}