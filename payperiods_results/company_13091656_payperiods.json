{"success": true, "company_id": "13091656", "data": {"metadata": {"contentItemCount": 42}, "content": [{"payPeriodId": "1040046203584130", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2024-12-22T00:00:00Z", "endDate": "2025-01-04T00:00:00Z", "submitByDate": "2025-01-08T00:00:00Z", "checkDate": "2025-01-10T00:00:00Z", "checkCount": 5, "id": "f427e67f-def4-474b-8646-f85bce25f0f9", "companyId": "13091656", "type": "payperiod", "_rid": "NmJkAKiCbEdlzgIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdlzgIAAAAAAA==/", "_etag": "\"a400f20a-0000-0100-0000-68701e590000\"", "_attachments": "attachments/", "_ts": 1752178265}, {"payPeriodId": "1040046284534105", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-01-05T00:00:00Z", "endDate": "2025-01-18T00:00:00Z", "submitByDate": "2025-01-22T00:00:00Z", "checkDate": "2025-01-24T00:00:00Z", "checkCount": 4, "id": "4d30fd20-1ee9-4466-80a4-7fb95781927f", "companyId": "13091656", "type": "payperiod", "_rid": "NmJkAKiCbEdmzgIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdmzgIAAAAAAA==/", "_etag": "\"a400fb0a-0000-0100-0000-68701e590000\"", "_attachments": "attachments/", "_ts": 1752178265}, {"payPeriodId": "1040046368732849", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-01-19T00:00:00Z", "endDate": "2025-02-01T00:00:00Z", "submitByDate": "2025-02-05T00:00:00Z", "checkDate": "2025-02-07T00:00:00Z", "checkCount": 5, "id": "8d79c4b8-e96e-4ee8-b38c-d0235efc225f", "companyId": "13091656", "type": "payperiod", "_rid": "NmJkAKiCbEdnzgIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdnzgIAAAAAAA==/", "_etag": "\"a400fe0a-0000-0100-0000-68701e590000\"", "_attachments": "attachments/", "_ts": 1752178265}, {"payPeriodId": "1040046449346436", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-02-02T00:00:00Z", "endDate": "2025-02-15T00:00:00Z", "submitByDate": "2025-02-19T00:00:00Z", "checkDate": "2025-02-21T00:00:00Z", "checkCount": 4, "id": "ab353e2e-131e-487a-b1a0-44f01001adcc", "companyId": "13091656", "type": "payperiod", "_rid": "NmJkAKiCbEdozgIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdozgIAAAAAAA==/", "_etag": "\"a400030b-0000-0100-0000-68701e590000\"", "_attachments": "attachments/", "_ts": 1752178265}, {"payPeriodId": "1040046527592118", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-02-16T00:00:00Z", "endDate": "2025-03-01T00:00:00Z", "submitByDate": "2025-03-05T00:00:00Z", "checkDate": "2025-03-07T00:00:00Z", "checkCount": 6, "id": "a472872c-27fa-4726-aaa3-ca47b575cc49", "companyId": "13091656", "type": "payperiod", "_rid": "NmJkAKiCbEdpzgIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdpzgIAAAAAAA==/", "_etag": "\"a400050b-0000-0100-0000-68701e590000\"", "_attachments": "attachments/", "_ts": 1752178265}, {"payPeriodId": "1040046609505350", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-03-02T00:00:00Z", "endDate": "2025-03-15T00:00:00Z", "submitByDate": "2025-03-19T00:00:00Z", "checkDate": "2025-03-21T00:00:00Z", "checkCount": 5, "id": "a8ea6e6c-6f07-4b5a-9312-5d40f70d68f7", "companyId": "13091656", "type": "payperiod", "_rid": "NmJkAKiCbEdqzgIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdqzgIAAAAAAA==/", "_etag": "\"a400070b-0000-0100-0000-68701e590000\"", "_attachments": "attachments/", "_ts": 1752178265}, {"payPeriodId": "1040046698108721", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-03-16T00:00:00Z", "endDate": "2025-03-29T00:00:00Z", "submitByDate": "2025-04-02T00:00:00Z", "checkDate": "2025-04-04T00:00:00Z", "checkCount": 0, "id": "5efdf10d-8937-4d1d-aa24-a4648b11559a", "companyId": "13091656", "type": "payperiod", "_rid": "NmJkAKiCbEdrzgIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdrzgIAAAAAAA==/", "_etag": "\"a4000a0b-0000-0100-0000-68701e590000\"", "_attachments": "attachments/", "_ts": 1752178265}, {"payPeriodId": "1040046780352536", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-03-30T00:00:00Z", "endDate": "2025-04-12T00:00:00Z", "submitByDate": "2025-04-16T00:00:00Z", "checkDate": "2025-04-18T00:00:00Z", "checkCount": 0, "id": "f3503e97-d58b-40a5-9d3f-476c71d2699e", "companyId": "13091656", "type": "payperiod", "_rid": "NmJkAKiCbEdszgIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdszgIAAAAAAA==/", "_etag": "\"a4000c0b-0000-0100-0000-68701e590000\"", "_attachments": "attachments/", "_ts": 1752178265}, {"payPeriodId": "1040046857640401", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-04-13T00:00:00Z", "endDate": "2025-04-26T00:00:00Z", "submitByDate": "2025-04-30T00:00:00Z", "checkDate": "2025-05-02T00:00:00Z", "checkCount": 0, "id": "ecc380b6-6a54-4972-a723-5caa15ae0e5b", "companyId": "13091656", "type": "payperiod", "_rid": "NmJkAKiCbEdtzgIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdtzgIAAAAAAA==/", "_etag": "\"a4000d0b-0000-0100-0000-68701e590000\"", "_attachments": "attachments/", "_ts": 1752178265}, {"payPeriodId": "1040046932221898", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-04-27T00:00:00Z", "endDate": "2025-05-10T00:00:00Z", "submitByDate": "2025-05-14T00:00:00Z", "checkDate": "2025-05-16T00:00:00Z", "checkCount": 0, "id": "ae268d92-de6e-4363-9650-d41a43386c41", "companyId": "13091656", "type": "payperiod", "_rid": "NmJkAKiCbEduzgIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEduzgIAAAAAAA==/", "_etag": "\"a4000f0b-0000-0100-0000-68701e5a0000\"", "_attachments": "attachments/", "_ts": 1752178266}, {"payPeriodId": "1040047006163359", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-05-11T00:00:00Z", "endDate": "2025-05-24T00:00:00Z", "submitByDate": "2025-05-28T00:00:00Z", "checkDate": "2025-05-30T00:00:00Z", "checkCount": 0, "id": "1bcee1ae-ab71-47c8-a452-2b7692ee8e3b", "companyId": "13091656", "type": "payperiod", "_rid": "NmJkAKiCbEdvzgIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdvzgIAAAAAAA==/", "_etag": "\"a400110b-0000-0100-0000-68701e5a0000\"", "_attachments": "attachments/", "_ts": 1752178266}, {"payPeriodId": "1040047083707127", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-05-25T00:00:00Z", "endDate": "2025-06-07T00:00:00Z", "submitByDate": "2025-06-11T00:00:00Z", "checkDate": "2025-06-13T00:00:00Z", "checkCount": 0, "id": "cc414231-4d4c-4eee-9603-355955b563c4", "companyId": "13091656", "type": "payperiod", "_rid": "NmJkAKiCbEdwzgIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdwzgIAAAAAAA==/", "_etag": "\"a400150b-0000-0100-0000-68701e5a0000\"", "_attachments": "attachments/", "_ts": 1752178266}, {"payPeriodId": "1040047157914919", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-06-08T00:00:00Z", "endDate": "2025-06-21T00:00:00Z", "submitByDate": "2025-06-25T00:00:00Z", "checkDate": "2025-06-27T00:00:00Z", "checkCount": 0, "id": "757bfb2c-c51d-4077-9a80-8d405a60a764", "companyId": "13091656", "type": "payperiod", "_rid": "NmJkAKiCbEdxzgIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdxzgIAAAAAAA==/", "_etag": "\"a400190b-0000-0100-0000-68701e5a0000\"", "_attachments": "attachments/", "_ts": 1752178266}, {"payPeriodId": "1040047234834592", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-06-22T00:00:00Z", "endDate": "2025-07-05T00:00:00Z", "submitByDate": "2025-07-09T00:00:00Z", "checkDate": "2025-07-11T00:00:00Z", "checkCount": 0, "id": "42362b2c-8ece-4569-9658-eb78c867ae6a", "companyId": "13091656", "type": "payperiod", "_rid": "NmJkAKiCbEdyzgIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdyzgIAAAAAAA==/", "_etag": "\"a4001b0b-0000-0100-0000-68701e5a0000\"", "_attachments": "attachments/", "_ts": 1752178266}, {"payPeriodId": "1040047320930583", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-07-06T00:00:00Z", "endDate": "2025-07-19T00:00:00Z", "submitByDate": "2025-07-23T00:00:00Z", "checkDate": "2025-07-25T00:00:00Z", "checkCount": 0, "id": "c3a80251-a1cc-499c-a606-06dc7f607534", "companyId": "13091656", "type": "payperiod", "_rid": "NmJkAKiCbEdzzgIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdzzgIAAAAAAA==/", "_etag": "\"a4001c0b-0000-0100-0000-68701e5a0000\"", "_attachments": "attachments/", "_ts": 1752178266}, {"payPeriodId": "1040047400396539", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-07-20T00:00:00Z", "endDate": "2025-08-02T00:00:00Z", "submitByDate": "2025-08-06T00:00:00Z", "checkDate": "2025-08-08T00:00:00Z", "checkCount": 0, "id": "6cfebcaa-8698-4645-8cb6-e656dbd86de9", "companyId": "13091656", "type": "payperiod", "_rid": "NmJkAKiCbEd0zgIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEd0zgIAAAAAAA==/", "_etag": "\"a400200b-0000-0100-0000-68701e5a0000\"", "_attachments": "attachments/", "_ts": 1752178266}, {"payPeriodId": "1040047481829253", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-08-03T00:00:00Z", "endDate": "2025-08-16T00:00:00Z", "submitByDate": "2025-08-20T00:00:00Z", "checkDate": "2025-08-22T00:00:00Z", "checkCount": 0, "id": "c472db12-e43d-44ae-8791-c79853cd89ae", "companyId": "13091656", "type": "payperiod", "_rid": "NmJkAKiCbEd1zgIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEd1zgIAAAAAAA==/", "_etag": "\"a400220b-0000-0100-0000-68701e5a0000\"", "_attachments": "attachments/", "_ts": 1752178266}, {"payPeriodId": "1040047558360754", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-08-17T00:00:00Z", "endDate": "2025-08-30T00:00:00Z", "submitByDate": "2025-09-03T00:00:00Z", "checkDate": "2025-09-05T00:00:00Z", "checkCount": 0, "id": "62389dfc-e7ef-43d6-b342-950eed054a42", "companyId": "13091656", "type": "payperiod", "_rid": "NmJkAKiCbEd2zgIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEd2zgIAAAAAAA==/", "_etag": "\"a400250b-0000-0100-0000-68701e5a0000\"", "_attachments": "attachments/", "_ts": 1752178266}, {"payPeriodId": "1040047634742240", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-08-31T00:00:00Z", "endDate": "2025-09-13T00:00:00Z", "submitByDate": "2025-09-17T00:00:00Z", "checkDate": "2025-09-19T00:00:00Z", "checkCount": 0, "id": "8f0e28dc-b67d-4a8c-9556-aa38d1d239e8", "companyId": "13091656", "type": "payperiod", "_rid": "NmJkAKiCbEd3zgIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEd3zgIAAAAAAA==/", "_etag": "\"a400290b-0000-0100-0000-68701e5a0000\"", "_attachments": "attachments/", "_ts": 1752178266}, {"payPeriodId": "1040047710296318", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-09-14T00:00:00Z", "endDate": "2025-09-27T00:00:00Z", "submitByDate": "2025-10-01T00:00:00Z", "checkDate": "2025-10-03T00:00:00Z", "checkCount": 0, "id": "2560b581-eef7-4473-ad17-86a83ba6fec7", "companyId": "13091656", "type": "payperiod", "_rid": "NmJkAKiCbEd4zgIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEd4zgIAAAAAAA==/", "_etag": "\"a4002e0b-0000-0100-0000-68701e5a0000\"", "_attachments": "attachments/", "_ts": 1752178266}, {"payPeriodId": "1040047789991689", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-09-28T00:00:00Z", "endDate": "2025-10-11T00:00:00Z", "submitByDate": "2025-10-15T00:00:00Z", "checkDate": "2025-10-17T00:00:00Z", "checkCount": 0, "id": "c42e698c-e0c7-48f7-952e-1d3ab27e03fc", "companyId": "13091656", "type": "payperiod", "_rid": "NmJkAKiCbEd5zgIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEd5zgIAAAAAAA==/", "_etag": "\"a400330b-0000-0100-0000-68701e5a0000\"", "_attachments": "attachments/", "_ts": 1752178266}, {"payPeriodId": "1040046203584130", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2024-12-22T00:00:00Z", "endDate": "2025-01-04T00:00:00Z", "submitByDate": "2025-01-08T00:00:00Z", "checkDate": "2025-01-10T00:00:00Z", "checkCount": 5, "id": "298093f7-03a7-4b26-8258-d943357fb74e", "companyId": "13091656", "type": "payperiod", "_rid": "NmJkAKiCbEeLzgIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeLzgIAAAAAAA==/", "_etag": "\"a400610b-0000-0100-0000-68701e5c0000\"", "_attachments": "attachments/", "_ts": 1752178268}, {"payPeriodId": "1040046284534105", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-01-05T00:00:00Z", "endDate": "2025-01-18T00:00:00Z", "submitByDate": "2025-01-22T00:00:00Z", "checkDate": "2025-01-24T00:00:00Z", "checkCount": 4, "id": "75a544a4-4bef-4a8c-bf6c-4505983e7ad6", "companyId": "13091656", "type": "payperiod", "_rid": "NmJkAKiCbEeMzgIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeMzgIAAAAAAA==/", "_etag": "\"a400630b-0000-0100-0000-68701e5c0000\"", "_attachments": "attachments/", "_ts": 1752178268}, {"payPeriodId": "1040046368732849", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-01-19T00:00:00Z", "endDate": "2025-02-01T00:00:00Z", "submitByDate": "2025-02-05T00:00:00Z", "checkDate": "2025-02-07T00:00:00Z", "checkCount": 5, "id": "88b8d13e-bf3f-45db-bf4e-2886460d7db1", "companyId": "13091656", "type": "payperiod", "_rid": "NmJkAKiCbEeNzgIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeNzgIAAAAAAA==/", "_etag": "\"a400640b-0000-0100-0000-68701e5c0000\"", "_attachments": "attachments/", "_ts": 1752178268}, {"payPeriodId": "1040046449346436", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-02-02T00:00:00Z", "endDate": "2025-02-15T00:00:00Z", "submitByDate": "2025-02-19T00:00:00Z", "checkDate": "2025-02-21T00:00:00Z", "checkCount": 4, "id": "45f4ff8c-5ef2-4933-8af5-276449b8f2d3", "companyId": "13091656", "type": "payperiod", "_rid": "NmJkAKiCbEeOzgIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeOzgIAAAAAAA==/", "_etag": "\"a400670b-0000-0100-0000-68701e5c0000\"", "_attachments": "attachments/", "_ts": 1752178268}, {"payPeriodId": "1040046527592118", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-02-16T00:00:00Z", "endDate": "2025-03-01T00:00:00Z", "submitByDate": "2025-03-05T00:00:00Z", "checkDate": "2025-03-07T00:00:00Z", "checkCount": 6, "id": "055d205b-32c0-4ade-af71-74207e4c5381", "companyId": "13091656", "type": "payperiod", "_rid": "NmJkAKiCbEePzgIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEePzgIAAAAAAA==/", "_etag": "\"a400690b-0000-0100-0000-68701e5c0000\"", "_attachments": "attachments/", "_ts": 1752178268}, {"payPeriodId": "1040046609505350", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-03-02T00:00:00Z", "endDate": "2025-03-15T00:00:00Z", "submitByDate": "2025-03-19T00:00:00Z", "checkDate": "2025-03-21T00:00:00Z", "checkCount": 5, "id": "f65585a1-687e-4d67-96d1-b62896ae108b", "companyId": "13091656", "type": "payperiod", "_rid": "NmJkAKiCbEeQzgIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeQzgIAAAAAAA==/", "_etag": "\"a4006f0b-0000-0100-0000-68701e5c0000\"", "_attachments": "attachments/", "_ts": 1752178268}, {"payPeriodId": "1040046698108721", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-03-16T00:00:00Z", "endDate": "2025-03-29T00:00:00Z", "submitByDate": "2025-04-02T00:00:00Z", "checkDate": "2025-04-04T00:00:00Z", "checkCount": 5, "id": "f45d93e1-7e77-445d-af8e-039389024f8a", "companyId": "13091656", "type": "payperiod", "_rid": "NmJkAKiCbEeRzgIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeRzgIAAAAAAA==/", "_etag": "\"a400740b-0000-0100-0000-68701e5c0000\"", "_attachments": "attachments/", "_ts": 1752178268}, {"payPeriodId": "1040046780352536", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-03-30T00:00:00Z", "endDate": "2025-04-12T00:00:00Z", "submitByDate": "2025-04-16T00:00:00Z", "checkDate": "2025-04-18T00:00:00Z", "checkCount": 5, "id": "54213872-7414-4caf-8245-c3308bf40493", "companyId": "13091656", "type": "payperiod", "_rid": "NmJkAKiCbEeSzgIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeSzgIAAAAAAA==/", "_etag": "\"a4007b0b-0000-0100-0000-68701e5c0000\"", "_attachments": "attachments/", "_ts": 1752178268}, {"payPeriodId": "1040046857640401", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-04-13T00:00:00Z", "endDate": "2025-04-26T00:00:00Z", "submitByDate": "2025-04-30T00:00:00Z", "checkDate": "2025-05-02T00:00:00Z", "checkCount": 5, "id": "5809bfad-2ff8-45db-90df-ae3eab15e6ea", "companyId": "13091656", "type": "payperiod", "_rid": "NmJkAKiCbEeTzgIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeTzgIAAAAAAA==/", "_etag": "\"a4007e0b-0000-0100-0000-68701e5c0000\"", "_attachments": "attachments/", "_ts": 1752178268}, {"payPeriodId": "1040046932221898", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-04-27T00:00:00Z", "endDate": "2025-05-10T00:00:00Z", "submitByDate": "2025-05-14T00:00:00Z", "checkDate": "2025-05-16T00:00:00Z", "checkCount": 4, "id": "3d55b4f4-7a0b-42ea-9202-438e7b0346ab", "companyId": "13091656", "type": "payperiod", "_rid": "NmJkAKiCbEeUzgIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeUzgIAAAAAAA==/", "_etag": "\"a400820b-0000-0100-0000-68701e5d0000\"", "_attachments": "attachments/", "_ts": 1752178269}, {"payPeriodId": "1040047006163359", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-05-11T00:00:00Z", "endDate": "2025-05-24T00:00:00Z", "submitByDate": "2025-05-28T00:00:00Z", "checkDate": "2025-05-30T00:00:00Z", "checkCount": 5, "id": "a2ce4a25-f400-4bb7-8e40-c8d2efbbeb3f", "companyId": "13091656", "type": "payperiod", "_rid": "NmJkAKiCbEeVzgIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeVzgIAAAAAAA==/", "_etag": "\"a400850b-0000-0100-0000-68701e5d0000\"", "_attachments": "attachments/", "_ts": 1752178269}, {"payPeriodId": "1040047083707127", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-05-25T00:00:00Z", "endDate": "2025-06-07T00:00:00Z", "submitByDate": "2025-06-11T00:00:00Z", "checkDate": "2025-06-13T00:00:00Z", "checkCount": 5, "id": "00970e5b-cc66-44b7-a540-173c2ec72ebd", "companyId": "13091656", "type": "payperiod", "_rid": "NmJkAKiCbEeWzgIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeWzgIAAAAAAA==/", "_etag": "\"a400890b-0000-0100-0000-68701e5d0000\"", "_attachments": "attachments/", "_ts": 1752178269}, {"payPeriodId": "1040047157914919", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-06-08T00:00:00Z", "endDate": "2025-06-21T00:00:00Z", "submitByDate": "2025-06-25T00:00:00Z", "checkDate": "2025-06-27T00:00:00Z", "checkCount": 4, "id": "fc70e079-927e-4a01-b1e1-4f404487b274", "companyId": "13091656", "type": "payperiod", "_rid": "NmJkAKiCbEeXzgIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeXzgIAAAAAAA==/", "_etag": "\"a4008b0b-0000-0100-0000-68701e5d0000\"", "_attachments": "attachments/", "_ts": 1752178269}, {"payPeriodId": "1040047234834592", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-06-22T00:00:00Z", "endDate": "2025-07-05T00:00:00Z", "submitByDate": "2025-07-09T00:00:00Z", "checkDate": "2025-07-11T00:00:00Z", "checkCount": 0, "id": "07494394-a320-4abc-88d2-7981b65c13f5", "companyId": "13091656", "type": "payperiod", "_rid": "NmJkAKiCbEeYzgIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeYzgIAAAAAAA==/", "_etag": "\"a4008e0b-0000-0100-0000-68701e5d0000\"", "_attachments": "attachments/", "_ts": 1752178269}, {"payPeriodId": "1040047320930583", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-07-06T00:00:00Z", "endDate": "2025-07-19T00:00:00Z", "submitByDate": "2025-07-23T00:00:00Z", "checkDate": "2025-07-25T00:00:00Z", "checkCount": 0, "id": "dc227b31-ce5a-46b3-9f2a-7be91d6b3b47", "companyId": "13091656", "type": "payperiod", "_rid": "NmJkAKiCbEeZzgIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeZzgIAAAAAAA==/", "_etag": "\"a400940b-0000-0100-0000-68701e5d0000\"", "_attachments": "attachments/", "_ts": 1752178269}, {"payPeriodId": "1040047400396539", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-07-20T00:00:00Z", "endDate": "2025-08-02T00:00:00Z", "submitByDate": "2025-08-06T00:00:00Z", "checkDate": "2025-08-08T00:00:00Z", "checkCount": 0, "id": "370d4e40-bea9-4752-a174-aba04cfdf93f", "companyId": "13091656", "type": "payperiod", "_rid": "NmJkAKiCbEeazgIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeazgIAAAAAAA==/", "_etag": "\"a400960b-0000-0100-0000-68701e5d0000\"", "_attachments": "attachments/", "_ts": 1752178269}, {"payPeriodId": "1040047481829253", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-08-03T00:00:00Z", "endDate": "2025-08-16T00:00:00Z", "submitByDate": "2025-08-20T00:00:00Z", "checkDate": "2025-08-22T00:00:00Z", "checkCount": 0, "id": "d01be403-18d2-4465-bbfd-ae07488e2362", "companyId": "13091656", "type": "payperiod", "_rid": "NmJkAKiCbEebzgIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEebzgIAAAAAAA==/", "_etag": "\"a4009c0b-0000-0100-0000-68701e5d0000\"", "_attachments": "attachments/", "_ts": 1752178269}, {"payPeriodId": "1040047558360754", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-08-17T00:00:00Z", "endDate": "2025-08-30T00:00:00Z", "submitByDate": "2025-09-03T00:00:00Z", "checkDate": "2025-09-05T00:00:00Z", "checkCount": 0, "id": "d515cff8-a9c8-4d19-bcd2-4eeed091be66", "companyId": "13091656", "type": "payperiod", "_rid": "NmJkAKiCbEeczgIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeczgIAAAAAAA==/", "_etag": "\"a4009f0b-0000-0100-0000-68701e5d0000\"", "_attachments": "attachments/", "_ts": 1752178269}, {"payPeriodId": "1040047634742240", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-08-31T00:00:00Z", "endDate": "2025-09-13T00:00:00Z", "submitByDate": "2025-09-17T00:00:00Z", "checkDate": "2025-09-19T00:00:00Z", "checkCount": 0, "id": "8dd952ea-381d-4f00-88a2-c90bddd8962d", "companyId": "13091656", "type": "payperiod", "_rid": "NmJkAKiCbEedzgIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEedzgIAAAAAAA==/", "_etag": "\"a400a20b-0000-0100-0000-68701e5d0000\"", "_attachments": "attachments/", "_ts": 1752178269}, {"payPeriodId": "1040047710296318", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-09-14T00:00:00Z", "endDate": "2025-09-27T00:00:00Z", "submitByDate": "2025-10-01T00:00:00Z", "checkDate": "2025-10-03T00:00:00Z", "checkCount": 0, "id": "4cec0db2-61eb-448c-9d23-66f428f1803c", "companyId": "13091656", "type": "payperiod", "_rid": "NmJkAKiCbEeezgIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeezgIAAAAAAA==/", "_etag": "\"a400a30b-0000-0100-0000-68701e5d0000\"", "_attachments": "attachments/", "_ts": 1752178269}, {"payPeriodId": "1040047789991689", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-09-28T00:00:00Z", "endDate": "2025-10-11T00:00:00Z", "submitByDate": "2025-10-15T00:00:00Z", "checkDate": "2025-10-17T00:00:00Z", "checkCount": 0, "id": "930bccda-7ad0-473e-a767-3692e55f6178", "companyId": "13091656", "type": "payperiod", "_rid": "NmJkAKiCbEefzgIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEefzgIAAAAAAA==/", "_etag": "\"a400a60b-0000-0100-0000-68701e5d0000\"", "_attachments": "attachments/", "_ts": 1752178269}], "links": [{"rel": "self", "href": "https://ca-payroll-ai-mock-sb-001-secure.nicebeach-8a7a52ab.eastus.azurecontainerapps.io/ca/companies/13091656/payperiods"}]}, "status_code": 200}