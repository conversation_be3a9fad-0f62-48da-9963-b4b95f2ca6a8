{"success": true, "company_id": "19090646", "data": {"metadata": {"contentItemCount": 40}, "content": [{"payPeriodId": "1140035023378205", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "HOURLY Semi-monthly Payroll", "startDate": "2024-12-26T00:00:00Z", "endDate": "2025-01-10T00:00:00Z", "submitByDate": "2025-01-13T00:00:00Z", "checkDate": "2025-01-15T00:00:00Z", "checkCount": 9, "id": "58fd811c-d6ba-4773-a1a6-ba31f631a52f", "companyId": "19090646", "type": "payperiod", "_rid": "NmJkAKiCbEdMrgIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdMrgIAAAAAAA==/", "_etag": "\"a300c1a1-0000-0100-0000-68701bc40000\"", "_attachments": "attachments/", "_ts": 1752177604}, {"payPeriodId": "1140035023378206", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "HOURLY Semi-monthly Payroll", "startDate": "2025-01-11T00:00:00Z", "endDate": "2025-01-25T00:00:00Z", "submitByDate": "2025-01-29T00:00:00Z", "checkDate": "2025-01-31T00:00:00Z", "checkCount": 8, "id": "fe1c8404-278e-4c6e-8454-f8a0e1a990f2", "companyId": "19090646", "type": "payperiod", "_rid": "NmJkAKiCbEdNrgIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdNrgIAAAAAAA==/", "_etag": "\"a300caa1-0000-0100-0000-68701bc40000\"", "_attachments": "attachments/", "_ts": 1752177604}, {"payPeriodId": "1140035159471313", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "HOURLY Semi-monthly Payroll", "startDate": "2025-01-26T00:00:00Z", "endDate": "2025-02-10T00:00:00Z", "submitByDate": "2025-02-12T00:00:00Z", "checkDate": "2025-02-14T00:00:00Z", "checkCount": 8, "id": "b3943950-fc40-4c50-8acc-1a5e735d3536", "companyId": "19090646", "type": "payperiod", "_rid": "NmJkAKiCbEdOrgIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdOrgIAAAAAAA==/", "_etag": "\"a300cea1-0000-0100-0000-68701bc40000\"", "_attachments": "attachments/", "_ts": 1752177604}, {"payPeriodId": "1140035159471314", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "HOURLY Semi-monthly Payroll", "startDate": "2025-02-11T00:00:00Z", "endDate": "2025-02-25T00:00:00Z", "submitByDate": "2025-02-26T00:00:00Z", "checkDate": "2025-02-28T00:00:00Z", "checkCount": 9, "id": "f94b5295-3a18-46b6-93f8-68bc8a71e6d7", "companyId": "19090646", "type": "payperiod", "_rid": "NmJkAKiCbEdPrgIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdPrgIAAAAAAA==/", "_etag": "\"a300d2a1-0000-0100-0000-68701bc40000\"", "_attachments": "attachments/", "_ts": 1752177604}, {"payPeriodId": "1140035281561836", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "HOURLY Semi-monthly Payroll", "startDate": "2025-02-26T00:00:00Z", "endDate": "2025-03-15T00:00:00Z", "submitByDate": "2025-03-12T00:00:00Z", "checkDate": "2025-03-14T00:00:00Z", "checkCount": 7, "id": "d83e635f-e173-46d6-8aef-eeb2c9cccef9", "companyId": "19090646", "type": "payperiod", "_rid": "NmJkAKiCbEdQrgIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdQrgIAAAAAAA==/", "_etag": "\"a300d6a1-0000-0100-0000-68701bc40000\"", "_attachments": "attachments/", "_ts": 1752177604}, {"payPeriodId": "1140035281561837", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "HOURLY Semi-monthly Payroll", "startDate": "2025-03-11T00:00:00Z", "endDate": "2025-03-25T00:00:00Z", "submitByDate": "2025-03-27T00:00:00Z", "checkDate": "2025-03-31T00:00:00Z", "checkCount": 9, "id": "8e250c7c-eba5-4d34-be0a-b2a7177c682a", "companyId": "19090646", "type": "payperiod", "_rid": "NmJkAKiCbEdRrgIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdRrgIAAAAAAA==/", "_etag": "\"a300d9a1-0000-0100-0000-68701bc50000\"", "_attachments": "attachments/", "_ts": 1752177605}, {"payPeriodId": "1140035437123472", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "HOURLY Semi-monthly Payroll", "startDate": "2025-03-26T00:00:00Z", "endDate": "2025-04-10T00:00:00Z", "submitByDate": "2025-04-11T00:00:00Z", "checkDate": "2025-04-15T00:00:00Z", "checkCount": 0, "id": "8408c43f-d575-4ecd-9bef-174fa125f8e1", "companyId": "19090646", "type": "payperiod", "_rid": "NmJkAKiCbEdSrgIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdSrgIAAAAAAA==/", "_etag": "\"a300daa1-0000-0100-0000-68701bc50000\"", "_attachments": "attachments/", "_ts": 1752177605}, {"payPeriodId": "1140035437123473", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "HOURLY Semi-monthly Payroll", "startDate": "2025-04-11T00:00:00Z", "endDate": "2025-04-25T00:00:00Z", "submitByDate": "2025-04-28T00:00:00Z", "checkDate": "2025-04-30T00:00:00Z", "checkCount": 0, "id": "64363bdf-033f-4f0c-9c95-35d92e997813", "companyId": "19090646", "type": "payperiod", "_rid": "NmJkAKiCbEdTrgIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdTrgIAAAAAAA==/", "_etag": "\"a300e3a1-0000-0100-0000-68701bc50000\"", "_attachments": "attachments/", "_ts": 1752177605}, {"payPeriodId": "1140035578056419", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "HOURLY Semi-monthly Payroll", "startDate": "2025-04-26T00:00:00Z", "endDate": "2025-05-10T00:00:00Z", "submitByDate": "2025-05-13T00:00:00Z", "checkDate": "2025-05-15T00:00:00Z", "checkCount": 0, "id": "91e6e4ad-b60a-4416-85a4-73a843cd8cc8", "companyId": "19090646", "type": "payperiod", "_rid": "NmJkAKiCbEdUrgIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdUrgIAAAAAAA==/", "_etag": "\"a300e7a1-0000-0100-0000-68701bc50000\"", "_attachments": "attachments/", "_ts": 1752177605}, {"payPeriodId": "1140035578056420", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "HOURLY Semi-monthly Payroll", "startDate": "2025-05-11T00:00:00Z", "endDate": "2025-05-25T00:00:00Z", "submitByDate": "2025-05-28T00:00:00Z", "checkDate": "2025-05-30T00:00:00Z", "checkCount": 0, "id": "76871879-1374-4f61-a907-c8d616b9e8c6", "companyId": "19090646", "type": "payperiod", "_rid": "NmJkAKiCbEdVrgIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdVrgIAAAAAAA==/", "_etag": "\"a300eca1-0000-0100-0000-68701bc50000\"", "_attachments": "attachments/", "_ts": 1752177605}, {"payPeriodId": "1140035683166021", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "HOURLY Semi-monthly Payroll", "startDate": "2025-05-26T00:00:00Z", "endDate": "2025-06-15T00:00:00Z", "submitByDate": "2025-06-11T00:00:00Z", "checkDate": "2025-06-13T00:00:00Z", "checkCount": 0, "id": "4c69e303-7e2f-491d-af6e-eaf905dedac3", "companyId": "19090646", "type": "payperiod", "_rid": "NmJkAKiCbEdWrgIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdWrgIAAAAAAA==/", "_etag": "\"a300eea1-0000-0100-0000-68701bc50000\"", "_attachments": "attachments/", "_ts": 1752177605}, {"payPeriodId": "1140035683166022", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "HOURLY Semi-monthly Payroll", "startDate": "2025-06-11T00:00:00Z", "endDate": "2025-06-25T00:00:00Z", "submitByDate": "2025-06-26T00:00:00Z", "checkDate": "2025-06-30T00:00:00Z", "checkCount": 0, "id": "21199521-374d-4095-b334-74ebef87e1d2", "companyId": "19090646", "type": "payperiod", "_rid": "NmJkAKiCbEdXrgIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdXrgIAAAAAAA==/", "_etag": "\"a300f7a1-0000-0100-0000-68701bc50000\"", "_attachments": "attachments/", "_ts": 1752177605}, {"payPeriodId": "1140035808669251", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "HOURLY Semi-monthly Payroll", "startDate": "2025-06-26T00:00:00Z", "endDate": "2025-07-10T00:00:00Z", "submitByDate": "2025-07-11T00:00:00Z", "checkDate": "2025-07-15T00:00:00Z", "checkCount": 0, "id": "dc8c6425-f039-455a-9c18-eafc7cf5558a", "companyId": "19090646", "type": "payperiod", "_rid": "NmJkAKiCbEdYrgIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdYrgIAAAAAAA==/", "_etag": "\"a300fba1-0000-0100-0000-68701bc50000\"", "_attachments": "attachments/", "_ts": 1752177605}, {"payPeriodId": "1140035808669252", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "HOURLY Semi-monthly Payroll", "startDate": "2025-07-11T00:00:00Z", "endDate": "2025-07-25T00:00:00Z", "submitByDate": "2025-07-29T00:00:00Z", "checkDate": "2025-07-31T00:00:00Z", "checkCount": 0, "id": "a6d50664-731e-4c30-b0b4-e1ff11bf109c", "companyId": "19090646", "type": "payperiod", "_rid": "NmJkAKiCbEdZrgIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdZrgIAAAAAAA==/", "_etag": "\"a30000a2-0000-0100-0000-68701bc50000\"", "_attachments": "attachments/", "_ts": 1752177605}, {"payPeriodId": "1140035928680025", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "HOURLY Semi-monthly Payroll", "startDate": "2025-07-26T00:00:00Z", "endDate": "2025-08-10T00:00:00Z", "submitByDate": "2025-08-13T00:00:00Z", "checkDate": "2025-08-15T00:00:00Z", "checkCount": 0, "id": "7f3743b1-4ea0-4f8c-8cef-a35490d4f81a", "companyId": "19090646", "type": "payperiod", "_rid": "NmJkAKiCbEdargIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdargIAAAAAAA==/", "_etag": "\"a30003a2-0000-0100-0000-68701bc50000\"", "_attachments": "attachments/", "_ts": 1752177605}, {"payPeriodId": "1140035928680026", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "HOURLY Semi-monthly Payroll", "startDate": "2025-08-11T00:00:00Z", "endDate": "2025-08-25T00:00:00Z", "submitByDate": "2025-08-27T00:00:00Z", "checkDate": "2025-08-29T00:00:00Z", "checkCount": 0, "id": "818ebe0b-e4b4-4488-9a12-bc19b99e2e9c", "companyId": "19090646", "type": "payperiod", "_rid": "NmJkAKiCbEdbrgIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdbrgIAAAAAAA==/", "_etag": "\"a30004a2-0000-0100-0000-68701bc50000\"", "_attachments": "attachments/", "_ts": 1752177605}, {"payPeriodId": "1140036067515038", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "HOURLY Semi-monthly Payroll", "startDate": "2025-08-26T00:00:00Z", "endDate": "2025-09-10T00:00:00Z", "submitByDate": "2025-09-11T00:00:00Z", "checkDate": "2025-09-15T00:00:00Z", "checkCount": 0, "id": "30bc0140-a67a-4183-9719-cc34cf98fcf3", "companyId": "19090646", "type": "payperiod", "_rid": "NmJkAKiCbEdcrgIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdcrgIAAAAAAA==/", "_etag": "\"a30007a2-0000-0100-0000-68701bc50000\"", "_attachments": "attachments/", "_ts": 1752177605}, {"payPeriodId": "1140036067515039", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "HOURLY Semi-monthly Payroll", "startDate": "2025-09-11T00:00:00Z", "endDate": "2025-09-25T00:00:00Z", "submitByDate": "2025-09-26T00:00:00Z", "checkDate": "2025-09-30T00:00:00Z", "checkCount": 0, "id": "4bbb45f1-a57a-4ab5-b0b9-38ae5c0c84f2", "companyId": "19090646", "type": "payperiod", "_rid": "NmJkAKiCbEddrgIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEddrgIAAAAAAA==/", "_etag": "\"a3000da2-0000-0100-0000-68701bc50000\"", "_attachments": "attachments/", "_ts": 1752177605}, {"payPeriodId": "1140036191499748", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "HOURLY Semi-monthly Payroll", "startDate": "2025-09-26T00:00:00Z", "endDate": "2025-10-10T00:00:00Z", "submitByDate": "2025-10-13T00:00:00Z", "checkDate": "2025-10-15T00:00:00Z", "checkCount": 0, "id": "6bf3f7ae-6cb9-42e2-bd29-e6e0e09eb824", "companyId": "19090646", "type": "payperiod", "_rid": "NmJkAKiCbEdergIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdergIAAAAAAA==/", "_etag": "\"a30010a2-0000-0100-0000-68701bc60000\"", "_attachments": "attachments/", "_ts": 1752177606}, {"payPeriodId": "1140036191499749", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "HOURLY Semi-monthly Payroll", "startDate": "2025-10-11T00:00:00Z", "endDate": "2025-10-25T00:00:00Z", "submitByDate": "2025-10-29T00:00:00Z", "checkDate": "2025-10-31T00:00:00Z", "checkCount": 0, "id": "4b2c69a6-b54b-415b-9804-05007cdf6017", "companyId": "19090646", "type": "payperiod", "_rid": "NmJkAKiCbEdfrgIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdfrgIAAAAAAA==/", "_etag": "\"a30014a2-0000-0100-0000-68701bc60000\"", "_attachments": "attachments/", "_ts": 1752177606}, {"payPeriodId": "1140035023378205", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "HOURLY Semi-monthly Payroll", "startDate": "2024-12-26T00:00:00Z", "endDate": "2025-01-10T00:00:00Z", "submitByDate": "2025-01-13T00:00:00Z", "checkDate": "2025-01-15T00:00:00Z", "checkCount": 9, "id": "cb2aacc5-b662-4c3a-be10-4967e6402a78", "companyId": "19090646", "type": "payperiod", "_rid": "NmJkAKiCbEd9rgIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEd9rgIAAAAAAA==/", "_etag": "\"a3009aa2-0000-0100-0000-68701bc80000\"", "_attachments": "attachments/", "_ts": 1752177608}, {"payPeriodId": "1140035023378206", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "HOURLY Semi-monthly Payroll", "startDate": "2025-01-11T00:00:00Z", "endDate": "2025-01-25T00:00:00Z", "submitByDate": "2025-01-29T00:00:00Z", "checkDate": "2025-01-31T00:00:00Z", "checkCount": 8, "id": "b7b3d7d6-afc2-4896-a0fb-f215e66e38ec", "companyId": "19090646", "type": "payperiod", "_rid": "NmJkAKiCbEd+rgIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEd+rgIAAAAAAA==/", "_etag": "\"a300a1a2-0000-0100-0000-68701bc80000\"", "_attachments": "attachments/", "_ts": 1752177608}, {"payPeriodId": "1140035159471313", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "HOURLY Semi-monthly Payroll", "startDate": "2025-01-26T00:00:00Z", "endDate": "2025-02-10T00:00:00Z", "submitByDate": "2025-02-12T00:00:00Z", "checkDate": "2025-02-14T00:00:00Z", "checkCount": 8, "id": "25cf5396-32db-436e-85da-fc6f32fac26e", "companyId": "19090646", "type": "payperiod", "_rid": "NmJkAKiCbEd-rgIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEd-rgIAAAAAAA==/", "_etag": "\"a300a2a2-0000-0100-0000-68701bc80000\"", "_attachments": "attachments/", "_ts": 1752177608}, {"payPeriodId": "1140035159471314", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "HOURLY Semi-monthly Payroll", "startDate": "2025-02-11T00:00:00Z", "endDate": "2025-02-25T00:00:00Z", "submitByDate": "2025-02-26T00:00:00Z", "checkDate": "2025-02-28T00:00:00Z", "checkCount": 9, "id": "e810e5f7-5bba-462f-b9ac-05adf82d3be6", "companyId": "19090646", "type": "payperiod", "_rid": "NmJkAKiCbEeArgIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeArgIAAAAAAA==/", "_etag": "\"a300a5a2-0000-0100-0000-68701bc80000\"", "_attachments": "attachments/", "_ts": 1752177608}, {"payPeriodId": "1140035281561836", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "HOURLY Semi-monthly Payroll", "startDate": "2025-02-26T00:00:00Z", "endDate": "2025-03-15T00:00:00Z", "submitByDate": "2025-03-12T00:00:00Z", "checkDate": "2025-03-14T00:00:00Z", "checkCount": 7, "id": "d55dd9de-ccc6-4a91-87fc-c63310fa6c4b", "companyId": "19090646", "type": "payperiod", "_rid": "NmJkAKiCbEeBrgIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeBrgIAAAAAAA==/", "_etag": "\"a300aea2-0000-0100-0000-68701bc90000\"", "_attachments": "attachments/", "_ts": 1752177609}, {"payPeriodId": "1140035281561837", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "HOURLY Semi-monthly Payroll", "startDate": "2025-03-11T00:00:00Z", "endDate": "2025-03-25T00:00:00Z", "submitByDate": "2025-03-27T00:00:00Z", "checkDate": "2025-03-31T00:00:00Z", "checkCount": 9, "id": "5a1afd8c-f0a0-48b8-9244-984feadc2b47", "companyId": "19090646", "type": "payperiod", "_rid": "NmJkAKiCbEeCrgIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeCrgIAAAAAAA==/", "_etag": "\"a300b7a2-0000-0100-0000-68701bc90000\"", "_attachments": "attachments/", "_ts": 1752177609}, {"payPeriodId": "1140035437123472", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "HOURLY Semi-monthly Payroll", "startDate": "2025-03-26T00:00:00Z", "endDate": "2025-04-10T00:00:00Z", "submitByDate": "2025-04-11T00:00:00Z", "checkDate": "2025-04-15T00:00:00Z", "checkCount": 9, "id": "b25503c6-e95f-4924-80f9-4dc3afba7342", "companyId": "19090646", "type": "payperiod", "_rid": "NmJkAKiCbEeDrgIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeDrgIAAAAAAA==/", "_etag": "\"a300baa2-0000-0100-0000-68701bc90000\"", "_attachments": "attachments/", "_ts": 1752177609}, {"payPeriodId": "1140035437123473", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "HOURLY Semi-monthly Payroll", "startDate": "2025-04-11T00:00:00Z", "endDate": "2025-04-25T00:00:00Z", "submitByDate": "2025-04-28T00:00:00Z", "checkDate": "2025-04-30T00:00:00Z", "checkCount": 9, "id": "dcdde361-ba61-424b-9b9e-c603cec4bb7a", "companyId": "19090646", "type": "payperiod", "_rid": "NmJkAKiCbEeErgIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeErgIAAAAAAA==/", "_etag": "\"a300c3a2-0000-0100-0000-68701bc90000\"", "_attachments": "attachments/", "_ts": 1752177609}, {"payPeriodId": "1140035578056419", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "HOURLY Semi-monthly Payroll", "startDate": "2025-04-26T00:00:00Z", "endDate": "2025-05-10T00:00:00Z", "submitByDate": "2025-05-13T00:00:00Z", "checkDate": "2025-05-15T00:00:00Z", "checkCount": 8, "id": "ee649092-eb00-4a3c-966c-8a5c9d3b4d4f", "companyId": "19090646", "type": "payperiod", "_rid": "NmJkAKiCbEeFrgIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeFrgIAAAAAAA==/", "_etag": "\"a300c8a2-0000-0100-0000-68701bc90000\"", "_attachments": "attachments/", "_ts": 1752177609}, {"payPeriodId": "1140035578056420", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "HOURLY Semi-monthly Payroll", "startDate": "2025-05-11T00:00:00Z", "endDate": "2025-05-25T00:00:00Z", "submitByDate": "2025-05-28T00:00:00Z", "checkDate": "2025-05-30T00:00:00Z", "checkCount": 10, "id": "46367a79-ed4d-4b59-89ec-f0b8942fefd6", "companyId": "19090646", "type": "payperiod", "_rid": "NmJkAKiCbEeGrgIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeGrgIAAAAAAA==/", "_etag": "\"a300cba2-0000-0100-0000-68701bc90000\"", "_attachments": "attachments/", "_ts": 1752177609}, {"payPeriodId": "1140035683166021", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "HOURLY Semi-monthly Payroll", "startDate": "2025-05-26T00:00:00Z", "endDate": "2025-06-15T00:00:00Z", "submitByDate": "2025-06-11T00:00:00Z", "checkDate": "2025-06-13T00:00:00Z", "checkCount": 9, "id": "dd8302a6-6012-484b-b42f-40a6d57ad317", "companyId": "19090646", "type": "payperiod", "_rid": "NmJkAKiCbEeHrgIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeHrgIAAAAAAA==/", "_etag": "\"a300cea2-0000-0100-0000-68701bc90000\"", "_attachments": "attachments/", "_ts": 1752177609}, {"payPeriodId": "1140035683166022", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "HOURLY Semi-monthly Payroll", "startDate": "2025-06-11T00:00:00Z", "endDate": "2025-06-25T00:00:00Z", "submitByDate": "2025-06-26T00:00:00Z", "checkDate": "2025-06-30T00:00:00Z", "checkCount": 8, "id": "e345ab15-b552-4439-9ad6-5979cc7b6169", "companyId": "19090646", "type": "payperiod", "_rid": "NmJkAKiCbEeIrgIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeIrgIAAAAAAA==/", "_etag": "\"a300d1a2-0000-0100-0000-68701bc90000\"", "_attachments": "attachments/", "_ts": 1752177609}, {"payPeriodId": "1140035808669251", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "HOURLY Semi-monthly Payroll", "startDate": "2025-06-26T00:00:00Z", "endDate": "2025-07-10T00:00:00Z", "submitByDate": "2025-07-11T00:00:00Z", "checkDate": "2025-07-15T00:00:00Z", "checkCount": 0, "id": "30fcc1e8-58af-4244-9f80-aa02ce83338a", "companyId": "19090646", "type": "payperiod", "_rid": "NmJkAKiCbEeJrgIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeJrgIAAAAAAA==/", "_etag": "\"a300d6a2-0000-0100-0000-68701bc90000\"", "_attachments": "attachments/", "_ts": 1752177609}, {"payPeriodId": "1140035808669252", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "HOURLY Semi-monthly Payroll", "startDate": "2025-07-11T00:00:00Z", "endDate": "2025-07-25T00:00:00Z", "submitByDate": "2025-07-29T00:00:00Z", "checkDate": "2025-07-31T00:00:00Z", "checkCount": 0, "id": "3403f315-8828-4592-95f4-b975a051762e", "companyId": "19090646", "type": "payperiod", "_rid": "NmJkAKiCbEeKrgIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeKrgIAAAAAAA==/", "_etag": "\"a300d7a2-0000-0100-0000-68701bc90000\"", "_attachments": "attachments/", "_ts": 1752177609}, {"payPeriodId": "1140035928680025", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "HOURLY Semi-monthly Payroll", "startDate": "2025-07-26T00:00:00Z", "endDate": "2025-08-10T00:00:00Z", "submitByDate": "2025-08-13T00:00:00Z", "checkDate": "2025-08-15T00:00:00Z", "checkCount": 0, "id": "ceb3759a-3e73-4f22-b4bc-5638a6a72155", "companyId": "19090646", "type": "payperiod", "_rid": "NmJkAKiCbEeLrgIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeLrgIAAAAAAA==/", "_etag": "\"a300daa2-0000-0100-0000-68701bc90000\"", "_attachments": "attachments/", "_ts": 1752177609}, {"payPeriodId": "1140035928680026", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "HOURLY Semi-monthly Payroll", "startDate": "2025-08-11T00:00:00Z", "endDate": "2025-08-25T00:00:00Z", "submitByDate": "2025-08-27T00:00:00Z", "checkDate": "2025-08-29T00:00:00Z", "checkCount": 0, "id": "8e79882c-b32d-4adb-be28-7a5e5595fc17", "companyId": "19090646", "type": "payperiod", "_rid": "NmJkAKiCbEeMrgIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeMrgIAAAAAAA==/", "_etag": "\"a300dfa2-0000-0100-0000-68701bc90000\"", "_attachments": "attachments/", "_ts": 1752177609}, {"payPeriodId": "1140036067515038", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "HOURLY Semi-monthly Payroll", "startDate": "2025-08-26T00:00:00Z", "endDate": "2025-09-10T00:00:00Z", "submitByDate": "2025-09-11T00:00:00Z", "checkDate": "2025-09-15T00:00:00Z", "checkCount": 0, "id": "8a9ac171-6750-49d0-9595-b64e845323c2", "companyId": "19090646", "type": "payperiod", "_rid": "NmJkAKiCbEeNrgIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeNrgIAAAAAAA==/", "_etag": "\"a300e7a2-0000-0100-0000-68701bc90000\"", "_attachments": "attachments/", "_ts": 1752177609}, {"payPeriodId": "1140036067515039", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "HOURLY Semi-monthly Payroll", "startDate": "2025-09-11T00:00:00Z", "endDate": "2025-09-25T00:00:00Z", "submitByDate": "2025-09-26T00:00:00Z", "checkDate": "2025-09-30T00:00:00Z", "checkCount": 0, "id": "9f53e68c-4c3e-4fb6-b83f-b702dd45cf65", "companyId": "19090646", "type": "payperiod", "_rid": "NmJkAKiCbEeOrgIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeOrgIAAAAAAA==/", "_etag": "\"a300eba2-0000-0100-0000-68701bca0000\"", "_attachments": "attachments/", "_ts": 1752177610}, {"payPeriodId": "1140036191499748", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "HOURLY Semi-monthly Payroll", "startDate": "2025-09-26T00:00:00Z", "endDate": "2025-10-10T00:00:00Z", "submitByDate": "2025-10-13T00:00:00Z", "checkDate": "2025-10-15T00:00:00Z", "checkCount": 0, "id": "905ca65a-917d-4437-9bba-86d00ab2f8fc", "companyId": "19090646", "type": "payperiod", "_rid": "NmJkAKiCbEePrgIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEePrgIAAAAAAA==/", "_etag": "\"a300f2a2-0000-0100-0000-68701bca0000\"", "_attachments": "attachments/", "_ts": 1752177610}, {"payPeriodId": "1140036191499749", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "HOURLY Semi-monthly Payroll", "startDate": "2025-10-11T00:00:00Z", "endDate": "2025-10-25T00:00:00Z", "submitByDate": "2025-10-29T00:00:00Z", "checkDate": "2025-10-31T00:00:00Z", "checkCount": 0, "id": "95e8a7cc-f07c-4e34-98b9-4d766c8dd2e6", "companyId": "19090646", "type": "payperiod", "_rid": "NmJkAKiCbEeQrgIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeQrgIAAAAAAA==/", "_etag": "\"a300f4a2-0000-0100-0000-68701bca0000\"", "_attachments": "attachments/", "_ts": 1752177610}], "links": [{"rel": "self", "href": "https://ca-payroll-ai-mock-sb-001-secure.nicebeach-8a7a52ab.eastus.azurecontainerapps.io/ca/companies/19090646/payperiods"}]}, "status_code": 200}