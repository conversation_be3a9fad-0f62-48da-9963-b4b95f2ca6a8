{"success": true, "company_id": "13029440", "data": {"metadata": {"contentItemCount": 38}, "content": [{"payPeriodId": "1040046160721453", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-01-01T00:00:00Z", "endDate": "2025-01-14T00:00:00Z", "submitByDate": "2025-01-13T00:00:00Z", "checkDate": "2025-01-15T00:00:00Z", "checkCount": 2, "id": "c31df180-6337-4e3e-8a83-6b3ede23ef7d", "companyId": "13029440", "type": "payperiod", "_rid": "NmJkAKiCbEfmxAIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfmxAIAAAAAAA==/", "_etag": "\"a3006aed-0000-0100-0000-68701d950000\"", "_attachments": "attachments/", "_ts": 1752178069}, {"payPeriodId": "1040046325221958", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-01-15T00:00:00Z", "endDate": "2025-01-31T00:00:00Z", "submitByDate": "2025-01-29T00:00:00Z", "checkDate": "2025-01-31T00:00:00Z", "checkCount": 1, "id": "b74ca2df-753b-4469-b18a-bc90acc0e6f4", "companyId": "13029440", "type": "payperiod", "_rid": "NmJkAKiCbEfnxAIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfnxAIAAAAAAA==/", "_etag": "\"a3006fed-0000-0100-0000-68701d950000\"", "_attachments": "attachments/", "_ts": 1752178069}, {"payPeriodId": "1040046325221959", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-02-01T00:00:00Z", "endDate": "2025-02-14T00:00:00Z", "submitByDate": "2025-02-12T00:00:00Z", "checkDate": "2025-02-14T00:00:00Z", "checkCount": 2, "id": "cd98cf17-a009-4c3f-8c58-04bcc3db986a", "companyId": "13029440", "type": "payperiod", "_rid": "NmJkAKiCbEfoxAIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfoxAIAAAAAAA==/", "_etag": "\"a30070ed-0000-0100-0000-68701d950000\"", "_attachments": "attachments/", "_ts": 1752178069}, {"payPeriodId": "1040046527553017", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-02-15T00:00:00Z", "endDate": "2025-02-28T00:00:00Z", "submitByDate": "2025-02-26T00:00:00Z", "checkDate": "2025-02-28T00:00:00Z", "checkCount": 1, "id": "7ac35075-2125-49aa-9313-83365c1ec0b1", "companyId": "13029440", "type": "payperiod", "_rid": "NmJkAKiCbEfpxAIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfpxAIAAAAAAA==/", "_etag": "\"a30072ed-0000-0100-0000-68701d950000\"", "_attachments": "attachments/", "_ts": 1752178069}, {"payPeriodId": "1040046527553018", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-03-01T00:00:00Z", "endDate": "2025-03-14T00:00:00Z", "submitByDate": "2025-03-12T00:00:00Z", "checkDate": "2025-03-14T00:00:00Z", "checkCount": 2, "id": "57d454de-83fc-4fc3-a4a5-07dd6eca1887", "companyId": "13029440", "type": "payperiod", "_rid": "NmJkAKiCbEfqxAIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfqxAIAAAAAAA==/", "_etag": "\"a30074ed-0000-0100-0000-68701d950000\"", "_attachments": "attachments/", "_ts": 1752178069}, {"payPeriodId": "1040046698086689", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-03-15T00:00:00Z", "endDate": "2025-03-31T00:00:00Z", "submitByDate": "2025-03-28T00:00:00Z", "checkDate": "2025-04-01T00:00:00Z", "checkCount": 0, "id": "d05fd228-831b-41de-baa4-bbfd1f5d2c04", "companyId": "13029440", "type": "payperiod", "_rid": "NmJkAKiCbEfrxAIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfrxAIAAAAAAA==/", "_etag": "\"a30076ed-0000-0100-0000-68701d950000\"", "_attachments": "attachments/", "_ts": 1752178069}, {"payPeriodId": "1040046698086690", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-04-01T00:00:00Z", "endDate": "2025-04-14T00:00:00Z", "submitByDate": "2025-04-11T00:00:00Z", "checkDate": "2025-04-15T00:00:00Z", "checkCount": 0, "id": "8c75738e-a4b4-4d38-b678-8f1c02119c45", "companyId": "13029440", "type": "payperiod", "_rid": "NmJkAKiCbEfsxAIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfsxAIAAAAAAA==/", "_etag": "\"a30079ed-0000-0100-0000-68701d950000\"", "_attachments": "attachments/", "_ts": 1752178069}, {"payPeriodId": "1040046857592336", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-04-15T00:00:00Z", "endDate": "2025-04-30T00:00:00Z", "submitByDate": "2025-04-29T00:00:00Z", "checkDate": "2025-05-01T00:00:00Z", "checkCount": 0, "id": "d4fdd761-8c34-44d3-a234-d6c944ce312f", "companyId": "13029440", "type": "payperiod", "_rid": "NmJkAKiCbEftxAIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEftxAIAAAAAAA==/", "_etag": "\"a3007bed-0000-0100-0000-68701d960000\"", "_attachments": "attachments/", "_ts": 1752178070}, {"payPeriodId": "1040046857592337", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-05-01T00:00:00Z", "endDate": "2025-05-14T00:00:00Z", "submitByDate": "2025-05-13T00:00:00Z", "checkDate": "2025-05-15T00:00:00Z", "checkCount": 0, "id": "f48b46f5-4021-4d26-b439-68ba86df4159", "companyId": "13029440", "type": "payperiod", "_rid": "NmJkAKiCbEfuxAIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfuxAIAAAAAAA==/", "_etag": "\"a3007fed-0000-0100-0000-68701d960000\"", "_attachments": "attachments/", "_ts": 1752178070}, {"payPeriodId": "1040047006094028", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-05-15T00:00:00Z", "endDate": "2025-05-31T00:00:00Z", "submitByDate": "2025-05-28T00:00:00Z", "checkDate": "2025-05-30T00:00:00Z", "checkCount": 0, "id": "b6a7d97c-83bd-48b1-8046-deb04ccffb49", "companyId": "13029440", "type": "payperiod", "_rid": "NmJkAKiCbEfvxAIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfvxAIAAAAAAA==/", "_etag": "\"a30082ed-0000-0100-0000-68701d960000\"", "_attachments": "attachments/", "_ts": 1752178070}, {"payPeriodId": "1040047006094029", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-06-01T00:00:00Z", "endDate": "2025-06-14T00:00:00Z", "submitByDate": "2025-06-11T00:00:00Z", "checkDate": "2025-06-13T00:00:00Z", "checkCount": 0, "id": "f3e0ab37-fa43-4cc8-b0ee-620a8683a678", "companyId": "13029440", "type": "payperiod", "_rid": "NmJkAKiCbEfwxAIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfwxAIAAAAAAA==/", "_etag": "\"a30089ed-0000-0100-0000-68701d960000\"", "_attachments": "attachments/", "_ts": 1752178070}, {"payPeriodId": "1040047194107196", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-06-15T00:00:00Z", "endDate": "2025-06-30T00:00:00Z", "submitByDate": "2025-06-27T00:00:00Z", "checkDate": "2025-07-01T00:00:00Z", "checkCount": 0, "id": "06bfbf3f-0399-4aca-968e-85c65b8bf5e2", "companyId": "13029440", "type": "payperiod", "_rid": "NmJkAKiCbEfxxAIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfxxAIAAAAAAA==/", "_etag": "\"a3008eed-0000-0100-0000-68701d960000\"", "_attachments": "attachments/", "_ts": 1752178070}, {"payPeriodId": "1040047194107197", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-07-01T00:00:00Z", "endDate": "2025-07-14T00:00:00Z", "submitByDate": "2025-07-11T00:00:00Z", "checkDate": "2025-07-15T00:00:00Z", "checkCount": 0, "id": "a19be709-acfd-4c19-afd3-4709fbf9b718", "companyId": "13029440", "type": "payperiod", "_rid": "NmJkAKiCbEfyxAIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfyxAIAAAAAAA==/", "_etag": "\"a30092ed-0000-0100-0000-68701d960000\"", "_attachments": "attachments/", "_ts": 1752178070}, {"payPeriodId": "1040047361535676", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-07-15T00:00:00Z", "endDate": "2025-07-31T00:00:00Z", "submitByDate": "2025-07-30T00:00:00Z", "checkDate": "2025-08-01T00:00:00Z", "checkCount": 0, "id": "302c791e-7c27-464f-a3be-caddc6e27250", "companyId": "13029440", "type": "payperiod", "_rid": "NmJkAKiCbEfzxAIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfzxAIAAAAAAA==/", "_etag": "\"a30094ed-0000-0100-0000-68701d960000\"", "_attachments": "attachments/", "_ts": 1752178070}, {"payPeriodId": "1040047361535677", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-08-01T00:00:00Z", "endDate": "2025-08-14T00:00:00Z", "submitByDate": "2025-08-13T00:00:00Z", "checkDate": "2025-08-15T00:00:00Z", "checkCount": 0, "id": "7b371673-2835-4c2a-b1d1-0ac094fad3c8", "companyId": "13029440", "type": "payperiod", "_rid": "NmJkAKiCbEf0xAIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf0xAIAAAAAAA==/", "_etag": "\"a30097ed-0000-0100-0000-68701d960000\"", "_attachments": "attachments/", "_ts": 1752178070}, {"payPeriodId": "1040047523389082", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-08-15T00:00:00Z", "endDate": "2025-08-31T00:00:00Z", "submitByDate": "2025-08-27T00:00:00Z", "checkDate": "2025-08-29T00:00:00Z", "checkCount": 0, "id": "a3faa32b-d94f-4c82-8183-a4e2937e1fcd", "companyId": "13029440", "type": "payperiod", "_rid": "NmJkAKiCbEf1xAIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf1xAIAAAAAAA==/", "_etag": "\"a3009ced-0000-0100-0000-68701d960000\"", "_attachments": "attachments/", "_ts": 1752178070}, {"payPeriodId": "1040047523389083", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-09-01T00:00:00Z", "endDate": "2025-09-14T00:00:00Z", "submitByDate": "2025-09-11T00:00:00Z", "checkDate": "2025-09-15T00:00:00Z", "checkCount": 0, "id": "f566ba6c-d74a-46d6-ad0b-87f660e2866f", "companyId": "13029440", "type": "payperiod", "_rid": "NmJkAKiCbEf2xAIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf2xAIAAAAAAA==/", "_etag": "\"a3009eed-0000-0100-0000-68701d960000\"", "_attachments": "attachments/", "_ts": 1752178070}, {"payPeriodId": "1040047709563501", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-09-15T00:00:00Z", "endDate": "2025-09-30T00:00:00Z", "submitByDate": "2025-09-29T00:00:00Z", "checkDate": "2025-10-01T00:00:00Z", "checkCount": 0, "id": "4919b9b1-24e5-4113-981c-c3bbf467f9fd", "companyId": "13029440", "type": "payperiod", "_rid": "NmJkAKiCbEf3xAIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf3xAIAAAAAAA==/", "_etag": "\"a300a1ed-0000-0100-0000-68701d960000\"", "_attachments": "attachments/", "_ts": 1752178070}, {"payPeriodId": "1040047709563502", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-10-01T00:00:00Z", "endDate": "2025-10-14T00:00:00Z", "submitByDate": "2025-10-13T00:00:00Z", "checkDate": "2025-10-15T00:00:00Z", "checkCount": 0, "id": "7cfb52e4-019a-42a5-a059-58abfb4ce991", "companyId": "13029440", "type": "payperiod", "_rid": "NmJkAKiCbEf4xAIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf4xAIAAAAAAA==/", "_etag": "\"a300a2ed-0000-0100-0000-68701d960000\"", "_attachments": "attachments/", "_ts": 1752178070}, {"payPeriodId": "1040046160721453", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-01-01T00:00:00Z", "endDate": "2025-01-14T00:00:00Z", "submitByDate": "2025-01-13T00:00:00Z", "checkDate": "2025-01-15T00:00:00Z", "checkCount": 2, "id": "8818be07-ca29-4d87-a691-d90b4f7dfa23", "companyId": "13029440", "type": "payperiod", "_rid": "NmJkAKiCbEf9xAIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf9xAIAAAAAAA==/", "_etag": "\"a300b4ed-0000-0100-0000-68701d970000\"", "_attachments": "attachments/", "_ts": 1752178071}, {"payPeriodId": "1040046325221958", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-01-15T00:00:00Z", "endDate": "2025-01-31T00:00:00Z", "submitByDate": "2025-01-29T00:00:00Z", "checkDate": "2025-01-31T00:00:00Z", "checkCount": 1, "id": "6f2d7d99-634c-4d63-9437-dd1dfc7809dc", "companyId": "13029440", "type": "payperiod", "_rid": "NmJkAKiCbEf+xAIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf+xAIAAAAAAA==/", "_etag": "\"a300b7ed-0000-0100-0000-68701d970000\"", "_attachments": "attachments/", "_ts": 1752178071}, {"payPeriodId": "1040046325221959", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-02-01T00:00:00Z", "endDate": "2025-02-14T00:00:00Z", "submitByDate": "2025-02-12T00:00:00Z", "checkDate": "2025-02-14T00:00:00Z", "checkCount": 2, "id": "da3a9909-cd6c-4702-89fa-da9ba409802c", "companyId": "13029440", "type": "payperiod", "_rid": "NmJkAKiCbEf-xAIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf-xAIAAAAAAA==/", "_etag": "\"a300baed-0000-0100-0000-68701d970000\"", "_attachments": "attachments/", "_ts": 1752178071}, {"payPeriodId": "1040046527553017", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-02-15T00:00:00Z", "endDate": "2025-02-28T00:00:00Z", "submitByDate": "2025-02-26T00:00:00Z", "checkDate": "2025-02-28T00:00:00Z", "checkCount": 1, "id": "f46a7c78-88c1-4273-aa65-a6d84a0cd443", "companyId": "13029440", "type": "payperiod", "_rid": "NmJkAKiCbEcAxQIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcAxQIAAAAAAA==/", "_etag": "\"a300c3ed-0000-0100-0000-68701d970000\"", "_attachments": "attachments/", "_ts": 1752178071}, {"payPeriodId": "1040046527553018", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-03-01T00:00:00Z", "endDate": "2025-03-14T00:00:00Z", "submitByDate": "2025-03-12T00:00:00Z", "checkDate": "2025-03-14T00:00:00Z", "checkCount": 2, "id": "6f98e1fe-16df-46d2-acd6-9b439dca0e13", "companyId": "13029440", "type": "payperiod", "_rid": "NmJkAKiCbEcBxQIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcBxQIAAAAAAA==/", "_etag": "\"a300c6ed-0000-0100-0000-68701d970000\"", "_attachments": "attachments/", "_ts": 1752178071}, {"payPeriodId": "1040046698086689", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-03-15T00:00:00Z", "endDate": "2025-03-31T00:00:00Z", "submitByDate": "2025-03-28T00:00:00Z", "checkDate": "2025-04-01T00:00:00Z", "checkCount": 1, "id": "9fb67890-1146-4904-9f2d-53b19d05f4ec", "companyId": "13029440", "type": "payperiod", "_rid": "NmJkAKiCbEcCxQIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcCxQIAAAAAAA==/", "_etag": "\"a300c9ed-0000-0100-0000-68701d970000\"", "_attachments": "attachments/", "_ts": 1752178071}, {"payPeriodId": "1040046698086690", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-04-01T00:00:00Z", "endDate": "2025-04-14T00:00:00Z", "submitByDate": "2025-04-11T00:00:00Z", "checkDate": "2025-04-15T00:00:00Z", "checkCount": 2, "id": "b52e5686-fc7b-45c7-a531-9911ea809c9f", "companyId": "13029440", "type": "payperiod", "_rid": "NmJkAKiCbEcDxQIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcDxQIAAAAAAA==/", "_etag": "\"a300cbed-0000-0100-0000-68701d970000\"", "_attachments": "attachments/", "_ts": 1752178071}, {"payPeriodId": "1040046857592336", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-04-15T00:00:00Z", "endDate": "2025-04-30T00:00:00Z", "submitByDate": "2025-04-29T00:00:00Z", "checkDate": "2025-05-01T00:00:00Z", "checkCount": 1, "id": "3186e0c4-6d50-4277-a0e4-297a7ebd7c1a", "companyId": "13029440", "type": "payperiod", "_rid": "NmJkAKiCbEcExQIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcExQIAAAAAAA==/", "_etag": "\"a300d0ed-0000-0100-0000-68701d970000\"", "_attachments": "attachments/", "_ts": 1752178071}, {"payPeriodId": "1040046857592337", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-05-01T00:00:00Z", "endDate": "2025-05-14T00:00:00Z", "submitByDate": "2025-05-13T00:00:00Z", "checkDate": "2025-05-15T00:00:00Z", "checkCount": 2, "id": "cea8f68f-940b-464d-a081-e9a00ea846b8", "companyId": "13029440", "type": "payperiod", "_rid": "NmJkAKiCbEcFxQIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcFxQIAAAAAAA==/", "_etag": "\"a300d6ed-0000-0100-0000-68701d970000\"", "_attachments": "attachments/", "_ts": 1752178071}, {"payPeriodId": "1040047006094028", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-05-15T00:00:00Z", "endDate": "2025-05-31T00:00:00Z", "submitByDate": "2025-05-28T00:00:00Z", "checkDate": "2025-05-30T00:00:00Z", "checkCount": 1, "id": "c7962ca8-5eb8-4849-8596-03f767594fa4", "companyId": "13029440", "type": "payperiod", "_rid": "NmJkAKiCbEcGxQIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcGxQIAAAAAAA==/", "_etag": "\"a300d9ed-0000-0100-0000-68701d980000\"", "_attachments": "attachments/", "_ts": 1752178072}, {"payPeriodId": "1040047006094029", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-06-01T00:00:00Z", "endDate": "2025-06-14T00:00:00Z", "submitByDate": "2025-06-11T00:00:00Z", "checkDate": "2025-06-13T00:00:00Z", "checkCount": 2, "id": "c02bb8aa-4d61-41a3-bb7c-bad8186c1efa", "companyId": "13029440", "type": "payperiod", "_rid": "NmJkAKiCbEcHxQIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcHxQIAAAAAAA==/", "_etag": "\"a300dbed-0000-0100-0000-68701d980000\"", "_attachments": "attachments/", "_ts": 1752178072}, {"payPeriodId": "1040047194107196", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-06-15T00:00:00Z", "endDate": "2025-06-30T00:00:00Z", "submitByDate": "2025-06-27T00:00:00Z", "checkDate": "2025-07-01T00:00:00Z", "checkCount": 1, "id": "7367a45d-000f-48f1-8635-4fa90049b1eb", "companyId": "13029440", "type": "payperiod", "_rid": "NmJkAKiCbEcIxQIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcIxQIAAAAAAA==/", "_etag": "\"a300dded-0000-0100-0000-68701d980000\"", "_attachments": "attachments/", "_ts": 1752178072}, {"payPeriodId": "1040047194107197", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-07-01T00:00:00Z", "endDate": "2025-07-14T00:00:00Z", "submitByDate": "2025-07-11T00:00:00Z", "checkDate": "2025-07-15T00:00:00Z", "checkCount": 0, "id": "62dd7680-35f2-4577-bff9-07b4c3f13ef8", "companyId": "13029440", "type": "payperiod", "_rid": "NmJkAKiCbEcJxQIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcJxQIAAAAAAA==/", "_etag": "\"a300dfed-0000-0100-0000-68701d980000\"", "_attachments": "attachments/", "_ts": 1752178072}, {"payPeriodId": "1040047361535676", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-07-15T00:00:00Z", "endDate": "2025-07-31T00:00:00Z", "submitByDate": "2025-07-30T00:00:00Z", "checkDate": "2025-08-01T00:00:00Z", "checkCount": 0, "id": "e14ceb14-99dc-40c2-988d-93abf012b50c", "companyId": "13029440", "type": "payperiod", "_rid": "NmJkAKiCbEcKxQIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcKxQIAAAAAAA==/", "_etag": "\"a300e0ed-0000-0100-0000-68701d980000\"", "_attachments": "attachments/", "_ts": 1752178072}, {"payPeriodId": "1040047361535677", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-08-01T00:00:00Z", "endDate": "2025-08-14T00:00:00Z", "submitByDate": "2025-08-13T00:00:00Z", "checkDate": "2025-08-15T00:00:00Z", "checkCount": 0, "id": "305a2945-3992-4631-8055-f580111b1417", "companyId": "13029440", "type": "payperiod", "_rid": "NmJkAKiCbEcLxQIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcLxQIAAAAAAA==/", "_etag": "\"a300e3ed-0000-0100-0000-68701d980000\"", "_attachments": "attachments/", "_ts": 1752178072}, {"payPeriodId": "1040047523389082", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-08-15T00:00:00Z", "endDate": "2025-08-31T00:00:00Z", "submitByDate": "2025-08-27T00:00:00Z", "checkDate": "2025-08-29T00:00:00Z", "checkCount": 0, "id": "ca127565-3f73-4288-ba12-8c2381075523", "companyId": "13029440", "type": "payperiod", "_rid": "NmJkAKiCbEcMxQIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcMxQIAAAAAAA==/", "_etag": "\"a300e6ed-0000-0100-0000-68701d980000\"", "_attachments": "attachments/", "_ts": 1752178072}, {"payPeriodId": "1040047523389083", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-09-01T00:00:00Z", "endDate": "2025-09-14T00:00:00Z", "submitByDate": "2025-09-11T00:00:00Z", "checkDate": "2025-09-15T00:00:00Z", "checkCount": 0, "id": "4e913219-2105-457b-94fc-a690931ed532", "companyId": "13029440", "type": "payperiod", "_rid": "NmJkAKiCbEcNxQIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcNxQIAAAAAAA==/", "_etag": "\"a300e8ed-0000-0100-0000-68701d980000\"", "_attachments": "attachments/", "_ts": 1752178072}, {"payPeriodId": "1040047709563501", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-09-15T00:00:00Z", "endDate": "2025-09-30T00:00:00Z", "submitByDate": "2025-09-29T00:00:00Z", "checkDate": "2025-10-01T00:00:00Z", "checkCount": 0, "id": "19aed00b-c903-4b73-a3bd-333ebf7a93d9", "companyId": "13029440", "type": "payperiod", "_rid": "NmJkAKiCbEcOxQIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcOxQIAAAAAAA==/", "_etag": "\"a300ebed-0000-0100-0000-68701d980000\"", "_attachments": "attachments/", "_ts": 1752178072}, {"payPeriodId": "1040047709563502", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-10-01T00:00:00Z", "endDate": "2025-10-14T00:00:00Z", "submitByDate": "2025-10-13T00:00:00Z", "checkDate": "2025-10-15T00:00:00Z", "checkCount": 0, "id": "18ee4175-3ebc-49f6-a076-db7e6d4b7dce", "companyId": "13029440", "type": "payperiod", "_rid": "NmJkAKiCbEcPxQIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcPxQIAAAAAAA==/", "_etag": "\"a300eeed-0000-0100-0000-68701d980000\"", "_attachments": "attachments/", "_ts": 1752178072}], "links": [{"rel": "self", "href": "https://ca-payroll-ai-mock-sb-001-secure.nicebeach-8a7a52ab.eastus.azurecontainerapps.io/ca/companies/13029440/payperiods"}]}, "status_code": 200}