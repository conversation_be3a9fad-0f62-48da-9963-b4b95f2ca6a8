{"success": true, "company_id": "18143909", "data": {"metadata": {"contentItemCount": 160}, "content": [{"payPeriodId": "1090066038055673", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2024-12-30T00:00:00Z", "endDate": "2025-01-05T00:00:00Z", "submitByDate": "2024-12-31T00:00:00Z", "checkDate": "2025-01-03T00:00:00Z", "checkCount": 1, "id": "becc4588-9bb8-4c20-9fb8-d6e37dec6e49", "companyId": "18143909", "type": "payperiod", "_rid": "NmJkAKiCbEcOSwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcOSwAAAAAAAA==/", "_etag": "\"9800ed02-0000-0100-0000-686fd4d70000\"", "_attachments": "attachments/", "_ts": 1752159447}, {"payPeriodId": "1090066259071880", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-01-06T00:00:00Z", "endDate": "2025-01-12T00:00:00Z", "submitByDate": "2025-01-07T00:00:00Z", "checkDate": "2025-01-08T00:00:00Z", "checkCount": 1, "id": "349e98ba-0c69-4c36-a94b-b3dd2d859560", "companyId": "18143909", "type": "payperiod", "_rid": "NmJkAKiCbEcPSwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcPSwAAAAAAAA==/", "_etag": "\"9800f002-0000-0100-0000-686fd4d70000\"", "_attachments": "attachments/", "_ts": 1752159447}, {"payPeriodId": "1090066339308360", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-01-13T00:00:00Z", "endDate": "2025-01-19T00:00:00Z", "submitByDate": "2025-01-15T00:00:00Z", "checkDate": "2025-01-16T00:00:00Z", "checkCount": 1, "id": "be2a2fbd-6fbb-4196-9438-6e1c8fb8e279", "companyId": "18143909", "type": "payperiod", "_rid": "NmJkAKiCbEcQSwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcQSwAAAAAAAA==/", "_etag": "\"9800f302-0000-0100-0000-686fd4d70000\"", "_attachments": "attachments/", "_ts": 1752159447}, {"payPeriodId": "1090066476126862", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-01-20T00:00:00Z", "endDate": "2025-01-26T00:00:00Z", "submitByDate": "2025-01-21T00:00:00Z", "checkDate": "2025-01-22T00:00:00Z", "checkCount": 1, "id": "bc3b8730-7ea7-491c-ac3c-a987158b8fae", "companyId": "18143909", "type": "payperiod", "_rid": "NmJkAKiCbEcRSwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcRSwAAAAAAAA==/", "_etag": "\"9800f502-0000-0100-0000-686fd4d70000\"", "_attachments": "attachments/", "_ts": 1752159447}, {"payPeriodId": "1090066648463141", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-01-27T00:00:00Z", "endDate": "2025-02-02T00:00:00Z", "submitByDate": "2025-01-29T00:00:00Z", "checkDate": "2025-01-31T00:00:00Z", "checkCount": 1, "id": "7894c00f-b775-4e5e-8c91-57eac354e90c", "companyId": "18143909", "type": "payperiod", "_rid": "NmJkAKiCbEcSSwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcSSwAAAAAAAA==/", "_etag": "\"9800fa02-0000-0100-0000-686fd4d70000\"", "_attachments": "attachments/", "_ts": 1752159447}, {"payPeriodId": "1090067067600674", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-02-17T00:00:00Z", "endDate": "2025-02-23T00:00:00Z", "submitByDate": "2025-02-19T00:00:00Z", "checkDate": "2025-02-21T00:00:00Z", "checkCount": 1, "id": "8efa5184-c266-4372-916c-7aecdcf214c3", "companyId": "18143909", "type": "payperiod", "_rid": "NmJkAKiCbEcTSwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcTSwAAAAAAAA==/", "_etag": "\"9800fb02-0000-0100-0000-686fd4d70000\"", "_attachments": "attachments/", "_ts": 1752159447}, {"payPeriodId": "1090069214844307", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-02-24T00:00:00Z", "endDate": "2025-03-02T00:00:00Z", "submitByDate": "2025-02-26T00:00:00Z", "checkDate": "2025-02-26T00:00:00Z", "checkCount": 1, "id": "7e919749-70a5-470e-bd7f-2d40625f952d", "companyId": "18143909", "type": "payperiod", "_rid": "NmJkAKiCbEcUSwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcUSwAAAAAAAA==/", "_etag": "\"9800fe02-0000-0100-0000-686fd4d70000\"", "_attachments": "attachments/", "_ts": 1752159447}, {"payPeriodId": "1090067384984132", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-03-03T00:00:00Z", "endDate": "2025-03-09T00:00:00Z", "submitByDate": "2025-03-04T00:00:00Z", "checkDate": "2025-03-05T00:00:00Z", "checkCount": 1, "id": "b07078be-139e-4170-aa59-7b2833b764dd", "companyId": "18143909", "type": "payperiod", "_rid": "NmJkAKiCbEcVSwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcVSwAAAAAAAA==/", "_etag": "\"98000203-0000-0100-0000-686fd4d70000\"", "_attachments": "attachments/", "_ts": 1752159447}, {"payPeriodId": "1090067526885120", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-03-10T00:00:00Z", "endDate": "2025-03-16T00:00:00Z", "submitByDate": "2025-03-11T00:00:00Z", "checkDate": "2025-03-12T00:00:00Z", "checkCount": 1, "id": "8493304c-cba4-4f07-b8ca-ab4017a5edc9", "companyId": "18143909", "type": "payperiod", "_rid": "NmJkAKiCbEcWSwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcWSwAAAAAAAA==/", "_etag": "\"98000503-0000-0100-0000-686fd4d80000\"", "_attachments": "attachments/", "_ts": 1752159448}, {"payPeriodId": "1090067695355956", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-03-17T00:00:00Z", "endDate": "2025-03-23T00:00:00Z", "submitByDate": "2025-03-18T00:00:00Z", "checkDate": "2025-03-19T00:00:00Z", "checkCount": 1, "id": "786dc12f-8da5-48b8-95bb-6cd3a202569c", "companyId": "18143909", "type": "payperiod", "_rid": "NmJkAKiCbEcXSwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcXSwAAAAAAAA==/", "_etag": "\"98000903-0000-0100-0000-686fd4d80000\"", "_attachments": "attachments/", "_ts": 1752159448}, {"payPeriodId": "1090068009767489", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-03-24T00:00:00Z", "endDate": "2025-03-30T00:00:00Z", "submitByDate": "2025-03-26T00:00:00Z", "checkDate": "2025-03-28T00:00:00Z", "checkCount": 1, "id": "273070d1-2845-436a-a330-efb9fd554484", "companyId": "18143909", "type": "payperiod", "_rid": "NmJkAKiCbEcYSwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcYSwAAAAAAAA==/", "_etag": "\"98000a03-0000-0100-0000-686fd4d80000\"", "_attachments": "attachments/", "_ts": 1752159448}, {"payPeriodId": "1090068015188524", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-03-31T00:00:00Z", "endDate": "2025-04-06T00:00:00Z", "submitByDate": "2025-04-01T00:00:00Z", "checkDate": "2025-04-02T00:00:00Z", "checkCount": 0, "id": "e2a06034-7ecc-4255-a8bf-7d0c3b2ff80d", "companyId": "18143909", "type": "payperiod", "_rid": "NmJkAKiCbEcZSwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcZSwAAAAAAAA==/", "_etag": "\"98000e03-0000-0100-0000-686fd4d80000\"", "_attachments": "attachments/", "_ts": 1752159448}, {"payPeriodId": "1090068163456072", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-04-07T00:00:00Z", "endDate": "2025-04-13T00:00:00Z", "submitByDate": "2025-04-08T00:00:00Z", "checkDate": "2025-04-09T00:00:00Z", "checkCount": 0, "id": "a8dce544-4579-49fe-82b2-c87a4c533d40", "companyId": "18143909", "type": "payperiod", "_rid": "NmJkAKiCbEcaSwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcaSwAAAAAAAA==/", "_etag": "\"98001103-0000-0100-0000-686fd4d80000\"", "_attachments": "attachments/", "_ts": 1752159448}, {"payPeriodId": "1090068323037662", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-04-14T00:00:00Z", "endDate": "2025-04-20T00:00:00Z", "submitByDate": "2025-04-15T00:00:00Z", "checkDate": "2025-04-16T00:00:00Z", "checkCount": 0, "id": "82829e82-babf-4910-a80d-aca0f7f7eb70", "companyId": "18143909", "type": "payperiod", "_rid": "NmJkAKiCbEcbSwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcbSwAAAAAAAA==/", "_etag": "\"98001203-0000-0100-0000-686fd4d80000\"", "_attachments": "attachments/", "_ts": 1752159448}, {"payPeriodId": "1090068475436336", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-04-21T00:00:00Z", "endDate": "2025-04-27T00:00:00Z", "submitByDate": "2025-04-22T00:00:00Z", "checkDate": "2025-04-23T00:00:00Z", "checkCount": 0, "id": "a5387dea-bace-4c00-82ad-ecb2c9c834c9", "companyId": "18143909", "type": "payperiod", "_rid": "NmJkAKiCbEccSwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEccSwAAAAAAAA==/", "_etag": "\"98001503-0000-0100-0000-686fd4d80000\"", "_attachments": "attachments/", "_ts": 1752159448}, {"payPeriodId": "1090068617280592", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-04-28T00:00:00Z", "endDate": "2025-05-04T00:00:00Z", "submitByDate": "2025-04-29T00:00:00Z", "checkDate": "2025-04-30T00:00:00Z", "checkCount": 0, "id": "2dbbc62e-a208-48c0-8c01-85bea9eb4950", "companyId": "18143909", "type": "payperiod", "_rid": "NmJkAKiCbEcdSwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcdSwAAAAAAAA==/", "_etag": "\"98001903-0000-0100-0000-686fd4d80000\"", "_attachments": "attachments/", "_ts": 1752159448}, {"payPeriodId": "1090069068278291", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-05-12T00:00:00Z", "endDate": "2025-05-18T00:00:00Z", "submitByDate": "2025-05-13T00:00:00Z", "checkDate": "2025-05-14T00:00:00Z", "checkCount": 0, "id": "0b3147d7-e983-4579-9523-8d5c32b45ad3", "companyId": "18143909", "type": "payperiod", "_rid": "NmJkAKiCbEceSwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEceSwAAAAAAAA==/", "_etag": "\"98001b03-0000-0100-0000-686fd4d80000\"", "_attachments": "attachments/", "_ts": 1752159448}, {"payPeriodId": "1090069203322485", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-05-19T00:00:00Z", "endDate": "2025-05-25T00:00:00Z", "submitByDate": "2025-05-20T00:00:00Z", "checkDate": "2025-05-21T00:00:00Z", "checkCount": 0, "id": "9bfef5e2-1870-48e6-ac08-393070d3dfbe", "companyId": "18143909", "type": "payperiod", "_rid": "NmJkAKiCbEcfSwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcfSwAAAAAAAA==/", "_etag": "\"98001c03-0000-0100-0000-686fd4d80000\"", "_attachments": "attachments/", "_ts": 1752159448}, {"payPeriodId": "1090069373000499", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-05-26T00:00:00Z", "endDate": "2025-06-01T00:00:00Z", "submitByDate": "2025-05-27T00:00:00Z", "checkDate": "2025-05-28T00:00:00Z", "checkCount": 0, "id": "71fc6475-2903-4c90-a3a5-031e2c1cda61", "companyId": "18143909", "type": "payperiod", "_rid": "NmJkAKiCbEcgSwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcgSwAAAAAAAA==/", "_etag": "\"98002203-0000-0100-0000-686fd4d80000\"", "_attachments": "attachments/", "_ts": 1752159448}, {"payPeriodId": "1090069423393864", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-06-02T00:00:00Z", "endDate": "2025-06-08T00:00:00Z", "submitByDate": "2025-06-04T00:00:00Z", "checkDate": "2025-06-06T00:00:00Z", "checkCount": 0, "id": "32166b44-1e0a-4dc5-866b-28651e2279eb", "companyId": "18143909", "type": "payperiod", "_rid": "NmJkAKiCbEchSwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEchSwAAAAAAAA==/", "_etag": "\"98002503-0000-0100-0000-686fd4d80000\"", "_attachments": "attachments/", "_ts": 1752159448}, {"payPeriodId": "1090069514803944", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-06-09T00:00:00Z", "endDate": "2025-06-15T00:00:00Z", "submitByDate": "2025-06-10T00:00:00Z", "checkDate": "2025-06-11T00:00:00Z", "checkCount": 0, "id": "200911b6-2a1a-467e-b009-c26bf4d73e91", "companyId": "18143909", "type": "payperiod", "_rid": "NmJkAKiCbEciSwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEciSwAAAAAAAA==/", "_etag": "\"98002903-0000-0100-0000-686fd4d80000\"", "_attachments": "attachments/", "_ts": 1752159448}, {"payPeriodId": "1090069679475517", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-06-16T00:00:00Z", "endDate": "2025-06-22T00:00:00Z", "submitByDate": "2025-06-17T00:00:00Z", "checkDate": "2025-06-18T00:00:00Z", "checkCount": 0, "id": "5adb62dc-08eb-4fa6-9d05-b164a2f1ecee", "companyId": "18143909", "type": "payperiod", "_rid": "NmJkAKiCbEcjSwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcjSwAAAAAAAA==/", "_etag": "\"98002d03-0000-0100-0000-686fd4d80000\"", "_attachments": "attachments/", "_ts": 1752159448}, {"payPeriodId": "1090069830640898", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-06-23T00:00:00Z", "endDate": "2025-06-29T00:00:00Z", "submitByDate": "2025-06-24T00:00:00Z", "checkDate": "2025-06-25T00:00:00Z", "checkCount": 0, "id": "6a439c1a-eea0-4e77-b063-23538353023a", "companyId": "18143909", "type": "payperiod", "_rid": "NmJkAKiCbEckSwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEckSwAAAAAAAA==/", "_etag": "\"98002e03-0000-0100-0000-686fd4d90000\"", "_attachments": "attachments/", "_ts": 1752159449}, {"payPeriodId": "1090070005029130", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-06-30T00:00:00Z", "endDate": "2025-07-06T00:00:00Z", "submitByDate": "2025-07-01T00:00:00Z", "checkDate": "2025-07-03T00:00:00Z", "checkCount": 0, "id": "5dd49f91-7802-4490-bbb0-2be3ffac8848", "companyId": "18143909", "type": "payperiod", "_rid": "NmJkAKiCbEclSwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEclSwAAAAAAAA==/", "_etag": "\"98003303-0000-0100-0000-686fd4d90000\"", "_attachments": "attachments/", "_ts": 1752159449}, {"payPeriodId": "1090066800428490", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-02-03T00:00:00Z", "endDate": "2025-02-09T00:00:00Z", "submitByDate": "2025-02-05T00:00:00Z", "checkDate": "2025-02-07T00:00:00Z", "checkCount": 0, "id": "5f2560e1-9aa6-44a4-98a9-85ade5672cca", "companyId": "18143909", "type": "payperiod", "_rid": "NmJkAKiCbEcmSwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcmSwAAAAAAAA==/", "_etag": "\"98003603-0000-0100-0000-686fd4d90000\"", "_attachments": "attachments/", "_ts": 1752159449}, {"payPeriodId": "1090066947285414", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-02-10T00:00:00Z", "endDate": "2025-02-16T00:00:00Z", "submitByDate": "2025-02-12T00:00:00Z", "checkDate": "2025-02-14T00:00:00Z", "checkCount": 0, "id": "11bff364-f7c4-4565-87ca-c6de7470bde5", "companyId": "18143909", "type": "payperiod", "_rid": "NmJkAKiCbEcnSwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcnSwAAAAAAAA==/", "_etag": "\"98003903-0000-0100-0000-686fd4d90000\"", "_attachments": "attachments/", "_ts": 1752159449}, {"payPeriodId": "1090068910263784", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-05-05T00:00:00Z", "endDate": "2025-05-11T00:00:00Z", "submitByDate": "2025-05-07T00:00:00Z", "checkDate": "2025-05-09T00:00:00Z", "checkCount": 0, "id": "443a53ae-75f2-486f-bee8-a07f995b08c7", "companyId": "18143909", "type": "payperiod", "_rid": "NmJkAKiCbEcoSwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcoSwAAAAAAAA==/", "_etag": "\"98003c03-0000-0100-0000-686fd4d90000\"", "_attachments": "attachments/", "_ts": 1752159449}, {"payPeriodId": "1090070150115745", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-07-07T00:00:00Z", "endDate": "2025-07-13T00:00:00Z", "submitByDate": "2025-07-09T00:00:00Z", "checkDate": "2025-07-11T00:00:00Z", "checkCount": 0, "id": "0877b2d2-fda0-4ada-9380-3cca2615ff30", "companyId": "18143909", "type": "payperiod", "_rid": "NmJkAKiCbEcpSwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcpSwAAAAAAAA==/", "_etag": "\"98003f03-0000-0100-0000-686fd4d90000\"", "_attachments": "attachments/", "_ts": 1752159449}, {"payPeriodId": "1090070305360542", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-07-14T00:00:00Z", "endDate": "2025-07-20T00:00:00Z", "submitByDate": "2025-07-16T00:00:00Z", "checkDate": "2025-07-18T00:00:00Z", "checkCount": 0, "id": "b8283fc2-d24c-4ee8-ba30-163ee25a33ef", "companyId": "18143909", "type": "payperiod", "_rid": "NmJkAKiCbEcqSwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcqSwAAAAAAAA==/", "_etag": "\"98004003-0000-0100-0000-686fd4d90000\"", "_attachments": "attachments/", "_ts": 1752159449}, {"payPeriodId": "1090070445527096", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-07-21T00:00:00Z", "endDate": "2025-07-27T00:00:00Z", "submitByDate": "2025-07-23T00:00:00Z", "checkDate": "2025-07-25T00:00:00Z", "checkCount": 0, "id": "aa311a14-918f-4dc4-ad0b-5039560d872f", "companyId": "18143909", "type": "payperiod", "_rid": "NmJkAKiCbEcrSwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcrSwAAAAAAAA==/", "_etag": "\"98004403-0000-0100-0000-686fd4d90000\"", "_attachments": "attachments/", "_ts": 1752159449}, {"payPeriodId": "1090070611468783", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-07-28T00:00:00Z", "endDate": "2025-08-03T00:00:00Z", "submitByDate": "2025-07-30T00:00:00Z", "checkDate": "2025-08-01T00:00:00Z", "checkCount": 0, "id": "241ec87d-ec0b-4aea-84c1-44db88d0dcbe", "companyId": "18143909", "type": "payperiod", "_rid": "NmJkAKiCbEcsSwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcsSwAAAAAAAA==/", "_etag": "\"98004803-0000-0100-0000-686fd4d90000\"", "_attachments": "attachments/", "_ts": 1752159449}, {"payPeriodId": "1090070902393919", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-08-04T00:00:00Z", "endDate": "2025-08-10T00:00:00Z", "submitByDate": "2025-08-06T00:00:00Z", "checkDate": "2025-08-08T00:00:00Z", "checkCount": 0, "id": "2bdd1a77-6928-48ac-bbbf-d4092152cba5", "companyId": "18143909", "type": "payperiod", "_rid": "NmJkAKiCbEctSwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEctSwAAAAAAAA==/", "_etag": "\"98004c03-0000-0100-0000-686fd4d90000\"", "_attachments": "attachments/", "_ts": 1752159449}, {"payPeriodId": "1090070908998405", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-08-11T00:00:00Z", "endDate": "2025-08-17T00:00:00Z", "submitByDate": "2025-08-13T00:00:00Z", "checkDate": "2025-08-15T00:00:00Z", "checkCount": 0, "id": "345b29d4-749c-424e-b7ce-143d306831e9", "companyId": "18143909", "type": "payperiod", "_rid": "NmJkAKiCbEcuSwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcuSwAAAAAAAA==/", "_etag": "\"98005003-0000-0100-0000-686fd4d90000\"", "_attachments": "attachments/", "_ts": 1752159449}, {"payPeriodId": "1090071061174847", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-08-18T00:00:00Z", "endDate": "2025-08-24T00:00:00Z", "submitByDate": "2025-08-20T00:00:00Z", "checkDate": "2025-08-22T00:00:00Z", "checkCount": 0, "id": "6e5482df-5e7b-4400-9855-91abf6bec97c", "companyId": "18143909", "type": "payperiod", "_rid": "NmJkAKiCbEcvSwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcvSwAAAAAAAA==/", "_etag": "\"98005203-0000-0100-0000-686fd4d90000\"", "_attachments": "attachments/", "_ts": 1752159449}, {"payPeriodId": "1090071231835880", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-08-25T00:00:00Z", "endDate": "2025-08-31T00:00:00Z", "submitByDate": "2025-08-27T00:00:00Z", "checkDate": "2025-08-29T00:00:00Z", "checkCount": 0, "id": "14463ba5-9344-4cb9-b732-36db13a7da2a", "companyId": "18143909", "type": "payperiod", "_rid": "NmJkAKiCbEcwSwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcwSwAAAAAAAA==/", "_etag": "\"98005303-0000-0100-0000-686fd4d90000\"", "_attachments": "attachments/", "_ts": 1752159449}, {"payPeriodId": "1090071380993253", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-09-01T00:00:00Z", "endDate": "2025-09-07T00:00:00Z", "submitByDate": "2025-09-03T00:00:00Z", "checkDate": "2025-09-05T00:00:00Z", "checkCount": 0, "id": "fc1fbc81-fdf3-4797-a63f-9636d3ccf717", "companyId": "18143909", "type": "payperiod", "_rid": "NmJkAKiCbEcxSwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcxSwAAAAAAAA==/", "_etag": "\"98005603-0000-0100-0000-686fd4d90000\"", "_attachments": "attachments/", "_ts": 1752159449}, {"payPeriodId": "1090071497970417", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-09-08T00:00:00Z", "endDate": "2025-09-14T00:00:00Z", "submitByDate": "2025-09-10T00:00:00Z", "checkDate": "2025-09-12T00:00:00Z", "checkCount": 0, "id": "00cace50-fcdf-4271-ab89-b016029133c8", "companyId": "18143909", "type": "payperiod", "_rid": "NmJkAKiCbEcySwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcySwAAAAAAAA==/", "_etag": "\"98005d03-0000-0100-0000-686fd4da0000\"", "_attachments": "attachments/", "_ts": 1752159450}, {"payPeriodId": "1090071693889437", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-09-15T00:00:00Z", "endDate": "2025-09-21T00:00:00Z", "submitByDate": "2025-09-17T00:00:00Z", "checkDate": "2025-09-19T00:00:00Z", "checkCount": 0, "id": "6bf507b6-0c16-4285-831e-3c5dd6772ade", "companyId": "18143909", "type": "payperiod", "_rid": "NmJkAKiCbEczSwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEczSwAAAAAAAA==/", "_etag": "\"98005f03-0000-0100-0000-686fd4da0000\"", "_attachments": "attachments/", "_ts": 1752159450}, {"payPeriodId": "1090071842151411", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-09-22T00:00:00Z", "endDate": "2025-09-28T00:00:00Z", "submitByDate": "2025-09-24T00:00:00Z", "checkDate": "2025-09-26T00:00:00Z", "checkCount": 0, "id": "5f401e10-e919-49bc-ac7f-f1333f04fad4", "companyId": "18143909", "type": "payperiod", "_rid": "NmJkAKiCbEc0SwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc0SwAAAAAAAA==/", "_etag": "\"98006303-0000-0100-0000-686fd4da0000\"", "_attachments": "attachments/", "_ts": 1752159450}, {"payPeriodId": "1090072007835082", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-09-29T00:00:00Z", "endDate": "2025-10-05T00:00:00Z", "submitByDate": "2025-10-01T00:00:00Z", "checkDate": "2025-10-03T00:00:00Z", "checkCount": 0, "id": "49e2f34f-02f0-4c56-8358-1d6fa91a2dba", "companyId": "18143909", "type": "payperiod", "_rid": "NmJkAKiCbEc1SwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc1SwAAAAAAAA==/", "_etag": "\"98006703-0000-0100-0000-686fd4da0000\"", "_attachments": "attachments/", "_ts": 1752159450}, {"payPeriodId": "1090066038055673", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2024-12-30T00:00:00Z", "endDate": "2025-01-05T00:00:00Z", "submitByDate": "2024-12-31T00:00:00Z", "checkDate": "2025-01-03T00:00:00Z", "checkCount": 1, "id": "25d914dd-d9d3-4095-89cb-4630026bd8d1", "companyId": "18143909", "type": "payperiod", "_rid": "NmJkAKiCbEc4SwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc4SwAAAAAAAA==/", "_etag": "\"98007503-0000-0100-0000-686fd4da0000\"", "_attachments": "attachments/", "_ts": 1752159450}, {"payPeriodId": "1090066259071880", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-01-06T00:00:00Z", "endDate": "2025-01-12T00:00:00Z", "submitByDate": "2025-01-07T00:00:00Z", "checkDate": "2025-01-08T00:00:00Z", "checkCount": 1, "id": "bb6d912f-973d-4542-8ed9-acc4bbee0abf", "companyId": "18143909", "type": "payperiod", "_rid": "NmJkAKiCbEc5SwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc5SwAAAAAAAA==/", "_etag": "\"98007b03-0000-0100-0000-686fd4da0000\"", "_attachments": "attachments/", "_ts": 1752159450}, {"payPeriodId": "1090066339308360", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-01-13T00:00:00Z", "endDate": "2025-01-19T00:00:00Z", "submitByDate": "2025-01-15T00:00:00Z", "checkDate": "2025-01-16T00:00:00Z", "checkCount": 1, "id": "a6999b27-9c67-4507-9e16-0d2979de6897", "companyId": "18143909", "type": "payperiod", "_rid": "NmJkAKiCbEc6SwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc6SwAAAAAAAA==/", "_etag": "\"98008003-0000-0100-0000-686fd4da0000\"", "_attachments": "attachments/", "_ts": 1752159450}, {"payPeriodId": "1090066476126862", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-01-20T00:00:00Z", "endDate": "2025-01-26T00:00:00Z", "submitByDate": "2025-01-21T00:00:00Z", "checkDate": "2025-01-22T00:00:00Z", "checkCount": 1, "id": "faa8968a-b7a4-4b01-8d71-ad144032f0b1", "companyId": "18143909", "type": "payperiod", "_rid": "NmJkAKiCbEc7SwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc7SwAAAAAAAA==/", "_etag": "\"98008203-0000-0100-0000-686fd4da0000\"", "_attachments": "attachments/", "_ts": 1752159450}, {"payPeriodId": "1090066648463141", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-01-27T00:00:00Z", "endDate": "2025-02-02T00:00:00Z", "submitByDate": "2025-01-29T00:00:00Z", "checkDate": "2025-01-31T00:00:00Z", "checkCount": 1, "id": "e3b3648f-21c6-4a4c-9652-cea8dd42e9fd", "companyId": "18143909", "type": "payperiod", "_rid": "NmJkAKiCbEc8SwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc8SwAAAAAAAA==/", "_etag": "\"98008503-0000-0100-0000-686fd4da0000\"", "_attachments": "attachments/", "_ts": 1752159450}, {"payPeriodId": "1090067067600674", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-02-17T00:00:00Z", "endDate": "2025-02-23T00:00:00Z", "submitByDate": "2025-02-19T00:00:00Z", "checkDate": "2025-02-21T00:00:00Z", "checkCount": 1, "id": "8902a26b-09d9-4d08-9efd-4b0aeffe950e", "companyId": "18143909", "type": "payperiod", "_rid": "NmJkAKiCbEc9SwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc9SwAAAAAAAA==/", "_etag": "\"98008803-0000-0100-0000-686fd4da0000\"", "_attachments": "attachments/", "_ts": 1752159450}, {"payPeriodId": "1090069214844307", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-02-24T00:00:00Z", "endDate": "2025-03-02T00:00:00Z", "submitByDate": "2025-02-26T00:00:00Z", "checkDate": "2025-02-26T00:00:00Z", "checkCount": 1, "id": "fcb8d4fb-4b16-4128-9074-bad072b308bc", "companyId": "18143909", "type": "payperiod", "_rid": "NmJkAKiCbEc+SwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc+SwAAAAAAAA==/", "_etag": "\"98008d03-0000-0100-0000-686fd4da0000\"", "_attachments": "attachments/", "_ts": 1752159450}, {"payPeriodId": "1090067384984132", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-03-03T00:00:00Z", "endDate": "2025-03-09T00:00:00Z", "submitByDate": "2025-03-04T00:00:00Z", "checkDate": "2025-03-05T00:00:00Z", "checkCount": 1, "id": "8824b59c-fc4e-47fd-b7cf-076fac683321", "companyId": "18143909", "type": "payperiod", "_rid": "NmJkAKiCbEc-SwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc-SwAAAAAAAA==/", "_etag": "\"98009003-0000-0100-0000-686fd4db0000\"", "_attachments": "attachments/", "_ts": 1752159451}, {"payPeriodId": "1090067526885120", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-03-10T00:00:00Z", "endDate": "2025-03-16T00:00:00Z", "submitByDate": "2025-03-11T00:00:00Z", "checkDate": "2025-03-12T00:00:00Z", "checkCount": 1, "id": "c5d699bc-da69-4176-961b-e977ac2cfcc3", "companyId": "18143909", "type": "payperiod", "_rid": "NmJkAKiCbEdASwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdASwAAAAAAAA==/", "_etag": "\"98009103-0000-0100-0000-686fd4db0000\"", "_attachments": "attachments/", "_ts": 1752159451}, {"payPeriodId": "1090067695355956", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-03-17T00:00:00Z", "endDate": "2025-03-23T00:00:00Z", "submitByDate": "2025-03-18T00:00:00Z", "checkDate": "2025-03-19T00:00:00Z", "checkCount": 1, "id": "9340092f-02ec-40b5-87b2-76ef87d6a8f4", "companyId": "18143909", "type": "payperiod", "_rid": "NmJkAKiCbEdBSwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdBSwAAAAAAAA==/", "_etag": "\"98009303-0000-0100-0000-686fd4db0000\"", "_attachments": "attachments/", "_ts": 1752159451}, {"payPeriodId": "1090068009767489", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-03-24T00:00:00Z", "endDate": "2025-03-30T00:00:00Z", "submitByDate": "2025-03-26T00:00:00Z", "checkDate": "2025-03-28T00:00:00Z", "checkCount": 1, "id": "239c4e5f-bac7-49e1-b1e8-f5a77f805377", "companyId": "18143909", "type": "payperiod", "_rid": "NmJkAKiCbEdCSwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdCSwAAAAAAAA==/", "_etag": "\"98009603-0000-0100-0000-686fd4db0000\"", "_attachments": "attachments/", "_ts": 1752159451}, {"payPeriodId": "1090068015188524", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-03-31T00:00:00Z", "endDate": "2025-04-06T00:00:00Z", "submitByDate": "2025-04-01T00:00:00Z", "checkDate": "2025-04-02T00:00:00Z", "checkCount": 1, "id": "20e61eb1-fe4a-4533-8ec9-55a0075f0a83", "companyId": "18143909", "type": "payperiod", "_rid": "NmJkAKiCbEdDSwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdDSwAAAAAAAA==/", "_etag": "\"98009c03-0000-0100-0000-686fd4db0000\"", "_attachments": "attachments/", "_ts": 1752159451}, {"payPeriodId": "1090068163456072", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-04-07T00:00:00Z", "endDate": "2025-04-13T00:00:00Z", "submitByDate": "2025-04-08T00:00:00Z", "checkDate": "2025-04-09T00:00:00Z", "checkCount": 1, "id": "256255c3-58da-434b-9615-35e3e0471885", "companyId": "18143909", "type": "payperiod", "_rid": "NmJkAKiCbEdESwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdESwAAAAAAAA==/", "_etag": "\"98009e03-0000-0100-0000-686fd4db0000\"", "_attachments": "attachments/", "_ts": 1752159451}, {"payPeriodId": "1090068323037662", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-04-14T00:00:00Z", "endDate": "2025-04-20T00:00:00Z", "submitByDate": "2025-04-15T00:00:00Z", "checkDate": "2025-04-16T00:00:00Z", "checkCount": 1, "id": "3bf331c0-ad18-476e-8db7-2f10abaa072e", "companyId": "18143909", "type": "payperiod", "_rid": "NmJkAKiCbEdFSwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdFSwAAAAAAAA==/", "_etag": "\"9800a603-0000-0100-0000-686fd4db0000\"", "_attachments": "attachments/", "_ts": 1752159451}, {"payPeriodId": "1090068475436336", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-04-21T00:00:00Z", "endDate": "2025-04-27T00:00:00Z", "submitByDate": "2025-04-22T00:00:00Z", "checkDate": "2025-04-23T00:00:00Z", "checkCount": 1, "id": "dfee0c38-df4a-4fc7-b357-d9ea4a2873bf", "companyId": "18143909", "type": "payperiod", "_rid": "NmJkAKiCbEdGSwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdGSwAAAAAAAA==/", "_etag": "\"9800ac03-0000-0100-0000-686fd4db0000\"", "_attachments": "attachments/", "_ts": 1752159451}, {"payPeriodId": "1090068617280592", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-04-28T00:00:00Z", "endDate": "2025-05-04T00:00:00Z", "submitByDate": "2025-04-29T00:00:00Z", "checkDate": "2025-04-30T00:00:00Z", "checkCount": 1, "id": "1f3191b4-5f1d-4cf7-b72b-e3aaf21c36eb", "companyId": "18143909", "type": "payperiod", "_rid": "NmJkAKiCbEdHSwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdHSwAAAAAAAA==/", "_etag": "\"9800ad03-0000-0100-0000-686fd4db0000\"", "_attachments": "attachments/", "_ts": 1752159451}, {"payPeriodId": "1090069068278291", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-05-12T00:00:00Z", "endDate": "2025-05-18T00:00:00Z", "submitByDate": "2025-05-13T00:00:00Z", "checkDate": "2025-05-14T00:00:00Z", "checkCount": 1, "id": "8307e10f-8083-4390-a701-eeb18e297832", "companyId": "18143909", "type": "payperiod", "_rid": "NmJkAKiCbEdISwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdISwAAAAAAAA==/", "_etag": "\"9800b003-0000-0100-0000-686fd4db0000\"", "_attachments": "attachments/", "_ts": 1752159451}, {"payPeriodId": "1090069203322485", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-05-19T00:00:00Z", "endDate": "2025-05-25T00:00:00Z", "submitByDate": "2025-05-20T00:00:00Z", "checkDate": "2025-05-21T00:00:00Z", "checkCount": 1, "id": "e866c5e1-333f-48dd-9e4e-b83c164851ef", "companyId": "18143909", "type": "payperiod", "_rid": "NmJkAKiCbEdJSwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdJSwAAAAAAAA==/", "_etag": "\"9800b303-0000-0100-0000-686fd4db0000\"", "_attachments": "attachments/", "_ts": 1752159451}, {"payPeriodId": "1090069373000499", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-05-26T00:00:00Z", "endDate": "2025-06-01T00:00:00Z", "submitByDate": "2025-05-27T00:00:00Z", "checkDate": "2025-05-28T00:00:00Z", "checkCount": 1, "id": "6d4253fd-759c-4253-ad02-3c7f30d1a37f", "companyId": "18143909", "type": "payperiod", "_rid": "NmJkAKiCbEdKSwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdKSwAAAAAAAA==/", "_etag": "\"9800b703-0000-0100-0000-686fd4db0000\"", "_attachments": "attachments/", "_ts": 1752159451}, {"payPeriodId": "1090069423393864", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-06-02T00:00:00Z", "endDate": "2025-06-08T00:00:00Z", "submitByDate": "2025-06-04T00:00:00Z", "checkDate": "2025-06-06T00:00:00Z", "checkCount": 1, "id": "5456f10c-85ac-405b-aae0-223e131f2f37", "companyId": "18143909", "type": "payperiod", "_rid": "NmJkAKiCbEdLSwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdLSwAAAAAAAA==/", "_etag": "\"9800ba03-0000-0100-0000-686fd4db0000\"", "_attachments": "attachments/", "_ts": 1752159451}, {"payPeriodId": "1090069514803944", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-06-09T00:00:00Z", "endDate": "2025-06-15T00:00:00Z", "submitByDate": "2025-06-10T00:00:00Z", "checkDate": "2025-06-11T00:00:00Z", "checkCount": 1, "id": "fbe55057-9bbd-40e6-b7e8-1fc90438c8aa", "companyId": "18143909", "type": "payperiod", "_rid": "NmJkAKiCbEdMSwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdMSwAAAAAAAA==/", "_etag": "\"9800be03-0000-0100-0000-686fd4dc0000\"", "_attachments": "attachments/", "_ts": 1752159452}, {"payPeriodId": "1090069679475517", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-06-16T00:00:00Z", "endDate": "2025-06-22T00:00:00Z", "submitByDate": "2025-06-17T00:00:00Z", "checkDate": "2025-06-18T00:00:00Z", "checkCount": 1, "id": "722e8d44-ee76-44f3-b9b4-ee9ce3382c32", "companyId": "18143909", "type": "payperiod", "_rid": "NmJkAKiCbEdNSwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdNSwAAAAAAAA==/", "_etag": "\"9800c103-0000-0100-0000-686fd4dc0000\"", "_attachments": "attachments/", "_ts": 1752159452}, {"payPeriodId": "1090069830640898", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-06-23T00:00:00Z", "endDate": "2025-06-29T00:00:00Z", "submitByDate": "2025-06-24T00:00:00Z", "checkDate": "2025-06-25T00:00:00Z", "checkCount": 1, "id": "15eb6971-4faf-41ad-b3c4-3936f706d7d9", "companyId": "18143909", "type": "payperiod", "_rid": "NmJkAKiCbEdOSwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdOSwAAAAAAAA==/", "_etag": "\"9800c203-0000-0100-0000-686fd4dc0000\"", "_attachments": "attachments/", "_ts": 1752159452}, {"payPeriodId": "1090070005029130", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-06-30T00:00:00Z", "endDate": "2025-07-06T00:00:00Z", "submitByDate": "2025-07-01T00:00:00Z", "checkDate": "2025-07-03T00:00:00Z", "checkCount": 1, "id": "4d10cf8a-daa8-4c05-9560-eafb31bf9bf1", "companyId": "18143909", "type": "payperiod", "_rid": "NmJkAKiCbEdPSwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdPSwAAAAAAAA==/", "_etag": "\"9800c403-0000-0100-0000-686fd4dc0000\"", "_attachments": "attachments/", "_ts": 1752159452}, {"payPeriodId": "1090066800428490", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-02-03T00:00:00Z", "endDate": "2025-02-09T00:00:00Z", "submitByDate": "2025-02-05T00:00:00Z", "checkDate": "2025-02-07T00:00:00Z", "checkCount": 0, "id": "0c7d58f8-ad38-4b94-917f-e3f28f5cf940", "companyId": "18143909", "type": "payperiod", "_rid": "NmJkAKiCbEdQSwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdQSwAAAAAAAA==/", "_etag": "\"9800c903-0000-0100-0000-686fd4dc0000\"", "_attachments": "attachments/", "_ts": 1752159452}, {"payPeriodId": "1090066947285414", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-02-10T00:00:00Z", "endDate": "2025-02-16T00:00:00Z", "submitByDate": "2025-02-12T00:00:00Z", "checkDate": "2025-02-14T00:00:00Z", "checkCount": 0, "id": "32d90fb3-b8ae-4f65-9a21-212a91ece060", "companyId": "18143909", "type": "payperiod", "_rid": "NmJkAKiCbEdRSwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdRSwAAAAAAAA==/", "_etag": "\"9800cd03-0000-0100-0000-686fd4dc0000\"", "_attachments": "attachments/", "_ts": 1752159452}, {"payPeriodId": "1090068910263784", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-05-05T00:00:00Z", "endDate": "2025-05-11T00:00:00Z", "submitByDate": "2025-05-07T00:00:00Z", "checkDate": "2025-05-09T00:00:00Z", "checkCount": 0, "id": "967b3617-7a6e-4fc4-b732-29eb85767fba", "companyId": "18143909", "type": "payperiod", "_rid": "NmJkAKiCbEdSSwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdSSwAAAAAAAA==/", "_etag": "\"9800d203-0000-0100-0000-686fd4dc0000\"", "_attachments": "attachments/", "_ts": 1752159452}, {"payPeriodId": "1090070150115745", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-07-07T00:00:00Z", "endDate": "2025-07-13T00:00:00Z", "submitByDate": "2025-07-09T00:00:00Z", "checkDate": "2025-07-11T00:00:00Z", "checkCount": 0, "id": "1a8f9d23-6b03-4c16-9beb-81b43a5bdfe5", "companyId": "18143909", "type": "payperiod", "_rid": "NmJkAKiCbEdTSwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdTSwAAAAAAAA==/", "_etag": "\"9800dd03-0000-0100-0000-686fd4dc0000\"", "_attachments": "attachments/", "_ts": 1752159452}, {"payPeriodId": "1090070305360542", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-07-14T00:00:00Z", "endDate": "2025-07-20T00:00:00Z", "submitByDate": "2025-07-16T00:00:00Z", "checkDate": "2025-07-18T00:00:00Z", "checkCount": 0, "id": "d6250a7e-8193-4561-93fb-9f37626d8de4", "companyId": "18143909", "type": "payperiod", "_rid": "NmJkAKiCbEdUSwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdUSwAAAAAAAA==/", "_etag": "\"9800e103-0000-0100-0000-686fd4dc0000\"", "_attachments": "attachments/", "_ts": 1752159452}, {"payPeriodId": "1090070445527096", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-07-21T00:00:00Z", "endDate": "2025-07-27T00:00:00Z", "submitByDate": "2025-07-23T00:00:00Z", "checkDate": "2025-07-25T00:00:00Z", "checkCount": 0, "id": "d1dbffe5-54e7-4f75-9cbc-3c0967169a2e", "companyId": "18143909", "type": "payperiod", "_rid": "NmJkAKiCbEdVSwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdVSwAAAAAAAA==/", "_etag": "\"9800e303-0000-0100-0000-686fd4dc0000\"", "_attachments": "attachments/", "_ts": 1752159452}, {"payPeriodId": "1090070611468783", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-07-28T00:00:00Z", "endDate": "2025-08-03T00:00:00Z", "submitByDate": "2025-07-30T00:00:00Z", "checkDate": "2025-08-01T00:00:00Z", "checkCount": 0, "id": "81c69938-2bd4-4e86-93ec-2c1f614a6a34", "companyId": "18143909", "type": "payperiod", "_rid": "NmJkAKiCbEdWSwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdWSwAAAAAAAA==/", "_etag": "\"9800e603-0000-0100-0000-686fd4dc0000\"", "_attachments": "attachments/", "_ts": 1752159452}, {"payPeriodId": "1090070902393919", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-08-04T00:00:00Z", "endDate": "2025-08-10T00:00:00Z", "submitByDate": "2025-08-06T00:00:00Z", "checkDate": "2025-08-08T00:00:00Z", "checkCount": 0, "id": "401011e9-5551-4ec3-a05c-0a8e723cc74e", "companyId": "18143909", "type": "payperiod", "_rid": "NmJkAKiCbEdXSwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdXSwAAAAAAAA==/", "_etag": "\"9800e903-0000-0100-0000-686fd4dc0000\"", "_attachments": "attachments/", "_ts": 1752159452}, {"payPeriodId": "1090070908998405", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-08-11T00:00:00Z", "endDate": "2025-08-17T00:00:00Z", "submitByDate": "2025-08-13T00:00:00Z", "checkDate": "2025-08-15T00:00:00Z", "checkCount": 0, "id": "25060eb5-f017-47b6-9b92-cd2772275196", "companyId": "18143909", "type": "payperiod", "_rid": "NmJkAKiCbEdYSwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdYSwAAAAAAAA==/", "_etag": "\"9800ec03-0000-0100-0000-686fd4dc0000\"", "_attachments": "attachments/", "_ts": 1752159452}, {"payPeriodId": "1090071061174847", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-08-18T00:00:00Z", "endDate": "2025-08-24T00:00:00Z", "submitByDate": "2025-08-20T00:00:00Z", "checkDate": "2025-08-22T00:00:00Z", "checkCount": 0, "id": "074e7d68-56b5-44f3-a1ae-533b4244c14c", "companyId": "18143909", "type": "payperiod", "_rid": "NmJkAKiCbEdZSwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdZSwAAAAAAAA==/", "_etag": "\"9800f103-0000-0100-0000-686fd4dc0000\"", "_attachments": "attachments/", "_ts": 1752159452}, {"payPeriodId": "1090071231835880", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-08-25T00:00:00Z", "endDate": "2025-08-31T00:00:00Z", "submitByDate": "2025-08-27T00:00:00Z", "checkDate": "2025-08-29T00:00:00Z", "checkCount": 0, "id": "0b2926d4-0c0d-42f9-840e-6f24c4e478b7", "companyId": "18143909", "type": "payperiod", "_rid": "NmJkAKiCbEdaSwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdaSwAAAAAAAA==/", "_etag": "\"9800f703-0000-0100-0000-686fd4dd0000\"", "_attachments": "attachments/", "_ts": 1752159453}, {"payPeriodId": "1090071380993253", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-09-01T00:00:00Z", "endDate": "2025-09-07T00:00:00Z", "submitByDate": "2025-09-03T00:00:00Z", "checkDate": "2025-09-05T00:00:00Z", "checkCount": 0, "id": "be32a38a-8f8a-4970-9e2d-e9948abd2801", "companyId": "18143909", "type": "payperiod", "_rid": "NmJkAKiCbEdbSwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdbSwAAAAAAAA==/", "_etag": "\"9800fe03-0000-0100-0000-686fd4dd0000\"", "_attachments": "attachments/", "_ts": 1752159453}, {"payPeriodId": "1090071497970417", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-09-08T00:00:00Z", "endDate": "2025-09-14T00:00:00Z", "submitByDate": "2025-09-10T00:00:00Z", "checkDate": "2025-09-12T00:00:00Z", "checkCount": 0, "id": "9d5aab36-123e-46c6-8913-e9bda57d10d6", "companyId": "18143909", "type": "payperiod", "_rid": "NmJkAKiCbEdcSwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdcSwAAAAAAAA==/", "_etag": "\"98000304-0000-0100-0000-686fd4dd0000\"", "_attachments": "attachments/", "_ts": 1752159453}, {"payPeriodId": "1090071693889437", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-09-15T00:00:00Z", "endDate": "2025-09-21T00:00:00Z", "submitByDate": "2025-09-17T00:00:00Z", "checkDate": "2025-09-19T00:00:00Z", "checkCount": 0, "id": "a3ff35e8-34a2-4b41-8806-3c4e5509da4d", "companyId": "18143909", "type": "payperiod", "_rid": "NmJkAKiCbEddSwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEddSwAAAAAAAA==/", "_etag": "\"98001004-0000-0100-0000-686fd4dd0000\"", "_attachments": "attachments/", "_ts": 1752159453}, {"payPeriodId": "1090071842151411", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-09-22T00:00:00Z", "endDate": "2025-09-28T00:00:00Z", "submitByDate": "2025-09-24T00:00:00Z", "checkDate": "2025-09-26T00:00:00Z", "checkCount": 0, "id": "609c482e-1355-41a4-a3ef-03b3467bc2ee", "companyId": "18143909", "type": "payperiod", "_rid": "NmJkAKiCbEdeSwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdeSwAAAAAAAA==/", "_etag": "\"98001304-0000-0100-0000-686fd4dd0000\"", "_attachments": "attachments/", "_ts": 1752159453}, {"payPeriodId": "1090072007835082", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-09-29T00:00:00Z", "endDate": "2025-10-05T00:00:00Z", "submitByDate": "2025-10-01T00:00:00Z", "checkDate": "2025-10-03T00:00:00Z", "checkCount": 0, "id": "321a0712-a7d7-4d83-a3dd-eeb9737cd5c5", "companyId": "18143909", "type": "payperiod", "_rid": "NmJkAKiCbEdfSwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdfSwAAAAAAAA==/", "_etag": "\"98001804-0000-0100-0000-686fd4dd0000\"", "_attachments": "attachments/", "_ts": 1752159453}, {"payPeriodId": "1090066038055673", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2024-12-30T00:00:00Z", "endDate": "2025-01-05T00:00:00Z", "submitByDate": "2024-12-31T00:00:00Z", "checkDate": "2025-01-03T00:00:00Z", "checkCount": 1, "id": "ed773922-557f-48ad-b533-334406be763f", "companyId": "18143909", "type": "payperiod", "_rid": "NmJkAKiCbEc7RAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc7RAEAAAAAAA==/", "_etag": "\"9e00d537-0000-0100-0000-686ffb7a0000\"", "_attachments": "attachments/", "_ts": 1752169338}, {"payPeriodId": "1090066259071880", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-01-06T00:00:00Z", "endDate": "2025-01-12T00:00:00Z", "submitByDate": "2025-01-07T00:00:00Z", "checkDate": "2025-01-08T00:00:00Z", "checkCount": 1, "id": "52a7a442-4431-4068-87ea-4e648300288c", "companyId": "18143909", "type": "payperiod", "_rid": "NmJkAKiCbEc8RAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc8RAEAAAAAAA==/", "_etag": "\"9e00d637-0000-0100-0000-686ffb7a0000\"", "_attachments": "attachments/", "_ts": 1752169338}, {"payPeriodId": "1090066339308360", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-01-13T00:00:00Z", "endDate": "2025-01-19T00:00:00Z", "submitByDate": "2025-01-15T00:00:00Z", "checkDate": "2025-01-16T00:00:00Z", "checkCount": 1, "id": "2560980a-d388-4169-bdc4-81877f7c32c2", "companyId": "18143909", "type": "payperiod", "_rid": "NmJkAKiCbEc9RAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc9RAEAAAAAAA==/", "_etag": "\"9e00d737-0000-0100-0000-686ffb7a0000\"", "_attachments": "attachments/", "_ts": 1752169338}, {"payPeriodId": "1090066476126862", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-01-20T00:00:00Z", "endDate": "2025-01-26T00:00:00Z", "submitByDate": "2025-01-21T00:00:00Z", "checkDate": "2025-01-22T00:00:00Z", "checkCount": 1, "id": "06172e0e-0179-409e-8f54-28b96f1b9e17", "companyId": "18143909", "type": "payperiod", "_rid": "NmJkAKiCbEc+RAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc+RAEAAAAAAA==/", "_etag": "\"9e00db37-0000-0100-0000-686ffb7b0000\"", "_attachments": "attachments/", "_ts": 1752169339}, {"payPeriodId": "1090066648463141", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-01-27T00:00:00Z", "endDate": "2025-02-02T00:00:00Z", "submitByDate": "2025-01-29T00:00:00Z", "checkDate": "2025-01-31T00:00:00Z", "checkCount": 1, "id": "6364731c-f567-42cf-a018-50d510d4f3d6", "companyId": "18143909", "type": "payperiod", "_rid": "NmJkAKiCbEc-RAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc-RAEAAAAAAA==/", "_etag": "\"9e00de37-0000-0100-0000-686ffb7b0000\"", "_attachments": "attachments/", "_ts": 1752169339}, {"payPeriodId": "1090067067600674", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-02-17T00:00:00Z", "endDate": "2025-02-23T00:00:00Z", "submitByDate": "2025-02-19T00:00:00Z", "checkDate": "2025-02-21T00:00:00Z", "checkCount": 1, "id": "bc12fbcb-0b35-48c4-a80b-1c69d81b26f3", "companyId": "18143909", "type": "payperiod", "_rid": "NmJkAKiCbEdARAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdARAEAAAAAAA==/", "_etag": "\"9e00e137-0000-0100-0000-686ffb7b0000\"", "_attachments": "attachments/", "_ts": 1752169339}, {"payPeriodId": "1090069214844307", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-02-24T00:00:00Z", "endDate": "2025-03-02T00:00:00Z", "submitByDate": "2025-02-26T00:00:00Z", "checkDate": "2025-02-26T00:00:00Z", "checkCount": 1, "id": "2eb32c1e-e545-4e03-9d38-2178e4fde950", "companyId": "18143909", "type": "payperiod", "_rid": "NmJkAKiCbEdBRAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdBRAEAAAAAAA==/", "_etag": "\"9e00e337-0000-0100-0000-686ffb7b0000\"", "_attachments": "attachments/", "_ts": 1752169339}, {"payPeriodId": "1090067384984132", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-03-03T00:00:00Z", "endDate": "2025-03-09T00:00:00Z", "submitByDate": "2025-03-04T00:00:00Z", "checkDate": "2025-03-05T00:00:00Z", "checkCount": 1, "id": "7169daa9-6e7f-4cdf-b179-b07ada2ddea2", "companyId": "18143909", "type": "payperiod", "_rid": "NmJkAKiCbEdCRAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdCRAEAAAAAAA==/", "_etag": "\"9e00e637-0000-0100-0000-686ffb7b0000\"", "_attachments": "attachments/", "_ts": 1752169339}, {"payPeriodId": "1090067526885120", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-03-10T00:00:00Z", "endDate": "2025-03-16T00:00:00Z", "submitByDate": "2025-03-11T00:00:00Z", "checkDate": "2025-03-12T00:00:00Z", "checkCount": 1, "id": "a15dc7dc-0e95-4f80-847f-c5588a23028b", "companyId": "18143909", "type": "payperiod", "_rid": "NmJkAKiCbEdDRAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdDRAEAAAAAAA==/", "_etag": "\"9e00e837-0000-0100-0000-686ffb7b0000\"", "_attachments": "attachments/", "_ts": 1752169339}, {"payPeriodId": "1090067695355956", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-03-17T00:00:00Z", "endDate": "2025-03-23T00:00:00Z", "submitByDate": "2025-03-18T00:00:00Z", "checkDate": "2025-03-19T00:00:00Z", "checkCount": 1, "id": "99cf1d93-ec06-47bb-8cfe-3a3e46211e46", "companyId": "18143909", "type": "payperiod", "_rid": "NmJkAKiCbEdERAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdERAEAAAAAAA==/", "_etag": "\"9e00eb37-0000-0100-0000-686ffb7b0000\"", "_attachments": "attachments/", "_ts": 1752169339}, {"payPeriodId": "1090068009767489", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-03-24T00:00:00Z", "endDate": "2025-03-30T00:00:00Z", "submitByDate": "2025-03-26T00:00:00Z", "checkDate": "2025-03-28T00:00:00Z", "checkCount": 1, "id": "f3c70a7d-acb3-42e8-95d3-43181e331e7a", "companyId": "18143909", "type": "payperiod", "_rid": "NmJkAKiCbEdFRAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdFRAEAAAAAAA==/", "_etag": "\"9e00ec37-0000-0100-0000-686ffb7b0000\"", "_attachments": "attachments/", "_ts": 1752169339}, {"payPeriodId": "1090068015188524", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-03-31T00:00:00Z", "endDate": "2025-04-06T00:00:00Z", "submitByDate": "2025-04-01T00:00:00Z", "checkDate": "2025-04-02T00:00:00Z", "checkCount": 0, "id": "88780c9c-c636-4110-a74f-245d1553b0f1", "companyId": "18143909", "type": "payperiod", "_rid": "NmJkAKiCbEdGRAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdGRAEAAAAAAA==/", "_etag": "\"9e00ee37-0000-0100-0000-686ffb7b0000\"", "_attachments": "attachments/", "_ts": 1752169339}, {"payPeriodId": "1090068163456072", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-04-07T00:00:00Z", "endDate": "2025-04-13T00:00:00Z", "submitByDate": "2025-04-08T00:00:00Z", "checkDate": "2025-04-09T00:00:00Z", "checkCount": 0, "id": "fb53b918-6ccf-45cc-8bfb-b600fd194f15", "companyId": "18143909", "type": "payperiod", "_rid": "NmJkAKiCbEdHRAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdHRAEAAAAAAA==/", "_etag": "\"9e00f037-0000-0100-0000-686ffb7b0000\"", "_attachments": "attachments/", "_ts": 1752169339}, {"payPeriodId": "1090068323037662", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-04-14T00:00:00Z", "endDate": "2025-04-20T00:00:00Z", "submitByDate": "2025-04-15T00:00:00Z", "checkDate": "2025-04-16T00:00:00Z", "checkCount": 0, "id": "59412b86-b9b3-440b-b386-1b1a5362041a", "companyId": "18143909", "type": "payperiod", "_rid": "NmJkAKiCbEdIRAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdIRAEAAAAAAA==/", "_etag": "\"9e00f137-0000-0100-0000-686ffb7b0000\"", "_attachments": "attachments/", "_ts": 1752169339}, {"payPeriodId": "1090068475436336", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-04-21T00:00:00Z", "endDate": "2025-04-27T00:00:00Z", "submitByDate": "2025-04-22T00:00:00Z", "checkDate": "2025-04-23T00:00:00Z", "checkCount": 0, "id": "733b290b-794d-4573-9917-973e61c26864", "companyId": "18143909", "type": "payperiod", "_rid": "NmJkAKiCbEdJRAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdJRAEAAAAAAA==/", "_etag": "\"9e00f237-0000-0100-0000-686ffb7b0000\"", "_attachments": "attachments/", "_ts": 1752169339}, {"payPeriodId": "1090068617280592", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-04-28T00:00:00Z", "endDate": "2025-05-04T00:00:00Z", "submitByDate": "2025-04-29T00:00:00Z", "checkDate": "2025-04-30T00:00:00Z", "checkCount": 0, "id": "2cd3eff3-b57c-4e8c-b364-4a9fa241abdc", "companyId": "18143909", "type": "payperiod", "_rid": "NmJkAKiCbEdKRAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdKRAEAAAAAAA==/", "_etag": "\"9e00f337-0000-0100-0000-686ffb7c0000\"", "_attachments": "attachments/", "_ts": 1752169340}, {"payPeriodId": "1090069068278291", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-05-12T00:00:00Z", "endDate": "2025-05-18T00:00:00Z", "submitByDate": "2025-05-13T00:00:00Z", "checkDate": "2025-05-14T00:00:00Z", "checkCount": 0, "id": "0eb030f7-1a1f-4b34-8c8f-268e964fdf2c", "companyId": "18143909", "type": "payperiod", "_rid": "NmJkAKiCbEdLRAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdLRAEAAAAAAA==/", "_etag": "\"9e00f737-0000-0100-0000-686ffb7c0000\"", "_attachments": "attachments/", "_ts": 1752169340}, {"payPeriodId": "1090069203322485", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-05-19T00:00:00Z", "endDate": "2025-05-25T00:00:00Z", "submitByDate": "2025-05-20T00:00:00Z", "checkDate": "2025-05-21T00:00:00Z", "checkCount": 0, "id": "ed9021f7-bff2-4fc3-979c-89b08e409ff2", "companyId": "18143909", "type": "payperiod", "_rid": "NmJkAKiCbEdMRAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdMRAEAAAAAAA==/", "_etag": "\"9e00fc37-0000-0100-0000-686ffb7c0000\"", "_attachments": "attachments/", "_ts": 1752169340}, {"payPeriodId": "1090069373000499", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-05-26T00:00:00Z", "endDate": "2025-06-01T00:00:00Z", "submitByDate": "2025-05-27T00:00:00Z", "checkDate": "2025-05-28T00:00:00Z", "checkCount": 0, "id": "3a28728b-e53d-4e27-a33a-7f496275ce6d", "companyId": "18143909", "type": "payperiod", "_rid": "NmJkAKiCbEdNRAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdNRAEAAAAAAA==/", "_etag": "\"9e00ff37-0000-0100-0000-686ffb7c0000\"", "_attachments": "attachments/", "_ts": 1752169340}, {"payPeriodId": "1090069423393864", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-06-02T00:00:00Z", "endDate": "2025-06-08T00:00:00Z", "submitByDate": "2025-06-04T00:00:00Z", "checkDate": "2025-06-06T00:00:00Z", "checkCount": 0, "id": "d141dc10-b632-4fd6-8f1b-3828c960dfc5", "companyId": "18143909", "type": "payperiod", "_rid": "NmJkAKiCbEdORAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdORAEAAAAAAA==/", "_etag": "\"9e000238-0000-0100-0000-686ffb7c0000\"", "_attachments": "attachments/", "_ts": 1752169340}, {"payPeriodId": "1090069514803944", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-06-09T00:00:00Z", "endDate": "2025-06-15T00:00:00Z", "submitByDate": "2025-06-10T00:00:00Z", "checkDate": "2025-06-11T00:00:00Z", "checkCount": 0, "id": "78118ebc-9af6-4728-bdf6-f962b6e4f0b6", "companyId": "18143909", "type": "payperiod", "_rid": "NmJkAKiCbEdPRAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdPRAEAAAAAAA==/", "_etag": "\"9e000438-0000-0100-0000-686ffb7c0000\"", "_attachments": "attachments/", "_ts": 1752169340}, {"payPeriodId": "1090069679475517", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-06-16T00:00:00Z", "endDate": "2025-06-22T00:00:00Z", "submitByDate": "2025-06-17T00:00:00Z", "checkDate": "2025-06-18T00:00:00Z", "checkCount": 0, "id": "78912946-c566-4250-8a5c-364b2f3ade75", "companyId": "18143909", "type": "payperiod", "_rid": "NmJkAKiCbEdQRAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdQRAEAAAAAAA==/", "_etag": "\"9e000838-0000-0100-0000-686ffb7c0000\"", "_attachments": "attachments/", "_ts": 1752169340}, {"payPeriodId": "1090069830640898", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-06-23T00:00:00Z", "endDate": "2025-06-29T00:00:00Z", "submitByDate": "2025-06-24T00:00:00Z", "checkDate": "2025-06-25T00:00:00Z", "checkCount": 0, "id": "2c68e0af-794a-4307-b4c0-ecc3f8e610e4", "companyId": "18143909", "type": "payperiod", "_rid": "NmJkAKiCbEdRRAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdRRAEAAAAAAA==/", "_etag": "\"9e000b38-0000-0100-0000-686ffb7c0000\"", "_attachments": "attachments/", "_ts": 1752169340}, {"payPeriodId": "1090070005029130", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-06-30T00:00:00Z", "endDate": "2025-07-06T00:00:00Z", "submitByDate": "2025-07-01T00:00:00Z", "checkDate": "2025-07-03T00:00:00Z", "checkCount": 0, "id": "1b7f650e-dcc4-4a77-9f2b-e1098ad22dc4", "companyId": "18143909", "type": "payperiod", "_rid": "NmJkAKiCbEdSRAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdSRAEAAAAAAA==/", "_etag": "\"9e000d38-0000-0100-0000-686ffb7c0000\"", "_attachments": "attachments/", "_ts": 1752169340}, {"payPeriodId": "1090066800428490", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-02-03T00:00:00Z", "endDate": "2025-02-09T00:00:00Z", "submitByDate": "2025-02-05T00:00:00Z", "checkDate": "2025-02-07T00:00:00Z", "checkCount": 0, "id": "ba7eb198-84b1-48a3-8813-35232c3b8058", "companyId": "18143909", "type": "payperiod", "_rid": "NmJkAKiCbEdTRAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdTRAEAAAAAAA==/", "_etag": "\"9e001038-0000-0100-0000-686ffb7c0000\"", "_attachments": "attachments/", "_ts": 1752169340}, {"payPeriodId": "1090066947285414", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-02-10T00:00:00Z", "endDate": "2025-02-16T00:00:00Z", "submitByDate": "2025-02-12T00:00:00Z", "checkDate": "2025-02-14T00:00:00Z", "checkCount": 0, "id": "0137fc95-0986-4162-b4b0-40adb3ade6da", "companyId": "18143909", "type": "payperiod", "_rid": "NmJkAKiCbEdURAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdURAEAAAAAAA==/", "_etag": "\"9e001738-0000-0100-0000-686ffb7c0000\"", "_attachments": "attachments/", "_ts": 1752169340}, {"payPeriodId": "1090068910263784", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-05-05T00:00:00Z", "endDate": "2025-05-11T00:00:00Z", "submitByDate": "2025-05-07T00:00:00Z", "checkDate": "2025-05-09T00:00:00Z", "checkCount": 0, "id": "68cbb7a0-c776-4442-960a-b3818414c702", "companyId": "18143909", "type": "payperiod", "_rid": "NmJkAKiCbEdVRAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdVRAEAAAAAAA==/", "_etag": "\"9e001c38-0000-0100-0000-686ffb7c0000\"", "_attachments": "attachments/", "_ts": 1752169340}, {"payPeriodId": "1090070150115745", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-07-07T00:00:00Z", "endDate": "2025-07-13T00:00:00Z", "submitByDate": "2025-07-09T00:00:00Z", "checkDate": "2025-07-11T00:00:00Z", "checkCount": 0, "id": "03ebb531-2bf5-4cb5-a7d2-b25d89441ece", "companyId": "18143909", "type": "payperiod", "_rid": "NmJkAKiCbEdWRAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdWRAEAAAAAAA==/", "_etag": "\"9e002238-0000-0100-0000-686ffb7c0000\"", "_attachments": "attachments/", "_ts": 1752169340}, {"payPeriodId": "1090070305360542", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-07-14T00:00:00Z", "endDate": "2025-07-20T00:00:00Z", "submitByDate": "2025-07-16T00:00:00Z", "checkDate": "2025-07-18T00:00:00Z", "checkCount": 0, "id": "1c729d63-b611-4c76-93c4-47370f48ed69", "companyId": "18143909", "type": "payperiod", "_rid": "NmJkAKiCbEdXRAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdXRAEAAAAAAA==/", "_etag": "\"9e002638-0000-0100-0000-686ffb7d0000\"", "_attachments": "attachments/", "_ts": 1752169341}, {"payPeriodId": "1090070445527096", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-07-21T00:00:00Z", "endDate": "2025-07-27T00:00:00Z", "submitByDate": "2025-07-23T00:00:00Z", "checkDate": "2025-07-25T00:00:00Z", "checkCount": 0, "id": "ab1236d5-c508-4305-b0fd-d5a06447132e", "companyId": "18143909", "type": "payperiod", "_rid": "NmJkAKiCbEdYRAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdYRAEAAAAAAA==/", "_etag": "\"9e002b38-0000-0100-0000-686ffb7d0000\"", "_attachments": "attachments/", "_ts": 1752169341}, {"payPeriodId": "1090070611468783", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-07-28T00:00:00Z", "endDate": "2025-08-03T00:00:00Z", "submitByDate": "2025-07-30T00:00:00Z", "checkDate": "2025-08-01T00:00:00Z", "checkCount": 0, "id": "5d28fbc3-673b-432f-804e-0087cf9f6d76", "companyId": "18143909", "type": "payperiod", "_rid": "NmJkAKiCbEdZRAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdZRAEAAAAAAA==/", "_etag": "\"9e002e38-0000-0100-0000-686ffb7d0000\"", "_attachments": "attachments/", "_ts": 1752169341}, {"payPeriodId": "1090070902393919", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-08-04T00:00:00Z", "endDate": "2025-08-10T00:00:00Z", "submitByDate": "2025-08-06T00:00:00Z", "checkDate": "2025-08-08T00:00:00Z", "checkCount": 0, "id": "5ed414de-7a1c-4fb0-98d0-15c6580c9a82", "companyId": "18143909", "type": "payperiod", "_rid": "NmJkAKiCbEdaRAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdaRAEAAAAAAA==/", "_etag": "\"9e003438-0000-0100-0000-686ffb7d0000\"", "_attachments": "attachments/", "_ts": 1752169341}, {"payPeriodId": "1090070908998405", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-08-11T00:00:00Z", "endDate": "2025-08-17T00:00:00Z", "submitByDate": "2025-08-13T00:00:00Z", "checkDate": "2025-08-15T00:00:00Z", "checkCount": 0, "id": "1ef09fee-ab6f-414d-9931-b04ae2ba6745", "companyId": "18143909", "type": "payperiod", "_rid": "NmJkAKiCbEdbRAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdbRAEAAAAAAA==/", "_etag": "\"9e003a38-0000-0100-0000-686ffb7d0000\"", "_attachments": "attachments/", "_ts": 1752169341}, {"payPeriodId": "1090071061174847", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-08-18T00:00:00Z", "endDate": "2025-08-24T00:00:00Z", "submitByDate": "2025-08-20T00:00:00Z", "checkDate": "2025-08-22T00:00:00Z", "checkCount": 0, "id": "e2bde11d-66d1-4b3a-8936-635d30f2ac59", "companyId": "18143909", "type": "payperiod", "_rid": "NmJkAKiCbEdcRAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdcRAEAAAAAAA==/", "_etag": "\"9e003f38-0000-0100-0000-686ffb7d0000\"", "_attachments": "attachments/", "_ts": 1752169341}, {"payPeriodId": "1090071231835880", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-08-25T00:00:00Z", "endDate": "2025-08-31T00:00:00Z", "submitByDate": "2025-08-27T00:00:00Z", "checkDate": "2025-08-29T00:00:00Z", "checkCount": 0, "id": "3b0cf4c9-1eab-4f87-bcd1-a5bd453064d2", "companyId": "18143909", "type": "payperiod", "_rid": "NmJkAKiCbEddRAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEddRAEAAAAAAA==/", "_etag": "\"9e004138-0000-0100-0000-686ffb7d0000\"", "_attachments": "attachments/", "_ts": 1752169341}, {"payPeriodId": "1090071380993253", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-09-01T00:00:00Z", "endDate": "2025-09-07T00:00:00Z", "submitByDate": "2025-09-03T00:00:00Z", "checkDate": "2025-09-05T00:00:00Z", "checkCount": 0, "id": "d6920191-fed0-44ea-94f0-8e0b701b2707", "companyId": "18143909", "type": "payperiod", "_rid": "NmJkAKiCbEdeRAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdeRAEAAAAAAA==/", "_etag": "\"9e004538-0000-0100-0000-686ffb7d0000\"", "_attachments": "attachments/", "_ts": 1752169341}, {"payPeriodId": "1090071497970417", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-09-08T00:00:00Z", "endDate": "2025-09-14T00:00:00Z", "submitByDate": "2025-09-10T00:00:00Z", "checkDate": "2025-09-12T00:00:00Z", "checkCount": 0, "id": "808847f6-0917-468f-a65c-9f15af0a6bca", "companyId": "18143909", "type": "payperiod", "_rid": "NmJkAKiCbEdfRAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdfRAEAAAAAAA==/", "_etag": "\"9e004838-0000-0100-0000-686ffb7d0000\"", "_attachments": "attachments/", "_ts": 1752169341}, {"payPeriodId": "1090071693889437", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-09-15T00:00:00Z", "endDate": "2025-09-21T00:00:00Z", "submitByDate": "2025-09-17T00:00:00Z", "checkDate": "2025-09-19T00:00:00Z", "checkCount": 0, "id": "e88fecc0-7101-447b-b92c-1146dc5fb602", "companyId": "18143909", "type": "payperiod", "_rid": "NmJkAKiCbEdgRAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdgRAEAAAAAAA==/", "_etag": "\"9e004b38-0000-0100-0000-686ffb7d0000\"", "_attachments": "attachments/", "_ts": 1752169341}, {"payPeriodId": "1090071842151411", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-09-22T00:00:00Z", "endDate": "2025-09-28T00:00:00Z", "submitByDate": "2025-09-24T00:00:00Z", "checkDate": "2025-09-26T00:00:00Z", "checkCount": 0, "id": "feffe52e-6a5d-4b7d-9532-d12febfbb62f", "companyId": "18143909", "type": "payperiod", "_rid": "NmJkAKiCbEdhRAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdhRAEAAAAAAA==/", "_etag": "\"9e005138-0000-0100-0000-686ffb7d0000\"", "_attachments": "attachments/", "_ts": 1752169341}, {"payPeriodId": "1090072007835082", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-09-29T00:00:00Z", "endDate": "2025-10-05T00:00:00Z", "submitByDate": "2025-10-01T00:00:00Z", "checkDate": "2025-10-03T00:00:00Z", "checkCount": 0, "id": "348822b3-c62a-477e-9af1-a1497f593039", "companyId": "18143909", "type": "payperiod", "_rid": "NmJkAKiCbEdiRAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdiRAEAAAAAAA==/", "_etag": "\"9e005538-0000-0100-0000-686ffb7d0000\"", "_attachments": "attachments/", "_ts": 1752169341}, {"payPeriodId": "1090066038055673", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2024-12-30T00:00:00Z", "endDate": "2025-01-05T00:00:00Z", "submitByDate": "2024-12-31T00:00:00Z", "checkDate": "2025-01-03T00:00:00Z", "checkCount": 1, "id": "e8d42d4e-ae50-4bed-a51d-503a0b2f9992", "companyId": "18143909", "type": "payperiod", "_rid": "NmJkAKiCbEdlRAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdlRAEAAAAAAA==/", "_etag": "\"9e006238-0000-0100-0000-686ffb7e0000\"", "_attachments": "attachments/", "_ts": 1752169342}, {"payPeriodId": "1090066259071880", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-01-06T00:00:00Z", "endDate": "2025-01-12T00:00:00Z", "submitByDate": "2025-01-07T00:00:00Z", "checkDate": "2025-01-08T00:00:00Z", "checkCount": 1, "id": "8e3bab59-c7f1-4839-b255-bece8d6a43ee", "companyId": "18143909", "type": "payperiod", "_rid": "NmJkAKiCbEdmRAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdmRAEAAAAAAA==/", "_etag": "\"9e006a38-0000-0100-0000-686ffb7e0000\"", "_attachments": "attachments/", "_ts": 1752169342}, {"payPeriodId": "1090066339308360", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-01-13T00:00:00Z", "endDate": "2025-01-19T00:00:00Z", "submitByDate": "2025-01-15T00:00:00Z", "checkDate": "2025-01-16T00:00:00Z", "checkCount": 1, "id": "d61772a1-b687-4cb7-8d3b-7bc3984b0428", "companyId": "18143909", "type": "payperiod", "_rid": "NmJkAKiCbEdnRAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdnRAEAAAAAAA==/", "_etag": "\"9e007038-0000-0100-0000-686ffb7e0000\"", "_attachments": "attachments/", "_ts": 1752169342}, {"payPeriodId": "1090066476126862", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-01-20T00:00:00Z", "endDate": "2025-01-26T00:00:00Z", "submitByDate": "2025-01-21T00:00:00Z", "checkDate": "2025-01-22T00:00:00Z", "checkCount": 1, "id": "46524156-1bc0-4fb4-bee8-a4cee3418331", "companyId": "18143909", "type": "payperiod", "_rid": "NmJkAKiCbEdoRAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdoRAEAAAAAAA==/", "_etag": "\"9e007638-0000-0100-0000-686ffb7e0000\"", "_attachments": "attachments/", "_ts": 1752169342}, {"payPeriodId": "1090066648463141", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-01-27T00:00:00Z", "endDate": "2025-02-02T00:00:00Z", "submitByDate": "2025-01-29T00:00:00Z", "checkDate": "2025-01-31T00:00:00Z", "checkCount": 1, "id": "c087d68e-19d8-4784-bc61-522295d60ba2", "companyId": "18143909", "type": "payperiod", "_rid": "NmJkAKiCbEdpRAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdpRAEAAAAAAA==/", "_etag": "\"9e007e38-0000-0100-0000-686ffb7e0000\"", "_attachments": "attachments/", "_ts": 1752169342}, {"payPeriodId": "1090067067600674", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-02-17T00:00:00Z", "endDate": "2025-02-23T00:00:00Z", "submitByDate": "2025-02-19T00:00:00Z", "checkDate": "2025-02-21T00:00:00Z", "checkCount": 1, "id": "eb28c9d1-4c50-447e-b7f2-2a9a2332b738", "companyId": "18143909", "type": "payperiod", "_rid": "NmJkAKiCbEdqRAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdqRAEAAAAAAA==/", "_etag": "\"9e008138-0000-0100-0000-686ffb7e0000\"", "_attachments": "attachments/", "_ts": 1752169342}, {"payPeriodId": "1090069214844307", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-02-24T00:00:00Z", "endDate": "2025-03-02T00:00:00Z", "submitByDate": "2025-02-26T00:00:00Z", "checkDate": "2025-02-26T00:00:00Z", "checkCount": 1, "id": "61fefee2-35c5-4cd1-a2c5-115ed4d5c4d2", "companyId": "18143909", "type": "payperiod", "_rid": "NmJkAKiCbEdrRAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdrRAEAAAAAAA==/", "_etag": "\"9e008538-0000-0100-0000-686ffb7e0000\"", "_attachments": "attachments/", "_ts": 1752169342}, {"payPeriodId": "1090067384984132", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-03-03T00:00:00Z", "endDate": "2025-03-09T00:00:00Z", "submitByDate": "2025-03-04T00:00:00Z", "checkDate": "2025-03-05T00:00:00Z", "checkCount": 1, "id": "5e887d63-5a50-4c71-9ca1-2575aeda3058", "companyId": "18143909", "type": "payperiod", "_rid": "NmJkAKiCbEdsRAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdsRAEAAAAAAA==/", "_etag": "\"9e008738-0000-0100-0000-686ffb7e0000\"", "_attachments": "attachments/", "_ts": 1752169342}, {"payPeriodId": "1090067526885120", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-03-10T00:00:00Z", "endDate": "2025-03-16T00:00:00Z", "submitByDate": "2025-03-11T00:00:00Z", "checkDate": "2025-03-12T00:00:00Z", "checkCount": 1, "id": "a473a78b-ddb7-439b-9eb2-beef3ad00224", "companyId": "18143909", "type": "payperiod", "_rid": "NmJkAKiCbEdtRAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdtRAEAAAAAAA==/", "_etag": "\"9e008e38-0000-0100-0000-686ffb7e0000\"", "_attachments": "attachments/", "_ts": 1752169342}, {"payPeriodId": "1090067695355956", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-03-17T00:00:00Z", "endDate": "2025-03-23T00:00:00Z", "submitByDate": "2025-03-18T00:00:00Z", "checkDate": "2025-03-19T00:00:00Z", "checkCount": 1, "id": "302f54cd-b4f1-4630-a6cd-768eca47d7bc", "companyId": "18143909", "type": "payperiod", "_rid": "NmJkAKiCbEduRAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEduRAEAAAAAAA==/", "_etag": "\"9e009338-0000-0100-0000-686ffb7e0000\"", "_attachments": "attachments/", "_ts": 1752169342}, {"payPeriodId": "1090068009767489", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-03-24T00:00:00Z", "endDate": "2025-03-30T00:00:00Z", "submitByDate": "2025-03-26T00:00:00Z", "checkDate": "2025-03-28T00:00:00Z", "checkCount": 1, "id": "da60dcd3-03e1-4048-9bfa-ee3f74047391", "companyId": "18143909", "type": "payperiod", "_rid": "NmJkAKiCbEdvRAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdvRAEAAAAAAA==/", "_etag": "\"9e009538-0000-0100-0000-686ffb7e0000\"", "_attachments": "attachments/", "_ts": 1752169342}, {"payPeriodId": "1090068015188524", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-03-31T00:00:00Z", "endDate": "2025-04-06T00:00:00Z", "submitByDate": "2025-04-01T00:00:00Z", "checkDate": "2025-04-02T00:00:00Z", "checkCount": 1, "id": "eb1334c9-d761-4fe1-b1a5-8a74e1d93a57", "companyId": "18143909", "type": "payperiod", "_rid": "NmJkAKiCbEdwRAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdwRAEAAAAAAA==/", "_etag": "\"9e009a38-0000-0100-0000-686ffb7e0000\"", "_attachments": "attachments/", "_ts": 1752169342}, {"payPeriodId": "1090068163456072", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-04-07T00:00:00Z", "endDate": "2025-04-13T00:00:00Z", "submitByDate": "2025-04-08T00:00:00Z", "checkDate": "2025-04-09T00:00:00Z", "checkCount": 1, "id": "6c4036b9-7f42-4100-8c89-a6c5f8b317f2", "companyId": "18143909", "type": "payperiod", "_rid": "NmJkAKiCbEdxRAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdxRAEAAAAAAA==/", "_etag": "\"9e009f38-0000-0100-0000-686ffb7f0000\"", "_attachments": "attachments/", "_ts": 1752169343}, {"payPeriodId": "1090068323037662", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-04-14T00:00:00Z", "endDate": "2025-04-20T00:00:00Z", "submitByDate": "2025-04-15T00:00:00Z", "checkDate": "2025-04-16T00:00:00Z", "checkCount": 1, "id": "c1ca684c-0f60-438c-bed1-c5ea91c3b199", "companyId": "18143909", "type": "payperiod", "_rid": "NmJkAKiCbEdyRAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdyRAEAAAAAAA==/", "_etag": "\"9e00a438-0000-0100-0000-686ffb7f0000\"", "_attachments": "attachments/", "_ts": 1752169343}, {"payPeriodId": "1090068475436336", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-04-21T00:00:00Z", "endDate": "2025-04-27T00:00:00Z", "submitByDate": "2025-04-22T00:00:00Z", "checkDate": "2025-04-23T00:00:00Z", "checkCount": 1, "id": "4d245fa0-673e-4e1f-bcd3-14e88509fd94", "companyId": "18143909", "type": "payperiod", "_rid": "NmJkAKiCbEdzRAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdzRAEAAAAAAA==/", "_etag": "\"9e00a938-0000-0100-0000-686ffb7f0000\"", "_attachments": "attachments/", "_ts": 1752169343}, {"payPeriodId": "1090068617280592", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-04-28T00:00:00Z", "endDate": "2025-05-04T00:00:00Z", "submitByDate": "2025-04-29T00:00:00Z", "checkDate": "2025-04-30T00:00:00Z", "checkCount": 1, "id": "0749d29c-bbe0-41fd-828d-7d3729cfa0aa", "companyId": "18143909", "type": "payperiod", "_rid": "NmJkAKiCbEd0RAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEd0RAEAAAAAAA==/", "_etag": "\"9e00ac38-0000-0100-0000-686ffb7f0000\"", "_attachments": "attachments/", "_ts": 1752169343}, {"payPeriodId": "1090069068278291", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-05-12T00:00:00Z", "endDate": "2025-05-18T00:00:00Z", "submitByDate": "2025-05-13T00:00:00Z", "checkDate": "2025-05-14T00:00:00Z", "checkCount": 1, "id": "0df4bbf8-fff2-499f-9224-91dc7ded4120", "companyId": "18143909", "type": "payperiod", "_rid": "NmJkAKiCbEd1RAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEd1RAEAAAAAAA==/", "_etag": "\"9e00b038-0000-0100-0000-686ffb7f0000\"", "_attachments": "attachments/", "_ts": 1752169343}, {"payPeriodId": "1090069203322485", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-05-19T00:00:00Z", "endDate": "2025-05-25T00:00:00Z", "submitByDate": "2025-05-20T00:00:00Z", "checkDate": "2025-05-21T00:00:00Z", "checkCount": 1, "id": "cf1db706-5b18-4427-a574-768246897a13", "companyId": "18143909", "type": "payperiod", "_rid": "NmJkAKiCbEd2RAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEd2RAEAAAAAAA==/", "_etag": "\"9e00b638-0000-0100-0000-686ffb7f0000\"", "_attachments": "attachments/", "_ts": 1752169343}, {"payPeriodId": "1090069373000499", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-05-26T00:00:00Z", "endDate": "2025-06-01T00:00:00Z", "submitByDate": "2025-05-27T00:00:00Z", "checkDate": "2025-05-28T00:00:00Z", "checkCount": 1, "id": "c92d27ca-24ef-4127-afe5-3e82f7fd9709", "companyId": "18143909", "type": "payperiod", "_rid": "NmJkAKiCbEd3RAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEd3RAEAAAAAAA==/", "_etag": "\"9e00bb38-0000-0100-0000-686ffb7f0000\"", "_attachments": "attachments/", "_ts": 1752169343}, {"payPeriodId": "1090069423393864", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-06-02T00:00:00Z", "endDate": "2025-06-08T00:00:00Z", "submitByDate": "2025-06-04T00:00:00Z", "checkDate": "2025-06-06T00:00:00Z", "checkCount": 1, "id": "5e4b6c4b-ff14-464b-ac8e-c9ad6c159140", "companyId": "18143909", "type": "payperiod", "_rid": "NmJkAKiCbEd4RAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEd4RAEAAAAAAA==/", "_etag": "\"9e00be38-0000-0100-0000-686ffb7f0000\"", "_attachments": "attachments/", "_ts": 1752169343}, {"payPeriodId": "1090069514803944", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-06-09T00:00:00Z", "endDate": "2025-06-15T00:00:00Z", "submitByDate": "2025-06-10T00:00:00Z", "checkDate": "2025-06-11T00:00:00Z", "checkCount": 1, "id": "214b6c3e-bb70-4570-9daa-ca6154defb86", "companyId": "18143909", "type": "payperiod", "_rid": "NmJkAKiCbEd5RAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEd5RAEAAAAAAA==/", "_etag": "\"9e00c238-0000-0100-0000-686ffb7f0000\"", "_attachments": "attachments/", "_ts": 1752169343}, {"payPeriodId": "1090069679475517", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-06-16T00:00:00Z", "endDate": "2025-06-22T00:00:00Z", "submitByDate": "2025-06-17T00:00:00Z", "checkDate": "2025-06-18T00:00:00Z", "checkCount": 1, "id": "d7af2306-16dc-4b88-a1f2-b8c34035ce95", "companyId": "18143909", "type": "payperiod", "_rid": "NmJkAKiCbEd6RAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEd6RAEAAAAAAA==/", "_etag": "\"9e00c538-0000-0100-0000-686ffb7f0000\"", "_attachments": "attachments/", "_ts": 1752169343}, {"payPeriodId": "1090069830640898", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-06-23T00:00:00Z", "endDate": "2025-06-29T00:00:00Z", "submitByDate": "2025-06-24T00:00:00Z", "checkDate": "2025-06-25T00:00:00Z", "checkCount": 1, "id": "9dc43459-bd24-4478-827c-29ad591bef68", "companyId": "18143909", "type": "payperiod", "_rid": "NmJkAKiCbEd7RAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEd7RAEAAAAAAA==/", "_etag": "\"9e00c638-0000-0100-0000-686ffb7f0000\"", "_attachments": "attachments/", "_ts": 1752169343}, {"payPeriodId": "1090070005029130", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-06-30T00:00:00Z", "endDate": "2025-07-06T00:00:00Z", "submitByDate": "2025-07-01T00:00:00Z", "checkDate": "2025-07-03T00:00:00Z", "checkCount": 1, "id": "a05d33bf-30c2-4e63-b550-7c2bc0e6ea48", "companyId": "18143909", "type": "payperiod", "_rid": "NmJkAKiCbEd8RAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEd8RAEAAAAAAA==/", "_etag": "\"9e00cc38-0000-0100-0000-686ffb7f0000\"", "_attachments": "attachments/", "_ts": 1752169343}, {"payPeriodId": "1090066800428490", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-02-03T00:00:00Z", "endDate": "2025-02-09T00:00:00Z", "submitByDate": "2025-02-05T00:00:00Z", "checkDate": "2025-02-07T00:00:00Z", "checkCount": 0, "id": "303e9c82-a03e-49be-bd9b-7ddf97152c5c", "companyId": "18143909", "type": "payperiod", "_rid": "NmJkAKiCbEd9RAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEd9RAEAAAAAAA==/", "_etag": "\"9e00d138-0000-0100-0000-686ffb7f0000\"", "_attachments": "attachments/", "_ts": 1752169343}, {"payPeriodId": "1090066947285414", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-02-10T00:00:00Z", "endDate": "2025-02-16T00:00:00Z", "submitByDate": "2025-02-12T00:00:00Z", "checkDate": "2025-02-14T00:00:00Z", "checkCount": 0, "id": "2f9c9ff2-0a5a-48e0-9426-8765f2c427bf", "companyId": "18143909", "type": "payperiod", "_rid": "NmJkAKiCbEd+RAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEd+RAEAAAAAAA==/", "_etag": "\"9e00d338-0000-0100-0000-686ffb800000\"", "_attachments": "attachments/", "_ts": 1752169344}, {"payPeriodId": "1090068910263784", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-05-05T00:00:00Z", "endDate": "2025-05-11T00:00:00Z", "submitByDate": "2025-05-07T00:00:00Z", "checkDate": "2025-05-09T00:00:00Z", "checkCount": 0, "id": "19dd63b2-b7c6-4db2-968a-2f89b742b396", "companyId": "18143909", "type": "payperiod", "_rid": "NmJkAKiCbEd-RAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEd-RAEAAAAAAA==/", "_etag": "\"9e00d738-0000-0100-0000-686ffb800000\"", "_attachments": "attachments/", "_ts": 1752169344}, {"payPeriodId": "1090070150115745", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-07-07T00:00:00Z", "endDate": "2025-07-13T00:00:00Z", "submitByDate": "2025-07-09T00:00:00Z", "checkDate": "2025-07-11T00:00:00Z", "checkCount": 0, "id": "a1ccfba9-c86a-4b05-85d6-dd24f7cdb205", "companyId": "18143909", "type": "payperiod", "_rid": "NmJkAKiCbEeARAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeARAEAAAAAAA==/", "_etag": "\"9e00d938-0000-0100-0000-686ffb800000\"", "_attachments": "attachments/", "_ts": 1752169344}, {"payPeriodId": "1090070305360542", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-07-14T00:00:00Z", "endDate": "2025-07-20T00:00:00Z", "submitByDate": "2025-07-16T00:00:00Z", "checkDate": "2025-07-18T00:00:00Z", "checkCount": 0, "id": "fa33725b-23c3-4236-81b7-51f6f537233a", "companyId": "18143909", "type": "payperiod", "_rid": "NmJkAKiCbEeBRAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeBRAEAAAAAAA==/", "_etag": "\"9e00da38-0000-0100-0000-686ffb800000\"", "_attachments": "attachments/", "_ts": 1752169344}, {"payPeriodId": "1090070445527096", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-07-21T00:00:00Z", "endDate": "2025-07-27T00:00:00Z", "submitByDate": "2025-07-23T00:00:00Z", "checkDate": "2025-07-25T00:00:00Z", "checkCount": 0, "id": "89577a31-82d9-4018-82ca-621dc153fbfe", "companyId": "18143909", "type": "payperiod", "_rid": "NmJkAKiCbEeCRAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeCRAEAAAAAAA==/", "_etag": "\"9e00df38-0000-0100-0000-686ffb800000\"", "_attachments": "attachments/", "_ts": 1752169344}, {"payPeriodId": "1090070611468783", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-07-28T00:00:00Z", "endDate": "2025-08-03T00:00:00Z", "submitByDate": "2025-07-30T00:00:00Z", "checkDate": "2025-08-01T00:00:00Z", "checkCount": 0, "id": "1a3599bc-3bf7-4b75-b8f1-4504676b4bc5", "companyId": "18143909", "type": "payperiod", "_rid": "NmJkAKiCbEeDRAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeDRAEAAAAAAA==/", "_etag": "\"9e00e538-0000-0100-0000-686ffb800000\"", "_attachments": "attachments/", "_ts": 1752169344}, {"payPeriodId": "1090070902393919", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-08-04T00:00:00Z", "endDate": "2025-08-10T00:00:00Z", "submitByDate": "2025-08-06T00:00:00Z", "checkDate": "2025-08-08T00:00:00Z", "checkCount": 0, "id": "311c5f2c-0166-459d-998d-efd2d350cb84", "companyId": "18143909", "type": "payperiod", "_rid": "NmJkAKiCbEeERAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeERAEAAAAAAA==/", "_etag": "\"9e00e738-0000-0100-0000-686ffb800000\"", "_attachments": "attachments/", "_ts": 1752169344}, {"payPeriodId": "1090070908998405", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-08-11T00:00:00Z", "endDate": "2025-08-17T00:00:00Z", "submitByDate": "2025-08-13T00:00:00Z", "checkDate": "2025-08-15T00:00:00Z", "checkCount": 0, "id": "ab518826-fdeb-40ee-80f2-95eb5b3754e6", "companyId": "18143909", "type": "payperiod", "_rid": "NmJkAKiCbEeFRAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeFRAEAAAAAAA==/", "_etag": "\"9e00eb38-0000-0100-0000-686ffb800000\"", "_attachments": "attachments/", "_ts": 1752169344}, {"payPeriodId": "1090071061174847", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-08-18T00:00:00Z", "endDate": "2025-08-24T00:00:00Z", "submitByDate": "2025-08-20T00:00:00Z", "checkDate": "2025-08-22T00:00:00Z", "checkCount": 0, "id": "9a23aafa-0240-410f-93ea-22ef661fa607", "companyId": "18143909", "type": "payperiod", "_rid": "NmJkAKiCbEeGRAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeGRAEAAAAAAA==/", "_etag": "\"9e00ec38-0000-0100-0000-686ffb800000\"", "_attachments": "attachments/", "_ts": 1752169344}, {"payPeriodId": "1090071231835880", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-08-25T00:00:00Z", "endDate": "2025-08-31T00:00:00Z", "submitByDate": "2025-08-27T00:00:00Z", "checkDate": "2025-08-29T00:00:00Z", "checkCount": 0, "id": "c9dd3788-5b43-4361-838d-bd62784856d7", "companyId": "18143909", "type": "payperiod", "_rid": "NmJkAKiCbEeHRAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeHRAEAAAAAAA==/", "_etag": "\"9e00f038-0000-0100-0000-686ffb800000\"", "_attachments": "attachments/", "_ts": 1752169344}, {"payPeriodId": "1090071380993253", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-09-01T00:00:00Z", "endDate": "2025-09-07T00:00:00Z", "submitByDate": "2025-09-03T00:00:00Z", "checkDate": "2025-09-05T00:00:00Z", "checkCount": 0, "id": "d1361c56-00b9-4ad7-a634-08ec0a27fff3", "companyId": "18143909", "type": "payperiod", "_rid": "NmJkAKiCbEeIRAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeIRAEAAAAAAA==/", "_etag": "\"9e00f438-0000-0100-0000-686ffb800000\"", "_attachments": "attachments/", "_ts": 1752169344}, {"payPeriodId": "1090071497970417", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-09-08T00:00:00Z", "endDate": "2025-09-14T00:00:00Z", "submitByDate": "2025-09-10T00:00:00Z", "checkDate": "2025-09-12T00:00:00Z", "checkCount": 0, "id": "daaf0c20-68a2-4f0b-ba5c-c091a18a3146", "companyId": "18143909", "type": "payperiod", "_rid": "NmJkAKiCbEeJRAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeJRAEAAAAAAA==/", "_etag": "\"9e00f638-0000-0100-0000-686ffb800000\"", "_attachments": "attachments/", "_ts": 1752169344}, {"payPeriodId": "1090071693889437", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-09-15T00:00:00Z", "endDate": "2025-09-21T00:00:00Z", "submitByDate": "2025-09-17T00:00:00Z", "checkDate": "2025-09-19T00:00:00Z", "checkCount": 0, "id": "dbcc39de-a18e-4356-b840-3031fbf67bfd", "companyId": "18143909", "type": "payperiod", "_rid": "NmJkAKiCbEeKRAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeKRAEAAAAAAA==/", "_etag": "\"9e00f838-0000-0100-0000-686ffb810000\"", "_attachments": "attachments/", "_ts": 1752169345}, {"payPeriodId": "1090071842151411", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-09-22T00:00:00Z", "endDate": "2025-09-28T00:00:00Z", "submitByDate": "2025-09-24T00:00:00Z", "checkDate": "2025-09-26T00:00:00Z", "checkCount": 0, "id": "93403c6d-3c7b-4d74-aee5-1d2104de9867", "companyId": "18143909", "type": "payperiod", "_rid": "NmJkAKiCbEeLRAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeLRAEAAAAAAA==/", "_etag": "\"9e00fb38-0000-0100-0000-686ffb810000\"", "_attachments": "attachments/", "_ts": 1752169345}, {"payPeriodId": "1090072007835082", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-09-29T00:00:00Z", "endDate": "2025-10-05T00:00:00Z", "submitByDate": "2025-10-01T00:00:00Z", "checkDate": "2025-10-03T00:00:00Z", "checkCount": 0, "id": "04037a1d-2a98-4708-ac73-496660a9440e", "companyId": "18143909", "type": "payperiod", "_rid": "NmJkAKiCbEeMRAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeMRAEAAAAAAA==/", "_etag": "\"9e00fd38-0000-0100-0000-686ffb810000\"", "_attachments": "attachments/", "_ts": 1752169345}], "links": [{"rel": "self", "href": "https://ca-payroll-ai-mock-sb-001-secure.nicebeach-8a7a52ab.eastus.azurecontainerapps.io/ca/companies/18143909/payperiods"}]}, "status_code": 200}