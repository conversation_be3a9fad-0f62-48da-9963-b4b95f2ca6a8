{"success": true, "company_id": "17076188", "data": {"metadata": {"contentItemCount": 40}, "content": [{"payPeriodId": "1090068299538483", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2024-12-29T00:00:00Z", "endDate": "2025-01-11T00:00:00Z", "submitByDate": "2025-01-15T00:00:00Z", "checkDate": "2025-01-17T00:00:00Z", "checkCount": 1, "id": "2fdfd84f-cda1-4a56-97fa-3bb12b898c26", "companyId": "17076188", "type": "payperiod", "_rid": "NmJkAKiCbEefcQUAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEefcQUAAAAAAA==/", "_etag": "\"aa00dcdb-0000-0100-0000-687055ca0000\"", "_attachments": "attachments/", "_ts": 1752192458}, {"payPeriodId": "1090068299538486", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-01-12T00:00:00Z", "endDate": "2025-01-25T00:00:00Z", "submitByDate": "2025-01-29T00:00:00Z", "checkDate": "2025-01-31T00:00:00Z", "checkCount": 1, "id": "799fe7e4-337d-48ce-8d90-ff586d65cab9", "companyId": "17076188", "type": "payperiod", "_rid": "NmJkAKiCbEegcQUAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEegcQUAAAAAAA==/", "_etag": "\"aa00dddb-0000-0100-0000-687055ca0000\"", "_attachments": "attachments/", "_ts": 1752192458}, {"payPeriodId": "1090068299538489", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-01-26T00:00:00Z", "endDate": "2025-02-08T00:00:00Z", "submitByDate": "2025-02-12T00:00:00Z", "checkDate": "2025-02-14T00:00:00Z", "checkCount": 1, "id": "ea024f7b-0116-49db-9514-44c8bdc19ae7", "companyId": "17076188", "type": "payperiod", "_rid": "NmJkAKiCbEehcQUAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEehcQUAAAAAAA==/", "_etag": "\"aa00dedb-0000-0100-0000-687055ca0000\"", "_attachments": "attachments/", "_ts": 1752192458}, {"payPeriodId": "1090068299538492", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-02-09T00:00:00Z", "endDate": "2025-02-22T00:00:00Z", "submitByDate": "2025-02-26T00:00:00Z", "checkDate": "2025-02-28T00:00:00Z", "checkCount": 1, "id": "3c0f8823-5063-45c4-bdfa-e064e1255caf", "companyId": "17076188", "type": "payperiod", "_rid": "NmJkAKiCbEeicQUAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeicQUAAAAAAA==/", "_etag": "\"aa00e1db-0000-0100-0000-687055ca0000\"", "_attachments": "attachments/", "_ts": 1752192458}, {"payPeriodId": "1090068299538495", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-02-23T00:00:00Z", "endDate": "2025-03-08T00:00:00Z", "submitByDate": "2025-03-12T00:00:00Z", "checkDate": "2025-03-14T00:00:00Z", "checkCount": 1, "id": "aa413ffd-3b7c-4e47-a29b-314a580cf633", "companyId": "17076188", "type": "payperiod", "_rid": "NmJkAKiCbEejcQUAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEejcQUAAAAAAA==/", "_etag": "\"aa00e7db-0000-0100-0000-687055ca0000\"", "_attachments": "attachments/", "_ts": 1752192458}, {"payPeriodId": "1090068299538498", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-03-09T00:00:00Z", "endDate": "2025-03-22T00:00:00Z", "submitByDate": "2025-03-26T00:00:00Z", "checkDate": "2025-03-28T00:00:00Z", "checkCount": 1, "id": "a4b8c817-2832-4044-98f6-0ca9b0af9f96", "companyId": "17076188", "type": "payperiod", "_rid": "NmJkAKiCbEekcQUAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEekcQUAAAAAAA==/", "_etag": "\"aa00eadb-0000-0100-0000-687055ca0000\"", "_attachments": "attachments/", "_ts": 1752192458}, {"payPeriodId": "1090068299538501", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-03-23T00:00:00Z", "endDate": "2025-04-05T00:00:00Z", "submitByDate": "2025-04-09T00:00:00Z", "checkDate": "2025-04-11T00:00:00Z", "checkCount": 0, "id": "d52a611e-3865-4aaa-87cb-fbfb6f4d5765", "companyId": "17076188", "type": "payperiod", "_rid": "NmJkAKiCbEelcQUAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEelcQUAAAAAAA==/", "_etag": "\"aa00ecdb-0000-0100-0000-687055cb0000\"", "_attachments": "attachments/", "_ts": 1752192459}, {"payPeriodId": "1090068345115562", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-04-06T00:00:00Z", "endDate": "2025-04-18T00:00:00Z", "submitByDate": "2025-04-23T00:00:00Z", "checkDate": "2025-04-25T00:00:00Z", "checkCount": 0, "id": "b7a71cdb-8997-4294-b931-0127d84b2328", "companyId": "17076188", "type": "payperiod", "_rid": "NmJkAKiCbEemcQUAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEemcQUAAAAAAA==/", "_etag": "\"aa00efdb-0000-0100-0000-687055cb0000\"", "_attachments": "attachments/", "_ts": 1752192459}, {"payPeriodId": "1090068607608448", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-04-20T00:00:00Z", "endDate": "2025-05-03T00:00:00Z", "submitByDate": "2025-05-07T00:00:00Z", "checkDate": "2025-05-09T00:00:00Z", "checkCount": 0, "id": "4b867d42-c268-4879-bf38-654167814c61", "companyId": "17076188", "type": "payperiod", "_rid": "NmJkAKiCbEencQUAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEencQUAAAAAAA==/", "_etag": "\"aa00f0db-0000-0100-0000-687055cb0000\"", "_attachments": "attachments/", "_ts": 1752192459}, {"payPeriodId": "1090068908913184", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-05-04T00:00:00Z", "endDate": "2025-05-17T00:00:00Z", "submitByDate": "2025-05-21T00:00:00Z", "checkDate": "2025-05-23T00:00:00Z", "checkCount": 0, "id": "11609747-9449-47c8-a996-2b3bd6d976b3", "companyId": "17076188", "type": "payperiod", "_rid": "NmJkAKiCbEeocQUAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeocQUAAAAAAA==/", "_etag": "\"aa00f1db-0000-0100-0000-687055cb0000\"", "_attachments": "attachments/", "_ts": 1752192459}, {"payPeriodId": "1090069211082682", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-05-18T00:00:00Z", "endDate": "2025-05-31T00:00:00Z", "submitByDate": "2025-06-04T00:00:00Z", "checkDate": "2025-06-06T00:00:00Z", "checkCount": 0, "id": "d7fab4b5-91d1-4bf0-88d7-bd9ee02846cf", "companyId": "17076188", "type": "payperiod", "_rid": "NmJkAKiCbEepcQUAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEepcQUAAAAAAA==/", "_etag": "\"aa00f2db-0000-0100-0000-687055cb0000\"", "_attachments": "attachments/", "_ts": 1752192459}, {"payPeriodId": "1090069518326944", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-06-01T00:00:00Z", "endDate": "2025-06-14T00:00:00Z", "submitByDate": "2025-06-18T00:00:00Z", "checkDate": "2025-06-20T00:00:00Z", "checkCount": 0, "id": "0b54b722-d775-4217-91fa-63d6e8bc162b", "companyId": "17076188", "type": "payperiod", "_rid": "NmJkAKiCbEeqcQUAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeqcQUAAAAAAA==/", "_etag": "\"aa00f3db-0000-0100-0000-687055cb0000\"", "_attachments": "attachments/", "_ts": 1752192459}, {"payPeriodId": "1090069819358230", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-06-15T00:00:00Z", "endDate": "2025-06-28T00:00:00Z", "submitByDate": "2025-07-01T00:00:00Z", "checkDate": "2025-07-03T00:00:00Z", "checkCount": 0, "id": "cd29b043-6584-4c80-b2db-781f128ff1bb", "companyId": "17076188", "type": "payperiod", "_rid": "NmJkAKiCbEercQUAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEercQUAAAAAAA==/", "_etag": "\"aa00f5db-0000-0100-0000-687055cb0000\"", "_attachments": "attachments/", "_ts": 1752192459}, {"payPeriodId": "1090070130254554", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-06-29T00:00:00Z", "endDate": "2025-07-12T00:00:00Z", "submitByDate": "2025-07-16T00:00:00Z", "checkDate": "2025-07-18T00:00:00Z", "checkCount": 0, "id": "0ae73723-8ef9-4979-ad39-e2439995371d", "companyId": "17076188", "type": "payperiod", "_rid": "NmJkAKiCbEescQUAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEescQUAAAAAAA==/", "_etag": "\"aa00f6db-0000-0100-0000-687055cb0000\"", "_attachments": "attachments/", "_ts": 1752192459}, {"payPeriodId": "1090070439408493", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-07-13T00:00:00Z", "endDate": "2025-07-26T00:00:00Z", "submitByDate": "2025-07-30T00:00:00Z", "checkDate": "2025-08-01T00:00:00Z", "checkCount": 0, "id": "2cf6d64e-2942-48f5-a361-17a4c6ae69fd", "companyId": "17076188", "type": "payperiod", "_rid": "NmJkAKiCbEetcQUAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEetcQUAAAAAAA==/", "_etag": "\"aa00f7db-0000-0100-0000-687055cb0000\"", "_attachments": "attachments/", "_ts": 1752192459}, {"payPeriodId": "1090070755297570", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-07-27T00:00:00Z", "endDate": "2025-08-09T00:00:00Z", "submitByDate": "2025-08-13T00:00:00Z", "checkDate": "2025-08-15T00:00:00Z", "checkCount": 0, "id": "0190778f-5519-49b7-9130-4a4e002f2890", "companyId": "17076188", "type": "payperiod", "_rid": "NmJkAKiCbEeucQUAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeucQUAAAAAAA==/", "_etag": "\"aa00f8db-0000-0100-0000-687055cb0000\"", "_attachments": "attachments/", "_ts": 1752192459}, {"payPeriodId": "1090071064705876", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-08-10T00:00:00Z", "endDate": "2025-08-23T00:00:00Z", "submitByDate": "2025-08-27T00:00:00Z", "checkDate": "2025-08-29T00:00:00Z", "checkCount": 0, "id": "99425099-5bab-4fec-a519-a725930004c2", "companyId": "17076188", "type": "payperiod", "_rid": "NmJkAKiCbEevcQUAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEevcQUAAAAAAA==/", "_etag": "\"aa00f9db-0000-0100-0000-687055cb0000\"", "_attachments": "attachments/", "_ts": 1752192459}, {"payPeriodId": "1090071375091555", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-08-24T00:00:00Z", "endDate": "2025-09-06T00:00:00Z", "submitByDate": "2025-09-10T00:00:00Z", "checkDate": "2025-09-12T00:00:00Z", "checkCount": 0, "id": "45912ecf-25f4-414a-937f-428ee7f927eb", "companyId": "17076188", "type": "payperiod", "_rid": "NmJkAKiCbEewcQUAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEewcQUAAAAAAA==/", "_etag": "\"aa00fedb-0000-0100-0000-687055cb0000\"", "_attachments": "attachments/", "_ts": 1752192459}, {"payPeriodId": "1090071689515621", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-09-07T00:00:00Z", "endDate": "2025-09-20T00:00:00Z", "submitByDate": "2025-09-24T00:00:00Z", "checkDate": "2025-09-26T00:00:00Z", "checkCount": 0, "id": "472e1a80-80c2-49e6-a18c-15cc63c2f6e8", "companyId": "17076188", "type": "payperiod", "_rid": "NmJkAKiCbEexcQUAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEexcQUAAAAAAA==/", "_etag": "\"aa0005dc-0000-0100-0000-687055cb0000\"", "_attachments": "attachments/", "_ts": 1752192459}, {"payPeriodId": "1090072003715462", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-09-21T00:00:00Z", "endDate": "2025-10-04T00:00:00Z", "submitByDate": "2025-10-08T00:00:00Z", "checkDate": "2025-10-10T00:00:00Z", "checkCount": 0, "id": "6c195b4e-5cc5-457b-98ca-3b0ae3d2d959", "companyId": "17076188", "type": "payperiod", "_rid": "NmJkAKiCbEeycQUAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeycQUAAAAAAA==/", "_etag": "\"aa0006dc-0000-0100-0000-687055cb0000\"", "_attachments": "attachments/", "_ts": 1752192459}, {"payPeriodId": "1090068299538483", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2024-12-29T00:00:00Z", "endDate": "2025-01-11T00:00:00Z", "submitByDate": "2025-01-15T00:00:00Z", "checkDate": "2025-01-17T00:00:00Z", "checkCount": 1, "id": "d1a79cb8-c6f9-4ba2-93bc-1cc73440f579", "companyId": "17076188", "type": "payperiod", "_rid": "NmJkAKiCbEe3cQUAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEe3cQUAAAAAAA==/", "_etag": "\"aa000ddc-0000-0100-0000-687055cc0000\"", "_attachments": "attachments/", "_ts": 1752192460}, {"payPeriodId": "1090068299538486", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-01-12T00:00:00Z", "endDate": "2025-01-25T00:00:00Z", "submitByDate": "2025-01-29T00:00:00Z", "checkDate": "2025-01-31T00:00:00Z", "checkCount": 1, "id": "4a3d9a74-f688-435c-ad7c-186d724a332c", "companyId": "17076188", "type": "payperiod", "_rid": "NmJkAKiCbEe4cQUAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEe4cQUAAAAAAA==/", "_etag": "\"aa000fdc-0000-0100-0000-687055cc0000\"", "_attachments": "attachments/", "_ts": 1752192460}, {"payPeriodId": "1090068299538489", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-01-26T00:00:00Z", "endDate": "2025-02-08T00:00:00Z", "submitByDate": "2025-02-12T00:00:00Z", "checkDate": "2025-02-14T00:00:00Z", "checkCount": 1, "id": "7eeeed43-153e-43a7-9a04-b2e610d2601c", "companyId": "17076188", "type": "payperiod", "_rid": "NmJkAKiCbEe5cQUAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEe5cQUAAAAAAA==/", "_etag": "\"aa0010dc-0000-0100-0000-687055cc0000\"", "_attachments": "attachments/", "_ts": 1752192460}, {"payPeriodId": "1090068299538492", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-02-09T00:00:00Z", "endDate": "2025-02-22T00:00:00Z", "submitByDate": "2025-02-26T00:00:00Z", "checkDate": "2025-02-28T00:00:00Z", "checkCount": 1, "id": "e4197c9f-f860-4a49-bdd2-73b8d2fff8d7", "companyId": "17076188", "type": "payperiod", "_rid": "NmJkAKiCbEe6cQUAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEe6cQUAAAAAAA==/", "_etag": "\"aa0012dc-0000-0100-0000-687055cc0000\"", "_attachments": "attachments/", "_ts": 1752192460}, {"payPeriodId": "1090068299538495", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-02-23T00:00:00Z", "endDate": "2025-03-08T00:00:00Z", "submitByDate": "2025-03-12T00:00:00Z", "checkDate": "2025-03-14T00:00:00Z", "checkCount": 1, "id": "f96cba72-5142-4f17-91a3-b21d62863685", "companyId": "17076188", "type": "payperiod", "_rid": "NmJkAKiCbEe7cQUAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEe7cQUAAAAAAA==/", "_etag": "\"aa0015dc-0000-0100-0000-687055cc0000\"", "_attachments": "attachments/", "_ts": 1752192460}, {"payPeriodId": "1090068299538498", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-03-09T00:00:00Z", "endDate": "2025-03-22T00:00:00Z", "submitByDate": "2025-03-26T00:00:00Z", "checkDate": "2025-03-28T00:00:00Z", "checkCount": 1, "id": "bbf476d9-8cd6-4db3-bf63-e0ec8ed383c2", "companyId": "17076188", "type": "payperiod", "_rid": "NmJkAKiCbEe8cQUAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEe8cQUAAAAAAA==/", "_etag": "\"aa0016dc-0000-0100-0000-687055cc0000\"", "_attachments": "attachments/", "_ts": 1752192460}, {"payPeriodId": "1090068299538501", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-03-23T00:00:00Z", "endDate": "2025-04-05T00:00:00Z", "submitByDate": "2025-04-09T00:00:00Z", "checkDate": "2025-04-11T00:00:00Z", "checkCount": 1, "id": "5d2e4a7b-44b8-4d7d-a060-c92f45284fc9", "companyId": "17076188", "type": "payperiod", "_rid": "NmJkAKiCbEe9cQUAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEe9cQUAAAAAAA==/", "_etag": "\"aa0017dc-0000-0100-0000-687055cc0000\"", "_attachments": "attachments/", "_ts": 1752192460}, {"payPeriodId": "1090068345115562", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-04-06T00:00:00Z", "endDate": "2025-04-18T00:00:00Z", "submitByDate": "2025-04-23T00:00:00Z", "checkDate": "2025-04-25T00:00:00Z", "checkCount": 1, "id": "a5aa243c-321f-431c-be88-7877ad9ea481", "companyId": "17076188", "type": "payperiod", "_rid": "NmJkAKiCbEe+cQUAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEe+cQUAAAAAAA==/", "_etag": "\"aa0018dc-0000-0100-0000-687055cc0000\"", "_attachments": "attachments/", "_ts": 1752192460}, {"payPeriodId": "1090068607608448", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-04-20T00:00:00Z", "endDate": "2025-05-03T00:00:00Z", "submitByDate": "2025-05-07T00:00:00Z", "checkDate": "2025-05-09T00:00:00Z", "checkCount": 1, "id": "98fae9c0-3f82-4d73-a977-2d5549ede796", "companyId": "17076188", "type": "payperiod", "_rid": "NmJkAKiCbEe-cQUAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEe-cQUAAAAAAA==/", "_etag": "\"aa001adc-0000-0100-0000-687055cc0000\"", "_attachments": "attachments/", "_ts": 1752192460}, {"payPeriodId": "1090068908913184", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-05-04T00:00:00Z", "endDate": "2025-05-17T00:00:00Z", "submitByDate": "2025-05-21T00:00:00Z", "checkDate": "2025-05-23T00:00:00Z", "checkCount": 1, "id": "e38e4a01-d8d0-492e-b805-f60f72f03ef3", "companyId": "17076188", "type": "payperiod", "_rid": "NmJkAKiCbEfAcQUAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfAcQUAAAAAAA==/", "_etag": "\"aa001bdc-0000-0100-0000-687055cd0000\"", "_attachments": "attachments/", "_ts": 1752192461}, {"payPeriodId": "1090069211082682", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-05-18T00:00:00Z", "endDate": "2025-05-31T00:00:00Z", "submitByDate": "2025-06-04T00:00:00Z", "checkDate": "2025-06-06T00:00:00Z", "checkCount": 1, "id": "6c9498b7-71b8-4716-aaa6-c8a3a7cd283b", "companyId": "17076188", "type": "payperiod", "_rid": "NmJkAKiCbEfBcQUAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfBcQUAAAAAAA==/", "_etag": "\"aa001cdc-0000-0100-0000-687055cd0000\"", "_attachments": "attachments/", "_ts": 1752192461}, {"payPeriodId": "1090069518326944", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-06-01T00:00:00Z", "endDate": "2025-06-14T00:00:00Z", "submitByDate": "2025-06-18T00:00:00Z", "checkDate": "2025-06-20T00:00:00Z", "checkCount": 1, "id": "0dd2ccde-f8c2-412e-b2f2-fd527ddbc0f6", "companyId": "17076188", "type": "payperiod", "_rid": "NmJkAKiCbEfCcQUAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfCcQUAAAAAAA==/", "_etag": "\"aa001ddc-0000-0100-0000-687055cd0000\"", "_attachments": "attachments/", "_ts": 1752192461}, {"payPeriodId": "1090069819358230", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-06-15T00:00:00Z", "endDate": "2025-06-28T00:00:00Z", "submitByDate": "2025-07-01T00:00:00Z", "checkDate": "2025-07-03T00:00:00Z", "checkCount": 1, "id": "dd2517d5-23ef-43de-833a-70d3e3e1dae9", "companyId": "17076188", "type": "payperiod", "_rid": "NmJkAKiCbEfDcQUAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfDcQUAAAAAAA==/", "_etag": "\"aa001fdc-0000-0100-0000-687055cd0000\"", "_attachments": "attachments/", "_ts": 1752192461}, {"payPeriodId": "1090070130254554", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-06-29T00:00:00Z", "endDate": "2025-07-12T00:00:00Z", "submitByDate": "2025-07-16T00:00:00Z", "checkDate": "2025-07-18T00:00:00Z", "checkCount": 0, "id": "8b1048bd-d721-40be-8c52-00733cda667f", "companyId": "17076188", "type": "payperiod", "_rid": "NmJkAKiCbEfEcQUAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfEcQUAAAAAAA==/", "_etag": "\"aa0020dc-0000-0100-0000-687055cd0000\"", "_attachments": "attachments/", "_ts": 1752192461}, {"payPeriodId": "1090070439408493", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-07-13T00:00:00Z", "endDate": "2025-07-26T00:00:00Z", "submitByDate": "2025-07-30T00:00:00Z", "checkDate": "2025-08-01T00:00:00Z", "checkCount": 0, "id": "489126eb-074a-4c81-90be-75973e71de3f", "companyId": "17076188", "type": "payperiod", "_rid": "NmJkAKiCbEfFcQUAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfFcQUAAAAAAA==/", "_etag": "\"aa0021dc-0000-0100-0000-687055cd0000\"", "_attachments": "attachments/", "_ts": 1752192461}, {"payPeriodId": "1090070755297570", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-07-27T00:00:00Z", "endDate": "2025-08-09T00:00:00Z", "submitByDate": "2025-08-13T00:00:00Z", "checkDate": "2025-08-15T00:00:00Z", "checkCount": 0, "id": "9de5375c-1a1d-4538-b4a3-aa04ca353289", "companyId": "17076188", "type": "payperiod", "_rid": "NmJkAKiCbEfGcQUAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfGcQUAAAAAAA==/", "_etag": "\"aa0023dc-0000-0100-0000-687055cd0000\"", "_attachments": "attachments/", "_ts": 1752192461}, {"payPeriodId": "1090071064705876", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-08-10T00:00:00Z", "endDate": "2025-08-23T00:00:00Z", "submitByDate": "2025-08-27T00:00:00Z", "checkDate": "2025-08-29T00:00:00Z", "checkCount": 0, "id": "30bfe93d-5b90-4fbc-a002-67499c690d90", "companyId": "17076188", "type": "payperiod", "_rid": "NmJkAKiCbEfHcQUAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfHcQUAAAAAAA==/", "_etag": "\"aa0026dc-0000-0100-0000-687055cd0000\"", "_attachments": "attachments/", "_ts": 1752192461}, {"payPeriodId": "1090071375091555", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-08-24T00:00:00Z", "endDate": "2025-09-06T00:00:00Z", "submitByDate": "2025-09-10T00:00:00Z", "checkDate": "2025-09-12T00:00:00Z", "checkCount": 0, "id": "4c54e1ca-b05c-41e5-9348-dc0209529652", "companyId": "17076188", "type": "payperiod", "_rid": "NmJkAKiCbEfIcQUAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfIcQUAAAAAAA==/", "_etag": "\"aa002adc-0000-0100-0000-687055cd0000\"", "_attachments": "attachments/", "_ts": 1752192461}, {"payPeriodId": "1090071689515621", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-09-07T00:00:00Z", "endDate": "2025-09-20T00:00:00Z", "submitByDate": "2025-09-24T00:00:00Z", "checkDate": "2025-09-26T00:00:00Z", "checkCount": 0, "id": "2eecfb83-03ea-421e-a318-9f091aa1b52f", "companyId": "17076188", "type": "payperiod", "_rid": "NmJkAKiCbEfJcQUAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfJcQUAAAAAAA==/", "_etag": "\"aa002bdc-0000-0100-0000-687055cd0000\"", "_attachments": "attachments/", "_ts": 1752192461}, {"payPeriodId": "1090072003715462", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-09-21T00:00:00Z", "endDate": "2025-10-04T00:00:00Z", "submitByDate": "2025-10-08T00:00:00Z", "checkDate": "2025-10-10T00:00:00Z", "checkCount": 0, "id": "24c52654-3e75-4a4f-978b-8398c3aa4e68", "companyId": "17076188", "type": "payperiod", "_rid": "NmJkAKiCbEfKcQUAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfKcQUAAAAAAA==/", "_etag": "\"aa0031dc-0000-0100-0000-687055cd0000\"", "_attachments": "attachments/", "_ts": 1752192461}], "links": [{"rel": "self", "href": "https://ca-payroll-ai-mock-sb-001-secure.nicebeach-8a7a52ab.eastus.azurecontainerapps.io/ca/companies/17076188/payperiods"}]}, "status_code": 200}