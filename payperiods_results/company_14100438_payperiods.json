{"success": true, "company_id": "14100438", "data": {"metadata": {"contentItemCount": 168}, "content": [{"payPeriodId": "1090066289487240", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2024-12-29T00:00:00Z", "endDate": "2025-01-04T00:00:00Z", "submitByDate": "2025-01-08T00:00:00Z", "checkDate": "2025-01-10T00:00:00Z", "checkCount": 5, "id": "3d99f5de-e7fd-44bc-9110-6c7e9a1f1203", "companyId": "14100438", "type": "payperiod", "_rid": "NmJkAKiCbEc-WQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc-WQAAAAAAAA==/", "_etag": "\"98005238-0000-0100-0000-686fd5e20000\"", "_attachments": "attachments/", "_ts": 1752159714}, {"payPeriodId": "1090066289487241", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-01-05T00:00:00Z", "endDate": "2025-01-11T00:00:00Z", "submitByDate": "2025-01-15T00:00:00Z", "checkDate": "2025-01-17T00:00:00Z", "checkCount": 5, "id": "03e804ba-65ff-44ae-9bf2-444696fb1e32", "companyId": "14100438", "type": "payperiod", "_rid": "NmJkAKiCbEdAWQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdAWQAAAAAAAA==/", "_etag": "\"98005538-0000-0100-0000-686fd5e20000\"", "_attachments": "attachments/", "_ts": 1752159714}, {"payPeriodId": "1050103597057267", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-01-12T00:00:00Z", "endDate": "2025-01-18T00:00:00Z", "submitByDate": "2025-01-22T00:00:00Z", "checkDate": "2025-01-24T00:00:00Z", "checkCount": 6, "id": "a9b3fc65-84c1-42ea-adbf-26682b0cfe17", "companyId": "14100438", "type": "payperiod", "_rid": "NmJkAKiCbEdBWQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdBWQAAAAAAAA==/", "_etag": "\"98006238-0000-0100-0000-686fd5e20000\"", "_attachments": "attachments/", "_ts": 1752159714}, {"payPeriodId": "1050107004769802", "status": "COMPLETED", "description": "payroll correction", "startDate": "2025-01-12T00:00:00Z", "endDate": "2025-01-18T00:00:00Z", "submitByDate": "2025-01-23T00:00:00Z", "checkDate": "2025-01-24T00:00:00Z", "checkCount": 1, "id": "e8fbc761-7ec3-452b-a13e-8d0d4884bde6", "companyId": "14100438", "type": "payperiod", "_rid": "NmJkAKiCbEdCWQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdCWQAAAAAAAA==/", "_etag": "\"98006638-0000-0100-0000-686fd5e20000\"", "_attachments": "attachments/", "_ts": 1752159714}, {"payPeriodId": "1050103626234586", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-01-19T00:00:00Z", "endDate": "2025-01-25T00:00:00Z", "submitByDate": "2025-01-29T00:00:00Z", "checkDate": "2025-01-31T00:00:00Z", "checkCount": 5, "id": "ba8610ee-a032-47bb-86f7-ff6b9a869e04", "companyId": "14100438", "type": "payperiod", "_rid": "NmJkAKiCbEdDWQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdDWQAAAAAAAA==/", "_etag": "\"98006a38-0000-0100-0000-686fd5e20000\"", "_attachments": "attachments/", "_ts": 1752159714}, {"payPeriodId": "1050104053099845", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-01-26T00:00:00Z", "endDate": "2025-02-01T00:00:00Z", "submitByDate": "2025-02-05T00:00:00Z", "checkDate": "2025-02-07T00:00:00Z", "checkCount": 5, "id": "b7349321-f829-4c98-8479-20a532f5ee72", "companyId": "14100438", "type": "payperiod", "_rid": "NmJkAKiCbEdEWQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdEWQAAAAAAAA==/", "_etag": "\"98006e38-0000-0100-0000-686fd5e20000\"", "_attachments": "attachments/", "_ts": 1752159714}, {"payPeriodId": "1050104144656671", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-02-02T00:00:00Z", "endDate": "2025-02-08T00:00:00Z", "submitByDate": "2025-02-12T00:00:00Z", "checkDate": "2025-02-14T00:00:00Z", "checkCount": 5, "id": "366bacfa-150f-41d8-bf4b-5facae8930e8", "companyId": "14100438", "type": "payperiod", "_rid": "NmJkAKiCbEdFWQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdFWQAAAAAAAA==/", "_etag": "\"98007638-0000-0100-0000-686fd5e20000\"", "_attachments": "attachments/", "_ts": 1752159714}, {"payPeriodId": "1050104542114198", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-02-09T00:00:00Z", "endDate": "2025-02-15T00:00:00Z", "submitByDate": "2025-02-19T00:00:00Z", "checkDate": "2025-02-21T00:00:00Z", "checkCount": 5, "id": "f2fea826-19d2-45db-95c6-fad7b938636a", "companyId": "14100438", "type": "payperiod", "_rid": "NmJkAKiCbEdGWQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdGWQAAAAAAAA==/", "_etag": "\"98007d38-0000-0100-0000-686fd5e30000\"", "_attachments": "attachments/", "_ts": 1752159715}, {"payPeriodId": "1050104745588762", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-02-16T00:00:00Z", "endDate": "2025-02-22T00:00:00Z", "submitByDate": "2025-02-26T00:00:00Z", "checkDate": "2025-02-28T00:00:00Z", "checkCount": 5, "id": "d52cc7cc-fe7e-4c86-95d0-4cfe622c6c9e", "companyId": "14100438", "type": "payperiod", "_rid": "NmJkAKiCbEdHWQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdHWQAAAAAAAA==/", "_etag": "\"98008038-0000-0100-0000-686fd5e30000\"", "_attachments": "attachments/", "_ts": 1752159715}, {"payPeriodId": "1050104908177422", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-02-23T00:00:00Z", "endDate": "2025-03-01T00:00:00Z", "submitByDate": "2025-03-05T00:00:00Z", "checkDate": "2025-03-07T00:00:00Z", "checkCount": 5, "id": "d4a81a6b-9562-4d36-ab13-e56e01659081", "companyId": "14100438", "type": "payperiod", "_rid": "NmJkAKiCbEdIWQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdIWQAAAAAAAA==/", "_etag": "\"98008738-0000-0100-0000-686fd5e30000\"", "_attachments": "attachments/", "_ts": 1752159715}, {"payPeriodId": "1050105242086994", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-03-02T00:00:00Z", "endDate": "2025-03-08T00:00:00Z", "submitByDate": "2025-03-12T00:00:00Z", "checkDate": "2025-03-14T00:00:00Z", "checkCount": 5, "id": "b2bd0113-5af8-4d80-8022-d9a01357837e", "companyId": "14100438", "type": "payperiod", "_rid": "NmJkAKiCbEdJWQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdJWQAAAAAAAA==/", "_etag": "\"98008938-0000-0100-0000-686fd5e30000\"", "_attachments": "attachments/", "_ts": 1752159715}, {"payPeriodId": "1050105516922502", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-03-09T00:00:00Z", "endDate": "2025-03-15T00:00:00Z", "submitByDate": "2025-03-19T00:00:00Z", "checkDate": "2025-03-21T00:00:00Z", "checkCount": 5, "id": "18a012aa-b3cb-4639-865b-3d13d5ec0eee", "companyId": "14100438", "type": "payperiod", "_rid": "NmJkAKiCbEdKWQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdKWQAAAAAAAA==/", "_etag": "\"98008d38-0000-0100-0000-686fd5e30000\"", "_attachments": "attachments/", "_ts": 1752159715}, {"payPeriodId": "1050105776014225", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-03-16T00:00:00Z", "endDate": "2025-03-22T00:00:00Z", "submitByDate": "2025-03-26T00:00:00Z", "checkDate": "2025-03-28T00:00:00Z", "checkCount": 5, "id": "7e558823-da4d-4427-a53c-8168ccea15d0", "companyId": "14100438", "type": "payperiod", "_rid": "NmJkAKiCbEdLWQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdLWQAAAAAAAA==/", "_etag": "\"98009238-0000-0100-0000-686fd5e30000\"", "_attachments": "attachments/", "_ts": 1752159715}, {"payPeriodId": "1050105870703820", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-03-23T00:00:00Z", "endDate": "2025-03-29T00:00:00Z", "submitByDate": "2025-04-02T00:00:00Z", "checkDate": "2025-04-04T00:00:00Z", "checkCount": 0, "id": "d96c6c88-4371-4a61-a856-1e3c1ff87341", "companyId": "14100438", "type": "payperiod", "_rid": "NmJkAKiCbEdMWQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdMWQAAAAAAAA==/", "_etag": "\"98009438-0000-0100-0000-686fd5e30000\"", "_attachments": "attachments/", "_ts": 1752159715}, {"payPeriodId": "1050106248360341", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-03-30T00:00:00Z", "endDate": "2025-04-05T00:00:00Z", "submitByDate": "2025-04-09T00:00:00Z", "checkDate": "2025-04-11T00:00:00Z", "checkCount": 0, "id": "00e07582-5e6b-426e-8a73-1b5b0a8d90d6", "companyId": "14100438", "type": "payperiod", "_rid": "NmJkAKiCbEdNWQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdNWQAAAAAAAA==/", "_etag": "\"98009938-0000-0100-0000-686fd5e30000\"", "_attachments": "attachments/", "_ts": 1752159715}, {"payPeriodId": "1050106497036762", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-04-06T00:00:00Z", "endDate": "2025-04-12T00:00:00Z", "submitByDate": "2025-04-16T00:00:00Z", "checkDate": "2025-04-18T00:00:00Z", "checkCount": 0, "id": "5d5dad90-69fc-42a0-8783-90978fc69e07", "companyId": "14100438", "type": "payperiod", "_rid": "NmJkAKiCbEdOWQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdOWQAAAAAAAA==/", "_etag": "\"98009b38-0000-0100-0000-686fd5e30000\"", "_attachments": "attachments/", "_ts": 1752159715}, {"payPeriodId": "1050106754428367", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-04-13T00:00:00Z", "endDate": "2025-04-19T00:00:00Z", "submitByDate": "2025-04-23T00:00:00Z", "checkDate": "2025-04-25T00:00:00Z", "checkCount": 0, "id": "9b2e23cf-f9de-4c5a-913b-8f215b77c5ef", "companyId": "14100438", "type": "payperiod", "_rid": "NmJkAKiCbEdPWQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdPWQAAAAAAAA==/", "_etag": "\"98009d38-0000-0100-0000-686fd5e30000\"", "_attachments": "attachments/", "_ts": 1752159715}, {"payPeriodId": "1050106837646431", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-04-20T00:00:00Z", "endDate": "2025-04-26T00:00:00Z", "submitByDate": "2025-04-30T00:00:00Z", "checkDate": "2025-05-02T00:00:00Z", "checkCount": 0, "id": "52b88421-5e97-4a6a-81e2-4d9199b3fe67", "companyId": "14100438", "type": "payperiod", "_rid": "NmJkAKiCbEdQWQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdQWQAAAAAAAA==/", "_etag": "\"98009e38-0000-0100-0000-686fd5e30000\"", "_attachments": "attachments/", "_ts": 1752159715}, {"payPeriodId": "1050110393010694", "status": "INITIAL", "description": "correction", "startDate": "2025-04-20T00:00:00Z", "endDate": "2025-04-26T00:00:00Z", "submitByDate": "2025-05-01T00:00:00Z", "checkDate": "2025-05-02T00:00:00Z", "checkCount": 0, "id": "217251c6-ad9f-4277-b1e9-3f89c2b67bfc", "companyId": "14100438", "type": "payperiod", "_rid": "NmJkAKiCbEdRWQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdRWQAAAAAAAA==/", "_etag": "\"9800a438-0000-0100-0000-686fd5e30000\"", "_attachments": "attachments/", "_ts": 1752159715}, {"payPeriodId": "1050107249807616", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-04-27T00:00:00Z", "endDate": "2025-05-03T00:00:00Z", "submitByDate": "2025-05-07T00:00:00Z", "checkDate": "2025-05-09T00:00:00Z", "checkCount": 0, "id": "3b67c4d8-802c-4f75-91c6-a6a82744c5af", "companyId": "14100438", "type": "payperiod", "_rid": "NmJkAKiCbEdSWQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdSWQAAAAAAAA==/", "_etag": "\"9800ab38-0000-0100-0000-686fd5e30000\"", "_attachments": "attachments/", "_ts": 1752159715}, {"payPeriodId": "1050107283363063", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-05-04T00:00:00Z", "endDate": "2025-05-10T00:00:00Z", "submitByDate": "2025-05-14T00:00:00Z", "checkDate": "2025-05-16T00:00:00Z", "checkCount": 0, "id": "8587254c-67e4-4629-a759-fa21778af0a5", "companyId": "14100438", "type": "payperiod", "_rid": "NmJkAKiCbEdTWQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdTWQAAAAAAAA==/", "_etag": "\"9800b238-0000-0100-0000-686fd5e30000\"", "_attachments": "attachments/", "_ts": 1752159715}, {"payPeriodId": "1050107639742607", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-05-11T00:00:00Z", "endDate": "2025-05-17T00:00:00Z", "submitByDate": "2025-05-21T00:00:00Z", "checkDate": "2025-05-23T00:00:00Z", "checkCount": 0, "id": "65c40cb7-6497-40e3-9dcd-7cbdb263fef7", "companyId": "14100438", "type": "payperiod", "_rid": "NmJkAKiCbEdUWQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdUWQAAAAAAAA==/", "_etag": "\"9800b738-0000-0100-0000-686fd5e40000\"", "_attachments": "attachments/", "_ts": 1752159716}, {"payPeriodId": "1050107856369794", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-05-18T00:00:00Z", "endDate": "2025-05-24T00:00:00Z", "submitByDate": "2025-05-28T00:00:00Z", "checkDate": "2025-05-30T00:00:00Z", "checkCount": 0, "id": "ce7ac06d-be57-4ffd-bc7c-21e454c35618", "companyId": "14100438", "type": "payperiod", "_rid": "NmJkAKiCbEdVWQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdVWQAAAAAAAA==/", "_etag": "\"9800ba38-0000-0100-0000-686fd5e40000\"", "_attachments": "attachments/", "_ts": 1752159716}, {"payPeriodId": "1050108157540182", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-05-25T00:00:00Z", "endDate": "2025-05-31T00:00:00Z", "submitByDate": "2025-06-04T00:00:00Z", "checkDate": "2025-06-06T00:00:00Z", "checkCount": 0, "id": "7d9e6a6c-be84-4704-89ff-09fbc5fc9a8e", "companyId": "14100438", "type": "payperiod", "_rid": "NmJkAKiCbEdWWQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdWWQAAAAAAAA==/", "_etag": "\"9800be38-0000-0100-0000-686fd5e40000\"", "_attachments": "attachments/", "_ts": 1752159716}, {"payPeriodId": "1050108277749688", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-06-01T00:00:00Z", "endDate": "2025-06-07T00:00:00Z", "submitByDate": "2025-06-11T00:00:00Z", "checkDate": "2025-06-13T00:00:00Z", "checkCount": 0, "id": "fc890d47-357a-4c38-a17b-ff530dde9ff9", "companyId": "14100438", "type": "payperiod", "_rid": "NmJkAKiCbEdXWQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdXWQAAAAAAAA==/", "_etag": "\"9800c138-0000-0100-0000-686fd5e40000\"", "_attachments": "attachments/", "_ts": 1752159716}, {"payPeriodId": "1050108510193051", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-06-08T00:00:00Z", "endDate": "2025-06-14T00:00:00Z", "submitByDate": "2025-06-18T00:00:00Z", "checkDate": "2025-06-20T00:00:00Z", "checkCount": 0, "id": "803992d6-c595-43ca-9b39-0329cd26899f", "companyId": "14100438", "type": "payperiod", "_rid": "NmJkAKiCbEdYWQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdYWQAAAAAAAA==/", "_etag": "\"9800c438-0000-0100-0000-686fd5e40000\"", "_attachments": "attachments/", "_ts": 1752159716}, {"payPeriodId": "1050108807713788", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-06-15T00:00:00Z", "endDate": "2025-06-21T00:00:00Z", "submitByDate": "2025-06-25T00:00:00Z", "checkDate": "2025-06-27T00:00:00Z", "checkCount": 0, "id": "f93d29d0-57ae-449a-824e-e30c0b2b5581", "companyId": "14100438", "type": "payperiod", "_rid": "NmJkAKiCbEdZWQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdZWQAAAAAAAA==/", "_etag": "\"9800c638-0000-0100-0000-686fd5e40000\"", "_attachments": "attachments/", "_ts": 1752159716}, {"payPeriodId": "1050109102138266", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-06-22T00:00:00Z", "endDate": "2025-06-28T00:00:00Z", "submitByDate": "2025-07-01T00:00:00Z", "checkDate": "2025-07-03T00:00:00Z", "checkCount": 0, "id": "56266ff3-5b84-493a-8d54-adb03bf99cfe", "companyId": "14100438", "type": "payperiod", "_rid": "NmJkAKiCbEdaWQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdaWQAAAAAAAA==/", "_etag": "\"9800cf38-0000-0100-0000-686fd5e40000\"", "_attachments": "attachments/", "_ts": 1752159716}, {"payPeriodId": "1050109358095878", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-06-29T00:00:00Z", "endDate": "2025-07-05T00:00:00Z", "submitByDate": "2025-07-09T00:00:00Z", "checkDate": "2025-07-11T00:00:00Z", "checkCount": 0, "id": "310a4793-db8f-480a-b706-6b66df3e3b5e", "companyId": "14100438", "type": "payperiod", "_rid": "NmJkAKiCbEdbWQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdbWQAAAAAAAA==/", "_etag": "\"9800d838-0000-0100-0000-686fd5e40000\"", "_attachments": "attachments/", "_ts": 1752159716}, {"payPeriodId": "1050109592368855", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-07-06T00:00:00Z", "endDate": "2025-07-12T00:00:00Z", "submitByDate": "2025-07-16T00:00:00Z", "checkDate": "2025-07-18T00:00:00Z", "checkCount": 0, "id": "30aa686b-a497-4692-a2a5-9003f0763138", "companyId": "14100438", "type": "payperiod", "_rid": "NmJkAKiCbEdcWQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdcWQAAAAAAAA==/", "_etag": "\"9800d938-0000-0100-0000-686fd5e40000\"", "_attachments": "attachments/", "_ts": 1752159716}, {"payPeriodId": "1050109823056314", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-07-13T00:00:00Z", "endDate": "2025-07-19T00:00:00Z", "submitByDate": "2025-07-23T00:00:00Z", "checkDate": "2025-07-25T00:00:00Z", "checkCount": 0, "id": "ecc5c712-9584-4ad5-b513-74ff56bbfc49", "companyId": "14100438", "type": "payperiod", "_rid": "NmJkAKiCbEddWQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEddWQAAAAAAAA==/", "_etag": "\"9800da38-0000-0100-0000-686fd5e40000\"", "_attachments": "attachments/", "_ts": 1752159716}, {"payPeriodId": "1050110032614018", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-07-20T00:00:00Z", "endDate": "2025-07-26T00:00:00Z", "submitByDate": "2025-07-30T00:00:00Z", "checkDate": "2025-08-01T00:00:00Z", "checkCount": 0, "id": "28ca373b-27ae-40a7-862b-d77a93516ce2", "companyId": "14100438", "type": "payperiod", "_rid": "NmJkAKiCbEdeWQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdeWQAAAAAAAA==/", "_etag": "\"9800dd38-0000-0100-0000-686fd5e40000\"", "_attachments": "attachments/", "_ts": 1752159716}, {"payPeriodId": "1050110152016737", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-07-27T00:00:00Z", "endDate": "2025-08-02T00:00:00Z", "submitByDate": "2025-08-06T00:00:00Z", "checkDate": "2025-08-08T00:00:00Z", "checkCount": 0, "id": "c882ce69-1db4-4997-94a5-54c4203653bb", "companyId": "14100438", "type": "payperiod", "_rid": "NmJkAKiCbEdfWQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdfWQAAAAAAAA==/", "_etag": "\"9800e038-0000-0100-0000-686fd5e40000\"", "_attachments": "attachments/", "_ts": 1752159716}, {"payPeriodId": "1050110525635123", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-08-03T00:00:00Z", "endDate": "2025-08-09T00:00:00Z", "submitByDate": "2025-08-13T00:00:00Z", "checkDate": "2025-08-15T00:00:00Z", "checkCount": 0, "id": "4d499bc2-e9a4-45b0-bc95-1159626bcb96", "companyId": "14100438", "type": "payperiod", "_rid": "NmJkAKiCbEdgWQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdgWQAAAAAAAA==/", "_etag": "\"9800e538-0000-0100-0000-686fd5e40000\"", "_attachments": "attachments/", "_ts": 1752159716}, {"payPeriodId": "1050110809587977", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-08-10T00:00:00Z", "endDate": "2025-08-16T00:00:00Z", "submitByDate": "2025-08-20T00:00:00Z", "checkDate": "2025-08-22T00:00:00Z", "checkCount": 0, "id": "23691bb2-fe95-416f-9f90-b7ff7abbc3a6", "companyId": "14100438", "type": "payperiod", "_rid": "NmJkAKiCbEdhWQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdhWQAAAAAAAA==/", "_etag": "\"9800e738-0000-0100-0000-686fd5e50000\"", "_attachments": "attachments/", "_ts": 1752159717}, {"payPeriodId": "1050111006406491", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-08-17T00:00:00Z", "endDate": "2025-08-23T00:00:00Z", "submitByDate": "2025-08-27T00:00:00Z", "checkDate": "2025-08-29T00:00:00Z", "checkCount": 0, "id": "969a3283-c4c3-44e6-bec2-53ff911b0dc5", "companyId": "14100438", "type": "payperiod", "_rid": "NmJkAKiCbEdiWQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdiWQAAAAAAAA==/", "_etag": "\"9800eb38-0000-0100-0000-686fd5e50000\"", "_attachments": "attachments/", "_ts": 1752159717}, {"payPeriodId": "1050111253888989", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-08-24T00:00:00Z", "endDate": "2025-08-30T00:00:00Z", "submitByDate": "2025-09-03T00:00:00Z", "checkDate": "2025-09-05T00:00:00Z", "checkCount": 0, "id": "46630eb9-051e-4179-9ca3-e274651e4c4c", "companyId": "14100438", "type": "payperiod", "_rid": "NmJkAKiCbEdjWQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdjWQAAAAAAAA==/", "_etag": "\"9800ef38-0000-0100-0000-686fd5e50000\"", "_attachments": "attachments/", "_ts": 1752159717}, {"payPeriodId": "1050111512541459", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-08-31T00:00:00Z", "endDate": "2025-09-06T00:00:00Z", "submitByDate": "2025-09-10T00:00:00Z", "checkDate": "2025-09-12T00:00:00Z", "checkCount": 0, "id": "f3f039c1-954c-4005-bc15-d28d697b16f1", "companyId": "14100438", "type": "payperiod", "_rid": "NmJkAKiCbEdkWQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdkWQAAAAAAAA==/", "_etag": "\"9800fa38-0000-0100-0000-686fd5e50000\"", "_attachments": "attachments/", "_ts": 1752159717}, {"payPeriodId": "1050111680108966", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-09-07T00:00:00Z", "endDate": "2025-09-13T00:00:00Z", "submitByDate": "2025-09-17T00:00:00Z", "checkDate": "2025-09-19T00:00:00Z", "checkCount": 0, "id": "a1666cea-5a8a-435b-81fb-d5979113412e", "companyId": "14100438", "type": "payperiod", "_rid": "NmJkAKiCbEdlWQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdlWQAAAAAAAA==/", "_etag": "\"9800fd38-0000-0100-0000-686fd5e50000\"", "_attachments": "attachments/", "_ts": 1752159717}, {"payPeriodId": "1050111901136327", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-09-14T00:00:00Z", "endDate": "2025-09-20T00:00:00Z", "submitByDate": "2025-09-24T00:00:00Z", "checkDate": "2025-09-26T00:00:00Z", "checkCount": 0, "id": "52e4b841-2605-4ce1-9940-a3147b93a4a0", "companyId": "14100438", "type": "payperiod", "_rid": "NmJkAKiCbEdmWQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdmWQAAAAAAAA==/", "_etag": "\"98000039-0000-0100-0000-686fd5e50000\"", "_attachments": "attachments/", "_ts": 1752159717}, {"payPeriodId": "1050112256925449", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-09-21T00:00:00Z", "endDate": "2025-09-27T00:00:00Z", "submitByDate": "2025-10-01T00:00:00Z", "checkDate": "2025-10-03T00:00:00Z", "checkCount": 0, "id": "23814afa-f1ed-4925-8410-874d903701b2", "companyId": "14100438", "type": "payperiod", "_rid": "NmJkAKiCbEdnWQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdnWQAAAAAAAA==/", "_etag": "\"98000639-0000-0100-0000-686fd5e50000\"", "_attachments": "attachments/", "_ts": 1752159717}, {"payPeriodId": "1050112405097953", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-09-28T00:00:00Z", "endDate": "2025-10-04T00:00:00Z", "submitByDate": "2025-10-08T00:00:00Z", "checkDate": "2025-10-10T00:00:00Z", "checkCount": 0, "id": "46462140-7a32-4c86-838c-29f5f79c6832", "companyId": "14100438", "type": "payperiod", "_rid": "NmJkAKiCbEdoWQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdoWQAAAAAAAA==/", "_etag": "\"98000839-0000-0100-0000-686fd5e50000\"", "_attachments": "attachments/", "_ts": 1752159717}, {"payPeriodId": "1090066289487240", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2024-12-29T00:00:00Z", "endDate": "2025-01-04T00:00:00Z", "submitByDate": "2025-01-08T00:00:00Z", "checkDate": "2025-01-10T00:00:00Z", "checkCount": 5, "id": "e9ddf2a2-cf75-4dab-81a5-c0a56f586d08", "companyId": "14100438", "type": "payperiod", "_rid": "NmJkAKiCbEdvWQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdvWQAAAAAAAA==/", "_etag": "\"98002839-0000-0100-0000-686fd5e60000\"", "_attachments": "attachments/", "_ts": 1752159718}, {"payPeriodId": "1090066289487241", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-01-05T00:00:00Z", "endDate": "2025-01-11T00:00:00Z", "submitByDate": "2025-01-15T00:00:00Z", "checkDate": "2025-01-17T00:00:00Z", "checkCount": 5, "id": "bc9a049a-91b7-4379-9e5b-91dc1e457f96", "companyId": "14100438", "type": "payperiod", "_rid": "NmJkAKiCbEdwWQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdwWQAAAAAAAA==/", "_etag": "\"98002c39-0000-0100-0000-686fd5e60000\"", "_attachments": "attachments/", "_ts": 1752159718}, {"payPeriodId": "1050103597057267", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-01-12T00:00:00Z", "endDate": "2025-01-18T00:00:00Z", "submitByDate": "2025-01-22T00:00:00Z", "checkDate": "2025-01-24T00:00:00Z", "checkCount": 6, "id": "42e5d478-cfda-4833-a4db-6271ea5b5a6e", "companyId": "14100438", "type": "payperiod", "_rid": "NmJkAKiCbEdxWQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdxWQAAAAAAAA==/", "_etag": "\"98002e39-0000-0100-0000-686fd5e60000\"", "_attachments": "attachments/", "_ts": 1752159718}, {"payPeriodId": "1050107004769802", "status": "COMPLETED", "description": "payroll correction", "startDate": "2025-01-12T00:00:00Z", "endDate": "2025-01-18T00:00:00Z", "submitByDate": "2025-01-23T00:00:00Z", "checkDate": "2025-01-24T00:00:00Z", "checkCount": 1, "id": "1287aef5-2b3e-4f1d-9fde-53fd22cfa9f4", "companyId": "14100438", "type": "payperiod", "_rid": "NmJkAKiCbEdyWQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdyWQAAAAAAAA==/", "_etag": "\"98003039-0000-0100-0000-686fd5e60000\"", "_attachments": "attachments/", "_ts": 1752159718}, {"payPeriodId": "1050103626234586", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-01-19T00:00:00Z", "endDate": "2025-01-25T00:00:00Z", "submitByDate": "2025-01-29T00:00:00Z", "checkDate": "2025-01-31T00:00:00Z", "checkCount": 5, "id": "089c99ff-a687-4a5c-9472-67ca9163c724", "companyId": "14100438", "type": "payperiod", "_rid": "NmJkAKiCbEdzWQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdzWQAAAAAAAA==/", "_etag": "\"98003339-0000-0100-0000-686fd5e60000\"", "_attachments": "attachments/", "_ts": 1752159718}, {"payPeriodId": "1050104053099845", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-01-26T00:00:00Z", "endDate": "2025-02-01T00:00:00Z", "submitByDate": "2025-02-05T00:00:00Z", "checkDate": "2025-02-07T00:00:00Z", "checkCount": 5, "id": "e2249585-b62e-4a87-8bb3-4b0f4fe7f710", "companyId": "14100438", "type": "payperiod", "_rid": "NmJkAKiCbEd0WQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEd0WQAAAAAAAA==/", "_etag": "\"98003539-0000-0100-0000-686fd5e60000\"", "_attachments": "attachments/", "_ts": 1752159718}, {"payPeriodId": "1050104144656671", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-02-02T00:00:00Z", "endDate": "2025-02-08T00:00:00Z", "submitByDate": "2025-02-12T00:00:00Z", "checkDate": "2025-02-14T00:00:00Z", "checkCount": 5, "id": "a7c24b57-43e9-4dac-809a-08d5498b7060", "companyId": "14100438", "type": "payperiod", "_rid": "NmJkAKiCbEd1WQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEd1WQAAAAAAAA==/", "_etag": "\"98003639-0000-0100-0000-686fd5e60000\"", "_attachments": "attachments/", "_ts": 1752159718}, {"payPeriodId": "1050104542114198", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-02-09T00:00:00Z", "endDate": "2025-02-15T00:00:00Z", "submitByDate": "2025-02-19T00:00:00Z", "checkDate": "2025-02-21T00:00:00Z", "checkCount": 5, "id": "a05a63e6-b7a8-430c-bd02-6d3f1be93326", "companyId": "14100438", "type": "payperiod", "_rid": "NmJkAKiCbEd2WQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEd2WQAAAAAAAA==/", "_etag": "\"98003a39-0000-0100-0000-686fd5e60000\"", "_attachments": "attachments/", "_ts": 1752159718}, {"payPeriodId": "1050104745588762", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-02-16T00:00:00Z", "endDate": "2025-02-22T00:00:00Z", "submitByDate": "2025-02-26T00:00:00Z", "checkDate": "2025-02-28T00:00:00Z", "checkCount": 5, "id": "847f76db-6216-4605-961a-c29358670914", "companyId": "14100438", "type": "payperiod", "_rid": "NmJkAKiCbEd3WQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEd3WQAAAAAAAA==/", "_etag": "\"98003d39-0000-0100-0000-686fd5e60000\"", "_attachments": "attachments/", "_ts": 1752159718}, {"payPeriodId": "1050104908177422", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-02-23T00:00:00Z", "endDate": "2025-03-01T00:00:00Z", "submitByDate": "2025-03-05T00:00:00Z", "checkDate": "2025-03-07T00:00:00Z", "checkCount": 5, "id": "6f54401e-5af2-4254-9533-0a33907b81e5", "companyId": "14100438", "type": "payperiod", "_rid": "NmJkAKiCbEd4WQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEd4WQAAAAAAAA==/", "_etag": "\"98004239-0000-0100-0000-686fd5e60000\"", "_attachments": "attachments/", "_ts": 1752159718}, {"payPeriodId": "1050105242086994", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-03-02T00:00:00Z", "endDate": "2025-03-08T00:00:00Z", "submitByDate": "2025-03-12T00:00:00Z", "checkDate": "2025-03-14T00:00:00Z", "checkCount": 5, "id": "4a8655b0-acc3-4a64-a782-4dfd0f1d6383", "companyId": "14100438", "type": "payperiod", "_rid": "NmJkAKiCbEd5WQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEd5WQAAAAAAAA==/", "_etag": "\"98004539-0000-0100-0000-686fd5e60000\"", "_attachments": "attachments/", "_ts": 1752159718}, {"payPeriodId": "1050105516922502", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-03-09T00:00:00Z", "endDate": "2025-03-15T00:00:00Z", "submitByDate": "2025-03-19T00:00:00Z", "checkDate": "2025-03-21T00:00:00Z", "checkCount": 5, "id": "db3cb53c-c63a-4236-bef4-c3b355e0a9b7", "companyId": "14100438", "type": "payperiod", "_rid": "NmJkAKiCbEd6WQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEd6WQAAAAAAAA==/", "_etag": "\"98004a39-0000-0100-0000-686fd5e60000\"", "_attachments": "attachments/", "_ts": 1752159718}, {"payPeriodId": "1050105776014225", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-03-16T00:00:00Z", "endDate": "2025-03-22T00:00:00Z", "submitByDate": "2025-03-26T00:00:00Z", "checkDate": "2025-03-28T00:00:00Z", "checkCount": 5, "id": "a9b84e4c-679e-4329-b0ee-38256c557179", "companyId": "14100438", "type": "payperiod", "_rid": "NmJkAKiCbEd7WQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEd7WQAAAAAAAA==/", "_etag": "\"98005339-0000-0100-0000-686fd5e60000\"", "_attachments": "attachments/", "_ts": 1752159718}, {"payPeriodId": "1050105870703820", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-03-23T00:00:00Z", "endDate": "2025-03-29T00:00:00Z", "submitByDate": "2025-04-02T00:00:00Z", "checkDate": "2025-04-04T00:00:00Z", "checkCount": 5, "id": "ac432103-11e8-4074-8340-2453b8805e81", "companyId": "14100438", "type": "payperiod", "_rid": "NmJkAKiCbEd8WQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEd8WQAAAAAAAA==/", "_etag": "\"98005739-0000-0100-0000-686fd5e60000\"", "_attachments": "attachments/", "_ts": 1752159718}, {"payPeriodId": "1050106248360341", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-03-30T00:00:00Z", "endDate": "2025-04-05T00:00:00Z", "submitByDate": "2025-04-09T00:00:00Z", "checkDate": "2025-04-11T00:00:00Z", "checkCount": 5, "id": "b167a242-4d32-4aeb-bafb-0ddf27f6d02d", "companyId": "14100438", "type": "payperiod", "_rid": "NmJkAKiCbEd9WQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEd9WQAAAAAAAA==/", "_etag": "\"98005f39-0000-0100-0000-686fd5e70000\"", "_attachments": "attachments/", "_ts": 1752159719}, {"payPeriodId": "1050106497036762", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-04-06T00:00:00Z", "endDate": "2025-04-12T00:00:00Z", "submitByDate": "2025-04-16T00:00:00Z", "checkDate": "2025-04-18T00:00:00Z", "checkCount": 5, "id": "cdc3087c-755f-4226-b155-1b2b7ca71c59", "companyId": "14100438", "type": "payperiod", "_rid": "NmJkAKiCbEd+WQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEd+WQAAAAAAAA==/", "_etag": "\"98006239-0000-0100-0000-686fd5e70000\"", "_attachments": "attachments/", "_ts": 1752159719}, {"payPeriodId": "1050106754428367", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-04-13T00:00:00Z", "endDate": "2025-04-19T00:00:00Z", "submitByDate": "2025-04-23T00:00:00Z", "checkDate": "2025-04-25T00:00:00Z", "checkCount": 5, "id": "48ce87a5-b6eb-4001-b127-9ff48085f822", "companyId": "14100438", "type": "payperiod", "_rid": "NmJkAKiCbEd-WQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEd-WQAAAAAAAA==/", "_etag": "\"98006739-0000-0100-0000-686fd5e70000\"", "_attachments": "attachments/", "_ts": 1752159719}, {"payPeriodId": "1050106837646431", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-04-20T00:00:00Z", "endDate": "2025-04-26T00:00:00Z", "submitByDate": "2025-04-30T00:00:00Z", "checkDate": "2025-05-02T00:00:00Z", "checkCount": 3, "id": "9daa908a-5d49-46ef-a209-ff9cb9fb5b67", "companyId": "14100438", "type": "payperiod", "_rid": "NmJkAKiCbEeAWQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeAWQAAAAAAAA==/", "_etag": "\"98006839-0000-0100-0000-686fd5e70000\"", "_attachments": "attachments/", "_ts": 1752159719}, {"payPeriodId": "1050110393010694", "status": "COMPLETED", "description": "correction", "startDate": "2025-04-20T00:00:00Z", "endDate": "2025-04-26T00:00:00Z", "submitByDate": "2025-05-01T00:00:00Z", "checkDate": "2025-05-02T00:00:00Z", "checkCount": 2, "id": "0b3048dd-1d56-4839-9b12-d93b6c2c73d6", "companyId": "14100438", "type": "payperiod", "_rid": "NmJkAKiCbEeBWQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeBWQAAAAAAAA==/", "_etag": "\"98006b39-0000-0100-0000-686fd5e70000\"", "_attachments": "attachments/", "_ts": 1752159719}, {"payPeriodId": "1050107249807616", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-04-27T00:00:00Z", "endDate": "2025-05-03T00:00:00Z", "submitByDate": "2025-05-07T00:00:00Z", "checkDate": "2025-05-09T00:00:00Z", "checkCount": 5, "id": "510bb141-3425-4dbc-935d-78976c930e14", "companyId": "14100438", "type": "payperiod", "_rid": "NmJkAKiCbEeCWQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeCWQAAAAAAAA==/", "_etag": "\"98006e39-0000-0100-0000-686fd5e70000\"", "_attachments": "attachments/", "_ts": 1752159719}, {"payPeriodId": "1050107283363063", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-05-04T00:00:00Z", "endDate": "2025-05-10T00:00:00Z", "submitByDate": "2025-05-14T00:00:00Z", "checkDate": "2025-05-16T00:00:00Z", "checkCount": 5, "id": "0c4749f8-fe31-4d2d-b3c7-b6befef2d4c8", "companyId": "14100438", "type": "payperiod", "_rid": "NmJkAKiCbEeDWQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeDWQAAAAAAAA==/", "_etag": "\"98007239-0000-0100-0000-686fd5e70000\"", "_attachments": "attachments/", "_ts": 1752159719}, {"payPeriodId": "1050107639742607", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-05-11T00:00:00Z", "endDate": "2025-05-17T00:00:00Z", "submitByDate": "2025-05-21T00:00:00Z", "checkDate": "2025-05-23T00:00:00Z", "checkCount": 5, "id": "2250634a-db33-4c53-96b5-6951d4ef5f7e", "companyId": "14100438", "type": "payperiod", "_rid": "NmJkAKiCbEeEWQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeEWQAAAAAAAA==/", "_etag": "\"98007339-0000-0100-0000-686fd5e70000\"", "_attachments": "attachments/", "_ts": 1752159719}, {"payPeriodId": "1050107856369794", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-05-18T00:00:00Z", "endDate": "2025-05-24T00:00:00Z", "submitByDate": "2025-05-28T00:00:00Z", "checkDate": "2025-05-30T00:00:00Z", "checkCount": 5, "id": "d51e4626-4017-4698-9208-86d0971a8343", "companyId": "14100438", "type": "payperiod", "_rid": "NmJkAKiCbEeFWQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeFWQAAAAAAAA==/", "_etag": "\"98007639-0000-0100-0000-686fd5e70000\"", "_attachments": "attachments/", "_ts": 1752159719}, {"payPeriodId": "1050108157540182", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-05-25T00:00:00Z", "endDate": "2025-05-31T00:00:00Z", "submitByDate": "2025-06-04T00:00:00Z", "checkDate": "2025-06-06T00:00:00Z", "checkCount": 5, "id": "91a4e6f0-2e6a-441a-b93f-2cfd5a2c777d", "companyId": "14100438", "type": "payperiod", "_rid": "NmJkAKiCbEeGWQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeGWQAAAAAAAA==/", "_etag": "\"98007b39-0000-0100-0000-686fd5e70000\"", "_attachments": "attachments/", "_ts": 1752159719}, {"payPeriodId": "1050108277749688", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-06-01T00:00:00Z", "endDate": "2025-06-07T00:00:00Z", "submitByDate": "2025-06-11T00:00:00Z", "checkDate": "2025-06-13T00:00:00Z", "checkCount": 5, "id": "dba69a59-a88e-4d60-b9e3-3d22257d6fc0", "companyId": "14100438", "type": "payperiod", "_rid": "NmJkAKiCbEeHWQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeHWQAAAAAAAA==/", "_etag": "\"98008039-0000-0100-0000-686fd5e70000\"", "_attachments": "attachments/", "_ts": 1752159719}, {"payPeriodId": "1050108510193051", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-06-08T00:00:00Z", "endDate": "2025-06-14T00:00:00Z", "submitByDate": "2025-06-18T00:00:00Z", "checkDate": "2025-06-20T00:00:00Z", "checkCount": 5, "id": "d05562d6-b649-4025-8de7-d979bb48e4c1", "companyId": "14100438", "type": "payperiod", "_rid": "NmJkAKiCbEeIWQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeIWQAAAAAAAA==/", "_etag": "\"98008139-0000-0100-0000-686fd5e70000\"", "_attachments": "attachments/", "_ts": 1752159719}, {"payPeriodId": "1050108807713788", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-06-15T00:00:00Z", "endDate": "2025-06-21T00:00:00Z", "submitByDate": "2025-06-25T00:00:00Z", "checkDate": "2025-06-27T00:00:00Z", "checkCount": 5, "id": "bf63bf34-c4b2-4578-be08-bdcf59f4f84f", "companyId": "14100438", "type": "payperiod", "_rid": "NmJkAKiCbEeJWQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeJWQAAAAAAAA==/", "_etag": "\"98008439-0000-0100-0000-686fd5e70000\"", "_attachments": "attachments/", "_ts": 1752159719}, {"payPeriodId": "1050109102138266", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-06-22T00:00:00Z", "endDate": "2025-06-28T00:00:00Z", "submitByDate": "2025-07-01T00:00:00Z", "checkDate": "2025-07-03T00:00:00Z", "checkCount": 5, "id": "6afc7974-6dfe-466c-820f-26cf10236f08", "companyId": "14100438", "type": "payperiod", "_rid": "NmJkAKiCbEeKWQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeKWQAAAAAAAA==/", "_etag": "\"98008739-0000-0100-0000-686fd5e70000\"", "_attachments": "attachments/", "_ts": 1752159719}, {"payPeriodId": "1050109358095878", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-06-29T00:00:00Z", "endDate": "2025-07-05T00:00:00Z", "submitByDate": "2025-07-09T00:00:00Z", "checkDate": "2025-07-11T00:00:00Z", "checkCount": 0, "id": "c904bd2a-3522-4821-824c-95b480bd1bad", "companyId": "14100438", "type": "payperiod", "_rid": "NmJkAKiCbEeLWQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeLWQAAAAAAAA==/", "_etag": "\"98008a39-0000-0100-0000-686fd5e80000\"", "_attachments": "attachments/", "_ts": 1752159720}, {"payPeriodId": "1050109592368855", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-07-06T00:00:00Z", "endDate": "2025-07-12T00:00:00Z", "submitByDate": "2025-07-16T00:00:00Z", "checkDate": "2025-07-18T00:00:00Z", "checkCount": 0, "id": "c722f753-09bf-4c69-b4a5-047d579ed4ea", "companyId": "14100438", "type": "payperiod", "_rid": "NmJkAKiCbEeMWQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeMWQAAAAAAAA==/", "_etag": "\"98009039-0000-0100-0000-686fd5e80000\"", "_attachments": "attachments/", "_ts": 1752159720}, {"payPeriodId": "1050109823056314", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-07-13T00:00:00Z", "endDate": "2025-07-19T00:00:00Z", "submitByDate": "2025-07-23T00:00:00Z", "checkDate": "2025-07-25T00:00:00Z", "checkCount": 0, "id": "0bc68f9d-f134-401b-91e1-9399a9e0e26f", "companyId": "14100438", "type": "payperiod", "_rid": "NmJkAKiCbEeNWQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeNWQAAAAAAAA==/", "_etag": "\"98009239-0000-0100-0000-686fd5e80000\"", "_attachments": "attachments/", "_ts": 1752159720}, {"payPeriodId": "1050110032614018", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-07-20T00:00:00Z", "endDate": "2025-07-26T00:00:00Z", "submitByDate": "2025-07-30T00:00:00Z", "checkDate": "2025-08-01T00:00:00Z", "checkCount": 0, "id": "0c86fae6-94f1-4e73-82b5-ad39d672249f", "companyId": "14100438", "type": "payperiod", "_rid": "NmJkAKiCbEeOWQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeOWQAAAAAAAA==/", "_etag": "\"98009639-0000-0100-0000-686fd5e80000\"", "_attachments": "attachments/", "_ts": 1752159720}, {"payPeriodId": "1050110152016737", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-07-27T00:00:00Z", "endDate": "2025-08-02T00:00:00Z", "submitByDate": "2025-08-06T00:00:00Z", "checkDate": "2025-08-08T00:00:00Z", "checkCount": 0, "id": "e60ed63c-147b-4b20-b393-32da39bfb5e0", "companyId": "14100438", "type": "payperiod", "_rid": "NmJkAKiCbEePWQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEePWQAAAAAAAA==/", "_etag": "\"98009939-0000-0100-0000-686fd5e80000\"", "_attachments": "attachments/", "_ts": 1752159720}, {"payPeriodId": "1050110525635123", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-08-03T00:00:00Z", "endDate": "2025-08-09T00:00:00Z", "submitByDate": "2025-08-13T00:00:00Z", "checkDate": "2025-08-15T00:00:00Z", "checkCount": 0, "id": "7ad7feb4-18bf-40e1-a169-c4d2b83dd78d", "companyId": "14100438", "type": "payperiod", "_rid": "NmJkAKiCbEeQWQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeQWQAAAAAAAA==/", "_etag": "\"98009d39-0000-0100-0000-686fd5e80000\"", "_attachments": "attachments/", "_ts": 1752159720}, {"payPeriodId": "1050110809587977", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-08-10T00:00:00Z", "endDate": "2025-08-16T00:00:00Z", "submitByDate": "2025-08-20T00:00:00Z", "checkDate": "2025-08-22T00:00:00Z", "checkCount": 0, "id": "f8d03963-e987-47fd-a702-5a82e2d849fb", "companyId": "14100438", "type": "payperiod", "_rid": "NmJkAKiCbEeRWQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeRWQAAAAAAAA==/", "_etag": "\"9800a439-0000-0100-0000-686fd5e80000\"", "_attachments": "attachments/", "_ts": 1752159720}, {"payPeriodId": "1050111006406491", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-08-17T00:00:00Z", "endDate": "2025-08-23T00:00:00Z", "submitByDate": "2025-08-27T00:00:00Z", "checkDate": "2025-08-29T00:00:00Z", "checkCount": 0, "id": "0cb53b84-2590-430f-8ea1-392e79ff4680", "companyId": "14100438", "type": "payperiod", "_rid": "NmJkAKiCbEeSWQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeSWQAAAAAAAA==/", "_etag": "\"9800aa39-0000-0100-0000-686fd5e80000\"", "_attachments": "attachments/", "_ts": 1752159720}, {"payPeriodId": "1050111253888989", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-08-24T00:00:00Z", "endDate": "2025-08-30T00:00:00Z", "submitByDate": "2025-09-03T00:00:00Z", "checkDate": "2025-09-05T00:00:00Z", "checkCount": 0, "id": "aaf437cc-1649-4b16-b74d-41ea5d80646b", "companyId": "14100438", "type": "payperiod", "_rid": "NmJkAKiCbEeTWQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeTWQAAAAAAAA==/", "_etag": "\"9800b439-0000-0100-0000-686fd5e80000\"", "_attachments": "attachments/", "_ts": 1752159720}, {"payPeriodId": "1050111512541459", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-08-31T00:00:00Z", "endDate": "2025-09-06T00:00:00Z", "submitByDate": "2025-09-10T00:00:00Z", "checkDate": "2025-09-12T00:00:00Z", "checkCount": 0, "id": "735e50a5-9d1f-4543-9ed3-9f407282098a", "companyId": "14100438", "type": "payperiod", "_rid": "NmJkAKiCbEeUWQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeUWQAAAAAAAA==/", "_etag": "\"9800ba39-0000-0100-0000-686fd5e80000\"", "_attachments": "attachments/", "_ts": 1752159720}, {"payPeriodId": "1050111680108966", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-09-07T00:00:00Z", "endDate": "2025-09-13T00:00:00Z", "submitByDate": "2025-09-17T00:00:00Z", "checkDate": "2025-09-19T00:00:00Z", "checkCount": 0, "id": "acad18e1-161f-459c-b27c-606a7f1b5f5f", "companyId": "14100438", "type": "payperiod", "_rid": "NmJkAKiCbEeVWQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeVWQAAAAAAAA==/", "_etag": "\"9800be39-0000-0100-0000-686fd5e80000\"", "_attachments": "attachments/", "_ts": 1752159720}, {"payPeriodId": "1050111901136327", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-09-14T00:00:00Z", "endDate": "2025-09-20T00:00:00Z", "submitByDate": "2025-09-24T00:00:00Z", "checkDate": "2025-09-26T00:00:00Z", "checkCount": 0, "id": "32982023-a660-40d3-a938-dc2ae85e0fa5", "companyId": "14100438", "type": "payperiod", "_rid": "NmJkAKiCbEeWWQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeWWQAAAAAAAA==/", "_etag": "\"9800bf39-0000-0100-0000-686fd5e80000\"", "_attachments": "attachments/", "_ts": 1752159720}, {"payPeriodId": "1050112256925449", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-09-21T00:00:00Z", "endDate": "2025-09-27T00:00:00Z", "submitByDate": "2025-10-01T00:00:00Z", "checkDate": "2025-10-03T00:00:00Z", "checkCount": 0, "id": "fbda1367-57ef-431e-bf0c-ea599888f774", "companyId": "14100438", "type": "payperiod", "_rid": "NmJkAKiCbEeXWQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeXWQAAAAAAAA==/", "_etag": "\"9800c039-0000-0100-0000-686fd5e80000\"", "_attachments": "attachments/", "_ts": 1752159720}, {"payPeriodId": "1050112405097953", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-09-28T00:00:00Z", "endDate": "2025-10-04T00:00:00Z", "submitByDate": "2025-10-08T00:00:00Z", "checkDate": "2025-10-10T00:00:00Z", "checkCount": 0, "id": "a6dd92b0-e3fa-4ba7-bc62-419676ccc34b", "companyId": "14100438", "type": "payperiod", "_rid": "NmJkAKiCbEeYWQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeYWQAAAAAAAA==/", "_etag": "\"9800c439-0000-0100-0000-686fd5e80000\"", "_attachments": "attachments/", "_ts": 1752159720}, {"payPeriodId": "1090066289487240", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2024-12-29T00:00:00Z", "endDate": "2025-01-04T00:00:00Z", "submitByDate": "2025-01-08T00:00:00Z", "checkDate": "2025-01-10T00:00:00Z", "checkCount": 5, "id": "0f0a0b2b-5c8f-4d76-8ba4-5aea84107816", "companyId": "14100438", "type": "payperiod", "_rid": "NmJkAKiCbEdsUgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdsUgEAAAAAAA==/", "_etag": "\"9e005164-0000-0100-0000-686ffc9d0000\"", "_attachments": "attachments/", "_ts": 1752169629}, {"payPeriodId": "1090066289487241", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-01-05T00:00:00Z", "endDate": "2025-01-11T00:00:00Z", "submitByDate": "2025-01-15T00:00:00Z", "checkDate": "2025-01-17T00:00:00Z", "checkCount": 5, "id": "0b8873fa-5864-4351-b99f-22b7b2042a53", "companyId": "14100438", "type": "payperiod", "_rid": "NmJkAKiCbEdtUgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdtUgEAAAAAAA==/", "_etag": "\"9e005264-0000-0100-0000-686ffc9d0000\"", "_attachments": "attachments/", "_ts": 1752169629}, {"payPeriodId": "1050103597057267", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-01-12T00:00:00Z", "endDate": "2025-01-18T00:00:00Z", "submitByDate": "2025-01-22T00:00:00Z", "checkDate": "2025-01-24T00:00:00Z", "checkCount": 6, "id": "e03a4e57-eee9-46e5-b14a-f4ea0e9cbe04", "companyId": "14100438", "type": "payperiod", "_rid": "NmJkAKiCbEduUgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEduUgEAAAAAAA==/", "_etag": "\"9e005764-0000-0100-0000-686ffc9e0000\"", "_attachments": "attachments/", "_ts": 1752169630}, {"payPeriodId": "1050107004769802", "status": "COMPLETED", "description": "payroll correction", "startDate": "2025-01-12T00:00:00Z", "endDate": "2025-01-18T00:00:00Z", "submitByDate": "2025-01-23T00:00:00Z", "checkDate": "2025-01-24T00:00:00Z", "checkCount": 1, "id": "733bffd4-a452-44fb-8ca7-acf4941a73a2", "companyId": "14100438", "type": "payperiod", "_rid": "NmJkAKiCbEdvUgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdvUgEAAAAAAA==/", "_etag": "\"9e005d64-0000-0100-0000-686ffc9e0000\"", "_attachments": "attachments/", "_ts": 1752169630}, {"payPeriodId": "1050103626234586", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-01-19T00:00:00Z", "endDate": "2025-01-25T00:00:00Z", "submitByDate": "2025-01-29T00:00:00Z", "checkDate": "2025-01-31T00:00:00Z", "checkCount": 5, "id": "d640dab8-5bef-419a-b7cc-feb98886ec09", "companyId": "14100438", "type": "payperiod", "_rid": "NmJkAKiCbEdwUgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdwUgEAAAAAAA==/", "_etag": "\"9e006164-0000-0100-0000-686ffc9e0000\"", "_attachments": "attachments/", "_ts": 1752169630}, {"payPeriodId": "1050104053099845", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-01-26T00:00:00Z", "endDate": "2025-02-01T00:00:00Z", "submitByDate": "2025-02-05T00:00:00Z", "checkDate": "2025-02-07T00:00:00Z", "checkCount": 5, "id": "3fd497bf-151d-48e3-b3a0-b6fde84c2f5b", "companyId": "14100438", "type": "payperiod", "_rid": "NmJkAKiCbEdxUgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdxUgEAAAAAAA==/", "_etag": "\"9e006764-0000-0100-0000-686ffc9e0000\"", "_attachments": "attachments/", "_ts": 1752169630}, {"payPeriodId": "1050104144656671", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-02-02T00:00:00Z", "endDate": "2025-02-08T00:00:00Z", "submitByDate": "2025-02-12T00:00:00Z", "checkDate": "2025-02-14T00:00:00Z", "checkCount": 5, "id": "32def8fe-793a-45b7-85c9-9fd5d0a52a03", "companyId": "14100438", "type": "payperiod", "_rid": "NmJkAKiCbEdyUgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdyUgEAAAAAAA==/", "_etag": "\"9e006a64-0000-0100-0000-686ffc9e0000\"", "_attachments": "attachments/", "_ts": 1752169630}, {"payPeriodId": "1050104542114198", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-02-09T00:00:00Z", "endDate": "2025-02-15T00:00:00Z", "submitByDate": "2025-02-19T00:00:00Z", "checkDate": "2025-02-21T00:00:00Z", "checkCount": 5, "id": "4456fafc-db83-4f03-90b5-8dd9a0ab2279", "companyId": "14100438", "type": "payperiod", "_rid": "NmJkAKiCbEdzUgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdzUgEAAAAAAA==/", "_etag": "\"9e007064-0000-0100-0000-686ffc9e0000\"", "_attachments": "attachments/", "_ts": 1752169630}, {"payPeriodId": "1050104745588762", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-02-16T00:00:00Z", "endDate": "2025-02-22T00:00:00Z", "submitByDate": "2025-02-26T00:00:00Z", "checkDate": "2025-02-28T00:00:00Z", "checkCount": 5, "id": "19bfd7fd-f4c0-4558-84b9-8b4465e35c98", "companyId": "14100438", "type": "payperiod", "_rid": "NmJkAKiCbEd0UgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEd0UgEAAAAAAA==/", "_etag": "\"9e007464-0000-0100-0000-686ffc9e0000\"", "_attachments": "attachments/", "_ts": 1752169630}, {"payPeriodId": "1050104908177422", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-02-23T00:00:00Z", "endDate": "2025-03-01T00:00:00Z", "submitByDate": "2025-03-05T00:00:00Z", "checkDate": "2025-03-07T00:00:00Z", "checkCount": 5, "id": "8fd900fa-5992-4b6c-9ffd-6f2437a934ee", "companyId": "14100438", "type": "payperiod", "_rid": "NmJkAKiCbEd1UgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEd1UgEAAAAAAA==/", "_etag": "\"9e007664-0000-0100-0000-686ffc9e0000\"", "_attachments": "attachments/", "_ts": 1752169630}, {"payPeriodId": "1050105242086994", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-03-02T00:00:00Z", "endDate": "2025-03-08T00:00:00Z", "submitByDate": "2025-03-12T00:00:00Z", "checkDate": "2025-03-14T00:00:00Z", "checkCount": 5, "id": "db9b9cc8-de5e-454d-b7ad-933787406443", "companyId": "14100438", "type": "payperiod", "_rid": "NmJkAKiCbEd2UgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEd2UgEAAAAAAA==/", "_etag": "\"9e007964-0000-0100-0000-686ffc9e0000\"", "_attachments": "attachments/", "_ts": 1752169630}, {"payPeriodId": "1050105516922502", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-03-09T00:00:00Z", "endDate": "2025-03-15T00:00:00Z", "submitByDate": "2025-03-19T00:00:00Z", "checkDate": "2025-03-21T00:00:00Z", "checkCount": 5, "id": "8ec7b4ef-13be-4026-82dc-efd0c1bcbd54", "companyId": "14100438", "type": "payperiod", "_rid": "NmJkAKiCbEd3UgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEd3UgEAAAAAAA==/", "_etag": "\"9e007b64-0000-0100-0000-686ffc9e0000\"", "_attachments": "attachments/", "_ts": 1752169630}, {"payPeriodId": "1050105776014225", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-03-16T00:00:00Z", "endDate": "2025-03-22T00:00:00Z", "submitByDate": "2025-03-26T00:00:00Z", "checkDate": "2025-03-28T00:00:00Z", "checkCount": 5, "id": "a0dc151d-965f-47b1-9040-3c965f097800", "companyId": "14100438", "type": "payperiod", "_rid": "NmJkAKiCbEd4UgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEd4UgEAAAAAAA==/", "_etag": "\"9e007f64-0000-0100-0000-686ffc9e0000\"", "_attachments": "attachments/", "_ts": 1752169630}, {"payPeriodId": "1050105870703820", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-03-23T00:00:00Z", "endDate": "2025-03-29T00:00:00Z", "submitByDate": "2025-04-02T00:00:00Z", "checkDate": "2025-04-04T00:00:00Z", "checkCount": 0, "id": "cdeeca73-fbd9-4cf1-96fd-878fcffabe63", "companyId": "14100438", "type": "payperiod", "_rid": "NmJkAKiCbEd5UgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEd5UgEAAAAAAA==/", "_etag": "\"9e008264-0000-0100-0000-686ffc9e0000\"", "_attachments": "attachments/", "_ts": 1752169630}, {"payPeriodId": "1050106248360341", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-03-30T00:00:00Z", "endDate": "2025-04-05T00:00:00Z", "submitByDate": "2025-04-09T00:00:00Z", "checkDate": "2025-04-11T00:00:00Z", "checkCount": 0, "id": "bba27b88-1221-4cad-b293-97a83bb23a19", "companyId": "14100438", "type": "payperiod", "_rid": "NmJkAKiCbEd6UgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEd6UgEAAAAAAA==/", "_etag": "\"9e008464-0000-0100-0000-686ffc9e0000\"", "_attachments": "attachments/", "_ts": 1752169630}, {"payPeriodId": "1050106497036762", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-04-06T00:00:00Z", "endDate": "2025-04-12T00:00:00Z", "submitByDate": "2025-04-16T00:00:00Z", "checkDate": "2025-04-18T00:00:00Z", "checkCount": 0, "id": "d7a7af23-bc8b-483c-9959-bbe12dfecc74", "companyId": "14100438", "type": "payperiod", "_rid": "NmJkAKiCbEd7UgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEd7UgEAAAAAAA==/", "_etag": "\"9e008664-0000-0100-0000-686ffc9f0000\"", "_attachments": "attachments/", "_ts": 1752169631}, {"payPeriodId": "1050106754428367", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-04-13T00:00:00Z", "endDate": "2025-04-19T00:00:00Z", "submitByDate": "2025-04-23T00:00:00Z", "checkDate": "2025-04-25T00:00:00Z", "checkCount": 0, "id": "d14c29f9-cfc3-4f4f-b88c-25cc40a591ae", "companyId": "14100438", "type": "payperiod", "_rid": "NmJkAKiCbEd8UgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEd8UgEAAAAAAA==/", "_etag": "\"9e008864-0000-0100-0000-686ffc9f0000\"", "_attachments": "attachments/", "_ts": 1752169631}, {"payPeriodId": "1050106837646431", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-04-20T00:00:00Z", "endDate": "2025-04-26T00:00:00Z", "submitByDate": "2025-04-30T00:00:00Z", "checkDate": "2025-05-02T00:00:00Z", "checkCount": 0, "id": "e071a575-cc71-42bd-9184-4d82cc8ad06a", "companyId": "14100438", "type": "payperiod", "_rid": "NmJkAKiCbEd9UgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEd9UgEAAAAAAA==/", "_etag": "\"9e008a64-0000-0100-0000-686ffc9f0000\"", "_attachments": "attachments/", "_ts": 1752169631}, {"payPeriodId": "1050110393010694", "status": "INITIAL", "description": "correction", "startDate": "2025-04-20T00:00:00Z", "endDate": "2025-04-26T00:00:00Z", "submitByDate": "2025-05-01T00:00:00Z", "checkDate": "2025-05-02T00:00:00Z", "checkCount": 0, "id": "456a71a4-d121-4587-b773-c2178b2a6be7", "companyId": "14100438", "type": "payperiod", "_rid": "NmJkAKiCbEd+UgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEd+UgEAAAAAAA==/", "_etag": "\"9e008d64-0000-0100-0000-686ffc9f0000\"", "_attachments": "attachments/", "_ts": 1752169631}, {"payPeriodId": "1050107249807616", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-04-27T00:00:00Z", "endDate": "2025-05-03T00:00:00Z", "submitByDate": "2025-05-07T00:00:00Z", "checkDate": "2025-05-09T00:00:00Z", "checkCount": 0, "id": "82b10815-991b-485d-9f15-7d66c340343e", "companyId": "14100438", "type": "payperiod", "_rid": "NmJkAKiCbEd-UgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEd-UgEAAAAAAA==/", "_etag": "\"9e009064-0000-0100-0000-686ffc9f0000\"", "_attachments": "attachments/", "_ts": 1752169631}, {"payPeriodId": "1050107283363063", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-05-04T00:00:00Z", "endDate": "2025-05-10T00:00:00Z", "submitByDate": "2025-05-14T00:00:00Z", "checkDate": "2025-05-16T00:00:00Z", "checkCount": 0, "id": "ca220468-7179-489d-9f9e-46ffac909d6a", "companyId": "14100438", "type": "payperiod", "_rid": "NmJkAKiCbEeAUgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeAUgEAAAAAAA==/", "_etag": "\"9e009164-0000-0100-0000-686ffc9f0000\"", "_attachments": "attachments/", "_ts": 1752169631}, {"payPeriodId": "1050107639742607", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-05-11T00:00:00Z", "endDate": "2025-05-17T00:00:00Z", "submitByDate": "2025-05-21T00:00:00Z", "checkDate": "2025-05-23T00:00:00Z", "checkCount": 0, "id": "d973a4d4-f3ba-4d22-b410-dd57f90c7744", "companyId": "14100438", "type": "payperiod", "_rid": "NmJkAKiCbEeBUgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeBUgEAAAAAAA==/", "_etag": "\"9e009464-0000-0100-0000-686ffc9f0000\"", "_attachments": "attachments/", "_ts": 1752169631}, {"payPeriodId": "1050107856369794", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-05-18T00:00:00Z", "endDate": "2025-05-24T00:00:00Z", "submitByDate": "2025-05-28T00:00:00Z", "checkDate": "2025-05-30T00:00:00Z", "checkCount": 0, "id": "cdee6cac-c527-4bb9-84bd-c572f1095ef7", "companyId": "14100438", "type": "payperiod", "_rid": "NmJkAKiCbEeCUgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeCUgEAAAAAAA==/", "_etag": "\"9e009764-0000-0100-0000-686ffc9f0000\"", "_attachments": "attachments/", "_ts": 1752169631}, {"payPeriodId": "1050108157540182", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-05-25T00:00:00Z", "endDate": "2025-05-31T00:00:00Z", "submitByDate": "2025-06-04T00:00:00Z", "checkDate": "2025-06-06T00:00:00Z", "checkCount": 0, "id": "4aba72a8-f703-41d2-9578-7666e83d2995", "companyId": "14100438", "type": "payperiod", "_rid": "NmJkAKiCbEeDUgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeDUgEAAAAAAA==/", "_etag": "\"9e009864-0000-0100-0000-686ffc9f0000\"", "_attachments": "attachments/", "_ts": 1752169631}, {"payPeriodId": "1050108277749688", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-06-01T00:00:00Z", "endDate": "2025-06-07T00:00:00Z", "submitByDate": "2025-06-11T00:00:00Z", "checkDate": "2025-06-13T00:00:00Z", "checkCount": 0, "id": "033c4570-4fc9-4334-a3b5-fcf15161e4c7", "companyId": "14100438", "type": "payperiod", "_rid": "NmJkAKiCbEeEUgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeEUgEAAAAAAA==/", "_etag": "\"9e009c64-0000-0100-0000-686ffc9f0000\"", "_attachments": "attachments/", "_ts": 1752169631}, {"payPeriodId": "1050108510193051", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-06-08T00:00:00Z", "endDate": "2025-06-14T00:00:00Z", "submitByDate": "2025-06-18T00:00:00Z", "checkDate": "2025-06-20T00:00:00Z", "checkCount": 0, "id": "93e681fc-f367-44ea-a016-969e61972ada", "companyId": "14100438", "type": "payperiod", "_rid": "NmJkAKiCbEeFUgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeFUgEAAAAAAA==/", "_etag": "\"9e009e64-0000-0100-0000-686ffc9f0000\"", "_attachments": "attachments/", "_ts": 1752169631}, {"payPeriodId": "1050108807713788", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-06-15T00:00:00Z", "endDate": "2025-06-21T00:00:00Z", "submitByDate": "2025-06-25T00:00:00Z", "checkDate": "2025-06-27T00:00:00Z", "checkCount": 0, "id": "921c95f0-a206-4981-8789-48ff9abce654", "companyId": "14100438", "type": "payperiod", "_rid": "NmJkAKiCbEeGUgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeGUgEAAAAAAA==/", "_etag": "\"9e00a064-0000-0100-0000-686ffc9f0000\"", "_attachments": "attachments/", "_ts": 1752169631}, {"payPeriodId": "1050109102138266", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-06-22T00:00:00Z", "endDate": "2025-06-28T00:00:00Z", "submitByDate": "2025-07-01T00:00:00Z", "checkDate": "2025-07-03T00:00:00Z", "checkCount": 0, "id": "e707ce6a-9076-488f-aeb7-5766e73a829c", "companyId": "14100438", "type": "payperiod", "_rid": "NmJkAKiCbEeHUgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeHUgEAAAAAAA==/", "_etag": "\"9e00a364-0000-0100-0000-686ffc9f0000\"", "_attachments": "attachments/", "_ts": 1752169631}, {"payPeriodId": "1050109358095878", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-06-29T00:00:00Z", "endDate": "2025-07-05T00:00:00Z", "submitByDate": "2025-07-09T00:00:00Z", "checkDate": "2025-07-11T00:00:00Z", "checkCount": 0, "id": "b107606a-5093-48f9-b560-861ef2b59da7", "companyId": "14100438", "type": "payperiod", "_rid": "NmJkAKiCbEeIUgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeIUgEAAAAAAA==/", "_etag": "\"9e00a564-0000-0100-0000-686ffca00000\"", "_attachments": "attachments/", "_ts": 1752169632}, {"payPeriodId": "1050109592368855", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-07-06T00:00:00Z", "endDate": "2025-07-12T00:00:00Z", "submitByDate": "2025-07-16T00:00:00Z", "checkDate": "2025-07-18T00:00:00Z", "checkCount": 0, "id": "118d7fb6-ef79-4718-8aa5-bc29b070c467", "companyId": "14100438", "type": "payperiod", "_rid": "NmJkAKiCbEeJUgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeJUgEAAAAAAA==/", "_etag": "\"9e00a864-0000-0100-0000-686ffca00000\"", "_attachments": "attachments/", "_ts": 1752169632}, {"payPeriodId": "1050109823056314", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-07-13T00:00:00Z", "endDate": "2025-07-19T00:00:00Z", "submitByDate": "2025-07-23T00:00:00Z", "checkDate": "2025-07-25T00:00:00Z", "checkCount": 0, "id": "85db295b-3e10-4afd-9e4f-dbe6368cc32a", "companyId": "14100438", "type": "payperiod", "_rid": "NmJkAKiCbEeKUgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeKUgEAAAAAAA==/", "_etag": "\"9e00ad64-0000-0100-0000-686ffca00000\"", "_attachments": "attachments/", "_ts": 1752169632}, {"payPeriodId": "1050110032614018", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-07-20T00:00:00Z", "endDate": "2025-07-26T00:00:00Z", "submitByDate": "2025-07-30T00:00:00Z", "checkDate": "2025-08-01T00:00:00Z", "checkCount": 0, "id": "182e9c08-0ec3-4c6f-bf82-847c488574fe", "companyId": "14100438", "type": "payperiod", "_rid": "NmJkAKiCbEeLUgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeLUgEAAAAAAA==/", "_etag": "\"9e00af64-0000-0100-0000-686ffca00000\"", "_attachments": "attachments/", "_ts": 1752169632}, {"payPeriodId": "1050110152016737", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-07-27T00:00:00Z", "endDate": "2025-08-02T00:00:00Z", "submitByDate": "2025-08-06T00:00:00Z", "checkDate": "2025-08-08T00:00:00Z", "checkCount": 0, "id": "81024c42-dba5-485e-9f64-dac2b17dbd3b", "companyId": "14100438", "type": "payperiod", "_rid": "NmJkAKiCbEeMUgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeMUgEAAAAAAA==/", "_etag": "\"9e00b164-0000-0100-0000-686ffca00000\"", "_attachments": "attachments/", "_ts": 1752169632}, {"payPeriodId": "1050110525635123", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-08-03T00:00:00Z", "endDate": "2025-08-09T00:00:00Z", "submitByDate": "2025-08-13T00:00:00Z", "checkDate": "2025-08-15T00:00:00Z", "checkCount": 0, "id": "be46cd63-0a75-4135-be3d-8b0177a445ac", "companyId": "14100438", "type": "payperiod", "_rid": "NmJkAKiCbEeNUgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeNUgEAAAAAAA==/", "_etag": "\"9e00b364-0000-0100-0000-686ffca00000\"", "_attachments": "attachments/", "_ts": 1752169632}, {"payPeriodId": "1050110809587977", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-08-10T00:00:00Z", "endDate": "2025-08-16T00:00:00Z", "submitByDate": "2025-08-20T00:00:00Z", "checkDate": "2025-08-22T00:00:00Z", "checkCount": 0, "id": "6ceb2412-2a44-4a8d-aaad-0d5283fcaaf3", "companyId": "14100438", "type": "payperiod", "_rid": "NmJkAKiCbEeOUgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeOUgEAAAAAAA==/", "_etag": "\"9e00b764-0000-0100-0000-686ffca00000\"", "_attachments": "attachments/", "_ts": 1752169632}, {"payPeriodId": "1050111006406491", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-08-17T00:00:00Z", "endDate": "2025-08-23T00:00:00Z", "submitByDate": "2025-08-27T00:00:00Z", "checkDate": "2025-08-29T00:00:00Z", "checkCount": 0, "id": "057a5d75-0545-4472-9386-045dfaea330c", "companyId": "14100438", "type": "payperiod", "_rid": "NmJkAKiCbEePUgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEePUgEAAAAAAA==/", "_etag": "\"9e00b964-0000-0100-0000-686ffca00000\"", "_attachments": "attachments/", "_ts": 1752169632}, {"payPeriodId": "1050111253888989", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-08-24T00:00:00Z", "endDate": "2025-08-30T00:00:00Z", "submitByDate": "2025-09-03T00:00:00Z", "checkDate": "2025-09-05T00:00:00Z", "checkCount": 0, "id": "89d909e6-3b47-4c9e-84cd-896c1315534c", "companyId": "14100438", "type": "payperiod", "_rid": "NmJkAKiCbEeQUgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeQUgEAAAAAAA==/", "_etag": "\"9e00bc64-0000-0100-0000-686ffca00000\"", "_attachments": "attachments/", "_ts": 1752169632}, {"payPeriodId": "1050111512541459", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-08-31T00:00:00Z", "endDate": "2025-09-06T00:00:00Z", "submitByDate": "2025-09-10T00:00:00Z", "checkDate": "2025-09-12T00:00:00Z", "checkCount": 0, "id": "7f1b2fca-6d2c-4c16-ba13-9d79515b7c08", "companyId": "14100438", "type": "payperiod", "_rid": "NmJkAKiCbEeRUgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeRUgEAAAAAAA==/", "_etag": "\"9e00c164-0000-0100-0000-686ffca00000\"", "_attachments": "attachments/", "_ts": 1752169632}, {"payPeriodId": "1050111680108966", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-09-07T00:00:00Z", "endDate": "2025-09-13T00:00:00Z", "submitByDate": "2025-09-17T00:00:00Z", "checkDate": "2025-09-19T00:00:00Z", "checkCount": 0, "id": "84af0e08-26e2-4df2-8d23-7209fd36642b", "companyId": "14100438", "type": "payperiod", "_rid": "NmJkAKiCbEeSUgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeSUgEAAAAAAA==/", "_etag": "\"9e00c364-0000-0100-0000-686ffca00000\"", "_attachments": "attachments/", "_ts": 1752169632}, {"payPeriodId": "1050111901136327", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-09-14T00:00:00Z", "endDate": "2025-09-20T00:00:00Z", "submitByDate": "2025-09-24T00:00:00Z", "checkDate": "2025-09-26T00:00:00Z", "checkCount": 0, "id": "e663830e-b30c-415b-bd72-89e8a787432b", "companyId": "14100438", "type": "payperiod", "_rid": "NmJkAKiCbEeTUgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeTUgEAAAAAAA==/", "_etag": "\"9e00c664-0000-0100-0000-686ffca00000\"", "_attachments": "attachments/", "_ts": 1752169632}, {"payPeriodId": "1050112256925449", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-09-21T00:00:00Z", "endDate": "2025-09-27T00:00:00Z", "submitByDate": "2025-10-01T00:00:00Z", "checkDate": "2025-10-03T00:00:00Z", "checkCount": 0, "id": "6be123a4-b152-46ad-a99f-c441279273d3", "companyId": "14100438", "type": "payperiod", "_rid": "NmJkAKiCbEeUUgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeUUgEAAAAAAA==/", "_etag": "\"9e00cb64-0000-0100-0000-686ffca00000\"", "_attachments": "attachments/", "_ts": 1752169632}, {"payPeriodId": "1050112405097953", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-09-28T00:00:00Z", "endDate": "2025-10-04T00:00:00Z", "submitByDate": "2025-10-08T00:00:00Z", "checkDate": "2025-10-10T00:00:00Z", "checkCount": 0, "id": "96818f6c-26c8-41fd-9eb9-9ea9f9c4ac34", "companyId": "14100438", "type": "payperiod", "_rid": "NmJkAKiCbEeVUgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeVUgEAAAAAAA==/", "_etag": "\"9e00d064-0000-0100-0000-686ffca10000\"", "_attachments": "attachments/", "_ts": 1752169633}, {"payPeriodId": "1090066289487240", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2024-12-29T00:00:00Z", "endDate": "2025-01-04T00:00:00Z", "submitByDate": "2025-01-08T00:00:00Z", "checkDate": "2025-01-10T00:00:00Z", "checkCount": 5, "id": "87c0cb78-8f63-4b01-b0ed-ade0b3488ab1", "companyId": "14100438", "type": "payperiod", "_rid": "NmJkAKiCbEecUgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEecUgEAAAAAAA==/", "_etag": "\"9e00e964-0000-0100-0000-686ffca10000\"", "_attachments": "attachments/", "_ts": 1752169633}, {"payPeriodId": "1090066289487241", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-01-05T00:00:00Z", "endDate": "2025-01-11T00:00:00Z", "submitByDate": "2025-01-15T00:00:00Z", "checkDate": "2025-01-17T00:00:00Z", "checkCount": 5, "id": "3f61ceb0-f928-440d-afbe-4dcb2b29f462", "companyId": "14100438", "type": "payperiod", "_rid": "NmJkAKiCbEedUgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEedUgEAAAAAAA==/", "_etag": "\"9e00ec64-0000-0100-0000-686ffca10000\"", "_attachments": "attachments/", "_ts": 1752169633}, {"payPeriodId": "1050103597057267", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-01-12T00:00:00Z", "endDate": "2025-01-18T00:00:00Z", "submitByDate": "2025-01-22T00:00:00Z", "checkDate": "2025-01-24T00:00:00Z", "checkCount": 6, "id": "fb436086-ef7d-4e20-9a04-111dafcbc720", "companyId": "14100438", "type": "payperiod", "_rid": "NmJkAKiCbEeeUgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeeUgEAAAAAAA==/", "_etag": "\"9e00f364-0000-0100-0000-686ffca10000\"", "_attachments": "attachments/", "_ts": 1752169633}, {"payPeriodId": "1050107004769802", "status": "COMPLETED", "description": "payroll correction", "startDate": "2025-01-12T00:00:00Z", "endDate": "2025-01-18T00:00:00Z", "submitByDate": "2025-01-23T00:00:00Z", "checkDate": "2025-01-24T00:00:00Z", "checkCount": 1, "id": "7d3d25ae-9833-460a-9250-c2dbd737e954", "companyId": "14100438", "type": "payperiod", "_rid": "NmJkAKiCbEefUgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEefUgEAAAAAAA==/", "_etag": "\"9e00f664-0000-0100-0000-686ffca10000\"", "_attachments": "attachments/", "_ts": 1752169633}, {"payPeriodId": "1050103626234586", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-01-19T00:00:00Z", "endDate": "2025-01-25T00:00:00Z", "submitByDate": "2025-01-29T00:00:00Z", "checkDate": "2025-01-31T00:00:00Z", "checkCount": 5, "id": "886443e6-e242-48d6-accc-6983b1f75bb2", "companyId": "14100438", "type": "payperiod", "_rid": "NmJkAKiCbEegUgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEegUgEAAAAAAA==/", "_etag": "\"9e00f864-0000-0100-0000-686ffca10000\"", "_attachments": "attachments/", "_ts": 1752169633}, {"payPeriodId": "1050104053099845", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-01-26T00:00:00Z", "endDate": "2025-02-01T00:00:00Z", "submitByDate": "2025-02-05T00:00:00Z", "checkDate": "2025-02-07T00:00:00Z", "checkCount": 5, "id": "d49252b5-f384-41dd-93cb-77bacd258f90", "companyId": "14100438", "type": "payperiod", "_rid": "NmJkAKiCbEehUgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEehUgEAAAAAAA==/", "_etag": "\"9e00fe64-0000-0100-0000-686ffca20000\"", "_attachments": "attachments/", "_ts": 1752169634}, {"payPeriodId": "1050104144656671", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-02-02T00:00:00Z", "endDate": "2025-02-08T00:00:00Z", "submitByDate": "2025-02-12T00:00:00Z", "checkDate": "2025-02-14T00:00:00Z", "checkCount": 5, "id": "81f923e3-fa03-438b-a4ce-757fcbb5e545", "companyId": "14100438", "type": "payperiod", "_rid": "NmJkAKiCbEeiUgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeiUgEAAAAAAA==/", "_etag": "\"9e000365-0000-0100-0000-686ffca20000\"", "_attachments": "attachments/", "_ts": 1752169634}, {"payPeriodId": "1050104542114198", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-02-09T00:00:00Z", "endDate": "2025-02-15T00:00:00Z", "submitByDate": "2025-02-19T00:00:00Z", "checkDate": "2025-02-21T00:00:00Z", "checkCount": 5, "id": "30e8f195-9422-4b5d-b3f3-5de9d53413b8", "companyId": "14100438", "type": "payperiod", "_rid": "NmJkAKiCbEejUgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEejUgEAAAAAAA==/", "_etag": "\"9e000465-0000-0100-0000-686ffca20000\"", "_attachments": "attachments/", "_ts": 1752169634}, {"payPeriodId": "1050104745588762", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-02-16T00:00:00Z", "endDate": "2025-02-22T00:00:00Z", "submitByDate": "2025-02-26T00:00:00Z", "checkDate": "2025-02-28T00:00:00Z", "checkCount": 5, "id": "2721f494-3424-4343-addc-2b9bee46f6ba", "companyId": "14100438", "type": "payperiod", "_rid": "NmJkAKiCbEekUgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEekUgEAAAAAAA==/", "_etag": "\"9e000865-0000-0100-0000-686ffca20000\"", "_attachments": "attachments/", "_ts": 1752169634}, {"payPeriodId": "1050104908177422", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-02-23T00:00:00Z", "endDate": "2025-03-01T00:00:00Z", "submitByDate": "2025-03-05T00:00:00Z", "checkDate": "2025-03-07T00:00:00Z", "checkCount": 5, "id": "ae6c1a50-2e8d-4aa0-9605-3ef0f019bf34", "companyId": "14100438", "type": "payperiod", "_rid": "NmJkAKiCbEelUgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEelUgEAAAAAAA==/", "_etag": "\"9e000a65-0000-0100-0000-686ffca20000\"", "_attachments": "attachments/", "_ts": 1752169634}, {"payPeriodId": "1050105242086994", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-03-02T00:00:00Z", "endDate": "2025-03-08T00:00:00Z", "submitByDate": "2025-03-12T00:00:00Z", "checkDate": "2025-03-14T00:00:00Z", "checkCount": 5, "id": "7a816a56-e86e-45e3-b759-fec1c86e85b4", "companyId": "14100438", "type": "payperiod", "_rid": "NmJkAKiCbEemUgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEemUgEAAAAAAA==/", "_etag": "\"9e000b65-0000-0100-0000-686ffca20000\"", "_attachments": "attachments/", "_ts": 1752169634}, {"payPeriodId": "1050105516922502", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-03-09T00:00:00Z", "endDate": "2025-03-15T00:00:00Z", "submitByDate": "2025-03-19T00:00:00Z", "checkDate": "2025-03-21T00:00:00Z", "checkCount": 5, "id": "b44afdc5-7f2e-49f3-95d0-8db8dff512d3", "companyId": "14100438", "type": "payperiod", "_rid": "NmJkAKiCbEenUgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEenUgEAAAAAAA==/", "_etag": "\"9e000e65-0000-0100-0000-686ffca20000\"", "_attachments": "attachments/", "_ts": 1752169634}, {"payPeriodId": "1050105776014225", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-03-16T00:00:00Z", "endDate": "2025-03-22T00:00:00Z", "submitByDate": "2025-03-26T00:00:00Z", "checkDate": "2025-03-28T00:00:00Z", "checkCount": 5, "id": "e56ce759-d942-44b0-a269-eda68c11d324", "companyId": "14100438", "type": "payperiod", "_rid": "NmJkAKiCbEeoUgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeoUgEAAAAAAA==/", "_etag": "\"9e001165-0000-0100-0000-686ffca20000\"", "_attachments": "attachments/", "_ts": 1752169634}, {"payPeriodId": "1050105870703820", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-03-23T00:00:00Z", "endDate": "2025-03-29T00:00:00Z", "submitByDate": "2025-04-02T00:00:00Z", "checkDate": "2025-04-04T00:00:00Z", "checkCount": 5, "id": "cac41430-0acd-4d0e-9469-7bae0e7484c8", "companyId": "14100438", "type": "payperiod", "_rid": "NmJkAKiCbEepUgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEepUgEAAAAAAA==/", "_etag": "\"9e001365-0000-0100-0000-686ffca20000\"", "_attachments": "attachments/", "_ts": 1752169634}, {"payPeriodId": "1050106248360341", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-03-30T00:00:00Z", "endDate": "2025-04-05T00:00:00Z", "submitByDate": "2025-04-09T00:00:00Z", "checkDate": "2025-04-11T00:00:00Z", "checkCount": 5, "id": "131c1ea3-c254-4025-8fb3-eba414780e70", "companyId": "14100438", "type": "payperiod", "_rid": "NmJkAKiCbEeqUgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeqUgEAAAAAAA==/", "_etag": "\"9e001465-0000-0100-0000-686ffca20000\"", "_attachments": "attachments/", "_ts": 1752169634}, {"payPeriodId": "1050106497036762", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-04-06T00:00:00Z", "endDate": "2025-04-12T00:00:00Z", "submitByDate": "2025-04-16T00:00:00Z", "checkDate": "2025-04-18T00:00:00Z", "checkCount": 5, "id": "c6ab76fc-0728-49ed-b984-5e94fbe4b53b", "companyId": "14100438", "type": "payperiod", "_rid": "NmJkAKiCbEerUgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEerUgEAAAAAAA==/", "_etag": "\"9e001d65-0000-0100-0000-686ffca20000\"", "_attachments": "attachments/", "_ts": 1752169634}, {"payPeriodId": "1050106754428367", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-04-13T00:00:00Z", "endDate": "2025-04-19T00:00:00Z", "submitByDate": "2025-04-23T00:00:00Z", "checkDate": "2025-04-25T00:00:00Z", "checkCount": 5, "id": "32746128-77d8-46f9-ad8f-a5183997a86e", "companyId": "14100438", "type": "payperiod", "_rid": "NmJkAKiCbEesUgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEesUgEAAAAAAA==/", "_etag": "\"9e001e65-0000-0100-0000-686ffca20000\"", "_attachments": "attachments/", "_ts": 1752169634}, {"payPeriodId": "1050106837646431", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-04-20T00:00:00Z", "endDate": "2025-04-26T00:00:00Z", "submitByDate": "2025-04-30T00:00:00Z", "checkDate": "2025-05-02T00:00:00Z", "checkCount": 3, "id": "c68fd8d0-f52b-43f9-8ec3-74e4a0d866fc", "companyId": "14100438", "type": "payperiod", "_rid": "NmJkAKiCbEetUgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEetUgEAAAAAAA==/", "_etag": "\"9e002265-0000-0100-0000-686ffca20000\"", "_attachments": "attachments/", "_ts": 1752169634}, {"payPeriodId": "1050110393010694", "status": "COMPLETED", "description": "correction", "startDate": "2025-04-20T00:00:00Z", "endDate": "2025-04-26T00:00:00Z", "submitByDate": "2025-05-01T00:00:00Z", "checkDate": "2025-05-02T00:00:00Z", "checkCount": 2, "id": "107b9bce-593c-4ace-93eb-12bfc01c0920", "companyId": "14100438", "type": "payperiod", "_rid": "NmJkAKiCbEeuUgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeuUgEAAAAAAA==/", "_etag": "\"9e002665-0000-0100-0000-686ffca30000\"", "_attachments": "attachments/", "_ts": 1752169635}, {"payPeriodId": "1050107249807616", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-04-27T00:00:00Z", "endDate": "2025-05-03T00:00:00Z", "submitByDate": "2025-05-07T00:00:00Z", "checkDate": "2025-05-09T00:00:00Z", "checkCount": 5, "id": "b4487b65-300f-425f-aa76-bf99149afe7a", "companyId": "14100438", "type": "payperiod", "_rid": "NmJkAKiCbEevUgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEevUgEAAAAAAA==/", "_etag": "\"9e002865-0000-0100-0000-686ffca30000\"", "_attachments": "attachments/", "_ts": 1752169635}, {"payPeriodId": "1050107283363063", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-05-04T00:00:00Z", "endDate": "2025-05-10T00:00:00Z", "submitByDate": "2025-05-14T00:00:00Z", "checkDate": "2025-05-16T00:00:00Z", "checkCount": 5, "id": "cdd92fbb-1763-441e-b787-2b4ab39d2fdf", "companyId": "14100438", "type": "payperiod", "_rid": "NmJkAKiCbEewUgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEewUgEAAAAAAA==/", "_etag": "\"9e002b65-0000-0100-0000-686ffca30000\"", "_attachments": "attachments/", "_ts": 1752169635}, {"payPeriodId": "1050107639742607", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-05-11T00:00:00Z", "endDate": "2025-05-17T00:00:00Z", "submitByDate": "2025-05-21T00:00:00Z", "checkDate": "2025-05-23T00:00:00Z", "checkCount": 5, "id": "880c9a25-46ca-4f70-99ec-b0711221f8e9", "companyId": "14100438", "type": "payperiod", "_rid": "NmJkAKiCbEexUgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEexUgEAAAAAAA==/", "_etag": "\"9e002d65-0000-0100-0000-686ffca30000\"", "_attachments": "attachments/", "_ts": 1752169635}, {"payPeriodId": "1050107856369794", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-05-18T00:00:00Z", "endDate": "2025-05-24T00:00:00Z", "submitByDate": "2025-05-28T00:00:00Z", "checkDate": "2025-05-30T00:00:00Z", "checkCount": 5, "id": "bf1c8cf5-ddf0-474e-bb20-0554ac096cf8", "companyId": "14100438", "type": "payperiod", "_rid": "NmJkAKiCbEeyUgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeyUgEAAAAAAA==/", "_etag": "\"9e003165-0000-0100-0000-686ffca30000\"", "_attachments": "attachments/", "_ts": 1752169635}, {"payPeriodId": "1050108157540182", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-05-25T00:00:00Z", "endDate": "2025-05-31T00:00:00Z", "submitByDate": "2025-06-04T00:00:00Z", "checkDate": "2025-06-06T00:00:00Z", "checkCount": 5, "id": "b1eb3398-c963-452e-9330-8daad398b600", "companyId": "14100438", "type": "payperiod", "_rid": "NmJkAKiCbEezUgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEezUgEAAAAAAA==/", "_etag": "\"9e003365-0000-0100-0000-686ffca30000\"", "_attachments": "attachments/", "_ts": 1752169635}, {"payPeriodId": "1050108277749688", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-06-01T00:00:00Z", "endDate": "2025-06-07T00:00:00Z", "submitByDate": "2025-06-11T00:00:00Z", "checkDate": "2025-06-13T00:00:00Z", "checkCount": 5, "id": "30ff8cee-93ef-49c4-bd14-0628da7167c1", "companyId": "14100438", "type": "payperiod", "_rid": "NmJkAKiCbEe0UgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEe0UgEAAAAAAA==/", "_etag": "\"9e003465-0000-0100-0000-686ffca30000\"", "_attachments": "attachments/", "_ts": 1752169635}, {"payPeriodId": "1050108510193051", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-06-08T00:00:00Z", "endDate": "2025-06-14T00:00:00Z", "submitByDate": "2025-06-18T00:00:00Z", "checkDate": "2025-06-20T00:00:00Z", "checkCount": 5, "id": "413a5d04-82c6-4461-b6c4-72cd51a57434", "companyId": "14100438", "type": "payperiod", "_rid": "NmJkAKiCbEe1UgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEe1UgEAAAAAAA==/", "_etag": "\"9e003865-0000-0100-0000-686ffca30000\"", "_attachments": "attachments/", "_ts": 1752169635}, {"payPeriodId": "1050108807713788", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-06-15T00:00:00Z", "endDate": "2025-06-21T00:00:00Z", "submitByDate": "2025-06-25T00:00:00Z", "checkDate": "2025-06-27T00:00:00Z", "checkCount": 5, "id": "69146ce9-39f6-4d70-9bee-afe15d88e483", "companyId": "14100438", "type": "payperiod", "_rid": "NmJkAKiCbEe2UgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEe2UgEAAAAAAA==/", "_etag": "\"9e003a65-0000-0100-0000-686ffca30000\"", "_attachments": "attachments/", "_ts": 1752169635}, {"payPeriodId": "1050109102138266", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-06-22T00:00:00Z", "endDate": "2025-06-28T00:00:00Z", "submitByDate": "2025-07-01T00:00:00Z", "checkDate": "2025-07-03T00:00:00Z", "checkCount": 5, "id": "1ebbd293-af37-400d-aa46-7ed2553cc729", "companyId": "14100438", "type": "payperiod", "_rid": "NmJkAKiCbEe3UgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEe3UgEAAAAAAA==/", "_etag": "\"9e003d65-0000-0100-0000-686ffca30000\"", "_attachments": "attachments/", "_ts": 1752169635}, {"payPeriodId": "1050109358095878", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-06-29T00:00:00Z", "endDate": "2025-07-05T00:00:00Z", "submitByDate": "2025-07-09T00:00:00Z", "checkDate": "2025-07-11T00:00:00Z", "checkCount": 0, "id": "89324224-5467-41e4-a62d-1022196cb59b", "companyId": "14100438", "type": "payperiod", "_rid": "NmJkAKiCbEe4UgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEe4UgEAAAAAAA==/", "_etag": "\"9e003f65-0000-0100-0000-686ffca30000\"", "_attachments": "attachments/", "_ts": 1752169635}, {"payPeriodId": "1050109592368855", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-07-06T00:00:00Z", "endDate": "2025-07-12T00:00:00Z", "submitByDate": "2025-07-16T00:00:00Z", "checkDate": "2025-07-18T00:00:00Z", "checkCount": 0, "id": "a36d6579-2a95-4784-8ad3-5aa87665e49d", "companyId": "14100438", "type": "payperiod", "_rid": "NmJkAKiCbEe5UgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEe5UgEAAAAAAA==/", "_etag": "\"9e004265-0000-0100-0000-686ffca30000\"", "_attachments": "attachments/", "_ts": 1752169635}, {"payPeriodId": "1050109823056314", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-07-13T00:00:00Z", "endDate": "2025-07-19T00:00:00Z", "submitByDate": "2025-07-23T00:00:00Z", "checkDate": "2025-07-25T00:00:00Z", "checkCount": 0, "id": "c841b05c-2887-4f70-b468-2f2c596b7c68", "companyId": "14100438", "type": "payperiod", "_rid": "NmJkAKiCbEe6UgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEe6UgEAAAAAAA==/", "_etag": "\"9e004465-0000-0100-0000-686ffca30000\"", "_attachments": "attachments/", "_ts": 1752169635}, {"payPeriodId": "1050110032614018", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-07-20T00:00:00Z", "endDate": "2025-07-26T00:00:00Z", "submitByDate": "2025-07-30T00:00:00Z", "checkDate": "2025-08-01T00:00:00Z", "checkCount": 0, "id": "31bef9cc-d6b9-4dd5-8fa8-bd5dc0121cd0", "companyId": "14100438", "type": "payperiod", "_rid": "NmJkAKiCbEe7UgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEe7UgEAAAAAAA==/", "_etag": "\"9e004665-0000-0100-0000-686ffca40000\"", "_attachments": "attachments/", "_ts": 1752169636}, {"payPeriodId": "1050110152016737", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-07-27T00:00:00Z", "endDate": "2025-08-02T00:00:00Z", "submitByDate": "2025-08-06T00:00:00Z", "checkDate": "2025-08-08T00:00:00Z", "checkCount": 0, "id": "e7c0c7d0-e6f6-4893-ab69-8674c27c32c7", "companyId": "14100438", "type": "payperiod", "_rid": "NmJkAKiCbEe8UgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEe8UgEAAAAAAA==/", "_etag": "\"9e004a65-0000-0100-0000-686ffca40000\"", "_attachments": "attachments/", "_ts": 1752169636}, {"payPeriodId": "1050110525635123", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-08-03T00:00:00Z", "endDate": "2025-08-09T00:00:00Z", "submitByDate": "2025-08-13T00:00:00Z", "checkDate": "2025-08-15T00:00:00Z", "checkCount": 0, "id": "e444982d-137d-466d-8e98-5d405563f02f", "companyId": "14100438", "type": "payperiod", "_rid": "NmJkAKiCbEe9UgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEe9UgEAAAAAAA==/", "_etag": "\"9e004e65-0000-0100-0000-686ffca40000\"", "_attachments": "attachments/", "_ts": 1752169636}, {"payPeriodId": "1050110809587977", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-08-10T00:00:00Z", "endDate": "2025-08-16T00:00:00Z", "submitByDate": "2025-08-20T00:00:00Z", "checkDate": "2025-08-22T00:00:00Z", "checkCount": 0, "id": "15643aad-4c8d-4137-afba-441b2e0dd992", "companyId": "14100438", "type": "payperiod", "_rid": "NmJkAKiCbEe+UgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEe+UgEAAAAAAA==/", "_etag": "\"9e005065-0000-0100-0000-686ffca40000\"", "_attachments": "attachments/", "_ts": 1752169636}, {"payPeriodId": "1050111006406491", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-08-17T00:00:00Z", "endDate": "2025-08-23T00:00:00Z", "submitByDate": "2025-08-27T00:00:00Z", "checkDate": "2025-08-29T00:00:00Z", "checkCount": 0, "id": "95c709a8-2b13-48d6-aadf-575c6f95d153", "companyId": "14100438", "type": "payperiod", "_rid": "NmJkAKiCbEe-UgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEe-UgEAAAAAAA==/", "_etag": "\"9e005465-0000-0100-0000-686ffca40000\"", "_attachments": "attachments/", "_ts": 1752169636}, {"payPeriodId": "1050111253888989", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-08-24T00:00:00Z", "endDate": "2025-08-30T00:00:00Z", "submitByDate": "2025-09-03T00:00:00Z", "checkDate": "2025-09-05T00:00:00Z", "checkCount": 0, "id": "2dcc6380-2b62-4f16-8556-e9771a6f7c4d", "companyId": "14100438", "type": "payperiod", "_rid": "NmJkAKiCbEfAUgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfAUgEAAAAAAA==/", "_etag": "\"9e005765-0000-0100-0000-686ffca40000\"", "_attachments": "attachments/", "_ts": 1752169636}, {"payPeriodId": "1050111512541459", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-08-31T00:00:00Z", "endDate": "2025-09-06T00:00:00Z", "submitByDate": "2025-09-10T00:00:00Z", "checkDate": "2025-09-12T00:00:00Z", "checkCount": 0, "id": "a17f1624-72f2-4851-95ce-80062561778e", "companyId": "14100438", "type": "payperiod", "_rid": "NmJkAKiCbEfBUgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfBUgEAAAAAAA==/", "_etag": "\"9e005b65-0000-0100-0000-686ffca40000\"", "_attachments": "attachments/", "_ts": 1752169636}, {"payPeriodId": "1050111680108966", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-09-07T00:00:00Z", "endDate": "2025-09-13T00:00:00Z", "submitByDate": "2025-09-17T00:00:00Z", "checkDate": "2025-09-19T00:00:00Z", "checkCount": 0, "id": "343b270f-7a4c-4e8e-a7d0-36071f8b3b9a", "companyId": "14100438", "type": "payperiod", "_rid": "NmJkAKiCbEfCUgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfCUgEAAAAAAA==/", "_etag": "\"9e005f65-0000-0100-0000-686ffca40000\"", "_attachments": "attachments/", "_ts": 1752169636}, {"payPeriodId": "1050111901136327", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-09-14T00:00:00Z", "endDate": "2025-09-20T00:00:00Z", "submitByDate": "2025-09-24T00:00:00Z", "checkDate": "2025-09-26T00:00:00Z", "checkCount": 0, "id": "b5d85aa0-eb74-4704-ad81-1b60866d2d66", "companyId": "14100438", "type": "payperiod", "_rid": "NmJkAKiCbEfDUgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfDUgEAAAAAAA==/", "_etag": "\"9e006365-0000-0100-0000-686ffca40000\"", "_attachments": "attachments/", "_ts": 1752169636}, {"payPeriodId": "1050112256925449", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-09-21T00:00:00Z", "endDate": "2025-09-27T00:00:00Z", "submitByDate": "2025-10-01T00:00:00Z", "checkDate": "2025-10-03T00:00:00Z", "checkCount": 0, "id": "67287950-d374-4aa8-a0be-1b0b0f26cc93", "companyId": "14100438", "type": "payperiod", "_rid": "NmJkAKiCbEfEUgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfEUgEAAAAAAA==/", "_etag": "\"9e006565-0000-0100-0000-686ffca40000\"", "_attachments": "attachments/", "_ts": 1752169636}, {"payPeriodId": "1050112405097953", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-09-28T00:00:00Z", "endDate": "2025-10-04T00:00:00Z", "submitByDate": "2025-10-08T00:00:00Z", "checkDate": "2025-10-10T00:00:00Z", "checkCount": 0, "id": "10b8fcee-d7d3-4e58-bdfe-e1b4ed3cec36", "companyId": "14100438", "type": "payperiod", "_rid": "NmJkAKiCbEfFUgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfFUgEAAAAAAA==/", "_etag": "\"9e006765-0000-0100-0000-686ffca40000\"", "_attachments": "attachments/", "_ts": 1752169636}], "links": [{"rel": "self", "href": "https://ca-payroll-ai-mock-sb-001-secure.nicebeach-8a7a52ab.eastus.azurecontainerapps.io/ca/companies/14100438/payperiods"}]}, "status_code": 200}