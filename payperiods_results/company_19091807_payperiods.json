{"success": true, "company_id": "19091807", "data": {"metadata": {"contentItemCount": 18}, "content": [{"payPeriodId": "1140035051818261", "intervalCode": "MONTHLY", "status": "COMPLETED", "description": "Monthly Payroll (2)", "startDate": "2025-01-01T00:00:00Z", "endDate": "2025-01-31T00:00:00Z", "submitByDate": "2025-02-06T00:00:00Z", "checkDate": "2025-02-10T00:00:00Z", "checkCount": 3, "id": "d9ab4dd8-0d39-457b-9fe2-96d81d222ec4", "companyId": "19091807", "type": "payperiod", "_rid": "NmJkAKiCbEfGaQQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfGaQQAAAAAAA==/", "_etag": "\"a80088a6-0000-0100-0000-68703fa30000\"", "_attachments": "attachments/", "_ts": 1752186787}, {"payPeriodId": "1140035197985503", "intervalCode": "MONTHLY", "status": "COMPLETED", "description": "Monthly Payroll (2)", "startDate": "2025-02-01T00:00:00Z", "endDate": "2025-02-28T00:00:00Z", "submitByDate": "2025-03-06T00:00:00Z", "checkDate": "2025-03-10T00:00:00Z", "checkCount": 3, "id": "6e4849b2-ec27-4e2e-b526-37635db63d10", "companyId": "19091807", "type": "payperiod", "_rid": "NmJkAKiCbEfHaQQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfHaQQAAAAAAA==/", "_etag": "\"a80089a6-0000-0100-0000-68703fa30000\"", "_attachments": "attachments/", "_ts": 1752186787}, {"payPeriodId": "1140035301954859", "intervalCode": "MONTHLY", "status": "INITIAL", "description": "Monthly Payroll (2)", "startDate": "2025-03-01T00:00:00Z", "endDate": "2025-03-31T00:00:00Z", "submitByDate": "2025-04-08T00:00:00Z", "checkDate": "2025-04-10T00:00:00Z", "checkCount": 0, "id": "3181283c-bace-45c7-a9f4-f05776436016", "companyId": "19091807", "type": "payperiod", "_rid": "NmJkAKiCbEfIaQQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfIaQQAAAAAAA==/", "_etag": "\"a8008aa6-0000-0100-0000-68703fa30000\"", "_attachments": "attachments/", "_ts": 1752186787}, {"payPeriodId": "1140035471100839", "intervalCode": "MONTHLY", "status": "INITIAL", "description": "Monthly Payroll (2)", "startDate": "2025-04-01T00:00:00Z", "endDate": "2025-04-30T00:00:00Z", "submitByDate": "2025-05-07T00:00:00Z", "checkDate": "2025-05-09T00:00:00Z", "checkCount": 0, "id": "bc519ad1-5c8f-4759-955f-1453491f0649", "companyId": "19091807", "type": "payperiod", "_rid": "NmJkAKiCbEfJaQQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfJaQQAAAAAAA==/", "_etag": "\"a8008ca6-0000-0100-0000-68703fa30000\"", "_attachments": "attachments/", "_ts": 1752186787}, {"payPeriodId": "1140035605586245", "intervalCode": "MONTHLY", "status": "INITIAL", "description": "Monthly Payroll (2)", "startDate": "2025-05-01T00:00:00Z", "endDate": "2025-05-31T00:00:00Z", "submitByDate": "2025-06-06T00:00:00Z", "checkDate": "2025-06-10T00:00:00Z", "checkCount": 0, "id": "495d8871-1857-43f8-afe9-e61b11b75e41", "companyId": "19091807", "type": "payperiod", "_rid": "NmJkAKiCbEfKaQQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfKaQQAAAAAAA==/", "_etag": "\"a8008ea6-0000-0100-0000-68703fa30000\"", "_attachments": "attachments/", "_ts": 1752186787}, {"payPeriodId": "1140035722124747", "intervalCode": "MONTHLY", "status": "INITIAL", "description": "Monthly Payroll (2)", "startDate": "2025-06-01T00:00:00Z", "endDate": "2025-06-30T00:00:00Z", "submitByDate": "2025-07-02T00:00:00Z", "checkDate": "2025-07-03T00:00:00Z", "checkCount": 0, "id": "ee821cf3-dc73-4403-8647-7268b1663c16", "companyId": "19091807", "type": "payperiod", "_rid": "NmJkAKiCbEfLaQQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfLaQQAAAAAAA==/", "_etag": "\"a80090a6-0000-0100-0000-68703fa30000\"", "_attachments": "attachments/", "_ts": 1752186787}, {"payPeriodId": "1140036220003937", "intervalCode": "MONTHLY", "status": "INITIAL", "description": "Monthly Payroll (3)", "startDate": "2025-07-01T00:00:00Z", "endDate": "2025-07-31T00:00:00Z", "submitByDate": "2025-08-01T00:00:00Z", "checkDate": "2025-08-05T00:00:00Z", "checkCount": 0, "id": "74105fe9-4374-4a7e-97ec-626cc32e4532", "companyId": "19091807", "type": "payperiod", "_rid": "NmJkAKiCbEfMaQQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfMaQQAAAAAAA==/", "_etag": "\"a80092a6-0000-0100-0000-68703fa30000\"", "_attachments": "attachments/", "_ts": 1752186787}, {"payPeriodId": "1140036220003940", "intervalCode": "MONTHLY", "status": "INITIAL", "description": "Monthly Payroll (3)", "startDate": "2025-08-01T00:00:00Z", "endDate": "2025-08-31T00:00:00Z", "submitByDate": "2025-09-03T00:00:00Z", "checkDate": "2025-09-05T00:00:00Z", "checkCount": 0, "id": "7168b25e-fb4f-476d-b477-e687dccda087", "companyId": "19091807", "type": "payperiod", "_rid": "NmJkAKiCbEfNaQQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfNaQQAAAAAAA==/", "_etag": "\"a80094a6-0000-0100-0000-68703fa30000\"", "_attachments": "attachments/", "_ts": 1752186787}, {"payPeriodId": "1140036220003943", "intervalCode": "MONTHLY", "status": "INITIAL", "description": "Monthly Payroll (3)", "startDate": "2025-09-01T00:00:00Z", "endDate": "2025-09-30T00:00:00Z", "submitByDate": "2025-10-01T00:00:00Z", "checkDate": "2025-10-03T00:00:00Z", "checkCount": 0, "id": "67cced32-91fd-40f6-85ae-dedd0ba17104", "companyId": "19091807", "type": "payperiod", "_rid": "NmJkAKiCbEfOaQQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfOaQQAAAAAAA==/", "_etag": "\"a80095a6-0000-0100-0000-68703fa30000\"", "_attachments": "attachments/", "_ts": 1752186787}, {"payPeriodId": "1140035051818261", "intervalCode": "MONTHLY", "status": "COMPLETED", "description": "Monthly Payroll (2)", "startDate": "2025-01-01T00:00:00Z", "endDate": "2025-01-31T00:00:00Z", "submitByDate": "2025-02-06T00:00:00Z", "checkDate": "2025-02-10T00:00:00Z", "checkCount": 3, "id": "6baa5442-bdf4-4ee8-8814-624eb83c338f", "companyId": "19091807", "type": "payperiod", "_rid": "NmJkAKiCbEfVaQQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfVaQQAAAAAAA==/", "_etag": "\"a800a4a6-0000-0100-0000-68703fa40000\"", "_attachments": "attachments/", "_ts": 1752186788}, {"payPeriodId": "1140035197985503", "intervalCode": "MONTHLY", "status": "COMPLETED", "description": "Monthly Payroll (2)", "startDate": "2025-02-01T00:00:00Z", "endDate": "2025-02-28T00:00:00Z", "submitByDate": "2025-03-06T00:00:00Z", "checkDate": "2025-03-10T00:00:00Z", "checkCount": 3, "id": "79fa0dac-f276-4c33-aff1-8cd92afd527f", "companyId": "19091807", "type": "payperiod", "_rid": "NmJkAKiCbEfWaQQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfWaQQAAAAAAA==/", "_etag": "\"a800a8a6-0000-0100-0000-68703fa40000\"", "_attachments": "attachments/", "_ts": 1752186788}, {"payPeriodId": "1140035301954859", "intervalCode": "MONTHLY", "status": "COMPLETED", "description": "Monthly Payroll (2)", "startDate": "2025-03-01T00:00:00Z", "endDate": "2025-03-31T00:00:00Z", "submitByDate": "2025-04-08T00:00:00Z", "checkDate": "2025-04-10T00:00:00Z", "checkCount": 3, "id": "946a3c75-8a5b-4fb4-aa15-8a6b7ac00b42", "companyId": "19091807", "type": "payperiod", "_rid": "NmJkAKiCbEfXaQQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfXaQQAAAAAAA==/", "_etag": "\"a800a9a6-0000-0100-0000-68703fa40000\"", "_attachments": "attachments/", "_ts": 1752186788}, {"payPeriodId": "1140035471100839", "intervalCode": "MONTHLY", "status": "COMPLETED", "description": "Monthly Payroll (2)", "startDate": "2025-04-01T00:00:00Z", "endDate": "2025-04-30T00:00:00Z", "submitByDate": "2025-05-07T00:00:00Z", "checkDate": "2025-05-09T00:00:00Z", "checkCount": 3, "id": "ed2afcb9-75b5-48b9-9573-024e2e0c8309", "companyId": "19091807", "type": "payperiod", "_rid": "NmJkAKiCbEfYaQQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfYaQQAAAAAAA==/", "_etag": "\"a800aaa6-0000-0100-0000-68703fa40000\"", "_attachments": "attachments/", "_ts": 1752186788}, {"payPeriodId": "1140035605586245", "intervalCode": "MONTHLY", "status": "COMPLETED", "description": "Monthly Payroll (2)", "startDate": "2025-05-01T00:00:00Z", "endDate": "2025-05-31T00:00:00Z", "submitByDate": "2025-06-06T00:00:00Z", "checkDate": "2025-06-10T00:00:00Z", "checkCount": 3, "id": "c12e3d59-a5cb-45f4-aab3-0683cffcb5b4", "companyId": "19091807", "type": "payperiod", "_rid": "NmJkAKiCbEfZaQQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfZaQQAAAAAAA==/", "_etag": "\"a800aba6-0000-0100-0000-68703fa40000\"", "_attachments": "attachments/", "_ts": 1752186788}, {"payPeriodId": "1140035722124747", "intervalCode": "MONTHLY", "status": "COMPLETED", "description": "Monthly Payroll (2)", "startDate": "2025-06-01T00:00:00Z", "endDate": "2025-06-30T00:00:00Z", "submitByDate": "2025-07-02T00:00:00Z", "checkDate": "2025-07-03T00:00:00Z", "checkCount": 3, "id": "1d97e063-73db-4dcc-bf06-8f69c9ebe2ed", "companyId": "19091807", "type": "payperiod", "_rid": "NmJkAKiCbEfaaQQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfaaQQAAAAAAA==/", "_etag": "\"a800aca6-0000-0100-0000-68703fa40000\"", "_attachments": "attachments/", "_ts": 1752186788}, {"payPeriodId": "1140036220003937", "intervalCode": "MONTHLY", "status": "INITIAL", "description": "Monthly Payroll (3)", "startDate": "2025-07-01T00:00:00Z", "endDate": "2025-07-31T00:00:00Z", "submitByDate": "2025-08-01T00:00:00Z", "checkDate": "2025-08-05T00:00:00Z", "checkCount": 0, "id": "fc7e566a-cf8e-4303-94a7-0dfd004fc6a1", "companyId": "19091807", "type": "payperiod", "_rid": "NmJkAKiCbEfbaQQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfbaQQAAAAAAA==/", "_etag": "\"a800afa6-0000-0100-0000-68703fa40000\"", "_attachments": "attachments/", "_ts": 1752186788}, {"payPeriodId": "1140036220003940", "intervalCode": "MONTHLY", "status": "INITIAL", "description": "Monthly Payroll (3)", "startDate": "2025-08-01T00:00:00Z", "endDate": "2025-08-31T00:00:00Z", "submitByDate": "2025-09-03T00:00:00Z", "checkDate": "2025-09-05T00:00:00Z", "checkCount": 0, "id": "76f7358d-0701-4b55-bf26-975201f97e58", "companyId": "19091807", "type": "payperiod", "_rid": "NmJkAKiCbEfcaQQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfcaQQAAAAAAA==/", "_etag": "\"a800b0a6-0000-0100-0000-68703fa40000\"", "_attachments": "attachments/", "_ts": 1752186788}, {"payPeriodId": "1140036220003943", "intervalCode": "MONTHLY", "status": "INITIAL", "description": "Monthly Payroll (3)", "startDate": "2025-09-01T00:00:00Z", "endDate": "2025-09-30T00:00:00Z", "submitByDate": "2025-10-01T00:00:00Z", "checkDate": "2025-10-03T00:00:00Z", "checkCount": 0, "id": "f330079a-34ee-4402-9cd2-51132b09df1f", "companyId": "19091807", "type": "payperiod", "_rid": "NmJkAKiCbEfdaQQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfdaQQAAAAAAA==/", "_etag": "\"a800b3a6-0000-0100-0000-68703fa50000\"", "_attachments": "attachments/", "_ts": 1752186789}], "links": [{"rel": "self", "href": "https://ca-payroll-ai-mock-sb-001-secure.nicebeach-8a7a52ab.eastus.azurecontainerapps.io/ca/companies/19091807/payperiods"}]}, "status_code": 200}