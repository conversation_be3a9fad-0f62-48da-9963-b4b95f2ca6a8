{"success": true, "company_id": "Y6543134", "data": {"metadata": {"contentItemCount": 40}, "content": [{"payPeriodId": "1090066174591690", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (2)", "startDate": "2024-12-30T00:00:00Z", "endDate": "2025-01-12T00:00:00Z", "submitByDate": "2025-01-15T00:00:00Z", "checkDate": "2025-01-17T00:00:00Z", "checkCount": 3, "id": "0b371393-8b09-40d2-aae0-17ac16aa912b", "companyId": "Y6543134", "type": "payperiod", "_rid": "NmJkAKiCbEef6AIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEef6AIAAAAAAA==/", "_etag": "\"a4000d6a-0000-0100-0000-6870207a0000\"", "_attachments": "attachments/", "_ts": 1752178810}, {"payPeriodId": "1090066470898582", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (2)", "startDate": "2025-01-13T00:00:00Z", "endDate": "2025-01-26T00:00:00Z", "submitByDate": "2025-01-29T00:00:00Z", "checkDate": "2025-01-31T00:00:00Z", "checkCount": 3, "id": "26b09951-a8aa-4cf8-85de-7f36c3c84269", "companyId": "Y6543134", "type": "payperiod", "_rid": "NmJkAKiCbEeg6AIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeg6AIAAAAAAA==/", "_etag": "\"a4000f6a-0000-0100-0000-6870207b0000\"", "_attachments": "attachments/", "_ts": 1752178811}, {"payPeriodId": "1090066775802053", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (2)", "startDate": "2025-01-27T00:00:00Z", "endDate": "2025-02-09T00:00:00Z", "submitByDate": "2025-02-12T00:00:00Z", "checkDate": "2025-02-14T00:00:00Z", "checkCount": 4, "id": "b0040671-efa1-4b33-a4de-5ca563781c9b", "companyId": "Y6543134", "type": "payperiod", "_rid": "NmJkAKiCbEeh6AIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeh6AIAAAAAAA==/", "_etag": "\"a400106a-0000-0100-0000-6870207b0000\"", "_attachments": "attachments/", "_ts": 1752178811}, {"payPeriodId": "1090067070920737", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (2)", "startDate": "2025-02-11T00:00:00Z", "endDate": "2025-02-24T00:00:00Z", "submitByDate": "2025-02-26T00:00:00Z", "checkDate": "2025-02-28T00:00:00Z", "checkCount": 4, "id": "7387109e-2bad-47fb-a4c9-5b6068fc3fcc", "companyId": "Y6543134", "type": "payperiod", "_rid": "NmJkAKiCbEei6AIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEei6AIAAAAAAA==/", "_etag": "\"a400136a-0000-0100-0000-6870207b0000\"", "_attachments": "attachments/", "_ts": 1752178811}, {"payPeriodId": "1090067367414675", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (2)", "startDate": "2025-02-24T00:00:00Z", "endDate": "2025-03-09T00:00:00Z", "submitByDate": "2025-03-12T00:00:00Z", "checkDate": "2025-03-14T00:00:00Z", "checkCount": 4, "id": "236db85a-c550-4abd-88d5-65cd8e811feb", "companyId": "Y6543134", "type": "payperiod", "_rid": "NmJkAKiCbEej6AIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEej6AIAAAAAAA==/", "_etag": "\"a400156a-0000-0100-0000-6870207b0000\"", "_attachments": "attachments/", "_ts": 1752178811}, {"payPeriodId": "1090067690843669", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (2)", "startDate": "2025-03-10T00:00:00Z", "endDate": "2025-03-23T00:00:00Z", "submitByDate": "2025-03-26T00:00:00Z", "checkDate": "2025-03-28T00:00:00Z", "checkCount": 4, "id": "5fa3e9f4-01ea-40f6-8b0b-9fc0f4e9f5ee", "companyId": "Y6543134", "type": "payperiod", "_rid": "NmJkAKiCbEek6AIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEek6AIAAAAAAA==/", "_etag": "\"a4001a6a-0000-0100-0000-6870207b0000\"", "_attachments": "attachments/", "_ts": 1752178811}, {"payPeriodId": "1090068009835264", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (2)", "startDate": "2025-03-25T00:00:00Z", "endDate": "2025-04-07T00:00:00Z", "submitByDate": "2025-04-09T00:00:00Z", "checkDate": "2025-04-11T00:00:00Z", "checkCount": 0, "id": "fc6471fa-95cc-4df4-9c32-6c63d8e52730", "companyId": "Y6543134", "type": "payperiod", "_rid": "NmJkAKiCbEel6AIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEel6AIAAAAAAA==/", "_etag": "\"a4001e6a-0000-0100-0000-6870207b0000\"", "_attachments": "attachments/", "_ts": 1752178811}, {"payPeriodId": "1090068308990065", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (2)", "startDate": "2025-04-08T00:00:00Z", "endDate": "2025-04-21T00:00:00Z", "submitByDate": "2025-04-23T00:00:00Z", "checkDate": "2025-04-25T00:00:00Z", "checkCount": 0, "id": "14f2e2df-3e48-471d-9f3e-61105adb2288", "companyId": "Y6543134", "type": "payperiod", "_rid": "NmJkAKiCbEem6AIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEem6AIAAAAAAA==/", "_etag": "\"a400216a-0000-0100-0000-6870207b0000\"", "_attachments": "attachments/", "_ts": 1752178811}, {"payPeriodId": "1090068607759782", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (2)", "startDate": "2025-04-22T00:00:00Z", "endDate": "2025-05-05T00:00:00Z", "submitByDate": "2025-05-07T00:00:00Z", "checkDate": "2025-05-09T00:00:00Z", "checkCount": 0, "id": "8976b4b9-50cf-4106-a4e9-a21b4a86a242", "companyId": "Y6543134", "type": "payperiod", "_rid": "NmJkAKiCbEen6AIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEen6AIAAAAAAA==/", "_etag": "\"a400226a-0000-0100-0000-6870207b0000\"", "_attachments": "attachments/", "_ts": 1752178811}, {"payPeriodId": "1090068910278557", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (2)", "startDate": "2025-05-06T00:00:00Z", "endDate": "2025-05-19T00:00:00Z", "submitByDate": "2025-05-21T00:00:00Z", "checkDate": "2025-05-23T00:00:00Z", "checkCount": 0, "id": "d8099a41-8d3f-4903-928d-93dc0449458d", "companyId": "Y6543134", "type": "payperiod", "_rid": "NmJkAKiCbEeo6AIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeo6AIAAAAAAA==/", "_etag": "\"a400246a-0000-0100-0000-6870207b0000\"", "_attachments": "attachments/", "_ts": 1752178811}, {"payPeriodId": "1090069211327919", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (2)", "startDate": "2025-05-20T00:00:00Z", "endDate": "2025-06-02T00:00:00Z", "submitByDate": "2025-06-04T00:00:00Z", "checkDate": "2025-06-06T00:00:00Z", "checkCount": 0, "id": "8e9f1632-8141-4037-a6a3-7b8171537193", "companyId": "Y6543134", "type": "payperiod", "_rid": "NmJkAKiCbEep6AIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEep6AIAAAAAAA==/", "_etag": "\"a400286a-0000-0100-0000-6870207b0000\"", "_attachments": "attachments/", "_ts": 1752178811}, {"payPeriodId": "1090069518547549", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (2)", "startDate": "2025-06-03T00:00:00Z", "endDate": "2025-06-16T00:00:00Z", "submitByDate": "2025-06-18T00:00:00Z", "checkDate": "2025-06-20T00:00:00Z", "checkCount": 0, "id": "3414264f-e203-4546-b8a1-b12ae30c4a69", "companyId": "Y6543134", "type": "payperiod", "_rid": "NmJkAKiCbEeq6AIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeq6AIAAAAAAA==/", "_etag": "\"a4002e6a-0000-0100-0000-6870207b0000\"", "_attachments": "attachments/", "_ts": 1752178811}, {"payPeriodId": "1090069819498514", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (2)", "startDate": "2025-06-17T00:00:00Z", "endDate": "2025-06-30T00:00:00Z", "submitByDate": "2025-07-01T00:00:00Z", "checkDate": "2025-07-03T00:00:00Z", "checkCount": 0, "id": "23921554-5416-43df-a04d-776d0afa061c", "companyId": "Y6543134", "type": "payperiod", "_rid": "NmJkAKiCbEer6AIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEer6AIAAAAAAA==/", "_etag": "\"a400306a-0000-0100-0000-6870207b0000\"", "_attachments": "attachments/", "_ts": 1752178811}, {"payPeriodId": "1090070130471728", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (2)", "startDate": "2025-07-01T00:00:00Z", "endDate": "2025-07-14T00:00:00Z", "submitByDate": "2025-07-16T00:00:00Z", "checkDate": "2025-07-18T00:00:00Z", "checkCount": 0, "id": "e672bbab-7747-479e-bcce-8d3267b7bb31", "companyId": "Y6543134", "type": "payperiod", "_rid": "NmJkAKiCbEes6AIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEes6AIAAAAAAA==/", "_etag": "\"a400326a-0000-0100-0000-6870207b0000\"", "_attachments": "attachments/", "_ts": 1752178811}, {"payPeriodId": "1090070439598194", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (2)", "startDate": "2025-07-15T00:00:00Z", "endDate": "2025-07-28T00:00:00Z", "submitByDate": "2025-07-30T00:00:00Z", "checkDate": "2025-08-01T00:00:00Z", "checkCount": 0, "id": "ce97d3fd-4db0-4ed2-abd5-592088b6d6ec", "companyId": "Y6543134", "type": "payperiod", "_rid": "NmJkAKiCbEet6AIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEet6AIAAAAAAA==/", "_etag": "\"a400366a-0000-0100-0000-6870207c0000\"", "_attachments": "attachments/", "_ts": 1752178812}, {"payPeriodId": "1090070755510347", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (2)", "startDate": "2025-07-29T00:00:00Z", "endDate": "2025-08-11T00:00:00Z", "submitByDate": "2025-08-13T00:00:00Z", "checkDate": "2025-08-15T00:00:00Z", "checkCount": 0, "id": "93b67255-3af4-472c-aaa0-4ffbc35d1b57", "companyId": "Y6543134", "type": "payperiod", "_rid": "NmJkAKiCbEeu6AIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeu6AIAAAAAAA==/", "_etag": "\"a4003c6a-0000-0100-0000-6870207c0000\"", "_attachments": "attachments/", "_ts": 1752178812}, {"payPeriodId": "1090071064944805", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (2)", "startDate": "2025-08-12T00:00:00Z", "endDate": "2025-08-25T00:00:00Z", "submitByDate": "2025-08-27T00:00:00Z", "checkDate": "2025-08-29T00:00:00Z", "checkCount": 0, "id": "5f9e87b1-6a6c-4c16-adc3-53aaf7a2140b", "companyId": "Y6543134", "type": "payperiod", "_rid": "NmJkAKiCbEev6AIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEev6AIAAAAAAA==/", "_etag": "\"a4003e6a-0000-0100-0000-6870207c0000\"", "_attachments": "attachments/", "_ts": 1752178812}, {"payPeriodId": "1090071376744296", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (2)", "startDate": "2025-08-26T00:00:00Z", "endDate": "2025-09-08T00:00:00Z", "submitByDate": "2025-09-10T00:00:00Z", "checkDate": "2025-09-12T00:00:00Z", "checkCount": 0, "id": "1851ab2c-70f5-4b23-91ed-e34f0caf2d99", "companyId": "Y6543134", "type": "payperiod", "_rid": "NmJkAKiCbEew6AIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEew6AIAAAAAAA==/", "_etag": "\"a400406a-0000-0100-0000-6870207c0000\"", "_attachments": "attachments/", "_ts": 1752178812}, {"payPeriodId": "1090071689628951", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (2)", "startDate": "2025-09-09T00:00:00Z", "endDate": "2025-09-22T00:00:00Z", "submitByDate": "2025-09-24T00:00:00Z", "checkDate": "2025-09-26T00:00:00Z", "checkCount": 0, "id": "18ac7af6-3fee-41a4-a9dd-83b8e222955c", "companyId": "Y6543134", "type": "payperiod", "_rid": "NmJkAKiCbEex6AIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEex6AIAAAAAAA==/", "_etag": "\"a400456a-0000-0100-0000-6870207c0000\"", "_attachments": "attachments/", "_ts": 1752178812}, {"payPeriodId": "1090072003845492", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (2)", "startDate": "2025-09-23T00:00:00Z", "endDate": "2025-10-06T00:00:00Z", "submitByDate": "2025-10-08T00:00:00Z", "checkDate": "2025-10-10T00:00:00Z", "checkCount": 0, "id": "8d950b4f-9907-4548-a976-69b46d4883d5", "companyId": "Y6543134", "type": "payperiod", "_rid": "NmJkAKiCbEey6AIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEey6AIAAAAAAA==/", "_etag": "\"a400476a-0000-0100-0000-6870207c0000\"", "_attachments": "attachments/", "_ts": 1752178812}, {"payPeriodId": "1090066174591690", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (2)", "startDate": "2024-12-30T00:00:00Z", "endDate": "2025-01-12T00:00:00Z", "submitByDate": "2025-01-15T00:00:00Z", "checkDate": "2025-01-17T00:00:00Z", "checkCount": 3, "id": "56f6feec-23c3-44a8-96e1-c655b00ee2c7", "companyId": "Y6543134", "type": "payperiod", "_rid": "NmJkAKiCbEe86AIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEe86AIAAAAAAA==/", "_etag": "\"a4006e6a-0000-0100-0000-6870207d0000\"", "_attachments": "attachments/", "_ts": 1752178813}, {"payPeriodId": "1090066470898582", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (2)", "startDate": "2025-01-13T00:00:00Z", "endDate": "2025-01-26T00:00:00Z", "submitByDate": "2025-01-29T00:00:00Z", "checkDate": "2025-01-31T00:00:00Z", "checkCount": 3, "id": "cc658ec0-f40f-4550-80fb-d889ec5e61ee", "companyId": "Y6543134", "type": "payperiod", "_rid": "NmJkAKiCbEe96AIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEe96AIAAAAAAA==/", "_etag": "\"a400736a-0000-0100-0000-6870207d0000\"", "_attachments": "attachments/", "_ts": 1752178813}, {"payPeriodId": "1090066775802053", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (2)", "startDate": "2025-01-27T00:00:00Z", "endDate": "2025-02-09T00:00:00Z", "submitByDate": "2025-02-12T00:00:00Z", "checkDate": "2025-02-14T00:00:00Z", "checkCount": 4, "id": "fefefeed-7292-4970-8d30-70c4ca8e3d89", "companyId": "Y6543134", "type": "payperiod", "_rid": "NmJkAKiCbEe+6AIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEe+6AIAAAAAAA==/", "_etag": "\"a4007b6a-0000-0100-0000-6870207d0000\"", "_attachments": "attachments/", "_ts": 1752178813}, {"payPeriodId": "1090067070920737", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (2)", "startDate": "2025-02-11T00:00:00Z", "endDate": "2025-02-24T00:00:00Z", "submitByDate": "2025-02-26T00:00:00Z", "checkDate": "2025-02-28T00:00:00Z", "checkCount": 4, "id": "35bb7bf5-1850-4675-8dfc-b667d3ad0076", "companyId": "Y6543134", "type": "payperiod", "_rid": "NmJkAKiCbEe-6AIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEe-6AIAAAAAAA==/", "_etag": "\"a400806a-0000-0100-0000-6870207d0000\"", "_attachments": "attachments/", "_ts": 1752178813}, {"payPeriodId": "1090067367414675", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (2)", "startDate": "2025-02-24T00:00:00Z", "endDate": "2025-03-09T00:00:00Z", "submitByDate": "2025-03-12T00:00:00Z", "checkDate": "2025-03-14T00:00:00Z", "checkCount": 4, "id": "83e8db19-422b-4992-bfda-34d327767b41", "companyId": "Y6543134", "type": "payperiod", "_rid": "NmJkAKiCbEfA6AIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfA6AIAAAAAAA==/", "_etag": "\"a400826a-0000-0100-0000-6870207d0000\"", "_attachments": "attachments/", "_ts": 1752178813}, {"payPeriodId": "1090067690843669", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (2)", "startDate": "2025-03-10T00:00:00Z", "endDate": "2025-03-23T00:00:00Z", "submitByDate": "2025-03-26T00:00:00Z", "checkDate": "2025-03-28T00:00:00Z", "checkCount": 4, "id": "ea1a73d7-8d62-4a61-84f4-01710d6b12d5", "companyId": "Y6543134", "type": "payperiod", "_rid": "NmJkAKiCbEfB6AIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfB6AIAAAAAAA==/", "_etag": "\"a400886a-0000-0100-0000-6870207d0000\"", "_attachments": "attachments/", "_ts": 1752178813}, {"payPeriodId": "1090068009835264", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (2)", "startDate": "2025-03-25T00:00:00Z", "endDate": "2025-04-07T00:00:00Z", "submitByDate": "2025-04-09T00:00:00Z", "checkDate": "2025-04-11T00:00:00Z", "checkCount": 4, "id": "cd99422f-5246-4a22-b07b-1bdaa8955e1f", "companyId": "Y6543134", "type": "payperiod", "_rid": "NmJkAKiCbEfC6AIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfC6AIAAAAAAA==/", "_etag": "\"a400966a-0000-0100-0000-6870207d0000\"", "_attachments": "attachments/", "_ts": 1752178813}, {"payPeriodId": "1090068308990065", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (2)", "startDate": "2025-04-08T00:00:00Z", "endDate": "2025-04-21T00:00:00Z", "submitByDate": "2025-04-23T00:00:00Z", "checkDate": "2025-04-25T00:00:00Z", "checkCount": 4, "id": "b168020c-85ed-4555-8834-8803493e3e35", "companyId": "Y6543134", "type": "payperiod", "_rid": "NmJkAKiCbEfD6AIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfD6AIAAAAAAA==/", "_etag": "\"a4009a6a-0000-0100-0000-6870207e0000\"", "_attachments": "attachments/", "_ts": 1752178814}, {"payPeriodId": "1090068607759782", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (2)", "startDate": "2025-04-22T00:00:00Z", "endDate": "2025-05-05T00:00:00Z", "submitByDate": "2025-05-07T00:00:00Z", "checkDate": "2025-05-09T00:00:00Z", "checkCount": 4, "id": "0a115296-dd54-4bcc-87fc-6fd0ddd26d2c", "companyId": "Y6543134", "type": "payperiod", "_rid": "NmJkAKiCbEfE6AIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfE6AIAAAAAAA==/", "_etag": "\"a4009c6a-0000-0100-0000-6870207e0000\"", "_attachments": "attachments/", "_ts": 1752178814}, {"payPeriodId": "1090068910278557", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (2)", "startDate": "2025-05-06T00:00:00Z", "endDate": "2025-05-19T00:00:00Z", "submitByDate": "2025-05-21T00:00:00Z", "checkDate": "2025-05-23T00:00:00Z", "checkCount": 4, "id": "c201d5a0-4591-4400-83df-2f10fca59c89", "companyId": "Y6543134", "type": "payperiod", "_rid": "NmJkAKiCbEfF6AIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfF6AIAAAAAAA==/", "_etag": "\"a4009e6a-0000-0100-0000-6870207e0000\"", "_attachments": "attachments/", "_ts": 1752178814}, {"payPeriodId": "1090069211327919", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (2)", "startDate": "2025-05-20T00:00:00Z", "endDate": "2025-06-02T00:00:00Z", "submitByDate": "2025-06-04T00:00:00Z", "checkDate": "2025-06-06T00:00:00Z", "checkCount": 4, "id": "27752789-116a-4e32-b45f-00db39450f96", "companyId": "Y6543134", "type": "payperiod", "_rid": "NmJkAKiCbEfG6AIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfG6AIAAAAAAA==/", "_etag": "\"a400a56a-0000-0100-0000-6870207e0000\"", "_attachments": "attachments/", "_ts": 1752178814}, {"payPeriodId": "1090069518547549", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (2)", "startDate": "2025-06-03T00:00:00Z", "endDate": "2025-06-16T00:00:00Z", "submitByDate": "2025-06-18T00:00:00Z", "checkDate": "2025-06-20T00:00:00Z", "checkCount": 5, "id": "c5e3197d-82f7-4f96-9eb4-b4ed169dcac9", "companyId": "Y6543134", "type": "payperiod", "_rid": "NmJkAKiCbEfH6AIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfH6AIAAAAAAA==/", "_etag": "\"a400b16a-0000-0100-0000-6870207e0000\"", "_attachments": "attachments/", "_ts": 1752178814}, {"payPeriodId": "1090069819498514", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (2)", "startDate": "2025-06-17T00:00:00Z", "endDate": "2025-06-30T00:00:00Z", "submitByDate": "2025-07-01T00:00:00Z", "checkDate": "2025-07-03T00:00:00Z", "checkCount": 4, "id": "6abc6532-9b05-4b9d-b89a-79e2f090ca64", "companyId": "Y6543134", "type": "payperiod", "_rid": "NmJkAKiCbEfI6AIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfI6AIAAAAAAA==/", "_etag": "\"a400b46a-0000-0100-0000-6870207e0000\"", "_attachments": "attachments/", "_ts": 1752178814}, {"payPeriodId": "1090070130471728", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (2)", "startDate": "2025-07-01T00:00:00Z", "endDate": "2025-07-14T00:00:00Z", "submitByDate": "2025-07-16T00:00:00Z", "checkDate": "2025-07-18T00:00:00Z", "checkCount": 0, "id": "3ed383d6-5dc6-409d-849f-4d8bdef7ed9a", "companyId": "Y6543134", "type": "payperiod", "_rid": "NmJkAKiCbEfJ6AIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfJ6AIAAAAAAA==/", "_etag": "\"a400bb6a-0000-0100-0000-6870207e0000\"", "_attachments": "attachments/", "_ts": 1752178814}, {"payPeriodId": "1090070439598194", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (2)", "startDate": "2025-07-15T00:00:00Z", "endDate": "2025-07-28T00:00:00Z", "submitByDate": "2025-07-30T00:00:00Z", "checkDate": "2025-08-01T00:00:00Z", "checkCount": 0, "id": "07091a79-0f28-45b2-bb74-38e7cd8b9e33", "companyId": "Y6543134", "type": "payperiod", "_rid": "NmJkAKiCbEfK6AIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfK6AIAAAAAAA==/", "_etag": "\"a400c06a-0000-0100-0000-6870207e0000\"", "_attachments": "attachments/", "_ts": 1752178814}, {"payPeriodId": "1090070755510347", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (2)", "startDate": "2025-07-29T00:00:00Z", "endDate": "2025-08-11T00:00:00Z", "submitByDate": "2025-08-13T00:00:00Z", "checkDate": "2025-08-15T00:00:00Z", "checkCount": 0, "id": "9eddb67a-9d08-43a5-98c5-059750d1d957", "companyId": "Y6543134", "type": "payperiod", "_rid": "NmJkAKiCbEfL6AIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfL6AIAAAAAAA==/", "_etag": "\"a400c16a-0000-0100-0000-6870207e0000\"", "_attachments": "attachments/", "_ts": 1752178814}, {"payPeriodId": "1090071064944805", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (2)", "startDate": "2025-08-12T00:00:00Z", "endDate": "2025-08-25T00:00:00Z", "submitByDate": "2025-08-27T00:00:00Z", "checkDate": "2025-08-29T00:00:00Z", "checkCount": 0, "id": "e9ba1beb-7808-461c-bb69-c48492ca18cf", "companyId": "Y6543134", "type": "payperiod", "_rid": "NmJkAKiCbEfM6AIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfM6AIAAAAAAA==/", "_etag": "\"a400c66a-0000-0100-0000-6870207e0000\"", "_attachments": "attachments/", "_ts": 1752178814}, {"payPeriodId": "1090071376744296", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (2)", "startDate": "2025-08-26T00:00:00Z", "endDate": "2025-09-08T00:00:00Z", "submitByDate": "2025-09-10T00:00:00Z", "checkDate": "2025-09-12T00:00:00Z", "checkCount": 0, "id": "09caf77e-15c2-4fea-b7c4-8122a910a958", "companyId": "Y6543134", "type": "payperiod", "_rid": "NmJkAKiCbEfN6AIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfN6AIAAAAAAA==/", "_etag": "\"a400c96a-0000-0100-0000-6870207e0000\"", "_attachments": "attachments/", "_ts": 1752178814}, {"payPeriodId": "1090071689628951", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (2)", "startDate": "2025-09-09T00:00:00Z", "endDate": "2025-09-22T00:00:00Z", "submitByDate": "2025-09-24T00:00:00Z", "checkDate": "2025-09-26T00:00:00Z", "checkCount": 0, "id": "e3d76764-19c4-46de-b7ba-27b8ab99562a", "companyId": "Y6543134", "type": "payperiod", "_rid": "NmJkAKiCbEfO6AIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfO6AIAAAAAAA==/", "_etag": "\"a400cc6a-0000-0100-0000-6870207e0000\"", "_attachments": "attachments/", "_ts": 1752178814}, {"payPeriodId": "1090072003845492", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (2)", "startDate": "2025-09-23T00:00:00Z", "endDate": "2025-10-06T00:00:00Z", "submitByDate": "2025-10-08T00:00:00Z", "checkDate": "2025-10-10T00:00:00Z", "checkCount": 0, "id": "fce25d62-71dc-454b-af83-8eab2162a0f9", "companyId": "Y6543134", "type": "payperiod", "_rid": "NmJkAKiCbEfP6AIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfP6AIAAAAAAA==/", "_etag": "\"a400d26a-0000-0100-0000-6870207e0000\"", "_attachments": "attachments/", "_ts": 1752178814}], "links": [{"rel": "self", "href": "https://ca-payroll-ai-mock-sb-001-secure.nicebeach-8a7a52ab.eastus.azurecontainerapps.io/ca/companies/Y6543134/payperiods"}]}, "status_code": 200}