{"success": true, "company_id": "15065600", "data": {"metadata": {"contentItemCount": 50}, "content": [{"payPeriodId": "1060038968198906", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-01-01T00:00:00Z", "endDate": "2025-01-15T00:00:00Z", "submitByDate": "2025-01-20T00:00:00Z", "checkDate": "2025-01-22T00:00:00Z", "checkCount": 3, "id": "dbd2ccd7-34b4-4636-9e9b-ea517bf00234", "companyId": "15065600", "type": "payperiod", "_rid": "NmJkAKiCbEcr8AMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcr8AMAAAAAAA==/", "_etag": "\"a7007f74-0000-0100-0000-687035d20000\"", "_attachments": "attachments/", "_ts": 1752184274}, {"payPeriodId": "1060038968198907", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-01-16T00:00:00Z", "endDate": "2025-01-31T00:00:00Z", "submitByDate": "2025-02-05T00:00:00Z", "checkDate": "2025-02-07T00:00:00Z", "checkCount": 3, "id": "4ad6f23d-9273-4776-ae74-36e8f03ee2e6", "companyId": "15065600", "type": "payperiod", "_rid": "NmJkAKiCbEcs8AMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcs8AMAAAAAAA==/", "_etag": "\"a7008174-0000-0100-0000-687035d20000\"", "_attachments": "attachments/", "_ts": 1752184274}, {"payPeriodId": "1060039135838134", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-02-01T00:00:00Z", "endDate": "2025-02-15T00:00:00Z", "submitByDate": "2025-02-19T00:00:00Z", "checkDate": "2025-02-21T00:00:00Z", "checkCount": 3, "id": "79efcf4c-5448-45ea-8c3d-e8b508efbed7", "companyId": "15065600", "type": "payperiod", "_rid": "NmJkAKiCbEct8AMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEct8AMAAAAAAA==/", "_etag": "\"a7008274-0000-0100-0000-687035d20000\"", "_attachments": "attachments/", "_ts": 1752184274}, {"payPeriodId": "1060039135838135", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-02-16T00:00:00Z", "endDate": "2025-02-28T00:00:00Z", "submitByDate": "2025-03-05T00:00:00Z", "checkDate": "2025-03-07T00:00:00Z", "checkCount": 3, "id": "7ddab637-8d70-42ea-b139-f5565e673e24", "companyId": "15065600", "type": "payperiod", "_rid": "NmJkAKiCbEcu8AMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcu8AMAAAAAAA==/", "_etag": "\"a7008474-0000-0100-0000-687035d20000\"", "_attachments": "attachments/", "_ts": 1752184274}, {"payPeriodId": "1060039291476323", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-03-01T00:00:00Z", "endDate": "2025-03-15T00:00:00Z", "submitByDate": "2025-03-19T00:00:00Z", "checkDate": "2025-03-21T00:00:00Z", "checkCount": 3, "id": "60d019d8-d3ce-4067-9c83-26262abe81af", "companyId": "15065600", "type": "payperiod", "_rid": "NmJkAKiCbEcv8AMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcv8AMAAAAAAA==/", "_etag": "\"a7008774-0000-0100-0000-687035d20000\"", "_attachments": "attachments/", "_ts": 1752184274}, {"payPeriodId": "1060039291476324", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-03-16T00:00:00Z", "endDate": "2025-03-31T00:00:00Z", "submitByDate": "2025-04-03T00:00:00Z", "checkDate": "2025-04-07T00:00:00Z", "checkCount": 0, "id": "8ff4cacf-893c-4f63-98e5-6416f5cd5a54", "companyId": "15065600", "type": "payperiod", "_rid": "NmJkAKiCbEcw8AMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcw8AMAAAAAAA==/", "_etag": "\"a7008874-0000-0100-0000-687035d20000\"", "_attachments": "attachments/", "_ts": 1752184274}, {"payPeriodId": "1060039453460685", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-04-01T00:00:00Z", "endDate": "2025-04-15T00:00:00Z", "submitByDate": "2025-04-18T00:00:00Z", "checkDate": "2025-04-22T00:00:00Z", "checkCount": 0, "id": "ce8e33de-2ca2-42d2-bae9-05b776812781", "companyId": "15065600", "type": "payperiod", "_rid": "NmJkAKiCbEcx8AMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcx8AMAAAAAAA==/", "_etag": "\"a7008a74-0000-0100-0000-687035d20000\"", "_attachments": "attachments/", "_ts": 1752184274}, {"payPeriodId": "1060039989981883", "status": "INITIAL", "description": "Payroll", "startDate": "2025-04-01T00:00:00Z", "endDate": "2025-04-15T00:00:00Z", "submitByDate": "2025-04-21T00:00:00Z", "checkDate": "2025-04-22T00:00:00Z", "checkCount": 0, "id": "7b203927-653b-4958-a95a-8a2d842f059f", "companyId": "15065600", "type": "payperiod", "_rid": "NmJkAKiCbEcy8AMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcy8AMAAAAAAA==/", "_etag": "\"a7008b74-0000-0100-0000-687035d20000\"", "_attachments": "attachments/", "_ts": 1752184274}, {"payPeriodId": "1060039453460686", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-04-16T00:00:00Z", "endDate": "2025-04-30T00:00:00Z", "submitByDate": "2025-05-05T00:00:00Z", "checkDate": "2025-05-07T00:00:00Z", "checkCount": 0, "id": "bd621ce6-4eef-48d1-b7ba-a33f76dbd6c1", "companyId": "15065600", "type": "payperiod", "_rid": "NmJkAKiCbEcz8AMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcz8AMAAAAAAA==/", "_etag": "\"a7008e74-0000-0100-0000-687035d20000\"", "_attachments": "attachments/", "_ts": 1752184274}, {"payPeriodId": "1060039617582898", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-05-01T00:00:00Z", "endDate": "2025-05-15T00:00:00Z", "submitByDate": "2025-05-20T00:00:00Z", "checkDate": "2025-05-22T00:00:00Z", "checkCount": 0, "id": "5911f8d4-73bc-4567-a068-b013ae347fd6", "companyId": "15065600", "type": "payperiod", "_rid": "NmJkAKiCbEc08AMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc08AMAAAAAAA==/", "_etag": "\"a7009074-0000-0100-0000-687035d20000\"", "_attachments": "attachments/", "_ts": 1752184274}, {"payPeriodId": "1060039591297865", "intervalCode": "MONTHLY", "status": "INITIAL", "description": "Monthly Payroll (1)", "startDate": "2025-05-01T00:00:00Z", "endDate": "2025-05-31T00:00:00Z", "submitByDate": "2025-06-05T00:00:00Z", "checkDate": "2025-06-06T00:00:00Z", "checkCount": 0, "id": "cdc59db0-4d9d-45a8-92b6-687164cfb06a", "companyId": "15065600", "type": "payperiod", "_rid": "NmJkAKiCbEc18AMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc18AMAAAAAAA==/", "_etag": "\"a7009274-0000-0100-0000-687035d20000\"", "_attachments": "attachments/", "_ts": 1752184274}, {"payPeriodId": "1060039756193822", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-06-01T00:00:00Z", "endDate": "2025-06-15T00:00:00Z", "submitByDate": "2025-06-18T00:00:00Z", "checkDate": "2025-06-20T00:00:00Z", "checkCount": 0, "id": "9c44000b-c361-4338-991b-98981e7833c7", "companyId": "15065600", "type": "payperiod", "_rid": "NmJkAKiCbEc28AMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc28AMAAAAAAA==/", "_etag": "\"a7009874-0000-0100-0000-687035d20000\"", "_attachments": "attachments/", "_ts": 1752184274}, {"payPeriodId": "1060039756193823", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-06-16T00:00:00Z", "endDate": "2025-06-30T00:00:00Z", "submitByDate": "2025-07-02T00:00:00Z", "checkDate": "2025-07-07T00:00:00Z", "checkCount": 0, "id": "c7926f8f-a8ac-46f8-934f-fbc6a7ef7ed6", "companyId": "15065600", "type": "payperiod", "_rid": "NmJkAKiCbEc38AMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc38AMAAAAAAA==/", "_etag": "\"a7009c74-0000-0100-0000-687035d30000\"", "_attachments": "attachments/", "_ts": 1752184275}, {"payPeriodId": "1060039897414558", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-07-01T00:00:00Z", "endDate": "2025-07-15T00:00:00Z", "submitByDate": "2025-07-18T00:00:00Z", "checkDate": "2025-07-22T00:00:00Z", "checkCount": 0, "id": "8b043211-442b-403d-b2e3-fb52ce269a51", "companyId": "15065600", "type": "payperiod", "_rid": "NmJkAKiCbEc48AMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc48AMAAAAAAA==/", "_etag": "\"a7009d74-0000-0100-0000-687035d30000\"", "_attachments": "attachments/", "_ts": 1752184275}, {"payPeriodId": "1060039905018552", "intervalCode": "MONTHLY", "status": "INITIAL", "description": "Monthly Payroll (1)", "startDate": "2025-07-01T00:00:00Z", "endDate": "2025-07-31T00:00:00Z", "submitByDate": "2025-07-29T00:00:00Z", "checkDate": "2025-07-31T00:00:00Z", "checkCount": 0, "id": "babdb125-f75b-4b1c-bd63-57465bdd515b", "companyId": "15065600", "type": "payperiod", "_rid": "NmJkAKiCbEc58AMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc58AMAAAAAAA==/", "_etag": "\"a7009e74-0000-0100-0000-687035d30000\"", "_attachments": "attachments/", "_ts": 1752184275}, {"payPeriodId": "1060039897414559", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-07-16T00:00:00Z", "endDate": "2025-07-31T00:00:00Z", "submitByDate": "2025-08-05T00:00:00Z", "checkDate": "2025-08-07T00:00:00Z", "checkCount": 0, "id": "1f26db1c-236f-4335-91e1-2bc5810b9d0a", "companyId": "15065600", "type": "payperiod", "_rid": "NmJkAKiCbEc68AMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc68AMAAAAAAA==/", "_etag": "\"a700a074-0000-0100-0000-687035d30000\"", "_attachments": "attachments/", "_ts": 1752184275}, {"payPeriodId": "1060040065252629", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-08-01T00:00:00Z", "endDate": "2025-08-15T00:00:00Z", "submitByDate": "2025-08-20T00:00:00Z", "checkDate": "2025-08-22T00:00:00Z", "checkCount": 0, "id": "452f7e67-d06e-4660-86eb-e168e9a1806e", "companyId": "15065600", "type": "payperiod", "_rid": "NmJkAKiCbEc78AMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc78AMAAAAAAA==/", "_etag": "\"a700a174-0000-0100-0000-687035d30000\"", "_attachments": "attachments/", "_ts": 1752184275}, {"payPeriodId": "1060040055926740", "intervalCode": "MONTHLY", "status": "INITIAL", "description": "Monthly Payroll (1)", "startDate": "2025-08-01T00:00:00Z", "endDate": "2025-08-31T00:00:00Z", "submitByDate": "2025-08-27T00:00:00Z", "checkDate": "2025-08-29T00:00:00Z", "checkCount": 0, "id": "4f405253-11aa-4f7d-8b31-c9a6a50455db", "companyId": "15065600", "type": "payperiod", "_rid": "NmJkAKiCbEc88AMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc88AMAAAAAAA==/", "_etag": "\"a700a474-0000-0100-0000-687035d30000\"", "_attachments": "attachments/", "_ts": 1752184275}, {"payPeriodId": "1060040065252630", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-08-16T00:00:00Z", "endDate": "2025-08-31T00:00:00Z", "submitByDate": "2025-09-03T00:00:00Z", "checkDate": "2025-09-05T00:00:00Z", "checkCount": 0, "id": "212467c7-5b0f-41f9-96f2-6c42a554bca6", "companyId": "15065600", "type": "payperiod", "_rid": "NmJkAKiCbEc98AMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc98AMAAAAAAA==/", "_etag": "\"a700ac74-0000-0100-0000-687035d30000\"", "_attachments": "attachments/", "_ts": 1752184275}, {"payPeriodId": "1060040264210999", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-09-01T00:00:00Z", "endDate": "2025-09-15T00:00:00Z", "submitByDate": "2025-09-18T00:00:00Z", "checkDate": "2025-09-22T00:00:00Z", "checkCount": 0, "id": "17901642-dbfc-4138-8a42-1256ef679c16", "companyId": "15065600", "type": "payperiod", "_rid": "NmJkAKiCbEc+8AMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc+8AMAAAAAAA==/", "_etag": "\"a700ad74-0000-0100-0000-687035d30000\"", "_attachments": "attachments/", "_ts": 1752184275}, {"payPeriodId": "1060040225772334", "intervalCode": "MONTHLY", "status": "INITIAL", "description": "Monthly Payroll (1)", "startDate": "2025-09-01T00:00:00Z", "endDate": "2025-09-30T00:00:00Z", "submitByDate": "2025-09-26T00:00:00Z", "checkDate": "2025-09-30T00:00:00Z", "checkCount": 0, "id": "65f36969-d2da-4877-bf44-c333c0c19a6d", "companyId": "15065600", "type": "payperiod", "_rid": "NmJkAKiCbEc-8AMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc-8AMAAAAAAA==/", "_etag": "\"a700af74-0000-0100-0000-687035d30000\"", "_attachments": "attachments/", "_ts": 1752184275}, {"payPeriodId": "1060040264211000", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-09-16T00:00:00Z", "endDate": "2025-09-30T00:00:00Z", "submitByDate": "2025-10-03T00:00:00Z", "checkDate": "2025-10-07T00:00:00Z", "checkCount": 0, "id": "0b8f7268-f074-4c05-8ac6-b3c0eef3fb2e", "companyId": "15065600", "type": "payperiod", "_rid": "NmJkAKiCbEdA8AMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdA8AMAAAAAAA==/", "_etag": "\"a700b274-0000-0100-0000-687035d30000\"", "_attachments": "attachments/", "_ts": 1752184275}, {"payPeriodId": "1060040365144564", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-10-01T00:00:00Z", "endDate": "2025-10-15T00:00:00Z", "submitByDate": "2025-10-20T00:00:00Z", "checkDate": "2025-10-22T00:00:00Z", "checkCount": 0, "id": "636bdb68-980b-4076-aed4-e804611e23c2", "companyId": "15065600", "type": "payperiod", "_rid": "NmJkAKiCbEdB8AMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdB8AMAAAAAAA==/", "_etag": "\"a700b574-0000-0100-0000-687035d30000\"", "_attachments": "attachments/", "_ts": 1752184275}, {"payPeriodId": "1060040369752545", "intervalCode": "MONTHLY", "status": "INITIAL", "description": "Monthly Payroll (1)", "startDate": "2025-10-01T00:00:00Z", "endDate": "2025-10-31T00:00:00Z", "submitByDate": "2025-10-29T00:00:00Z", "checkDate": "2025-10-31T00:00:00Z", "checkCount": 0, "id": "b8890ec2-5b8c-4956-bab2-52d4302a4592", "companyId": "15065600", "type": "payperiod", "_rid": "NmJkAKiCbEdC8AMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdC8AMAAAAAAA==/", "_etag": "\"a700b874-0000-0100-0000-687035d30000\"", "_attachments": "attachments/", "_ts": 1752184275}, {"payPeriodId": "1060040365144565", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-10-16T00:00:00Z", "endDate": "2025-10-31T00:00:00Z", "submitByDate": "2025-11-05T00:00:00Z", "checkDate": "2025-11-07T00:00:00Z", "checkCount": 0, "id": "94c10091-35f4-4abd-9b4a-a8df07072fb6", "companyId": "15065600", "type": "payperiod", "_rid": "NmJkAKiCbEdD8AMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdD8AMAAAAAAA==/", "_etag": "\"a700b974-0000-0100-0000-687035d30000\"", "_attachments": "attachments/", "_ts": 1752184275}, {"payPeriodId": "1060038968198906", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-01-01T00:00:00Z", "endDate": "2025-01-15T00:00:00Z", "submitByDate": "2025-01-20T00:00:00Z", "checkDate": "2025-01-22T00:00:00Z", "checkCount": 3, "id": "537d40f0-b61f-4902-9f2d-d4f66240c625", "companyId": "15065600", "type": "payperiod", "_rid": "NmJkAKiCbEdN8AMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdN8AMAAAAAAA==/", "_etag": "\"a700d274-0000-0100-0000-687035d40000\"", "_attachments": "attachments/", "_ts": 1752184276}, {"payPeriodId": "1060038968198907", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-01-16T00:00:00Z", "endDate": "2025-01-31T00:00:00Z", "submitByDate": "2025-02-05T00:00:00Z", "checkDate": "2025-02-07T00:00:00Z", "checkCount": 3, "id": "64bff066-52a0-4bdc-8550-7f68566ef098", "companyId": "15065600", "type": "payperiod", "_rid": "NmJkAKiCbEdO8AMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdO8AMAAAAAAA==/", "_etag": "\"a700d474-0000-0100-0000-687035d40000\"", "_attachments": "attachments/", "_ts": 1752184276}, {"payPeriodId": "1060039135838134", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-02-01T00:00:00Z", "endDate": "2025-02-15T00:00:00Z", "submitByDate": "2025-02-19T00:00:00Z", "checkDate": "2025-02-21T00:00:00Z", "checkCount": 3, "id": "1eff3373-959f-4741-8249-7fc3e02593f3", "companyId": "15065600", "type": "payperiod", "_rid": "NmJkAKiCbEdP8AMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdP8AMAAAAAAA==/", "_etag": "\"a700d874-0000-0100-0000-687035d40000\"", "_attachments": "attachments/", "_ts": 1752184276}, {"payPeriodId": "1060039135838135", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-02-16T00:00:00Z", "endDate": "2025-02-28T00:00:00Z", "submitByDate": "2025-03-05T00:00:00Z", "checkDate": "2025-03-07T00:00:00Z", "checkCount": 3, "id": "618c0e6e-1d41-449e-9eb9-62c5d718f75c", "companyId": "15065600", "type": "payperiod", "_rid": "NmJkAKiCbEdQ8AMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdQ8AMAAAAAAA==/", "_etag": "\"a700dc74-0000-0100-0000-687035d40000\"", "_attachments": "attachments/", "_ts": 1752184276}, {"payPeriodId": "1060039291476323", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-03-01T00:00:00Z", "endDate": "2025-03-15T00:00:00Z", "submitByDate": "2025-03-19T00:00:00Z", "checkDate": "2025-03-21T00:00:00Z", "checkCount": 3, "id": "f7d8a084-b69b-4e58-a61d-de0e51afc039", "companyId": "15065600", "type": "payperiod", "_rid": "NmJkAKiCbEdR8AMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdR8AMAAAAAAA==/", "_etag": "\"a700de74-0000-0100-0000-687035d50000\"", "_attachments": "attachments/", "_ts": 1752184277}, {"payPeriodId": "1060039291476324", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-03-16T00:00:00Z", "endDate": "2025-03-31T00:00:00Z", "submitByDate": "2025-04-03T00:00:00Z", "checkDate": "2025-04-07T00:00:00Z", "checkCount": 3, "id": "14116394-5c1e-4865-82a8-98884f20f84d", "companyId": "15065600", "type": "payperiod", "_rid": "NmJkAKiCbEdS8AMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdS8AMAAAAAAA==/", "_etag": "\"a700df74-0000-0100-0000-687035d50000\"", "_attachments": "attachments/", "_ts": 1752184277}, {"payPeriodId": "1060039453460685", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-04-01T00:00:00Z", "endDate": "2025-04-15T00:00:00Z", "submitByDate": "2025-04-18T00:00:00Z", "checkDate": "2025-04-22T00:00:00Z", "checkCount": 3, "id": "08287e49-a2b3-4eff-bbd4-9be2186f96c6", "companyId": "15065600", "type": "payperiod", "_rid": "NmJkAKiCbEdT8AMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdT8AMAAAAAAA==/", "_etag": "\"a700e274-0000-0100-0000-687035d50000\"", "_attachments": "attachments/", "_ts": 1752184277}, {"payPeriodId": "1060039989981883", "status": "COMPLETED", "description": "Payroll", "startDate": "2025-04-01T00:00:00Z", "endDate": "2025-04-15T00:00:00Z", "submitByDate": "2025-04-21T00:00:00Z", "checkDate": "2025-04-22T00:00:00Z", "checkCount": 2, "id": "6c9bbbde-5e90-454b-9519-b3b829b983a8", "companyId": "15065600", "type": "payperiod", "_rid": "NmJkAKiCbEdU8AMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdU8AMAAAAAAA==/", "_etag": "\"a700e574-0000-0100-0000-687035d50000\"", "_attachments": "attachments/", "_ts": 1752184277}, {"payPeriodId": "1060039453460686", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-04-16T00:00:00Z", "endDate": "2025-04-30T00:00:00Z", "submitByDate": "2025-05-05T00:00:00Z", "checkDate": "2025-05-07T00:00:00Z", "checkCount": 3, "id": "01ada0e5-829a-40e7-a928-d29d60c22f83", "companyId": "15065600", "type": "payperiod", "_rid": "NmJkAKiCbEdV8AMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdV8AMAAAAAAA==/", "_etag": "\"a700e674-0000-0100-0000-687035d50000\"", "_attachments": "attachments/", "_ts": 1752184277}, {"payPeriodId": "1060039617582898", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-05-01T00:00:00Z", "endDate": "2025-05-15T00:00:00Z", "submitByDate": "2025-05-20T00:00:00Z", "checkDate": "2025-05-22T00:00:00Z", "checkCount": 3, "id": "00791516-baf4-49e9-86eb-1fa9db4f4013", "companyId": "15065600", "type": "payperiod", "_rid": "NmJkAKiCbEdW8AMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdW8AMAAAAAAA==/", "_etag": "\"a700e774-0000-0100-0000-687035d50000\"", "_attachments": "attachments/", "_ts": 1752184277}, {"payPeriodId": "1060039591297865", "intervalCode": "MONTHLY", "status": "COMPLETED", "description": "Monthly Payroll (1)", "startDate": "2025-05-01T00:00:00Z", "endDate": "2025-05-31T00:00:00Z", "submitByDate": "2025-06-05T00:00:00Z", "checkDate": "2025-06-06T00:00:00Z", "checkCount": 3, "id": "ac8a0fb7-95d6-4858-ab15-f0485c6593dd", "companyId": "15065600", "type": "payperiod", "_rid": "NmJkAKiCbEdX8AMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdX8AMAAAAAAA==/", "_etag": "\"a700e974-0000-0100-0000-687035d50000\"", "_attachments": "attachments/", "_ts": 1752184277}, {"payPeriodId": "1060039756193822", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-06-01T00:00:00Z", "endDate": "2025-06-15T00:00:00Z", "submitByDate": "2025-06-18T00:00:00Z", "checkDate": "2025-06-20T00:00:00Z", "checkCount": 3, "id": "1f7f0b1b-726f-4bce-9c63-728911332489", "companyId": "15065600", "type": "payperiod", "_rid": "NmJkAKiCbEdY8AMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdY8AMAAAAAAA==/", "_etag": "\"a700ec74-0000-0100-0000-687035d50000\"", "_attachments": "attachments/", "_ts": 1752184277}, {"payPeriodId": "1060039756193823", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-06-16T00:00:00Z", "endDate": "2025-06-30T00:00:00Z", "submitByDate": "2025-07-02T00:00:00Z", "checkDate": "2025-07-07T00:00:00Z", "checkCount": 3, "id": "5ed5c58a-913e-4fc4-9b90-85eea0e45aba", "companyId": "15065600", "type": "payperiod", "_rid": "NmJkAKiCbEdZ8AMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdZ8AMAAAAAAA==/", "_etag": "\"a700ed74-0000-0100-0000-687035d50000\"", "_attachments": "attachments/", "_ts": 1752184277}, {"payPeriodId": "1060039897414558", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-07-01T00:00:00Z", "endDate": "2025-07-15T00:00:00Z", "submitByDate": "2025-07-18T00:00:00Z", "checkDate": "2025-07-22T00:00:00Z", "checkCount": 0, "id": "367b4287-f5ed-49d8-bb1f-2b8d81f41907", "companyId": "15065600", "type": "payperiod", "_rid": "NmJkAKiCbEda8AMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEda8AMAAAAAAA==/", "_etag": "\"a700ee74-0000-0100-0000-687035d50000\"", "_attachments": "attachments/", "_ts": 1752184277}, {"payPeriodId": "1060039905018552", "intervalCode": "MONTHLY", "status": "INITIAL", "description": "Monthly Payroll (1)", "startDate": "2025-07-01T00:00:00Z", "endDate": "2025-07-31T00:00:00Z", "submitByDate": "2025-07-29T00:00:00Z", "checkDate": "2025-07-31T00:00:00Z", "checkCount": 0, "id": "1db29d81-f126-4182-b8ea-551995644858", "companyId": "15065600", "type": "payperiod", "_rid": "NmJkAKiCbEdb8AMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdb8AMAAAAAAA==/", "_etag": "\"a700f474-0000-0100-0000-687035d50000\"", "_attachments": "attachments/", "_ts": 1752184277}, {"payPeriodId": "1060039897414559", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-07-16T00:00:00Z", "endDate": "2025-07-31T00:00:00Z", "submitByDate": "2025-08-05T00:00:00Z", "checkDate": "2025-08-07T00:00:00Z", "checkCount": 0, "id": "d7fe7adc-bed4-43f2-81ae-018c0c481d8a", "companyId": "15065600", "type": "payperiod", "_rid": "NmJkAKiCbEdc8AMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdc8AMAAAAAAA==/", "_etag": "\"a700f774-0000-0100-0000-687035d50000\"", "_attachments": "attachments/", "_ts": 1752184277}, {"payPeriodId": "1060040065252629", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-08-01T00:00:00Z", "endDate": "2025-08-15T00:00:00Z", "submitByDate": "2025-08-20T00:00:00Z", "checkDate": "2025-08-22T00:00:00Z", "checkCount": 0, "id": "9982b8ab-a6f1-4518-ac20-20848d70f7a8", "companyId": "15065600", "type": "payperiod", "_rid": "NmJkAKiCbEdd8AMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdd8AMAAAAAAA==/", "_etag": "\"a700f874-0000-0100-0000-687035d60000\"", "_attachments": "attachments/", "_ts": 1752184278}, {"payPeriodId": "1060040055926740", "intervalCode": "MONTHLY", "status": "INITIAL", "description": "Monthly Payroll (1)", "startDate": "2025-08-01T00:00:00Z", "endDate": "2025-08-31T00:00:00Z", "submitByDate": "2025-08-27T00:00:00Z", "checkDate": "2025-08-29T00:00:00Z", "checkCount": 0, "id": "26013e9e-3a4f-4648-9b6d-6a68e86cd887", "companyId": "15065600", "type": "payperiod", "_rid": "NmJkAKiCbEde8AMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEde8AMAAAAAAA==/", "_etag": "\"a7000375-0000-0100-0000-687035d60000\"", "_attachments": "attachments/", "_ts": 1752184278}, {"payPeriodId": "1060040065252630", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-08-16T00:00:00Z", "endDate": "2025-08-31T00:00:00Z", "submitByDate": "2025-09-03T00:00:00Z", "checkDate": "2025-09-05T00:00:00Z", "checkCount": 0, "id": "68f06b67-36d7-4ae3-85b3-cf50a7e65546", "companyId": "15065600", "type": "payperiod", "_rid": "NmJkAKiCbEdf8AMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdf8AMAAAAAAA==/", "_etag": "\"a7000675-0000-0100-0000-687035d60000\"", "_attachments": "attachments/", "_ts": 1752184278}, {"payPeriodId": "1060040264210999", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-09-01T00:00:00Z", "endDate": "2025-09-15T00:00:00Z", "submitByDate": "2025-09-18T00:00:00Z", "checkDate": "2025-09-22T00:00:00Z", "checkCount": 0, "id": "269cad4c-5641-4611-bf88-b518a1dc7788", "companyId": "15065600", "type": "payperiod", "_rid": "NmJkAKiCbEdg8AMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdg8AMAAAAAAA==/", "_etag": "\"a7000a75-0000-0100-0000-687035d60000\"", "_attachments": "attachments/", "_ts": 1752184278}, {"payPeriodId": "1060040225772334", "intervalCode": "MONTHLY", "status": "INITIAL", "description": "Monthly Payroll (1)", "startDate": "2025-09-01T00:00:00Z", "endDate": "2025-09-30T00:00:00Z", "submitByDate": "2025-09-26T00:00:00Z", "checkDate": "2025-09-30T00:00:00Z", "checkCount": 0, "id": "6416af3d-7f36-4816-b947-f16bb71dc7ff", "companyId": "15065600", "type": "payperiod", "_rid": "NmJkAKiCbEdh8AMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdh8AMAAAAAAA==/", "_etag": "\"a7000e75-0000-0100-0000-687035d60000\"", "_attachments": "attachments/", "_ts": 1752184278}, {"payPeriodId": "1060040264211000", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-09-16T00:00:00Z", "endDate": "2025-09-30T00:00:00Z", "submitByDate": "2025-10-03T00:00:00Z", "checkDate": "2025-10-07T00:00:00Z", "checkCount": 0, "id": "77492178-d70e-44a8-9b0d-bf7867d60618", "companyId": "15065600", "type": "payperiod", "_rid": "NmJkAKiCbEdi8AMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdi8AMAAAAAAA==/", "_etag": "\"a7001575-0000-0100-0000-687035d60000\"", "_attachments": "attachments/", "_ts": 1752184278}, {"payPeriodId": "1060040365144564", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-10-01T00:00:00Z", "endDate": "2025-10-15T00:00:00Z", "submitByDate": "2025-10-20T00:00:00Z", "checkDate": "2025-10-22T00:00:00Z", "checkCount": 0, "id": "9de89b3b-d0c2-458c-b186-b47442d050ed", "companyId": "15065600", "type": "payperiod", "_rid": "NmJkAKiCbEdj8AMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdj8AMAAAAAAA==/", "_etag": "\"a7001875-0000-0100-0000-687035d60000\"", "_attachments": "attachments/", "_ts": 1752184278}, {"payPeriodId": "1060040369752545", "intervalCode": "MONTHLY", "status": "INITIAL", "description": "Monthly Payroll (1)", "startDate": "2025-10-01T00:00:00Z", "endDate": "2025-10-31T00:00:00Z", "submitByDate": "2025-10-29T00:00:00Z", "checkDate": "2025-10-31T00:00:00Z", "checkCount": 0, "id": "ae1c0254-ce6f-49b9-981c-aca31c964bf6", "companyId": "15065600", "type": "payperiod", "_rid": "NmJkAKiCbEdk8AMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdk8AMAAAAAAA==/", "_etag": "\"a7001975-0000-0100-0000-687035d60000\"", "_attachments": "attachments/", "_ts": 1752184278}, {"payPeriodId": "1060040365144565", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-10-16T00:00:00Z", "endDate": "2025-10-31T00:00:00Z", "submitByDate": "2025-11-05T00:00:00Z", "checkDate": "2025-11-07T00:00:00Z", "checkCount": 0, "id": "3ca98a49-a53e-40c5-81a1-b7de96968539", "companyId": "15065600", "type": "payperiod", "_rid": "NmJkAKiCbEdl8AMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdl8AMAAAAAAA==/", "_etag": "\"a7001a75-0000-0100-0000-687035d60000\"", "_attachments": "attachments/", "_ts": 1752184278}], "links": [{"rel": "self", "href": "https://ca-payroll-ai-mock-sb-001-secure.nicebeach-8a7a52ab.eastus.azurecontainerapps.io/ca/companies/15065600/payperiods"}]}, "status_code": 200}