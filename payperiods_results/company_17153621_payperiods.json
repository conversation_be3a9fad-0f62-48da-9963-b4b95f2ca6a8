{"success": true, "company_id": "17153621", "data": {"metadata": {"contentItemCount": 56}, "content": [{"payPeriodId": "1080039040688305", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2024-12-28T00:00:00Z", "endDate": "2025-01-03T00:00:00Z", "submitByDate": "2025-01-08T00:00:00Z", "checkDate": "2025-01-10T00:00:00Z", "checkCount": 1, "id": "01c68ec9-f55d-4f71-a1b1-085b0150b801", "companyId": "17153621", "type": "payperiod", "_rid": "NmJkAKiCbEe5wwIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEe5wwIAAAAAAA==/", "_etag": "\"a30098e9-0000-0100-0000-68701d7d0000\"", "_attachments": "attachments/", "_ts": 1752178045}, {"payPeriodId": "1080039133783109", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-01-04T00:00:00Z", "endDate": "2025-01-10T00:00:00Z", "submitByDate": "2025-01-15T00:00:00Z", "checkDate": "2025-01-17T00:00:00Z", "checkCount": 1, "id": "85318ede-3acf-48ff-aa62-3fe115ef232a", "companyId": "17153621", "type": "payperiod", "_rid": "NmJkAKiCbEe6wwIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEe6wwIAAAAAAA==/", "_etag": "\"a3009ce9-0000-0100-0000-68701d7d0000\"", "_attachments": "attachments/", "_ts": 1752178045}, {"payPeriodId": "1080039322543255", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-02-08T00:00:00Z", "endDate": "2025-02-14T00:00:00Z", "submitByDate": "2025-02-19T00:00:00Z", "checkDate": "2025-02-21T00:00:00Z", "checkCount": 1, "id": "96765ef9-b20d-43c8-996d-1ea0071e681c", "companyId": "17153621", "type": "payperiod", "_rid": "NmJkAKiCbEe7wwIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEe7wwIAAAAAAA==/", "_etag": "\"a3009fe9-0000-0100-0000-68701d7d0000\"", "_attachments": "attachments/", "_ts": 1752178045}, {"payPeriodId": "1080039497400939", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-03-01T00:00:00Z", "endDate": "2025-03-07T00:00:00Z", "submitByDate": "2025-03-12T00:00:00Z", "checkDate": "2025-03-14T00:00:00Z", "checkCount": 1, "id": "0cb8deff-8f93-4d92-805d-201dfee2bd2c", "companyId": "17153621", "type": "payperiod", "_rid": "NmJkAKiCbEe8wwIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEe8wwIAAAAAAA==/", "_etag": "\"a300a3e9-0000-0100-0000-68701d7d0000\"", "_attachments": "attachments/", "_ts": 1752178045}, {"payPeriodId": "1080039564283517", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-03-08T00:00:00Z", "endDate": "2025-03-14T00:00:00Z", "submitByDate": "2025-03-19T00:00:00Z", "checkDate": "2025-03-21T00:00:00Z", "checkCount": 1, "id": "66c92535-66c5-45fd-83e9-c0994dbc51c3", "companyId": "17153621", "type": "payperiod", "_rid": "NmJkAKiCbEe9wwIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEe9wwIAAAAAAA==/", "_etag": "\"a300a5e9-0000-0100-0000-68701d7d0000\"", "_attachments": "attachments/", "_ts": 1752178045}, {"payPeriodId": "1080039711691712", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-03-29T00:00:00Z", "endDate": "2025-04-04T00:00:00Z", "submitByDate": "2025-04-09T00:00:00Z", "checkDate": "2025-04-11T00:00:00Z", "checkCount": 0, "id": "6733e08a-dda7-4bb1-8f3e-724c163a1cc8", "companyId": "17153621", "type": "payperiod", "_rid": "NmJkAKiCbEe+wwIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEe+wwIAAAAAAA==/", "_etag": "\"a300a9e9-0000-0100-0000-68701d7d0000\"", "_attachments": "attachments/", "_ts": 1752178045}, {"payPeriodId": "1080039739726556", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-04-05T00:00:00Z", "endDate": "2025-04-11T00:00:00Z", "submitByDate": "2025-04-16T00:00:00Z", "checkDate": "2025-04-18T00:00:00Z", "checkCount": 0, "id": "71ae83c4-ec1e-4390-8b7a-96557f77da62", "companyId": "17153621", "type": "payperiod", "_rid": "NmJkAKiCbEe-wwIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEe-wwIAAAAAAA==/", "_etag": "\"a300ade9-0000-0100-0000-68701d7d0000\"", "_attachments": "attachments/", "_ts": 1752178045}, {"payPeriodId": "1080039846465165", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-04-19T00:00:00Z", "endDate": "2025-04-25T00:00:00Z", "submitByDate": "2025-04-30T00:00:00Z", "checkDate": "2025-05-02T00:00:00Z", "checkCount": 0, "id": "2a74d257-32bf-45e6-a1e3-b6f8d9e50cc6", "companyId": "17153621", "type": "payperiod", "_rid": "NmJkAKiCbEfAwwIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfAwwIAAAAAAA==/", "_etag": "\"a300b6e9-0000-0100-0000-68701d7e0000\"", "_attachments": "attachments/", "_ts": 1752178046}, {"payPeriodId": "1080040085699687", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-05-24T00:00:00Z", "endDate": "2025-05-30T00:00:00Z", "submitByDate": "2025-06-04T00:00:00Z", "checkDate": "2025-06-06T00:00:00Z", "checkCount": 0, "id": "905ef5ab-bdd3-4b37-bbaf-7a53e48be3a1", "companyId": "17153621", "type": "payperiod", "_rid": "NmJkAKiCbEfBwwIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfBwwIAAAAAAA==/", "_etag": "\"a300b8e9-0000-0100-0000-68701d7e0000\"", "_attachments": "attachments/", "_ts": 1752178046}, {"payPeriodId": "1080040085699688", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-05-31T00:00:00Z", "endDate": "2025-06-06T00:00:00Z", "submitByDate": "2025-06-11T00:00:00Z", "checkDate": "2025-06-13T00:00:00Z", "checkCount": 0, "id": "6094a423-ee78-4c08-8f8c-0155261f098a", "companyId": "17153621", "type": "payperiod", "_rid": "NmJkAKiCbEfCwwIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfCwwIAAAAAAA==/", "_etag": "\"a300bae9-0000-0100-0000-68701d7e0000\"", "_attachments": "attachments/", "_ts": 1752178046}, {"payPeriodId": "1080040139873414", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-06-07T00:00:00Z", "endDate": "2025-06-13T00:00:00Z", "submitByDate": "2025-06-18T00:00:00Z", "checkDate": "2025-06-20T00:00:00Z", "checkCount": 0, "id": "71198262-68f2-49d9-acc6-cb6404a63f3c", "companyId": "17153621", "type": "payperiod", "_rid": "NmJkAKiCbEfDwwIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfDwwIAAAAAAA==/", "_etag": "\"a300bce9-0000-0100-0000-68701d7e0000\"", "_attachments": "attachments/", "_ts": 1752178046}, {"payPeriodId": "1080040174736494", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-06-14T00:00:00Z", "endDate": "2025-06-20T00:00:00Z", "submitByDate": "2025-06-25T00:00:00Z", "checkDate": "2025-06-27T00:00:00Z", "checkCount": 0, "id": "a04f570b-3767-4930-aba6-e160807f0efd", "companyId": "17153621", "type": "payperiod", "_rid": "NmJkAKiCbEfEwwIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfEwwIAAAAAAA==/", "_etag": "\"a300c3e9-0000-0100-0000-68701d7e0000\"", "_attachments": "attachments/", "_ts": 1752178046}, {"payPeriodId": "1080040271737322", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-06-28T00:00:00Z", "endDate": "2025-07-04T00:00:00Z", "submitByDate": "2025-07-09T00:00:00Z", "checkDate": "2025-07-11T00:00:00Z", "checkCount": 0, "id": "82b22e63-ebae-4d9d-af4e-70508037b197", "companyId": "17153621", "type": "payperiod", "_rid": "NmJkAKiCbEfFwwIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfFwwIAAAAAAA==/", "_etag": "\"a300cae9-0000-0100-0000-68701d7e0000\"", "_attachments": "attachments/", "_ts": 1752178046}, {"payPeriodId": "1080040351124808", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-07-05T00:00:00Z", "endDate": "2025-07-11T00:00:00Z", "submitByDate": "2025-07-16T00:00:00Z", "checkDate": "2025-07-18T00:00:00Z", "checkCount": 0, "id": "635e19e3-6992-48d9-999e-efce5238d7cd", "companyId": "17153621", "type": "payperiod", "_rid": "NmJkAKiCbEfGwwIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfGwwIAAAAAAA==/", "_etag": "\"a300cfe9-0000-0100-0000-68701d7e0000\"", "_attachments": "attachments/", "_ts": 1752178046}, {"payPeriodId": "1080040421169483", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-07-12T00:00:00Z", "endDate": "2025-07-18T00:00:00Z", "submitByDate": "2025-07-23T00:00:00Z", "checkDate": "2025-07-25T00:00:00Z", "checkCount": 0, "id": "630dd654-0851-448c-a88b-d7392c3126a8", "companyId": "17153621", "type": "payperiod", "_rid": "NmJkAKiCbEfHwwIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfHwwIAAAAAAA==/", "_etag": "\"a300d4e9-0000-0100-0000-68701d7e0000\"", "_attachments": "attachments/", "_ts": 1752178046}, {"payPeriodId": "1080040482389197", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-07-19T00:00:00Z", "endDate": "2025-07-25T00:00:00Z", "submitByDate": "2025-07-30T00:00:00Z", "checkDate": "2025-08-01T00:00:00Z", "checkCount": 0, "id": "f597fbf4-7b84-4a06-bd60-ec36c4f5402f", "companyId": "17153621", "type": "payperiod", "_rid": "NmJkAKiCbEfIwwIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfIwwIAAAAAAA==/", "_etag": "\"a300d6e9-0000-0100-0000-68701d7e0000\"", "_attachments": "attachments/", "_ts": 1752178046}, {"payPeriodId": "1080040482389198", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-07-26T00:00:00Z", "endDate": "2025-08-01T00:00:00Z", "submitByDate": "2025-08-06T00:00:00Z", "checkDate": "2025-08-08T00:00:00Z", "checkCount": 0, "id": "5833b229-ec94-4a2e-85aa-df1c8dee05a0", "companyId": "17153621", "type": "payperiod", "_rid": "NmJkAKiCbEfJwwIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfJwwIAAAAAAA==/", "_etag": "\"a300d8e9-0000-0100-0000-68701d7e0000\"", "_attachments": "attachments/", "_ts": 1752178046}, {"payPeriodId": "1080040584463665", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-08-02T00:00:00Z", "endDate": "2025-08-08T00:00:00Z", "submitByDate": "2025-08-13T00:00:00Z", "checkDate": "2025-08-15T00:00:00Z", "checkCount": 0, "id": "180126cc-13d1-4102-8d9b-853b0942588b", "companyId": "17153621", "type": "payperiod", "_rid": "NmJkAKiCbEfKwwIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfKwwIAAAAAAA==/", "_etag": "\"a300dae9-0000-0100-0000-68701d7e0000\"", "_attachments": "attachments/", "_ts": 1752178046}, {"payPeriodId": "1080040584463666", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-08-09T00:00:00Z", "endDate": "2025-08-15T00:00:00Z", "submitByDate": "2025-08-20T00:00:00Z", "checkDate": "2025-08-22T00:00:00Z", "checkCount": 0, "id": "efbd8e5e-8242-4167-91e9-f658c6b2f68c", "companyId": "17153621", "type": "payperiod", "_rid": "NmJkAKiCbEfLwwIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfLwwIAAAAAAA==/", "_etag": "\"a300dbe9-0000-0100-0000-68701d7e0000\"", "_attachments": "attachments/", "_ts": 1752178046}, {"payPeriodId": "1080040680735683", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-08-16T00:00:00Z", "endDate": "2025-08-22T00:00:00Z", "submitByDate": "2025-08-27T00:00:00Z", "checkDate": "2025-08-29T00:00:00Z", "checkCount": 0, "id": "8f051b05-d523-477e-8a7a-d13d99a5b69a", "companyId": "17153621", "type": "payperiod", "_rid": "NmJkAKiCbEfMwwIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfMwwIAAAAAAA==/", "_etag": "\"a300dde9-0000-0100-0000-68701d7e0000\"", "_attachments": "attachments/", "_ts": 1752178046}, {"payPeriodId": "1080040680735684", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-08-23T00:00:00Z", "endDate": "2025-08-29T00:00:00Z", "submitByDate": "2025-09-03T00:00:00Z", "checkDate": "2025-09-05T00:00:00Z", "checkCount": 0, "id": "cdf827cc-9918-4fcc-8160-de8e9e80bc5c", "companyId": "17153621", "type": "payperiod", "_rid": "NmJkAKiCbEfNwwIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfNwwIAAAAAAA==/", "_etag": "\"a300e0e9-0000-0100-0000-68701d7f0000\"", "_attachments": "attachments/", "_ts": 1752178047}, {"payPeriodId": "1080040765357863", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-08-30T00:00:00Z", "endDate": "2025-09-05T00:00:00Z", "submitByDate": "2025-09-10T00:00:00Z", "checkDate": "2025-09-12T00:00:00Z", "checkCount": 0, "id": "60cad925-2e8c-4cae-85fa-7ce3a9edeb96", "companyId": "17153621", "type": "payperiod", "_rid": "NmJkAKiCbEfOwwIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfOwwIAAAAAAA==/", "_etag": "\"a300e2e9-0000-0100-0000-68701d7f0000\"", "_attachments": "attachments/", "_ts": 1752178047}, {"payPeriodId": "1080040802266783", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-09-06T00:00:00Z", "endDate": "2025-09-12T00:00:00Z", "submitByDate": "2025-09-17T00:00:00Z", "checkDate": "2025-09-19T00:00:00Z", "checkCount": 0, "id": "d7ba9fe7-1716-46b9-ab8a-3ea8787a4cd8", "companyId": "17153621", "type": "payperiod", "_rid": "NmJkAKiCbEfPwwIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfPwwIAAAAAAA==/", "_etag": "\"a300e6e9-0000-0100-0000-68701d7f0000\"", "_attachments": "attachments/", "_ts": 1752178047}, {"payPeriodId": "1080040845869256", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-09-13T00:00:00Z", "endDate": "2025-09-19T00:00:00Z", "submitByDate": "2025-09-24T00:00:00Z", "checkDate": "2025-09-26T00:00:00Z", "checkCount": 0, "id": "179a9478-c8b8-491c-a1db-d3dfdf1328ab", "companyId": "17153621", "type": "payperiod", "_rid": "NmJkAKiCbEfQwwIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfQwwIAAAAAAA==/", "_etag": "\"a300e8e9-0000-0100-0000-68701d7f0000\"", "_attachments": "attachments/", "_ts": 1752178047}, {"payPeriodId": "1080040901313756", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-09-20T00:00:00Z", "endDate": "2025-09-26T00:00:00Z", "submitByDate": "2025-10-01T00:00:00Z", "checkDate": "2025-10-03T00:00:00Z", "checkCount": 0, "id": "ca0d7916-011f-4513-a97b-8614f74a0349", "companyId": "17153621", "type": "payperiod", "_rid": "NmJkAKiCbEfRwwIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfRwwIAAAAAAA==/", "_etag": "\"a300ece9-0000-0100-0000-68701d7f0000\"", "_attachments": "attachments/", "_ts": 1752178047}, {"payPeriodId": "1080040962132721", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-09-27T00:00:00Z", "endDate": "2025-10-03T00:00:00Z", "submitByDate": "2025-10-08T00:00:00Z", "checkDate": "2025-10-10T00:00:00Z", "checkCount": 0, "id": "5e71aa4f-1828-41b5-bfff-dcb9b40646f1", "companyId": "17153621", "type": "payperiod", "_rid": "NmJkAKiCbEfSwwIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfSwwIAAAAAAA==/", "_etag": "\"a300f0e9-0000-0100-0000-68701d7f0000\"", "_attachments": "attachments/", "_ts": 1752178047}, {"payPeriodId": "1080040962132722", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-10-04T00:00:00Z", "endDate": "2025-10-10T00:00:00Z", "submitByDate": "2025-10-15T00:00:00Z", "checkDate": "2025-10-17T00:00:00Z", "checkCount": 0, "id": "c3fa25bd-477d-46c9-a887-4b50ecc56f97", "companyId": "17153621", "type": "payperiod", "_rid": "NmJkAKiCbEfTwwIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfTwwIAAAAAAA==/", "_etag": "\"a300f6e9-0000-0100-0000-68701d7f0000\"", "_attachments": "attachments/", "_ts": 1752178047}, {"payPeriodId": "1080040271737321", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-06-21T00:00:00Z", "endDate": "2025-06-27T00:00:00Z", "submitByDate": "2025-07-01T00:00:00Z", "checkDate": "2025-07-03T00:00:00Z", "checkCount": 0, "id": "c1072327-7e65-4e65-977f-e0bb108b0dfb", "companyId": "17153621", "type": "payperiod", "_rid": "NmJkAKiCbEfUwwIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfUwwIAAAAAAA==/", "_etag": "\"a300fae9-0000-0100-0000-68701d7f0000\"", "_attachments": "attachments/", "_ts": 1752178047}, {"payPeriodId": "1080039040688305", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2024-12-28T00:00:00Z", "endDate": "2025-01-03T00:00:00Z", "submitByDate": "2025-01-08T00:00:00Z", "checkDate": "2025-01-10T00:00:00Z", "checkCount": 1, "id": "6070c320-8fa4-4de5-acc5-db54effbc885", "companyId": "17153621", "type": "payperiod", "_rid": "NmJkAKiCbEfWwwIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfWwwIAAAAAAA==/", "_etag": "\"a300fee9-0000-0100-0000-68701d7f0000\"", "_attachments": "attachments/", "_ts": 1752178047}, {"payPeriodId": "1080039133783109", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-01-04T00:00:00Z", "endDate": "2025-01-10T00:00:00Z", "submitByDate": "2025-01-15T00:00:00Z", "checkDate": "2025-01-17T00:00:00Z", "checkCount": 1, "id": "1796f1d4-b59e-4236-9740-100f65f5c3e9", "companyId": "17153621", "type": "payperiod", "_rid": "NmJkAKiCbEfXwwIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfXwwIAAAAAAA==/", "_etag": "\"a30001ea-0000-0100-0000-68701d7f0000\"", "_attachments": "attachments/", "_ts": 1752178047}, {"payPeriodId": "1080039322543255", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-02-08T00:00:00Z", "endDate": "2025-02-14T00:00:00Z", "submitByDate": "2025-02-19T00:00:00Z", "checkDate": "2025-02-21T00:00:00Z", "checkCount": 1, "id": "a8f0a75d-79b6-40be-a6fe-2b98a1bb79ab", "companyId": "17153621", "type": "payperiod", "_rid": "NmJkAKiCbEfYwwIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfYwwIAAAAAAA==/", "_etag": "\"a30004ea-0000-0100-0000-68701d7f0000\"", "_attachments": "attachments/", "_ts": 1752178047}, {"payPeriodId": "1080039497400939", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-03-01T00:00:00Z", "endDate": "2025-03-07T00:00:00Z", "submitByDate": "2025-03-12T00:00:00Z", "checkDate": "2025-03-14T00:00:00Z", "checkCount": 1, "id": "4ec7d727-e073-45b9-91bd-6847d3b95848", "companyId": "17153621", "type": "payperiod", "_rid": "NmJkAKiCbEfZwwIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfZwwIAAAAAAA==/", "_etag": "\"a30009ea-0000-0100-0000-68701d800000\"", "_attachments": "attachments/", "_ts": 1752178048}, {"payPeriodId": "1080039564283517", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-03-08T00:00:00Z", "endDate": "2025-03-14T00:00:00Z", "submitByDate": "2025-03-19T00:00:00Z", "checkDate": "2025-03-21T00:00:00Z", "checkCount": 1, "id": "0c5644f8-0da9-48fc-af03-3a74307b21f4", "companyId": "17153621", "type": "payperiod", "_rid": "NmJkAKiCbEfawwIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfawwIAAAAAAA==/", "_etag": "\"a3000cea-0000-0100-0000-68701d800000\"", "_attachments": "attachments/", "_ts": 1752178048}, {"payPeriodId": "1080039711691712", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-03-29T00:00:00Z", "endDate": "2025-04-04T00:00:00Z", "submitByDate": "2025-04-09T00:00:00Z", "checkDate": "2025-04-11T00:00:00Z", "checkCount": 1, "id": "10c995f3-2446-430f-874e-c69823f9ea15", "companyId": "17153621", "type": "payperiod", "_rid": "NmJkAKiCbEfbwwIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfbwwIAAAAAAA==/", "_etag": "\"a3000dea-0000-0100-0000-68701d800000\"", "_attachments": "attachments/", "_ts": 1752178048}, {"payPeriodId": "1080039739726556", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-04-05T00:00:00Z", "endDate": "2025-04-11T00:00:00Z", "submitByDate": "2025-04-16T00:00:00Z", "checkDate": "2025-04-18T00:00:00Z", "checkCount": 1, "id": "d199a7f0-0195-4e99-b8eb-08fc248c07b4", "companyId": "17153621", "type": "payperiod", "_rid": "NmJkAKiCbEfcwwIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfcwwIAAAAAAA==/", "_etag": "\"a3000eea-0000-0100-0000-68701d800000\"", "_attachments": "attachments/", "_ts": 1752178048}, {"payPeriodId": "1080039846465165", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-04-19T00:00:00Z", "endDate": "2025-04-25T00:00:00Z", "submitByDate": "2025-04-30T00:00:00Z", "checkDate": "2025-05-02T00:00:00Z", "checkCount": 1, "id": "3de5287c-ea88-4d53-b4ed-5698e429fca2", "companyId": "17153621", "type": "payperiod", "_rid": "NmJkAKiCbEfdwwIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfdwwIAAAAAAA==/", "_etag": "\"a30011ea-0000-0100-0000-68701d800000\"", "_attachments": "attachments/", "_ts": 1752178048}, {"payPeriodId": "1080040085699687", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-05-24T00:00:00Z", "endDate": "2025-05-30T00:00:00Z", "submitByDate": "2025-06-04T00:00:00Z", "checkDate": "2025-06-06T00:00:00Z", "checkCount": 1, "id": "0df677ed-0e66-41bd-9953-3a97540a8b6f", "companyId": "17153621", "type": "payperiod", "_rid": "NmJkAKiCbEfewwIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfewwIAAAAAAA==/", "_etag": "\"a30018ea-0000-0100-0000-68701d800000\"", "_attachments": "attachments/", "_ts": 1752178048}, {"payPeriodId": "1080040085699688", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-05-31T00:00:00Z", "endDate": "2025-06-06T00:00:00Z", "submitByDate": "2025-06-11T00:00:00Z", "checkDate": "2025-06-13T00:00:00Z", "checkCount": 1, "id": "33646db7-128b-456b-b328-cde1904d3727", "companyId": "17153621", "type": "payperiod", "_rid": "NmJkAKiCbEffwwIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEffwwIAAAAAAA==/", "_etag": "\"a3001cea-0000-0100-0000-68701d800000\"", "_attachments": "attachments/", "_ts": 1752178048}, {"payPeriodId": "1080040139873414", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-06-07T00:00:00Z", "endDate": "2025-06-13T00:00:00Z", "submitByDate": "2025-06-18T00:00:00Z", "checkDate": "2025-06-20T00:00:00Z", "checkCount": 1, "id": "433e9add-384e-456b-aa94-133b5589a756", "companyId": "17153621", "type": "payperiod", "_rid": "NmJkAKiCbEfgwwIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfgwwIAAAAAAA==/", "_etag": "\"a3001eea-0000-0100-0000-68701d800000\"", "_attachments": "attachments/", "_ts": 1752178048}, {"payPeriodId": "1080040174736494", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-06-14T00:00:00Z", "endDate": "2025-06-20T00:00:00Z", "submitByDate": "2025-06-25T00:00:00Z", "checkDate": "2025-06-27T00:00:00Z", "checkCount": 1, "id": "d049f2c0-79fb-42a9-b404-b2a02816a6b5", "companyId": "17153621", "type": "payperiod", "_rid": "NmJkAKiCbEfhwwIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfhwwIAAAAAAA==/", "_etag": "\"a30021ea-0000-0100-0000-68701d800000\"", "_attachments": "attachments/", "_ts": 1752178048}, {"payPeriodId": "1080040271737322", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-06-28T00:00:00Z", "endDate": "2025-07-04T00:00:00Z", "submitByDate": "2025-07-09T00:00:00Z", "checkDate": "2025-07-11T00:00:00Z", "checkCount": 0, "id": "21ec261f-e62d-4e85-b4e7-ae09e88d9fe9", "companyId": "17153621", "type": "payperiod", "_rid": "NmJkAKiCbEfiwwIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfiwwIAAAAAAA==/", "_etag": "\"a30026ea-0000-0100-0000-68701d800000\"", "_attachments": "attachments/", "_ts": 1752178048}, {"payPeriodId": "1080040351124808", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-07-05T00:00:00Z", "endDate": "2025-07-11T00:00:00Z", "submitByDate": "2025-07-16T00:00:00Z", "checkDate": "2025-07-18T00:00:00Z", "checkCount": 0, "id": "da15e93e-ee12-442a-83f1-472eebf5ff0c", "companyId": "17153621", "type": "payperiod", "_rid": "NmJkAKiCbEfjwwIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfjwwIAAAAAAA==/", "_etag": "\"a30029ea-0000-0100-0000-68701d800000\"", "_attachments": "attachments/", "_ts": 1752178048}, {"payPeriodId": "1080040421169483", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-07-12T00:00:00Z", "endDate": "2025-07-18T00:00:00Z", "submitByDate": "2025-07-23T00:00:00Z", "checkDate": "2025-07-25T00:00:00Z", "checkCount": 0, "id": "430cd4c5-8d22-4f84-96bf-63634c7f505a", "companyId": "17153621", "type": "payperiod", "_rid": "NmJkAKiCbEfkwwIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfkwwIAAAAAAA==/", "_etag": "\"a3002dea-0000-0100-0000-68701d800000\"", "_attachments": "attachments/", "_ts": 1752178048}, {"payPeriodId": "1080040482389197", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-07-19T00:00:00Z", "endDate": "2025-07-25T00:00:00Z", "submitByDate": "2025-07-30T00:00:00Z", "checkDate": "2025-08-01T00:00:00Z", "checkCount": 0, "id": "55768333-7f64-4cf9-87e5-7a15eb11a184", "companyId": "17153621", "type": "payperiod", "_rid": "NmJkAKiCbEflwwIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEflwwIAAAAAAA==/", "_etag": "\"a30031ea-0000-0100-0000-68701d800000\"", "_attachments": "attachments/", "_ts": 1752178048}, {"payPeriodId": "1080040482389198", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-07-26T00:00:00Z", "endDate": "2025-08-01T00:00:00Z", "submitByDate": "2025-08-06T00:00:00Z", "checkDate": "2025-08-08T00:00:00Z", "checkCount": 0, "id": "c502167c-5ae5-45a5-a251-c2db8f495e56", "companyId": "17153621", "type": "payperiod", "_rid": "NmJkAKiCbEfmwwIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfmwwIAAAAAAA==/", "_etag": "\"a30033ea-0000-0100-0000-68701d810000\"", "_attachments": "attachments/", "_ts": 1752178049}, {"payPeriodId": "1080040584463665", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-08-02T00:00:00Z", "endDate": "2025-08-08T00:00:00Z", "submitByDate": "2025-08-13T00:00:00Z", "checkDate": "2025-08-15T00:00:00Z", "checkCount": 0, "id": "c8c6c8a0-4fc4-4543-a99c-bce833f8baef", "companyId": "17153621", "type": "payperiod", "_rid": "NmJkAKiCbEfnwwIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfnwwIAAAAAAA==/", "_etag": "\"a30035ea-0000-0100-0000-68701d810000\"", "_attachments": "attachments/", "_ts": 1752178049}, {"payPeriodId": "1080040584463666", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-08-09T00:00:00Z", "endDate": "2025-08-15T00:00:00Z", "submitByDate": "2025-08-20T00:00:00Z", "checkDate": "2025-08-22T00:00:00Z", "checkCount": 0, "id": "7cbc74d1-ba61-417d-a8f5-07cb11ba5e6c", "companyId": "17153621", "type": "payperiod", "_rid": "NmJkAKiCbEfowwIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfowwIAAAAAAA==/", "_etag": "\"a3003aea-0000-0100-0000-68701d810000\"", "_attachments": "attachments/", "_ts": 1752178049}, {"payPeriodId": "1080040680735683", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-08-16T00:00:00Z", "endDate": "2025-08-22T00:00:00Z", "submitByDate": "2025-08-27T00:00:00Z", "checkDate": "2025-08-29T00:00:00Z", "checkCount": 0, "id": "0a439d01-6f8e-4df1-a42b-3f855e4255f4", "companyId": "17153621", "type": "payperiod", "_rid": "NmJkAKiCbEfpwwIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfpwwIAAAAAAA==/", "_etag": "\"a3003dea-0000-0100-0000-68701d810000\"", "_attachments": "attachments/", "_ts": 1752178049}, {"payPeriodId": "1080040680735684", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-08-23T00:00:00Z", "endDate": "2025-08-29T00:00:00Z", "submitByDate": "2025-09-03T00:00:00Z", "checkDate": "2025-09-05T00:00:00Z", "checkCount": 0, "id": "b0fdcc26-905b-42da-82be-244985c04a79", "companyId": "17153621", "type": "payperiod", "_rid": "NmJkAKiCbEfqwwIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfqwwIAAAAAAA==/", "_etag": "\"a30040ea-0000-0100-0000-68701d810000\"", "_attachments": "attachments/", "_ts": 1752178049}, {"payPeriodId": "1080040765357863", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-08-30T00:00:00Z", "endDate": "2025-09-05T00:00:00Z", "submitByDate": "2025-09-10T00:00:00Z", "checkDate": "2025-09-12T00:00:00Z", "checkCount": 0, "id": "ab506064-364e-4786-ab84-c846df954fc4", "companyId": "17153621", "type": "payperiod", "_rid": "NmJkAKiCbEfrwwIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfrwwIAAAAAAA==/", "_etag": "\"a30043ea-0000-0100-0000-68701d810000\"", "_attachments": "attachments/", "_ts": 1752178049}, {"payPeriodId": "1080040802266783", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-09-06T00:00:00Z", "endDate": "2025-09-12T00:00:00Z", "submitByDate": "2025-09-17T00:00:00Z", "checkDate": "2025-09-19T00:00:00Z", "checkCount": 0, "id": "2210c92a-3be6-495c-820a-b3c3b52df020", "companyId": "17153621", "type": "payperiod", "_rid": "NmJkAKiCbEfswwIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfswwIAAAAAAA==/", "_etag": "\"a30045ea-0000-0100-0000-68701d810000\"", "_attachments": "attachments/", "_ts": 1752178049}, {"payPeriodId": "1080040845869256", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-09-13T00:00:00Z", "endDate": "2025-09-19T00:00:00Z", "submitByDate": "2025-09-24T00:00:00Z", "checkDate": "2025-09-26T00:00:00Z", "checkCount": 0, "id": "3793b9fc-6126-4a20-a40f-0827d651188c", "companyId": "17153621", "type": "payperiod", "_rid": "NmJkAKiCbEftwwIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEftwwIAAAAAAA==/", "_etag": "\"a30046ea-0000-0100-0000-68701d810000\"", "_attachments": "attachments/", "_ts": 1752178049}, {"payPeriodId": "1080040901313756", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-09-20T00:00:00Z", "endDate": "2025-09-26T00:00:00Z", "submitByDate": "2025-10-01T00:00:00Z", "checkDate": "2025-10-03T00:00:00Z", "checkCount": 0, "id": "97593f5d-7556-4be4-9053-946a4dd83e65", "companyId": "17153621", "type": "payperiod", "_rid": "NmJkAKiCbEfuwwIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfuwwIAAAAAAA==/", "_etag": "\"a3004bea-0000-0100-0000-68701d810000\"", "_attachments": "attachments/", "_ts": 1752178049}, {"payPeriodId": "1080040962132721", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-09-27T00:00:00Z", "endDate": "2025-10-03T00:00:00Z", "submitByDate": "2025-10-08T00:00:00Z", "checkDate": "2025-10-10T00:00:00Z", "checkCount": 0, "id": "72a3f53a-730a-4b25-b312-cd9e442cfad3", "companyId": "17153621", "type": "payperiod", "_rid": "NmJkAKiCbEfvwwIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfvwwIAAAAAAA==/", "_etag": "\"a3004fea-0000-0100-0000-68701d810000\"", "_attachments": "attachments/", "_ts": 1752178049}, {"payPeriodId": "1080040962132722", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-10-04T00:00:00Z", "endDate": "2025-10-10T00:00:00Z", "submitByDate": "2025-10-15T00:00:00Z", "checkDate": "2025-10-17T00:00:00Z", "checkCount": 0, "id": "6a5cdc0a-5721-4347-8415-c33ac403fde0", "companyId": "17153621", "type": "payperiod", "_rid": "NmJkAKiCbEfwwwIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfwwwIAAAAAAA==/", "_etag": "\"a30050ea-0000-0100-0000-68701d810000\"", "_attachments": "attachments/", "_ts": 1752178049}, {"payPeriodId": "1080040271737321", "intervalCode": "WEEKLY", "status": "ENTRY", "description": "Weekly Payroll (1)", "startDate": "2025-06-21T00:00:00Z", "endDate": "2025-06-27T00:00:00Z", "submitByDate": "2025-07-01T00:00:00Z", "checkDate": "2025-07-03T00:00:00Z", "checkCount": 1, "id": "30eb0bcd-c174-479e-a9a0-83bf1f9b5ae3", "companyId": "17153621", "type": "payperiod", "_rid": "NmJkAKiCbEfxwwIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfxwwIAAAAAAA==/", "_etag": "\"a30051ea-0000-0100-0000-68701d810000\"", "_attachments": "attachments/", "_ts": 1752178049}], "links": [{"rel": "self", "href": "https://ca-payroll-ai-mock-sb-001-secure.nicebeach-8a7a52ab.eastus.azurecontainerapps.io/ca/companies/17153621/payperiods"}]}, "status_code": 200}