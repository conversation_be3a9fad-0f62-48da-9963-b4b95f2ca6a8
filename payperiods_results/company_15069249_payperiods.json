{"success": true, "company_id": "15069249", "data": {"metadata": {"contentItemCount": 22}, "content": [{"payPeriodId": "1060038969770081", "intervalCode": "MONTHLY", "status": "COMPLETED", "description": "Monthly Payroll (1)", "startDate": "2025-01-01T00:00:00Z", "endDate": "2025-01-31T00:00:00Z", "submitByDate": "2025-02-02T00:00:00Z", "checkDate": "2025-02-03T00:00:00Z", "checkCount": 2, "id": "9ecfaf8a-1485-4399-9cec-48a268fdcdf8", "companyId": "15069249", "type": "payperiod", "_rid": "NmJkAKiCbEfe5gIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfe5gIAAAAAAA==/", "_etag": "\"a4006f62-0000-0100-0000-687020560000\"", "_attachments": "attachments/", "_ts": 1752178774}, {"payPeriodId": "1060039122377930", "intervalCode": "MONTHLY", "status": "COMPLETED", "description": "Monthly Payroll (1)", "startDate": "2025-02-01T00:00:00Z", "endDate": "2025-02-28T00:00:00Z", "submitByDate": "2025-03-02T00:00:00Z", "checkDate": "2025-03-03T00:00:00Z", "checkCount": 2, "id": "4a8e26ae-e5fd-4929-848e-f2f9a7829074", "companyId": "15069249", "type": "payperiod", "_rid": "NmJkAKiCbEff5gIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEff5gIAAAAAAA==/", "_etag": "\"a4007362-0000-0100-0000-687020560000\"", "_attachments": "attachments/", "_ts": 1752178774}, {"payPeriodId": "1060039257378322", "intervalCode": "MONTHLY", "status": "INITIAL", "description": "Monthly Payroll (1)", "startDate": "2025-03-01T00:00:00Z", "endDate": "2025-03-31T00:00:00Z", "submitByDate": "2025-03-31T00:00:00Z", "checkDate": "2025-04-01T00:00:00Z", "checkCount": 0, "id": "47377767-b9b3-4d55-a521-fa0156b8a4df", "companyId": "15069249", "type": "payperiod", "_rid": "NmJkAKiCbEfg5gIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfg5gIAAAAAAA==/", "_etag": "\"a4007862-0000-0100-0000-687020560000\"", "_attachments": "attachments/", "_ts": 1752178774}, {"payPeriodId": "1060039433971277", "intervalCode": "MONTHLY", "status": "INITIAL", "description": "Monthly Payroll (1)", "startDate": "2025-04-01T00:00:00Z", "endDate": "2025-04-30T00:00:00Z", "submitByDate": "2025-04-30T00:00:00Z", "checkDate": "2025-05-01T00:00:00Z", "checkCount": 0, "id": "62860b37-1dd3-4a40-b548-271fe2aeeb45", "companyId": "15069249", "type": "payperiod", "_rid": "NmJkAKiCbEfh5gIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfh5gIAAAAAAA==/", "_etag": "\"a4007c62-0000-0100-0000-687020560000\"", "_attachments": "attachments/", "_ts": 1752178774}, {"payPeriodId": "1060039585792227", "intervalCode": "MONTHLY", "status": "INITIAL", "description": "Monthly Payroll (1)", "startDate": "2025-05-01T00:00:00Z", "endDate": "2025-05-31T00:00:00Z", "submitByDate": "2025-06-01T00:00:00Z", "checkDate": "2025-06-02T00:00:00Z", "checkCount": 0, "id": "ba1e8824-9d8c-4b56-9aed-f075e19c20d8", "companyId": "15069249", "type": "payperiod", "_rid": "NmJkAKiCbEfi5gIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfi5gIAAAAAAA==/", "_etag": "\"a4007f62-0000-0100-0000-687020560000\"", "_attachments": "attachments/", "_ts": 1752178774}, {"payPeriodId": "1060039724326547", "intervalCode": "MONTHLY", "status": "INITIAL", "description": "Monthly Payroll (1)", "startDate": "2025-06-01T00:00:00Z", "endDate": "2025-06-30T00:00:00Z", "submitByDate": "2025-06-30T00:00:00Z", "checkDate": "2025-07-01T00:00:00Z", "checkCount": 0, "id": "8f5eb592-f23d-4154-a1d3-ad7b03a73a29", "companyId": "15069249", "type": "payperiod", "_rid": "NmJkAKiCbEfj5gIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfj5gIAAAAAAA==/", "_etag": "\"a4008162-0000-0100-0000-687020560000\"", "_attachments": "attachments/", "_ts": 1752178774}, {"payPeriodId": "1060040342966679", "status": "INITIAL", "description": "Missed <PERSON><PERSON>", "startDate": "2025-06-01T00:00:00Z", "endDate": "2025-06-30T00:00:00Z", "submitByDate": "2025-06-30T00:00:00Z", "checkDate": "2025-07-01T00:00:00Z", "checkCount": 0, "id": "b3626989-cde6-42a7-ba5c-b75fe00ab7f9", "companyId": "15069249", "type": "payperiod", "_rid": "NmJkAKiCbEfk5gIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfk5gIAAAAAAA==/", "_etag": "\"a4008662-0000-0100-0000-687020560000\"", "_attachments": "attachments/", "_ts": 1752178774}, {"payPeriodId": "1060039904991150", "intervalCode": "MONTHLY", "status": "INITIAL", "description": "Monthly Payroll (1)", "startDate": "2025-07-01T00:00:00Z", "endDate": "2025-07-31T00:00:00Z", "submitByDate": "2025-07-29T00:00:00Z", "checkDate": "2025-07-31T00:00:00Z", "checkCount": 0, "id": "7e2e3d69-0696-4df2-a261-150702c896c6", "companyId": "15069249", "type": "payperiod", "_rid": "NmJkAKiCbEfl5gIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfl5gIAAAAAAA==/", "_etag": "\"a4008762-0000-0100-0000-687020560000\"", "_attachments": "attachments/", "_ts": 1752178774}, {"payPeriodId": "1060040055962042", "intervalCode": "MONTHLY", "status": "INITIAL", "description": "Monthly Payroll (1)", "startDate": "2025-08-01T00:00:00Z", "endDate": "2025-08-31T00:00:00Z", "submitByDate": "2025-08-27T00:00:00Z", "checkDate": "2025-08-29T00:00:00Z", "checkCount": 0, "id": "8fb51c6f-3b8a-4e4a-bd44-dfe6af5b8aa1", "companyId": "15069249", "type": "payperiod", "_rid": "NmJkAKiCbEfm5gIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfm5gIAAAAAAA==/", "_etag": "\"a4008c62-0000-0100-0000-687020570000\"", "_attachments": "attachments/", "_ts": 1752178775}, {"payPeriodId": "1060040191684042", "intervalCode": "MONTHLY", "status": "INITIAL", "description": "Monthly Payroll (1)", "startDate": "2025-09-01T00:00:00Z", "endDate": "2025-09-30T00:00:00Z", "submitByDate": "2025-09-26T00:00:00Z", "checkDate": "2025-09-30T00:00:00Z", "checkCount": 0, "id": "722de1ca-6d04-47bc-a4e7-d3ebb7ae4629", "companyId": "15069249", "type": "payperiod", "_rid": "NmJkAKiCbEfn5gIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfn5gIAAAAAAA==/", "_etag": "\"a4009162-0000-0100-0000-687020570000\"", "_attachments": "attachments/", "_ts": 1752178775}, {"payPeriodId": "1060040369768019", "intervalCode": "MONTHLY", "status": "INITIAL", "description": "Monthly Payroll (1)", "startDate": "2025-10-01T00:00:00Z", "endDate": "2025-10-31T00:00:00Z", "submitByDate": "2025-10-29T00:00:00Z", "checkDate": "2025-10-31T00:00:00Z", "checkCount": 0, "id": "d164aa75-8bee-489e-ab14-a1a187ee302e", "companyId": "15069249", "type": "payperiod", "_rid": "NmJkAKiCbEfo5gIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfo5gIAAAAAAA==/", "_etag": "\"a4009662-0000-0100-0000-687020570000\"", "_attachments": "attachments/", "_ts": 1752178775}, {"payPeriodId": "1060038969770081", "intervalCode": "MONTHLY", "status": "COMPLETED", "description": "Monthly Payroll (1)", "startDate": "2025-01-01T00:00:00Z", "endDate": "2025-01-31T00:00:00Z", "submitByDate": "2025-02-02T00:00:00Z", "checkDate": "2025-02-03T00:00:00Z", "checkCount": 2, "id": "ecea164d-6e29-4400-a4b5-3eadd58ad5d4", "companyId": "15069249", "type": "payperiod", "_rid": "NmJkAKiCbEfq5gIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfq5gIAAAAAAA==/", "_etag": "\"a400a362-0000-0100-0000-687020570000\"", "_attachments": "attachments/", "_ts": 1752178775}, {"payPeriodId": "1060039122377930", "intervalCode": "MONTHLY", "status": "COMPLETED", "description": "Monthly Payroll (1)", "startDate": "2025-02-01T00:00:00Z", "endDate": "2025-02-28T00:00:00Z", "submitByDate": "2025-03-02T00:00:00Z", "checkDate": "2025-03-03T00:00:00Z", "checkCount": 2, "id": "bfde2979-30e4-40c9-bcef-cd4fe0de6137", "companyId": "15069249", "type": "payperiod", "_rid": "NmJkAKiCbEfr5gIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfr5gIAAAAAAA==/", "_etag": "\"a400a762-0000-0100-0000-687020570000\"", "_attachments": "attachments/", "_ts": 1752178775}, {"payPeriodId": "1060039257378322", "intervalCode": "MONTHLY", "status": "COMPLETED", "description": "Monthly Payroll (1)", "startDate": "2025-03-01T00:00:00Z", "endDate": "2025-03-31T00:00:00Z", "submitByDate": "2025-03-31T00:00:00Z", "checkDate": "2025-04-01T00:00:00Z", "checkCount": 2, "id": "694ba46e-13ab-4341-b2a5-07a77c0af1aa", "companyId": "15069249", "type": "payperiod", "_rid": "NmJkAKiCbEfs5gIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfs5gIAAAAAAA==/", "_etag": "\"a400ac62-0000-0100-0000-687020570000\"", "_attachments": "attachments/", "_ts": 1752178775}, {"payPeriodId": "1060039433971277", "intervalCode": "MONTHLY", "status": "COMPLETED", "description": "Monthly Payroll (1)", "startDate": "2025-04-01T00:00:00Z", "endDate": "2025-04-30T00:00:00Z", "submitByDate": "2025-04-30T00:00:00Z", "checkDate": "2025-05-01T00:00:00Z", "checkCount": 2, "id": "fd457191-56a5-4533-8acd-274f36e8f346", "companyId": "15069249", "type": "payperiod", "_rid": "NmJkAKiCbEft5gIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEft5gIAAAAAAA==/", "_etag": "\"a400b062-0000-0100-0000-687020570000\"", "_attachments": "attachments/", "_ts": 1752178775}, {"payPeriodId": "1060039585792227", "intervalCode": "MONTHLY", "status": "COMPLETED", "description": "Monthly Payroll (1)", "startDate": "2025-05-01T00:00:00Z", "endDate": "2025-05-31T00:00:00Z", "submitByDate": "2025-06-01T00:00:00Z", "checkDate": "2025-06-02T00:00:00Z", "checkCount": 2, "id": "e2385d27-3fe8-486c-9644-af16e525c7c3", "companyId": "15069249", "type": "payperiod", "_rid": "NmJkAKiCbEfu5gIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfu5gIAAAAAAA==/", "_etag": "\"a400b462-0000-0100-0000-687020570000\"", "_attachments": "attachments/", "_ts": 1752178775}, {"payPeriodId": "1060039724326547", "intervalCode": "MONTHLY", "status": "COMPLETED", "description": "Monthly Payroll (1)", "startDate": "2025-06-01T00:00:00Z", "endDate": "2025-06-30T00:00:00Z", "submitByDate": "2025-06-30T00:00:00Z", "checkDate": "2025-07-01T00:00:00Z", "checkCount": 2, "id": "6a926570-88b0-40bc-b322-fd383a07a19c", "companyId": "15069249", "type": "payperiod", "_rid": "NmJkAKiCbEfv5gIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfv5gIAAAAAAA==/", "_etag": "\"a400bb62-0000-0100-0000-687020570000\"", "_attachments": "attachments/", "_ts": 1752178775}, {"payPeriodId": "1060040342966679", "status": "COMPLETED", "description": "Missed <PERSON><PERSON>", "startDate": "2025-06-01T00:00:00Z", "endDate": "2025-06-30T00:00:00Z", "submitByDate": "2025-06-30T00:00:00Z", "checkDate": "2025-07-01T00:00:00Z", "checkCount": 1, "id": "c43c279d-462c-4700-92c0-f0c1d20fe15c", "companyId": "15069249", "type": "payperiod", "_rid": "NmJkAKiCbEfw5gIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfw5gIAAAAAAA==/", "_etag": "\"a400bd62-0000-0100-0000-687020570000\"", "_attachments": "attachments/", "_ts": 1752178775}, {"payPeriodId": "1060039904991150", "intervalCode": "MONTHLY", "status": "INITIAL", "description": "Monthly Payroll (1)", "startDate": "2025-07-01T00:00:00Z", "endDate": "2025-07-31T00:00:00Z", "submitByDate": "2025-07-29T00:00:00Z", "checkDate": "2025-07-31T00:00:00Z", "checkCount": 0, "id": "5a400d26-dadb-4f98-9941-3fe17acf29ab", "companyId": "15069249", "type": "payperiod", "_rid": "NmJkAKiCbEfx5gIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfx5gIAAAAAAA==/", "_etag": "\"a400c062-0000-0100-0000-687020570000\"", "_attachments": "attachments/", "_ts": 1752178775}, {"payPeriodId": "1060040055962042", "intervalCode": "MONTHLY", "status": "INITIAL", "description": "Monthly Payroll (1)", "startDate": "2025-08-01T00:00:00Z", "endDate": "2025-08-31T00:00:00Z", "submitByDate": "2025-08-27T00:00:00Z", "checkDate": "2025-08-29T00:00:00Z", "checkCount": 0, "id": "54d9b562-532b-4274-b5e1-e684e409862c", "companyId": "15069249", "type": "payperiod", "_rid": "NmJkAKiCbEfy5gIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfy5gIAAAAAAA==/", "_etag": "\"a400c662-0000-0100-0000-687020570000\"", "_attachments": "attachments/", "_ts": 1752178775}, {"payPeriodId": "1060040191684042", "intervalCode": "MONTHLY", "status": "INITIAL", "description": "Monthly Payroll (1)", "startDate": "2025-09-01T00:00:00Z", "endDate": "2025-09-30T00:00:00Z", "submitByDate": "2025-09-26T00:00:00Z", "checkDate": "2025-09-30T00:00:00Z", "checkCount": 0, "id": "7892e15e-7629-410a-8929-b992fa619f25", "companyId": "15069249", "type": "payperiod", "_rid": "NmJkAKiCbEfz5gIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfz5gIAAAAAAA==/", "_etag": "\"a400ce62-0000-0100-0000-687020580000\"", "_attachments": "attachments/", "_ts": 1752178776}, {"payPeriodId": "1060040369768019", "intervalCode": "MONTHLY", "status": "INITIAL", "description": "Monthly Payroll (1)", "startDate": "2025-10-01T00:00:00Z", "endDate": "2025-10-31T00:00:00Z", "submitByDate": "2025-10-29T00:00:00Z", "checkDate": "2025-10-31T00:00:00Z", "checkCount": 0, "id": "e50100af-6956-4c3a-b79c-4417289a07fb", "companyId": "15069249", "type": "payperiod", "_rid": "NmJkAKiCbEf05gIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf05gIAAAAAAA==/", "_etag": "\"a400d162-0000-0100-0000-687020580000\"", "_attachments": "attachments/", "_ts": 1752178776}], "links": [{"rel": "self", "href": "https://ca-payroll-ai-mock-sb-001-secure.nicebeach-8a7a52ab.eastus.azurecontainerapps.io/ca/companies/15069249/payperiods"}]}, "status_code": 200}