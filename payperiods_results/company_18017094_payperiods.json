{"success": true, "company_id": "18017094", "data": {"metadata": {"contentItemCount": 18}, "content": [{"payPeriodId": "1090065845817851", "intervalCode": "MONTHLY", "status": "COMPLETED", "description": "Monthly Payroll (1)", "startDate": "2024-12-24T00:00:00Z", "endDate": "2025-01-23T00:00:00Z", "submitByDate": "2025-01-24T00:00:00Z", "checkDate": "2025-01-28T00:00:00Z", "checkCount": 1, "id": "e0560c97-d0a0-46e4-99f7-c262b2b34fba", "companyId": "18017094", "type": "payperiod", "_rid": "NmJkAKiCbEdjkgQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdjkgQAAAAAAA==/", "_etag": "\"a9009506-0000-0100-0000-687044360000\"", "_attachments": "attachments/", "_ts": 1752187958}, {"payPeriodId": "1090066461419435", "intervalCode": "MONTHLY", "status": "COMPLETED", "description": "Monthly Payroll (1)", "startDate": "2025-01-24T00:00:00Z", "endDate": "2025-02-23T00:00:00Z", "submitByDate": "2025-02-26T00:00:00Z", "checkDate": "2025-02-28T00:00:00Z", "checkCount": 1, "id": "a1ede87f-c616-436f-bc9c-9be8a174282f", "companyId": "18017094", "type": "payperiod", "_rid": "NmJkAKiCbEdkkgQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdkkgQAAAAAAA==/", "_etag": "\"a9009606-0000-0100-0000-687044360000\"", "_attachments": "attachments/", "_ts": 1752187958}, {"payPeriodId": "1090069791704138", "status": "COMPLETED", "description": "Monthly Payroll (1)", "startDate": "2025-02-24T00:00:00Z", "endDate": "2025-03-23T00:00:00Z", "submitByDate": "2025-03-26T00:00:00Z", "checkDate": "2025-03-27T00:00:00Z", "checkCount": 1, "id": "11f5ffb7-6ff5-4b80-a9e9-7bda3c6b7117", "companyId": "18017094", "type": "payperiod", "_rid": "NmJkAKiCbEdlkgQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdlkgQAAAAAAA==/", "_etag": "\"a9009806-0000-0100-0000-687044360000\"", "_attachments": "attachments/", "_ts": 1752187958}, {"payPeriodId": "1090067841759336", "intervalCode": "MONTHLY", "status": "INITIAL", "description": "Monthly Payroll (1)", "startDate": "2025-03-24T00:00:00Z", "endDate": "2025-04-23T00:00:00Z", "submitByDate": "2025-04-26T00:00:00Z", "checkDate": "2025-04-27T00:00:00Z", "checkCount": 0, "id": "0e153d67-22e9-441e-8f88-55146e8fa520", "companyId": "18017094", "type": "payperiod", "_rid": "NmJkAKiCbEdmkgQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdmkgQAAAAAAA==/", "_etag": "\"a9009a06-0000-0100-0000-687044360000\"", "_attachments": "attachments/", "_ts": 1752187958}, {"payPeriodId": "1090068475853821", "intervalCode": "MONTHLY", "status": "INITIAL", "description": "Monthly Payroll (1)", "startDate": "2025-04-24T00:00:00Z", "endDate": "2025-05-23T00:00:00Z", "submitByDate": "2025-05-23T00:00:00Z", "checkDate": "2025-05-28T00:00:00Z", "checkCount": 0, "id": "c5847fcf-ed59-48f7-ac5c-4b4ce49f3c5a", "companyId": "18017094", "type": "payperiod", "_rid": "NmJkAKiCbEdnkgQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdnkgQAAAAAAA==/", "_etag": "\"a9009b06-0000-0100-0000-687044360000\"", "_attachments": "attachments/", "_ts": 1752187958}, {"payPeriodId": "1090070130331698", "intervalCode": "MONTHLY", "status": "INITIAL", "description": "Monthly Payroll (1)", "startDate": "2025-06-24T00:00:00Z", "endDate": "2025-07-23T00:00:00Z", "submitByDate": "2025-07-24T00:00:00Z", "checkDate": "2025-07-28T00:00:00Z", "checkCount": 0, "id": "ae1ecc75-9670-4b8e-bd26-f4891d7cb32a", "companyId": "18017094", "type": "payperiod", "_rid": "NmJkAKiCbEdokgQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdokgQAAAAAAA==/", "_etag": "\"a9009d06-0000-0100-0000-687044360000\"", "_attachments": "attachments/", "_ts": 1752187958}, {"payPeriodId": "1090070450138734", "intervalCode": "MONTHLY", "status": "INITIAL", "description": "Monthly Payroll (1)", "startDate": "2025-07-24T00:00:00Z", "endDate": "2025-08-23T00:00:00Z", "submitByDate": "2025-08-26T00:00:00Z", "checkDate": "2025-08-28T00:00:00Z", "checkCount": 0, "id": "4e53972a-e05b-49ce-a446-e4e8c9f180e6", "companyId": "18017094", "type": "payperiod", "_rid": "NmJkAKiCbEdpkgQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdpkgQAAAAAAA==/", "_etag": "\"a9009f06-0000-0100-0000-687044360000\"", "_attachments": "attachments/", "_ts": 1752187958}, {"payPeriodId": "1090071096200227", "intervalCode": "MONTHLY", "status": "INITIAL", "description": "Monthly Payroll (1)", "startDate": "2025-08-24T00:00:00Z", "endDate": "2025-09-23T00:00:00Z", "submitByDate": "2025-09-24T00:00:00Z", "checkDate": "2025-09-26T00:00:00Z", "checkCount": 0, "id": "00d24df0-0b63-43fb-83f2-5423dac1f22d", "companyId": "18017094", "type": "payperiod", "_rid": "NmJkAKiCbEdqkgQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdqkgQAAAAAAA==/", "_etag": "\"a900a006-0000-0100-0000-687044360000\"", "_attachments": "attachments/", "_ts": 1752187958}, {"payPeriodId": "1090072150014047", "intervalCode": "MONTHLY", "status": "INITIAL", "description": "Monthly Payroll (1)", "startDate": "2025-09-24T00:00:00Z", "endDate": "2025-10-23T00:00:00Z", "submitByDate": "2025-10-24T00:00:00Z", "checkDate": "2025-10-28T00:00:00Z", "checkCount": 0, "id": "2433c130-671e-4ff0-a65f-5f44097072b1", "companyId": "18017094", "type": "payperiod", "_rid": "NmJkAKiCbEdrkgQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdrkgQAAAAAAA==/", "_etag": "\"a900a206-0000-0100-0000-687044370000\"", "_attachments": "attachments/", "_ts": 1752187959}, {"payPeriodId": "1090065845817851", "intervalCode": "MONTHLY", "status": "COMPLETED", "description": "Monthly Payroll (1)", "startDate": "2024-12-24T00:00:00Z", "endDate": "2025-01-23T00:00:00Z", "submitByDate": "2025-01-24T00:00:00Z", "checkDate": "2025-01-28T00:00:00Z", "checkCount": 1, "id": "891c49ff-75da-4ebe-9c0c-098d628e0f61", "companyId": "18017094", "type": "payperiod", "_rid": "NmJkAKiCbEdskgQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdskgQAAAAAAA==/", "_etag": "\"a900a506-0000-0100-0000-687044370000\"", "_attachments": "attachments/", "_ts": 1752187959}, {"payPeriodId": "1090066461419435", "intervalCode": "MONTHLY", "status": "COMPLETED", "description": "Monthly Payroll (1)", "startDate": "2025-01-24T00:00:00Z", "endDate": "2025-02-23T00:00:00Z", "submitByDate": "2025-02-26T00:00:00Z", "checkDate": "2025-02-28T00:00:00Z", "checkCount": 1, "id": "9a5dd74a-9862-438e-bf3b-1bab8c39b0c9", "companyId": "18017094", "type": "payperiod", "_rid": "NmJkAKiCbEdtkgQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdtkgQAAAAAAA==/", "_etag": "\"a900a606-0000-0100-0000-687044370000\"", "_attachments": "attachments/", "_ts": 1752187959}, {"payPeriodId": "1090069791704138", "status": "COMPLETED", "description": "Monthly Payroll (1)", "startDate": "2025-02-24T00:00:00Z", "endDate": "2025-03-23T00:00:00Z", "submitByDate": "2025-03-26T00:00:00Z", "checkDate": "2025-03-27T00:00:00Z", "checkCount": 1, "id": "900eea90-5370-43b7-865c-98ef9311fda4", "companyId": "18017094", "type": "payperiod", "_rid": "NmJkAKiCbEdukgQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdukgQAAAAAAA==/", "_etag": "\"a900aa06-0000-0100-0000-687044370000\"", "_attachments": "attachments/", "_ts": 1752187959}, {"payPeriodId": "1090067841759336", "intervalCode": "MONTHLY", "status": "COMPLETED", "description": "Monthly Payroll (1)", "startDate": "2025-03-24T00:00:00Z", "endDate": "2025-04-23T00:00:00Z", "submitByDate": "2025-04-26T00:00:00Z", "checkDate": "2025-04-27T00:00:00Z", "checkCount": 1, "id": "5a16fa0e-06c3-487a-a557-35314862a654", "companyId": "18017094", "type": "payperiod", "_rid": "NmJkAKiCbEdvkgQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdvkgQAAAAAAA==/", "_etag": "\"a900ae06-0000-0100-0000-687044370000\"", "_attachments": "attachments/", "_ts": 1752187959}, {"payPeriodId": "1090068475853821", "intervalCode": "MONTHLY", "status": "COMPLETED", "description": "Monthly Payroll (1)", "startDate": "2025-04-24T00:00:00Z", "endDate": "2025-05-23T00:00:00Z", "submitByDate": "2025-05-23T00:00:00Z", "checkDate": "2025-05-28T00:00:00Z", "checkCount": 1, "id": "ed0dbf79-749b-4094-b15c-c3b372f72b0a", "companyId": "18017094", "type": "payperiod", "_rid": "NmJkAKiCbEdwkgQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdwkgQAAAAAAA==/", "_etag": "\"a900af06-0000-0100-0000-687044370000\"", "_attachments": "attachments/", "_ts": 1752187959}, {"payPeriodId": "1090070130331698", "intervalCode": "MONTHLY", "status": "INITIAL", "description": "Monthly Payroll (1)", "startDate": "2025-06-24T00:00:00Z", "endDate": "2025-07-23T00:00:00Z", "submitByDate": "2025-07-24T00:00:00Z", "checkDate": "2025-07-28T00:00:00Z", "checkCount": 0, "id": "8636e81f-a164-4c34-bd91-68fe650d4aa2", "companyId": "18017094", "type": "payperiod", "_rid": "NmJkAKiCbEdxkgQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdxkgQAAAAAAA==/", "_etag": "\"a900b006-0000-0100-0000-687044370000\"", "_attachments": "attachments/", "_ts": 1752187959}, {"payPeriodId": "1090070450138734", "intervalCode": "MONTHLY", "status": "INITIAL", "description": "Monthly Payroll (1)", "startDate": "2025-07-24T00:00:00Z", "endDate": "2025-08-23T00:00:00Z", "submitByDate": "2025-08-26T00:00:00Z", "checkDate": "2025-08-28T00:00:00Z", "checkCount": 0, "id": "cb924a10-69d1-4d45-baf5-e65b6c91019e", "companyId": "18017094", "type": "payperiod", "_rid": "NmJkAKiCbEdykgQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdykgQAAAAAAA==/", "_etag": "\"a900b306-0000-0100-0000-687044370000\"", "_attachments": "attachments/", "_ts": 1752187959}, {"payPeriodId": "1090071096200227", "intervalCode": "MONTHLY", "status": "INITIAL", "description": "Monthly Payroll (1)", "startDate": "2025-08-24T00:00:00Z", "endDate": "2025-09-23T00:00:00Z", "submitByDate": "2025-09-24T00:00:00Z", "checkDate": "2025-09-26T00:00:00Z", "checkCount": 0, "id": "3bb4e7d0-513d-47f2-95b5-70fa1c47fb8f", "companyId": "18017094", "type": "payperiod", "_rid": "NmJkAKiCbEdzkgQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdzkgQAAAAAAA==/", "_etag": "\"a900b406-0000-0100-0000-687044370000\"", "_attachments": "attachments/", "_ts": 1752187959}, {"payPeriodId": "1090072150014047", "intervalCode": "MONTHLY", "status": "INITIAL", "description": "Monthly Payroll (1)", "startDate": "2025-09-24T00:00:00Z", "endDate": "2025-10-23T00:00:00Z", "submitByDate": "2025-10-24T00:00:00Z", "checkDate": "2025-10-28T00:00:00Z", "checkCount": 0, "id": "8abc42c6-e2e2-4624-a80a-e0b5dc308f0b", "companyId": "18017094", "type": "payperiod", "_rid": "NmJkAKiCbEd0kgQAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEd0kgQAAAAAAA==/", "_etag": "\"a900b506-0000-0100-0000-687044370000\"", "_attachments": "attachments/", "_ts": 1752187959}], "links": [{"rel": "self", "href": "https://ca-payroll-ai-mock-sb-001-secure.nicebeach-8a7a52ab.eastus.azurecontainerapps.io/ca/companies/18017094/payperiods"}]}, "status_code": 200}