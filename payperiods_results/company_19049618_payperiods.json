{"success": true, "company_id": "19049618", "data": {"metadata": {"contentItemCount": 22}, "content": [{"payPeriodId": "1140035058695562", "intervalCode": "MONTHLY", "status": "COMPLETED", "description": "Monthly Payroll (1)", "startDate": "2025-01-01T00:00:00Z", "endDate": "2025-01-31T00:00:00Z", "submitByDate": "2025-01-29T00:00:00Z", "checkDate": "2025-01-31T00:00:00Z", "checkCount": 5, "id": "d75621d2-3951-4f21-aa83-8673b002e2d2", "companyId": "19049618", "type": "payperiod", "_rid": "NmJkAKiCbEc1TgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc1TgMAAAAAAA==/", "_etag": "\"a500b6ad-0000-0100-0000-687028b40000\"", "_attachments": "attachments/", "_ts": 1752180916}, {"payPeriodId": "1140035183611800", "intervalCode": "MONTHLY", "status": "COMPLETED", "description": "Monthly Payroll (1)", "startDate": "2025-02-01T00:00:00Z", "endDate": "2025-02-25T00:00:00Z", "submitByDate": "2025-02-26T00:00:00Z", "checkDate": "2025-02-28T00:00:00Z", "checkCount": 5, "id": "aca33e2a-0d9d-4482-81ae-56f3506f3a76", "companyId": "19049618", "type": "payperiod", "_rid": "NmJkAKiCbEc2TgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc2TgMAAAAAAA==/", "_etag": "\"a500b8ad-0000-0100-0000-687028b40000\"", "_attachments": "attachments/", "_ts": 1752180916}, {"payPeriodId": "1140035324892455", "intervalCode": "MONTHLY", "status": "COMPLETED", "description": "Monthly Payroll (1)", "startDate": "2025-03-01T00:00:00Z", "endDate": "2025-03-31T00:00:00Z", "submitByDate": "2025-03-27T00:00:00Z", "checkDate": "2025-03-31T00:00:00Z", "checkCount": 4, "id": "264b8604-ab33-4152-8c66-f29ca14038d3", "companyId": "19049618", "type": "payperiod", "_rid": "NmJkAKiCbEc3TgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc3TgMAAAAAAA==/", "_etag": "\"a500bbad-0000-0100-0000-687028b40000\"", "_attachments": "attachments/", "_ts": 1752180916}, {"payPeriodId": "1140035475476643", "intervalCode": "MONTHLY", "status": "INITIAL", "description": "Monthly Payroll (1)", "startDate": "2025-04-01T00:00:00Z", "endDate": "2025-04-30T00:00:00Z", "submitByDate": "2025-04-28T00:00:00Z", "checkDate": "2025-04-30T00:00:00Z", "checkCount": 0, "id": "1a467d83-2d14-4bb8-82a6-14a866892dfc", "companyId": "19049618", "type": "payperiod", "_rid": "NmJkAKiCbEc4TgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc4TgMAAAAAAA==/", "_etag": "\"a500bdad-0000-0100-0000-687028b40000\"", "_attachments": "attachments/", "_ts": 1752180916}, {"payPeriodId": "1140035588239728", "intervalCode": "MONTHLY", "status": "INITIAL", "description": "Monthly Payroll (1)", "startDate": "2025-05-01T00:00:00Z", "endDate": "2025-05-31T00:00:00Z", "submitByDate": "2025-05-28T00:00:00Z", "checkDate": "2025-05-30T00:00:00Z", "checkCount": 0, "id": "3b31de74-14e8-4fea-b69e-f7a0ed73faf8", "companyId": "19049618", "type": "payperiod", "_rid": "NmJkAKiCbEc5TgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc5TgMAAAAAAA==/", "_etag": "\"a500c3ad-0000-0100-0000-687028b40000\"", "_attachments": "attachments/", "_ts": 1752180916}, {"payPeriodId": "1140035730280180", "intervalCode": "MONTHLY", "status": "INITIAL", "description": "Monthly Payroll (1)", "startDate": "2025-06-01T00:00:00Z", "endDate": "2025-06-30T00:00:00Z", "submitByDate": "2025-06-26T00:00:00Z", "checkDate": "2025-06-30T00:00:00Z", "checkCount": 0, "id": "8d707806-5b19-4b23-be09-ad4f80209da3", "companyId": "19049618", "type": "payperiod", "_rid": "NmJkAKiCbEc6TgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc6TgMAAAAAAA==/", "_etag": "\"a500c5ad-0000-0100-0000-687028b40000\"", "_attachments": "attachments/", "_ts": 1752180916}, {"payPeriodId": "1140036197041093", "status": "INITIAL", "description": "Patty Correction", "startDate": "2025-06-01T00:00:00Z", "endDate": "2025-06-30T00:00:00Z", "submitByDate": "2025-06-29T00:00:00Z", "checkDate": "2025-06-30T00:00:00Z", "checkCount": 0, "id": "635f6e24-7bef-43cf-ab69-c81d0aa3db68", "companyId": "19049618", "type": "payperiod", "_rid": "NmJkAKiCbEc7TgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc7TgMAAAAAAA==/", "_etag": "\"a500c7ad-0000-0100-0000-687028b40000\"", "_attachments": "attachments/", "_ts": 1752180916}, {"payPeriodId": "1140035845424891", "intervalCode": "MONTHLY", "status": "INITIAL", "description": "Monthly Payroll (1)", "startDate": "2025-07-01T00:00:00Z", "endDate": "2025-07-31T00:00:00Z", "submitByDate": "2025-07-29T00:00:00Z", "checkDate": "2025-07-31T00:00:00Z", "checkCount": 0, "id": "7c20608c-98e5-4085-8383-2e526f338250", "companyId": "19049618", "type": "payperiod", "_rid": "NmJkAKiCbEc8TgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc8TgMAAAAAAA==/", "_etag": "\"a500cbad-0000-0100-0000-687028b40000\"", "_attachments": "attachments/", "_ts": 1752180916}, {"payPeriodId": "1140035966023338", "intervalCode": "MONTHLY", "status": "INITIAL", "description": "Monthly Payroll (1)", "startDate": "2025-08-01T00:00:00Z", "endDate": "2025-08-31T00:00:00Z", "submitByDate": "2025-08-27T00:00:00Z", "checkDate": "2025-08-29T00:00:00Z", "checkCount": 0, "id": "a1cd58e0-8369-4245-8d95-9b8943e97bf3", "companyId": "19049618", "type": "payperiod", "_rid": "NmJkAKiCbEc9TgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc9TgMAAAAAAA==/", "_etag": "\"a500cead-0000-0100-0000-687028b40000\"", "_attachments": "attachments/", "_ts": 1752180916}, {"payPeriodId": "1140036117734059", "intervalCode": "MONTHLY", "status": "INITIAL", "description": "Monthly Payroll (1)", "startDate": "2025-09-01T00:00:00Z", "endDate": "2025-09-30T00:00:00Z", "submitByDate": "2025-09-26T00:00:00Z", "checkDate": "2025-09-30T00:00:00Z", "checkCount": 0, "id": "8e013d9a-b8ab-48a1-8b0f-2248b8cf8aef", "companyId": "19049618", "type": "payperiod", "_rid": "NmJkAKiCbEc+TgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc+TgMAAAAAAA==/", "_etag": "\"a500d1ad-0000-0100-0000-687028b50000\"", "_attachments": "attachments/", "_ts": 1752180917}, {"payPeriodId": "1140036227500492", "intervalCode": "MONTHLY", "status": "INITIAL", "description": "Monthly Payroll (1)", "startDate": "2025-10-01T00:00:00Z", "endDate": "2025-10-31T00:00:00Z", "submitByDate": "2025-10-29T00:00:00Z", "checkDate": "2025-10-31T00:00:00Z", "checkCount": 0, "id": "7274892a-4123-4c10-a223-2bc2c7dd590b", "companyId": "19049618", "type": "payperiod", "_rid": "NmJkAKiCbEc-TgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc-TgMAAAAAAA==/", "_etag": "\"a500d4ad-0000-0100-0000-687028b50000\"", "_attachments": "attachments/", "_ts": 1752180917}, {"payPeriodId": "1140035058695562", "intervalCode": "MONTHLY", "status": "COMPLETED", "description": "Monthly Payroll (1)", "startDate": "2025-01-01T00:00:00Z", "endDate": "2025-01-31T00:00:00Z", "submitByDate": "2025-01-29T00:00:00Z", "checkDate": "2025-01-31T00:00:00Z", "checkCount": 5, "id": "909655de-f856-46b6-9fca-65efe8b4ceac", "companyId": "19049618", "type": "payperiod", "_rid": "NmJkAKiCbEdHTgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdHTgMAAAAAAA==/", "_etag": "\"a500edad-0000-0100-0000-687028b50000\"", "_attachments": "attachments/", "_ts": 1752180917}, {"payPeriodId": "1140035183611800", "intervalCode": "MONTHLY", "status": "COMPLETED", "description": "Monthly Payroll (1)", "startDate": "2025-02-01T00:00:00Z", "endDate": "2025-02-25T00:00:00Z", "submitByDate": "2025-02-26T00:00:00Z", "checkDate": "2025-02-28T00:00:00Z", "checkCount": 5, "id": "20137a27-e533-4e6a-a884-291894980d0c", "companyId": "19049618", "type": "payperiod", "_rid": "NmJkAKiCbEdITgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdITgMAAAAAAA==/", "_etag": "\"a500eead-0000-0100-0000-687028b50000\"", "_attachments": "attachments/", "_ts": 1752180917}, {"payPeriodId": "1140035324892455", "intervalCode": "MONTHLY", "status": "COMPLETED", "description": "Monthly Payroll (1)", "startDate": "2025-03-01T00:00:00Z", "endDate": "2025-03-31T00:00:00Z", "submitByDate": "2025-03-27T00:00:00Z", "checkDate": "2025-03-31T00:00:00Z", "checkCount": 4, "id": "0de669a8-f1ca-4f5b-b0d9-0826674fbae7", "companyId": "19049618", "type": "payperiod", "_rid": "NmJkAKiCbEdJTgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdJTgMAAAAAAA==/", "_etag": "\"a500f1ad-0000-0100-0000-687028b50000\"", "_attachments": "attachments/", "_ts": 1752180917}, {"payPeriodId": "1140035475476643", "intervalCode": "MONTHLY", "status": "COMPLETED", "description": "Monthly Payroll (1)", "startDate": "2025-04-01T00:00:00Z", "endDate": "2025-04-30T00:00:00Z", "submitByDate": "2025-04-28T00:00:00Z", "checkDate": "2025-04-30T00:00:00Z", "checkCount": 4, "id": "9500d8a7-82fb-4dd0-ac5d-a8da537b3634", "companyId": "19049618", "type": "payperiod", "_rid": "NmJkAKiCbEdKTgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdKTgMAAAAAAA==/", "_etag": "\"a500f7ad-0000-0100-0000-687028b60000\"", "_attachments": "attachments/", "_ts": 1752180918}, {"payPeriodId": "1140035588239728", "intervalCode": "MONTHLY", "status": "COMPLETED", "description": "Monthly Payroll (1)", "startDate": "2025-05-01T00:00:00Z", "endDate": "2025-05-31T00:00:00Z", "submitByDate": "2025-05-28T00:00:00Z", "checkDate": "2025-05-30T00:00:00Z", "checkCount": 4, "id": "6e3aed90-1b00-4a41-a491-e22698f41420", "companyId": "19049618", "type": "payperiod", "_rid": "NmJkAKiCbEdLTgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdLTgMAAAAAAA==/", "_etag": "\"a500f8ad-0000-0100-0000-687028b60000\"", "_attachments": "attachments/", "_ts": 1752180918}, {"payPeriodId": "1140035730280180", "intervalCode": "MONTHLY", "status": "COMPLETED", "description": "Monthly Payroll (1)", "startDate": "2025-06-01T00:00:00Z", "endDate": "2025-06-30T00:00:00Z", "submitByDate": "2025-06-26T00:00:00Z", "checkDate": "2025-06-30T00:00:00Z", "checkCount": 4, "id": "cf6e1cf6-28a8-4d7a-b724-6cf34f0ebc50", "companyId": "19049618", "type": "payperiod", "_rid": "NmJkAKiCbEdMTgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdMTgMAAAAAAA==/", "_etag": "\"a500faad-0000-0100-0000-687028b60000\"", "_attachments": "attachments/", "_ts": 1752180918}, {"payPeriodId": "1140036197041093", "status": "COMPLETED", "description": "Patty Correction", "startDate": "2025-06-01T00:00:00Z", "endDate": "2025-06-30T00:00:00Z", "submitByDate": "2025-06-29T00:00:00Z", "checkDate": "2025-06-30T00:00:00Z", "checkCount": 2, "id": "2162149b-434d-463f-ba05-52b76da1d774", "companyId": "19049618", "type": "payperiod", "_rid": "NmJkAKiCbEdNTgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdNTgMAAAAAAA==/", "_etag": "\"a500fcad-0000-0100-0000-687028b60000\"", "_attachments": "attachments/", "_ts": 1752180918}, {"payPeriodId": "1140035845424891", "intervalCode": "MONTHLY", "status": "INITIAL", "description": "Monthly Payroll (1)", "startDate": "2025-07-01T00:00:00Z", "endDate": "2025-07-31T00:00:00Z", "submitByDate": "2025-07-29T00:00:00Z", "checkDate": "2025-07-31T00:00:00Z", "checkCount": 0, "id": "4aa73a34-ecd2-4732-b09e-b52bf47dc811", "companyId": "19049618", "type": "payperiod", "_rid": "NmJkAKiCbEdOTgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdOTgMAAAAAAA==/", "_etag": "\"a500ffad-0000-0100-0000-687028b60000\"", "_attachments": "attachments/", "_ts": 1752180918}, {"payPeriodId": "1140035966023338", "intervalCode": "MONTHLY", "status": "INITIAL", "description": "Monthly Payroll (1)", "startDate": "2025-08-01T00:00:00Z", "endDate": "2025-08-31T00:00:00Z", "submitByDate": "2025-08-27T00:00:00Z", "checkDate": "2025-08-29T00:00:00Z", "checkCount": 0, "id": "382c9f5c-d53a-49bb-8555-92e599858f12", "companyId": "19049618", "type": "payperiod", "_rid": "NmJkAKiCbEdPTgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdPTgMAAAAAAA==/", "_etag": "\"a50005ae-0000-0100-0000-687028b60000\"", "_attachments": "attachments/", "_ts": 1752180918}, {"payPeriodId": "1140036117734059", "intervalCode": "MONTHLY", "status": "INITIAL", "description": "Monthly Payroll (1)", "startDate": "2025-09-01T00:00:00Z", "endDate": "2025-09-30T00:00:00Z", "submitByDate": "2025-09-26T00:00:00Z", "checkDate": "2025-09-30T00:00:00Z", "checkCount": 0, "id": "5e7aec90-9f8a-4602-8462-eafe35eded89", "companyId": "19049618", "type": "payperiod", "_rid": "NmJkAKiCbEdQTgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdQTgMAAAAAAA==/", "_etag": "\"a50007ae-0000-0100-0000-687028b60000\"", "_attachments": "attachments/", "_ts": 1752180918}, {"payPeriodId": "1140036227500492", "intervalCode": "MONTHLY", "status": "INITIAL", "description": "Monthly Payroll (1)", "startDate": "2025-10-01T00:00:00Z", "endDate": "2025-10-31T00:00:00Z", "submitByDate": "2025-10-29T00:00:00Z", "checkDate": "2025-10-31T00:00:00Z", "checkCount": 0, "id": "506d6467-8728-42c3-8168-ad9de13225fd", "companyId": "19049618", "type": "payperiod", "_rid": "NmJkAKiCbEdRTgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdRTgMAAAAAAA==/", "_etag": "\"a5000aae-0000-0100-0000-687028b60000\"", "_attachments": "attachments/", "_ts": 1752180918}], "links": [{"rel": "self", "href": "https://ca-payroll-ai-mock-sb-001-secure.nicebeach-8a7a52ab.eastus.azurecontainerapps.io/ca/companies/19049618/payperiods"}]}, "status_code": 200}