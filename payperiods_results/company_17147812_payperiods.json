{"success": true, "company_id": "17147812", "data": {"metadata": {"contentItemCount": 40}, "content": [{"payPeriodId": "1070074876512096", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (2)", "startDate": "2025-01-01T00:00:00Z", "endDate": "2025-01-15T00:00:00Z", "submitByDate": "2025-01-13T00:00:00Z", "checkDate": "2025-01-15T00:00:00Z", "checkCount": 4, "id": "88968b02-1958-42e5-a2f7-d341bc1a0092", "companyId": "17147812", "type": "payperiod", "_rid": "NmJkAKiCbEcnuAIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcnuAIAAAAAAA==/", "_etag": "\"a300b2c5-0000-0100-0000-68701c900000\"", "_attachments": "attachments/", "_ts": 1752177808}, {"payPeriodId": "1070074876512097", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (2)", "startDate": "2025-01-16T00:00:00Z", "endDate": "2025-01-31T00:00:00Z", "submitByDate": "2025-01-29T00:00:00Z", "checkDate": "2025-01-31T00:00:00Z", "checkCount": 4, "id": "c28781d7-67af-412c-8536-07f13e5f7f27", "companyId": "17147812", "type": "payperiod", "_rid": "NmJkAKiCbEcouAIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcouAIAAAAAAA==/", "_etag": "\"a300bac5-0000-0100-0000-68701c900000\"", "_attachments": "attachments/", "_ts": 1752177808}, {"payPeriodId": "1070075412535794", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (2)", "startDate": "2025-02-01T00:00:00Z", "endDate": "2025-02-15T00:00:00Z", "submitByDate": "2025-02-12T00:00:00Z", "checkDate": "2025-02-14T00:00:00Z", "checkCount": 4, "id": "366ee5cc-8ec5-40df-9121-0f479c708952", "companyId": "17147812", "type": "payperiod", "_rid": "NmJkAKiCbEcpuAIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcpuAIAAAAAAA==/", "_etag": "\"a300bfc5-0000-0100-0000-68701c900000\"", "_attachments": "attachments/", "_ts": 1752177808}, {"payPeriodId": "1070075412535795", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (2)", "startDate": "2025-02-16T00:00:00Z", "endDate": "2025-02-28T00:00:00Z", "submitByDate": "2025-02-26T00:00:00Z", "checkDate": "2025-02-28T00:00:00Z", "checkCount": 4, "id": "ec803103-a35e-4169-abc6-7d1b65c9a98e", "companyId": "17147812", "type": "payperiod", "_rid": "NmJkAKiCbEcquAIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcquAIAAAAAAA==/", "_etag": "\"a300c3c5-0000-0100-0000-68701c900000\"", "_attachments": "attachments/", "_ts": 1752177808}, {"payPeriodId": "1070076069261260", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (2)", "startDate": "2025-03-01T00:00:00Z", "endDate": "2025-03-15T00:00:00Z", "submitByDate": "2025-03-12T00:00:00Z", "checkDate": "2025-03-14T00:00:00Z", "checkCount": 4, "id": "74bfc484-89aa-44e8-830b-a8c5b0b17c3a", "companyId": "17147812", "type": "payperiod", "_rid": "NmJkAKiCbEcruAIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcruAIAAAAAAA==/", "_etag": "\"a300c5c5-0000-0100-0000-68701c900000\"", "_attachments": "attachments/", "_ts": 1752177808}, {"payPeriodId": "1070076069261261", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (2)", "startDate": "2025-03-16T00:00:00Z", "endDate": "2025-03-31T00:00:00Z", "submitByDate": "2025-03-27T00:00:00Z", "checkDate": "2025-03-31T00:00:00Z", "checkCount": 4, "id": "e49491b5-bea0-4cbc-9b45-60b38fcca39f", "companyId": "17147812", "type": "payperiod", "_rid": "NmJkAKiCbEcsuAIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcsuAIAAAAAAA==/", "_etag": "\"a300c7c5-0000-0100-0000-68701c900000\"", "_attachments": "attachments/", "_ts": 1752177808}, {"payPeriodId": "1070076604240628", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (2)", "startDate": "2025-04-01T00:00:00Z", "endDate": "2025-04-15T00:00:00Z", "submitByDate": "2025-04-11T00:00:00Z", "checkDate": "2025-04-15T00:00:00Z", "checkCount": 0, "id": "764b8e9a-712d-45f5-8a40-64b9a29c6624", "companyId": "17147812", "type": "payperiod", "_rid": "NmJkAKiCbEctuAIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEctuAIAAAAAAA==/", "_etag": "\"a300cbc5-0000-0100-0000-68701c900000\"", "_attachments": "attachments/", "_ts": 1752177808}, {"payPeriodId": "1070076604240629", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (2)", "startDate": "2025-04-16T00:00:00Z", "endDate": "2025-04-30T00:00:00Z", "submitByDate": "2025-04-28T00:00:00Z", "checkDate": "2025-04-30T00:00:00Z", "checkCount": 0, "id": "fa7e9495-2e66-432f-877e-2dbe1c8f7ead", "companyId": "17147812", "type": "payperiod", "_rid": "NmJkAKiCbEcuuAIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcuuAIAAAAAAA==/", "_etag": "\"a300cfc5-0000-0100-0000-68701c900000\"", "_attachments": "attachments/", "_ts": 1752177808}, {"payPeriodId": "1070077119677048", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (2)", "startDate": "2025-05-01T00:00:00Z", "endDate": "2025-05-15T00:00:00Z", "submitByDate": "2025-05-13T00:00:00Z", "checkDate": "2025-05-15T00:00:00Z", "checkCount": 0, "id": "bdb9ba56-102c-4676-8085-ecd8620ec4db", "companyId": "17147812", "type": "payperiod", "_rid": "NmJkAKiCbEcvuAIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcvuAIAAAAAAA==/", "_etag": "\"a300d3c5-0000-0100-0000-68701c900000\"", "_attachments": "attachments/", "_ts": 1752177808}, {"payPeriodId": "1070077119677049", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (2)", "startDate": "2025-05-16T00:00:00Z", "endDate": "2025-05-31T00:00:00Z", "submitByDate": "2025-05-28T00:00:00Z", "checkDate": "2025-05-30T00:00:00Z", "checkCount": 0, "id": "be9ed2eb-48a9-4ce2-86be-65812153a8b8", "companyId": "17147812", "type": "payperiod", "_rid": "NmJkAKiCbEcwuAIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcwuAIAAAAAAA==/", "_etag": "\"a300d6c5-0000-0100-0000-68701c900000\"", "_attachments": "attachments/", "_ts": 1752177808}, {"payPeriodId": "1070077786911428", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (2)", "startDate": "2025-06-01T00:00:00Z", "endDate": "2025-06-15T00:00:00Z", "submitByDate": "2025-06-11T00:00:00Z", "checkDate": "2025-06-13T00:00:00Z", "checkCount": 0, "id": "13dde625-b98c-4ab7-aec0-499cff1b793e", "companyId": "17147812", "type": "payperiod", "_rid": "NmJkAKiCbEcxuAIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcxuAIAAAAAAA==/", "_etag": "\"a300d7c5-0000-0100-0000-68701c910000\"", "_attachments": "attachments/", "_ts": 1752177809}, {"payPeriodId": "1070077786911429", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (2)", "startDate": "2025-06-16T00:00:00Z", "endDate": "2025-06-30T00:00:00Z", "submitByDate": "2025-06-26T00:00:00Z", "checkDate": "2025-06-30T00:00:00Z", "checkCount": 0, "id": "09d55564-5eae-4a99-9031-747d1deef4ee", "companyId": "17147812", "type": "payperiod", "_rid": "NmJkAKiCbEcyuAIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcyuAIAAAAAAA==/", "_etag": "\"a300dcc5-0000-0100-0000-68701c910000\"", "_attachments": "attachments/", "_ts": 1752177809}, {"payPeriodId": "1070078315402693", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (2)", "startDate": "2025-07-01T00:00:00Z", "endDate": "2025-07-15T00:00:00Z", "submitByDate": "2025-07-11T00:00:00Z", "checkDate": "2025-07-15T00:00:00Z", "checkCount": 0, "id": "a731e8c8-c061-4505-a22b-ae9bcf905e22", "companyId": "17147812", "type": "payperiod", "_rid": "NmJkAKiCbEczuAIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEczuAIAAAAAAA==/", "_etag": "\"a300dec5-0000-0100-0000-68701c910000\"", "_attachments": "attachments/", "_ts": 1752177809}, {"payPeriodId": "1070078315402694", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (2)", "startDate": "2025-07-16T00:00:00Z", "endDate": "2025-07-31T00:00:00Z", "submitByDate": "2025-07-29T00:00:00Z", "checkDate": "2025-07-31T00:00:00Z", "checkCount": 0, "id": "84f761ac-d11b-4700-9db1-93537d2098d7", "companyId": "17147812", "type": "payperiod", "_rid": "NmJkAKiCbEc0uAIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc0uAIAAAAAAA==/", "_etag": "\"a300e3c5-0000-0100-0000-68701c910000\"", "_attachments": "attachments/", "_ts": 1752177809}, {"payPeriodId": "1070078836967507", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (2)", "startDate": "2025-08-01T00:00:00Z", "endDate": "2025-08-15T00:00:00Z", "submitByDate": "2025-08-13T00:00:00Z", "checkDate": "2025-08-15T00:00:00Z", "checkCount": 0, "id": "e4a27086-4a0b-462f-91a2-cc8bd5c7aae1", "companyId": "17147812", "type": "payperiod", "_rid": "NmJkAKiCbEc1uAIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc1uAIAAAAAAA==/", "_etag": "\"a300e5c5-0000-0100-0000-68701c910000\"", "_attachments": "attachments/", "_ts": 1752177809}, {"payPeriodId": "1070078836967508", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (2)", "startDate": "2025-08-16T00:00:00Z", "endDate": "2025-08-31T00:00:00Z", "submitByDate": "2025-08-27T00:00:00Z", "checkDate": "2025-08-29T00:00:00Z", "checkCount": 0, "id": "c3326e9f-2a0c-448b-8537-b5914d057875", "companyId": "17147812", "type": "payperiod", "_rid": "NmJkAKiCbEc2uAIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc2uAIAAAAAAA==/", "_etag": "\"a300e8c5-0000-0100-0000-68701c910000\"", "_attachments": "attachments/", "_ts": 1752177809}, {"payPeriodId": "1070079505104914", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (2)", "startDate": "2025-09-01T00:00:00Z", "endDate": "2025-09-15T00:00:00Z", "submitByDate": "2025-09-11T00:00:00Z", "checkDate": "2025-09-15T00:00:00Z", "checkCount": 0, "id": "9bada11b-4ff3-4e61-8e10-2ae0af2ed286", "companyId": "17147812", "type": "payperiod", "_rid": "NmJkAKiCbEc3uAIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc3uAIAAAAAAA==/", "_etag": "\"a300ebc5-0000-0100-0000-68701c910000\"", "_attachments": "attachments/", "_ts": 1752177809}, {"payPeriodId": "1070079505104915", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (2)", "startDate": "2025-09-16T00:00:00Z", "endDate": "2025-09-30T00:00:00Z", "submitByDate": "2025-09-26T00:00:00Z", "checkDate": "2025-09-30T00:00:00Z", "checkCount": 0, "id": "b24428d4-dc57-4467-ac49-1c5c70a7ce41", "companyId": "17147812", "type": "payperiod", "_rid": "NmJkAKiCbEc4uAIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc4uAIAAAAAAA==/", "_etag": "\"a300f2c5-0000-0100-0000-68701c910000\"", "_attachments": "attachments/", "_ts": 1752177809}, {"payPeriodId": "1070080023527540", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (2)", "startDate": "2025-10-01T00:00:00Z", "endDate": "2025-10-15T00:00:00Z", "submitByDate": "2025-10-13T00:00:00Z", "checkDate": "2025-10-15T00:00:00Z", "checkCount": 0, "id": "b5993c85-db0c-41ae-9e2a-d74278ea9617", "companyId": "17147812", "type": "payperiod", "_rid": "NmJkAKiCbEc5uAIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc5uAIAAAAAAA==/", "_etag": "\"a300f6c5-0000-0100-0000-68701c910000\"", "_attachments": "attachments/", "_ts": 1752177809}, {"payPeriodId": "1070080023527541", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (2)", "startDate": "2025-10-16T00:00:00Z", "endDate": "2025-10-31T00:00:00Z", "submitByDate": "2025-10-29T00:00:00Z", "checkDate": "2025-10-31T00:00:00Z", "checkCount": 0, "id": "b31701cd-da55-45ab-a108-b0b25c5c6b5b", "companyId": "17147812", "type": "payperiod", "_rid": "NmJkAKiCbEc6uAIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc6uAIAAAAAAA==/", "_etag": "\"a300fcc5-0000-0100-0000-68701c910000\"", "_attachments": "attachments/", "_ts": 1752177809}, {"payPeriodId": "1070074876512096", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (2)", "startDate": "2025-01-01T00:00:00Z", "endDate": "2025-01-15T00:00:00Z", "submitByDate": "2025-01-13T00:00:00Z", "checkDate": "2025-01-15T00:00:00Z", "checkCount": 4, "id": "af972cfb-4730-4b04-aee4-2f163580b9d2", "companyId": "17147812", "type": "payperiod", "_rid": "NmJkAKiCbEdDuAIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdDuAIAAAAAAA==/", "_etag": "\"a30017c6-0000-0100-0000-68701c920000\"", "_attachments": "attachments/", "_ts": 1752177810}, {"payPeriodId": "1070074876512097", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (2)", "startDate": "2025-01-16T00:00:00Z", "endDate": "2025-01-31T00:00:00Z", "submitByDate": "2025-01-29T00:00:00Z", "checkDate": "2025-01-31T00:00:00Z", "checkCount": 4, "id": "15ff7324-0f03-4f59-9433-1b3e67422747", "companyId": "17147812", "type": "payperiod", "_rid": "NmJkAKiCbEdEuAIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdEuAIAAAAAAA==/", "_etag": "\"a3001ac6-0000-0100-0000-68701c920000\"", "_attachments": "attachments/", "_ts": 1752177810}, {"payPeriodId": "1070075412535794", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (2)", "startDate": "2025-02-01T00:00:00Z", "endDate": "2025-02-15T00:00:00Z", "submitByDate": "2025-02-12T00:00:00Z", "checkDate": "2025-02-14T00:00:00Z", "checkCount": 4, "id": "812ab741-98a9-4fef-8493-7af92799d33a", "companyId": "17147812", "type": "payperiod", "_rid": "NmJkAKiCbEdFuAIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdFuAIAAAAAAA==/", "_etag": "\"a3001cc6-0000-0100-0000-68701c920000\"", "_attachments": "attachments/", "_ts": 1752177810}, {"payPeriodId": "1070075412535795", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (2)", "startDate": "2025-02-16T00:00:00Z", "endDate": "2025-02-28T00:00:00Z", "submitByDate": "2025-02-26T00:00:00Z", "checkDate": "2025-02-28T00:00:00Z", "checkCount": 4, "id": "46ae1532-05e8-42c6-9848-20847551f997", "companyId": "17147812", "type": "payperiod", "_rid": "NmJkAKiCbEdGuAIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdGuAIAAAAAAA==/", "_etag": "\"a3001fc6-0000-0100-0000-68701c920000\"", "_attachments": "attachments/", "_ts": 1752177810}, {"payPeriodId": "1070076069261260", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (2)", "startDate": "2025-03-01T00:00:00Z", "endDate": "2025-03-15T00:00:00Z", "submitByDate": "2025-03-12T00:00:00Z", "checkDate": "2025-03-14T00:00:00Z", "checkCount": 4, "id": "bf064db4-9357-4ced-9c05-184a5ba31b8e", "companyId": "17147812", "type": "payperiod", "_rid": "NmJkAKiCbEdHuAIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdHuAIAAAAAAA==/", "_etag": "\"a30020c6-0000-0100-0000-68701c920000\"", "_attachments": "attachments/", "_ts": 1752177810}, {"payPeriodId": "1070076069261261", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (2)", "startDate": "2025-03-16T00:00:00Z", "endDate": "2025-03-31T00:00:00Z", "submitByDate": "2025-03-27T00:00:00Z", "checkDate": "2025-03-31T00:00:00Z", "checkCount": 4, "id": "5c8a075c-3717-4bfe-954a-6184caecb77e", "companyId": "17147812", "type": "payperiod", "_rid": "NmJkAKiCbEdIuAIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdIuAIAAAAAAA==/", "_etag": "\"a30021c6-0000-0100-0000-68701c920000\"", "_attachments": "attachments/", "_ts": 1752177810}, {"payPeriodId": "1070076604240628", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (2)", "startDate": "2025-04-01T00:00:00Z", "endDate": "2025-04-15T00:00:00Z", "submitByDate": "2025-04-11T00:00:00Z", "checkDate": "2025-04-15T00:00:00Z", "checkCount": 4, "id": "c36f2ac9-e13c-4d38-b1b8-8f9b97a4885e", "companyId": "17147812", "type": "payperiod", "_rid": "NmJkAKiCbEdJuAIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdJuAIAAAAAAA==/", "_etag": "\"a30023c6-0000-0100-0000-68701c920000\"", "_attachments": "attachments/", "_ts": 1752177810}, {"payPeriodId": "1070076604240629", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (2)", "startDate": "2025-04-16T00:00:00Z", "endDate": "2025-04-30T00:00:00Z", "submitByDate": "2025-04-28T00:00:00Z", "checkDate": "2025-04-30T00:00:00Z", "checkCount": 4, "id": "09d05136-b102-4a8d-ad8b-cbf043a3a81f", "companyId": "17147812", "type": "payperiod", "_rid": "NmJkAKiCbEdKuAIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdKuAIAAAAAAA==/", "_etag": "\"a30027c6-0000-0100-0000-68701c930000\"", "_attachments": "attachments/", "_ts": 1752177811}, {"payPeriodId": "1070077119677048", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (2)", "startDate": "2025-05-01T00:00:00Z", "endDate": "2025-05-15T00:00:00Z", "submitByDate": "2025-05-13T00:00:00Z", "checkDate": "2025-05-15T00:00:00Z", "checkCount": 4, "id": "c004169d-1fb1-4295-8ed1-544a7d792eae", "companyId": "17147812", "type": "payperiod", "_rid": "NmJkAKiCbEdLuAIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdLuAIAAAAAAA==/", "_etag": "\"a30028c6-0000-0100-0000-68701c930000\"", "_attachments": "attachments/", "_ts": 1752177811}, {"payPeriodId": "1070077119677049", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (2)", "startDate": "2025-05-16T00:00:00Z", "endDate": "2025-05-31T00:00:00Z", "submitByDate": "2025-05-28T00:00:00Z", "checkDate": "2025-05-30T00:00:00Z", "checkCount": 4, "id": "50aabd01-a6b0-41bd-ac81-a1469158eefd", "companyId": "17147812", "type": "payperiod", "_rid": "NmJkAKiCbEdMuAIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdMuAIAAAAAAA==/", "_etag": "\"a3002cc6-0000-0100-0000-68701c930000\"", "_attachments": "attachments/", "_ts": 1752177811}, {"payPeriodId": "1070077786911428", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (2)", "startDate": "2025-06-01T00:00:00Z", "endDate": "2025-06-15T00:00:00Z", "submitByDate": "2025-06-11T00:00:00Z", "checkDate": "2025-06-13T00:00:00Z", "checkCount": 4, "id": "2ffe6f79-46a5-49e2-9f9e-97423976658e", "companyId": "17147812", "type": "payperiod", "_rid": "NmJkAKiCbEdNuAIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdNuAIAAAAAAA==/", "_etag": "\"a30030c6-0000-0100-0000-68701c930000\"", "_attachments": "attachments/", "_ts": 1752177811}, {"payPeriodId": "1070077786911429", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (2)", "startDate": "2025-06-16T00:00:00Z", "endDate": "2025-06-30T00:00:00Z", "submitByDate": "2025-06-26T00:00:00Z", "checkDate": "2025-06-30T00:00:00Z", "checkCount": 4, "id": "74f9702b-b362-4bb1-8cf8-b8893e96b3d9", "companyId": "17147812", "type": "payperiod", "_rid": "NmJkAKiCbEdOuAIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdOuAIAAAAAAA==/", "_etag": "\"a30036c6-0000-0100-0000-68701c930000\"", "_attachments": "attachments/", "_ts": 1752177811}, {"payPeriodId": "1070078315402693", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (2)", "startDate": "2025-07-01T00:00:00Z", "endDate": "2025-07-15T00:00:00Z", "submitByDate": "2025-07-11T00:00:00Z", "checkDate": "2025-07-15T00:00:00Z", "checkCount": 0, "id": "e28f5bda-55d7-47d2-b9a7-e2f1a8371d70", "companyId": "17147812", "type": "payperiod", "_rid": "NmJkAKiCbEdPuAIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdPuAIAAAAAAA==/", "_etag": "\"a30037c6-0000-0100-0000-68701c930000\"", "_attachments": "attachments/", "_ts": 1752177811}, {"payPeriodId": "1070078315402694", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (2)", "startDate": "2025-07-16T00:00:00Z", "endDate": "2025-07-31T00:00:00Z", "submitByDate": "2025-07-29T00:00:00Z", "checkDate": "2025-07-31T00:00:00Z", "checkCount": 0, "id": "439e78bb-0159-4d2b-a22f-8acc7ede2233", "companyId": "17147812", "type": "payperiod", "_rid": "NmJkAKiCbEdQuAIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdQuAIAAAAAAA==/", "_etag": "\"a3003ac6-0000-0100-0000-68701c930000\"", "_attachments": "attachments/", "_ts": 1752177811}, {"payPeriodId": "1070078836967507", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (2)", "startDate": "2025-08-01T00:00:00Z", "endDate": "2025-08-15T00:00:00Z", "submitByDate": "2025-08-13T00:00:00Z", "checkDate": "2025-08-15T00:00:00Z", "checkCount": 0, "id": "f1eda063-0b6c-421a-acf4-a8241952b97d", "companyId": "17147812", "type": "payperiod", "_rid": "NmJkAKiCbEdRuAIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdRuAIAAAAAAA==/", "_etag": "\"a3003dc6-0000-0100-0000-68701c930000\"", "_attachments": "attachments/", "_ts": 1752177811}, {"payPeriodId": "1070078836967508", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (2)", "startDate": "2025-08-16T00:00:00Z", "endDate": "2025-08-31T00:00:00Z", "submitByDate": "2025-08-27T00:00:00Z", "checkDate": "2025-08-29T00:00:00Z", "checkCount": 0, "id": "5ca527bb-5bb1-4488-a1b2-bacccc0c4e8a", "companyId": "17147812", "type": "payperiod", "_rid": "NmJkAKiCbEdSuAIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdSuAIAAAAAAA==/", "_etag": "\"a30042c6-0000-0100-0000-68701c930000\"", "_attachments": "attachments/", "_ts": 1752177811}, {"payPeriodId": "1070079505104914", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (2)", "startDate": "2025-09-01T00:00:00Z", "endDate": "2025-09-15T00:00:00Z", "submitByDate": "2025-09-11T00:00:00Z", "checkDate": "2025-09-15T00:00:00Z", "checkCount": 0, "id": "5f9e89bc-24ca-41a5-9989-a77afbb76c7e", "companyId": "17147812", "type": "payperiod", "_rid": "NmJkAKiCbEdTuAIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdTuAIAAAAAAA==/", "_etag": "\"a30043c6-0000-0100-0000-68701c930000\"", "_attachments": "attachments/", "_ts": 1752177811}, {"payPeriodId": "1070079505104915", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (2)", "startDate": "2025-09-16T00:00:00Z", "endDate": "2025-09-30T00:00:00Z", "submitByDate": "2025-09-26T00:00:00Z", "checkDate": "2025-09-30T00:00:00Z", "checkCount": 0, "id": "ff2815b9-9125-4359-aa1a-af9c25804308", "companyId": "17147812", "type": "payperiod", "_rid": "NmJkAKiCbEdUuAIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdUuAIAAAAAAA==/", "_etag": "\"a30047c6-0000-0100-0000-68701c930000\"", "_attachments": "attachments/", "_ts": 1752177811}, {"payPeriodId": "1070080023527540", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (2)", "startDate": "2025-10-01T00:00:00Z", "endDate": "2025-10-15T00:00:00Z", "submitByDate": "2025-10-13T00:00:00Z", "checkDate": "2025-10-15T00:00:00Z", "checkCount": 0, "id": "3b3bc20c-4e68-44ef-abb5-e47101882d5c", "companyId": "17147812", "type": "payperiod", "_rid": "NmJkAKiCbEdVuAIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdVuAIAAAAAAA==/", "_etag": "\"a3004cc6-0000-0100-0000-68701c930000\"", "_attachments": "attachments/", "_ts": 1752177811}, {"payPeriodId": "1070080023527541", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (2)", "startDate": "2025-10-16T00:00:00Z", "endDate": "2025-10-31T00:00:00Z", "submitByDate": "2025-10-29T00:00:00Z", "checkDate": "2025-10-31T00:00:00Z", "checkCount": 0, "id": "46f0d543-25c7-4e15-b3bd-adb5dc2f46de", "companyId": "17147812", "type": "payperiod", "_rid": "NmJkAKiCbEdWuAIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdWuAIAAAAAAA==/", "_etag": "\"a3004ec6-0000-0100-0000-68701c930000\"", "_attachments": "attachments/", "_ts": 1752177811}], "links": [{"rel": "self", "href": "https://ca-payroll-ai-mock-sb-001-secure.nicebeach-8a7a52ab.eastus.azurecontainerapps.io/ca/companies/17147812/payperiods"}]}, "status_code": 200}