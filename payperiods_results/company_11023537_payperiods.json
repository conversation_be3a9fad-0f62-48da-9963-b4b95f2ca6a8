{"success": true, "company_id": "11023537", "data": {"metadata": {"contentItemCount": 46}, "content": [{"payPeriodId": "1020050091714126", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2024-12-22T00:00:00Z", "endDate": "2025-01-04T00:00:00Z", "submitByDate": "2024-12-31T00:00:00Z", "checkDate": "2025-01-03T00:00:00Z", "checkCount": 3, "id": "08cf3441-cf10-4cb8-9926-2c16ebcbf877", "companyId": "11023537", "type": "payperiod", "_rid": "NmJkAKiCbEeHyAIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeHyAIAAAAAAA==/", "_etag": "\"a300b0f8-0000-0100-0000-68701de00000\"", "_attachments": "attachments/", "_ts": 1752178144}, {"payPeriodId": "1020050187131784", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-01-05T00:00:00Z", "endDate": "2025-01-18T00:00:00Z", "submitByDate": "2025-01-15T00:00:00Z", "checkDate": "2025-01-17T00:00:00Z", "checkCount": 3, "id": "90bdb56c-71b6-48bb-83af-4aeb3e63ae7b", "companyId": "11023537", "type": "payperiod", "_rid": "NmJkAKiCbEeIyAIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeIyAIAAAAAAA==/", "_etag": "\"a300b3f8-0000-0100-0000-68701de00000\"", "_attachments": "attachments/", "_ts": 1752178144}, {"payPeriodId": "1020050310152242", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-01-19T00:00:00Z", "endDate": "2025-02-01T00:00:00Z", "submitByDate": "2025-01-29T00:00:00Z", "checkDate": "2025-01-31T00:00:00Z", "checkCount": 3, "id": "91060450-a162-42d7-8d93-cd3486e6049d", "companyId": "11023537", "type": "payperiod", "_rid": "NmJkAKiCbEeJyAIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeJyAIAAAAAAA==/", "_etag": "\"a300b6f8-0000-0100-0000-68701de00000\"", "_attachments": "attachments/", "_ts": 1752178144}, {"payPeriodId": "1020050443422940", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-02-02T00:00:00Z", "endDate": "2025-02-15T00:00:00Z", "submitByDate": "2025-02-12T00:00:00Z", "checkDate": "2025-02-14T00:00:00Z", "checkCount": 3, "id": "075ad410-9c1a-41ab-96d1-fc5a73d4929c", "companyId": "11023537", "type": "payperiod", "_rid": "NmJkAKiCbEeKyAIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeKyAIAAAAAAA==/", "_etag": "\"a300baf8-0000-0100-0000-68701de00000\"", "_attachments": "attachments/", "_ts": 1752178144}, {"payPeriodId": "1020050561678194", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-02-16T00:00:00Z", "endDate": "2025-03-01T00:00:00Z", "submitByDate": "2025-02-26T00:00:00Z", "checkDate": "2025-02-28T00:00:00Z", "checkCount": 3, "id": "a1b44605-7819-48ec-8755-fad7f262543f", "companyId": "11023537", "type": "payperiod", "_rid": "NmJkAKiCbEeLyAIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeLyAIAAAAAAA==/", "_etag": "\"a300bcf8-0000-0100-0000-68701de00000\"", "_attachments": "attachments/", "_ts": 1752178144}, {"payPeriodId": "1020050701566366", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-03-02T00:00:00Z", "endDate": "2025-03-15T00:00:00Z", "submitByDate": "2025-03-12T00:00:00Z", "checkDate": "2025-03-14T00:00:00Z", "checkCount": 3, "id": "3dbea560-bcbf-4ccb-8e24-c8c35d092060", "companyId": "11023537", "type": "payperiod", "_rid": "NmJkAKiCbEeMyAIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeMyAIAAAAAAA==/", "_etag": "\"a300bef8-0000-0100-0000-68701de00000\"", "_attachments": "attachments/", "_ts": 1752178144}, {"payPeriodId": "1020050828517156", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-03-16T00:00:00Z", "endDate": "2025-03-29T00:00:00Z", "submitByDate": "2025-03-26T00:00:00Z", "checkDate": "2025-03-28T00:00:00Z", "checkCount": 3, "id": "4e55b38a-1adb-457c-a6bf-a0d04d660d13", "companyId": "11023537", "type": "payperiod", "_rid": "NmJkAKiCbEeNyAIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeNyAIAAAAAAA==/", "_etag": "\"a300bff8-0000-0100-0000-68701de00000\"", "_attachments": "attachments/", "_ts": 1752178144}, {"payPeriodId": "1020051660358659", "status": "COMPLETED", "description": "Void", "startDate": "2025-03-16T00:00:00Z", "endDate": "2025-03-29T00:00:00Z", "submitByDate": "2025-03-27T00:00:00Z", "checkDate": "2025-03-28T00:00:00Z", "checkCount": 2, "id": "3550c716-1ec4-4c80-8287-6d7e4cc2f810", "companyId": "11023537", "type": "payperiod", "_rid": "NmJkAKiCbEeOyAIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeOyAIAAAAAAA==/", "_etag": "\"a300c4f8-0000-0100-0000-68701de00000\"", "_attachments": "attachments/", "_ts": 1752178144}, {"payPeriodId": "1020051660584128", "status": "COMPLETED", "description": "Additional payroll", "startDate": "2025-03-16T00:00:00Z", "endDate": "2025-03-29T00:00:00Z", "submitByDate": "2025-03-27T00:00:00Z", "checkDate": "2025-03-28T00:00:00Z", "checkCount": 2, "id": "8076d3f6-543b-4d39-a8ac-f29285679117", "companyId": "11023537", "type": "payperiod", "_rid": "NmJkAKiCbEePyAIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEePyAIAAAAAAA==/", "_etag": "\"a300c7f8-0000-0100-0000-68701de10000\"", "_attachments": "attachments/", "_ts": 1752178145}, {"payPeriodId": "1020050943929713", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-03-30T00:00:00Z", "endDate": "2025-04-12T00:00:00Z", "submitByDate": "2025-04-09T00:00:00Z", "checkDate": "2025-04-11T00:00:00Z", "checkCount": 0, "id": "4794fb65-d08e-4354-806f-1556502095b5", "companyId": "11023537", "type": "payperiod", "_rid": "NmJkAKiCbEeQyAIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeQyAIAAAAAAA==/", "_etag": "\"a300c9f8-0000-0100-0000-68701de10000\"", "_attachments": "attachments/", "_ts": 1752178145}, {"payPeriodId": "1020051078108601", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-04-13T00:00:00Z", "endDate": "2025-04-26T00:00:00Z", "submitByDate": "2025-04-23T00:00:00Z", "checkDate": "2025-04-25T00:00:00Z", "checkCount": 0, "id": "96eb2ad0-52f7-46f8-87eb-026a8c882d22", "companyId": "11023537", "type": "payperiod", "_rid": "NmJkAKiCbEeRyAIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeRyAIAAAAAAA==/", "_etag": "\"a300ccf8-0000-0100-0000-68701de10000\"", "_attachments": "attachments/", "_ts": 1752178145}, {"payPeriodId": "1020051187999395", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-04-27T00:00:00Z", "endDate": "2025-05-10T00:00:00Z", "submitByDate": "2025-05-07T00:00:00Z", "checkDate": "2025-05-09T00:00:00Z", "checkCount": 0, "id": "3b30a366-eb30-458c-933c-1f158185f817", "companyId": "11023537", "type": "payperiod", "_rid": "NmJkAKiCbEeSyAIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeSyAIAAAAAAA==/", "_etag": "\"a300d2f8-0000-0100-0000-68701de10000\"", "_attachments": "attachments/", "_ts": 1752178145}, {"payPeriodId": "1020051302141831", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-05-11T00:00:00Z", "endDate": "2025-05-24T00:00:00Z", "submitByDate": "2025-05-21T00:00:00Z", "checkDate": "2025-05-23T00:00:00Z", "checkCount": 0, "id": "c63b6aac-8065-4b23-806a-f7a19d4074b1", "companyId": "11023537", "type": "payperiod", "_rid": "NmJkAKiCbEeTyAIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeTyAIAAAAAAA==/", "_etag": "\"a300d6f8-0000-0100-0000-68701de10000\"", "_attachments": "attachments/", "_ts": 1752178145}, {"payPeriodId": "1020051422988591", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-05-25T00:00:00Z", "endDate": "2025-06-07T00:00:00Z", "submitByDate": "2025-06-04T00:00:00Z", "checkDate": "2025-06-06T00:00:00Z", "checkCount": 0, "id": "1a9febf6-e152-414d-b508-1a2f6bfd642f", "companyId": "11023537", "type": "payperiod", "_rid": "NmJkAKiCbEeUyAIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeUyAIAAAAAAA==/", "_etag": "\"a300dbf8-0000-0100-0000-68701de10000\"", "_attachments": "attachments/", "_ts": 1752178145}, {"payPeriodId": "1020051513392615", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-06-08T00:00:00Z", "endDate": "2025-06-21T00:00:00Z", "submitByDate": "2025-06-18T00:00:00Z", "checkDate": "2025-06-20T00:00:00Z", "checkCount": 0, "id": "8251c333-f42b-45eb-8c9a-63d4ded8f6b4", "companyId": "11023537", "type": "payperiod", "_rid": "NmJkAKiCbEeVyAIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeVyAIAAAAAAA==/", "_etag": "\"a300ddf8-0000-0100-0000-68701de10000\"", "_attachments": "attachments/", "_ts": 1752178145}, {"payPeriodId": "1020051625361467", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-06-22T00:00:00Z", "endDate": "2025-07-05T00:00:00Z", "submitByDate": "2025-07-01T00:00:00Z", "checkDate": "2025-07-03T00:00:00Z", "checkCount": 0, "id": "52cafc28-389b-4c59-8a5f-5ab1aa8c7f47", "companyId": "11023537", "type": "payperiod", "_rid": "NmJkAKiCbEeWyAIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeWyAIAAAAAAA==/", "_etag": "\"a300e3f8-0000-0100-0000-68701de10000\"", "_attachments": "attachments/", "_ts": 1752178145}, {"payPeriodId": "1020051771182099", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-07-06T00:00:00Z", "endDate": "2025-07-19T00:00:00Z", "submitByDate": "2025-07-16T00:00:00Z", "checkDate": "2025-07-18T00:00:00Z", "checkCount": 0, "id": "12bed478-b212-4e6c-b968-6c96fcd55829", "companyId": "11023537", "type": "payperiod", "_rid": "NmJkAKiCbEeXyAIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeXyAIAAAAAAA==/", "_etag": "\"a300e9f8-0000-0100-0000-68701de10000\"", "_attachments": "attachments/", "_ts": 1752178145}, {"payPeriodId": "1020051894376092", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-07-20T00:00:00Z", "endDate": "2025-08-02T00:00:00Z", "submitByDate": "2025-07-30T00:00:00Z", "checkDate": "2025-08-01T00:00:00Z", "checkCount": 0, "id": "41e18345-f8a1-45a3-8772-ad19c15a1db3", "companyId": "11023537", "type": "payperiod", "_rid": "NmJkAKiCbEeYyAIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeYyAIAAAAAAA==/", "_etag": "\"a300ecf8-0000-0100-0000-68701de10000\"", "_attachments": "attachments/", "_ts": 1752178145}, {"payPeriodId": "1020052001587697", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-08-03T00:00:00Z", "endDate": "2025-08-16T00:00:00Z", "submitByDate": "2025-08-13T00:00:00Z", "checkDate": "2025-08-15T00:00:00Z", "checkCount": 0, "id": "3722dcac-88bd-469f-81d9-70ea455c1c1a", "companyId": "11023537", "type": "payperiod", "_rid": "NmJkAKiCbEeZyAIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeZyAIAAAAAAA==/", "_etag": "\"a300eff8-0000-0100-0000-68701de10000\"", "_attachments": "attachments/", "_ts": 1752178145}, {"payPeriodId": "1020052122550187", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-08-17T00:00:00Z", "endDate": "2025-08-30T00:00:00Z", "submitByDate": "2025-08-27T00:00:00Z", "checkDate": "2025-08-29T00:00:00Z", "checkCount": 0, "id": "dea5fc53-95b3-425f-b42d-2358fc877919", "companyId": "11023537", "type": "payperiod", "_rid": "NmJkAKiCbEeayAIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeayAIAAAAAAA==/", "_etag": "\"a300f6f8-0000-0100-0000-68701de10000\"", "_attachments": "attachments/", "_ts": 1752178145}, {"payPeriodId": "1020052240803887", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-08-31T00:00:00Z", "endDate": "2025-09-13T00:00:00Z", "submitByDate": "2025-09-10T00:00:00Z", "checkDate": "2025-09-12T00:00:00Z", "checkCount": 0, "id": "033a93fb-30b3-4541-b552-d1e6a9f22fb0", "companyId": "11023537", "type": "payperiod", "_rid": "NmJkAKiCbEebyAIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEebyAIAAAAAAA==/", "_etag": "\"a300f7f8-0000-0100-0000-68701de10000\"", "_attachments": "attachments/", "_ts": 1752178146}, {"payPeriodId": "1020052357962295", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-09-14T00:00:00Z", "endDate": "2025-09-27T00:00:00Z", "submitByDate": "2025-09-24T00:00:00Z", "checkDate": "2025-09-26T00:00:00Z", "checkCount": 0, "id": "e9b712b0-bce4-484f-ad5f-a1ec05196248", "companyId": "11023537", "type": "payperiod", "_rid": "NmJkAKiCbEecyAIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEecyAIAAAAAAA==/", "_etag": "\"a300fbf8-0000-0100-0000-68701de20000\"", "_attachments": "attachments/", "_ts": 1752178146}, {"payPeriodId": "1020052481400084", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-09-28T00:00:00Z", "endDate": "2025-10-11T00:00:00Z", "submitByDate": "2025-10-08T00:00:00Z", "checkDate": "2025-10-10T00:00:00Z", "checkCount": 0, "id": "198cebf8-0e50-41b1-a44f-b0bec09291f6", "companyId": "11023537", "type": "payperiod", "_rid": "NmJkAKiCbEedyAIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEedyAIAAAAAAA==/", "_etag": "\"a300fcf8-0000-0100-0000-68701de20000\"", "_attachments": "attachments/", "_ts": 1752178146}, {"payPeriodId": "1020050091714126", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2024-12-22T00:00:00Z", "endDate": "2025-01-04T00:00:00Z", "submitByDate": "2024-12-31T00:00:00Z", "checkDate": "2025-01-03T00:00:00Z", "checkCount": 3, "id": "4dc48ffc-9064-4db1-9d26-4ddd860e5fb7", "companyId": "11023537", "type": "payperiod", "_rid": "NmJkAKiCbEenyAIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEenyAIAAAAAAA==/", "_etag": "\"a3001bf9-0000-0100-0000-68701de20000\"", "_attachments": "attachments/", "_ts": 1752178146}, {"payPeriodId": "1020050187131784", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-01-05T00:00:00Z", "endDate": "2025-01-18T00:00:00Z", "submitByDate": "2025-01-15T00:00:00Z", "checkDate": "2025-01-17T00:00:00Z", "checkCount": 3, "id": "dbb663cd-ad02-4ac2-81a7-25783b6acae6", "companyId": "11023537", "type": "payperiod", "_rid": "NmJkAKiCbEeoyAIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeoyAIAAAAAAA==/", "_etag": "\"a3001ff9-0000-0100-0000-68701de30000\"", "_attachments": "attachments/", "_ts": 1752178147}, {"payPeriodId": "1020050310152242", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-01-19T00:00:00Z", "endDate": "2025-02-01T00:00:00Z", "submitByDate": "2025-01-29T00:00:00Z", "checkDate": "2025-01-31T00:00:00Z", "checkCount": 3, "id": "321e845c-4963-4d9c-9cc6-c5610cfbc1fd", "companyId": "11023537", "type": "payperiod", "_rid": "NmJkAKiCbEepyAIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEepyAIAAAAAAA==/", "_etag": "\"a30023f9-0000-0100-0000-68701de30000\"", "_attachments": "attachments/", "_ts": 1752178147}, {"payPeriodId": "1020050443422940", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-02-02T00:00:00Z", "endDate": "2025-02-15T00:00:00Z", "submitByDate": "2025-02-12T00:00:00Z", "checkDate": "2025-02-14T00:00:00Z", "checkCount": 3, "id": "3b899086-7473-42e1-88fa-fa48633915dc", "companyId": "11023537", "type": "payperiod", "_rid": "NmJkAKiCbEeqyAIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeqyAIAAAAAAA==/", "_etag": "\"a30027f9-0000-0100-0000-68701de30000\"", "_attachments": "attachments/", "_ts": 1752178147}, {"payPeriodId": "1020050561678194", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-02-16T00:00:00Z", "endDate": "2025-03-01T00:00:00Z", "submitByDate": "2025-02-26T00:00:00Z", "checkDate": "2025-02-28T00:00:00Z", "checkCount": 3, "id": "cbbf02f8-11ae-4205-b2e3-d9de6545dbad", "companyId": "11023537", "type": "payperiod", "_rid": "NmJkAKiCbEeryAIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeryAIAAAAAAA==/", "_etag": "\"a3002bf9-0000-0100-0000-68701de30000\"", "_attachments": "attachments/", "_ts": 1752178147}, {"payPeriodId": "1020050701566366", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-03-02T00:00:00Z", "endDate": "2025-03-15T00:00:00Z", "submitByDate": "2025-03-12T00:00:00Z", "checkDate": "2025-03-14T00:00:00Z", "checkCount": 3, "id": "3b86932e-9f0d-41ad-909e-0cff821baf1d", "companyId": "11023537", "type": "payperiod", "_rid": "NmJkAKiCbEesyAIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEesyAIAAAAAAA==/", "_etag": "\"a3002ef9-0000-0100-0000-68701de30000\"", "_attachments": "attachments/", "_ts": 1752178147}, {"payPeriodId": "1020050828517156", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-03-16T00:00:00Z", "endDate": "2025-03-29T00:00:00Z", "submitByDate": "2025-03-26T00:00:00Z", "checkDate": "2025-03-28T00:00:00Z", "checkCount": 3, "id": "6ed88661-02d1-4aaf-b52d-c86fb508ae8e", "companyId": "11023537", "type": "payperiod", "_rid": "NmJkAKiCbEetyAIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEetyAIAAAAAAA==/", "_etag": "\"a3002ff9-0000-0100-0000-68701de30000\"", "_attachments": "attachments/", "_ts": 1752178147}, {"payPeriodId": "1020051660358659", "status": "COMPLETED", "description": "Void", "startDate": "2025-03-16T00:00:00Z", "endDate": "2025-03-29T00:00:00Z", "submitByDate": "2025-03-27T00:00:00Z", "checkDate": "2025-03-28T00:00:00Z", "checkCount": 2, "id": "af9b6376-556c-47f9-a9dc-d0a6a14edc66", "companyId": "11023537", "type": "payperiod", "_rid": "NmJkAKiCbEeuyAIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeuyAIAAAAAAA==/", "_etag": "\"a30030f9-0000-0100-0000-68701de30000\"", "_attachments": "attachments/", "_ts": 1752178147}, {"payPeriodId": "1020051660584128", "status": "COMPLETED", "description": "Additional payroll", "startDate": "2025-03-16T00:00:00Z", "endDate": "2025-03-29T00:00:00Z", "submitByDate": "2025-03-27T00:00:00Z", "checkDate": "2025-03-28T00:00:00Z", "checkCount": 2, "id": "c4ceba2b-90d8-4b9b-8e93-201259711f43", "companyId": "11023537", "type": "payperiod", "_rid": "NmJkAKiCbEevyAIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEevyAIAAAAAAA==/", "_etag": "\"a30035f9-0000-0100-0000-68701de30000\"", "_attachments": "attachments/", "_ts": 1752178147}, {"payPeriodId": "1020050943929713", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-03-30T00:00:00Z", "endDate": "2025-04-12T00:00:00Z", "submitByDate": "2025-04-09T00:00:00Z", "checkDate": "2025-04-11T00:00:00Z", "checkCount": 3, "id": "c455ecc7-32ad-40cc-a992-bfbe47aedfa2", "companyId": "11023537", "type": "payperiod", "_rid": "NmJkAKiCbEewyAIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEewyAIAAAAAAA==/", "_etag": "\"a30039f9-0000-0100-0000-68701de30000\"", "_attachments": "attachments/", "_ts": 1752178147}, {"payPeriodId": "1020051078108601", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-04-13T00:00:00Z", "endDate": "2025-04-26T00:00:00Z", "submitByDate": "2025-04-23T00:00:00Z", "checkDate": "2025-04-25T00:00:00Z", "checkCount": 3, "id": "b87051cd-c62e-4b8f-a34e-88f7c0a8c6b4", "companyId": "11023537", "type": "payperiod", "_rid": "NmJkAKiCbEexyAIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEexyAIAAAAAAA==/", "_etag": "\"a3003ef9-0000-0100-0000-68701de30000\"", "_attachments": "attachments/", "_ts": 1752178147}, {"payPeriodId": "1020051187999395", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-04-27T00:00:00Z", "endDate": "2025-05-10T00:00:00Z", "submitByDate": "2025-05-07T00:00:00Z", "checkDate": "2025-05-09T00:00:00Z", "checkCount": 3, "id": "e9bd2de3-124a-4805-a5c9-0f920516379d", "companyId": "11023537", "type": "payperiod", "_rid": "NmJkAKiCbEeyyAIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeyyAIAAAAAAA==/", "_etag": "\"a30042f9-0000-0100-0000-68701de30000\"", "_attachments": "attachments/", "_ts": 1752178147}, {"payPeriodId": "1020051302141831", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-05-11T00:00:00Z", "endDate": "2025-05-24T00:00:00Z", "submitByDate": "2025-05-21T00:00:00Z", "checkDate": "2025-05-23T00:00:00Z", "checkCount": 3, "id": "3afe7cd7-f14b-477e-95b1-59e0cd7a27e2", "companyId": "11023537", "type": "payperiod", "_rid": "NmJkAKiCbEezyAIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEezyAIAAAAAAA==/", "_etag": "\"a30046f9-0000-0100-0000-68701de30000\"", "_attachments": "attachments/", "_ts": 1752178147}, {"payPeriodId": "1020051422988591", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-05-25T00:00:00Z", "endDate": "2025-06-07T00:00:00Z", "submitByDate": "2025-06-04T00:00:00Z", "checkDate": "2025-06-06T00:00:00Z", "checkCount": 3, "id": "284a2bad-3b9f-472c-a075-58c7700db319", "companyId": "11023537", "type": "payperiod", "_rid": "NmJkAKiCbEe0yAIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEe0yAIAAAAAAA==/", "_etag": "\"a30049f9-0000-0100-0000-68701de30000\"", "_attachments": "attachments/", "_ts": 1752178147}, {"payPeriodId": "1020051513392615", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-06-08T00:00:00Z", "endDate": "2025-06-21T00:00:00Z", "submitByDate": "2025-06-18T00:00:00Z", "checkDate": "2025-06-20T00:00:00Z", "checkCount": 3, "id": "7b079ce3-f6cd-41b5-93b2-3228a5f5b6fb", "companyId": "11023537", "type": "payperiod", "_rid": "NmJkAKiCbEe1yAIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEe1yAIAAAAAAA==/", "_etag": "\"a3004bf9-0000-0100-0000-68701de40000\"", "_attachments": "attachments/", "_ts": 1752178148}, {"payPeriodId": "1020051625361467", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-06-22T00:00:00Z", "endDate": "2025-07-05T00:00:00Z", "submitByDate": "2025-07-01T00:00:00Z", "checkDate": "2025-07-03T00:00:00Z", "checkCount": 3, "id": "cffe0d51-8079-4b21-af40-adb404006aee", "companyId": "11023537", "type": "payperiod", "_rid": "NmJkAKiCbEe2yAIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEe2yAIAAAAAAA==/", "_etag": "\"a3004cf9-0000-0100-0000-68701de40000\"", "_attachments": "attachments/", "_ts": 1752178148}, {"payPeriodId": "1020051771182099", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-07-06T00:00:00Z", "endDate": "2025-07-19T00:00:00Z", "submitByDate": "2025-07-16T00:00:00Z", "checkDate": "2025-07-18T00:00:00Z", "checkCount": 0, "id": "f2bf1797-990d-4ab2-9edb-ec06b32bf3b0", "companyId": "11023537", "type": "payperiod", "_rid": "NmJkAKiCbEe3yAIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEe3yAIAAAAAAA==/", "_etag": "\"a3004ef9-0000-0100-0000-68701de40000\"", "_attachments": "attachments/", "_ts": 1752178148}, {"payPeriodId": "1020051894376092", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-07-20T00:00:00Z", "endDate": "2025-08-02T00:00:00Z", "submitByDate": "2025-07-30T00:00:00Z", "checkDate": "2025-08-01T00:00:00Z", "checkCount": 0, "id": "7d423682-a3c4-481d-b530-3685f59b64e4", "companyId": "11023537", "type": "payperiod", "_rid": "NmJkAKiCbEe4yAIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEe4yAIAAAAAAA==/", "_etag": "\"a3004ff9-0000-0100-0000-68701de40000\"", "_attachments": "attachments/", "_ts": 1752178148}, {"payPeriodId": "1020052001587697", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-08-03T00:00:00Z", "endDate": "2025-08-16T00:00:00Z", "submitByDate": "2025-08-13T00:00:00Z", "checkDate": "2025-08-15T00:00:00Z", "checkCount": 0, "id": "3d8b56fb-0cee-4df9-be44-ef550af2e0a6", "companyId": "11023537", "type": "payperiod", "_rid": "NmJkAKiCbEe5yAIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEe5yAIAAAAAAA==/", "_etag": "\"a30054f9-0000-0100-0000-68701de40000\"", "_attachments": "attachments/", "_ts": 1752178148}, {"payPeriodId": "1020052122550187", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-08-17T00:00:00Z", "endDate": "2025-08-30T00:00:00Z", "submitByDate": "2025-08-27T00:00:00Z", "checkDate": "2025-08-29T00:00:00Z", "checkCount": 0, "id": "6466d327-6172-4a50-b31c-0dfaadc77cb4", "companyId": "11023537", "type": "payperiod", "_rid": "NmJkAKiCbEe6yAIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEe6yAIAAAAAAA==/", "_etag": "\"a30055f9-0000-0100-0000-68701de40000\"", "_attachments": "attachments/", "_ts": 1752178148}, {"payPeriodId": "1020052240803887", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-08-31T00:00:00Z", "endDate": "2025-09-13T00:00:00Z", "submitByDate": "2025-09-10T00:00:00Z", "checkDate": "2025-09-12T00:00:00Z", "checkCount": 0, "id": "6e9a1a1e-5ed4-4cba-9ed0-1b0a862578e1", "companyId": "11023537", "type": "payperiod", "_rid": "NmJkAKiCbEe7yAIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEe7yAIAAAAAAA==/", "_etag": "\"a30057f9-0000-0100-0000-68701de40000\"", "_attachments": "attachments/", "_ts": 1752178148}, {"payPeriodId": "1020052357962295", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-09-14T00:00:00Z", "endDate": "2025-09-27T00:00:00Z", "submitByDate": "2025-09-24T00:00:00Z", "checkDate": "2025-09-26T00:00:00Z", "checkCount": 0, "id": "b361c08c-6883-4e66-b737-8db15baa4819", "companyId": "11023537", "type": "payperiod", "_rid": "NmJkAKiCbEe8yAIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEe8yAIAAAAAAA==/", "_etag": "\"a3005af9-0000-0100-0000-68701de40000\"", "_attachments": "attachments/", "_ts": 1752178148}, {"payPeriodId": "1020052481400084", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-09-28T00:00:00Z", "endDate": "2025-10-11T00:00:00Z", "submitByDate": "2025-10-08T00:00:00Z", "checkDate": "2025-10-10T00:00:00Z", "checkCount": 0, "id": "88f25c94-cbef-4f67-ac69-44b5f0e455cf", "companyId": "11023537", "type": "payperiod", "_rid": "NmJkAKiCbEe9yAIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEe9yAIAAAAAAA==/", "_etag": "\"a3005df9-0000-0100-0000-68701de40000\"", "_attachments": "attachments/", "_ts": 1752178148}], "links": [{"rel": "self", "href": "https://ca-payroll-ai-mock-sb-001-secure.nicebeach-8a7a52ab.eastus.azurecontainerapps.io/ca/companies/11023537/payperiods"}]}, "status_code": 200}