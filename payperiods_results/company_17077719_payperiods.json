{"success": true, "company_id": "17077719", "data": {"metadata": {"contentItemCount": 40}, "content": [{"payPeriodId": "1080039085421902", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2024-12-30T00:00:00Z", "endDate": "2025-01-12T00:00:00Z", "submitByDate": "2025-01-15T00:00:00Z", "checkDate": "2025-01-17T00:00:00Z", "checkCount": 3, "id": "ddcc5f87-997a-4e03-94b0-029402815b07", "companyId": "17077719", "type": "payperiod", "_rid": "NmJkAKiCbEcz6AIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcz6AIAAAAAAA==/", "_etag": "\"a4005b68-0000-0100-0000-687020720000\"", "_attachments": "attachments/", "_ts": 1752178802}, {"payPeriodId": "1080039177978566", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-01-13T00:00:00Z", "endDate": "2025-01-26T00:00:00Z", "submitByDate": "2025-01-29T00:00:00Z", "checkDate": "2025-01-31T00:00:00Z", "checkCount": 3, "id": "62abee14-6cea-49ad-a830-1eb737e53554", "companyId": "17077719", "type": "payperiod", "_rid": "NmJkAKiCbEc06AIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc06AIAAAAAAA==/", "_etag": "\"a4006068-0000-0100-0000-687020720000\"", "_attachments": "attachments/", "_ts": 1752178802}, {"payPeriodId": "1080039281022982", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-01-27T00:00:00Z", "endDate": "2025-02-09T00:00:00Z", "submitByDate": "2025-02-12T00:00:00Z", "checkDate": "2025-02-14T00:00:00Z", "checkCount": 3, "id": "2d76b8ac-49f7-40e0-b1fd-47d49d8216cb", "companyId": "17077719", "type": "payperiod", "_rid": "NmJkAKiCbEc16AIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc16AIAAAAAAA==/", "_etag": "\"a4006568-0000-0100-0000-687020720000\"", "_attachments": "attachments/", "_ts": 1752178802}, {"payPeriodId": "1080039371168631", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-02-10T00:00:00Z", "endDate": "2025-02-23T00:00:00Z", "submitByDate": "2025-02-26T00:00:00Z", "checkDate": "2025-02-28T00:00:00Z", "checkCount": 3, "id": "be9e7734-2de5-4b14-ad3b-2169e6b4c40e", "companyId": "17077719", "type": "payperiod", "_rid": "NmJkAKiCbEc26AIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc26AIAAAAAAA==/", "_etag": "\"a4006768-0000-0100-0000-687020720000\"", "_attachments": "attachments/", "_ts": 1752178802}, {"payPeriodId": "1080039465651258", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-02-24T00:00:00Z", "endDate": "2025-03-09T00:00:00Z", "submitByDate": "2025-03-12T00:00:00Z", "checkDate": "2025-03-14T00:00:00Z", "checkCount": 3, "id": "9df87fff-1ca2-4d06-a542-cbf8733764c0", "companyId": "17077719", "type": "payperiod", "_rid": "NmJkAKiCbEc36AIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc36AIAAAAAAA==/", "_etag": "\"a4006a68-0000-0100-0000-687020720000\"", "_attachments": "attachments/", "_ts": 1752178802}, {"payPeriodId": "1080039564271709", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-03-10T00:00:00Z", "endDate": "2025-03-23T00:00:00Z", "submitByDate": "2025-03-26T00:00:00Z", "checkDate": "2025-03-28T00:00:00Z", "checkCount": 3, "id": "78bbb929-95e5-4b73-819c-946d0b26d6e9", "companyId": "17077719", "type": "payperiod", "_rid": "NmJkAKiCbEc46AIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc46AIAAAAAAA==/", "_etag": "\"a4006d68-0000-0100-0000-687020720000\"", "_attachments": "attachments/", "_ts": 1752178802}, {"payPeriodId": "1080039664294350", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-03-24T00:00:00Z", "endDate": "2025-04-06T00:00:00Z", "submitByDate": "2025-04-09T00:00:00Z", "checkDate": "2025-04-11T00:00:00Z", "checkCount": 0, "id": "fea80cd7-28e6-4fe9-8074-94d838ea854b", "companyId": "17077719", "type": "payperiod", "_rid": "NmJkAKiCbEc56AIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc56AIAAAAAAA==/", "_etag": "\"a4007068-0000-0100-0000-687020720000\"", "_attachments": "attachments/", "_ts": 1752178802}, {"payPeriodId": "1080039755476664", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-04-07T00:00:00Z", "endDate": "2025-04-20T00:00:00Z", "submitByDate": "2025-04-23T00:00:00Z", "checkDate": "2025-04-25T00:00:00Z", "checkCount": 0, "id": "122db78c-fc2a-43b4-b600-455eba54a9a1", "companyId": "17077719", "type": "payperiod", "_rid": "NmJkAKiCbEc66AIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc66AIAAAAAAA==/", "_etag": "\"a4007168-0000-0100-0000-687020720000\"", "_attachments": "attachments/", "_ts": 1752178802}, {"payPeriodId": "1080039846376114", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-04-21T00:00:00Z", "endDate": "2025-05-04T00:00:00Z", "submitByDate": "2025-05-07T00:00:00Z", "checkDate": "2025-05-09T00:00:00Z", "checkCount": 0, "id": "34429472-7f6b-44f9-974b-fc917654ec6a", "companyId": "17077719", "type": "payperiod", "_rid": "NmJkAKiCbEc76AIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc76AIAAAAAAA==/", "_etag": "\"a4007668-0000-0100-0000-687020730000\"", "_attachments": "attachments/", "_ts": 1752178803}, {"payPeriodId": "1080039941504482", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-05-05T00:00:00Z", "endDate": "2025-05-18T00:00:00Z", "submitByDate": "2025-05-21T00:00:00Z", "checkDate": "2025-05-23T00:00:00Z", "checkCount": 0, "id": "c0b530ba-42a6-44eb-8a85-975d6359aef2", "companyId": "17077719", "type": "payperiod", "_rid": "NmJkAKiCbEc86AIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc86AIAAAAAAA==/", "_etag": "\"a4007b68-0000-0100-0000-687020730000\"", "_attachments": "attachments/", "_ts": 1752178803}, {"payPeriodId": "1080040031903338", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-05-19T00:00:00Z", "endDate": "2025-06-01T00:00:00Z", "submitByDate": "2025-06-04T00:00:00Z", "checkDate": "2025-06-06T00:00:00Z", "checkCount": 0, "id": "e1368887-67e8-488f-9ed1-560f8702d0cc", "companyId": "17077719", "type": "payperiod", "_rid": "NmJkAKiCbEc96AIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc96AIAAAAAAA==/", "_etag": "\"a4008168-0000-0100-0000-687020730000\"", "_attachments": "attachments/", "_ts": 1752178803}, {"payPeriodId": "1080040128275933", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-06-02T00:00:00Z", "endDate": "2025-06-15T00:00:00Z", "submitByDate": "2025-06-18T00:00:00Z", "checkDate": "2025-06-20T00:00:00Z", "checkCount": 0, "id": "596df18f-77b9-430c-9b55-70da910a774c", "companyId": "17077719", "type": "payperiod", "_rid": "NmJkAKiCbEc+6AIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc+6AIAAAAAAA==/", "_etag": "\"a4008668-0000-0100-0000-687020730000\"", "_attachments": "attachments/", "_ts": 1752178803}, {"payPeriodId": "1080040219242421", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-06-16T00:00:00Z", "endDate": "2025-06-29T00:00:00Z", "submitByDate": "2025-07-01T00:00:00Z", "checkDate": "2025-07-03T00:00:00Z", "checkCount": 0, "id": "ffb798c8-5ab0-4ace-8583-2a112b6b4a73", "companyId": "17077719", "type": "payperiod", "_rid": "NmJkAKiCbEc-6AIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc-6AIAAAAAAA==/", "_etag": "\"a4008968-0000-0100-0000-687020730000\"", "_attachments": "attachments/", "_ts": 1752178803}, {"payPeriodId": "1080040325241469", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-06-30T00:00:00Z", "endDate": "2025-07-13T00:00:00Z", "submitByDate": "2025-07-16T00:00:00Z", "checkDate": "2025-07-18T00:00:00Z", "checkCount": 0, "id": "7b79f5cf-1374-42ed-a8ed-b08e3524d889", "companyId": "17077719", "type": "payperiod", "_rid": "NmJkAKiCbEdA6AIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdA6AIAAAAAAA==/", "_etag": "\"a4008e68-0000-0100-0000-687020730000\"", "_attachments": "attachments/", "_ts": 1752178803}, {"payPeriodId": "1080040432531669", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-07-14T00:00:00Z", "endDate": "2025-07-27T00:00:00Z", "submitByDate": "2025-07-30T00:00:00Z", "checkDate": "2025-08-01T00:00:00Z", "checkCount": 0, "id": "07f3a10b-d125-4454-ac18-9b401a1aff5d", "companyId": "17077719", "type": "payperiod", "_rid": "NmJkAKiCbEdB6AIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdB6AIAAAAAAA==/", "_etag": "\"a4009468-0000-0100-0000-687020730000\"", "_attachments": "attachments/", "_ts": 1752178803}, {"payPeriodId": "1080040539203834", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-07-28T00:00:00Z", "endDate": "2025-08-10T00:00:00Z", "submitByDate": "2025-08-13T00:00:00Z", "checkDate": "2025-08-15T00:00:00Z", "checkCount": 0, "id": "8593ed6b-fea6-4adb-8df6-730818efe04e", "companyId": "17077719", "type": "payperiod", "_rid": "NmJkAKiCbEdC6AIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdC6AIAAAAAAA==/", "_etag": "\"a4009868-0000-0100-0000-687020730000\"", "_attachments": "attachments/", "_ts": 1752178803}, {"payPeriodId": "1080040636188486", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-08-11T00:00:00Z", "endDate": "2025-08-24T00:00:00Z", "submitByDate": "2025-08-27T00:00:00Z", "checkDate": "2025-08-29T00:00:00Z", "checkCount": 0, "id": "df948f53-d8e6-4cf5-8ef9-390237bc3dab", "companyId": "17077719", "type": "payperiod", "_rid": "NmJkAKiCbEdD6AIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdD6AIAAAAAAA==/", "_etag": "\"a4009f68-0000-0100-0000-687020730000\"", "_attachments": "attachments/", "_ts": 1752178803}, {"payPeriodId": "1080040730912645", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-08-25T00:00:00Z", "endDate": "2025-09-07T00:00:00Z", "submitByDate": "2025-09-10T00:00:00Z", "checkDate": "2025-09-12T00:00:00Z", "checkCount": 0, "id": "b8e77aa6-ac0f-445f-bb44-2ea69f2bfa94", "companyId": "17077719", "type": "payperiod", "_rid": "NmJkAKiCbEdE6AIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdE6AIAAAAAAA==/", "_etag": "\"a400a768-0000-0100-0000-687020730000\"", "_attachments": "attachments/", "_ts": 1752178803}, {"payPeriodId": "1080040823554392", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-09-08T00:00:00Z", "endDate": "2025-09-21T00:00:00Z", "submitByDate": "2025-09-24T00:00:00Z", "checkDate": "2025-09-26T00:00:00Z", "checkCount": 0, "id": "56bfa676-57f8-4e6d-9731-8e61b01f7250", "companyId": "17077719", "type": "payperiod", "_rid": "NmJkAKiCbEdF6AIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdF6AIAAAAAAA==/", "_etag": "\"a400b068-0000-0100-0000-687020730000\"", "_attachments": "attachments/", "_ts": 1752178803}, {"payPeriodId": "1080040918606200", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-09-22T00:00:00Z", "endDate": "2025-10-05T00:00:00Z", "submitByDate": "2025-10-08T00:00:00Z", "checkDate": "2025-10-10T00:00:00Z", "checkCount": 0, "id": "baafcae8-d49d-4a34-9fdb-3e4f6d073f6a", "companyId": "17077719", "type": "payperiod", "_rid": "NmJkAKiCbEdG6AIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdG6AIAAAAAAA==/", "_etag": "\"a400b968-0000-0100-0000-687020730000\"", "_attachments": "attachments/", "_ts": 1752178803}, {"payPeriodId": "1080039085421902", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2024-12-30T00:00:00Z", "endDate": "2025-01-12T00:00:00Z", "submitByDate": "2025-01-15T00:00:00Z", "checkDate": "2025-01-17T00:00:00Z", "checkCount": 3, "id": "5d72cceb-4ba8-48f1-a7f1-41aa0bfb5f1a", "companyId": "17077719", "type": "payperiod", "_rid": "NmJkAKiCbEdP6AIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdP6AIAAAAAAA==/", "_etag": "\"a400e168-0000-0100-0000-687020740000\"", "_attachments": "attachments/", "_ts": 1752178804}, {"payPeriodId": "1080039177978566", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-01-13T00:00:00Z", "endDate": "2025-01-26T00:00:00Z", "submitByDate": "2025-01-29T00:00:00Z", "checkDate": "2025-01-31T00:00:00Z", "checkCount": 3, "id": "be01e905-f453-40fe-8e4b-8dd84bede20f", "companyId": "17077719", "type": "payperiod", "_rid": "NmJkAKiCbEdQ6AIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdQ6AIAAAAAAA==/", "_etag": "\"a400e668-0000-0100-0000-687020740000\"", "_attachments": "attachments/", "_ts": 1752178804}, {"payPeriodId": "1080039281022982", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-01-27T00:00:00Z", "endDate": "2025-02-09T00:00:00Z", "submitByDate": "2025-02-12T00:00:00Z", "checkDate": "2025-02-14T00:00:00Z", "checkCount": 3, "id": "2cc8c55f-cb18-47a1-a51a-e9a52ed0a93d", "companyId": "17077719", "type": "payperiod", "_rid": "NmJkAKiCbEdR6AIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdR6AIAAAAAAA==/", "_etag": "\"a400e868-0000-0100-0000-687020740000\"", "_attachments": "attachments/", "_ts": 1752178804}, {"payPeriodId": "1080039371168631", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-02-10T00:00:00Z", "endDate": "2025-02-23T00:00:00Z", "submitByDate": "2025-02-26T00:00:00Z", "checkDate": "2025-02-28T00:00:00Z", "checkCount": 3, "id": "8612790c-92e9-41be-bbe9-d8a4fea11a54", "companyId": "17077719", "type": "payperiod", "_rid": "NmJkAKiCbEdS6AIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdS6AIAAAAAAA==/", "_etag": "\"a400eb68-0000-0100-0000-687020740000\"", "_attachments": "attachments/", "_ts": 1752178804}, {"payPeriodId": "1080039465651258", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-02-24T00:00:00Z", "endDate": "2025-03-09T00:00:00Z", "submitByDate": "2025-03-12T00:00:00Z", "checkDate": "2025-03-14T00:00:00Z", "checkCount": 3, "id": "aab32684-cd6f-4878-a176-118d7fdeaec7", "companyId": "17077719", "type": "payperiod", "_rid": "NmJkAKiCbEdT6AIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdT6AIAAAAAAA==/", "_etag": "\"a400ed68-0000-0100-0000-687020740000\"", "_attachments": "attachments/", "_ts": 1752178804}, {"payPeriodId": "1080039564271709", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-03-10T00:00:00Z", "endDate": "2025-03-23T00:00:00Z", "submitByDate": "2025-03-26T00:00:00Z", "checkDate": "2025-03-28T00:00:00Z", "checkCount": 3, "id": "ce52690c-034b-499c-924c-69f9eeddfe00", "companyId": "17077719", "type": "payperiod", "_rid": "NmJkAKiCbEdU6AIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdU6AIAAAAAAA==/", "_etag": "\"a400ef68-0000-0100-0000-687020740000\"", "_attachments": "attachments/", "_ts": 1752178804}, {"payPeriodId": "1080039664294350", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-03-24T00:00:00Z", "endDate": "2025-04-06T00:00:00Z", "submitByDate": "2025-04-09T00:00:00Z", "checkDate": "2025-04-11T00:00:00Z", "checkCount": 3, "id": "d9329e58-fa15-4511-806d-8a63a5783d79", "companyId": "17077719", "type": "payperiod", "_rid": "NmJkAKiCbEdV6AIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdV6AIAAAAAAA==/", "_etag": "\"a400f368-0000-0100-0000-687020750000\"", "_attachments": "attachments/", "_ts": 1752178805}, {"payPeriodId": "1080039755476664", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-04-07T00:00:00Z", "endDate": "2025-04-20T00:00:00Z", "submitByDate": "2025-04-23T00:00:00Z", "checkDate": "2025-04-25T00:00:00Z", "checkCount": 3, "id": "d613c093-8425-4e51-ad9f-19aadc158dca", "companyId": "17077719", "type": "payperiod", "_rid": "NmJkAKiCbEdW6AIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdW6AIAAAAAAA==/", "_etag": "\"a400f668-0000-0100-0000-687020750000\"", "_attachments": "attachments/", "_ts": 1752178805}, {"payPeriodId": "1080039846376114", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-04-21T00:00:00Z", "endDate": "2025-05-04T00:00:00Z", "submitByDate": "2025-05-07T00:00:00Z", "checkDate": "2025-05-09T00:00:00Z", "checkCount": 3, "id": "ac949ecf-e01f-405c-bbe7-39ba89c73da7", "companyId": "17077719", "type": "payperiod", "_rid": "NmJkAKiCbEdX6AIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdX6AIAAAAAAA==/", "_etag": "\"a400f868-0000-0100-0000-687020750000\"", "_attachments": "attachments/", "_ts": 1752178805}, {"payPeriodId": "1080039941504482", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-05-05T00:00:00Z", "endDate": "2025-05-18T00:00:00Z", "submitByDate": "2025-05-21T00:00:00Z", "checkDate": "2025-05-23T00:00:00Z", "checkCount": 3, "id": "7a336d1d-1917-4a89-ab7b-04b7d5524d39", "companyId": "17077719", "type": "payperiod", "_rid": "NmJkAKiCbEdY6AIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdY6AIAAAAAAA==/", "_etag": "\"a400fb68-0000-0100-0000-687020750000\"", "_attachments": "attachments/", "_ts": 1752178805}, {"payPeriodId": "1080040031903338", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-05-19T00:00:00Z", "endDate": "2025-06-01T00:00:00Z", "submitByDate": "2025-06-04T00:00:00Z", "checkDate": "2025-06-06T00:00:00Z", "checkCount": 3, "id": "b2396bb5-6269-4109-98f8-1cdf7816cdf9", "companyId": "17077719", "type": "payperiod", "_rid": "NmJkAKiCbEdZ6AIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdZ6AIAAAAAAA==/", "_etag": "\"a400fe68-0000-0100-0000-687020750000\"", "_attachments": "attachments/", "_ts": 1752178805}, {"payPeriodId": "1080040128275933", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-06-02T00:00:00Z", "endDate": "2025-06-15T00:00:00Z", "submitByDate": "2025-06-18T00:00:00Z", "checkDate": "2025-06-20T00:00:00Z", "checkCount": 3, "id": "f1d80cba-5065-4905-95f8-905e5340c50d", "companyId": "17077719", "type": "payperiod", "_rid": "NmJkAKiCbEda6AIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEda6AIAAAAAAA==/", "_etag": "\"a4000069-0000-0100-0000-687020750000\"", "_attachments": "attachments/", "_ts": 1752178805}, {"payPeriodId": "1080040219242421", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-06-16T00:00:00Z", "endDate": "2025-06-29T00:00:00Z", "submitByDate": "2025-07-01T00:00:00Z", "checkDate": "2025-07-03T00:00:00Z", "checkCount": 3, "id": "e9a87ded-4769-411f-86b0-a19898c757d9", "companyId": "17077719", "type": "payperiod", "_rid": "NmJkAKiCbEdb6AIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdb6AIAAAAAAA==/", "_etag": "\"a4000769-0000-0100-0000-687020750000\"", "_attachments": "attachments/", "_ts": 1752178805}, {"payPeriodId": "1080040325241469", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-06-30T00:00:00Z", "endDate": "2025-07-13T00:00:00Z", "submitByDate": "2025-07-16T00:00:00Z", "checkDate": "2025-07-18T00:00:00Z", "checkCount": 0, "id": "9eee317b-2ec0-4ee6-b3e4-b2e5b1db542e", "companyId": "17077719", "type": "payperiod", "_rid": "NmJkAKiCbEdc6AIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdc6AIAAAAAAA==/", "_etag": "\"a4000869-0000-0100-0000-687020750000\"", "_attachments": "attachments/", "_ts": 1752178805}, {"payPeriodId": "1080040432531669", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-07-14T00:00:00Z", "endDate": "2025-07-27T00:00:00Z", "submitByDate": "2025-07-30T00:00:00Z", "checkDate": "2025-08-01T00:00:00Z", "checkCount": 0, "id": "0d519366-e341-41e9-a0ec-a9083bb09170", "companyId": "17077719", "type": "payperiod", "_rid": "NmJkAKiCbEdd6AIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdd6AIAAAAAAA==/", "_etag": "\"a4000a69-0000-0100-0000-687020750000\"", "_attachments": "attachments/", "_ts": 1752178805}, {"payPeriodId": "1080040539203834", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-07-28T00:00:00Z", "endDate": "2025-08-10T00:00:00Z", "submitByDate": "2025-08-13T00:00:00Z", "checkDate": "2025-08-15T00:00:00Z", "checkCount": 0, "id": "4dbed3c8-bc31-4e27-b98f-9b076b5a13fe", "companyId": "17077719", "type": "payperiod", "_rid": "NmJkAKiCbEde6AIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEde6AIAAAAAAA==/", "_etag": "\"a4000c69-0000-0100-0000-687020750000\"", "_attachments": "attachments/", "_ts": 1752178805}, {"payPeriodId": "1080040636188486", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-08-11T00:00:00Z", "endDate": "2025-08-24T00:00:00Z", "submitByDate": "2025-08-27T00:00:00Z", "checkDate": "2025-08-29T00:00:00Z", "checkCount": 0, "id": "339abce1-0f40-4454-a461-b83dd1b2de1c", "companyId": "17077719", "type": "payperiod", "_rid": "NmJkAKiCbEdf6AIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdf6AIAAAAAAA==/", "_etag": "\"a4000e69-0000-0100-0000-687020750000\"", "_attachments": "attachments/", "_ts": 1752178805}, {"payPeriodId": "1080040730912645", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-08-25T00:00:00Z", "endDate": "2025-09-07T00:00:00Z", "submitByDate": "2025-09-10T00:00:00Z", "checkDate": "2025-09-12T00:00:00Z", "checkCount": 0, "id": "41d8d4e6-8c37-490b-98a9-a0a55884f91f", "companyId": "17077719", "type": "payperiod", "_rid": "NmJkAKiCbEdg6AIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdg6AIAAAAAAA==/", "_etag": "\"a4000f69-0000-0100-0000-687020750000\"", "_attachments": "attachments/", "_ts": 1752178805}, {"payPeriodId": "1080040823554392", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-09-08T00:00:00Z", "endDate": "2025-09-21T00:00:00Z", "submitByDate": "2025-09-24T00:00:00Z", "checkDate": "2025-09-26T00:00:00Z", "checkCount": 0, "id": "1eb306db-4ef3-43c1-bc2b-7660fdb20b42", "companyId": "17077719", "type": "payperiod", "_rid": "NmJkAKiCbEdh6AIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdh6AIAAAAAAA==/", "_etag": "\"a4001469-0000-0100-0000-687020760000\"", "_attachments": "attachments/", "_ts": 1752178806}, {"payPeriodId": "1080040918606200", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-09-22T00:00:00Z", "endDate": "2025-10-05T00:00:00Z", "submitByDate": "2025-10-08T00:00:00Z", "checkDate": "2025-10-10T00:00:00Z", "checkCount": 0, "id": "7325d1ad-483b-49b9-ac35-fcbb67bf95ae", "companyId": "17077719", "type": "payperiod", "_rid": "NmJkAKiCbEdi6AIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdi6AIAAAAAAA==/", "_etag": "\"a4001769-0000-0100-0000-687020760000\"", "_attachments": "attachments/", "_ts": 1752178806}], "links": [{"rel": "self", "href": "https://ca-payroll-ai-mock-sb-001-secure.nicebeach-8a7a52ab.eastus.azurecontainerapps.io/ca/companies/17077719/payperiods"}]}, "status_code": 200}