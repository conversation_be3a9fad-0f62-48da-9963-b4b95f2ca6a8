{"success": true, "company_id": "14052843", "data": {"metadata": {"contentItemCount": 148}, "content": [{"payPeriodId": "1050102929399815", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2024-12-29T00:00:00Z", "endDate": "2025-01-04T00:00:00Z", "submitByDate": "2025-01-08T00:00:00Z", "checkDate": "2025-01-10T00:00:00Z", "checkCount": 3, "id": "633eefd1-e885-4e76-ac5b-de9d7860b4c2", "companyId": "14052843", "type": "payperiod", "_rid": "NmJkAKiCbEemKwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEemKwAAAAAAAA==/", "_etag": "\"9700e391-0000-0100-0000-686fd2830000\"", "_attachments": "attachments/", "_ts": 1752158851}, {"payPeriodId": "1050103155687189", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-01-05T00:00:00Z", "endDate": "2025-01-11T00:00:00Z", "submitByDate": "2025-01-15T00:00:00Z", "checkDate": "2025-01-17T00:00:00Z", "checkCount": 6, "id": "0a243af1-560f-469f-bfc6-4a0343a39acf", "companyId": "14052843", "type": "payperiod", "_rid": "NmJkAKiCbEenKwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEenKwAAAAAAAA==/", "_etag": "\"9700ea91-0000-0100-0000-686fd2830000\"", "_attachments": "attachments/", "_ts": 1752158851}, {"payPeriodId": "1050103402318411", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-01-12T00:00:00Z", "endDate": "2025-01-18T00:00:00Z", "submitByDate": "2025-01-22T00:00:00Z", "checkDate": "2025-01-24T00:00:00Z", "checkCount": 5, "id": "6db2cbf0-fdfe-4f15-8753-f1adc0b09d78", "companyId": "14052843", "type": "payperiod", "_rid": "NmJkAKiCbEeoKwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeoKwAAAAAAAA==/", "_etag": "\"9700f091-0000-0100-0000-686fd2830000\"", "_attachments": "attachments/", "_ts": 1752158851}, {"payPeriodId": "1050103640037019", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-01-19T00:00:00Z", "endDate": "2025-01-25T00:00:00Z", "submitByDate": "2025-01-29T00:00:00Z", "checkDate": "2025-01-31T00:00:00Z", "checkCount": 8, "id": "da123f73-6a0b-425f-9144-72d4672b7bd3", "companyId": "14052843", "type": "payperiod", "_rid": "NmJkAKiCbEepKwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEepKwAAAAAAAA==/", "_etag": "\"9700f391-0000-0100-0000-686fd2830000\"", "_attachments": "attachments/", "_ts": 1752158851}, {"payPeriodId": "1050103852784197", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-01-26T00:00:00Z", "endDate": "2025-02-01T00:00:00Z", "submitByDate": "2025-02-05T00:00:00Z", "checkDate": "2025-02-07T00:00:00Z", "checkCount": 6, "id": "36fac5b2-a13b-4017-baf1-a76ecb1a1191", "companyId": "14052843", "type": "payperiod", "_rid": "NmJkAKiCbEeqKwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeqKwAAAAAAAA==/", "_etag": "\"9700f891-0000-0100-0000-686fd2830000\"", "_attachments": "attachments/", "_ts": 1752158851}, {"payPeriodId": "1050104125310472", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-02-02T00:00:00Z", "endDate": "2025-02-08T00:00:00Z", "submitByDate": "2025-02-12T00:00:00Z", "checkDate": "2025-02-14T00:00:00Z", "checkCount": 5, "id": "36707fce-1328-41ec-94f1-88375664c69e", "companyId": "14052843", "type": "payperiod", "_rid": "NmJkAKiCbEerKwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEerKwAAAAAAAA==/", "_etag": "\"9700ff91-0000-0100-0000-686fd2830000\"", "_attachments": "attachments/", "_ts": 1752158851}, {"payPeriodId": "1050104379763205", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-02-09T00:00:00Z", "endDate": "2025-02-15T00:00:00Z", "submitByDate": "2025-02-19T00:00:00Z", "checkDate": "2025-02-21T00:00:00Z", "checkCount": 6, "id": "5b1d2b9a-fdab-4667-a307-f3cfbe08b77a", "companyId": "14052843", "type": "payperiod", "_rid": "NmJkAKiCbEesKwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEesKwAAAAAAAA==/", "_etag": "\"97000092-0000-0100-0000-686fd2840000\"", "_attachments": "attachments/", "_ts": 1752158852}, {"payPeriodId": "1050104581852455", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-02-16T00:00:00Z", "endDate": "2025-02-22T00:00:00Z", "submitByDate": "2025-02-26T00:00:00Z", "checkDate": "2025-02-28T00:00:00Z", "checkCount": 6, "id": "860c6baa-1188-49a7-b920-2b7d59da8cb4", "companyId": "14052843", "type": "payperiod", "_rid": "NmJkAKiCbEetKwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEetKwAAAAAAAA==/", "_etag": "\"97000192-0000-0100-0000-686fd2840000\"", "_attachments": "attachments/", "_ts": 1752158852}, {"payPeriodId": "1050105572261631", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-03-16T00:00:00Z", "endDate": "2025-03-22T00:00:00Z", "submitByDate": "2025-03-26T00:00:00Z", "checkDate": "2025-03-28T00:00:00Z", "checkCount": 3, "id": "275fbcc8-979b-47a9-9c44-47290132facd", "companyId": "14052843", "type": "payperiod", "_rid": "NmJkAKiCbEeuKwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeuKwAAAAAAAA==/", "_etag": "\"97000392-0000-0100-0000-686fd2840000\"", "_attachments": "attachments/", "_ts": 1752158852}, {"payPeriodId": "1050105862080094", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-03-23T00:00:00Z", "endDate": "2025-03-29T00:00:00Z", "submitByDate": "2025-04-02T00:00:00Z", "checkDate": "2025-04-04T00:00:00Z", "checkCount": 0, "id": "f2a3ebda-b812-436b-8d88-0ba94fea7610", "companyId": "14052843", "type": "payperiod", "_rid": "NmJkAKiCbEevKwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEevKwAAAAAAAA==/", "_etag": "\"97000892-0000-0100-0000-686fd2840000\"", "_attachments": "attachments/", "_ts": 1752158852}, {"payPeriodId": "1050106109780543", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-03-30T00:00:00Z", "endDate": "2025-04-05T00:00:00Z", "submitByDate": "2025-04-09T00:00:00Z", "checkDate": "2025-04-11T00:00:00Z", "checkCount": 0, "id": "6efbd4ff-5254-4c0e-8aa6-57ffe7b9fb30", "companyId": "14052843", "type": "payperiod", "_rid": "NmJkAKiCbEewKwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEewKwAAAAAAAA==/", "_etag": "\"97000b92-0000-0100-0000-686fd2840000\"", "_attachments": "attachments/", "_ts": 1752158852}, {"payPeriodId": "1050106318468388", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-04-06T00:00:00Z", "endDate": "2025-04-12T00:00:00Z", "submitByDate": "2025-04-16T00:00:00Z", "checkDate": "2025-04-18T00:00:00Z", "checkCount": 0, "id": "0a6f55a5-872c-4094-b220-95a96d433dc3", "companyId": "14052843", "type": "payperiod", "_rid": "NmJkAKiCbEexKwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEexKwAAAAAAAA==/", "_etag": "\"97001192-0000-0100-0000-686fd2840000\"", "_attachments": "attachments/", "_ts": 1752158852}, {"payPeriodId": "1050106559179732", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-04-13T00:00:00Z", "endDate": "2025-04-19T00:00:00Z", "submitByDate": "2025-04-23T00:00:00Z", "checkDate": "2025-04-25T00:00:00Z", "checkCount": 0, "id": "684ad5b4-e0d5-4e07-9fbe-d5279a0c199e", "companyId": "14052843", "type": "payperiod", "_rid": "NmJkAKiCbEeyKwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeyKwAAAAAAAA==/", "_etag": "\"97001592-0000-0100-0000-686fd2840000\"", "_attachments": "attachments/", "_ts": 1752158852}, {"payPeriodId": "1050106815373420", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-04-20T00:00:00Z", "endDate": "2025-04-26T00:00:00Z", "submitByDate": "2025-04-30T00:00:00Z", "checkDate": "2025-05-02T00:00:00Z", "checkCount": 0, "id": "031248ff-71ba-4587-ac04-2dacd60f631d", "companyId": "14052843", "type": "payperiod", "_rid": "NmJkAKiCbEezKwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEezKwAAAAAAAA==/", "_etag": "\"97001892-0000-0100-0000-686fd2840000\"", "_attachments": "attachments/", "_ts": 1752158852}, {"payPeriodId": "1050107034697902", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-04-27T00:00:00Z", "endDate": "2025-05-03T00:00:00Z", "submitByDate": "2025-05-07T00:00:00Z", "checkDate": "2025-05-09T00:00:00Z", "checkCount": 0, "id": "98a28093-1f7a-4fbb-b083-6126397d775f", "companyId": "14052843", "type": "payperiod", "_rid": "NmJkAKiCbEe0KwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEe0KwAAAAAAAA==/", "_etag": "\"97001c92-0000-0100-0000-686fd2840000\"", "_attachments": "attachments/", "_ts": 1752158852}, {"payPeriodId": "1050107274063399", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-05-04T00:00:00Z", "endDate": "2025-05-10T00:00:00Z", "submitByDate": "2025-05-14T00:00:00Z", "checkDate": "2025-05-16T00:00:00Z", "checkCount": 0, "id": "f9e6df0a-08e2-494c-9787-0cc2b67b28c1", "companyId": "14052843", "type": "payperiod", "_rid": "NmJkAKiCbEe1KwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEe1KwAAAAAAAA==/", "_etag": "\"97001f92-0000-0100-0000-686fd2840000\"", "_attachments": "attachments/", "_ts": 1752158852}, {"payPeriodId": "1050107531450973", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-05-11T00:00:00Z", "endDate": "2025-05-17T00:00:00Z", "submitByDate": "2025-05-21T00:00:00Z", "checkDate": "2025-05-23T00:00:00Z", "checkCount": 0, "id": "36d0fc05-7ced-46cf-898b-3925d70f6f02", "companyId": "14052843", "type": "payperiod", "_rid": "NmJkAKiCbEe2KwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEe2KwAAAAAAAA==/", "_etag": "\"97002192-0000-0100-0000-686fd2840000\"", "_attachments": "attachments/", "_ts": 1752158852}, {"payPeriodId": "1050107758657651", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-05-18T00:00:00Z", "endDate": "2025-05-24T00:00:00Z", "submitByDate": "2025-05-28T00:00:00Z", "checkDate": "2025-05-30T00:00:00Z", "checkCount": 0, "id": "3384b606-9295-41c5-b65c-48c486f9fba2", "companyId": "14052843", "type": "payperiod", "_rid": "NmJkAKiCbEe3KwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEe3KwAAAAAAAA==/", "_etag": "\"97002592-0000-0100-0000-686fd2840000\"", "_attachments": "attachments/", "_ts": 1752158852}, {"payPeriodId": "1050107981033962", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-05-25T00:00:00Z", "endDate": "2025-05-31T00:00:00Z", "submitByDate": "2025-06-04T00:00:00Z", "checkDate": "2025-06-06T00:00:00Z", "checkCount": 0, "id": "e32cce7c-cad4-43e4-83d3-25f2479b1210", "companyId": "14052843", "type": "payperiod", "_rid": "NmJkAKiCbEe4KwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEe4KwAAAAAAAA==/", "_etag": "\"97002892-0000-0100-0000-686fd2840000\"", "_attachments": "attachments/", "_ts": 1752158852}, {"payPeriodId": "1050108448784660", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-06-01T00:00:00Z", "endDate": "2025-06-07T00:00:00Z", "submitByDate": "2025-06-11T00:00:00Z", "checkDate": "2025-06-13T00:00:00Z", "checkCount": 0, "id": "d54b7813-36a8-4c64-aeb4-49a59df74fc4", "companyId": "14052843", "type": "payperiod", "_rid": "NmJkAKiCbEe5KwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEe5KwAAAAAAAA==/", "_etag": "\"97002b92-0000-0100-0000-686fd2840000\"", "_attachments": "attachments/", "_ts": 1752158852}, {"payPeriodId": "1050108448784661", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-06-08T00:00:00Z", "endDate": "2025-06-14T00:00:00Z", "submitByDate": "2025-06-18T00:00:00Z", "checkDate": "2025-06-20T00:00:00Z", "checkCount": 0, "id": "beff249e-e9aa-4d59-8c0e-aa2c6fe1c9fc", "companyId": "14052843", "type": "payperiod", "_rid": "NmJkAKiCbEe6KwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEe6KwAAAAAAAA==/", "_etag": "\"97003092-0000-0100-0000-686fd2850000\"", "_attachments": "attachments/", "_ts": 1752158853}, {"payPeriodId": "1050108918793802", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-06-15T00:00:00Z", "endDate": "2025-06-21T00:00:00Z", "submitByDate": "2025-06-25T00:00:00Z", "checkDate": "2025-06-27T00:00:00Z", "checkCount": 0, "id": "054171cf-8efe-4760-a1f6-18f9dff557ea", "companyId": "14052843", "type": "payperiod", "_rid": "NmJkAKiCbEe7KwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEe7KwAAAAAAAA==/", "_etag": "\"97003692-0000-0100-0000-686fd2850000\"", "_attachments": "attachments/", "_ts": 1752158853}, {"payPeriodId": "1050108918793803", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-06-22T00:00:00Z", "endDate": "2025-06-28T00:00:00Z", "submitByDate": "2025-07-01T00:00:00Z", "checkDate": "2025-07-03T00:00:00Z", "checkCount": 0, "id": "b6b49feb-5e3a-4ef6-a817-c3b17230d10e", "companyId": "14052843", "type": "payperiod", "_rid": "NmJkAKiCbEe8KwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEe8KwAAAAAAAA==/", "_etag": "\"97003a92-0000-0100-0000-686fd2850000\"", "_attachments": "attachments/", "_ts": 1752158853}, {"payPeriodId": "1050109187769005", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-06-29T00:00:00Z", "endDate": "2025-07-05T00:00:00Z", "submitByDate": "2025-07-09T00:00:00Z", "checkDate": "2025-07-11T00:00:00Z", "checkCount": 0, "id": "ffadb170-9918-454d-b3e7-2f3519a26a10", "companyId": "14052843", "type": "payperiod", "_rid": "NmJkAKiCbEe9KwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEe9KwAAAAAAAA==/", "_etag": "\"97003c92-0000-0100-0000-686fd2850000\"", "_attachments": "attachments/", "_ts": 1752158853}, {"payPeriodId": "1050109427511272", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-07-06T00:00:00Z", "endDate": "2025-07-12T00:00:00Z", "submitByDate": "2025-07-16T00:00:00Z", "checkDate": "2025-07-18T00:00:00Z", "checkCount": 0, "id": "81dfab9f-d530-4db5-a47b-3e0db5abe2c2", "companyId": "14052843", "type": "payperiod", "_rid": "NmJkAKiCbEe+KwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEe+KwAAAAAAAA==/", "_etag": "\"97003e92-0000-0100-0000-686fd2850000\"", "_attachments": "attachments/", "_ts": 1752158853}, {"payPeriodId": "1050109691212908", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-07-13T00:00:00Z", "endDate": "2025-07-19T00:00:00Z", "submitByDate": "2025-07-23T00:00:00Z", "checkDate": "2025-07-25T00:00:00Z", "checkCount": 0, "id": "8f265032-00dd-4e32-a56d-4522a48d5925", "companyId": "14052843", "type": "payperiod", "_rid": "NmJkAKiCbEe-KwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEe-KwAAAAAAAA==/", "_etag": "\"97004092-0000-0100-0000-686fd2850000\"", "_attachments": "attachments/", "_ts": 1752158853}, {"payPeriodId": "1050109925202469", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-07-20T00:00:00Z", "endDate": "2025-07-26T00:00:00Z", "submitByDate": "2025-07-30T00:00:00Z", "checkDate": "2025-08-01T00:00:00Z", "checkCount": 0, "id": "197b3181-27bc-453d-a6c5-51987e3b726d", "companyId": "14052843", "type": "payperiod", "_rid": "NmJkAKiCbEfAKwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfAKwAAAAAAAA==/", "_etag": "\"97004592-0000-0100-0000-686fd2850000\"", "_attachments": "attachments/", "_ts": 1752158853}, {"payPeriodId": "1050110153680325", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-07-27T00:00:00Z", "endDate": "2025-08-02T00:00:00Z", "submitByDate": "2025-08-06T00:00:00Z", "checkDate": "2025-08-08T00:00:00Z", "checkCount": 0, "id": "4f015a5f-cab8-49be-bf84-400dcd379097", "companyId": "14052843", "type": "payperiod", "_rid": "NmJkAKiCbEfBKwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfBKwAAAAAAAA==/", "_etag": "\"97004692-0000-0100-0000-686fd2850000\"", "_attachments": "attachments/", "_ts": 1752158853}, {"payPeriodId": "1050110502325282", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-08-03T00:00:00Z", "endDate": "2025-08-09T00:00:00Z", "submitByDate": "2025-08-13T00:00:00Z", "checkDate": "2025-08-15T00:00:00Z", "checkCount": 0, "id": "538f83f0-dfbd-4feb-88dd-0129a949448d", "companyId": "14052843", "type": "payperiod", "_rid": "NmJkAKiCbEfCKwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfCKwAAAAAAAA==/", "_etag": "\"97004992-0000-0100-0000-686fd2850000\"", "_attachments": "attachments/", "_ts": 1752158853}, {"payPeriodId": "1050110668887007", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-08-10T00:00:00Z", "endDate": "2025-08-16T00:00:00Z", "submitByDate": "2025-08-20T00:00:00Z", "checkDate": "2025-08-22T00:00:00Z", "checkCount": 0, "id": "54c5f8a3-bad8-4719-ab82-a896f5b94408", "companyId": "14052843", "type": "payperiod", "_rid": "NmJkAKiCbEfDKwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfDKwAAAAAAAA==/", "_etag": "\"97004e92-0000-0100-0000-686fd2850000\"", "_attachments": "attachments/", "_ts": 1752158853}, {"payPeriodId": "1050110903286925", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-08-17T00:00:00Z", "endDate": "2025-08-23T00:00:00Z", "submitByDate": "2025-08-27T00:00:00Z", "checkDate": "2025-08-29T00:00:00Z", "checkCount": 0, "id": "ec7a63df-8972-420b-a296-fb0628dcaedf", "companyId": "14052843", "type": "payperiod", "_rid": "NmJkAKiCbEfEKwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfEKwAAAAAAAA==/", "_etag": "\"97005392-0000-0100-0000-686fd2850000\"", "_attachments": "attachments/", "_ts": 1752158853}, {"payPeriodId": "1050111163086979", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-08-24T00:00:00Z", "endDate": "2025-08-30T00:00:00Z", "submitByDate": "2025-09-03T00:00:00Z", "checkDate": "2025-09-05T00:00:00Z", "checkCount": 0, "id": "1088de4a-d69e-4fe0-8e2b-e5e01ec9a5e2", "companyId": "14052843", "type": "payperiod", "_rid": "NmJkAKiCbEfFKwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfFKwAAAAAAAA==/", "_etag": "\"97005592-0000-0100-0000-686fd2850000\"", "_attachments": "attachments/", "_ts": 1752158853}, {"payPeriodId": "1050111408371848", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-08-31T00:00:00Z", "endDate": "2025-09-06T00:00:00Z", "submitByDate": "2025-09-10T00:00:00Z", "checkDate": "2025-09-12T00:00:00Z", "checkCount": 0, "id": "1aac3197-5eb1-47c0-9a1f-2492b1d4c4a3", "companyId": "14052843", "type": "payperiod", "_rid": "NmJkAKiCbEfGKwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfGKwAAAAAAAA==/", "_etag": "\"97005992-0000-0100-0000-686fd2850000\"", "_attachments": "attachments/", "_ts": 1752158853}, {"payPeriodId": "1050111650719808", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-09-07T00:00:00Z", "endDate": "2025-09-13T00:00:00Z", "submitByDate": "2025-09-17T00:00:00Z", "checkDate": "2025-09-19T00:00:00Z", "checkCount": 0, "id": "ec0bcd67-f45f-4ae9-a43d-44cc1dff226b", "companyId": "14052843", "type": "payperiod", "_rid": "NmJkAKiCbEfHKwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfHKwAAAAAAAA==/", "_etag": "\"97005c92-0000-0100-0000-686fd2850000\"", "_attachments": "attachments/", "_ts": 1752158853}, {"payPeriodId": "1050111910226474", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-09-14T00:00:00Z", "endDate": "2025-09-20T00:00:00Z", "submitByDate": "2025-09-24T00:00:00Z", "checkDate": "2025-09-26T00:00:00Z", "checkCount": 0, "id": "5807fc53-db5e-432a-9959-a0f4c6f07cde", "companyId": "14052843", "type": "payperiod", "_rid": "NmJkAKiCbEfIKwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfIKwAAAAAAAA==/", "_etag": "\"97005e92-0000-0100-0000-686fd2860000\"", "_attachments": "attachments/", "_ts": 1752158854}, {"payPeriodId": "1050112149669064", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-09-21T00:00:00Z", "endDate": "2025-09-27T00:00:00Z", "submitByDate": "2025-10-01T00:00:00Z", "checkDate": "2025-10-03T00:00:00Z", "checkCount": 0, "id": "c4821a89-af93-4e82-b0a1-53045a9d26e3", "companyId": "14052843", "type": "payperiod", "_rid": "NmJkAKiCbEfJKwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfJKwAAAAAAAA==/", "_etag": "\"97006292-0000-0100-0000-686fd2860000\"", "_attachments": "attachments/", "_ts": 1752158854}, {"payPeriodId": "1050112462890282", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-09-28T00:00:00Z", "endDate": "2025-10-04T00:00:00Z", "submitByDate": "2025-10-08T00:00:00Z", "checkDate": "2025-10-10T00:00:00Z", "checkCount": 0, "id": "c4005784-1b4e-431c-a5a8-f1d231a05742", "companyId": "14052843", "type": "payperiod", "_rid": "NmJkAKiCbEfKKwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfKKwAAAAAAAA==/", "_etag": "\"97006592-0000-0100-0000-686fd2860000\"", "_attachments": "attachments/", "_ts": 1752158854}, {"payPeriodId": "1050102929399815", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2024-12-29T00:00:00Z", "endDate": "2025-01-04T00:00:00Z", "submitByDate": "2025-01-08T00:00:00Z", "checkDate": "2025-01-10T00:00:00Z", "checkCount": 3, "id": "da251aa5-da36-416e-98f9-891f84725e7d", "companyId": "14052843", "type": "payperiod", "_rid": "NmJkAKiCbEf4KwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf4KwAAAAAAAA==/", "_etag": "\"97000d93-0000-0100-0000-686fd2890000\"", "_attachments": "attachments/", "_ts": 1752158857}, {"payPeriodId": "1050103155687189", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-01-05T00:00:00Z", "endDate": "2025-01-11T00:00:00Z", "submitByDate": "2025-01-15T00:00:00Z", "checkDate": "2025-01-17T00:00:00Z", "checkCount": 6, "id": "9a619ad9-daee-4a0a-9115-28742da3dc9d", "companyId": "14052843", "type": "payperiod", "_rid": "NmJkAKiCbEf5KwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf5KwAAAAAAAA==/", "_etag": "\"97001093-0000-0100-0000-686fd2890000\"", "_attachments": "attachments/", "_ts": 1752158857}, {"payPeriodId": "1050103402318411", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-01-12T00:00:00Z", "endDate": "2025-01-18T00:00:00Z", "submitByDate": "2025-01-22T00:00:00Z", "checkDate": "2025-01-24T00:00:00Z", "checkCount": 5, "id": "0e9d9c8e-8785-4c2d-8342-9eca6b946893", "companyId": "14052843", "type": "payperiod", "_rid": "NmJkAKiCbEf6KwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf6KwAAAAAAAA==/", "_etag": "\"97001493-0000-0100-0000-686fd2890000\"", "_attachments": "attachments/", "_ts": 1752158857}, {"payPeriodId": "1050103640037019", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-01-19T00:00:00Z", "endDate": "2025-01-25T00:00:00Z", "submitByDate": "2025-01-29T00:00:00Z", "checkDate": "2025-01-31T00:00:00Z", "checkCount": 8, "id": "4a331c0a-eb72-4a46-ab06-3e3556ecd379", "companyId": "14052843", "type": "payperiod", "_rid": "NmJkAKiCbEf7KwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf7KwAAAAAAAA==/", "_etag": "\"97001593-0000-0100-0000-686fd2890000\"", "_attachments": "attachments/", "_ts": 1752158857}, {"payPeriodId": "1050103852784197", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-01-26T00:00:00Z", "endDate": "2025-02-01T00:00:00Z", "submitByDate": "2025-02-05T00:00:00Z", "checkDate": "2025-02-07T00:00:00Z", "checkCount": 6, "id": "c6fb5438-e0e2-49b0-971d-ef065cc02d6d", "companyId": "14052843", "type": "payperiod", "_rid": "NmJkAKiCbEf8KwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf8KwAAAAAAAA==/", "_etag": "\"97001893-0000-0100-0000-686fd2890000\"", "_attachments": "attachments/", "_ts": 1752158857}, {"payPeriodId": "1050104125310472", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-02-02T00:00:00Z", "endDate": "2025-02-08T00:00:00Z", "submitByDate": "2025-02-12T00:00:00Z", "checkDate": "2025-02-14T00:00:00Z", "checkCount": 5, "id": "89a096e3-b9c8-4c92-a9e8-8f76d0764a42", "companyId": "14052843", "type": "payperiod", "_rid": "NmJkAKiCbEf9KwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf9KwAAAAAAAA==/", "_etag": "\"97001d93-0000-0100-0000-686fd2890000\"", "_attachments": "attachments/", "_ts": 1752158857}, {"payPeriodId": "1050104379763205", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-02-09T00:00:00Z", "endDate": "2025-02-15T00:00:00Z", "submitByDate": "2025-02-19T00:00:00Z", "checkDate": "2025-02-21T00:00:00Z", "checkCount": 6, "id": "ade01b1a-d6c3-4377-96ea-94a43491971c", "companyId": "14052843", "type": "payperiod", "_rid": "NmJkAKiCbEf+KwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf+KwAAAAAAAA==/", "_etag": "\"97001f93-0000-0100-0000-686fd28a0000\"", "_attachments": "attachments/", "_ts": 1752158858}, {"payPeriodId": "1050104581852455", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-02-16T00:00:00Z", "endDate": "2025-02-22T00:00:00Z", "submitByDate": "2025-02-26T00:00:00Z", "checkDate": "2025-02-28T00:00:00Z", "checkCount": 6, "id": "bb13e070-14ce-4fa0-9fe8-637b6c043dbc", "companyId": "14052843", "type": "payperiod", "_rid": "NmJkAKiCbEf-KwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf-KwAAAAAAAA==/", "_etag": "\"97002393-0000-0100-0000-686fd28a0000\"", "_attachments": "attachments/", "_ts": 1752158858}, {"payPeriodId": "1050105572261631", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-03-16T00:00:00Z", "endDate": "2025-03-22T00:00:00Z", "submitByDate": "2025-03-26T00:00:00Z", "checkDate": "2025-03-28T00:00:00Z", "checkCount": 3, "id": "df284cd9-ff04-4baf-95c3-b1825515a4e6", "companyId": "14052843", "type": "payperiod", "_rid": "NmJkAKiCbEcALAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcALAAAAAAAAA==/", "_etag": "\"97002693-0000-0100-0000-686fd28a0000\"", "_attachments": "attachments/", "_ts": 1752158858}, {"payPeriodId": "1050105862080094", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-03-23T00:00:00Z", "endDate": "2025-03-29T00:00:00Z", "submitByDate": "2025-04-02T00:00:00Z", "checkDate": "2025-04-04T00:00:00Z", "checkCount": 6, "id": "fc7f8aad-c683-4bf0-b564-c07d91d86154", "companyId": "14052843", "type": "payperiod", "_rid": "NmJkAKiCbEcBLAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcBLAAAAAAAAA==/", "_etag": "\"97002893-0000-0100-0000-686fd28a0000\"", "_attachments": "attachments/", "_ts": 1752158858}, {"payPeriodId": "1050106109780543", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-03-30T00:00:00Z", "endDate": "2025-04-05T00:00:00Z", "submitByDate": "2025-04-09T00:00:00Z", "checkDate": "2025-04-11T00:00:00Z", "checkCount": 7, "id": "0ccc141a-623c-473f-ab6a-c7679dcfad8c", "companyId": "14052843", "type": "payperiod", "_rid": "NmJkAKiCbEcCLAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcCLAAAAAAAAA==/", "_etag": "\"97002a93-0000-0100-0000-686fd28a0000\"", "_attachments": "attachments/", "_ts": 1752158858}, {"payPeriodId": "1050106318468388", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-04-06T00:00:00Z", "endDate": "2025-04-12T00:00:00Z", "submitByDate": "2025-04-16T00:00:00Z", "checkDate": "2025-04-18T00:00:00Z", "checkCount": 7, "id": "8b3666bd-0986-462d-8f2e-5d67ba6abe9c", "companyId": "14052843", "type": "payperiod", "_rid": "NmJkAKiCbEcDLAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcDLAAAAAAAAA==/", "_etag": "\"97002c93-0000-0100-0000-686fd28a0000\"", "_attachments": "attachments/", "_ts": 1752158858}, {"payPeriodId": "1050106559179732", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-04-13T00:00:00Z", "endDate": "2025-04-19T00:00:00Z", "submitByDate": "2025-04-23T00:00:00Z", "checkDate": "2025-04-25T00:00:00Z", "checkCount": 7, "id": "b3b238b9-491a-4136-a6f2-0a57471d7c0d", "companyId": "14052843", "type": "payperiod", "_rid": "NmJkAKiCbEcELAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcELAAAAAAAAA==/", "_etag": "\"97002f93-0000-0100-0000-686fd28a0000\"", "_attachments": "attachments/", "_ts": 1752158858}, {"payPeriodId": "1050106815373420", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-04-20T00:00:00Z", "endDate": "2025-04-26T00:00:00Z", "submitByDate": "2025-04-30T00:00:00Z", "checkDate": "2025-05-02T00:00:00Z", "checkCount": 7, "id": "18e01e97-472b-48f2-956b-d90bb49d8ad7", "companyId": "14052843", "type": "payperiod", "_rid": "NmJkAKiCbEcFLAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcFLAAAAAAAAA==/", "_etag": "\"97003293-0000-0100-0000-686fd28a0000\"", "_attachments": "attachments/", "_ts": 1752158858}, {"payPeriodId": "1050107034697902", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-04-27T00:00:00Z", "endDate": "2025-05-03T00:00:00Z", "submitByDate": "2025-05-07T00:00:00Z", "checkDate": "2025-05-09T00:00:00Z", "checkCount": 5, "id": "91b67e14-8496-4ba8-88ed-2a23fccd29a6", "companyId": "14052843", "type": "payperiod", "_rid": "NmJkAKiCbEcGLAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcGLAAAAAAAAA==/", "_etag": "\"97003493-0000-0100-0000-686fd28a0000\"", "_attachments": "attachments/", "_ts": 1752158858}, {"payPeriodId": "1050107274063399", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-05-04T00:00:00Z", "endDate": "2025-05-10T00:00:00Z", "submitByDate": "2025-05-14T00:00:00Z", "checkDate": "2025-05-16T00:00:00Z", "checkCount": 7, "id": "b256fd6d-c9fc-481f-a3e9-46330d68d398", "companyId": "14052843", "type": "payperiod", "_rid": "NmJkAKiCbEcHLAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcHLAAAAAAAAA==/", "_etag": "\"97003793-0000-0100-0000-686fd28a0000\"", "_attachments": "attachments/", "_ts": 1752158858}, {"payPeriodId": "1050107531450973", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-05-11T00:00:00Z", "endDate": "2025-05-17T00:00:00Z", "submitByDate": "2025-05-21T00:00:00Z", "checkDate": "2025-05-23T00:00:00Z", "checkCount": 6, "id": "e0156e6c-da4b-4ac5-9994-78adb0a77683", "companyId": "14052843", "type": "payperiod", "_rid": "NmJkAKiCbEcILAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcILAAAAAAAAA==/", "_etag": "\"97003a93-0000-0100-0000-686fd28a0000\"", "_attachments": "attachments/", "_ts": 1752158858}, {"payPeriodId": "1050107758657651", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-05-18T00:00:00Z", "endDate": "2025-05-24T00:00:00Z", "submitByDate": "2025-05-28T00:00:00Z", "checkDate": "2025-05-30T00:00:00Z", "checkCount": 6, "id": "ee86186b-39bc-436b-b2a9-ad3cba935bfd", "companyId": "14052843", "type": "payperiod", "_rid": "NmJkAKiCbEcJLAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcJLAAAAAAAAA==/", "_etag": "\"97003d93-0000-0100-0000-686fd28a0000\"", "_attachments": "attachments/", "_ts": 1752158858}, {"payPeriodId": "1050107981033962", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-05-25T00:00:00Z", "endDate": "2025-05-31T00:00:00Z", "submitByDate": "2025-06-04T00:00:00Z", "checkDate": "2025-06-06T00:00:00Z", "checkCount": 6, "id": "a7b2097b-073b-4bd1-a28c-847bdb220178", "companyId": "14052843", "type": "payperiod", "_rid": "NmJkAKiCbEcKLAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcKLAAAAAAAAA==/", "_etag": "\"97004393-0000-0100-0000-686fd28a0000\"", "_attachments": "attachments/", "_ts": 1752158858}, {"payPeriodId": "1050108448784660", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-06-01T00:00:00Z", "endDate": "2025-06-07T00:00:00Z", "submitByDate": "2025-06-11T00:00:00Z", "checkDate": "2025-06-13T00:00:00Z", "checkCount": 7, "id": "cbef0e1f-adc4-4410-85c1-78a1f76a8923", "companyId": "14052843", "type": "payperiod", "_rid": "NmJkAKiCbEcLLAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcLLAAAAAAAAA==/", "_etag": "\"97004593-0000-0100-0000-686fd28a0000\"", "_attachments": "attachments/", "_ts": 1752158858}, {"payPeriodId": "1050108448784661", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-06-08T00:00:00Z", "endDate": "2025-06-14T00:00:00Z", "submitByDate": "2025-06-18T00:00:00Z", "checkDate": "2025-06-20T00:00:00Z", "checkCount": 7, "id": "dedcc384-355f-40d0-8b7e-721c6a62dc11", "companyId": "14052843", "type": "payperiod", "_rid": "NmJkAKiCbEcMLAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcMLAAAAAAAAA==/", "_etag": "\"97004893-0000-0100-0000-686fd28b0000\"", "_attachments": "attachments/", "_ts": 1752158859}, {"payPeriodId": "1050108918793802", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-06-15T00:00:00Z", "endDate": "2025-06-21T00:00:00Z", "submitByDate": "2025-06-25T00:00:00Z", "checkDate": "2025-06-27T00:00:00Z", "checkCount": 6, "id": "b0fd4b89-0d11-4ef9-ab1b-4bbc9ba0072c", "companyId": "14052843", "type": "payperiod", "_rid": "NmJkAKiCbEcNLAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcNLAAAAAAAAA==/", "_etag": "\"97004d93-0000-0100-0000-686fd28b0000\"", "_attachments": "attachments/", "_ts": 1752158859}, {"payPeriodId": "1050108918793803", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-06-22T00:00:00Z", "endDate": "2025-06-28T00:00:00Z", "submitByDate": "2025-07-01T00:00:00Z", "checkDate": "2025-07-03T00:00:00Z", "checkCount": 7, "id": "dc616089-f2a8-44ab-8c76-d57c1187076d", "companyId": "14052843", "type": "payperiod", "_rid": "NmJkAKiCbEcOLAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcOLAAAAAAAAA==/", "_etag": "\"97005393-0000-0100-0000-686fd28b0000\"", "_attachments": "attachments/", "_ts": 1752158859}, {"payPeriodId": "1050109187769005", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-06-29T00:00:00Z", "endDate": "2025-07-05T00:00:00Z", "submitByDate": "2025-07-09T00:00:00Z", "checkDate": "2025-07-11T00:00:00Z", "checkCount": 0, "id": "197a1c97-a2b7-471c-b103-2334c7fb61fd", "companyId": "14052843", "type": "payperiod", "_rid": "NmJkAKiCbEcPLAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcPLAAAAAAAAA==/", "_etag": "\"97005593-0000-0100-0000-686fd28b0000\"", "_attachments": "attachments/", "_ts": 1752158859}, {"payPeriodId": "1050109427511272", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-07-06T00:00:00Z", "endDate": "2025-07-12T00:00:00Z", "submitByDate": "2025-07-16T00:00:00Z", "checkDate": "2025-07-18T00:00:00Z", "checkCount": 0, "id": "98dc0dcb-30dd-494d-8d20-6a07f2ad67ee", "companyId": "14052843", "type": "payperiod", "_rid": "NmJkAKiCbEcQLAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcQLAAAAAAAAA==/", "_etag": "\"97005793-0000-0100-0000-686fd28b0000\"", "_attachments": "attachments/", "_ts": 1752158859}, {"payPeriodId": "1050109691212908", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-07-13T00:00:00Z", "endDate": "2025-07-19T00:00:00Z", "submitByDate": "2025-07-23T00:00:00Z", "checkDate": "2025-07-25T00:00:00Z", "checkCount": 0, "id": "d791d418-3c70-4e6b-a547-14f288c82b71", "companyId": "14052843", "type": "payperiod", "_rid": "NmJkAKiCbEcRLAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcRLAAAAAAAAA==/", "_etag": "\"97005a93-0000-0100-0000-686fd28b0000\"", "_attachments": "attachments/", "_ts": 1752158859}, {"payPeriodId": "1050109925202469", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-07-20T00:00:00Z", "endDate": "2025-07-26T00:00:00Z", "submitByDate": "2025-07-30T00:00:00Z", "checkDate": "2025-08-01T00:00:00Z", "checkCount": 0, "id": "1ee13bbb-19cc-4ba9-a0b3-846ce984643d", "companyId": "14052843", "type": "payperiod", "_rid": "NmJkAKiCbEcSLAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcSLAAAAAAAAA==/", "_etag": "\"97006093-0000-0100-0000-686fd28b0000\"", "_attachments": "attachments/", "_ts": 1752158859}, {"payPeriodId": "1050110153680325", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-07-27T00:00:00Z", "endDate": "2025-08-02T00:00:00Z", "submitByDate": "2025-08-06T00:00:00Z", "checkDate": "2025-08-08T00:00:00Z", "checkCount": 0, "id": "46ff3b2a-c3e4-4272-9846-44ac3dc9905c", "companyId": "14052843", "type": "payperiod", "_rid": "NmJkAKiCbEcTLAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcTLAAAAAAAAA==/", "_etag": "\"97006393-0000-0100-0000-686fd28b0000\"", "_attachments": "attachments/", "_ts": 1752158859}, {"payPeriodId": "1050110502325282", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-08-03T00:00:00Z", "endDate": "2025-08-09T00:00:00Z", "submitByDate": "2025-08-13T00:00:00Z", "checkDate": "2025-08-15T00:00:00Z", "checkCount": 0, "id": "4425e0de-babb-4e95-a506-c3f4f656a9d6", "companyId": "14052843", "type": "payperiod", "_rid": "NmJkAKiCbEcULAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcULAAAAAAAAA==/", "_etag": "\"97006593-0000-0100-0000-686fd28b0000\"", "_attachments": "attachments/", "_ts": 1752158859}, {"payPeriodId": "1050110668887007", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-08-10T00:00:00Z", "endDate": "2025-08-16T00:00:00Z", "submitByDate": "2025-08-20T00:00:00Z", "checkDate": "2025-08-22T00:00:00Z", "checkCount": 0, "id": "d4156a9a-db11-412f-aac5-fe9ac7353c65", "companyId": "14052843", "type": "payperiod", "_rid": "NmJkAKiCbEcVLAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcVLAAAAAAAAA==/", "_etag": "\"97006893-0000-0100-0000-686fd28b0000\"", "_attachments": "attachments/", "_ts": 1752158859}, {"payPeriodId": "1050110903286925", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-08-17T00:00:00Z", "endDate": "2025-08-23T00:00:00Z", "submitByDate": "2025-08-27T00:00:00Z", "checkDate": "2025-08-29T00:00:00Z", "checkCount": 0, "id": "b5076a5a-7c61-41c1-b208-660389f0b12a", "companyId": "14052843", "type": "payperiod", "_rid": "NmJkAKiCbEcWLAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcWLAAAAAAAAA==/", "_etag": "\"97006a93-0000-0100-0000-686fd28b0000\"", "_attachments": "attachments/", "_ts": 1752158859}, {"payPeriodId": "1050111163086979", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-08-24T00:00:00Z", "endDate": "2025-08-30T00:00:00Z", "submitByDate": "2025-09-03T00:00:00Z", "checkDate": "2025-09-05T00:00:00Z", "checkCount": 0, "id": "2a31ac5c-aa82-4792-86a1-62b4a6a277e8", "companyId": "14052843", "type": "payperiod", "_rid": "NmJkAKiCbEcXLAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcXLAAAAAAAAA==/", "_etag": "\"97006d93-0000-0100-0000-686fd28b0000\"", "_attachments": "attachments/", "_ts": 1752158859}, {"payPeriodId": "1050111408371848", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-08-31T00:00:00Z", "endDate": "2025-09-06T00:00:00Z", "submitByDate": "2025-09-10T00:00:00Z", "checkDate": "2025-09-12T00:00:00Z", "checkCount": 0, "id": "7e5ec650-88e8-4137-b545-48c32d0d163a", "companyId": "14052843", "type": "payperiod", "_rid": "NmJkAKiCbEcYLAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcYLAAAAAAAAA==/", "_etag": "\"97007093-0000-0100-0000-686fd28b0000\"", "_attachments": "attachments/", "_ts": 1752158859}, {"payPeriodId": "1050111650719808", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-09-07T00:00:00Z", "endDate": "2025-09-13T00:00:00Z", "submitByDate": "2025-09-17T00:00:00Z", "checkDate": "2025-09-19T00:00:00Z", "checkCount": 0, "id": "859cb221-7615-4ce2-bca4-16527d9efd63", "companyId": "14052843", "type": "payperiod", "_rid": "NmJkAKiCbEcZLAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcZLAAAAAAAAA==/", "_etag": "\"97007393-0000-0100-0000-686fd28b0000\"", "_attachments": "attachments/", "_ts": 1752158859}, {"payPeriodId": "1050111910226474", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-09-14T00:00:00Z", "endDate": "2025-09-20T00:00:00Z", "submitByDate": "2025-09-24T00:00:00Z", "checkDate": "2025-09-26T00:00:00Z", "checkCount": 0, "id": "baa5a3a4-f204-4664-bb39-54ecbc7bf589", "companyId": "14052843", "type": "payperiod", "_rid": "NmJkAKiCbEcaLAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcaLAAAAAAAAA==/", "_etag": "\"97007493-0000-0100-0000-686fd28c0000\"", "_attachments": "attachments/", "_ts": 1752158860}, {"payPeriodId": "1050112149669064", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-09-21T00:00:00Z", "endDate": "2025-09-27T00:00:00Z", "submitByDate": "2025-10-01T00:00:00Z", "checkDate": "2025-10-03T00:00:00Z", "checkCount": 0, "id": "0ebe79b1-0897-4196-a5df-18e251b4fd53", "companyId": "14052843", "type": "payperiod", "_rid": "NmJkAKiCbEcbLAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcbLAAAAAAAAA==/", "_etag": "\"97007893-0000-0100-0000-686fd28c0000\"", "_attachments": "attachments/", "_ts": 1752158860}, {"payPeriodId": "1050112462890282", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-09-28T00:00:00Z", "endDate": "2025-10-04T00:00:00Z", "submitByDate": "2025-10-08T00:00:00Z", "checkDate": "2025-10-10T00:00:00Z", "checkCount": 0, "id": "16439365-42f3-4066-b0c0-f4a2b9a340ea", "companyId": "14052843", "type": "payperiod", "_rid": "NmJkAKiCbEccLAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEccLAAAAAAAAA==/", "_etag": "\"97007993-0000-0100-0000-686fd28c0000\"", "_attachments": "attachments/", "_ts": 1752158860}, {"payPeriodId": "1050102929399815", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2024-12-29T00:00:00Z", "endDate": "2025-01-04T00:00:00Z", "submitByDate": "2025-01-08T00:00:00Z", "checkDate": "2025-01-10T00:00:00Z", "checkCount": 3, "id": "49eb44e7-d306-4d64-bb4e-673eb22e6f66", "companyId": "14052843", "type": "payperiod", "_rid": "NmJkAKiCbEfTJAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfTJAEAAAAAAA==/", "_etag": "\"9d00c9ca-0000-0100-0000-686ff8f30000\"", "_attachments": "attachments/", "_ts": 1752168691}, {"payPeriodId": "1050103155687189", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-01-05T00:00:00Z", "endDate": "2025-01-11T00:00:00Z", "submitByDate": "2025-01-15T00:00:00Z", "checkDate": "2025-01-17T00:00:00Z", "checkCount": 6, "id": "7519bdcc-2d83-426f-8fab-ba4bc394a854", "companyId": "14052843", "type": "payperiod", "_rid": "NmJkAKiCbEfUJAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfUJAEAAAAAAA==/", "_etag": "\"9d00ccca-0000-0100-0000-686ff8f30000\"", "_attachments": "attachments/", "_ts": 1752168691}, {"payPeriodId": "1050103402318411", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-01-12T00:00:00Z", "endDate": "2025-01-18T00:00:00Z", "submitByDate": "2025-01-22T00:00:00Z", "checkDate": "2025-01-24T00:00:00Z", "checkCount": 5, "id": "9f132eab-7d54-4fa2-80d7-e5193b87a792", "companyId": "14052843", "type": "payperiod", "_rid": "NmJkAKiCbEfVJAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfVJAEAAAAAAA==/", "_etag": "\"9d00ceca-0000-0100-0000-686ff8f30000\"", "_attachments": "attachments/", "_ts": 1752168691}, {"payPeriodId": "1050103640037019", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-01-19T00:00:00Z", "endDate": "2025-01-25T00:00:00Z", "submitByDate": "2025-01-29T00:00:00Z", "checkDate": "2025-01-31T00:00:00Z", "checkCount": 8, "id": "fa65f790-ef26-40fd-9b5f-ec8e26991a0e", "companyId": "14052843", "type": "payperiod", "_rid": "NmJkAKiCbEfWJAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfWJAEAAAAAAA==/", "_etag": "\"9d00d2ca-0000-0100-0000-686ff8f30000\"", "_attachments": "attachments/", "_ts": 1752168691}, {"payPeriodId": "1050103852784197", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-01-26T00:00:00Z", "endDate": "2025-02-01T00:00:00Z", "submitByDate": "2025-02-05T00:00:00Z", "checkDate": "2025-02-07T00:00:00Z", "checkCount": 6, "id": "89639c1c-37e5-4dad-915b-9e62e24c07b0", "companyId": "14052843", "type": "payperiod", "_rid": "NmJkAKiCbEfXJAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfXJAEAAAAAAA==/", "_etag": "\"9d00d5ca-0000-0100-0000-686ff8f30000\"", "_attachments": "attachments/", "_ts": 1752168691}, {"payPeriodId": "1050104125310472", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-02-02T00:00:00Z", "endDate": "2025-02-08T00:00:00Z", "submitByDate": "2025-02-12T00:00:00Z", "checkDate": "2025-02-14T00:00:00Z", "checkCount": 5, "id": "5538ea28-fd08-441f-a77d-5b344fb7fb05", "companyId": "14052843", "type": "payperiod", "_rid": "NmJkAKiCbEfYJAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfYJAEAAAAAAA==/", "_etag": "\"9d00d7ca-0000-0100-0000-686ff8f40000\"", "_attachments": "attachments/", "_ts": 1752168692}, {"payPeriodId": "1050104379763205", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-02-09T00:00:00Z", "endDate": "2025-02-15T00:00:00Z", "submitByDate": "2025-02-19T00:00:00Z", "checkDate": "2025-02-21T00:00:00Z", "checkCount": 6, "id": "7bb8d123-13a0-422a-8477-eaff4eb7eef9", "companyId": "14052843", "type": "payperiod", "_rid": "NmJkAKiCbEfZJAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfZJAEAAAAAAA==/", "_etag": "\"9d00e3ca-0000-0100-0000-686ff8f40000\"", "_attachments": "attachments/", "_ts": 1752168692}, {"payPeriodId": "1050104581852455", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-02-16T00:00:00Z", "endDate": "2025-02-22T00:00:00Z", "submitByDate": "2025-02-26T00:00:00Z", "checkDate": "2025-02-28T00:00:00Z", "checkCount": 6, "id": "f7238180-864f-4dc6-945f-4c2c8f025f9c", "companyId": "14052843", "type": "payperiod", "_rid": "NmJkAKiCbEfaJAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfaJAEAAAAAAA==/", "_etag": "\"9d00e5ca-0000-0100-0000-686ff8f40000\"", "_attachments": "attachments/", "_ts": 1752168692}, {"payPeriodId": "1050105572261631", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-03-16T00:00:00Z", "endDate": "2025-03-22T00:00:00Z", "submitByDate": "2025-03-26T00:00:00Z", "checkDate": "2025-03-28T00:00:00Z", "checkCount": 3, "id": "c8df51a2-01bc-451d-a8ff-c23147998915", "companyId": "14052843", "type": "payperiod", "_rid": "NmJkAKiCbEfbJAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfbJAEAAAAAAA==/", "_etag": "\"9d00e7ca-0000-0100-0000-686ff8f40000\"", "_attachments": "attachments/", "_ts": 1752168692}, {"payPeriodId": "1050105862080094", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-03-23T00:00:00Z", "endDate": "2025-03-29T00:00:00Z", "submitByDate": "2025-04-02T00:00:00Z", "checkDate": "2025-04-04T00:00:00Z", "checkCount": 0, "id": "7aa43123-405a-42c0-bbaf-ca34cad7787a", "companyId": "14052843", "type": "payperiod", "_rid": "NmJkAKiCbEfcJAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfcJAEAAAAAAA==/", "_etag": "\"9d00e9ca-0000-0100-0000-686ff8f40000\"", "_attachments": "attachments/", "_ts": 1752168692}, {"payPeriodId": "1050106109780543", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-03-30T00:00:00Z", "endDate": "2025-04-05T00:00:00Z", "submitByDate": "2025-04-09T00:00:00Z", "checkDate": "2025-04-11T00:00:00Z", "checkCount": 0, "id": "9c1a9f3a-03f9-4ef1-b3b6-bf0a85473f87", "companyId": "14052843", "type": "payperiod", "_rid": "NmJkAKiCbEfdJAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfdJAEAAAAAAA==/", "_etag": "\"9d00edca-0000-0100-0000-686ff8f40000\"", "_attachments": "attachments/", "_ts": 1752168692}, {"payPeriodId": "1050106318468388", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-04-06T00:00:00Z", "endDate": "2025-04-12T00:00:00Z", "submitByDate": "2025-04-16T00:00:00Z", "checkDate": "2025-04-18T00:00:00Z", "checkCount": 0, "id": "b5d86e06-6f66-4b12-9ccb-582dbe10d201", "companyId": "14052843", "type": "payperiod", "_rid": "NmJkAKiCbEfeJAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfeJAEAAAAAAA==/", "_etag": "\"9d00f1ca-0000-0100-0000-686ff8f40000\"", "_attachments": "attachments/", "_ts": 1752168692}, {"payPeriodId": "1050106559179732", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-04-13T00:00:00Z", "endDate": "2025-04-19T00:00:00Z", "submitByDate": "2025-04-23T00:00:00Z", "checkDate": "2025-04-25T00:00:00Z", "checkCount": 0, "id": "f6bcc0e4-b87e-4d82-a569-ea0641fd0f98", "companyId": "14052843", "type": "payperiod", "_rid": "NmJkAKiCbEffJAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEffJAEAAAAAAA==/", "_etag": "\"9d00f5ca-0000-0100-0000-686ff8f40000\"", "_attachments": "attachments/", "_ts": 1752168692}, {"payPeriodId": "1050106815373420", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-04-20T00:00:00Z", "endDate": "2025-04-26T00:00:00Z", "submitByDate": "2025-04-30T00:00:00Z", "checkDate": "2025-05-02T00:00:00Z", "checkCount": 0, "id": "82627916-ccca-493a-b0fa-03e30fe1f75f", "companyId": "14052843", "type": "payperiod", "_rid": "NmJkAKiCbEfgJAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfgJAEAAAAAAA==/", "_etag": "\"9d00faca-0000-0100-0000-686ff8f40000\"", "_attachments": "attachments/", "_ts": 1752168692}, {"payPeriodId": "1050107034697902", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-04-27T00:00:00Z", "endDate": "2025-05-03T00:00:00Z", "submitByDate": "2025-05-07T00:00:00Z", "checkDate": "2025-05-09T00:00:00Z", "checkCount": 0, "id": "6e2f8e18-e90a-4fcc-a2ae-3edfe9c44275", "companyId": "14052843", "type": "payperiod", "_rid": "NmJkAKiCbEfhJAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfhJAEAAAAAAA==/", "_etag": "\"9d00fdca-0000-0100-0000-686ff8f40000\"", "_attachments": "attachments/", "_ts": 1752168692}, {"payPeriodId": "1050107274063399", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-05-04T00:00:00Z", "endDate": "2025-05-10T00:00:00Z", "submitByDate": "2025-05-14T00:00:00Z", "checkDate": "2025-05-16T00:00:00Z", "checkCount": 0, "id": "14f82046-9ea5-4940-944a-f57cf9e7029b", "companyId": "14052843", "type": "payperiod", "_rid": "NmJkAKiCbEfiJAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfiJAEAAAAAAA==/", "_etag": "\"9d00ffca-0000-0100-0000-686ff8f40000\"", "_attachments": "attachments/", "_ts": 1752168692}, {"payPeriodId": "1050107531450973", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-05-11T00:00:00Z", "endDate": "2025-05-17T00:00:00Z", "submitByDate": "2025-05-21T00:00:00Z", "checkDate": "2025-05-23T00:00:00Z", "checkCount": 0, "id": "20f83eee-6f6b-4b18-b152-1b1f689a3e73", "companyId": "14052843", "type": "payperiod", "_rid": "NmJkAKiCbEfjJAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfjJAEAAAAAAA==/", "_etag": "\"9d0001cb-0000-0100-0000-686ff8f40000\"", "_attachments": "attachments/", "_ts": 1752168692}, {"payPeriodId": "1050107758657651", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-05-18T00:00:00Z", "endDate": "2025-05-24T00:00:00Z", "submitByDate": "2025-05-28T00:00:00Z", "checkDate": "2025-05-30T00:00:00Z", "checkCount": 0, "id": "5b30ed45-a6d3-4097-9f30-e1233ff93128", "companyId": "14052843", "type": "payperiod", "_rid": "NmJkAKiCbEfkJAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfkJAEAAAAAAA==/", "_etag": "\"9d0004cb-0000-0100-0000-686ff8f40000\"", "_attachments": "attachments/", "_ts": 1752168692}, {"payPeriodId": "1050107981033962", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-05-25T00:00:00Z", "endDate": "2025-05-31T00:00:00Z", "submitByDate": "2025-06-04T00:00:00Z", "checkDate": "2025-06-06T00:00:00Z", "checkCount": 0, "id": "1c90b51f-898e-4826-b986-e4e30ba214db", "companyId": "14052843", "type": "payperiod", "_rid": "NmJkAKiCbEflJAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEflJAEAAAAAAA==/", "_etag": "\"9d0006cb-0000-0100-0000-686ff8f50000\"", "_attachments": "attachments/", "_ts": 1752168693}, {"payPeriodId": "1050108448784660", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-06-01T00:00:00Z", "endDate": "2025-06-07T00:00:00Z", "submitByDate": "2025-06-11T00:00:00Z", "checkDate": "2025-06-13T00:00:00Z", "checkCount": 0, "id": "039b07da-3dbf-44e0-90bd-9c17a606a3ee", "companyId": "14052843", "type": "payperiod", "_rid": "NmJkAKiCbEfmJAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfmJAEAAAAAAA==/", "_etag": "\"9d000bcb-0000-0100-0000-686ff8f50000\"", "_attachments": "attachments/", "_ts": 1752168693}, {"payPeriodId": "1050108448784661", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-06-08T00:00:00Z", "endDate": "2025-06-14T00:00:00Z", "submitByDate": "2025-06-18T00:00:00Z", "checkDate": "2025-06-20T00:00:00Z", "checkCount": 0, "id": "0fc908fe-36e4-4264-a68b-b754e39494b8", "companyId": "14052843", "type": "payperiod", "_rid": "NmJkAKiCbEfnJAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfnJAEAAAAAAA==/", "_etag": "\"9d000fcb-0000-0100-0000-686ff8f50000\"", "_attachments": "attachments/", "_ts": 1752168693}, {"payPeriodId": "1050108918793802", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-06-15T00:00:00Z", "endDate": "2025-06-21T00:00:00Z", "submitByDate": "2025-06-25T00:00:00Z", "checkDate": "2025-06-27T00:00:00Z", "checkCount": 0, "id": "f9b0d494-33f1-4fa3-987a-e2c40539c175", "companyId": "14052843", "type": "payperiod", "_rid": "NmJkAKiCbEfoJAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfoJAEAAAAAAA==/", "_etag": "\"9d0014cb-0000-0100-0000-686ff8f50000\"", "_attachments": "attachments/", "_ts": 1752168693}, {"payPeriodId": "1050108918793803", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-06-22T00:00:00Z", "endDate": "2025-06-28T00:00:00Z", "submitByDate": "2025-07-01T00:00:00Z", "checkDate": "2025-07-03T00:00:00Z", "checkCount": 0, "id": "e134a8b3-cbf8-45b4-8b23-e80213b5daab", "companyId": "14052843", "type": "payperiod", "_rid": "NmJkAKiCbEfpJAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfpJAEAAAAAAA==/", "_etag": "\"9d0019cb-0000-0100-0000-686ff8f50000\"", "_attachments": "attachments/", "_ts": 1752168693}, {"payPeriodId": "1050109187769005", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-06-29T00:00:00Z", "endDate": "2025-07-05T00:00:00Z", "submitByDate": "2025-07-09T00:00:00Z", "checkDate": "2025-07-11T00:00:00Z", "checkCount": 0, "id": "e3d6229d-52ca-4310-87cb-2fbfb3d319bf", "companyId": "14052843", "type": "payperiod", "_rid": "NmJkAKiCbEfqJAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfqJAEAAAAAAA==/", "_etag": "\"9d001ccb-0000-0100-0000-686ff8f50000\"", "_attachments": "attachments/", "_ts": 1752168693}, {"payPeriodId": "1050109427511272", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-07-06T00:00:00Z", "endDate": "2025-07-12T00:00:00Z", "submitByDate": "2025-07-16T00:00:00Z", "checkDate": "2025-07-18T00:00:00Z", "checkCount": 0, "id": "2599e08d-f3ab-49e7-8db9-07c2461cd70a", "companyId": "14052843", "type": "payperiod", "_rid": "NmJkAKiCbEfrJAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfrJAEAAAAAAA==/", "_etag": "\"9d0020cb-0000-0100-0000-686ff8f50000\"", "_attachments": "attachments/", "_ts": 1752168693}, {"payPeriodId": "1050109691212908", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-07-13T00:00:00Z", "endDate": "2025-07-19T00:00:00Z", "submitByDate": "2025-07-23T00:00:00Z", "checkDate": "2025-07-25T00:00:00Z", "checkCount": 0, "id": "61e32f9b-8789-4216-9e8e-846411d664b7", "companyId": "14052843", "type": "payperiod", "_rid": "NmJkAKiCbEfsJAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfsJAEAAAAAAA==/", "_etag": "\"9d0024cb-0000-0100-0000-686ff8f50000\"", "_attachments": "attachments/", "_ts": 1752168693}, {"payPeriodId": "1050109925202469", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-07-20T00:00:00Z", "endDate": "2025-07-26T00:00:00Z", "submitByDate": "2025-07-30T00:00:00Z", "checkDate": "2025-08-01T00:00:00Z", "checkCount": 0, "id": "a3ae1dcd-b7a0-4755-b6ab-07ef308b2c40", "companyId": "14052843", "type": "payperiod", "_rid": "NmJkAKiCbEftJAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEftJAEAAAAAAA==/", "_etag": "\"9d0029cb-0000-0100-0000-686ff8f50000\"", "_attachments": "attachments/", "_ts": 1752168693}, {"payPeriodId": "1050110153680325", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-07-27T00:00:00Z", "endDate": "2025-08-02T00:00:00Z", "submitByDate": "2025-08-06T00:00:00Z", "checkDate": "2025-08-08T00:00:00Z", "checkCount": 0, "id": "7c915a4e-63fe-4c8a-aef3-b37944484377", "companyId": "14052843", "type": "payperiod", "_rid": "NmJkAKiCbEfuJAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfuJAEAAAAAAA==/", "_etag": "\"9d002bcb-0000-0100-0000-686ff8f50000\"", "_attachments": "attachments/", "_ts": 1752168693}, {"payPeriodId": "1050110502325282", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-08-03T00:00:00Z", "endDate": "2025-08-09T00:00:00Z", "submitByDate": "2025-08-13T00:00:00Z", "checkDate": "2025-08-15T00:00:00Z", "checkCount": 0, "id": "129f5c64-cd8f-4467-8c57-86795bb8bbfb", "companyId": "14052843", "type": "payperiod", "_rid": "NmJkAKiCbEfvJAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfvJAEAAAAAAA==/", "_etag": "\"9d002dcb-0000-0100-0000-686ff8f50000\"", "_attachments": "attachments/", "_ts": 1752168693}, {"payPeriodId": "1050110668887007", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-08-10T00:00:00Z", "endDate": "2025-08-16T00:00:00Z", "submitByDate": "2025-08-20T00:00:00Z", "checkDate": "2025-08-22T00:00:00Z", "checkCount": 0, "id": "d4f89bf5-f2a1-4215-a87b-c3f44832499d", "companyId": "14052843", "type": "payperiod", "_rid": "NmJkAKiCbEfwJAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfwJAEAAAAAAA==/", "_etag": "\"9d0033cb-0000-0100-0000-686ff8f50000\"", "_attachments": "attachments/", "_ts": 1752168693}, {"payPeriodId": "1050110903286925", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-08-17T00:00:00Z", "endDate": "2025-08-23T00:00:00Z", "submitByDate": "2025-08-27T00:00:00Z", "checkDate": "2025-08-29T00:00:00Z", "checkCount": 0, "id": "536903e7-dc34-456a-9ded-975bdfaa053c", "companyId": "14052843", "type": "payperiod", "_rid": "NmJkAKiCbEfxJAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfxJAEAAAAAAA==/", "_etag": "\"9d0046cb-0000-0100-0000-686ff8f50000\"", "_attachments": "attachments/", "_ts": 1752168693}, {"payPeriodId": "1050111163086979", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-08-24T00:00:00Z", "endDate": "2025-08-30T00:00:00Z", "submitByDate": "2025-09-03T00:00:00Z", "checkDate": "2025-09-05T00:00:00Z", "checkCount": 0, "id": "f8cd91fe-0b6a-4b7d-bae0-df22593fa8d3", "companyId": "14052843", "type": "payperiod", "_rid": "NmJkAKiCbEfyJAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfyJAEAAAAAAA==/", "_etag": "\"9d004acb-0000-0100-0000-686ff8f60000\"", "_attachments": "attachments/", "_ts": 1752168694}, {"payPeriodId": "1050111408371848", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-08-31T00:00:00Z", "endDate": "2025-09-06T00:00:00Z", "submitByDate": "2025-09-10T00:00:00Z", "checkDate": "2025-09-12T00:00:00Z", "checkCount": 0, "id": "eebb1101-d285-4b00-ba5e-ea69b1a02aae", "companyId": "14052843", "type": "payperiod", "_rid": "NmJkAKiCbEfzJAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfzJAEAAAAAAA==/", "_etag": "\"9d004ccb-0000-0100-0000-686ff8f60000\"", "_attachments": "attachments/", "_ts": 1752168694}, {"payPeriodId": "1050111650719808", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-09-07T00:00:00Z", "endDate": "2025-09-13T00:00:00Z", "submitByDate": "2025-09-17T00:00:00Z", "checkDate": "2025-09-19T00:00:00Z", "checkCount": 0, "id": "bac3dce9-b614-48c1-ba74-f1a23b5f3548", "companyId": "14052843", "type": "payperiod", "_rid": "NmJkAKiCbEf0JAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf0JAEAAAAAAA==/", "_etag": "\"9d0052cb-0000-0100-0000-686ff8f60000\"", "_attachments": "attachments/", "_ts": 1752168694}, {"payPeriodId": "1050111910226474", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-09-14T00:00:00Z", "endDate": "2025-09-20T00:00:00Z", "submitByDate": "2025-09-24T00:00:00Z", "checkDate": "2025-09-26T00:00:00Z", "checkCount": 0, "id": "efe6652a-3a2b-4d3c-90c1-36cbe12fcc46", "companyId": "14052843", "type": "payperiod", "_rid": "NmJkAKiCbEf1JAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf1JAEAAAAAAA==/", "_etag": "\"9d0058cb-0000-0100-0000-686ff8f60000\"", "_attachments": "attachments/", "_ts": 1752168694}, {"payPeriodId": "1050112149669064", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-09-21T00:00:00Z", "endDate": "2025-09-27T00:00:00Z", "submitByDate": "2025-10-01T00:00:00Z", "checkDate": "2025-10-03T00:00:00Z", "checkCount": 0, "id": "60b1828e-dc0b-4d14-8f07-a52b6f73cc4e", "companyId": "14052843", "type": "payperiod", "_rid": "NmJkAKiCbEf2JAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf2JAEAAAAAAA==/", "_etag": "\"9d005ecb-0000-0100-0000-686ff8f60000\"", "_attachments": "attachments/", "_ts": 1752168694}, {"payPeriodId": "1050112462890282", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-09-28T00:00:00Z", "endDate": "2025-10-04T00:00:00Z", "submitByDate": "2025-10-08T00:00:00Z", "checkDate": "2025-10-10T00:00:00Z", "checkCount": 0, "id": "ca7f0761-2ca3-41ec-94f1-cacee86cfd93", "companyId": "14052843", "type": "payperiod", "_rid": "NmJkAKiCbEf3JAEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf3JAEAAAAAAA==/", "_etag": "\"9d0064cb-0000-0100-0000-686ff8f60000\"", "_attachments": "attachments/", "_ts": 1752168694}, {"payPeriodId": "1050102929399815", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2024-12-29T00:00:00Z", "endDate": "2025-01-04T00:00:00Z", "submitByDate": "2025-01-08T00:00:00Z", "checkDate": "2025-01-10T00:00:00Z", "checkCount": 3, "id": "1f36b993-f875-433f-8b3b-e6f47e5fa6f1", "companyId": "14052843", "type": "payperiod", "_rid": "NmJkAKiCbEclJQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEclJQEAAAAAAA==/", "_etag": "\"9d001dcc-0000-0100-0000-686ff8fa0000\"", "_attachments": "attachments/", "_ts": 1752168698}, {"payPeriodId": "1050103155687189", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-01-05T00:00:00Z", "endDate": "2025-01-11T00:00:00Z", "submitByDate": "2025-01-15T00:00:00Z", "checkDate": "2025-01-17T00:00:00Z", "checkCount": 6, "id": "df3cc8c6-73e2-4740-a9c1-ea1d2e951042", "companyId": "14052843", "type": "payperiod", "_rid": "NmJkAKiCbEcmJQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcmJQEAAAAAAA==/", "_etag": "\"9d0023cc-0000-0100-0000-686ff8fa0000\"", "_attachments": "attachments/", "_ts": 1752168698}, {"payPeriodId": "1050103402318411", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-01-12T00:00:00Z", "endDate": "2025-01-18T00:00:00Z", "submitByDate": "2025-01-22T00:00:00Z", "checkDate": "2025-01-24T00:00:00Z", "checkCount": 5, "id": "b1af8fbe-ca37-47a1-ac84-338ba8065082", "companyId": "14052843", "type": "payperiod", "_rid": "NmJkAKiCbEcnJQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcnJQEAAAAAAA==/", "_etag": "\"9d0028cc-0000-0100-0000-686ff8fa0000\"", "_attachments": "attachments/", "_ts": 1752168698}, {"payPeriodId": "1050103640037019", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-01-19T00:00:00Z", "endDate": "2025-01-25T00:00:00Z", "submitByDate": "2025-01-29T00:00:00Z", "checkDate": "2025-01-31T00:00:00Z", "checkCount": 8, "id": "760aa166-6d32-453a-91bf-fa304f09d824", "companyId": "14052843", "type": "payperiod", "_rid": "NmJkAKiCbEcoJQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcoJQEAAAAAAA==/", "_etag": "\"9d002dcc-0000-0100-0000-686ff8fa0000\"", "_attachments": "attachments/", "_ts": 1752168698}, {"payPeriodId": "1050103852784197", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-01-26T00:00:00Z", "endDate": "2025-02-01T00:00:00Z", "submitByDate": "2025-02-05T00:00:00Z", "checkDate": "2025-02-07T00:00:00Z", "checkCount": 6, "id": "d80291da-6498-4286-bce1-ca987039a7e4", "companyId": "14052843", "type": "payperiod", "_rid": "NmJkAKiCbEcpJQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcpJQEAAAAAAA==/", "_etag": "\"9d0031cc-0000-0100-0000-686ff8fa0000\"", "_attachments": "attachments/", "_ts": 1752168698}, {"payPeriodId": "1050104125310472", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-02-02T00:00:00Z", "endDate": "2025-02-08T00:00:00Z", "submitByDate": "2025-02-12T00:00:00Z", "checkDate": "2025-02-14T00:00:00Z", "checkCount": 5, "id": "2ca96783-891b-4783-844c-1282a216b8a8", "companyId": "14052843", "type": "payperiod", "_rid": "NmJkAKiCbEcqJQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcqJQEAAAAAAA==/", "_etag": "\"9d0035cc-0000-0100-0000-686ff8fa0000\"", "_attachments": "attachments/", "_ts": 1752168698}, {"payPeriodId": "1050104379763205", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-02-09T00:00:00Z", "endDate": "2025-02-15T00:00:00Z", "submitByDate": "2025-02-19T00:00:00Z", "checkDate": "2025-02-21T00:00:00Z", "checkCount": 6, "id": "fdbb2f10-ef5e-4770-b5b2-e583e8164ec8", "companyId": "14052843", "type": "payperiod", "_rid": "NmJkAKiCbEcrJQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcrJQEAAAAAAA==/", "_etag": "\"9d0039cc-0000-0100-0000-686ff8fa0000\"", "_attachments": "attachments/", "_ts": 1752168698}, {"payPeriodId": "1050104581852455", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-02-16T00:00:00Z", "endDate": "2025-02-22T00:00:00Z", "submitByDate": "2025-02-26T00:00:00Z", "checkDate": "2025-02-28T00:00:00Z", "checkCount": 6, "id": "090d47bd-c333-4bbd-bcb1-ec1e9824c98f", "companyId": "14052843", "type": "payperiod", "_rid": "NmJkAKiCbEcsJQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcsJQEAAAAAAA==/", "_etag": "\"9d003ccc-0000-0100-0000-686ff8fb0000\"", "_attachments": "attachments/", "_ts": 1752168699}, {"payPeriodId": "1050105572261631", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-03-16T00:00:00Z", "endDate": "2025-03-22T00:00:00Z", "submitByDate": "2025-03-26T00:00:00Z", "checkDate": "2025-03-28T00:00:00Z", "checkCount": 3, "id": "4a593deb-93e6-4788-b498-0f87f6401d1b", "companyId": "14052843", "type": "payperiod", "_rid": "NmJkAKiCbEctJQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEctJQEAAAAAAA==/", "_etag": "\"9d0043cc-0000-0100-0000-686ff8fb0000\"", "_attachments": "attachments/", "_ts": 1752168699}, {"payPeriodId": "1050105862080094", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-03-23T00:00:00Z", "endDate": "2025-03-29T00:00:00Z", "submitByDate": "2025-04-02T00:00:00Z", "checkDate": "2025-04-04T00:00:00Z", "checkCount": 6, "id": "152ce0db-3d05-4a25-9036-58e3d912df30", "companyId": "14052843", "type": "payperiod", "_rid": "NmJkAKiCbEcuJQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcuJQEAAAAAAA==/", "_etag": "\"9d0047cc-0000-0100-0000-686ff8fb0000\"", "_attachments": "attachments/", "_ts": 1752168699}, {"payPeriodId": "1050106109780543", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-03-30T00:00:00Z", "endDate": "2025-04-05T00:00:00Z", "submitByDate": "2025-04-09T00:00:00Z", "checkDate": "2025-04-11T00:00:00Z", "checkCount": 7, "id": "c4d1298b-56da-41b3-a5c1-fdb73a675e07", "companyId": "14052843", "type": "payperiod", "_rid": "NmJkAKiCbEcvJQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcvJQEAAAAAAA==/", "_etag": "\"9d004fcc-0000-0100-0000-686ff8fb0000\"", "_attachments": "attachments/", "_ts": 1752168699}, {"payPeriodId": "1050106318468388", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-04-06T00:00:00Z", "endDate": "2025-04-12T00:00:00Z", "submitByDate": "2025-04-16T00:00:00Z", "checkDate": "2025-04-18T00:00:00Z", "checkCount": 7, "id": "8d703470-a0c0-46d3-bf01-fabcac78a96b", "companyId": "14052843", "type": "payperiod", "_rid": "NmJkAKiCbEcwJQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcwJQEAAAAAAA==/", "_etag": "\"9d0054cc-0000-0100-0000-686ff8fb0000\"", "_attachments": "attachments/", "_ts": 1752168699}, {"payPeriodId": "1050106559179732", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-04-13T00:00:00Z", "endDate": "2025-04-19T00:00:00Z", "submitByDate": "2025-04-23T00:00:00Z", "checkDate": "2025-04-25T00:00:00Z", "checkCount": 7, "id": "98c85a6a-7b14-45fb-9f7c-5a65a4890246", "companyId": "14052843", "type": "payperiod", "_rid": "NmJkAKiCbEcxJQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcxJQEAAAAAAA==/", "_etag": "\"9d0057cc-0000-0100-0000-686ff8fb0000\"", "_attachments": "attachments/", "_ts": 1752168699}, {"payPeriodId": "1050106815373420", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-04-20T00:00:00Z", "endDate": "2025-04-26T00:00:00Z", "submitByDate": "2025-04-30T00:00:00Z", "checkDate": "2025-05-02T00:00:00Z", "checkCount": 7, "id": "677052ee-1344-4a8d-a452-e895aa57c2a3", "companyId": "14052843", "type": "payperiod", "_rid": "NmJkAKiCbEcyJQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcyJQEAAAAAAA==/", "_etag": "\"9d005dcc-0000-0100-0000-686ff8fb0000\"", "_attachments": "attachments/", "_ts": 1752168699}, {"payPeriodId": "1050107034697902", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-04-27T00:00:00Z", "endDate": "2025-05-03T00:00:00Z", "submitByDate": "2025-05-07T00:00:00Z", "checkDate": "2025-05-09T00:00:00Z", "checkCount": 5, "id": "c9b36cab-8fc7-4cb8-b8a1-3d4befa13cac", "companyId": "14052843", "type": "payperiod", "_rid": "NmJkAKiCbEczJQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEczJQEAAAAAAA==/", "_etag": "\"9d0062cc-0000-0100-0000-686ff8fb0000\"", "_attachments": "attachments/", "_ts": 1752168699}, {"payPeriodId": "1050107274063399", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-05-04T00:00:00Z", "endDate": "2025-05-10T00:00:00Z", "submitByDate": "2025-05-14T00:00:00Z", "checkDate": "2025-05-16T00:00:00Z", "checkCount": 7, "id": "3eabe3fa-1827-4ac6-87ff-a6e352c7a707", "companyId": "14052843", "type": "payperiod", "_rid": "NmJkAKiCbEc0JQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc0JQEAAAAAAA==/", "_etag": "\"9d0065cc-0000-0100-0000-686ff8fb0000\"", "_attachments": "attachments/", "_ts": 1752168699}, {"payPeriodId": "1050107531450973", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-05-11T00:00:00Z", "endDate": "2025-05-17T00:00:00Z", "submitByDate": "2025-05-21T00:00:00Z", "checkDate": "2025-05-23T00:00:00Z", "checkCount": 6, "id": "0a8588da-6515-4382-a487-4597016d7ca0", "companyId": "14052843", "type": "payperiod", "_rid": "NmJkAKiCbEc1JQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc1JQEAAAAAAA==/", "_etag": "\"9d0068cc-0000-0100-0000-686ff8fb0000\"", "_attachments": "attachments/", "_ts": 1752168699}, {"payPeriodId": "1050107758657651", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-05-18T00:00:00Z", "endDate": "2025-05-24T00:00:00Z", "submitByDate": "2025-05-28T00:00:00Z", "checkDate": "2025-05-30T00:00:00Z", "checkCount": 6, "id": "083f84be-d4d3-4921-9645-140c4ecf6a53", "companyId": "14052843", "type": "payperiod", "_rid": "NmJkAKiCbEc2JQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc2JQEAAAAAAA==/", "_etag": "\"9d006ccc-0000-0100-0000-686ff8fb0000\"", "_attachments": "attachments/", "_ts": 1752168699}, {"payPeriodId": "1050107981033962", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-05-25T00:00:00Z", "endDate": "2025-05-31T00:00:00Z", "submitByDate": "2025-06-04T00:00:00Z", "checkDate": "2025-06-06T00:00:00Z", "checkCount": 6, "id": "14eedcb6-542b-4256-b7e3-22e37a57342b", "companyId": "14052843", "type": "payperiod", "_rid": "NmJkAKiCbEc3JQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc3JQEAAAAAAA==/", "_etag": "\"9d0071cc-0000-0100-0000-686ff8fb0000\"", "_attachments": "attachments/", "_ts": 1752168699}, {"payPeriodId": "1050108448784660", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-06-01T00:00:00Z", "endDate": "2025-06-07T00:00:00Z", "submitByDate": "2025-06-11T00:00:00Z", "checkDate": "2025-06-13T00:00:00Z", "checkCount": 7, "id": "1df5bbe8-09a4-4ed2-8eb7-2c62c8bc2414", "companyId": "14052843", "type": "payperiod", "_rid": "NmJkAKiCbEc4JQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc4JQEAAAAAAA==/", "_etag": "\"9d0073cc-0000-0100-0000-686ff8fb0000\"", "_attachments": "attachments/", "_ts": 1752168699}, {"payPeriodId": "1050108448784661", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-06-08T00:00:00Z", "endDate": "2025-06-14T00:00:00Z", "submitByDate": "2025-06-18T00:00:00Z", "checkDate": "2025-06-20T00:00:00Z", "checkCount": 7, "id": "c686a76b-ab1a-4d91-b82e-ae14c8a509f8", "companyId": "14052843", "type": "payperiod", "_rid": "NmJkAKiCbEc5JQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc5JQEAAAAAAA==/", "_etag": "\"9d0077cc-0000-0100-0000-686ff8fc0000\"", "_attachments": "attachments/", "_ts": 1752168700}, {"payPeriodId": "1050108918793802", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-06-15T00:00:00Z", "endDate": "2025-06-21T00:00:00Z", "submitByDate": "2025-06-25T00:00:00Z", "checkDate": "2025-06-27T00:00:00Z", "checkCount": 6, "id": "acc7071c-9c0a-4912-aaf3-b23b358a9cf0", "companyId": "14052843", "type": "payperiod", "_rid": "NmJkAKiCbEc6JQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc6JQEAAAAAAA==/", "_etag": "\"9d0079cc-0000-0100-0000-686ff8fc0000\"", "_attachments": "attachments/", "_ts": 1752168700}, {"payPeriodId": "1050108918793803", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-06-22T00:00:00Z", "endDate": "2025-06-28T00:00:00Z", "submitByDate": "2025-07-01T00:00:00Z", "checkDate": "2025-07-03T00:00:00Z", "checkCount": 7, "id": "e133cb89-01ef-44bc-b388-cb1def2a0d47", "companyId": "14052843", "type": "payperiod", "_rid": "NmJkAKiCbEc7JQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc7JQEAAAAAAA==/", "_etag": "\"9d007ccc-0000-0100-0000-686ff8fc0000\"", "_attachments": "attachments/", "_ts": 1752168700}, {"payPeriodId": "1050109187769005", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-06-29T00:00:00Z", "endDate": "2025-07-05T00:00:00Z", "submitByDate": "2025-07-09T00:00:00Z", "checkDate": "2025-07-11T00:00:00Z", "checkCount": 0, "id": "40d8e09d-e2e4-4810-addc-cbcc9ce783d4", "companyId": "14052843", "type": "payperiod", "_rid": "NmJkAKiCbEc8JQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc8JQEAAAAAAA==/", "_etag": "\"9d0081cc-0000-0100-0000-686ff8fc0000\"", "_attachments": "attachments/", "_ts": 1752168700}, {"payPeriodId": "1050109427511272", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-07-06T00:00:00Z", "endDate": "2025-07-12T00:00:00Z", "submitByDate": "2025-07-16T00:00:00Z", "checkDate": "2025-07-18T00:00:00Z", "checkCount": 0, "id": "ba2aa77c-40f9-4704-9660-3c38127cb998", "companyId": "14052843", "type": "payperiod", "_rid": "NmJkAKiCbEc9JQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc9JQEAAAAAAA==/", "_etag": "\"9d008dcc-0000-0100-0000-686ff8fc0000\"", "_attachments": "attachments/", "_ts": 1752168700}, {"payPeriodId": "1050109691212908", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-07-13T00:00:00Z", "endDate": "2025-07-19T00:00:00Z", "submitByDate": "2025-07-23T00:00:00Z", "checkDate": "2025-07-25T00:00:00Z", "checkCount": 0, "id": "07ee1015-de7f-4e04-aae2-70991e0bd1cf", "companyId": "14052843", "type": "payperiod", "_rid": "NmJkAKiCbEc+JQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc+JQEAAAAAAA==/", "_etag": "\"9d0093cc-0000-0100-0000-686ff8fc0000\"", "_attachments": "attachments/", "_ts": 1752168700}, {"payPeriodId": "1050109925202469", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-07-20T00:00:00Z", "endDate": "2025-07-26T00:00:00Z", "submitByDate": "2025-07-30T00:00:00Z", "checkDate": "2025-08-01T00:00:00Z", "checkCount": 0, "id": "7a91b3d5-1c3e-447b-b6ba-34f6b0097bf4", "companyId": "14052843", "type": "payperiod", "_rid": "NmJkAKiCbEc-JQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc-JQEAAAAAAA==/", "_etag": "\"9d0098cc-0000-0100-0000-686ff8fc0000\"", "_attachments": "attachments/", "_ts": 1752168700}, {"payPeriodId": "1050110153680325", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-07-27T00:00:00Z", "endDate": "2025-08-02T00:00:00Z", "submitByDate": "2025-08-06T00:00:00Z", "checkDate": "2025-08-08T00:00:00Z", "checkCount": 0, "id": "2554cd8f-f427-434d-9d8c-10e6acd889b0", "companyId": "14052843", "type": "payperiod", "_rid": "NmJkAKiCbEdAJQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdAJQEAAAAAAA==/", "_etag": "\"9d009ccc-0000-0100-0000-686ff8fc0000\"", "_attachments": "attachments/", "_ts": 1752168700}, {"payPeriodId": "1050110502325282", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-08-03T00:00:00Z", "endDate": "2025-08-09T00:00:00Z", "submitByDate": "2025-08-13T00:00:00Z", "checkDate": "2025-08-15T00:00:00Z", "checkCount": 0, "id": "8e81be21-4648-4503-aeac-5cf8362b9d4b", "companyId": "14052843", "type": "payperiod", "_rid": "NmJkAKiCbEdBJQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdBJQEAAAAAAA==/", "_etag": "\"9d00a1cc-0000-0100-0000-686ff8fc0000\"", "_attachments": "attachments/", "_ts": 1752168700}, {"payPeriodId": "1050110668887007", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-08-10T00:00:00Z", "endDate": "2025-08-16T00:00:00Z", "submitByDate": "2025-08-20T00:00:00Z", "checkDate": "2025-08-22T00:00:00Z", "checkCount": 0, "id": "8b1814b0-8b2d-4b0b-867b-497efbf8f44f", "companyId": "14052843", "type": "payperiod", "_rid": "NmJkAKiCbEdCJQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdCJQEAAAAAAA==/", "_etag": "\"9d00a4cc-0000-0100-0000-686ff8fc0000\"", "_attachments": "attachments/", "_ts": 1752168700}, {"payPeriodId": "1050110903286925", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-08-17T00:00:00Z", "endDate": "2025-08-23T00:00:00Z", "submitByDate": "2025-08-27T00:00:00Z", "checkDate": "2025-08-29T00:00:00Z", "checkCount": 0, "id": "f9bc2c5d-7791-4e47-9220-cc1b9677f9aa", "companyId": "14052843", "type": "payperiod", "_rid": "NmJkAKiCbEdDJQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdDJQEAAAAAAA==/", "_etag": "\"9d00aacc-0000-0100-0000-686ff8fc0000\"", "_attachments": "attachments/", "_ts": 1752168700}, {"payPeriodId": "1050111163086979", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-08-24T00:00:00Z", "endDate": "2025-08-30T00:00:00Z", "submitByDate": "2025-09-03T00:00:00Z", "checkDate": "2025-09-05T00:00:00Z", "checkCount": 0, "id": "d7d70c49-f0fa-40a2-a9f4-9b8793d15c5d", "companyId": "14052843", "type": "payperiod", "_rid": "NmJkAKiCbEdEJQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdEJQEAAAAAAA==/", "_etag": "\"9d00accc-0000-0100-0000-686ff8fc0000\"", "_attachments": "attachments/", "_ts": 1752168700}, {"payPeriodId": "1050111408371848", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-08-31T00:00:00Z", "endDate": "2025-09-06T00:00:00Z", "submitByDate": "2025-09-10T00:00:00Z", "checkDate": "2025-09-12T00:00:00Z", "checkCount": 0, "id": "e0c34462-7d7d-47de-845c-14cbaefde629", "companyId": "14052843", "type": "payperiod", "_rid": "NmJkAKiCbEdFJQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdFJQEAAAAAAA==/", "_etag": "\"9d00afcc-0000-0100-0000-686ff8fd0000\"", "_attachments": "attachments/", "_ts": 1752168701}, {"payPeriodId": "1050111650719808", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-09-07T00:00:00Z", "endDate": "2025-09-13T00:00:00Z", "submitByDate": "2025-09-17T00:00:00Z", "checkDate": "2025-09-19T00:00:00Z", "checkCount": 0, "id": "d3cdb6de-8c16-4d7a-ad90-3f295843ce33", "companyId": "14052843", "type": "payperiod", "_rid": "NmJkAKiCbEdGJQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdGJQEAAAAAAA==/", "_etag": "\"9d00b3cc-0000-0100-0000-686ff8fd0000\"", "_attachments": "attachments/", "_ts": 1752168701}, {"payPeriodId": "1050111910226474", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-09-14T00:00:00Z", "endDate": "2025-09-20T00:00:00Z", "submitByDate": "2025-09-24T00:00:00Z", "checkDate": "2025-09-26T00:00:00Z", "checkCount": 0, "id": "7af3f3d8-e8eb-4f31-b0e2-bf6b96a5b608", "companyId": "14052843", "type": "payperiod", "_rid": "NmJkAKiCbEdHJQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdHJQEAAAAAAA==/", "_etag": "\"9d00b9cc-0000-0100-0000-686ff8fd0000\"", "_attachments": "attachments/", "_ts": 1752168701}, {"payPeriodId": "1050112149669064", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-09-21T00:00:00Z", "endDate": "2025-09-27T00:00:00Z", "submitByDate": "2025-10-01T00:00:00Z", "checkDate": "2025-10-03T00:00:00Z", "checkCount": 0, "id": "8beb7d0b-335a-4dc1-8e46-4d140dea8714", "companyId": "14052843", "type": "payperiod", "_rid": "NmJkAKiCbEdIJQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdIJQEAAAAAAA==/", "_etag": "\"9d00bfcc-0000-0100-0000-686ff8fd0000\"", "_attachments": "attachments/", "_ts": 1752168701}, {"payPeriodId": "1050112462890282", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-09-28T00:00:00Z", "endDate": "2025-10-04T00:00:00Z", "submitByDate": "2025-10-08T00:00:00Z", "checkDate": "2025-10-10T00:00:00Z", "checkCount": 0, "id": "eae88431-6ca7-48c1-98e6-eff76c08f84f", "companyId": "14052843", "type": "payperiod", "_rid": "NmJkAKiCbEdJJQEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdJJQEAAAAAAA==/", "_etag": "\"9d00c2cc-0000-0100-0000-686ff8fd0000\"", "_attachments": "attachments/", "_ts": 1752168701}], "links": [{"rel": "self", "href": "https://ca-payroll-ai-mock-sb-001-secure.nicebeach-8a7a52ab.eastus.azurecontainerapps.io/ca/companies/14052843/payperiods"}]}, "status_code": 200}