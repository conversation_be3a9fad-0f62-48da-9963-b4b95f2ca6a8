{"success": true, "company_id": "0020PT20", "data": {"metadata": {"contentItemCount": 42}, "content": [{"payPeriodId": "1140034998204961", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-01-01T00:00:00Z", "endDate": "2025-01-15T00:00:00Z", "submitByDate": "2025-01-13T00:00:00Z", "checkDate": "2025-01-15T00:00:00Z", "checkCount": 4, "id": "3536769b-1790-4003-a84b-a13768ee574b", "companyId": "0020PT20", "type": "payperiod", "_rid": "NmJkAKiCbEd9uAIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEd9uAIAAAAAAA==/", "_etag": "\"a300d9c6-0000-0100-0000-68701c970000\"", "_attachments": "attachments/", "_ts": 1752177815}, {"payPeriodId": "1140035526218332", "status": "COMPLETED", "description": "missing pay", "startDate": "2025-01-01T00:00:00Z", "endDate": "2025-01-15T00:00:00Z", "submitByDate": "2025-01-15T00:00:00Z", "checkDate": "2025-01-16T00:00:00Z", "checkCount": 1, "id": "aa433db6-21a5-4adb-91fc-250134c741f8", "companyId": "0020PT20", "type": "payperiod", "_rid": "NmJkAKiCbEd+uAIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEd+uAIAAAAAAA==/", "_etag": "\"a300ddc6-0000-0100-0000-68701c970000\"", "_attachments": "attachments/", "_ts": 1752177815}, {"payPeriodId": "1140035117053200", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-01-16T00:00:00Z", "endDate": "2025-01-31T00:00:00Z", "submitByDate": "2025-01-29T00:00:00Z", "checkDate": "2025-01-31T00:00:00Z", "checkCount": 4, "id": "bddc3b40-6116-4b87-9c11-8a46073b9802", "companyId": "0020PT20", "type": "payperiod", "_rid": "NmJkAKiCbEd-uAIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEd-uAIAAAAAAA==/", "_etag": "\"a300dec6-0000-0100-0000-68701c970000\"", "_attachments": "attachments/", "_ts": 1752177815}, {"payPeriodId": "1140035117053201", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-02-01T00:00:00Z", "endDate": "2025-02-15T00:00:00Z", "submitByDate": "2025-02-12T00:00:00Z", "checkDate": "2025-02-14T00:00:00Z", "checkCount": 4, "id": "cea35c41-33a0-4b63-bafb-25e7d8bfecf1", "companyId": "0020PT20", "type": "payperiod", "_rid": "NmJkAKiCbEeAuAIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeAuAIAAAAAAA==/", "_etag": "\"a300e0c6-0000-0100-0000-68701c970000\"", "_attachments": "attachments/", "_ts": 1752177815}, {"payPeriodId": "1140035266113329", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-02-16T00:00:00Z", "endDate": "2025-02-28T00:00:00Z", "submitByDate": "2025-02-26T00:00:00Z", "checkDate": "2025-02-28T00:00:00Z", "checkCount": 4, "id": "5def24e3-a92d-448e-9cdd-79d72dc7d758", "companyId": "0020PT20", "type": "payperiod", "_rid": "NmJkAKiCbEeBuAIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeBuAIAAAAAAA==/", "_etag": "\"a300e7c6-0000-0100-0000-68701c970000\"", "_attachments": "attachments/", "_ts": 1752177815}, {"payPeriodId": "1140035266113330", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-03-01T00:00:00Z", "endDate": "2025-03-15T00:00:00Z", "submitByDate": "2025-03-12T00:00:00Z", "checkDate": "2025-03-14T00:00:00Z", "checkCount": 4, "id": "5a57268f-4b52-48ad-af8f-f7ad6de4012c", "companyId": "0020PT20", "type": "payperiod", "_rid": "NmJkAKiCbEeCuAIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeCuAIAAAAAAA==/", "_etag": "\"a300e8c6-0000-0100-0000-68701c970000\"", "_attachments": "attachments/", "_ts": 1752177815}, {"payPeriodId": "1140035414574165", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-03-16T00:00:00Z", "endDate": "2025-03-31T00:00:00Z", "submitByDate": "2025-03-27T00:00:00Z", "checkDate": "2025-03-31T00:00:00Z", "checkCount": 4, "id": "81719207-f4df-4773-85f0-6187cb5fd510", "companyId": "0020PT20", "type": "payperiod", "_rid": "NmJkAKiCbEeDuAIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeDuAIAAAAAAA==/", "_etag": "\"a300e9c6-0000-0100-0000-68701c970000\"", "_attachments": "attachments/", "_ts": 1752177815}, {"payPeriodId": "1140035414574166", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-04-01T00:00:00Z", "endDate": "2025-04-15T00:00:00Z", "submitByDate": "2025-04-11T00:00:00Z", "checkDate": "2025-04-15T00:00:00Z", "checkCount": 0, "id": "84b804cf-1ade-47aa-994f-aa8ff8b73bcd", "companyId": "0020PT20", "type": "payperiod", "_rid": "NmJkAKiCbEeEuAIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeEuAIAAAAAAA==/", "_etag": "\"a300ebc6-0000-0100-0000-68701c970000\"", "_attachments": "attachments/", "_ts": 1752177815}, {"payPeriodId": "1140035533918007", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-04-16T00:00:00Z", "endDate": "2025-04-30T00:00:00Z", "submitByDate": "2025-04-28T00:00:00Z", "checkDate": "2025-04-30T00:00:00Z", "checkCount": 0, "id": "4ed94dc5-a3ca-4da4-8474-73403c5704bb", "companyId": "0020PT20", "type": "payperiod", "_rid": "NmJkAKiCbEeFuAIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeFuAIAAAAAAA==/", "_etag": "\"a300edc6-0000-0100-0000-68701c970000\"", "_attachments": "attachments/", "_ts": 1752177815}, {"payPeriodId": "1140035533918008", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-05-01T00:00:00Z", "endDate": "2025-05-15T00:00:00Z", "submitByDate": "2025-05-13T00:00:00Z", "checkDate": "2025-05-15T00:00:00Z", "checkCount": 0, "id": "8e56f677-59c6-4b59-bf29-5e797a6a661d", "companyId": "0020PT20", "type": "payperiod", "_rid": "NmJkAKiCbEeGuAIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeGuAIAAAAAAA==/", "_etag": "\"a300f1c6-0000-0100-0000-68701c970000\"", "_attachments": "attachments/", "_ts": 1752177815}, {"payPeriodId": "1140036035674734", "status": "INITIAL", "description": "error on p/r", "startDate": "2025-05-01T00:00:00Z", "endDate": "2025-05-15T00:00:00Z", "submitByDate": "2025-05-19T00:00:00Z", "checkDate": "2025-05-20T00:00:00Z", "checkCount": 0, "id": "5c2fc9e1-364b-4121-a261-c0a780cb1c5d", "companyId": "0020PT20", "type": "payperiod", "_rid": "NmJkAKiCbEeHuAIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeHuAIAAAAAAA==/", "_etag": "\"a300f2c6-0000-0100-0000-68701c970000\"", "_attachments": "attachments/", "_ts": 1752177815}, {"payPeriodId": "1140035670401371", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-05-16T00:00:00Z", "endDate": "2025-05-31T00:00:00Z", "submitByDate": "2025-05-28T00:00:00Z", "checkDate": "2025-05-30T00:00:00Z", "checkCount": 0, "id": "a0a65f39-9252-490f-a259-6a6b51fdbf92", "companyId": "0020PT20", "type": "payperiod", "_rid": "NmJkAKiCbEeIuAIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeIuAIAAAAAAA==/", "_etag": "\"a300f6c6-0000-0100-0000-68701c970000\"", "_attachments": "attachments/", "_ts": 1752177815}, {"payPeriodId": "1140035670401372", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-06-01T00:00:00Z", "endDate": "2025-06-15T00:00:00Z", "submitByDate": "2025-06-11T00:00:00Z", "checkDate": "2025-06-13T00:00:00Z", "checkCount": 0, "id": "c70882d7-3d28-498a-91fb-1d5781c88a5c", "companyId": "0020PT20", "type": "payperiod", "_rid": "NmJkAKiCbEeJuAIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeJuAIAAAAAAA==/", "_etag": "\"a300f9c6-0000-0100-0000-68701c980000\"", "_attachments": "attachments/", "_ts": 1752177816}, {"payPeriodId": "1140035784028702", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-06-16T00:00:00Z", "endDate": "2025-06-30T00:00:00Z", "submitByDate": "2025-06-26T00:00:00Z", "checkDate": "2025-06-30T00:00:00Z", "checkCount": 0, "id": "5bb33792-1ca1-44a5-b51c-7eb625ac2c7d", "companyId": "0020PT20", "type": "payperiod", "_rid": "NmJkAKiCbEeKuAIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeKuAIAAAAAAA==/", "_etag": "\"a300fbc6-0000-0100-0000-68701c980000\"", "_attachments": "attachments/", "_ts": 1752177816}, {"payPeriodId": "1140035784028703", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-07-01T00:00:00Z", "endDate": "2025-07-15T00:00:00Z", "submitByDate": "2025-07-11T00:00:00Z", "checkDate": "2025-07-15T00:00:00Z", "checkCount": 0, "id": "b6fbe4f4-4056-422d-be5d-75784f2a3daf", "companyId": "0020PT20", "type": "payperiod", "_rid": "NmJkAKiCbEeLuAIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeLuAIAAAAAAA==/", "_etag": "\"a30003c7-0000-0100-0000-68701c980000\"", "_attachments": "attachments/", "_ts": 1752177816}, {"payPeriodId": "1140035905102754", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-07-16T00:00:00Z", "endDate": "2025-07-31T00:00:00Z", "submitByDate": "2025-07-29T00:00:00Z", "checkDate": "2025-07-31T00:00:00Z", "checkCount": 0, "id": "18244862-e803-45d5-868a-ae310a378fff", "companyId": "0020PT20", "type": "payperiod", "_rid": "NmJkAKiCbEeMuAIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeMuAIAAAAAAA==/", "_etag": "\"a30006c7-0000-0100-0000-68701c980000\"", "_attachments": "attachments/", "_ts": 1752177816}, {"payPeriodId": "1140035905102755", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-08-01T00:00:00Z", "endDate": "2025-08-15T00:00:00Z", "submitByDate": "2025-08-13T00:00:00Z", "checkDate": "2025-08-15T00:00:00Z", "checkCount": 0, "id": "d4988bf7-9c19-4d47-a159-a3f28eead5d6", "companyId": "0020PT20", "type": "payperiod", "_rid": "NmJkAKiCbEeNuAIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeNuAIAAAAAAA==/", "_etag": "\"a3000ac7-0000-0100-0000-68701c980000\"", "_attachments": "attachments/", "_ts": 1752177816}, {"payPeriodId": "1140036029394695", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-08-16T00:00:00Z", "endDate": "2025-08-31T00:00:00Z", "submitByDate": "2025-08-27T00:00:00Z", "checkDate": "2025-08-29T00:00:00Z", "checkCount": 0, "id": "9b59a160-8e19-4865-a80b-4b6b858c533a", "companyId": "0020PT20", "type": "payperiod", "_rid": "NmJkAKiCbEeOuAIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeOuAIAAAAAAA==/", "_etag": "\"a3000ec7-0000-0100-0000-68701c980000\"", "_attachments": "attachments/", "_ts": 1752177816}, {"payPeriodId": "1140036029394696", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-09-01T00:00:00Z", "endDate": "2025-09-15T00:00:00Z", "submitByDate": "2025-09-11T00:00:00Z", "checkDate": "2025-09-15T00:00:00Z", "checkCount": 0, "id": "959af992-bd98-4526-9b16-b558402007e1", "companyId": "0020PT20", "type": "payperiod", "_rid": "NmJkAKiCbEePuAIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEePuAIAAAAAAA==/", "_etag": "\"a30011c7-0000-0100-0000-68701c980000\"", "_attachments": "attachments/", "_ts": 1752177816}, {"payPeriodId": "1140036170002809", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-09-16T00:00:00Z", "endDate": "2025-09-30T00:00:00Z", "submitByDate": "2025-09-26T00:00:00Z", "checkDate": "2025-09-30T00:00:00Z", "checkCount": 0, "id": "7a7a89f4-0ca1-4925-860c-94538191dfa0", "companyId": "0020PT20", "type": "payperiod", "_rid": "NmJkAKiCbEeQuAIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeQuAIAAAAAAA==/", "_etag": "\"a30013c7-0000-0100-0000-68701c980000\"", "_attachments": "attachments/", "_ts": 1752177816}, {"payPeriodId": "1140036170002810", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-10-01T00:00:00Z", "endDate": "2025-10-15T00:00:00Z", "submitByDate": "2025-10-13T00:00:00Z", "checkDate": "2025-10-15T00:00:00Z", "checkCount": 0, "id": "ce8d1e47-0538-4c76-b6e7-d7ffe3db6dae", "companyId": "0020PT20", "type": "payperiod", "_rid": "NmJkAKiCbEeRuAIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeRuAIAAAAAAA==/", "_etag": "\"a30016c7-0000-0100-0000-68701c980000\"", "_attachments": "attachments/", "_ts": 1752177816}, {"payPeriodId": "1140034998204961", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-01-01T00:00:00Z", "endDate": "2025-01-15T00:00:00Z", "submitByDate": "2025-01-13T00:00:00Z", "checkDate": "2025-01-15T00:00:00Z", "checkCount": 4, "id": "881461ea-7e52-4d74-8a72-614533dea609", "companyId": "0020PT20", "type": "payperiod", "_rid": "NmJkAKiCbEebuAIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEebuAIAAAAAAA==/", "_etag": "\"a30037c7-0000-0100-0000-68701c990000\"", "_attachments": "attachments/", "_ts": 1752177817}, {"payPeriodId": "1140035526218332", "status": "COMPLETED", "description": "missing pay", "startDate": "2025-01-01T00:00:00Z", "endDate": "2025-01-15T00:00:00Z", "submitByDate": "2025-01-15T00:00:00Z", "checkDate": "2025-01-16T00:00:00Z", "checkCount": 1, "id": "94c44bfa-d191-4e4b-bcfc-fb3911809500", "companyId": "0020PT20", "type": "payperiod", "_rid": "NmJkAKiCbEecuAIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEecuAIAAAAAAA==/", "_etag": "\"a3003cc7-0000-0100-0000-68701c990000\"", "_attachments": "attachments/", "_ts": 1752177817}, {"payPeriodId": "1140035117053200", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-01-16T00:00:00Z", "endDate": "2025-01-31T00:00:00Z", "submitByDate": "2025-01-29T00:00:00Z", "checkDate": "2025-01-31T00:00:00Z", "checkCount": 4, "id": "55a23491-e61f-4fe1-beaf-116c9b9aa293", "companyId": "0020PT20", "type": "payperiod", "_rid": "NmJkAKiCbEeduAIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeduAIAAAAAAA==/", "_etag": "\"a30041c7-0000-0100-0000-68701c990000\"", "_attachments": "attachments/", "_ts": 1752177817}, {"payPeriodId": "1140035117053201", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-02-01T00:00:00Z", "endDate": "2025-02-15T00:00:00Z", "submitByDate": "2025-02-12T00:00:00Z", "checkDate": "2025-02-14T00:00:00Z", "checkCount": 4, "id": "18dbde4c-2499-4ae1-8d99-2e3d1a2eb963", "companyId": "0020PT20", "type": "payperiod", "_rid": "NmJkAKiCbEeeuAIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeeuAIAAAAAAA==/", "_etag": "\"a30043c7-0000-0100-0000-68701c990000\"", "_attachments": "attachments/", "_ts": 1752177817}, {"payPeriodId": "1140035266113329", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-02-16T00:00:00Z", "endDate": "2025-02-28T00:00:00Z", "submitByDate": "2025-02-26T00:00:00Z", "checkDate": "2025-02-28T00:00:00Z", "checkCount": 4, "id": "501cf739-8456-47f6-8eec-b8df74049842", "companyId": "0020PT20", "type": "payperiod", "_rid": "NmJkAKiCbEefuAIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEefuAIAAAAAAA==/", "_etag": "\"a30046c7-0000-0100-0000-68701c990000\"", "_attachments": "attachments/", "_ts": 1752177817}, {"payPeriodId": "1140035266113330", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-03-01T00:00:00Z", "endDate": "2025-03-15T00:00:00Z", "submitByDate": "2025-03-12T00:00:00Z", "checkDate": "2025-03-14T00:00:00Z", "checkCount": 4, "id": "fdd1c924-ddb3-45d5-a0c9-681862fe2950", "companyId": "0020PT20", "type": "payperiod", "_rid": "NmJkAKiCbEeguAIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeguAIAAAAAAA==/", "_etag": "\"a30048c7-0000-0100-0000-68701c990000\"", "_attachments": "attachments/", "_ts": 1752177817}, {"payPeriodId": "1140035414574165", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-03-16T00:00:00Z", "endDate": "2025-03-31T00:00:00Z", "submitByDate": "2025-03-27T00:00:00Z", "checkDate": "2025-03-31T00:00:00Z", "checkCount": 4, "id": "ca33a2cd-1b7c-4dd4-8075-8495a59150b8", "companyId": "0020PT20", "type": "payperiod", "_rid": "NmJkAKiCbEehuAIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEehuAIAAAAAAA==/", "_etag": "\"a3004ac7-0000-0100-0000-68701c990000\"", "_attachments": "attachments/", "_ts": 1752177817}, {"payPeriodId": "1140035414574166", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-04-01T00:00:00Z", "endDate": "2025-04-15T00:00:00Z", "submitByDate": "2025-04-11T00:00:00Z", "checkDate": "2025-04-15T00:00:00Z", "checkCount": 4, "id": "15e18c1e-7cda-46aa-9eab-869e5464f8b3", "companyId": "0020PT20", "type": "payperiod", "_rid": "NmJkAKiCbEeiuAIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeiuAIAAAAAAA==/", "_etag": "\"a3004cc7-0000-0100-0000-68701c990000\"", "_attachments": "attachments/", "_ts": 1752177817}, {"payPeriodId": "1140035533918007", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-04-16T00:00:00Z", "endDate": "2025-04-30T00:00:00Z", "submitByDate": "2025-04-28T00:00:00Z", "checkDate": "2025-04-30T00:00:00Z", "checkCount": 4, "id": "fe12adb7-4797-49ae-bcbe-0d6c535c2b5b", "companyId": "0020PT20", "type": "payperiod", "_rid": "NmJkAKiCbEejuAIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEejuAIAAAAAAA==/", "_etag": "\"a30050c7-0000-0100-0000-68701c9a0000\"", "_attachments": "attachments/", "_ts": 1752177818}, {"payPeriodId": "1140035533918008", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-05-01T00:00:00Z", "endDate": "2025-05-15T00:00:00Z", "submitByDate": "2025-05-13T00:00:00Z", "checkDate": "2025-05-15T00:00:00Z", "checkCount": 4, "id": "edc64c8c-bdd0-49e4-86e4-b70d92a4c79e", "companyId": "0020PT20", "type": "payperiod", "_rid": "NmJkAKiCbEekuAIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEekuAIAAAAAAA==/", "_etag": "\"a30054c7-0000-0100-0000-68701c9a0000\"", "_attachments": "attachments/", "_ts": 1752177818}, {"payPeriodId": "1140036035674734", "status": "COMPLETED", "description": "error on p/r", "startDate": "2025-05-01T00:00:00Z", "endDate": "2025-05-15T00:00:00Z", "submitByDate": "2025-05-19T00:00:00Z", "checkDate": "2025-05-20T00:00:00Z", "checkCount": 1, "id": "5470a275-fe18-43e3-903a-e7f2b8518366", "companyId": "0020PT20", "type": "payperiod", "_rid": "NmJkAKiCbEeluAIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeluAIAAAAAAA==/", "_etag": "\"a30056c7-0000-0100-0000-68701c9a0000\"", "_attachments": "attachments/", "_ts": 1752177818}, {"payPeriodId": "1140035670401371", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-05-16T00:00:00Z", "endDate": "2025-05-31T00:00:00Z", "submitByDate": "2025-05-28T00:00:00Z", "checkDate": "2025-05-30T00:00:00Z", "checkCount": 4, "id": "11538cdb-4036-4d2a-a206-e56e632854ae", "companyId": "0020PT20", "type": "payperiod", "_rid": "NmJkAKiCbEemuAIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEemuAIAAAAAAA==/", "_etag": "\"a30057c7-0000-0100-0000-68701c9a0000\"", "_attachments": "attachments/", "_ts": 1752177818}, {"payPeriodId": "1140035670401372", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-06-01T00:00:00Z", "endDate": "2025-06-15T00:00:00Z", "submitByDate": "2025-06-11T00:00:00Z", "checkDate": "2025-06-13T00:00:00Z", "checkCount": 4, "id": "f689791a-beb8-425e-a12b-290eb442e366", "companyId": "0020PT20", "type": "payperiod", "_rid": "NmJkAKiCbEenuAIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEenuAIAAAAAAA==/", "_etag": "\"a3005bc7-0000-0100-0000-68701c9a0000\"", "_attachments": "attachments/", "_ts": 1752177818}, {"payPeriodId": "1140035784028702", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-06-16T00:00:00Z", "endDate": "2025-06-30T00:00:00Z", "submitByDate": "2025-06-26T00:00:00Z", "checkDate": "2025-06-30T00:00:00Z", "checkCount": 4, "id": "c7902646-e245-4089-9fbc-f6059a3aa0da", "companyId": "0020PT20", "type": "payperiod", "_rid": "NmJkAKiCbEeouAIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeouAIAAAAAAA==/", "_etag": "\"a3005ec7-0000-0100-0000-68701c9a0000\"", "_attachments": "attachments/", "_ts": 1752177818}, {"payPeriodId": "1140035784028703", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-07-01T00:00:00Z", "endDate": "2025-07-15T00:00:00Z", "submitByDate": "2025-07-11T00:00:00Z", "checkDate": "2025-07-15T00:00:00Z", "checkCount": 0, "id": "d2d3588b-4bf5-48f6-a17c-cf4f7aa4ebb5", "companyId": "0020PT20", "type": "payperiod", "_rid": "NmJkAKiCbEepuAIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEepuAIAAAAAAA==/", "_etag": "\"a30063c7-0000-0100-0000-68701c9a0000\"", "_attachments": "attachments/", "_ts": 1752177818}, {"payPeriodId": "1140035905102754", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-07-16T00:00:00Z", "endDate": "2025-07-31T00:00:00Z", "submitByDate": "2025-07-29T00:00:00Z", "checkDate": "2025-07-31T00:00:00Z", "checkCount": 0, "id": "9edfa3b1-5c6c-42a0-a6d3-f43346804a07", "companyId": "0020PT20", "type": "payperiod", "_rid": "NmJkAKiCbEequAIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEequAIAAAAAAA==/", "_etag": "\"a30066c7-0000-0100-0000-68701c9a0000\"", "_attachments": "attachments/", "_ts": 1752177818}, {"payPeriodId": "1140035905102755", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-08-01T00:00:00Z", "endDate": "2025-08-15T00:00:00Z", "submitByDate": "2025-08-13T00:00:00Z", "checkDate": "2025-08-15T00:00:00Z", "checkCount": 0, "id": "adad733c-cbf0-4ea1-a882-3e1c37b36feb", "companyId": "0020PT20", "type": "payperiod", "_rid": "NmJkAKiCbEeruAIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeruAIAAAAAAA==/", "_etag": "\"a30069c7-0000-0100-0000-68701c9a0000\"", "_attachments": "attachments/", "_ts": 1752177818}, {"payPeriodId": "1140036029394695", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-08-16T00:00:00Z", "endDate": "2025-08-31T00:00:00Z", "submitByDate": "2025-08-27T00:00:00Z", "checkDate": "2025-08-29T00:00:00Z", "checkCount": 0, "id": "4ed8c6c9-ff9a-4a5d-8c57-e9480d5dc429", "companyId": "0020PT20", "type": "payperiod", "_rid": "NmJkAKiCbEesuAIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEesuAIAAAAAAA==/", "_etag": "\"a3006bc7-0000-0100-0000-68701c9a0000\"", "_attachments": "attachments/", "_ts": 1752177818}, {"payPeriodId": "1140036029394696", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-09-01T00:00:00Z", "endDate": "2025-09-15T00:00:00Z", "submitByDate": "2025-09-11T00:00:00Z", "checkDate": "2025-09-15T00:00:00Z", "checkCount": 0, "id": "b6d76942-b9e7-4420-aa0d-93c195c761ad", "companyId": "0020PT20", "type": "payperiod", "_rid": "NmJkAKiCbEetuAIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEetuAIAAAAAAA==/", "_etag": "\"a3006ec7-0000-0100-0000-68701c9a0000\"", "_attachments": "attachments/", "_ts": 1752177818}, {"payPeriodId": "1140036170002809", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-09-16T00:00:00Z", "endDate": "2025-09-30T00:00:00Z", "submitByDate": "2025-09-26T00:00:00Z", "checkDate": "2025-09-30T00:00:00Z", "checkCount": 0, "id": "2912d012-258b-4f32-9e6b-68482d8f77f4", "companyId": "0020PT20", "type": "payperiod", "_rid": "NmJkAKiCbEeuuAIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeuuAIAAAAAAA==/", "_etag": "\"a30071c7-0000-0100-0000-68701c9a0000\"", "_attachments": "attachments/", "_ts": 1752177818}, {"payPeriodId": "1140036170002810", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-10-01T00:00:00Z", "endDate": "2025-10-15T00:00:00Z", "submitByDate": "2025-10-13T00:00:00Z", "checkDate": "2025-10-15T00:00:00Z", "checkCount": 0, "id": "90da4a5a-e950-4508-9abd-d74005653c1f", "companyId": "0020PT20", "type": "payperiod", "_rid": "NmJkAKiCbEevuAIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEevuAIAAAAAAA==/", "_etag": "\"a30072c7-0000-0100-0000-68701c9a0000\"", "_attachments": "attachments/", "_ts": 1752177818}], "links": [{"rel": "self", "href": "https://ca-payroll-ai-mock-sb-001-secure.nicebeach-8a7a52ab.eastus.azurecontainerapps.io/ca/companies/0020PT20/payperiods"}]}, "status_code": 200}