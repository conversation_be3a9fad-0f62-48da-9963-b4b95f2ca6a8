{"success": true, "company_id": "17185521", "data": {"metadata": {"contentItemCount": 40}, "content": [{"payPeriodId": "1080039085603017", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-01-01T00:00:00Z", "endDate": "2025-01-15T00:00:00Z", "submitByDate": "2025-01-15T00:00:00Z", "checkDate": "2025-01-16T00:00:00Z", "checkCount": 5, "id": "153966c4-b622-40c7-b22c-7d2d146dc7d5", "companyId": "17185521", "type": "payperiod", "_rid": "NmJkAKiCbEc7QgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc7QgMAAAAAAA==/", "_etag": "\"a500dd84-0000-0100-0000-687027bb0000\"", "_attachments": "attachments/", "_ts": 1752180667}, {"payPeriodId": "1080039085603018", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-01-16T00:00:00Z", "endDate": "2025-01-31T00:00:00Z", "submitByDate": "2025-02-02T00:00:00Z", "checkDate": "2025-02-03T00:00:00Z", "checkCount": 5, "id": "c8d3f0c0-d877-41a5-8ba9-5a2df25f7883", "companyId": "17185521", "type": "payperiod", "_rid": "NmJkAKiCbEc8QgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc8QgMAAAAAAA==/", "_etag": "\"a500df84-0000-0100-0000-687027bb0000\"", "_attachments": "attachments/", "_ts": 1752180667}, {"payPeriodId": "1080039322567056", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-02-01T00:00:00Z", "endDate": "2025-02-15T00:00:00Z", "submitByDate": "2025-02-13T00:00:00Z", "checkDate": "2025-02-14T00:00:00Z", "checkCount": 5, "id": "c3d6dd75-7bc6-43e1-9f54-944dcaaef7fa", "companyId": "17185521", "type": "payperiod", "_rid": "NmJkAKiCbEc9QgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc9QgMAAAAAAA==/", "_etag": "\"a500e084-0000-0100-0000-687027bb0000\"", "_attachments": "attachments/", "_ts": 1752180667}, {"payPeriodId": "1080039322567057", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-02-16T00:00:00Z", "endDate": "2025-02-28T00:00:00Z", "submitByDate": "2025-02-26T00:00:00Z", "checkDate": "2025-02-28T00:00:00Z", "checkCount": 5, "id": "c135c9b8-9192-4f14-a4d0-bf8e80752fa4", "companyId": "17185521", "type": "payperiod", "_rid": "NmJkAKiCbEc+QgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc+QgMAAAAAAA==/", "_etag": "\"a500e384-0000-0100-0000-687027bb0000\"", "_attachments": "attachments/", "_ts": 1752180667}, {"payPeriodId": "1080039443219602", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-03-01T00:00:00Z", "endDate": "2025-03-15T00:00:00Z", "submitByDate": "2025-03-13T00:00:00Z", "checkDate": "2025-03-14T00:00:00Z", "checkCount": 5, "id": "037ff3f6-d835-4508-889d-977b484b21dc", "companyId": "17185521", "type": "payperiod", "_rid": "NmJkAKiCbEc-QgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc-QgMAAAAAAA==/", "_etag": "\"a500e884-0000-0100-0000-687027bb0000\"", "_attachments": "attachments/", "_ts": 1752180667}, {"payPeriodId": "1080039443219603", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-03-16T00:00:00Z", "endDate": "2025-03-31T00:00:00Z", "submitByDate": "2025-03-30T00:00:00Z", "checkDate": "2025-03-31T00:00:00Z", "checkCount": 5, "id": "bfdec249-2583-4e71-b158-797d775567b4", "companyId": "17185521", "type": "payperiod", "_rid": "NmJkAKiCbEdAQgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdAQgMAAAAAAA==/", "_etag": "\"a500ea84-0000-0100-0000-687027bb0000\"", "_attachments": "attachments/", "_ts": 1752180667}, {"payPeriodId": "1080039711756801", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-04-01T00:00:00Z", "endDate": "2025-04-15T00:00:00Z", "submitByDate": "2025-04-15T00:00:00Z", "checkDate": "2025-04-17T00:00:00Z", "checkCount": 0, "id": "cd57dd8e-a5c2-4b77-99c9-d8eae4c6f422", "companyId": "17185521", "type": "payperiod", "_rid": "NmJkAKiCbEdBQgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdBQgMAAAAAAA==/", "_etag": "\"a500ed84-0000-0100-0000-687027bb0000\"", "_attachments": "attachments/", "_ts": 1752180667}, {"payPeriodId": "1080039711756802", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-04-16T00:00:00Z", "endDate": "2025-04-30T00:00:00Z", "submitByDate": "2025-04-29T00:00:00Z", "checkDate": "2025-05-01T00:00:00Z", "checkCount": 0, "id": "dedd7209-39ac-43a9-9234-2c4afcfb4b09", "companyId": "17185521", "type": "payperiod", "_rid": "NmJkAKiCbEdCQgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdCQgMAAAAAAA==/", "_etag": "\"a500ef84-0000-0100-0000-687027bb0000\"", "_attachments": "attachments/", "_ts": 1752180667}, {"payPeriodId": "1080039896479174", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-05-01T00:00:00Z", "endDate": "2025-05-15T00:00:00Z", "submitByDate": "2025-05-14T00:00:00Z", "checkDate": "2025-05-16T00:00:00Z", "checkCount": 0, "id": "2c63b214-4b60-43f0-aa59-480b753697a9", "companyId": "17185521", "type": "payperiod", "_rid": "NmJkAKiCbEdDQgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdDQgMAAAAAAA==/", "_etag": "\"a500f084-0000-0100-0000-687027bb0000\"", "_attachments": "attachments/", "_ts": 1752180667}, {"payPeriodId": "1080039896479175", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-05-16T00:00:00Z", "endDate": "2025-05-31T00:00:00Z", "submitByDate": "2025-05-28T00:00:00Z", "checkDate": "2025-05-30T00:00:00Z", "checkCount": 0, "id": "75b775b0-0632-40de-9022-18beab3910c8", "companyId": "17185521", "type": "payperiod", "_rid": "NmJkAKiCbEdEQgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdEQgMAAAAAAA==/", "_etag": "\"a500f284-0000-0100-0000-687027bc0000\"", "_attachments": "attachments/", "_ts": 1752180668}, {"payPeriodId": "1080040128389591", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-06-01T00:00:00Z", "endDate": "2025-06-15T00:00:00Z", "submitByDate": "2025-06-13T00:00:00Z", "checkDate": "2025-06-17T00:00:00Z", "checkCount": 0, "id": "bfcc3575-ecff-45db-a865-94d7901aea75", "companyId": "17185521", "type": "payperiod", "_rid": "NmJkAKiCbEdFQgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdFQgMAAAAAAA==/", "_etag": "\"a500f684-0000-0100-0000-687027bc0000\"", "_attachments": "attachments/", "_ts": 1752180668}, {"payPeriodId": "1080040128389592", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-06-16T00:00:00Z", "endDate": "2025-06-30T00:00:00Z", "submitByDate": "2025-06-27T00:00:00Z", "checkDate": "2025-07-01T00:00:00Z", "checkCount": 0, "id": "0c49a275-19b9-4985-86d8-49346145c379", "companyId": "17185521", "type": "payperiod", "_rid": "NmJkAKiCbEdGQgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdGQgMAAAAAAA==/", "_etag": "\"a500f984-0000-0100-0000-687027bc0000\"", "_attachments": "attachments/", "_ts": 1752180668}, {"payPeriodId": "1080040325399445", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-07-01T00:00:00Z", "endDate": "2025-07-15T00:00:00Z", "submitByDate": "2025-07-15T00:00:00Z", "checkDate": "2025-07-17T00:00:00Z", "checkCount": 0, "id": "68823fba-a108-40dc-a306-70d6755f007b", "companyId": "17185521", "type": "payperiod", "_rid": "NmJkAKiCbEdHQgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdHQgMAAAAAAA==/", "_etag": "\"a500fa84-0000-0100-0000-687027bc0000\"", "_attachments": "attachments/", "_ts": 1752180668}, {"payPeriodId": "1080040325399446", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-07-16T00:00:00Z", "endDate": "2025-07-31T00:00:00Z", "submitByDate": "2025-07-30T00:00:00Z", "checkDate": "2025-08-01T00:00:00Z", "checkCount": 0, "id": "eb342de3-2f3f-4feb-8c8a-06382b9b2602", "companyId": "17185521", "type": "payperiod", "_rid": "NmJkAKiCbEdIQgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdIQgMAAAAAAA==/", "_etag": "\"a500fd84-0000-0100-0000-687027bc0000\"", "_attachments": "attachments/", "_ts": 1752180668}, {"payPeriodId": "1080040539332138", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-08-01T00:00:00Z", "endDate": "2025-08-15T00:00:00Z", "submitByDate": "2025-08-13T00:00:00Z", "checkDate": "2025-08-15T00:00:00Z", "checkCount": 0, "id": "647cccd3-1309-4319-9e56-405399cd081f", "companyId": "17185521", "type": "payperiod", "_rid": "NmJkAKiCbEdJQgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdJQgMAAAAAAA==/", "_etag": "\"a5000085-0000-0100-0000-687027bc0000\"", "_attachments": "attachments/", "_ts": 1752180668}, {"payPeriodId": "1080040539332139", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-08-16T00:00:00Z", "endDate": "2025-08-31T00:00:00Z", "submitByDate": "2025-08-27T00:00:00Z", "checkDate": "2025-08-29T00:00:00Z", "checkCount": 0, "id": "115479be-6fdd-4964-88a0-1fc97a0b8a55", "companyId": "17185521", "type": "payperiod", "_rid": "NmJkAKiCbEdKQgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdKQgMAAAAAAA==/", "_etag": "\"a5000285-0000-0100-0000-687027bc0000\"", "_attachments": "attachments/", "_ts": 1752180668}, {"payPeriodId": "1080040773426801", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-09-01T00:00:00Z", "endDate": "2025-09-15T00:00:00Z", "submitByDate": "2025-09-15T00:00:00Z", "checkDate": "2025-09-17T00:00:00Z", "checkCount": 0, "id": "8a618fe6-dc7f-4451-bb25-a3f0621197fb", "companyId": "17185521", "type": "payperiod", "_rid": "NmJkAKiCbEdLQgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdLQgMAAAAAAA==/", "_etag": "\"a5000485-0000-0100-0000-687027bc0000\"", "_attachments": "attachments/", "_ts": 1752180668}, {"payPeriodId": "1080040773426802", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-09-16T00:00:00Z", "endDate": "2025-09-30T00:00:00Z", "submitByDate": "2025-09-29T00:00:00Z", "checkDate": "2025-10-01T00:00:00Z", "checkCount": 0, "id": "cdd0d6b0-a1d2-4dc3-879d-ba87ec62f1af", "companyId": "17185521", "type": "payperiod", "_rid": "NmJkAKiCbEdMQgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdMQgMAAAAAAA==/", "_etag": "\"a5000685-0000-0100-0000-687027bc0000\"", "_attachments": "attachments/", "_ts": 1752180668}, {"payPeriodId": "1080040962173971", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-10-01T00:00:00Z", "endDate": "2025-10-15T00:00:00Z", "submitByDate": "2025-10-15T00:00:00Z", "checkDate": "2025-10-17T00:00:00Z", "checkCount": 0, "id": "be99a5f3-1b73-42af-8d39-79767235c387", "companyId": "17185521", "type": "payperiod", "_rid": "NmJkAKiCbEdNQgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdNQgMAAAAAAA==/", "_etag": "\"a5000a85-0000-0100-0000-687027bc0000\"", "_attachments": "attachments/", "_ts": 1752180668}, {"payPeriodId": "1080040962173972", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-10-16T00:00:00Z", "endDate": "2025-10-31T00:00:00Z", "submitByDate": "2025-10-29T00:00:00Z", "checkDate": "2025-10-31T00:00:00Z", "checkCount": 0, "id": "14ce841e-8411-4116-a1b7-dd913c670193", "companyId": "17185521", "type": "payperiod", "_rid": "NmJkAKiCbEdOQgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdOQgMAAAAAAA==/", "_etag": "\"a5000c85-0000-0100-0000-687027bc0000\"", "_attachments": "attachments/", "_ts": 1752180668}, {"payPeriodId": "1080039085603017", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-01-01T00:00:00Z", "endDate": "2025-01-15T00:00:00Z", "submitByDate": "2025-01-15T00:00:00Z", "checkDate": "2025-01-16T00:00:00Z", "checkCount": 5, "id": "fdb92b0e-f7eb-4f52-a23b-b97fdbba94fe", "companyId": "17185521", "type": "payperiod", "_rid": "NmJkAKiCbEdfQgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdfQgMAAAAAAA==/", "_etag": "\"a5003e85-0000-0100-0000-687027be0000\"", "_attachments": "attachments/", "_ts": 1752180670}, {"payPeriodId": "1080039085603018", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-01-16T00:00:00Z", "endDate": "2025-01-31T00:00:00Z", "submitByDate": "2025-02-02T00:00:00Z", "checkDate": "2025-02-03T00:00:00Z", "checkCount": 5, "id": "e814854c-6699-435c-8157-8a444f59d3dc", "companyId": "17185521", "type": "payperiod", "_rid": "NmJkAKiCbEdgQgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdgQgMAAAAAAA==/", "_etag": "\"a5004185-0000-0100-0000-687027be0000\"", "_attachments": "attachments/", "_ts": 1752180670}, {"payPeriodId": "1080039322567056", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-02-01T00:00:00Z", "endDate": "2025-02-15T00:00:00Z", "submitByDate": "2025-02-13T00:00:00Z", "checkDate": "2025-02-14T00:00:00Z", "checkCount": 5, "id": "41ea695e-aac5-43cd-ae76-390926c9333b", "companyId": "17185521", "type": "payperiod", "_rid": "NmJkAKiCbEdhQgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdhQgMAAAAAAA==/", "_etag": "\"a5004485-0000-0100-0000-687027be0000\"", "_attachments": "attachments/", "_ts": 1752180670}, {"payPeriodId": "1080039322567057", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-02-16T00:00:00Z", "endDate": "2025-02-28T00:00:00Z", "submitByDate": "2025-02-26T00:00:00Z", "checkDate": "2025-02-28T00:00:00Z", "checkCount": 5, "id": "e1789eeb-0c15-47a8-b307-6252cfe569d0", "companyId": "17185521", "type": "payperiod", "_rid": "NmJkAKiCbEdiQgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdiQgMAAAAAAA==/", "_etag": "\"a5004985-0000-0100-0000-687027be0000\"", "_attachments": "attachments/", "_ts": 1752180670}, {"payPeriodId": "1080039443219602", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-03-01T00:00:00Z", "endDate": "2025-03-15T00:00:00Z", "submitByDate": "2025-03-13T00:00:00Z", "checkDate": "2025-03-14T00:00:00Z", "checkCount": 5, "id": "5f41c5f0-fd9d-4c1e-b090-14aa81066698", "companyId": "17185521", "type": "payperiod", "_rid": "NmJkAKiCbEdjQgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdjQgMAAAAAAA==/", "_etag": "\"a5004c85-0000-0100-0000-687027be0000\"", "_attachments": "attachments/", "_ts": 1752180670}, {"payPeriodId": "1080039443219603", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-03-16T00:00:00Z", "endDate": "2025-03-31T00:00:00Z", "submitByDate": "2025-03-30T00:00:00Z", "checkDate": "2025-03-31T00:00:00Z", "checkCount": 5, "id": "2c018aae-71ff-437a-a7a9-d8626981fbd7", "companyId": "17185521", "type": "payperiod", "_rid": "NmJkAKiCbEdkQgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdkQgMAAAAAAA==/", "_etag": "\"a5004f85-0000-0100-0000-687027be0000\"", "_attachments": "attachments/", "_ts": 1752180670}, {"payPeriodId": "1080039711756801", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-04-01T00:00:00Z", "endDate": "2025-04-15T00:00:00Z", "submitByDate": "2025-04-15T00:00:00Z", "checkDate": "2025-04-17T00:00:00Z", "checkCount": 5, "id": "53b6c45f-aabd-4db5-8aca-df4543917e2e", "companyId": "17185521", "type": "payperiod", "_rid": "NmJkAKiCbEdlQgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdlQgMAAAAAAA==/", "_etag": "\"a5005085-0000-0100-0000-687027be0000\"", "_attachments": "attachments/", "_ts": 1752180670}, {"payPeriodId": "1080039711756802", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-04-16T00:00:00Z", "endDate": "2025-04-30T00:00:00Z", "submitByDate": "2025-04-29T00:00:00Z", "checkDate": "2025-05-01T00:00:00Z", "checkCount": 5, "id": "6cfa00c3-42bd-44dd-a475-41f7a90d1b2f", "companyId": "17185521", "type": "payperiod", "_rid": "NmJkAKiCbEdmQgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdmQgMAAAAAAA==/", "_etag": "\"a5005485-0000-0100-0000-687027be0000\"", "_attachments": "attachments/", "_ts": 1752180670}, {"payPeriodId": "1080039896479174", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-05-01T00:00:00Z", "endDate": "2025-05-15T00:00:00Z", "submitByDate": "2025-05-14T00:00:00Z", "checkDate": "2025-05-16T00:00:00Z", "checkCount": 5, "id": "c985fcee-b3a0-4a0e-a582-d1121a28d965", "companyId": "17185521", "type": "payperiod", "_rid": "NmJkAKiCbEdnQgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdnQgMAAAAAAA==/", "_etag": "\"a5005a85-0000-0100-0000-687027be0000\"", "_attachments": "attachments/", "_ts": 1752180670}, {"payPeriodId": "1080039896479175", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-05-16T00:00:00Z", "endDate": "2025-05-31T00:00:00Z", "submitByDate": "2025-05-28T00:00:00Z", "checkDate": "2025-05-30T00:00:00Z", "checkCount": 5, "id": "f31cb1f2-c801-420c-bb95-f61ccd4c4ef4", "companyId": "17185521", "type": "payperiod", "_rid": "NmJkAKiCbEdoQgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdoQgMAAAAAAA==/", "_etag": "\"a5005d85-0000-0100-0000-687027be0000\"", "_attachments": "attachments/", "_ts": 1752180670}, {"payPeriodId": "1080040128389591", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-06-01T00:00:00Z", "endDate": "2025-06-15T00:00:00Z", "submitByDate": "2025-06-13T00:00:00Z", "checkDate": "2025-06-17T00:00:00Z", "checkCount": 5, "id": "ee6c0695-ef82-4561-b997-d6b58103e154", "companyId": "17185521", "type": "payperiod", "_rid": "NmJkAKiCbEdpQgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdpQgMAAAAAAA==/", "_etag": "\"a5005f85-0000-0100-0000-687027be0000\"", "_attachments": "attachments/", "_ts": 1752180670}, {"payPeriodId": "1080040128389592", "intervalCode": "SEMI_MONTHLY", "status": "COMPLETED", "description": "Semi-monthly Payroll (1)", "startDate": "2025-06-16T00:00:00Z", "endDate": "2025-06-30T00:00:00Z", "submitByDate": "2025-06-27T00:00:00Z", "checkDate": "2025-07-01T00:00:00Z", "checkCount": 5, "id": "a2381a30-ad75-432d-88f5-a3cfba9d772e", "companyId": "17185521", "type": "payperiod", "_rid": "NmJkAKiCbEdqQgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdqQgMAAAAAAA==/", "_etag": "\"a5006185-0000-0100-0000-687027bf0000\"", "_attachments": "attachments/", "_ts": 1752180671}, {"payPeriodId": "1080040325399445", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-07-01T00:00:00Z", "endDate": "2025-07-15T00:00:00Z", "submitByDate": "2025-07-15T00:00:00Z", "checkDate": "2025-07-17T00:00:00Z", "checkCount": 0, "id": "efcc87ed-adbd-4f2c-87e9-1e2f50bce5f0", "companyId": "17185521", "type": "payperiod", "_rid": "NmJkAKiCbEdrQgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdrQgMAAAAAAA==/", "_etag": "\"a5006285-0000-0100-0000-687027bf0000\"", "_attachments": "attachments/", "_ts": 1752180671}, {"payPeriodId": "1080040325399446", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-07-16T00:00:00Z", "endDate": "2025-07-31T00:00:00Z", "submitByDate": "2025-07-30T00:00:00Z", "checkDate": "2025-08-01T00:00:00Z", "checkCount": 0, "id": "f479f36d-209a-4a28-8a6a-bf9d4846bee9", "companyId": "17185521", "type": "payperiod", "_rid": "NmJkAKiCbEdsQgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdsQgMAAAAAAA==/", "_etag": "\"a5006685-0000-0100-0000-687027bf0000\"", "_attachments": "attachments/", "_ts": 1752180671}, {"payPeriodId": "1080040539332138", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-08-01T00:00:00Z", "endDate": "2025-08-15T00:00:00Z", "submitByDate": "2025-08-13T00:00:00Z", "checkDate": "2025-08-15T00:00:00Z", "checkCount": 0, "id": "00b4153d-faf8-45ca-981c-d54f4b4f5afb", "companyId": "17185521", "type": "payperiod", "_rid": "NmJkAKiCbEdtQgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdtQgMAAAAAAA==/", "_etag": "\"a5006a85-0000-0100-0000-687027bf0000\"", "_attachments": "attachments/", "_ts": 1752180671}, {"payPeriodId": "1080040539332139", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-08-16T00:00:00Z", "endDate": "2025-08-31T00:00:00Z", "submitByDate": "2025-08-27T00:00:00Z", "checkDate": "2025-08-29T00:00:00Z", "checkCount": 0, "id": "c3ffc5e7-24e8-47fa-882e-cafe63e56e6d", "companyId": "17185521", "type": "payperiod", "_rid": "NmJkAKiCbEduQgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEduQgMAAAAAAA==/", "_etag": "\"a5006c85-0000-0100-0000-687027bf0000\"", "_attachments": "attachments/", "_ts": 1752180671}, {"payPeriodId": "1080040773426801", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-09-01T00:00:00Z", "endDate": "2025-09-15T00:00:00Z", "submitByDate": "2025-09-15T00:00:00Z", "checkDate": "2025-09-17T00:00:00Z", "checkCount": 0, "id": "ecc04d12-d0d4-4dbe-a59f-d10a24802024", "companyId": "17185521", "type": "payperiod", "_rid": "NmJkAKiCbEdvQgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdvQgMAAAAAAA==/", "_etag": "\"a5006d85-0000-0100-0000-687027bf0000\"", "_attachments": "attachments/", "_ts": 1752180671}, {"payPeriodId": "1080040773426802", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-09-16T00:00:00Z", "endDate": "2025-09-30T00:00:00Z", "submitByDate": "2025-09-29T00:00:00Z", "checkDate": "2025-10-01T00:00:00Z", "checkCount": 0, "id": "689969e8-7294-417c-a2d1-c37496441840", "companyId": "17185521", "type": "payperiod", "_rid": "NmJkAKiCbEdwQgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdwQgMAAAAAAA==/", "_etag": "\"a5006f85-0000-0100-0000-687027bf0000\"", "_attachments": "attachments/", "_ts": 1752180671}, {"payPeriodId": "1080040962173971", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-10-01T00:00:00Z", "endDate": "2025-10-15T00:00:00Z", "submitByDate": "2025-10-15T00:00:00Z", "checkDate": "2025-10-17T00:00:00Z", "checkCount": 0, "id": "896ccf73-5dae-49a3-ab8a-c3b72a790046", "companyId": "17185521", "type": "payperiod", "_rid": "NmJkAKiCbEdxQgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdxQgMAAAAAAA==/", "_etag": "\"a5007185-0000-0100-0000-687027bf0000\"", "_attachments": "attachments/", "_ts": 1752180671}, {"payPeriodId": "1080040962173972", "intervalCode": "SEMI_MONTHLY", "status": "INITIAL", "description": "Semi-monthly Payroll (1)", "startDate": "2025-10-16T00:00:00Z", "endDate": "2025-10-31T00:00:00Z", "submitByDate": "2025-10-29T00:00:00Z", "checkDate": "2025-10-31T00:00:00Z", "checkCount": 0, "id": "aa9855d0-93fa-4c2f-8e6d-f933b7c0da5a", "companyId": "17185521", "type": "payperiod", "_rid": "NmJkAKiCbEdyQgMAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdyQgMAAAAAAA==/", "_etag": "\"a5007485-0000-0100-0000-687027bf0000\"", "_attachments": "attachments/", "_ts": 1752180671}], "links": [{"rel": "self", "href": "https://ca-payroll-ai-mock-sb-001-secure.nicebeach-8a7a52ab.eastus.azurecontainerapps.io/ca/companies/17185521/payperiods"}]}, "status_code": 200}