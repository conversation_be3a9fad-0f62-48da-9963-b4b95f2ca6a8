{"success": true, "company_id": "17101339", "data": {"metadata": {"contentItemCount": 246}, "content": [{"payPeriodId": "1080039812849907", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2024-12-30T00:00:00Z", "endDate": "2025-01-05T00:00:00Z", "submitByDate": "2025-01-15T00:00:00Z", "checkDate": "2025-01-21T00:00:00Z", "checkCount": 29, "id": "ec596b49-3600-4eeb-ac9e-8413e38d7a1d", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEfxAwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfxAwAAAAAAAA==/", "_etag": "\"9700de06-0000-0100-0000-686fcf930000\"", "_attachments": "attachments/", "_ts": 1752158099}, {"payPeriodId": "1080039866034570", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-01-06T00:00:00Z", "endDate": "2025-01-12T00:00:00Z", "submitByDate": "2025-01-22T00:00:00Z", "checkDate": "2025-01-29T00:00:00Z", "checkCount": 28, "id": "dc5f0a21-f696-4295-9f1d-ac0ac5688d59", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEfyAwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfyAwAAAAAAAA==/", "_etag": "\"9700df06-0000-0100-0000-686fcf930000\"", "_attachments": "attachments/", "_ts": 1752158099}, {"payPeriodId": "1080039906934417", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-01-13T00:00:00Z", "endDate": "2025-01-19T00:00:00Z", "submitByDate": "2025-01-29T00:00:00Z", "checkDate": "2025-02-04T00:00:00Z", "checkCount": 30, "id": "e73aa9ea-3a57-4a23-92c9-3c0fb94b992f", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEfzAwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfzAwAAAAAAAA==/", "_etag": "\"9700e206-0000-0100-0000-686fcf930000\"", "_attachments": "attachments/", "_ts": 1752158099}, {"payPeriodId": "1080039238323342", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-01-20T00:00:00Z", "endDate": "2025-01-26T00:00:00Z", "submitByDate": "2025-02-05T00:00:00Z", "checkDate": "2025-02-07T00:00:00Z", "checkCount": 30, "id": "014fc427-841e-45b5-84c0-a1e734e807c6", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEf0AwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf0AwAAAAAAAA==/", "_etag": "\"9700e706-0000-0100-0000-686fcf930000\"", "_attachments": "attachments/", "_ts": 1752158099}, {"payPeriodId": "1080039322503869", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-01-27T00:00:00Z", "endDate": "2025-02-02T00:00:00Z", "submitByDate": "2025-02-12T00:00:00Z", "checkDate": "2025-02-14T00:00:00Z", "checkCount": 31, "id": "7dd8043a-45e1-4677-b10a-39b6f4f09eb9", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEf1AwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf1AwAAAAAAAA==/", "_etag": "\"9700ea06-0000-0100-0000-686fcf930000\"", "_attachments": "attachments/", "_ts": 1752158099}, {"payPeriodId": "1080040033496426", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-02-03T00:00:00Z", "endDate": "2025-02-09T00:00:00Z", "submitByDate": "2025-02-19T00:00:00Z", "checkDate": "2025-02-25T00:00:00Z", "checkCount": 30, "id": "11101034-7780-47cf-8422-f4c5a136b437", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEf2AwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf2AwAAAAAAAA==/", "_etag": "\"9700ee06-0000-0100-0000-686fcf930000\"", "_attachments": "attachments/", "_ts": 1752158099}, {"payPeriodId": "1080039415135065", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-02-10T00:00:00Z", "endDate": "2025-02-16T00:00:00Z", "submitByDate": "2025-02-26T00:00:00Z", "checkDate": "2025-02-28T00:00:00Z", "checkCount": 31, "id": "fc5771a8-0b9d-49d3-80f6-74aceaba87aa", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEf3AwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf3AwAAAAAAAA==/", "_etag": "\"9700f006-0000-0100-0000-686fcf930000\"", "_attachments": "attachments/", "_ts": 1752158099}, {"payPeriodId": "1080040129804289", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-02-17T00:00:00Z", "endDate": "2025-02-23T00:00:00Z", "submitByDate": "2025-03-05T00:00:00Z", "checkDate": "2025-03-11T00:00:00Z", "checkCount": 30, "id": "3a477ced-e4af-4969-8834-67788e42a9b4", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEf4AwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf4AwAAAAAAAA==/", "_etag": "\"9700f606-0000-0100-0000-686fcf930000\"", "_attachments": "attachments/", "_ts": 1752158099}, {"payPeriodId": "1080040180670341", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-02-24T00:00:00Z", "endDate": "2025-03-02T00:00:00Z", "submitByDate": "2025-03-12T00:00:00Z", "checkDate": "2025-03-18T00:00:00Z", "checkCount": 30, "id": "17206e3c-b434-4004-8805-735440d1145f", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEf5AwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf5AwAAAAAAAA==/", "_etag": "\"9700f906-0000-0100-0000-686fcf930000\"", "_attachments": "attachments/", "_ts": 1752158099}, {"payPeriodId": "1080040220825478", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-03-03T00:00:00Z", "endDate": "2025-03-09T00:00:00Z", "submitByDate": "2025-03-19T00:00:00Z", "checkDate": "2025-03-25T00:00:00Z", "checkCount": 30, "id": "85c03484-9474-4a65-ab12-d7f4adbc8ab5", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEf6AwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf6AwAAAAAAAA==/", "_etag": "\"9700fd06-0000-0100-0000-686fcf930000\"", "_attachments": "attachments/", "_ts": 1752158099}, {"payPeriodId": "1080040278492591", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-03-10T00:00:00Z", "endDate": "2025-03-16T00:00:00Z", "submitByDate": "2025-03-26T00:00:00Z", "checkDate": "2025-04-01T00:00:00Z", "checkCount": 0, "id": "c32a8842-e45c-4464-ab3b-3f77ee42f67f", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEf7AwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf7AwAAAAAAAA==/", "_etag": "\"9700fe06-0000-0100-0000-686fcf930000\"", "_attachments": "attachments/", "_ts": 1752158099}, {"payPeriodId": "1080040335270371", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-03-17T00:00:00Z", "endDate": "2025-03-23T00:00:00Z", "submitByDate": "2025-04-02T00:00:00Z", "checkDate": "2025-04-08T00:00:00Z", "checkCount": 0, "id": "6faa59dd-5e09-4e56-b832-a560840ebe33", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEf8AwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf8AwAAAAAAAA==/", "_etag": "\"97000107-0000-0100-0000-686fcf930000\"", "_attachments": "attachments/", "_ts": 1752158099}, {"payPeriodId": "1080040385987922", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-03-24T00:00:00Z", "endDate": "2025-03-30T00:00:00Z", "submitByDate": "2025-04-09T00:00:00Z", "checkDate": "2025-04-15T00:00:00Z", "checkCount": 0, "id": "dad8c366-87a5-450e-b33b-57c09cbd530a", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEf9AwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf9AwAAAAAAAA==/", "_etag": "\"97000807-0000-0100-0000-686fcf930000\"", "_attachments": "attachments/", "_ts": 1752158099}, {"payPeriodId": "1080040450436724", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-03-31T00:00:00Z", "endDate": "2025-04-06T00:00:00Z", "submitByDate": "2025-04-16T00:00:00Z", "checkDate": "2025-04-24T00:00:00Z", "checkCount": 0, "id": "6ccafc0a-b13f-4d7d-a077-fc905b96efe4", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEf+AwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf+AwAAAAAAAA==/", "_etag": "\"97000a07-0000-0100-0000-686fcf930000\"", "_attachments": "attachments/", "_ts": 1752158099}, {"payPeriodId": "1080040450436682", "status": "INITIAL", "description": "<PERSON>", "startDate": "2025-03-31T00:00:00Z", "endDate": "2025-04-06T00:00:00Z", "submitByDate": "2025-04-23T00:00:00Z", "checkDate": "2025-04-25T00:00:00Z", "checkCount": 0, "id": "97045a43-ade6-4bac-8f5f-23315433f906", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEf-AwAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf-AwAAAAAAAA==/", "_etag": "\"97000c07-0000-0100-0000-686fcf940000\"", "_attachments": "attachments/", "_ts": 1752158100}, {"payPeriodId": "1080040511250850", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-04-07T00:00:00Z", "endDate": "2025-04-13T00:00:00Z", "submitByDate": "2025-04-25T00:00:00Z", "checkDate": "2025-05-01T00:00:00Z", "checkCount": 0, "id": "cdefabd8-facd-4069-b977-b17e6ae7d9f6", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEcABAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcABAAAAAAAAA==/", "_etag": "\"97000f07-0000-0100-0000-686fcf940000\"", "_attachments": "attachments/", "_ts": 1752158100}, {"payPeriodId": "1080040561759779", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-04-14T00:00:00Z", "endDate": "2025-04-20T00:00:00Z", "submitByDate": "2025-05-02T00:00:00Z", "checkDate": "2025-05-08T00:00:00Z", "checkCount": 0, "id": "6dbf5305-8cd6-4b31-ab82-cfb673a30df4", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEcBBAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcBBAAAAAAAAA==/", "_etag": "\"97001207-0000-0100-0000-686fcf940000\"", "_attachments": "attachments/", "_ts": 1752158100}, {"payPeriodId": "1080040620622346", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-04-21T00:00:00Z", "endDate": "2025-04-27T00:00:00Z", "submitByDate": "2025-05-09T00:00:00Z", "checkDate": "2025-05-15T00:00:00Z", "checkCount": 0, "id": "80048242-d6f6-4db9-8627-26c20e82e6af", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEcCBAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcCBAAAAAAAAA==/", "_etag": "\"97001607-0000-0100-0000-686fcf940000\"", "_attachments": "attachments/", "_ts": 1752158100}, {"payPeriodId": "1080040620621582", "status": "INITIAL", "description": "<PERSON>", "startDate": "2025-04-21T00:00:00Z", "endDate": "2025-04-27T00:00:00Z", "submitByDate": "2025-05-15T00:00:00Z", "checkDate": "2025-05-16T00:00:00Z", "checkCount": 0, "id": "2d20dd3d-3f20-4bc8-8f8c-987e2f169bd0", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEcDBAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcDBAAAAAAAAA==/", "_etag": "\"97001807-0000-0100-0000-686fcf940000\"", "_attachments": "attachments/", "_ts": 1752158100}, {"payPeriodId": "1080040450437243", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-04-28T00:00:00Z", "endDate": "2025-05-04T00:00:00Z", "submitByDate": "2025-05-16T00:00:00Z", "checkDate": "2025-05-20T00:00:00Z", "checkCount": 0, "id": "975c6f52-6b7e-435e-aa47-7879e397f36f", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEcEBAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcEBAAAAAAAAA==/", "_etag": "\"97001b07-0000-0100-0000-686fcf940000\"", "_attachments": "attachments/", "_ts": 1752158100}, {"payPeriodId": "1080040450437246", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-05-05T00:00:00Z", "endDate": "2025-05-11T00:00:00Z", "submitByDate": "2025-05-22T00:00:00Z", "checkDate": "2025-05-27T00:00:00Z", "checkCount": 0, "id": "a86ef9f8-80c2-4861-8029-a2a989f8c0fe", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEcFBAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcFBAAAAAAAAA==/", "_etag": "\"97001f07-0000-0100-0000-686fcf940000\"", "_attachments": "attachments/", "_ts": 1752158100}, {"payPeriodId": "1090071423493049", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-05-12T00:00:00Z", "endDate": "2025-05-18T00:00:00Z", "submitByDate": "2025-05-30T00:00:00Z", "checkDate": "2025-06-03T00:00:00Z", "checkCount": 0, "id": "e1af9b23-3140-47ad-abac-488bbc8f73a9", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEcGBAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcGBAAAAAAAAA==/", "_etag": "\"97002107-0000-0100-0000-686fcf940000\"", "_attachments": "attachments/", "_ts": 1752158100}, {"payPeriodId": "1090071423493052", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-05-19T00:00:00Z", "endDate": "2025-05-25T00:00:00Z", "submitByDate": "2025-06-06T00:00:00Z", "checkDate": "2025-06-10T00:00:00Z", "checkCount": 0, "id": "4d8ecbef-4387-4ed8-b55f-bed34f212bc7", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEcHBAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcHBAAAAAAAAA==/", "_etag": "\"97002407-0000-0100-0000-686fcf940000\"", "_attachments": "attachments/", "_ts": 1752158100}, {"payPeriodId": "1090071423493055", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-05-26T00:00:00Z", "endDate": "2025-06-01T00:00:00Z", "submitByDate": "2025-06-13T00:00:00Z", "checkDate": "2025-06-17T00:00:00Z", "checkCount": 0, "id": "e3316ae7-7fee-4b29-aa1a-dfff632dbf31", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEcIBAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcIBAAAAAAAAA==/", "_etag": "\"97002707-0000-0100-0000-686fcf940000\"", "_attachments": "attachments/", "_ts": 1752158100}, {"payPeriodId": "1090071423493058", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-06-02T00:00:00Z", "endDate": "2025-06-08T00:00:00Z", "submitByDate": "2025-06-20T00:00:00Z", "checkDate": "2025-06-24T00:00:00Z", "checkCount": 0, "id": "a7413737-91dc-40d3-9c4a-c54389f140eb", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEcJBAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcJBAAAAAAAAA==/", "_etag": "\"97002907-0000-0100-0000-686fcf940000\"", "_attachments": "attachments/", "_ts": 1752158100}, {"payPeriodId": "1090071423493064", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-06-16T00:00:00Z", "endDate": "2025-06-22T00:00:00Z", "submitByDate": "2025-07-03T00:00:00Z", "checkDate": "2025-07-08T00:00:00Z", "checkCount": 0, "id": "629a96a7-a4b1-40e5-abfa-09f3e2138729", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEcKBAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcKBAAAAAAAAA==/", "_etag": "\"97002c07-0000-0100-0000-686fcf940000\"", "_attachments": "attachments/", "_ts": 1752158100}, {"payPeriodId": "1090071423493067", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-06-23T00:00:00Z", "endDate": "2025-06-29T00:00:00Z", "submitByDate": "2025-07-11T00:00:00Z", "checkDate": "2025-07-15T00:00:00Z", "checkCount": 0, "id": "1d5074b8-fecd-46fb-a60b-7d341d457c25", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEcLBAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcLBAAAAAAAAA==/", "_etag": "\"97002d07-0000-0100-0000-686fcf940000\"", "_attachments": "attachments/", "_ts": 1752158100}, {"payPeriodId": "1090071423493070", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-06-30T00:00:00Z", "endDate": "2025-07-06T00:00:00Z", "submitByDate": "2025-07-18T00:00:00Z", "checkDate": "2025-07-22T00:00:00Z", "checkCount": 0, "id": "8515d27c-fbc3-4a2f-aef0-5c6b88429bcd", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEcMBAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcMBAAAAAAAAA==/", "_etag": "\"97002e07-0000-0100-0000-686fcf940000\"", "_attachments": "attachments/", "_ts": 1752158100}, {"payPeriodId": "1090071423493073", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-07-07T00:00:00Z", "endDate": "2025-07-13T00:00:00Z", "submitByDate": "2025-07-25T00:00:00Z", "checkDate": "2025-07-29T00:00:00Z", "checkCount": 0, "id": "b5335b98-33e1-4f84-8b64-3266f50f9b4c", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEcNBAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcNBAAAAAAAAA==/", "_etag": "\"97003107-0000-0100-0000-686fcf950000\"", "_attachments": "attachments/", "_ts": 1752158101}, {"payPeriodId": "1090071423493076", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-07-14T00:00:00Z", "endDate": "2025-07-20T00:00:00Z", "submitByDate": "2025-08-01T00:00:00Z", "checkDate": "2025-08-05T00:00:00Z", "checkCount": 0, "id": "d0864555-ee8f-4f62-b919-1e078f0afd82", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEcOBAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcOBAAAAAAAAA==/", "_etag": "\"97003407-0000-0100-0000-686fcf950000\"", "_attachments": "attachments/", "_ts": 1752158101}, {"payPeriodId": "1090071423493079", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-07-21T00:00:00Z", "endDate": "2025-07-27T00:00:00Z", "submitByDate": "2025-08-08T00:00:00Z", "checkDate": "2025-08-12T00:00:00Z", "checkCount": 0, "id": "1ce83961-d1aa-4cb0-a4af-9cc8ccef5613", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEcPBAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcPBAAAAAAAAA==/", "_etag": "\"97003707-0000-0100-0000-686fcf950000\"", "_attachments": "attachments/", "_ts": 1752158101}, {"payPeriodId": "1090071423493082", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-07-28T00:00:00Z", "endDate": "2025-08-03T00:00:00Z", "submitByDate": "2025-08-15T00:00:00Z", "checkDate": "2025-08-19T00:00:00Z", "checkCount": 0, "id": "92844d06-14c6-42c0-a00a-f8b1ba48f463", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEcQBAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcQBAAAAAAAAA==/", "_etag": "\"97003a07-0000-0100-0000-686fcf950000\"", "_attachments": "attachments/", "_ts": 1752158101}, {"payPeriodId": "1090071423493085", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-08-04T00:00:00Z", "endDate": "2025-08-10T00:00:00Z", "submitByDate": "2025-08-22T00:00:00Z", "checkDate": "2025-08-26T00:00:00Z", "checkCount": 0, "id": "e8e78c13-fa65-4c24-b9de-d6f44878922e", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEcRBAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcRBAAAAAAAAA==/", "_etag": "\"97003d07-0000-0100-0000-686fcf950000\"", "_attachments": "attachments/", "_ts": 1752158101}, {"payPeriodId": "1090071493994120", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-08-11T00:00:00Z", "endDate": "2025-08-17T00:00:00Z", "submitByDate": "2025-08-28T00:00:00Z", "checkDate": "2025-09-02T00:00:00Z", "checkCount": 0, "id": "bdc938f9-e5c6-4836-9d3f-e5d7ddfddf40", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEcSBAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcSBAAAAAAAAA==/", "_etag": "\"97004207-0000-0100-0000-686fcf950000\"", "_attachments": "attachments/", "_ts": 1752158101}, {"payPeriodId": "1090071493994121", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-08-18T00:00:00Z", "endDate": "2025-08-24T00:00:00Z", "submitByDate": "2025-09-05T00:00:00Z", "checkDate": "2025-09-09T00:00:00Z", "checkCount": 0, "id": "24d276ec-85db-44a8-9850-4ca071aae39c", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEcTBAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcTBAAAAAAAAA==/", "_etag": "\"97004407-0000-0100-0000-686fcf950000\"", "_attachments": "attachments/", "_ts": 1752158101}, {"payPeriodId": "1090071493994122", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-08-25T00:00:00Z", "endDate": "2025-08-31T00:00:00Z", "submitByDate": "2025-09-12T00:00:00Z", "checkDate": "2025-09-16T00:00:00Z", "checkCount": 0, "id": "5400b0fd-5760-4668-b3e0-33826f15d01c", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEcUBAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcUBAAAAAAAAA==/", "_etag": "\"97004807-0000-0100-0000-686fcf950000\"", "_attachments": "attachments/", "_ts": 1752158101}, {"payPeriodId": "1090071493994123", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-09-01T00:00:00Z", "endDate": "2025-09-07T00:00:00Z", "submitByDate": "2025-09-19T00:00:00Z", "checkDate": "2025-09-23T00:00:00Z", "checkCount": 0, "id": "4fcdf577-c3fd-42bd-9c53-f959dc4cf159", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEcVBAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcVBAAAAAAAAA==/", "_etag": "\"97004c07-0000-0100-0000-686fcf950000\"", "_attachments": "attachments/", "_ts": 1752158101}, {"payPeriodId": "1090071612860167", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-09-08T00:00:00Z", "endDate": "2025-09-14T00:00:00Z", "submitByDate": "2025-09-26T00:00:00Z", "checkDate": "2025-09-30T00:00:00Z", "checkCount": 0, "id": "0e6a3186-7214-4502-9564-70352a86e767", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEcWBAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcWBAAAAAAAAA==/", "_etag": "\"97004f07-0000-0100-0000-686fcf950000\"", "_attachments": "attachments/", "_ts": 1752158101}, {"payPeriodId": "1090071819953873", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-09-15T00:00:00Z", "endDate": "2025-09-21T00:00:00Z", "submitByDate": "2025-10-03T00:00:00Z", "checkDate": "2025-10-07T00:00:00Z", "checkCount": 0, "id": "8f084d3a-a6be-40dd-8b30-041d1cc8b8a1", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEcXBAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcXBAAAAAAAAA==/", "_etag": "\"97005207-0000-0100-0000-686fcf950000\"", "_attachments": "attachments/", "_ts": 1752158101}, {"payPeriodId": "1090071983067164", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-09-22T00:00:00Z", "endDate": "2025-09-28T00:00:00Z", "submitByDate": "2025-10-10T00:00:00Z", "checkDate": "2025-10-14T00:00:00Z", "checkCount": 0, "id": "83d91c9e-e90d-489b-954e-b74d89057333", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEcYBAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcYBAAAAAAAAA==/", "_etag": "\"97005507-0000-0100-0000-686fcf950000\"", "_attachments": "attachments/", "_ts": 1752158101}, {"payPeriodId": "1090071423493061", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-06-09T00:00:00Z", "endDate": "2025-06-15T00:00:00Z", "submitByDate": "2025-06-27T00:00:00Z", "checkDate": "2025-07-01T00:00:00Z", "checkCount": 0, "id": "09d7df09-73cb-4cd8-b800-77600d438f9c", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEcZBAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcZBAAAAAAAAA==/", "_etag": "\"97005907-0000-0100-0000-686fcf950000\"", "_attachments": "attachments/", "_ts": 1752158101}, {"payPeriodId": "1080039812849907", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2024-12-30T00:00:00Z", "endDate": "2025-01-05T00:00:00Z", "submitByDate": "2025-01-15T00:00:00Z", "checkDate": "2025-01-21T00:00:00Z", "checkCount": 29, "id": "8e27a56d-16e7-403c-b35a-28adb483a6f5", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEeYBAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeYBAAAAAAAAA==/", "_etag": "\"9700ee08-0000-0100-0000-686fcf9f0000\"", "_attachments": "attachments/", "_ts": 1752158111}, {"payPeriodId": "1080039866034570", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-01-06T00:00:00Z", "endDate": "2025-01-12T00:00:00Z", "submitByDate": "2025-01-22T00:00:00Z", "checkDate": "2025-01-29T00:00:00Z", "checkCount": 28, "id": "b10244fb-05d8-48fd-b60c-0a91f0724d82", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEeZBAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeZBAAAAAAAAA==/", "_etag": "\"9700ef08-0000-0100-0000-686fcf9f0000\"", "_attachments": "attachments/", "_ts": 1752158111}, {"payPeriodId": "1080039906934417", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-01-13T00:00:00Z", "endDate": "2025-01-19T00:00:00Z", "submitByDate": "2025-01-29T00:00:00Z", "checkDate": "2025-02-04T00:00:00Z", "checkCount": 30, "id": "8f418203-c7f0-44ed-b02a-d5804c378cbf", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEeaBAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeaBAAAAAAAAA==/", "_etag": "\"9700f508-0000-0100-0000-686fcf9f0000\"", "_attachments": "attachments/", "_ts": 1752158111}, {"payPeriodId": "1080039238323342", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-01-20T00:00:00Z", "endDate": "2025-01-26T00:00:00Z", "submitByDate": "2025-02-05T00:00:00Z", "checkDate": "2025-02-07T00:00:00Z", "checkCount": 30, "id": "b6c6fa98-8565-4dc9-b2d4-2866b2f98562", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEebBAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEebBAAAAAAAAA==/", "_etag": "\"9700f708-0000-0100-0000-686fcf9f0000\"", "_attachments": "attachments/", "_ts": 1752158111}, {"payPeriodId": "1080039322503869", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-01-27T00:00:00Z", "endDate": "2025-02-02T00:00:00Z", "submitByDate": "2025-02-12T00:00:00Z", "checkDate": "2025-02-14T00:00:00Z", "checkCount": 31, "id": "a2c94950-483e-4413-bdbf-0e0cbec8d3cf", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEecBAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEecBAAAAAAAAA==/", "_etag": "\"9700fa08-0000-0100-0000-686fcf9f0000\"", "_attachments": "attachments/", "_ts": 1752158111}, {"payPeriodId": "1080040033496426", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-02-03T00:00:00Z", "endDate": "2025-02-09T00:00:00Z", "submitByDate": "2025-02-19T00:00:00Z", "checkDate": "2025-02-25T00:00:00Z", "checkCount": 30, "id": "74140a4d-91ff-4469-b196-ab5ba9654dd4", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEedBAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEedBAAAAAAAAA==/", "_etag": "\"9700fd08-0000-0100-0000-686fcf9f0000\"", "_attachments": "attachments/", "_ts": 1752158111}, {"payPeriodId": "1080039415135065", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-02-10T00:00:00Z", "endDate": "2025-02-16T00:00:00Z", "submitByDate": "2025-02-26T00:00:00Z", "checkDate": "2025-02-28T00:00:00Z", "checkCount": 31, "id": "05ac2096-6f06-4d34-97a9-1bb2bb6067c2", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEeeBAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeeBAAAAAAAAA==/", "_etag": "\"97000109-0000-0100-0000-686fcf9f0000\"", "_attachments": "attachments/", "_ts": 1752158111}, {"payPeriodId": "1080040129804289", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-02-17T00:00:00Z", "endDate": "2025-02-23T00:00:00Z", "submitByDate": "2025-03-05T00:00:00Z", "checkDate": "2025-03-11T00:00:00Z", "checkCount": 30, "id": "e3ba1c87-14bd-4f96-af1e-d274968ebedf", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEefBAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEefBAAAAAAAAA==/", "_etag": "\"97000209-0000-0100-0000-686fcf9f0000\"", "_attachments": "attachments/", "_ts": 1752158111}, {"payPeriodId": "1080040180670341", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-02-24T00:00:00Z", "endDate": "2025-03-02T00:00:00Z", "submitByDate": "2025-03-12T00:00:00Z", "checkDate": "2025-03-18T00:00:00Z", "checkCount": 30, "id": "c7f9c27b-1b73-4819-8c95-b93c3ec7696c", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEegBAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEegBAAAAAAAAA==/", "_etag": "\"97000509-0000-0100-0000-686fcf9f0000\"", "_attachments": "attachments/", "_ts": 1752158111}, {"payPeriodId": "1080040220825478", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-03-03T00:00:00Z", "endDate": "2025-03-09T00:00:00Z", "submitByDate": "2025-03-19T00:00:00Z", "checkDate": "2025-03-25T00:00:00Z", "checkCount": 30, "id": "5954aa6b-d38d-4a86-b5e0-7e6f1f97476c", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEehBAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEehBAAAAAAAAA==/", "_etag": "\"97000909-0000-0100-0000-686fcf9f0000\"", "_attachments": "attachments/", "_ts": 1752158111}, {"payPeriodId": "1080040278492591", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-03-10T00:00:00Z", "endDate": "2025-03-16T00:00:00Z", "submitByDate": "2025-03-26T00:00:00Z", "checkDate": "2025-04-01T00:00:00Z", "checkCount": 30, "id": "5d6134fd-fe1e-4089-a35b-9278eb403cd5", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEeiBAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeiBAAAAAAAAA==/", "_etag": "\"97000c09-0000-0100-0000-686fcfa00000\"", "_attachments": "attachments/", "_ts": 1752158112}, {"payPeriodId": "1080040335270371", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-03-17T00:00:00Z", "endDate": "2025-03-23T00:00:00Z", "submitByDate": "2025-04-02T00:00:00Z", "checkDate": "2025-04-08T00:00:00Z", "checkCount": 29, "id": "7389bf8f-7ad0-4a7e-ac5a-7204142a76a5", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEejBAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEejBAAAAAAAAA==/", "_etag": "\"97000e09-0000-0100-0000-686fcfa00000\"", "_attachments": "attachments/", "_ts": 1752158112}, {"payPeriodId": "1080040385987922", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-03-24T00:00:00Z", "endDate": "2025-03-30T00:00:00Z", "submitByDate": "2025-04-09T00:00:00Z", "checkDate": "2025-04-15T00:00:00Z", "checkCount": 28, "id": "4f0a7419-c502-4235-a2d7-98e95d54d133", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEekBAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEekBAAAAAAAAA==/", "_etag": "\"97001509-0000-0100-0000-686fcfa00000\"", "_attachments": "attachments/", "_ts": 1752158112}, {"payPeriodId": "1080040450436724", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-03-31T00:00:00Z", "endDate": "2025-04-06T00:00:00Z", "submitByDate": "2025-04-16T00:00:00Z", "checkDate": "2025-04-24T00:00:00Z", "checkCount": 28, "id": "59bc6f08-b14f-4d43-8102-4e3ce1f279a7", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEelBAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEelBAAAAAAAAA==/", "_etag": "\"97001a09-0000-0100-0000-686fcfa00000\"", "_attachments": "attachments/", "_ts": 1752158112}, {"payPeriodId": "1080040450436682", "status": "COMPLETED", "description": "<PERSON>", "startDate": "2025-03-31T00:00:00Z", "endDate": "2025-04-06T00:00:00Z", "submitByDate": "2025-04-23T00:00:00Z", "checkDate": "2025-04-25T00:00:00Z", "checkCount": 1, "id": "77c4231d-6a8b-4a75-ac23-801e142eff7c", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEemBAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEemBAAAAAAAAA==/", "_etag": "\"97001d09-0000-0100-0000-686fcfa00000\"", "_attachments": "attachments/", "_ts": 1752158112}, {"payPeriodId": "1080040511250850", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-04-07T00:00:00Z", "endDate": "2025-04-13T00:00:00Z", "submitByDate": "2025-04-25T00:00:00Z", "checkDate": "2025-05-01T00:00:00Z", "checkCount": 20, "id": "e9ad0873-f233-4b44-978c-486860511a90", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEenBAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEenBAAAAAAAAA==/", "_etag": "\"97002209-0000-0100-0000-686fcfa00000\"", "_attachments": "attachments/", "_ts": 1752158112}, {"payPeriodId": "1080040561759779", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-04-14T00:00:00Z", "endDate": "2025-04-20T00:00:00Z", "submitByDate": "2025-05-02T00:00:00Z", "checkDate": "2025-05-08T00:00:00Z", "checkCount": 22, "id": "1124b392-7b3e-4473-806e-a1b0709188b0", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEeoBAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeoBAAAAAAAAA==/", "_etag": "\"97002309-0000-0100-0000-686fcfa00000\"", "_attachments": "attachments/", "_ts": 1752158112}, {"payPeriodId": "1080040620622346", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-04-21T00:00:00Z", "endDate": "2025-04-27T00:00:00Z", "submitByDate": "2025-05-09T00:00:00Z", "checkDate": "2025-05-15T00:00:00Z", "checkCount": 29, "id": "12be2741-27f3-4582-81dd-08bfb987d5b0", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEepBAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEepBAAAAAAAAA==/", "_etag": "\"97002609-0000-0100-0000-686fcfa00000\"", "_attachments": "attachments/", "_ts": 1752158112}, {"payPeriodId": "1080040620621582", "status": "COMPLETED", "description": "<PERSON>", "startDate": "2025-04-21T00:00:00Z", "endDate": "2025-04-27T00:00:00Z", "submitByDate": "2025-05-15T00:00:00Z", "checkDate": "2025-05-16T00:00:00Z", "checkCount": 1, "id": "1e47e769-4d26-48f1-9e29-ba7b937c419f", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEeqBAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeqBAAAAAAAAA==/", "_etag": "\"97002909-0000-0100-0000-686fcfa00000\"", "_attachments": "attachments/", "_ts": 1752158112}, {"payPeriodId": "1080040450437243", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-04-28T00:00:00Z", "endDate": "2025-05-04T00:00:00Z", "submitByDate": "2025-05-16T00:00:00Z", "checkDate": "2025-05-20T00:00:00Z", "checkCount": 25, "id": "1ea46633-78ed-4307-8a5e-cb06e2ccde6c", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEerBAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEerBAAAAAAAAA==/", "_etag": "\"97002c09-0000-0100-0000-686fcfa00000\"", "_attachments": "attachments/", "_ts": 1752158112}, {"payPeriodId": "1080040450437246", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-05-05T00:00:00Z", "endDate": "2025-05-11T00:00:00Z", "submitByDate": "2025-05-22T00:00:00Z", "checkDate": "2025-05-27T00:00:00Z", "checkCount": 25, "id": "d2ad957f-2e0c-4043-86a0-4f5d65a6d41d", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEesBAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEesBAAAAAAAAA==/", "_etag": "\"97002e09-0000-0100-0000-686fcfa00000\"", "_attachments": "attachments/", "_ts": 1752158112}, {"payPeriodId": "1090071423493049", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-05-12T00:00:00Z", "endDate": "2025-05-18T00:00:00Z", "submitByDate": "2025-05-30T00:00:00Z", "checkDate": "2025-06-03T00:00:00Z", "checkCount": 28, "id": "ce22f380-6fe3-4f9e-81a9-20b4d94733a8", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEetBAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEetBAAAAAAAAA==/", "_etag": "\"97003409-0000-0100-0000-686fcfa00000\"", "_attachments": "attachments/", "_ts": 1752158112}, {"payPeriodId": "1090071423493052", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-05-19T00:00:00Z", "endDate": "2025-05-25T00:00:00Z", "submitByDate": "2025-06-06T00:00:00Z", "checkDate": "2025-06-10T00:00:00Z", "checkCount": 23, "id": "f20404c2-8991-4da4-968c-770d34c139e6", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEeuBAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeuBAAAAAAAAA==/", "_etag": "\"97003509-0000-0100-0000-686fcfa00000\"", "_attachments": "attachments/", "_ts": 1752158112}, {"payPeriodId": "1090071423493055", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-05-26T00:00:00Z", "endDate": "2025-06-01T00:00:00Z", "submitByDate": "2025-06-13T00:00:00Z", "checkDate": "2025-06-17T00:00:00Z", "checkCount": 24, "id": "df1b9f3f-5ea1-4cbe-b50e-1f4c36a97e3e", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEevBAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEevBAAAAAAAAA==/", "_etag": "\"97003709-0000-0100-0000-686fcfa00000\"", "_attachments": "attachments/", "_ts": 1752158112}, {"payPeriodId": "1090071423493058", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-06-02T00:00:00Z", "endDate": "2025-06-08T00:00:00Z", "submitByDate": "2025-06-20T00:00:00Z", "checkDate": "2025-06-24T00:00:00Z", "checkCount": 25, "id": "bfaf84ec-2bff-4e66-8c35-6f655fe03653", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEewBAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEewBAAAAAAAAA==/", "_etag": "\"97003a09-0000-0100-0000-686fcfa10000\"", "_attachments": "attachments/", "_ts": 1752158113}, {"payPeriodId": "1090071423493064", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-06-16T00:00:00Z", "endDate": "2025-06-22T00:00:00Z", "submitByDate": "2025-07-03T00:00:00Z", "checkDate": "2025-07-08T00:00:00Z", "checkCount": 0, "id": "0eb5c86b-1f47-46d4-b065-4fee28c16c1f", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEexBAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEexBAAAAAAAAA==/", "_etag": "\"97003f09-0000-0100-0000-686fcfa10000\"", "_attachments": "attachments/", "_ts": 1752158113}, {"payPeriodId": "1090071423493067", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-06-23T00:00:00Z", "endDate": "2025-06-29T00:00:00Z", "submitByDate": "2025-07-11T00:00:00Z", "checkDate": "2025-07-15T00:00:00Z", "checkCount": 0, "id": "9066340b-7f67-4e08-a276-db7f5201f673", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEeyBAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeyBAAAAAAAAA==/", "_etag": "\"97004109-0000-0100-0000-686fcfa10000\"", "_attachments": "attachments/", "_ts": 1752158113}, {"payPeriodId": "1090071423493070", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-06-30T00:00:00Z", "endDate": "2025-07-06T00:00:00Z", "submitByDate": "2025-07-18T00:00:00Z", "checkDate": "2025-07-22T00:00:00Z", "checkCount": 0, "id": "1851877a-6791-4924-9291-4e0274f6da10", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEezBAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEezBAAAAAAAAA==/", "_etag": "\"97004a09-0000-0100-0000-686fcfa10000\"", "_attachments": "attachments/", "_ts": 1752158113}, {"payPeriodId": "1090071423493073", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-07-07T00:00:00Z", "endDate": "2025-07-13T00:00:00Z", "submitByDate": "2025-07-25T00:00:00Z", "checkDate": "2025-07-29T00:00:00Z", "checkCount": 0, "id": "ce24e259-d91b-4e8f-97d4-2dab0501f741", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEe0BAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEe0BAAAAAAAAA==/", "_etag": "\"97004d09-0000-0100-0000-686fcfa10000\"", "_attachments": "attachments/", "_ts": 1752158113}, {"payPeriodId": "1090071423493076", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-07-14T00:00:00Z", "endDate": "2025-07-20T00:00:00Z", "submitByDate": "2025-08-01T00:00:00Z", "checkDate": "2025-08-05T00:00:00Z", "checkCount": 0, "id": "385cf9b4-9060-49a3-9f1c-cb4edf955115", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEe1BAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEe1BAAAAAAAAA==/", "_etag": "\"97004e09-0000-0100-0000-686fcfa10000\"", "_attachments": "attachments/", "_ts": 1752158113}, {"payPeriodId": "1090071423493079", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-07-21T00:00:00Z", "endDate": "2025-07-27T00:00:00Z", "submitByDate": "2025-08-08T00:00:00Z", "checkDate": "2025-08-12T00:00:00Z", "checkCount": 0, "id": "c4988018-c599-4b10-96e9-5c2e935b2049", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEe2BAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEe2BAAAAAAAAA==/", "_etag": "\"97005109-0000-0100-0000-686fcfa10000\"", "_attachments": "attachments/", "_ts": 1752158113}, {"payPeriodId": "1090071423493082", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-07-28T00:00:00Z", "endDate": "2025-08-03T00:00:00Z", "submitByDate": "2025-08-15T00:00:00Z", "checkDate": "2025-08-19T00:00:00Z", "checkCount": 0, "id": "7fc31c9c-c918-4d23-a5f0-b17980e0ad85", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEe3BAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEe3BAAAAAAAAA==/", "_etag": "\"97005409-0000-0100-0000-686fcfa10000\"", "_attachments": "attachments/", "_ts": 1752158113}, {"payPeriodId": "1090071423493085", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-08-04T00:00:00Z", "endDate": "2025-08-10T00:00:00Z", "submitByDate": "2025-08-22T00:00:00Z", "checkDate": "2025-08-26T00:00:00Z", "checkCount": 0, "id": "12c384d7-9c75-457c-b0b6-39a841fe3d0f", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEe4BAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEe4BAAAAAAAAA==/", "_etag": "\"97005509-0000-0100-0000-686fcfa10000\"", "_attachments": "attachments/", "_ts": 1752158113}, {"payPeriodId": "1090071493994120", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-08-11T00:00:00Z", "endDate": "2025-08-17T00:00:00Z", "submitByDate": "2025-08-28T00:00:00Z", "checkDate": "2025-09-02T00:00:00Z", "checkCount": 0, "id": "97458fc4-afa2-42a4-98b9-33ef4291c39c", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEe5BAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEe5BAAAAAAAAA==/", "_etag": "\"97005a09-0000-0100-0000-686fcfa10000\"", "_attachments": "attachments/", "_ts": 1752158113}, {"payPeriodId": "1090071493994121", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-08-18T00:00:00Z", "endDate": "2025-08-24T00:00:00Z", "submitByDate": "2025-09-05T00:00:00Z", "checkDate": "2025-09-09T00:00:00Z", "checkCount": 0, "id": "e963eccf-d0ba-4510-a5ce-ef265843383c", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEe6BAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEe6BAAAAAAAAA==/", "_etag": "\"97005d09-0000-0100-0000-686fcfa10000\"", "_attachments": "attachments/", "_ts": 1752158113}, {"payPeriodId": "1090071493994122", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-08-25T00:00:00Z", "endDate": "2025-08-31T00:00:00Z", "submitByDate": "2025-09-12T00:00:00Z", "checkDate": "2025-09-16T00:00:00Z", "checkCount": 0, "id": "52c06402-0199-4def-8189-9ab0ec0fa53c", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEe7BAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEe7BAAAAAAAAA==/", "_etag": "\"97005f09-0000-0100-0000-686fcfa10000\"", "_attachments": "attachments/", "_ts": 1752158113}, {"payPeriodId": "1090071493994123", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-09-01T00:00:00Z", "endDate": "2025-09-07T00:00:00Z", "submitByDate": "2025-09-19T00:00:00Z", "checkDate": "2025-09-23T00:00:00Z", "checkCount": 0, "id": "5efdb1ea-2f85-4c27-bf4c-02e54b759ab1", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEe8BAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEe8BAAAAAAAAA==/", "_etag": "\"97006209-0000-0100-0000-686fcfa10000\"", "_attachments": "attachments/", "_ts": 1752158113}, {"payPeriodId": "1090071612860167", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-09-08T00:00:00Z", "endDate": "2025-09-14T00:00:00Z", "submitByDate": "2025-09-26T00:00:00Z", "checkDate": "2025-09-30T00:00:00Z", "checkCount": 0, "id": "db55487c-3e73-4fba-8e71-778396a2bb7c", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEe9BAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEe9BAAAAAAAAA==/", "_etag": "\"97006609-0000-0100-0000-686fcfa10000\"", "_attachments": "attachments/", "_ts": 1752158113}, {"payPeriodId": "1090071819953873", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-09-15T00:00:00Z", "endDate": "2025-09-21T00:00:00Z", "submitByDate": "2025-10-03T00:00:00Z", "checkDate": "2025-10-07T00:00:00Z", "checkCount": 0, "id": "30f26e67-a295-44f1-85a8-062a52256ebb", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEe+BAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEe+BAAAAAAAAA==/", "_etag": "\"97006a09-0000-0100-0000-686fcfa20000\"", "_attachments": "attachments/", "_ts": 1752158114}, {"payPeriodId": "1090071983067164", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-09-22T00:00:00Z", "endDate": "2025-09-28T00:00:00Z", "submitByDate": "2025-10-10T00:00:00Z", "checkDate": "2025-10-14T00:00:00Z", "checkCount": 0, "id": "286c7536-c70f-4837-87c7-826ee45ac258", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEe-BAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEe-BAAAAAAAAA==/", "_etag": "\"97006e09-0000-0100-0000-686fcfa20000\"", "_attachments": "attachments/", "_ts": 1752158114}, {"payPeriodId": "1090071423493061", "intervalCode": "WEEKLY", "status": "ENTRY", "description": "Weekly Payroll (1)", "startDate": "2025-06-09T00:00:00Z", "endDate": "2025-06-15T00:00:00Z", "submitByDate": "2025-06-27T00:00:00Z", "checkDate": "2025-07-01T00:00:00Z", "checkCount": 12, "id": "99bb5baf-fa37-4034-b5ad-07c3c4c4a41d", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEfABAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfABAAAAAAAAA==/", "_etag": "\"97007109-0000-0100-0000-686fcfa20000\"", "_attachments": "attachments/", "_ts": 1752158114}, {"payPeriodId": "1090071423493049", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-05-12T00:00:00Z", "endDate": "2025-05-18T00:00:00Z", "submitByDate": "2025-05-30T00:00:00Z", "checkDate": "2025-06-03T00:00:00Z", "checkCount": 0, "id": "14b2bc8a-9f6f-4a7a-a95b-a5b526e003c1", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEfV9gAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfV9gAAAAAAAA==/", "_etag": "\"9a0046f7-0000-0100-0000-686fe52e0000\"", "_attachments": "attachments/", "_ts": 1752163630}, {"payPeriodId": "1080040511250850", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-04-07T00:00:00Z", "endDate": "2025-04-13T00:00:00Z", "submitByDate": "2025-04-25T00:00:00Z", "checkDate": "2025-05-01T00:00:00Z", "checkCount": 0, "id": "089db79a-6f86-47f4-a069-28582e3e4ada", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEfW9gAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfW9gAAAAAAAA==/", "_etag": "\"9a0047f7-0000-0100-0000-686fe52e0000\"", "_attachments": "attachments/", "_ts": 1752163630}, {"payPeriodId": "1080040450437246", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-05-05T00:00:00Z", "endDate": "2025-05-11T00:00:00Z", "submitByDate": "2025-05-22T00:00:00Z", "checkDate": "2025-05-27T00:00:00Z", "checkCount": 0, "id": "670d1d5a-8c91-4401-858f-1e64f4049a6a", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEfX9gAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfX9gAAAAAAAA==/", "_etag": "\"9a0048f7-0000-0100-0000-686fe52e0000\"", "_attachments": "attachments/", "_ts": 1752163630}, {"payPeriodId": "1080040385987922", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-03-24T00:00:00Z", "endDate": "2025-03-30T00:00:00Z", "submitByDate": "2025-04-09T00:00:00Z", "checkDate": "2025-04-15T00:00:00Z", "checkCount": 0, "id": "79a7de68-c613-4a77-8ec6-760f6903b8df", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEfY9gAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfY9gAAAAAAAA==/", "_etag": "\"9a0049f7-0000-0100-0000-686fe52e0000\"", "_attachments": "attachments/", "_ts": 1752163630}, {"payPeriodId": "1080039866034570", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-01-06T00:00:00Z", "endDate": "2025-01-12T00:00:00Z", "submitByDate": "2025-01-22T00:00:00Z", "checkDate": "2025-01-29T00:00:00Z", "checkCount": 28, "id": "26865a5c-3239-4789-9d56-ff2a1a01ef2e", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEfZ9gAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfZ9gAAAAAAAA==/", "_etag": "\"9a004af7-0000-0100-0000-686fe52e0000\"", "_attachments": "attachments/", "_ts": 1752163630}, {"payPeriodId": "1080040180670341", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-02-24T00:00:00Z", "endDate": "2025-03-02T00:00:00Z", "submitByDate": "2025-03-12T00:00:00Z", "checkDate": "2025-03-18T00:00:00Z", "checkCount": 30, "id": "1b839973-76bf-42f0-839c-591e6869d92c", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEfa9gAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfa9gAAAAAAAA==/", "_etag": "\"9a004bf7-0000-0100-0000-686fe52e0000\"", "_attachments": "attachments/", "_ts": 1752163630}, {"payPeriodId": "1080039238323342", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-01-20T00:00:00Z", "endDate": "2025-01-26T00:00:00Z", "submitByDate": "2025-02-05T00:00:00Z", "checkDate": "2025-02-07T00:00:00Z", "checkCount": 30, "id": "594d7860-ec3a-407a-add0-7ba054bbeb2c", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEfb9gAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfb9gAAAAAAAA==/", "_etag": "\"9a004cf7-0000-0100-0000-686fe52e0000\"", "_attachments": "attachments/", "_ts": 1752163630}, {"payPeriodId": "1080040450436682", "status": "INITIAL", "description": "<PERSON>", "startDate": "2025-03-31T00:00:00Z", "endDate": "2025-04-06T00:00:00Z", "submitByDate": "2025-04-23T00:00:00Z", "checkDate": "2025-04-25T00:00:00Z", "checkCount": 0, "id": "3ae10edd-327b-4266-9d22-c6e4e06f65f5", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEfc9gAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfc9gAAAAAAAA==/", "_etag": "\"9a004df7-0000-0100-0000-686fe52e0000\"", "_attachments": "attachments/", "_ts": 1752163630}, {"payPeriodId": "1080039415135065", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-02-10T00:00:00Z", "endDate": "2025-02-16T00:00:00Z", "submitByDate": "2025-02-26T00:00:00Z", "checkDate": "2025-02-28T00:00:00Z", "checkCount": 31, "id": "83feb5ad-cf10-4217-9d87-145a7ff614a5", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEfd9gAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfd9gAAAAAAAA==/", "_etag": "\"9a004ef7-0000-0100-0000-686fe52e0000\"", "_attachments": "attachments/", "_ts": 1752163630}, {"payPeriodId": "1080039812849907", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2024-12-30T00:00:00Z", "endDate": "2025-01-05T00:00:00Z", "submitByDate": "2025-01-15T00:00:00Z", "checkDate": "2025-01-21T00:00:00Z", "checkCount": 29, "id": "a887af24-eb9e-497e-b446-2ec87f6ba46b", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEfe9gAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfe9gAAAAAAAA==/", "_etag": "\"9a004ff7-0000-0100-0000-686fe52e0000\"", "_attachments": "attachments/", "_ts": 1752163630}, {"payPeriodId": "1090071493994121", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-08-18T00:00:00Z", "endDate": "2025-08-24T00:00:00Z", "submitByDate": "2025-09-05T00:00:00Z", "checkDate": "2025-09-09T00:00:00Z", "checkCount": 0, "id": "8791d365-6778-4692-af3d-e0efb81fc6c3", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEff9gAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEff9gAAAAAAAA==/", "_etag": "\"9a0051f7-0000-0100-0000-686fe52e0000\"", "_attachments": "attachments/", "_ts": 1752163630}, {"payPeriodId": "1090071612860167", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-09-08T00:00:00Z", "endDate": "2025-09-14T00:00:00Z", "submitByDate": "2025-09-26T00:00:00Z", "checkDate": "2025-09-30T00:00:00Z", "checkCount": 0, "id": "82a2919f-b4e5-4128-ac24-4cdd8f624d3b", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEfg9gAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfg9gAAAAAAAA==/", "_etag": "\"9a0052f7-0000-0100-0000-686fe52e0000\"", "_attachments": "attachments/", "_ts": 1752163630}, {"payPeriodId": "1090071493994123", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-09-01T00:00:00Z", "endDate": "2025-09-07T00:00:00Z", "submitByDate": "2025-09-19T00:00:00Z", "checkDate": "2025-09-23T00:00:00Z", "checkCount": 0, "id": "8a0801d3-a040-43d0-8b0f-28cbdee07947", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEfh9gAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfh9gAAAAAAAA==/", "_etag": "\"9a0053f7-0000-0100-0000-686fe52e0000\"", "_attachments": "attachments/", "_ts": 1752163630}, {"payPeriodId": "1090071423493061", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-06-09T00:00:00Z", "endDate": "2025-06-15T00:00:00Z", "submitByDate": "2025-06-27T00:00:00Z", "checkDate": "2025-07-01T00:00:00Z", "checkCount": 0, "id": "f690ffdd-12be-4937-b0a3-24bf64d15d55", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEfi9gAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfi9gAAAAAAAA==/", "_etag": "\"9a0054f7-0000-0100-0000-686fe52e0000\"", "_attachments": "attachments/", "_ts": 1752163630}, {"payPeriodId": "1090071983067164", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-09-22T00:00:00Z", "endDate": "2025-09-28T00:00:00Z", "submitByDate": "2025-10-10T00:00:00Z", "checkDate": "2025-10-14T00:00:00Z", "checkCount": 0, "id": "1a695b9a-dc03-4246-9a5e-5799b8edab3e", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEfj9gAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfj9gAAAAAAAA==/", "_etag": "\"9a0055f7-0000-0100-0000-686fe52e0000\"", "_attachments": "attachments/", "_ts": 1752163630}, {"payPeriodId": "1090071819953873", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-09-15T00:00:00Z", "endDate": "2025-09-21T00:00:00Z", "submitByDate": "2025-10-03T00:00:00Z", "checkDate": "2025-10-07T00:00:00Z", "checkCount": 0, "id": "5c8c0507-85c6-4973-93f0-65b19882488c", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEfk9gAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfk9gAAAAAAAA==/", "_etag": "\"9a0056f7-0000-0100-0000-686fe52e0000\"", "_attachments": "attachments/", "_ts": 1752163630}, {"payPeriodId": "1090071493994122", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-08-25T00:00:00Z", "endDate": "2025-08-31T00:00:00Z", "submitByDate": "2025-09-12T00:00:00Z", "checkDate": "2025-09-16T00:00:00Z", "checkCount": 0, "id": "87a3951e-9307-49e2-ba74-77ad4a59bb78", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEfl9gAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfl9gAAAAAAAA==/", "_etag": "\"9a0057f7-0000-0100-0000-686fe52e0000\"", "_attachments": "attachments/", "_ts": 1752163630}, {"payPeriodId": "1090071493994120", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-08-11T00:00:00Z", "endDate": "2025-08-17T00:00:00Z", "submitByDate": "2025-08-28T00:00:00Z", "checkDate": "2025-09-02T00:00:00Z", "checkCount": 0, "id": "4bab2b30-20a2-4139-822f-5b9275db435e", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEfm9gAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfm9gAAAAAAAA==/", "_etag": "\"9a0058f7-0000-0100-0000-686fe52e0000\"", "_attachments": "attachments/", "_ts": 1752163630}, {"payPeriodId": "1090071423493085", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-08-04T00:00:00Z", "endDate": "2025-08-10T00:00:00Z", "submitByDate": "2025-08-22T00:00:00Z", "checkDate": "2025-08-26T00:00:00Z", "checkCount": 0, "id": "d29ce19f-9f5f-4f74-baba-5a2f389fd536", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEfn9gAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfn9gAAAAAAAA==/", "_etag": "\"9a0059f7-0000-0100-0000-686fe52e0000\"", "_attachments": "attachments/", "_ts": 1752163630}, {"payPeriodId": "1080040220825478", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-03-03T00:00:00Z", "endDate": "2025-03-09T00:00:00Z", "submitByDate": "2025-03-19T00:00:00Z", "checkDate": "2025-03-25T00:00:00Z", "checkCount": 30, "id": "8bd42d9e-0217-44f6-9c1b-9e22a7299d1f", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEfo9gAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfo9gAAAAAAAA==/", "_etag": "\"9a005cf7-0000-0100-0000-686fe52f0000\"", "_attachments": "attachments/", "_ts": 1752163631}, {"payPeriodId": "1080040620621582", "status": "INITIAL", "description": "<PERSON>", "startDate": "2025-04-21T00:00:00Z", "endDate": "2025-04-27T00:00:00Z", "submitByDate": "2025-05-15T00:00:00Z", "checkDate": "2025-05-16T00:00:00Z", "checkCount": 0, "id": "5c23805c-d591-409f-940c-2352d6114cc2", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEfp9gAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfp9gAAAAAAAA==/", "_etag": "\"9a005df7-0000-0100-0000-686fe52f0000\"", "_attachments": "attachments/", "_ts": 1752163631}, {"payPeriodId": "1080040335270371", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-03-17T00:00:00Z", "endDate": "2025-03-23T00:00:00Z", "submitByDate": "2025-04-02T00:00:00Z", "checkDate": "2025-04-08T00:00:00Z", "checkCount": 0, "id": "d80b3046-61a8-4670-a15b-4dff4ab32342", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEfq9gAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfq9gAAAAAAAA==/", "_etag": "\"9a005ef7-0000-0100-0000-686fe52f0000\"", "_attachments": "attachments/", "_ts": 1752163631}, {"payPeriodId": "1090071423493058", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-06-02T00:00:00Z", "endDate": "2025-06-08T00:00:00Z", "submitByDate": "2025-06-20T00:00:00Z", "checkDate": "2025-06-24T00:00:00Z", "checkCount": 0, "id": "7c3b688f-ff86-4058-89d6-a308e5f28290", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEfr9gAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfr9gAAAAAAAA==/", "_etag": "\"9a005ff7-0000-0100-0000-686fe52f0000\"", "_attachments": "attachments/", "_ts": 1752163631}, {"payPeriodId": "1090071423493052", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-05-19T00:00:00Z", "endDate": "2025-05-25T00:00:00Z", "submitByDate": "2025-06-06T00:00:00Z", "checkDate": "2025-06-10T00:00:00Z", "checkCount": 0, "id": "93d28fc1-a3ae-45f8-a84e-ca861e9135a6", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEfs9gAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfs9gAAAAAAAA==/", "_etag": "\"9a0060f7-0000-0100-0000-686fe52f0000\"", "_attachments": "attachments/", "_ts": 1752163631}, {"payPeriodId": "1080040450437243", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-04-28T00:00:00Z", "endDate": "2025-05-04T00:00:00Z", "submitByDate": "2025-05-16T00:00:00Z", "checkDate": "2025-05-20T00:00:00Z", "checkCount": 0, "id": "71dd23a6-c18b-4ec6-8262-c04765ec2b88", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEft9gAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEft9gAAAAAAAA==/", "_etag": "\"9a0061f7-0000-0100-0000-686fe52f0000\"", "_attachments": "attachments/", "_ts": 1752163631}, {"payPeriodId": "1090071423493082", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-07-28T00:00:00Z", "endDate": "2025-08-03T00:00:00Z", "submitByDate": "2025-08-15T00:00:00Z", "checkDate": "2025-08-19T00:00:00Z", "checkCount": 0, "id": "02f337d2-c0dc-4f8c-adc2-2dbf0504f861", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEfu9gAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfu9gAAAAAAAA==/", "_etag": "\"9a0062f7-0000-0100-0000-686fe52f0000\"", "_attachments": "attachments/", "_ts": 1752163631}, {"payPeriodId": "1090071423493073", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-07-07T00:00:00Z", "endDate": "2025-07-13T00:00:00Z", "submitByDate": "2025-07-25T00:00:00Z", "checkDate": "2025-07-29T00:00:00Z", "checkCount": 0, "id": "97eba925-237e-4ea5-9356-8aa5b6fa77ac", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEfv9gAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfv9gAAAAAAAA==/", "_etag": "\"9a0063f7-0000-0100-0000-686fe52f0000\"", "_attachments": "attachments/", "_ts": 1752163631}, {"payPeriodId": "1090071423493076", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-07-14T00:00:00Z", "endDate": "2025-07-20T00:00:00Z", "submitByDate": "2025-08-01T00:00:00Z", "checkDate": "2025-08-05T00:00:00Z", "checkCount": 0, "id": "c183726b-12fb-4cbd-aae1-3e6e7203920f", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEfw9gAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfw9gAAAAAAAA==/", "_etag": "\"9a0064f7-0000-0100-0000-686fe52f0000\"", "_attachments": "attachments/", "_ts": 1752163631}, {"payPeriodId": "1090071423493064", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-06-16T00:00:00Z", "endDate": "2025-06-22T00:00:00Z", "submitByDate": "2025-07-03T00:00:00Z", "checkDate": "2025-07-08T00:00:00Z", "checkCount": 0, "id": "9479e0d6-c6bf-472c-a531-c76fe1b2ec8e", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEfx9gAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfx9gAAAAAAAA==/", "_etag": "\"9a0065f7-0000-0100-0000-686fe52f0000\"", "_attachments": "attachments/", "_ts": 1752163631}, {"payPeriodId": "1080040129804289", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-02-17T00:00:00Z", "endDate": "2025-02-23T00:00:00Z", "submitByDate": "2025-03-05T00:00:00Z", "checkDate": "2025-03-11T00:00:00Z", "checkCount": 30, "id": "a060a48a-2b0f-4608-9169-112ad6fd94d9", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEfy9gAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfy9gAAAAAAAA==/", "_etag": "\"9a0066f7-0000-0100-0000-686fe52f0000\"", "_attachments": "attachments/", "_ts": 1752163631}, {"payPeriodId": "1090071423493070", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-06-30T00:00:00Z", "endDate": "2025-07-06T00:00:00Z", "submitByDate": "2025-07-18T00:00:00Z", "checkDate": "2025-07-22T00:00:00Z", "checkCount": 0, "id": "3586c756-73b2-473d-bbd7-3b393e3d2f36", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEfz9gAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfz9gAAAAAAAA==/", "_etag": "\"9a0067f7-0000-0100-0000-686fe52f0000\"", "_attachments": "attachments/", "_ts": 1752163631}, {"payPeriodId": "1080040278492591", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-03-10T00:00:00Z", "endDate": "2025-03-16T00:00:00Z", "submitByDate": "2025-03-26T00:00:00Z", "checkDate": "2025-04-01T00:00:00Z", "checkCount": 0, "id": "9bb9e178-2228-48bf-9704-4017cf257f0b", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEf09gAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf09gAAAAAAAA==/", "_etag": "\"9a0068f7-0000-0100-0000-686fe52f0000\"", "_attachments": "attachments/", "_ts": 1752163631}, {"payPeriodId": "1080039322503869", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-01-27T00:00:00Z", "endDate": "2025-02-02T00:00:00Z", "submitByDate": "2025-02-12T00:00:00Z", "checkDate": "2025-02-14T00:00:00Z", "checkCount": 31, "id": "1ad37a1a-3594-4efd-b736-0aed339e3b43", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEf19gAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf19gAAAAAAAA==/", "_etag": "\"9a0069f7-0000-0100-0000-686fe52f0000\"", "_attachments": "attachments/", "_ts": 1752163631}, {"payPeriodId": "1090071423493079", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-07-21T00:00:00Z", "endDate": "2025-07-27T00:00:00Z", "submitByDate": "2025-08-08T00:00:00Z", "checkDate": "2025-08-12T00:00:00Z", "checkCount": 0, "id": "0bd73b80-84de-4a20-8ee4-22bba47adf15", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEf29gAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf29gAAAAAAAA==/", "_etag": "\"9a006af7-0000-0100-0000-686fe52f0000\"", "_attachments": "attachments/", "_ts": 1752163631}, {"payPeriodId": "1080040561759779", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-04-14T00:00:00Z", "endDate": "2025-04-20T00:00:00Z", "submitByDate": "2025-05-02T00:00:00Z", "checkDate": "2025-05-08T00:00:00Z", "checkCount": 0, "id": "8c50eb32-011b-409b-8657-e4e5fb5ab8c0", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEf39gAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf39gAAAAAAAA==/", "_etag": "\"9a006bf7-0000-0100-0000-686fe52f0000\"", "_attachments": "attachments/", "_ts": 1752163631}, {"payPeriodId": "1080040620622346", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-04-21T00:00:00Z", "endDate": "2025-04-27T00:00:00Z", "submitByDate": "2025-05-09T00:00:00Z", "checkDate": "2025-05-15T00:00:00Z", "checkCount": 0, "id": "743b00a2-55bc-421b-befb-afdfaa778a99", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEf49gAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf49gAAAAAAAA==/", "_etag": "\"9a006cf7-0000-0100-0000-686fe52f0000\"", "_attachments": "attachments/", "_ts": 1752163631}, {"payPeriodId": "1090071423493055", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-05-26T00:00:00Z", "endDate": "2025-06-01T00:00:00Z", "submitByDate": "2025-06-13T00:00:00Z", "checkDate": "2025-06-17T00:00:00Z", "checkCount": 0, "id": "9529cbb0-4989-4608-973f-321b357ea323", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEf59gAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf59gAAAAAAAA==/", "_etag": "\"9a006df7-0000-0100-0000-686fe52f0000\"", "_attachments": "attachments/", "_ts": 1752163631}, {"payPeriodId": "1080040033496426", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-02-03T00:00:00Z", "endDate": "2025-02-09T00:00:00Z", "submitByDate": "2025-02-19T00:00:00Z", "checkDate": "2025-02-25T00:00:00Z", "checkCount": 30, "id": "8b583da5-7a79-4669-b73f-45c8bfe45d7c", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEf69gAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf69gAAAAAAAA==/", "_etag": "\"9a006ef7-0000-0100-0000-686fe52f0000\"", "_attachments": "attachments/", "_ts": 1752163631}, {"payPeriodId": "1090071423493067", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-06-23T00:00:00Z", "endDate": "2025-06-29T00:00:00Z", "submitByDate": "2025-07-11T00:00:00Z", "checkDate": "2025-07-15T00:00:00Z", "checkCount": 0, "id": "b1272fab-d859-4926-9821-95213ef8aaeb", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEf79gAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf79gAAAAAAAA==/", "_etag": "\"9a006ff7-0000-0100-0000-686fe52f0000\"", "_attachments": "attachments/", "_ts": 1752163631}, {"payPeriodId": "1080039906934417", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-01-13T00:00:00Z", "endDate": "2025-01-19T00:00:00Z", "submitByDate": "2025-01-29T00:00:00Z", "checkDate": "2025-02-04T00:00:00Z", "checkCount": 30, "id": "fd16abf9-8ad1-4a74-ad93-434fe7b9e6b1", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEf89gAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf89gAAAAAAAA==/", "_etag": "\"9a0070f7-0000-0100-0000-686fe52f0000\"", "_attachments": "attachments/", "_ts": 1752163631}, {"payPeriodId": "1080040450436724", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-03-31T00:00:00Z", "endDate": "2025-04-06T00:00:00Z", "submitByDate": "2025-04-16T00:00:00Z", "checkDate": "2025-04-24T00:00:00Z", "checkCount": 0, "id": "d2bdf28e-dad2-4517-941c-32eec54889e9", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEf99gAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf99gAAAAAAAA==/", "_etag": "\"9a0071f7-0000-0100-0000-686fe52f0000\"", "_attachments": "attachments/", "_ts": 1752163631}, {"payPeriodId": "1090071423493082", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-07-28T00:00:00Z", "endDate": "2025-08-03T00:00:00Z", "submitByDate": "2025-08-15T00:00:00Z", "checkDate": "2025-08-19T00:00:00Z", "checkCount": 0, "id": "947e17b8-03e9-4da9-aa44-f6b7e60eb78a", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEd89wAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEd89wAAAAAAAA==/", "_etag": "\"9a00b3f8-0000-0100-0000-686fe5350000\"", "_attachments": "attachments/", "_ts": 1752163637}, {"payPeriodId": "1080040278492591", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-03-10T00:00:00Z", "endDate": "2025-03-16T00:00:00Z", "submitByDate": "2025-03-26T00:00:00Z", "checkDate": "2025-04-01T00:00:00Z", "checkCount": 30, "id": "9bc3a6bf-e3d9-4bb2-9f49-817cfd8c7862", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEd99wAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEd99wAAAAAAAA==/", "_etag": "\"9a00b4f8-0000-0100-0000-686fe5350000\"", "_attachments": "attachments/", "_ts": 1752163637}, {"payPeriodId": "1080040450436724", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-03-31T00:00:00Z", "endDate": "2025-04-06T00:00:00Z", "submitByDate": "2025-04-16T00:00:00Z", "checkDate": "2025-04-24T00:00:00Z", "checkCount": 28, "id": "9cea4319-148d-4f15-8f41-8a8673694236", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEd+9wAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEd+9wAAAAAAAA==/", "_etag": "\"9a00b5f8-0000-0100-0000-686fe5350000\"", "_attachments": "attachments/", "_ts": 1752163637}, {"payPeriodId": "1080040180670341", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-02-24T00:00:00Z", "endDate": "2025-03-02T00:00:00Z", "submitByDate": "2025-03-12T00:00:00Z", "checkDate": "2025-03-18T00:00:00Z", "checkCount": 30, "id": "6c2bfc7d-659e-4a57-9f28-f6c1b00c3906", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEd-9wAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEd-9wAAAAAAAA==/", "_etag": "\"9a00b6f8-0000-0100-0000-686fe5350000\"", "_attachments": "attachments/", "_ts": 1752163637}, {"payPeriodId": "1080040450437246", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-05-05T00:00:00Z", "endDate": "2025-05-11T00:00:00Z", "submitByDate": "2025-05-22T00:00:00Z", "checkDate": "2025-05-27T00:00:00Z", "checkCount": 25, "id": "a817ef68-6866-4f8a-945e-4d4b9f5940aa", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEeA9wAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeA9wAAAAAAAA==/", "_etag": "\"9a00b7f8-0000-0100-0000-686fe5350000\"", "_attachments": "attachments/", "_ts": 1752163637}, {"payPeriodId": "1080040511250850", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-04-07T00:00:00Z", "endDate": "2025-04-13T00:00:00Z", "submitByDate": "2025-04-25T00:00:00Z", "checkDate": "2025-05-01T00:00:00Z", "checkCount": 20, "id": "8c620f7d-6ace-41d9-9c3d-11e6a78c8336", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEeB9wAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeB9wAAAAAAAA==/", "_etag": "\"9a00b8f8-0000-0100-0000-686fe5350000\"", "_attachments": "attachments/", "_ts": 1752163637}, {"payPeriodId": "1080040033496426", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-02-03T00:00:00Z", "endDate": "2025-02-09T00:00:00Z", "submitByDate": "2025-02-19T00:00:00Z", "checkDate": "2025-02-25T00:00:00Z", "checkCount": 30, "id": "85e891cf-6976-48e8-9f09-bd6c9452857a", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEeC9wAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeC9wAAAAAAAA==/", "_etag": "\"9a00b9f8-0000-0100-0000-686fe5350000\"", "_attachments": "attachments/", "_ts": 1752163637}, {"payPeriodId": "1080040620621582", "status": "COMPLETED", "description": "<PERSON>", "startDate": "2025-04-21T00:00:00Z", "endDate": "2025-04-27T00:00:00Z", "submitByDate": "2025-05-15T00:00:00Z", "checkDate": "2025-05-16T00:00:00Z", "checkCount": 1, "id": "a94ab310-b32c-4898-a361-aa5f8a2ab23e", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEeD9wAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeD9wAAAAAAAA==/", "_etag": "\"9a00baf8-0000-0100-0000-686fe5350000\"", "_attachments": "attachments/", "_ts": 1752163637}, {"payPeriodId": "1080039238323342", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-01-20T00:00:00Z", "endDate": "2025-01-26T00:00:00Z", "submitByDate": "2025-02-05T00:00:00Z", "checkDate": "2025-02-07T00:00:00Z", "checkCount": 30, "id": "2cff1c7d-5865-473a-8604-88ea8b4c4d4c", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEeE9wAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeE9wAAAAAAAA==/", "_etag": "\"9a00bbf8-0000-0100-0000-686fe5350000\"", "_attachments": "attachments/", "_ts": 1752163637}, {"payPeriodId": "1080039415135065", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-02-10T00:00:00Z", "endDate": "2025-02-16T00:00:00Z", "submitByDate": "2025-02-26T00:00:00Z", "checkDate": "2025-02-28T00:00:00Z", "checkCount": 31, "id": "0d79979e-2ec6-474a-92f9-a23c0fd8e8ff", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEeF9wAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeF9wAAAAAAAA==/", "_etag": "\"9a00bcf8-0000-0100-0000-686fe5350000\"", "_attachments": "attachments/", "_ts": 1752163637}, {"payPeriodId": "1090071493994121", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-08-18T00:00:00Z", "endDate": "2025-08-24T00:00:00Z", "submitByDate": "2025-09-05T00:00:00Z", "checkDate": "2025-09-09T00:00:00Z", "checkCount": 0, "id": "6f7b4225-3514-4583-8653-413faf6378ef", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEeG9wAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeG9wAAAAAAAA==/", "_etag": "\"9a00c0f8-0000-0100-0000-686fe5350000\"", "_attachments": "attachments/", "_ts": 1752163637}, {"payPeriodId": "1090071493994120", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-08-11T00:00:00Z", "endDate": "2025-08-17T00:00:00Z", "submitByDate": "2025-08-28T00:00:00Z", "checkDate": "2025-09-02T00:00:00Z", "checkCount": 0, "id": "31efeeda-d97a-4b1b-9360-e03b45268b8b", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEeH9wAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeH9wAAAAAAAA==/", "_etag": "\"9a00c1f8-0000-0100-0000-686fe5350000\"", "_attachments": "attachments/", "_ts": 1752163637}, {"payPeriodId": "1090071423493061", "intervalCode": "WEEKLY", "status": "ENTRY", "description": "Weekly Payroll (1)", "startDate": "2025-06-09T00:00:00Z", "endDate": "2025-06-15T00:00:00Z", "submitByDate": "2025-06-27T00:00:00Z", "checkDate": "2025-07-01T00:00:00Z", "checkCount": 12, "id": "750eb92a-18b2-49d2-9617-e87b3459dee0", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEeI9wAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeI9wAAAAAAAA==/", "_etag": "\"9a00c2f8-0000-0100-0000-686fe5350000\"", "_attachments": "attachments/", "_ts": 1752163637}, {"payPeriodId": "1090071493994122", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-08-25T00:00:00Z", "endDate": "2025-08-31T00:00:00Z", "submitByDate": "2025-09-12T00:00:00Z", "checkDate": "2025-09-16T00:00:00Z", "checkCount": 0, "id": "3cdae4d1-fa1b-4b45-b459-245514e8a557", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEeJ9wAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeJ9wAAAAAAAA==/", "_etag": "\"9a00c3f8-0000-0100-0000-686fe5350000\"", "_attachments": "attachments/", "_ts": 1752163637}, {"payPeriodId": "1090071612860167", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-09-08T00:00:00Z", "endDate": "2025-09-14T00:00:00Z", "submitByDate": "2025-09-26T00:00:00Z", "checkDate": "2025-09-30T00:00:00Z", "checkCount": 0, "id": "d2e6f90d-c7f2-4f17-b06e-925a07310b20", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEeK9wAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeK9wAAAAAAAA==/", "_etag": "\"9a00c4f8-0000-0100-0000-686fe5350000\"", "_attachments": "attachments/", "_ts": 1752163637}, {"payPeriodId": "1090071819953873", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-09-15T00:00:00Z", "endDate": "2025-09-21T00:00:00Z", "submitByDate": "2025-10-03T00:00:00Z", "checkDate": "2025-10-07T00:00:00Z", "checkCount": 0, "id": "ba854262-1ee6-413f-9f28-21261bb96ef7", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEeL9wAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeL9wAAAAAAAA==/", "_etag": "\"9a00c5f8-0000-0100-0000-686fe5350000\"", "_attachments": "attachments/", "_ts": 1752163637}, {"payPeriodId": "1090071423493085", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-08-04T00:00:00Z", "endDate": "2025-08-10T00:00:00Z", "submitByDate": "2025-08-22T00:00:00Z", "checkDate": "2025-08-26T00:00:00Z", "checkCount": 0, "id": "50e73db7-45a9-4c5f-8c9c-83c56d70f936", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEeM9wAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeM9wAAAAAAAA==/", "_etag": "\"9a00c6f8-0000-0100-0000-686fe5350000\"", "_attachments": "attachments/", "_ts": 1752163637}, {"payPeriodId": "1090071493994123", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-09-01T00:00:00Z", "endDate": "2025-09-07T00:00:00Z", "submitByDate": "2025-09-19T00:00:00Z", "checkDate": "2025-09-23T00:00:00Z", "checkCount": 0, "id": "24df248d-6989-4bba-97aa-a467563733bd", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEeN9wAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeN9wAAAAAAAA==/", "_etag": "\"9a00c7f8-0000-0100-0000-686fe5350000\"", "_attachments": "attachments/", "_ts": 1752163637}, {"payPeriodId": "1090071983067164", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-09-22T00:00:00Z", "endDate": "2025-09-28T00:00:00Z", "submitByDate": "2025-10-10T00:00:00Z", "checkDate": "2025-10-14T00:00:00Z", "checkCount": 0, "id": "016bd037-4521-47c8-aa5d-5b75c6edce1a", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEeO9wAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeO9wAAAAAAAA==/", "_etag": "\"9a00c8f8-0000-0100-0000-686fe5350000\"", "_attachments": "attachments/", "_ts": 1752163637}, {"payPeriodId": "1080039812849907", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2024-12-30T00:00:00Z", "endDate": "2025-01-05T00:00:00Z", "submitByDate": "2025-01-15T00:00:00Z", "checkDate": "2025-01-21T00:00:00Z", "checkCount": 29, "id": "11bd8814-6a7d-4f91-9000-3d7df4a26ab9", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEeP9wAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeP9wAAAAAAAA==/", "_etag": "\"9a00cff8-0000-0100-0000-686fe5350000\"", "_attachments": "attachments/", "_ts": 1752163637}, {"payPeriodId": "1090071423493076", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-07-14T00:00:00Z", "endDate": "2025-07-20T00:00:00Z", "submitByDate": "2025-08-01T00:00:00Z", "checkDate": "2025-08-05T00:00:00Z", "checkCount": 0, "id": "ac7c70b4-f5a1-4cdb-9241-8937bd5decb2", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEeQ9wAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeQ9wAAAAAAAA==/", "_etag": "\"9a00d0f8-0000-0100-0000-686fe5350000\"", "_attachments": "attachments/", "_ts": 1752163637}, {"payPeriodId": "1090071423493058", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-06-02T00:00:00Z", "endDate": "2025-06-08T00:00:00Z", "submitByDate": "2025-06-20T00:00:00Z", "checkDate": "2025-06-24T00:00:00Z", "checkCount": 25, "id": "7a197d84-9f67-4724-8d61-e09e42b196bc", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEeR9wAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeR9wAAAAAAAA==/", "_etag": "\"9a00d1f8-0000-0100-0000-686fe5350000\"", "_attachments": "attachments/", "_ts": 1752163637}, {"payPeriodId": "1090071423493073", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-07-07T00:00:00Z", "endDate": "2025-07-13T00:00:00Z", "submitByDate": "2025-07-25T00:00:00Z", "checkDate": "2025-07-29T00:00:00Z", "checkCount": 0, "id": "0b4d362d-b235-4fea-9429-0baab756b539", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEeS9wAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeS9wAAAAAAAA==/", "_etag": "\"9a00d3f8-0000-0100-0000-686fe5350000\"", "_attachments": "attachments/", "_ts": 1752163637}, {"payPeriodId": "1080039322503869", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-01-27T00:00:00Z", "endDate": "2025-02-02T00:00:00Z", "submitByDate": "2025-02-12T00:00:00Z", "checkDate": "2025-02-14T00:00:00Z", "checkCount": 31, "id": "b4150c8d-09eb-4386-a221-88ce1f5681a5", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEeT9wAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeT9wAAAAAAAA==/", "_etag": "\"9a00d4f8-0000-0100-0000-686fe5350000\"", "_attachments": "attachments/", "_ts": 1752163637}, {"payPeriodId": "1080040335270371", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-03-17T00:00:00Z", "endDate": "2025-03-23T00:00:00Z", "submitByDate": "2025-04-02T00:00:00Z", "checkDate": "2025-04-08T00:00:00Z", "checkCount": 29, "id": "e4ecc029-c108-42dc-9b18-49d0f16d0a35", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEeU9wAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeU9wAAAAAAAA==/", "_etag": "\"9a00daf8-0000-0100-0000-686fe5350000\"", "_attachments": "attachments/", "_ts": 1752163637}, {"payPeriodId": "1080039866034570", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-01-06T00:00:00Z", "endDate": "2025-01-12T00:00:00Z", "submitByDate": "2025-01-22T00:00:00Z", "checkDate": "2025-01-29T00:00:00Z", "checkCount": 28, "id": "1847f211-e6f4-45cf-a075-448a7e3d1775", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEeV9wAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeV9wAAAAAAAA==/", "_etag": "\"9a00ddf8-0000-0100-0000-686fe5350000\"", "_attachments": "attachments/", "_ts": 1752163637}, {"payPeriodId": "1080040385987922", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-03-24T00:00:00Z", "endDate": "2025-03-30T00:00:00Z", "submitByDate": "2025-04-09T00:00:00Z", "checkDate": "2025-04-15T00:00:00Z", "checkCount": 28, "id": "e90adb91-5044-4bf2-a4ad-6639e5c3453d", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEeW9wAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeW9wAAAAAAAA==/", "_etag": "\"9a00e1f8-0000-0100-0000-686fe5350000\"", "_attachments": "attachments/", "_ts": 1752163637}, {"payPeriodId": "1090071423493055", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-05-26T00:00:00Z", "endDate": "2025-06-01T00:00:00Z", "submitByDate": "2025-06-13T00:00:00Z", "checkDate": "2025-06-17T00:00:00Z", "checkCount": 24, "id": "8794c74a-bb80-46c9-ae8c-c57de77a617f", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEeX9wAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeX9wAAAAAAAA==/", "_etag": "\"9a00eaf8-0000-0100-0000-686fe5360000\"", "_attachments": "attachments/", "_ts": 1752163638}, {"payPeriodId": "1080040561759779", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-04-14T00:00:00Z", "endDate": "2025-04-20T00:00:00Z", "submitByDate": "2025-05-02T00:00:00Z", "checkDate": "2025-05-08T00:00:00Z", "checkCount": 22, "id": "218d5ad6-d8a0-4275-9906-f14e4739ef6d", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEeY9wAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeY9wAAAAAAAA==/", "_etag": "\"9a00ebf8-0000-0100-0000-686fe5360000\"", "_attachments": "attachments/", "_ts": 1752163638}, {"payPeriodId": "1090071423493067", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-06-23T00:00:00Z", "endDate": "2025-06-29T00:00:00Z", "submitByDate": "2025-07-11T00:00:00Z", "checkDate": "2025-07-15T00:00:00Z", "checkCount": 0, "id": "cd5a5c83-81d8-48f6-a115-c9a0eb83b8c7", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEeZ9wAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeZ9wAAAAAAAA==/", "_etag": "\"9a00ecf8-0000-0100-0000-686fe5360000\"", "_attachments": "attachments/", "_ts": 1752163638}, {"payPeriodId": "1080040129804289", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-02-17T00:00:00Z", "endDate": "2025-02-23T00:00:00Z", "submitByDate": "2025-03-05T00:00:00Z", "checkDate": "2025-03-11T00:00:00Z", "checkCount": 30, "id": "7396aa76-8ca5-4d2c-b297-13cb767a1097", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEea9wAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEea9wAAAAAAAA==/", "_etag": "\"9a00edf8-0000-0100-0000-686fe5360000\"", "_attachments": "attachments/", "_ts": 1752163638}, {"payPeriodId": "1080040450436682", "status": "COMPLETED", "description": "<PERSON>", "startDate": "2025-03-31T00:00:00Z", "endDate": "2025-04-06T00:00:00Z", "submitByDate": "2025-04-23T00:00:00Z", "checkDate": "2025-04-25T00:00:00Z", "checkCount": 1, "id": "a68cccc1-0c1d-4a3d-9173-c27f35ca9147", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEeb9wAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeb9wAAAAAAAA==/", "_etag": "\"9a00eef8-0000-0100-0000-686fe5360000\"", "_attachments": "attachments/", "_ts": 1752163638}, {"payPeriodId": "1080040450437243", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-04-28T00:00:00Z", "endDate": "2025-05-04T00:00:00Z", "submitByDate": "2025-05-16T00:00:00Z", "checkDate": "2025-05-20T00:00:00Z", "checkCount": 25, "id": "3e6ceaad-7ec8-4ab8-95e9-9226fa859353", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEec9wAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEec9wAAAAAAAA==/", "_etag": "\"9a00eff8-0000-0100-0000-686fe5360000\"", "_attachments": "attachments/", "_ts": 1752163638}, {"payPeriodId": "1080040220825478", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-03-03T00:00:00Z", "endDate": "2025-03-09T00:00:00Z", "submitByDate": "2025-03-19T00:00:00Z", "checkDate": "2025-03-25T00:00:00Z", "checkCount": 30, "id": "ba3ab6a7-73cd-458a-98bc-4779673ccb6a", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEed9wAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEed9wAAAAAAAA==/", "_etag": "\"9a00f0f8-0000-0100-0000-686fe5360000\"", "_attachments": "attachments/", "_ts": 1752163638}, {"payPeriodId": "1090071423493052", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-05-19T00:00:00Z", "endDate": "2025-05-25T00:00:00Z", "submitByDate": "2025-06-06T00:00:00Z", "checkDate": "2025-06-10T00:00:00Z", "checkCount": 23, "id": "8b0a22c6-9fc8-406e-a5fd-4cf3ed720af0", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEee9wAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEee9wAAAAAAAA==/", "_etag": "\"9a00f1f8-0000-0100-0000-686fe5360000\"", "_attachments": "attachments/", "_ts": 1752163638}, {"payPeriodId": "1090071423493079", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-07-21T00:00:00Z", "endDate": "2025-07-27T00:00:00Z", "submitByDate": "2025-08-08T00:00:00Z", "checkDate": "2025-08-12T00:00:00Z", "checkCount": 0, "id": "56a07b13-24ec-4a1d-a5c9-3e75e6724a1f", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEef9wAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEef9wAAAAAAAA==/", "_etag": "\"9a00f2f8-0000-0100-0000-686fe5360000\"", "_attachments": "attachments/", "_ts": 1752163638}, {"payPeriodId": "1090071423493064", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-06-16T00:00:00Z", "endDate": "2025-06-22T00:00:00Z", "submitByDate": "2025-07-03T00:00:00Z", "checkDate": "2025-07-08T00:00:00Z", "checkCount": 0, "id": "f48ea229-807b-4a4a-83d0-c17afae77d29", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEeg9wAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeg9wAAAAAAAA==/", "_etag": "\"9a00f3f8-0000-0100-0000-686fe5360000\"", "_attachments": "attachments/", "_ts": 1752163638}, {"payPeriodId": "1090071423493049", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-05-12T00:00:00Z", "endDate": "2025-05-18T00:00:00Z", "submitByDate": "2025-05-30T00:00:00Z", "checkDate": "2025-06-03T00:00:00Z", "checkCount": 28, "id": "2fd84a09-d73c-4e44-9d6c-374f0ac9881b", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEeh9wAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEeh9wAAAAAAAA==/", "_etag": "\"9a00fdf8-0000-0100-0000-686fe5360000\"", "_attachments": "attachments/", "_ts": 1752163638}, {"payPeriodId": "1080040620622346", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-04-21T00:00:00Z", "endDate": "2025-04-27T00:00:00Z", "submitByDate": "2025-05-09T00:00:00Z", "checkDate": "2025-05-15T00:00:00Z", "checkCount": 29, "id": "c94a0a83-285f-430a-80cd-bc67404e100f", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEei9wAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEei9wAAAAAAAA==/", "_etag": "\"9a00fef8-0000-0100-0000-686fe5360000\"", "_attachments": "attachments/", "_ts": 1752163638}, {"payPeriodId": "1080039906934417", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-01-13T00:00:00Z", "endDate": "2025-01-19T00:00:00Z", "submitByDate": "2025-01-29T00:00:00Z", "checkDate": "2025-02-04T00:00:00Z", "checkCount": 30, "id": "ebe4ec6a-ecb9-48a9-8a17-3bb222fc2aec", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEej9wAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEej9wAAAAAAAA==/", "_etag": "\"9a00fff8-0000-0100-0000-686fe5360000\"", "_attachments": "attachments/", "_ts": 1752163638}, {"payPeriodId": "1090071423493070", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-06-30T00:00:00Z", "endDate": "2025-07-06T00:00:00Z", "submitByDate": "2025-07-18T00:00:00Z", "checkDate": "2025-07-22T00:00:00Z", "checkCount": 0, "id": "2faddb2b-db28-4be3-9bbe-385fb435a865", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEek9wAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEek9wAAAAAAAA==/", "_etag": "\"9a0001f9-0000-0100-0000-686fe5360000\"", "_attachments": "attachments/", "_ts": 1752163638}, {"payPeriodId": "1080039812849907", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2024-12-30T00:00:00Z", "endDate": "2025-01-05T00:00:00Z", "submitByDate": "2025-01-15T00:00:00Z", "checkDate": "2025-01-21T00:00:00Z", "checkCount": 29, "id": "b8544bbc-6912-4672-9e14-548e0c4a3583", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEce-QAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEce-QAAAAAAAA==/", "_etag": "\"9d00ed44-0000-0100-0000-686ff5c60000\"", "_attachments": "attachments/", "_ts": 1752167878}, {"payPeriodId": "1080039866034570", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-01-06T00:00:00Z", "endDate": "2025-01-12T00:00:00Z", "submitByDate": "2025-01-22T00:00:00Z", "checkDate": "2025-01-29T00:00:00Z", "checkCount": 28, "id": "c9f69cbf-6328-44e0-bc6a-cc801771aa40", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEcf-QAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcf-QAAAAAAAA==/", "_etag": "\"9d00f044-0000-0100-0000-686ff5c60000\"", "_attachments": "attachments/", "_ts": 1752167878}, {"payPeriodId": "1080039906934417", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-01-13T00:00:00Z", "endDate": "2025-01-19T00:00:00Z", "submitByDate": "2025-01-29T00:00:00Z", "checkDate": "2025-02-04T00:00:00Z", "checkCount": 30, "id": "31c2d3e0-6a3b-4f4e-b824-1aa1e522fa37", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEcg-QAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcg-QAAAAAAAA==/", "_etag": "\"9d00f244-0000-0100-0000-686ff5c60000\"", "_attachments": "attachments/", "_ts": 1752167878}, {"payPeriodId": "1080039238323342", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-01-20T00:00:00Z", "endDate": "2025-01-26T00:00:00Z", "submitByDate": "2025-02-05T00:00:00Z", "checkDate": "2025-02-07T00:00:00Z", "checkCount": 30, "id": "07ab9357-3dc0-48dd-88b2-68066a689a14", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEch-QAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEch-QAAAAAAAA==/", "_etag": "\"9d00f544-0000-0100-0000-686ff5c60000\"", "_attachments": "attachments/", "_ts": 1752167878}, {"payPeriodId": "1080039322503869", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-01-27T00:00:00Z", "endDate": "2025-02-02T00:00:00Z", "submitByDate": "2025-02-12T00:00:00Z", "checkDate": "2025-02-14T00:00:00Z", "checkCount": 31, "id": "9c089b89-7485-4cd1-aace-e19189704aec", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEci-QAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEci-QAAAAAAAA==/", "_etag": "\"9d00f844-0000-0100-0000-686ff5c70000\"", "_attachments": "attachments/", "_ts": 1752167879}, {"payPeriodId": "1080040033496426", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-02-03T00:00:00Z", "endDate": "2025-02-09T00:00:00Z", "submitByDate": "2025-02-19T00:00:00Z", "checkDate": "2025-02-25T00:00:00Z", "checkCount": 30, "id": "91d9727e-360f-4777-8c9a-1d0df4e2bf90", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEcj-QAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcj-QAAAAAAAA==/", "_etag": "\"9d00fa44-0000-0100-0000-686ff5c70000\"", "_attachments": "attachments/", "_ts": 1752167879}, {"payPeriodId": "1080039415135065", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-02-10T00:00:00Z", "endDate": "2025-02-16T00:00:00Z", "submitByDate": "2025-02-26T00:00:00Z", "checkDate": "2025-02-28T00:00:00Z", "checkCount": 31, "id": "97fbec37-b9a6-4c91-a50e-afe1696cf4e9", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEck-QAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEck-QAAAAAAAA==/", "_etag": "\"9d00fb44-0000-0100-0000-686ff5c70000\"", "_attachments": "attachments/", "_ts": 1752167879}, {"payPeriodId": "1080040129804289", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-02-17T00:00:00Z", "endDate": "2025-02-23T00:00:00Z", "submitByDate": "2025-03-05T00:00:00Z", "checkDate": "2025-03-11T00:00:00Z", "checkCount": 30, "id": "b3c969b6-8a96-4915-b6e6-7c505fd601a0", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEcl-QAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcl-QAAAAAAAA==/", "_etag": "\"9d000045-0000-0100-0000-686ff5c70000\"", "_attachments": "attachments/", "_ts": 1752167879}, {"payPeriodId": "1080040180670341", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-02-24T00:00:00Z", "endDate": "2025-03-02T00:00:00Z", "submitByDate": "2025-03-12T00:00:00Z", "checkDate": "2025-03-18T00:00:00Z", "checkCount": 30, "id": "0edc83f2-f181-4c79-a8de-17f84b89abe6", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEcm-QAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcm-QAAAAAAAA==/", "_etag": "\"9d000745-0000-0100-0000-686ff5c70000\"", "_attachments": "attachments/", "_ts": 1752167879}, {"payPeriodId": "1080040220825478", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-03-03T00:00:00Z", "endDate": "2025-03-09T00:00:00Z", "submitByDate": "2025-03-19T00:00:00Z", "checkDate": "2025-03-25T00:00:00Z", "checkCount": 30, "id": "5b73a5d6-d283-476d-8c22-77c17c315d78", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEcn-QAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcn-QAAAAAAAA==/", "_etag": "\"9d000c45-0000-0100-0000-686ff5c70000\"", "_attachments": "attachments/", "_ts": 1752167879}, {"payPeriodId": "1080040278492591", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-03-10T00:00:00Z", "endDate": "2025-03-16T00:00:00Z", "submitByDate": "2025-03-26T00:00:00Z", "checkDate": "2025-04-01T00:00:00Z", "checkCount": 0, "id": "7950c1b9-05e2-4384-8989-d2f69338ef14", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEco-QAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEco-QAAAAAAAA==/", "_etag": "\"9d000e45-0000-0100-0000-686ff5c70000\"", "_attachments": "attachments/", "_ts": 1752167879}, {"payPeriodId": "1080040335270371", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-03-17T00:00:00Z", "endDate": "2025-03-23T00:00:00Z", "submitByDate": "2025-04-02T00:00:00Z", "checkDate": "2025-04-08T00:00:00Z", "checkCount": 0, "id": "c80b0ebb-f082-428b-b65e-e32cea39caa1", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEcp-QAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcp-QAAAAAAAA==/", "_etag": "\"9d001145-0000-0100-0000-686ff5c70000\"", "_attachments": "attachments/", "_ts": 1752167879}, {"payPeriodId": "1080040385987922", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-03-24T00:00:00Z", "endDate": "2025-03-30T00:00:00Z", "submitByDate": "2025-04-09T00:00:00Z", "checkDate": "2025-04-15T00:00:00Z", "checkCount": 0, "id": "c6850030-0041-4c9c-8c2b-56ea588e4888", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEcq-QAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcq-QAAAAAAAA==/", "_etag": "\"9d001545-0000-0100-0000-686ff5c70000\"", "_attachments": "attachments/", "_ts": 1752167879}, {"payPeriodId": "1080040450436724", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-03-31T00:00:00Z", "endDate": "2025-04-06T00:00:00Z", "submitByDate": "2025-04-16T00:00:00Z", "checkDate": "2025-04-24T00:00:00Z", "checkCount": 0, "id": "de5a1653-b4c6-473e-a825-65edaffd9154", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEcr-QAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcr-QAAAAAAAA==/", "_etag": "\"9d001c45-0000-0100-0000-686ff5c70000\"", "_attachments": "attachments/", "_ts": 1752167879}, {"payPeriodId": "1080040450436682", "status": "INITIAL", "description": "<PERSON>", "startDate": "2025-03-31T00:00:00Z", "endDate": "2025-04-06T00:00:00Z", "submitByDate": "2025-04-23T00:00:00Z", "checkDate": "2025-04-25T00:00:00Z", "checkCount": 0, "id": "c4773814-e900-4ed5-a915-69c8edc140ce", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEcs-QAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcs-QAAAAAAAA==/", "_etag": "\"9d002145-0000-0100-0000-686ff5c70000\"", "_attachments": "attachments/", "_ts": 1752167879}, {"payPeriodId": "1080040511250850", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-04-07T00:00:00Z", "endDate": "2025-04-13T00:00:00Z", "submitByDate": "2025-04-25T00:00:00Z", "checkDate": "2025-05-01T00:00:00Z", "checkCount": 0, "id": "88efce52-448c-47c3-b2b2-c7b29812bd05", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEct-QAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEct-QAAAAAAAA==/", "_etag": "\"9d002345-0000-0100-0000-686ff5c70000\"", "_attachments": "attachments/", "_ts": 1752167879}, {"payPeriodId": "1080040561759779", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-04-14T00:00:00Z", "endDate": "2025-04-20T00:00:00Z", "submitByDate": "2025-05-02T00:00:00Z", "checkDate": "2025-05-08T00:00:00Z", "checkCount": 0, "id": "ce5333f0-7df4-4f87-b286-abc804c6b864", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEcu-QAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcu-QAAAAAAAA==/", "_etag": "\"9d002745-0000-0100-0000-686ff5c70000\"", "_attachments": "attachments/", "_ts": 1752167879}, {"payPeriodId": "1080040620622346", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-04-21T00:00:00Z", "endDate": "2025-04-27T00:00:00Z", "submitByDate": "2025-05-09T00:00:00Z", "checkDate": "2025-05-15T00:00:00Z", "checkCount": 0, "id": "8d29f41f-c643-426c-b065-aab73bf73158", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEcv-QAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcv-QAAAAAAAA==/", "_etag": "\"9d002845-0000-0100-0000-686ff5c80000\"", "_attachments": "attachments/", "_ts": 1752167880}, {"payPeriodId": "1080040620621582", "status": "INITIAL", "description": "<PERSON>", "startDate": "2025-04-21T00:00:00Z", "endDate": "2025-04-27T00:00:00Z", "submitByDate": "2025-05-15T00:00:00Z", "checkDate": "2025-05-16T00:00:00Z", "checkCount": 0, "id": "e521f6ff-36e9-4c70-a8cd-2a3cea6867e2", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEcw-QAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcw-QAAAAAAAA==/", "_etag": "\"9d002d45-0000-0100-0000-686ff5c80000\"", "_attachments": "attachments/", "_ts": 1752167880}, {"payPeriodId": "1080040450437243", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-04-28T00:00:00Z", "endDate": "2025-05-04T00:00:00Z", "submitByDate": "2025-05-16T00:00:00Z", "checkDate": "2025-05-20T00:00:00Z", "checkCount": 0, "id": "5b353722-d0f7-4228-a839-6d992efde0c3", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEcx-QAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcx-QAAAAAAAA==/", "_etag": "\"9d003345-0000-0100-0000-686ff5c80000\"", "_attachments": "attachments/", "_ts": 1752167880}, {"payPeriodId": "1080040450437246", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (2)", "startDate": "2025-05-05T00:00:00Z", "endDate": "2025-05-11T00:00:00Z", "submitByDate": "2025-05-22T00:00:00Z", "checkDate": "2025-05-27T00:00:00Z", "checkCount": 0, "id": "dc7e7421-f6c8-4d65-a236-2119a81e1a4d", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEcy-QAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcy-QAAAAAAAA==/", "_etag": "\"9d003545-0000-0100-0000-686ff5c80000\"", "_attachments": "attachments/", "_ts": 1752167880}, {"payPeriodId": "1090071423493049", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-05-12T00:00:00Z", "endDate": "2025-05-18T00:00:00Z", "submitByDate": "2025-05-30T00:00:00Z", "checkDate": "2025-06-03T00:00:00Z", "checkCount": 0, "id": "f687660e-248f-4e02-9c7a-8e7276b8938f", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEcz-QAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcz-QAAAAAAAA==/", "_etag": "\"9d003b45-0000-0100-0000-686ff5c80000\"", "_attachments": "attachments/", "_ts": 1752167880}, {"payPeriodId": "1090071423493052", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-05-19T00:00:00Z", "endDate": "2025-05-25T00:00:00Z", "submitByDate": "2025-06-06T00:00:00Z", "checkDate": "2025-06-10T00:00:00Z", "checkCount": 0, "id": "e5899648-96f7-4f0f-b357-43fa08ba2e37", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEc0-QAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc0-QAAAAAAAA==/", "_etag": "\"9d004245-0000-0100-0000-686ff5c80000\"", "_attachments": "attachments/", "_ts": 1752167880}, {"payPeriodId": "1090071423493055", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-05-26T00:00:00Z", "endDate": "2025-06-01T00:00:00Z", "submitByDate": "2025-06-13T00:00:00Z", "checkDate": "2025-06-17T00:00:00Z", "checkCount": 0, "id": "c0d99879-1108-4957-8874-89b8eb840637", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEc1-QAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc1-QAAAAAAAA==/", "_etag": "\"9d004945-0000-0100-0000-686ff5c80000\"", "_attachments": "attachments/", "_ts": 1752167880}, {"payPeriodId": "1090071423493058", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-06-02T00:00:00Z", "endDate": "2025-06-08T00:00:00Z", "submitByDate": "2025-06-20T00:00:00Z", "checkDate": "2025-06-24T00:00:00Z", "checkCount": 0, "id": "c5f9f54b-3649-49dc-b65b-72908457c977", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEc2-QAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc2-QAAAAAAAA==/", "_etag": "\"9d004e45-0000-0100-0000-686ff5c80000\"", "_attachments": "attachments/", "_ts": 1752167880}, {"payPeriodId": "1090071423493064", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-06-16T00:00:00Z", "endDate": "2025-06-22T00:00:00Z", "submitByDate": "2025-07-03T00:00:00Z", "checkDate": "2025-07-08T00:00:00Z", "checkCount": 0, "id": "f288a873-8626-4dbc-9345-9ab7f1963b11", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEc3-QAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc3-QAAAAAAAA==/", "_etag": "\"9d005245-0000-0100-0000-686ff5c80000\"", "_attachments": "attachments/", "_ts": 1752167880}, {"payPeriodId": "1090071423493067", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-06-23T00:00:00Z", "endDate": "2025-06-29T00:00:00Z", "submitByDate": "2025-07-11T00:00:00Z", "checkDate": "2025-07-15T00:00:00Z", "checkCount": 0, "id": "8d213418-913b-4360-aa98-7b04018d500e", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEc4-QAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc4-QAAAAAAAA==/", "_etag": "\"9d005645-0000-0100-0000-686ff5c80000\"", "_attachments": "attachments/", "_ts": 1752167880}, {"payPeriodId": "1090071423493070", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-06-30T00:00:00Z", "endDate": "2025-07-06T00:00:00Z", "submitByDate": "2025-07-18T00:00:00Z", "checkDate": "2025-07-22T00:00:00Z", "checkCount": 0, "id": "cd42dea3-1d6e-4b4c-82c5-b614b65544e7", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEc5-QAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc5-QAAAAAAAA==/", "_etag": "\"9d005b45-0000-0100-0000-686ff5c80000\"", "_attachments": "attachments/", "_ts": 1752167880}, {"payPeriodId": "1090071423493073", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-07-07T00:00:00Z", "endDate": "2025-07-13T00:00:00Z", "submitByDate": "2025-07-25T00:00:00Z", "checkDate": "2025-07-29T00:00:00Z", "checkCount": 0, "id": "22b8e76c-97b8-4b23-a8f8-f5cd5e5ca625", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEc6-QAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc6-QAAAAAAAA==/", "_etag": "\"9d006045-0000-0100-0000-686ff5c80000\"", "_attachments": "attachments/", "_ts": 1752167880}, {"payPeriodId": "1090071423493076", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-07-14T00:00:00Z", "endDate": "2025-07-20T00:00:00Z", "submitByDate": "2025-08-01T00:00:00Z", "checkDate": "2025-08-05T00:00:00Z", "checkCount": 0, "id": "9efce8d7-3b63-460f-b163-063ccf5cc585", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEc7-QAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc7-QAAAAAAAA==/", "_etag": "\"9d006545-0000-0100-0000-686ff5c80000\"", "_attachments": "attachments/", "_ts": 1752167880}, {"payPeriodId": "1090071423493079", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-07-21T00:00:00Z", "endDate": "2025-07-27T00:00:00Z", "submitByDate": "2025-08-08T00:00:00Z", "checkDate": "2025-08-12T00:00:00Z", "checkCount": 0, "id": "bc3a748b-b7b0-4602-a3e0-ba518dc50320", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEc8-QAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc8-QAAAAAAAA==/", "_etag": "\"9d006845-0000-0100-0000-686ff5c90000\"", "_attachments": "attachments/", "_ts": 1752167881}, {"payPeriodId": "1090071423493082", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-07-28T00:00:00Z", "endDate": "2025-08-03T00:00:00Z", "submitByDate": "2025-08-15T00:00:00Z", "checkDate": "2025-08-19T00:00:00Z", "checkCount": 0, "id": "4f76bc7e-c099-40ab-81ae-2ca417cc3b50", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEc9-QAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc9-QAAAAAAAA==/", "_etag": "\"9d006d45-0000-0100-0000-686ff5c90000\"", "_attachments": "attachments/", "_ts": 1752167881}, {"payPeriodId": "1090071423493085", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-08-04T00:00:00Z", "endDate": "2025-08-10T00:00:00Z", "submitByDate": "2025-08-22T00:00:00Z", "checkDate": "2025-08-26T00:00:00Z", "checkCount": 0, "id": "f5a34816-2c7c-4a87-8f56-58689f14a2da", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEc+-QAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc+-QAAAAAAAA==/", "_etag": "\"9d006f45-0000-0100-0000-686ff5c90000\"", "_attachments": "attachments/", "_ts": 1752167881}, {"payPeriodId": "1090071493994120", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-08-11T00:00:00Z", "endDate": "2025-08-17T00:00:00Z", "submitByDate": "2025-08-28T00:00:00Z", "checkDate": "2025-09-02T00:00:00Z", "checkCount": 0, "id": "98c9e69f-0e87-4a47-b21d-ec2bee524bf2", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEc--QAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc--QAAAAAAAA==/", "_etag": "\"9d007345-0000-0100-0000-686ff5c90000\"", "_attachments": "attachments/", "_ts": 1752167881}, {"payPeriodId": "1090071493994121", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-08-18T00:00:00Z", "endDate": "2025-08-24T00:00:00Z", "submitByDate": "2025-09-05T00:00:00Z", "checkDate": "2025-09-09T00:00:00Z", "checkCount": 0, "id": "07862180-8f46-4a53-b053-08b38c70961f", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEdA-QAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdA-QAAAAAAAA==/", "_etag": "\"9d007545-0000-0100-0000-686ff5c90000\"", "_attachments": "attachments/", "_ts": 1752167881}, {"payPeriodId": "1090071493994122", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-08-25T00:00:00Z", "endDate": "2025-08-31T00:00:00Z", "submitByDate": "2025-09-12T00:00:00Z", "checkDate": "2025-09-16T00:00:00Z", "checkCount": 0, "id": "14171708-f4f7-4721-9041-25a639d2bde4", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEdB-QAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdB-QAAAAAAAA==/", "_etag": "\"9d007b45-0000-0100-0000-686ff5c90000\"", "_attachments": "attachments/", "_ts": 1752167881}, {"payPeriodId": "1090071493994123", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-09-01T00:00:00Z", "endDate": "2025-09-07T00:00:00Z", "submitByDate": "2025-09-19T00:00:00Z", "checkDate": "2025-09-23T00:00:00Z", "checkCount": 0, "id": "832d8943-f007-4cd0-9aa8-2c30963b1433", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEdC-QAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdC-QAAAAAAAA==/", "_etag": "\"9d007c45-0000-0100-0000-686ff5c90000\"", "_attachments": "attachments/", "_ts": 1752167881}, {"payPeriodId": "1090071612860167", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-09-08T00:00:00Z", "endDate": "2025-09-14T00:00:00Z", "submitByDate": "2025-09-26T00:00:00Z", "checkDate": "2025-09-30T00:00:00Z", "checkCount": 0, "id": "0bfd1aed-6334-49e7-b012-8f2c8441bb8d", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEdD-QAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdD-QAAAAAAAA==/", "_etag": "\"9d007e45-0000-0100-0000-686ff5c90000\"", "_attachments": "attachments/", "_ts": 1752167881}, {"payPeriodId": "1090071819953873", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-09-15T00:00:00Z", "endDate": "2025-09-21T00:00:00Z", "submitByDate": "2025-10-03T00:00:00Z", "checkDate": "2025-10-07T00:00:00Z", "checkCount": 0, "id": "f5ee3d96-de8a-4ffc-89de-bd6e78895340", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEdE-QAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdE-QAAAAAAAA==/", "_etag": "\"9d008245-0000-0100-0000-686ff5c90000\"", "_attachments": "attachments/", "_ts": 1752167881}, {"payPeriodId": "1090071983067164", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-09-22T00:00:00Z", "endDate": "2025-09-28T00:00:00Z", "submitByDate": "2025-10-10T00:00:00Z", "checkDate": "2025-10-14T00:00:00Z", "checkCount": 0, "id": "995d6d38-e1ee-43ff-8b8c-3bf467324bb1", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEdF-QAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdF-QAAAAAAAA==/", "_etag": "\"9d008645-0000-0100-0000-686ff5c90000\"", "_attachments": "attachments/", "_ts": 1752167881}, {"payPeriodId": "1090071423493061", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-06-09T00:00:00Z", "endDate": "2025-06-15T00:00:00Z", "submitByDate": "2025-06-27T00:00:00Z", "checkDate": "2025-07-01T00:00:00Z", "checkCount": 0, "id": "8d8e58a6-2606-407a-a59d-0ef935a4bec3", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEdG-QAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdG-QAAAAAAAA==/", "_etag": "\"9d008b45-0000-0100-0000-686ff5c90000\"", "_attachments": "attachments/", "_ts": 1752167881}, {"payPeriodId": "1080039812849907", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2024-12-30T00:00:00Z", "endDate": "2025-01-05T00:00:00Z", "submitByDate": "2025-01-15T00:00:00Z", "checkDate": "2025-01-21T00:00:00Z", "checkCount": 29, "id": "780d011d-827a-48f4-aaa2-573071f342e2", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEfF-QAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfF-QAAAAAAAA==/", "_etag": "\"9d00a647-0000-0100-0000-686ff5d40000\"", "_attachments": "attachments/", "_ts": 1752167892}, {"payPeriodId": "1080039866034570", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-01-06T00:00:00Z", "endDate": "2025-01-12T00:00:00Z", "submitByDate": "2025-01-22T00:00:00Z", "checkDate": "2025-01-29T00:00:00Z", "checkCount": 28, "id": "e7d54de8-ae30-4cee-888a-2b06462a9154", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEfG-QAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfG-QAAAAAAAA==/", "_etag": "\"9d00a847-0000-0100-0000-686ff5d40000\"", "_attachments": "attachments/", "_ts": 1752167892}, {"payPeriodId": "1080039906934417", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-01-13T00:00:00Z", "endDate": "2025-01-19T00:00:00Z", "submitByDate": "2025-01-29T00:00:00Z", "checkDate": "2025-02-04T00:00:00Z", "checkCount": 30, "id": "b4c3dbdd-a642-4d91-8a90-583a1554164d", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEfH-QAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfH-QAAAAAAAA==/", "_etag": "\"9d00a947-0000-0100-0000-686ff5d40000\"", "_attachments": "attachments/", "_ts": 1752167892}, {"payPeriodId": "1080039238323342", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-01-20T00:00:00Z", "endDate": "2025-01-26T00:00:00Z", "submitByDate": "2025-02-05T00:00:00Z", "checkDate": "2025-02-07T00:00:00Z", "checkCount": 30, "id": "1c803354-5442-45fb-b1fd-f5b38aaf25da", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEfI-QAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfI-QAAAAAAAA==/", "_etag": "\"9d00ac47-0000-0100-0000-686ff5d40000\"", "_attachments": "attachments/", "_ts": 1752167892}, {"payPeriodId": "1080039322503869", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-01-27T00:00:00Z", "endDate": "2025-02-02T00:00:00Z", "submitByDate": "2025-02-12T00:00:00Z", "checkDate": "2025-02-14T00:00:00Z", "checkCount": 31, "id": "e0f2a1ea-cb38-439f-b7e3-38db8a473e05", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEfJ-QAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfJ-QAAAAAAAA==/", "_etag": "\"9d00af47-0000-0100-0000-686ff5d40000\"", "_attachments": "attachments/", "_ts": 1752167892}, {"payPeriodId": "1080040033496426", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-02-03T00:00:00Z", "endDate": "2025-02-09T00:00:00Z", "submitByDate": "2025-02-19T00:00:00Z", "checkDate": "2025-02-25T00:00:00Z", "checkCount": 30, "id": "86865844-e7b3-4330-b183-31238c9329fa", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEfK-QAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfK-QAAAAAAAA==/", "_etag": "\"9d00b247-0000-0100-0000-686ff5d40000\"", "_attachments": "attachments/", "_ts": 1752167892}, {"payPeriodId": "1080039415135065", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-02-10T00:00:00Z", "endDate": "2025-02-16T00:00:00Z", "submitByDate": "2025-02-26T00:00:00Z", "checkDate": "2025-02-28T00:00:00Z", "checkCount": 31, "id": "57b094f5-5ddf-4415-ba5a-058a84cbfbd5", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEfL-QAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfL-QAAAAAAAA==/", "_etag": "\"9d00ba47-0000-0100-0000-686ff5d40000\"", "_attachments": "attachments/", "_ts": 1752167892}, {"payPeriodId": "1080040129804289", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-02-17T00:00:00Z", "endDate": "2025-02-23T00:00:00Z", "submitByDate": "2025-03-05T00:00:00Z", "checkDate": "2025-03-11T00:00:00Z", "checkCount": 30, "id": "e136bfdf-135c-460b-a7ad-490953124631", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEfM-QAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfM-QAAAAAAAA==/", "_etag": "\"9d00bd47-0000-0100-0000-686ff5d40000\"", "_attachments": "attachments/", "_ts": 1752167892}, {"payPeriodId": "1080040180670341", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-02-24T00:00:00Z", "endDate": "2025-03-02T00:00:00Z", "submitByDate": "2025-03-12T00:00:00Z", "checkDate": "2025-03-18T00:00:00Z", "checkCount": 30, "id": "60694644-abe1-48b7-a13f-7a2446ab357c", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEfN-QAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfN-QAAAAAAAA==/", "_etag": "\"9d00c247-0000-0100-0000-686ff5d40000\"", "_attachments": "attachments/", "_ts": 1752167892}, {"payPeriodId": "1080040220825478", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-03-03T00:00:00Z", "endDate": "2025-03-09T00:00:00Z", "submitByDate": "2025-03-19T00:00:00Z", "checkDate": "2025-03-25T00:00:00Z", "checkCount": 30, "id": "6b0187dc-32d4-4230-8317-9179259a9eef", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEfO-QAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfO-QAAAAAAAA==/", "_etag": "\"9d00c647-0000-0100-0000-686ff5d40000\"", "_attachments": "attachments/", "_ts": 1752167892}, {"payPeriodId": "1080040278492591", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-03-10T00:00:00Z", "endDate": "2025-03-16T00:00:00Z", "submitByDate": "2025-03-26T00:00:00Z", "checkDate": "2025-04-01T00:00:00Z", "checkCount": 30, "id": "93dace93-4aa3-479d-8d6b-585e64ffa9b0", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEfP-QAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfP-QAAAAAAAA==/", "_etag": "\"9d00ca47-0000-0100-0000-686ff5d40000\"", "_attachments": "attachments/", "_ts": 1752167892}, {"payPeriodId": "1080040335270371", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-03-17T00:00:00Z", "endDate": "2025-03-23T00:00:00Z", "submitByDate": "2025-04-02T00:00:00Z", "checkDate": "2025-04-08T00:00:00Z", "checkCount": 29, "id": "c595de36-2a48-4f9d-af65-c77a1293cc4e", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEfQ-QAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfQ-QAAAAAAAA==/", "_etag": "\"9d00cf47-0000-0100-0000-686ff5d40000\"", "_attachments": "attachments/", "_ts": 1752167892}, {"payPeriodId": "1080040385987922", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-03-24T00:00:00Z", "endDate": "2025-03-30T00:00:00Z", "submitByDate": "2025-04-09T00:00:00Z", "checkDate": "2025-04-15T00:00:00Z", "checkCount": 28, "id": "f4946530-d5fd-418e-9881-4ded720ae057", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEfR-QAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfR-QAAAAAAAA==/", "_etag": "\"9d00d347-0000-0100-0000-686ff5d40000\"", "_attachments": "attachments/", "_ts": 1752167892}, {"payPeriodId": "1080040450436724", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-03-31T00:00:00Z", "endDate": "2025-04-06T00:00:00Z", "submitByDate": "2025-04-16T00:00:00Z", "checkDate": "2025-04-24T00:00:00Z", "checkCount": 28, "id": "c93de31a-9e3d-4f52-91aa-023a1de44ced", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEfS-QAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfS-QAAAAAAAA==/", "_etag": "\"9d00da47-0000-0100-0000-686ff5d50000\"", "_attachments": "attachments/", "_ts": 1752167893}, {"payPeriodId": "1080040450436682", "status": "COMPLETED", "description": "<PERSON>", "startDate": "2025-03-31T00:00:00Z", "endDate": "2025-04-06T00:00:00Z", "submitByDate": "2025-04-23T00:00:00Z", "checkDate": "2025-04-25T00:00:00Z", "checkCount": 1, "id": "5033d1fe-eeab-4ece-9b97-11c33486429b", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEfT-QAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfT-QAAAAAAAA==/", "_etag": "\"9d00db47-0000-0100-0000-686ff5d50000\"", "_attachments": "attachments/", "_ts": 1752167893}, {"payPeriodId": "1080040511250850", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-04-07T00:00:00Z", "endDate": "2025-04-13T00:00:00Z", "submitByDate": "2025-04-25T00:00:00Z", "checkDate": "2025-05-01T00:00:00Z", "checkCount": 20, "id": "fae7dda0-9beb-474e-a152-e9b9fa85ec36", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEfU-QAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfU-QAAAAAAAA==/", "_etag": "\"9d00de47-0000-0100-0000-686ff5d50000\"", "_attachments": "attachments/", "_ts": 1752167893}, {"payPeriodId": "1080040561759779", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-04-14T00:00:00Z", "endDate": "2025-04-20T00:00:00Z", "submitByDate": "2025-05-02T00:00:00Z", "checkDate": "2025-05-08T00:00:00Z", "checkCount": 22, "id": "6acdafd3-a293-4a8f-abda-44f5a655c67e", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEfV-QAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfV-QAAAAAAAA==/", "_etag": "\"9d00e347-0000-0100-0000-686ff5d50000\"", "_attachments": "attachments/", "_ts": 1752167893}, {"payPeriodId": "1080040620622346", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-04-21T00:00:00Z", "endDate": "2025-04-27T00:00:00Z", "submitByDate": "2025-05-09T00:00:00Z", "checkDate": "2025-05-15T00:00:00Z", "checkCount": 29, "id": "46f0010c-067d-4f4a-9ba3-fea88197f717", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEfW-QAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfW-QAAAAAAAA==/", "_etag": "\"9d00e647-0000-0100-0000-686ff5d50000\"", "_attachments": "attachments/", "_ts": 1752167893}, {"payPeriodId": "1080040620621582", "status": "COMPLETED", "description": "<PERSON>", "startDate": "2025-04-21T00:00:00Z", "endDate": "2025-04-27T00:00:00Z", "submitByDate": "2025-05-15T00:00:00Z", "checkDate": "2025-05-16T00:00:00Z", "checkCount": 1, "id": "4f2571fe-ce69-42b6-8beb-7946a9078e26", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEfX-QAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfX-QAAAAAAAA==/", "_etag": "\"9d00e847-0000-0100-0000-686ff5d50000\"", "_attachments": "attachments/", "_ts": 1752167893}, {"payPeriodId": "1080040450437243", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-04-28T00:00:00Z", "endDate": "2025-05-04T00:00:00Z", "submitByDate": "2025-05-16T00:00:00Z", "checkDate": "2025-05-20T00:00:00Z", "checkCount": 25, "id": "bc0d5df0-a05b-4a2e-af60-b85af795ab95", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEfY-QAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfY-QAAAAAAAA==/", "_etag": "\"9d00e947-0000-0100-0000-686ff5d50000\"", "_attachments": "attachments/", "_ts": 1752167893}, {"payPeriodId": "1080040450437246", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (2)", "startDate": "2025-05-05T00:00:00Z", "endDate": "2025-05-11T00:00:00Z", "submitByDate": "2025-05-22T00:00:00Z", "checkDate": "2025-05-27T00:00:00Z", "checkCount": 25, "id": "3c569999-1870-4701-aa44-00a14a0e1e11", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEfZ-QAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfZ-QAAAAAAAA==/", "_etag": "\"9d00ea47-0000-0100-0000-686ff5d50000\"", "_attachments": "attachments/", "_ts": 1752167893}, {"payPeriodId": "1090071423493049", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-05-12T00:00:00Z", "endDate": "2025-05-18T00:00:00Z", "submitByDate": "2025-05-30T00:00:00Z", "checkDate": "2025-06-03T00:00:00Z", "checkCount": 28, "id": "1c5561a4-7586-413c-913a-613edd8ed8b4", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEfa-QAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfa-QAAAAAAAA==/", "_etag": "\"9d00eb47-0000-0100-0000-686ff5d50000\"", "_attachments": "attachments/", "_ts": 1752167893}, {"payPeriodId": "1090071423493052", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-05-19T00:00:00Z", "endDate": "2025-05-25T00:00:00Z", "submitByDate": "2025-06-06T00:00:00Z", "checkDate": "2025-06-10T00:00:00Z", "checkCount": 23, "id": "1332b801-6244-4165-9a6b-5623882cb506", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEfb-QAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfb-QAAAAAAAA==/", "_etag": "\"9d00f447-0000-0100-0000-686ff5d50000\"", "_attachments": "attachments/", "_ts": 1752167893}, {"payPeriodId": "1090071423493055", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-05-26T00:00:00Z", "endDate": "2025-06-01T00:00:00Z", "submitByDate": "2025-06-13T00:00:00Z", "checkDate": "2025-06-17T00:00:00Z", "checkCount": 24, "id": "a4cdfa06-a122-4701-aa20-728a1cb95c8c", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEfc-QAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfc-QAAAAAAAA==/", "_etag": "\"9d00f847-0000-0100-0000-686ff5d50000\"", "_attachments": "attachments/", "_ts": 1752167893}, {"payPeriodId": "1090071423493058", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-06-02T00:00:00Z", "endDate": "2025-06-08T00:00:00Z", "submitByDate": "2025-06-20T00:00:00Z", "checkDate": "2025-06-24T00:00:00Z", "checkCount": 25, "id": "47f15acb-78dc-40d6-9ad5-66a8514bc1fd", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEfd-QAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfd-QAAAAAAAA==/", "_etag": "\"9d00fa47-0000-0100-0000-686ff5d50000\"", "_attachments": "attachments/", "_ts": 1752167893}, {"payPeriodId": "1090071423493064", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-06-16T00:00:00Z", "endDate": "2025-06-22T00:00:00Z", "submitByDate": "2025-07-03T00:00:00Z", "checkDate": "2025-07-08T00:00:00Z", "checkCount": 0, "id": "30b198c5-9173-4a2c-850e-31ffed63f3a5", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEfe-QAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfe-QAAAAAAAA==/", "_etag": "\"9d00ff47-0000-0100-0000-686ff5d50000\"", "_attachments": "attachments/", "_ts": 1752167893}, {"payPeriodId": "1090071423493067", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-06-23T00:00:00Z", "endDate": "2025-06-29T00:00:00Z", "submitByDate": "2025-07-11T00:00:00Z", "checkDate": "2025-07-15T00:00:00Z", "checkCount": 0, "id": "176670e6-bf0a-4ede-9618-a4b6dbcdbcea", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEff-QAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEff-QAAAAAAAA==/", "_etag": "\"9d000248-0000-0100-0000-686ff5d60000\"", "_attachments": "attachments/", "_ts": 1752167894}, {"payPeriodId": "1090071423493070", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-06-30T00:00:00Z", "endDate": "2025-07-06T00:00:00Z", "submitByDate": "2025-07-18T00:00:00Z", "checkDate": "2025-07-22T00:00:00Z", "checkCount": 0, "id": "78e85159-f4fb-4618-8fc2-b25d003b1943", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEfg-QAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfg-QAAAAAAAA==/", "_etag": "\"9d000948-0000-0100-0000-686ff5d60000\"", "_attachments": "attachments/", "_ts": 1752167894}, {"payPeriodId": "1090071423493073", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-07-07T00:00:00Z", "endDate": "2025-07-13T00:00:00Z", "submitByDate": "2025-07-25T00:00:00Z", "checkDate": "2025-07-29T00:00:00Z", "checkCount": 0, "id": "57598cae-ab43-4437-a6e7-5f0104d3387c", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEfh-QAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfh-QAAAAAAAA==/", "_etag": "\"9d000a48-0000-0100-0000-686ff5d60000\"", "_attachments": "attachments/", "_ts": 1752167894}, {"payPeriodId": "1090071423493076", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-07-14T00:00:00Z", "endDate": "2025-07-20T00:00:00Z", "submitByDate": "2025-08-01T00:00:00Z", "checkDate": "2025-08-05T00:00:00Z", "checkCount": 0, "id": "91b8eceb-7482-4a54-b4e3-da2cba128fef", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEfi-QAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfi-QAAAAAAAA==/", "_etag": "\"9d000d48-0000-0100-0000-686ff5d60000\"", "_attachments": "attachments/", "_ts": 1752167894}, {"payPeriodId": "1090071423493079", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-07-21T00:00:00Z", "endDate": "2025-07-27T00:00:00Z", "submitByDate": "2025-08-08T00:00:00Z", "checkDate": "2025-08-12T00:00:00Z", "checkCount": 0, "id": "cc73f679-d8c2-44cb-908b-f3cd7c085732", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEfj-QAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfj-QAAAAAAAA==/", "_etag": "\"9d001248-0000-0100-0000-686ff5d60000\"", "_attachments": "attachments/", "_ts": 1752167894}, {"payPeriodId": "1090071423493082", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-07-28T00:00:00Z", "endDate": "2025-08-03T00:00:00Z", "submitByDate": "2025-08-15T00:00:00Z", "checkDate": "2025-08-19T00:00:00Z", "checkCount": 0, "id": "ddb37bca-20e2-4726-b265-2be30f7fdadd", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEfk-QAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfk-QAAAAAAAA==/", "_etag": "\"9d001748-0000-0100-0000-686ff5d60000\"", "_attachments": "attachments/", "_ts": 1752167894}, {"payPeriodId": "1090071423493085", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-08-04T00:00:00Z", "endDate": "2025-08-10T00:00:00Z", "submitByDate": "2025-08-22T00:00:00Z", "checkDate": "2025-08-26T00:00:00Z", "checkCount": 0, "id": "cd80c73a-667b-423e-a87d-7970b12efe9b", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEfl-QAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfl-QAAAAAAAA==/", "_etag": "\"9d001a48-0000-0100-0000-686ff5d60000\"", "_attachments": "attachments/", "_ts": 1752167894}, {"payPeriodId": "1090071493994120", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-08-11T00:00:00Z", "endDate": "2025-08-17T00:00:00Z", "submitByDate": "2025-08-28T00:00:00Z", "checkDate": "2025-09-02T00:00:00Z", "checkCount": 0, "id": "f47cb60d-2693-4390-b76b-33df8f39d0fd", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEfm-QAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfm-QAAAAAAAA==/", "_etag": "\"9d001c48-0000-0100-0000-686ff5d60000\"", "_attachments": "attachments/", "_ts": 1752167894}, {"payPeriodId": "1090071493994121", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-08-18T00:00:00Z", "endDate": "2025-08-24T00:00:00Z", "submitByDate": "2025-09-05T00:00:00Z", "checkDate": "2025-09-09T00:00:00Z", "checkCount": 0, "id": "5f4a1e2b-83b4-4f71-8ecf-339789118e3f", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEfn-QAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfn-QAAAAAAAA==/", "_etag": "\"9d001d48-0000-0100-0000-686ff5d60000\"", "_attachments": "attachments/", "_ts": 1752167894}, {"payPeriodId": "1090071493994122", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-08-25T00:00:00Z", "endDate": "2025-08-31T00:00:00Z", "submitByDate": "2025-09-12T00:00:00Z", "checkDate": "2025-09-16T00:00:00Z", "checkCount": 0, "id": "f3f3a130-ee1c-4a0c-80bf-a538e6fc9ca6", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEfo-QAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfo-QAAAAAAAA==/", "_etag": "\"9d002248-0000-0100-0000-686ff5d60000\"", "_attachments": "attachments/", "_ts": 1752167894}, {"payPeriodId": "1090071493994123", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-09-01T00:00:00Z", "endDate": "2025-09-07T00:00:00Z", "submitByDate": "2025-09-19T00:00:00Z", "checkDate": "2025-09-23T00:00:00Z", "checkCount": 0, "id": "6beaee0d-92e4-4947-90f7-32989f552b15", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEfp-QAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfp-QAAAAAAAA==/", "_etag": "\"9d002748-0000-0100-0000-686ff5d60000\"", "_attachments": "attachments/", "_ts": 1752167894}, {"payPeriodId": "1090071612860167", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-09-08T00:00:00Z", "endDate": "2025-09-14T00:00:00Z", "submitByDate": "2025-09-26T00:00:00Z", "checkDate": "2025-09-30T00:00:00Z", "checkCount": 0, "id": "bb7714a3-1e62-4651-975d-e1e16836454f", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEfq-QAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfq-QAAAAAAAA==/", "_etag": "\"9d002948-0000-0100-0000-686ff5d60000\"", "_attachments": "attachments/", "_ts": 1752167894}, {"payPeriodId": "1090071819953873", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-09-15T00:00:00Z", "endDate": "2025-09-21T00:00:00Z", "submitByDate": "2025-10-03T00:00:00Z", "checkDate": "2025-10-07T00:00:00Z", "checkCount": 0, "id": "181358cd-4753-4ff8-8bf1-44a98e79d305", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEfr-QAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfr-QAAAAAAAA==/", "_etag": "\"9d002c48-0000-0100-0000-686ff5d60000\"", "_attachments": "attachments/", "_ts": 1752167894}, {"payPeriodId": "1090071983067164", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-09-22T00:00:00Z", "endDate": "2025-09-28T00:00:00Z", "submitByDate": "2025-10-10T00:00:00Z", "checkDate": "2025-10-14T00:00:00Z", "checkCount": 0, "id": "851f9ef9-0923-493f-9716-31977166241e", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEfs-QAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfs-QAAAAAAAA==/", "_etag": "\"9d003248-0000-0100-0000-686ff5d70000\"", "_attachments": "attachments/", "_ts": 1752167895}, {"payPeriodId": "1090071423493061", "intervalCode": "WEEKLY", "status": "ENTRY", "description": "Weekly Payroll (1)", "startDate": "2025-06-09T00:00:00Z", "endDate": "2025-06-15T00:00:00Z", "submitByDate": "2025-06-27T00:00:00Z", "checkDate": "2025-07-01T00:00:00Z", "checkCount": 12, "id": "6aff7f02-26e6-4ea2-ba3f-9a88a734c3d8", "companyId": "17101339", "type": "payperiod", "_rid": "NmJkAKiCbEft-QAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEft-QAAAAAAAA==/", "_etag": "\"9d003548-0000-0100-0000-686ff5d70000\"", "_attachments": "attachments/", "_ts": 1752167895}], "links": [{"rel": "self", "href": "https://ca-payroll-ai-mock-sb-001-secure.nicebeach-8a7a52ab.eastus.azurecontainerapps.io/ca/companies/17101339/payperiods"}]}, "status_code": 200}