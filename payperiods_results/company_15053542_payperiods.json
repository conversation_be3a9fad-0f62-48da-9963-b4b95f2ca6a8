{"success": true, "company_id": "15053542", "data": {"metadata": {"contentItemCount": 42}, "content": [{"payPeriodId": "1060038933324796", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2024-12-28T00:00:00Z", "endDate": "2025-01-10T00:00:00Z", "submitByDate": "2025-01-08T00:00:00Z", "checkDate": "2025-01-10T00:00:00Z", "checkCount": 2, "id": "0bd0de65-eda7-4fb9-ad0d-4758acad4972", "companyId": "15053542", "type": "payperiod", "_rid": "NmJkAKiCbEclrQIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEclrQIAAAAAAA==/", "_etag": "\"a300369c-0000-0100-0000-68701bac0000\"", "_attachments": "attachments/", "_ts": 1752177580}, {"payPeriodId": "1060039005766995", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-01-11T00:00:00Z", "endDate": "2025-01-24T00:00:00Z", "submitByDate": "2025-01-22T00:00:00Z", "checkDate": "2025-01-24T00:00:00Z", "checkCount": 2, "id": "eee65307-7838-4a7e-9a09-8197e1802170", "companyId": "15053542", "type": "payperiod", "_rid": "NmJkAKiCbEcmrQIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcmrQIAAAAAAA==/", "_etag": "\"a300399c-0000-0100-0000-68701bac0000\"", "_attachments": "attachments/", "_ts": 1752177580}, {"payPeriodId": "1060039082324947", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-01-25T00:00:00Z", "endDate": "2025-02-07T00:00:00Z", "submitByDate": "2025-02-05T00:00:00Z", "checkDate": "2025-02-07T00:00:00Z", "checkCount": 2, "id": "91aac9d9-6cc7-4e65-9744-1a38303ce10e", "companyId": "15053542", "type": "payperiod", "_rid": "NmJkAKiCbEcnrQIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcnrQIAAAAAAA==/", "_etag": "\"a3003d9c-0000-0100-0000-68701bac0000\"", "_attachments": "attachments/", "_ts": 1752177580}, {"payPeriodId": "1060039155227372", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-02-08T00:00:00Z", "endDate": "2025-02-21T00:00:00Z", "submitByDate": "2025-02-19T00:00:00Z", "checkDate": "2025-02-21T00:00:00Z", "checkCount": 2, "id": "37e3c2b5-acc1-4cd8-a4a5-d9bca78a0132", "companyId": "15053542", "type": "payperiod", "_rid": "NmJkAKiCbEcorQIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcorQIAAAAAAA==/", "_etag": "\"a3003e9c-0000-0100-0000-68701bac0000\"", "_attachments": "attachments/", "_ts": 1752177580}, {"payPeriodId": "1060039226816095", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-02-22T00:00:00Z", "endDate": "2025-03-07T00:00:00Z", "submitByDate": "2025-03-05T00:00:00Z", "checkDate": "2025-03-07T00:00:00Z", "checkCount": 2, "id": "6a704fce-a472-4816-84c2-bd1c886c96e1", "companyId": "15053542", "type": "payperiod", "_rid": "NmJkAKiCbEcprQIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcprQIAAAAAAA==/", "_etag": "\"a300419c-0000-0100-0000-68701bac0000\"", "_attachments": "attachments/", "_ts": 1752177580}, {"payPeriodId": "1060039322698844", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-03-08T00:00:00Z", "endDate": "2025-03-21T00:00:00Z", "submitByDate": "2025-03-19T00:00:00Z", "checkDate": "2025-03-21T00:00:00Z", "checkCount": 2, "id": "4e96e406-b286-48a5-b991-20c7e304ff56", "companyId": "15053542", "type": "payperiod", "_rid": "NmJkAKiCbEcqrQIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcqrQIAAAAAAA==/", "_etag": "\"a300489c-0000-0100-0000-68701bac0000\"", "_attachments": "attachments/", "_ts": 1752177580}, {"payPeriodId": "1060039377659245", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-03-22T00:00:00Z", "endDate": "2025-04-04T00:00:00Z", "submitByDate": "2025-04-02T00:00:00Z", "checkDate": "2025-04-04T00:00:00Z", "checkCount": 0, "id": "ff4af654-152c-4487-9e2f-f7823c8ce2b4", "companyId": "15053542", "type": "payperiod", "_rid": "NmJkAKiCbEcrrQIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcrrQIAAAAAAA==/", "_etag": "\"a3004c9c-0000-0100-0000-68701bad0000\"", "_attachments": "attachments/", "_ts": 1752177581}, {"payPeriodId": "1060039468095452", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-04-05T00:00:00Z", "endDate": "2025-04-18T00:00:00Z", "submitByDate": "2025-04-16T00:00:00Z", "checkDate": "2025-04-18T00:00:00Z", "checkCount": 0, "id": "f78ad788-b4c2-4f44-93b4-4859e576f0b0", "companyId": "15053542", "type": "payperiod", "_rid": "NmJkAKiCbEcsrQIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcsrQIAAAAAAA==/", "_etag": "\"a300519c-0000-0100-0000-68701bad0000\"", "_attachments": "attachments/", "_ts": 1752177581}, {"payPeriodId": "1060039536105789", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-04-19T00:00:00Z", "endDate": "2025-05-02T00:00:00Z", "submitByDate": "2025-04-30T00:00:00Z", "checkDate": "2025-05-02T00:00:00Z", "checkCount": 0, "id": "49698bad-bcb1-405a-ad76-5fb99e8ec445", "companyId": "15053542", "type": "payperiod", "_rid": "NmJkAKiCbEctrQIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEctrQIAAAAAAA==/", "_etag": "\"a300559c-0000-0100-0000-68701bad0000\"", "_attachments": "attachments/", "_ts": 1752177581}, {"payPeriodId": "1060039609017436", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-05-03T00:00:00Z", "endDate": "2025-05-16T00:00:00Z", "submitByDate": "2025-05-14T00:00:00Z", "checkDate": "2025-05-16T00:00:00Z", "checkCount": 0, "id": "4c2fbb9d-83fc-420c-a26e-a32006e731b3", "companyId": "15053542", "type": "payperiod", "_rid": "NmJkAKiCbEcurQIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcurQIAAAAAAA==/", "_etag": "\"a3005c9c-0000-0100-0000-68701bad0000\"", "_attachments": "attachments/", "_ts": 1752177581}, {"payPeriodId": "1060039668461552", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-05-17T00:00:00Z", "endDate": "2025-05-30T00:00:00Z", "submitByDate": "2025-05-28T00:00:00Z", "checkDate": "2025-05-30T00:00:00Z", "checkCount": 0, "id": "c27d533e-1ff2-4781-84bd-2c40917918fa", "companyId": "15053542", "type": "payperiod", "_rid": "NmJkAKiCbEcvrQIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcvrQIAAAAAAA==/", "_etag": "\"a300609c-0000-0100-0000-68701bad0000\"", "_attachments": "attachments/", "_ts": 1752177581}, {"payPeriodId": "1060039727884576", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-05-31T00:00:00Z", "endDate": "2025-06-13T00:00:00Z", "submitByDate": "2025-06-11T00:00:00Z", "checkDate": "2025-06-13T00:00:00Z", "checkCount": 0, "id": "4dc9a5e3-8d17-438d-aa64-3b8a1f16f2b8", "companyId": "15053542", "type": "payperiod", "_rid": "NmJkAKiCbEcwrQIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcwrQIAAAAAAA==/", "_etag": "\"a300639c-0000-0100-0000-68701bad0000\"", "_attachments": "attachments/", "_ts": 1752177581}, {"payPeriodId": "1060039795327264", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-06-14T00:00:00Z", "endDate": "2025-06-27T00:00:00Z", "submitByDate": "2025-06-25T00:00:00Z", "checkDate": "2025-06-27T00:00:00Z", "checkCount": 0, "id": "b6f433d2-1bb2-4118-a870-3b7571403f95", "companyId": "15053542", "type": "payperiod", "_rid": "NmJkAKiCbEcxrQIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcxrQIAAAAAAA==/", "_etag": "\"a300679c-0000-0100-0000-68701bad0000\"", "_attachments": "attachments/", "_ts": 1752177581}, {"payPeriodId": "1060039864835261", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-06-28T00:00:00Z", "endDate": "2025-07-11T00:00:00Z", "submitByDate": "2025-07-09T00:00:00Z", "checkDate": "2025-07-11T00:00:00Z", "checkCount": 0, "id": "c5a63088-48ba-4c2e-a460-61e52121d5b5", "companyId": "15053542", "type": "payperiod", "_rid": "NmJkAKiCbEcyrQIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcyrQIAAAAAAA==/", "_etag": "\"a300689c-0000-0100-0000-68701bad0000\"", "_attachments": "attachments/", "_ts": 1752177581}, {"payPeriodId": "1060039943334947", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-07-12T00:00:00Z", "endDate": "2025-07-25T00:00:00Z", "submitByDate": "2025-07-23T00:00:00Z", "checkDate": "2025-07-25T00:00:00Z", "checkCount": 0, "id": "b826bdbf-b087-448b-ab0e-0c96948dd367", "companyId": "15053542", "type": "payperiod", "_rid": "NmJkAKiCbEczrQIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEczrQIAAAAAAA==/", "_etag": "\"a3006a9c-0000-0100-0000-68701bad0000\"", "_attachments": "attachments/", "_ts": 1752177581}, {"payPeriodId": "1060040014093691", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-07-26T00:00:00Z", "endDate": "2025-08-08T00:00:00Z", "submitByDate": "2025-08-06T00:00:00Z", "checkDate": "2025-08-08T00:00:00Z", "checkCount": 0, "id": "ff80a15c-21e9-4ff2-896d-29810e109cfa", "companyId": "15053542", "type": "payperiod", "_rid": "NmJkAKiCbEc0rQIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc0rQIAAAAAAA==/", "_etag": "\"a3006b9c-0000-0100-0000-68701bad0000\"", "_attachments": "attachments/", "_ts": 1752177581}, {"payPeriodId": "1060040090014739", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-08-09T00:00:00Z", "endDate": "2025-08-22T00:00:00Z", "submitByDate": "2025-08-20T00:00:00Z", "checkDate": "2025-08-22T00:00:00Z", "checkCount": 0, "id": "f4f3cc2f-03ec-4bcd-875f-ec02940d663f", "companyId": "15053542", "type": "payperiod", "_rid": "NmJkAKiCbEc1rQIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc1rQIAAAAAAA==/", "_etag": "\"a3006f9c-0000-0100-0000-68701bad0000\"", "_attachments": "attachments/", "_ts": 1752177581}, {"payPeriodId": "1060040160492759", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-08-23T00:00:00Z", "endDate": "2025-09-05T00:00:00Z", "submitByDate": "2025-09-03T00:00:00Z", "checkDate": "2025-09-05T00:00:00Z", "checkCount": 0, "id": "a8b6714c-6d0f-4020-9942-ad0d3adca5db", "companyId": "15053542", "type": "payperiod", "_rid": "NmJkAKiCbEc2rQIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc2rQIAAAAAAA==/", "_etag": "\"a300769c-0000-0100-0000-68701bad0000\"", "_attachments": "attachments/", "_ts": 1752177581}, {"payPeriodId": "1060040228996405", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-09-06T00:00:00Z", "endDate": "2025-09-19T00:00:00Z", "submitByDate": "2025-09-17T00:00:00Z", "checkDate": "2025-09-19T00:00:00Z", "checkCount": 0, "id": "82a5e4a2-248f-463c-9e87-6ed04db2fa23", "companyId": "15053542", "type": "payperiod", "_rid": "NmJkAKiCbEc3rQIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc3rQIAAAAAAA==/", "_etag": "\"a3007d9c-0000-0100-0000-68701bad0000\"", "_attachments": "attachments/", "_ts": 1752177581}, {"payPeriodId": "1060040297309714", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-09-20T00:00:00Z", "endDate": "2025-10-03T00:00:00Z", "submitByDate": "2025-10-01T00:00:00Z", "checkDate": "2025-10-03T00:00:00Z", "checkCount": 0, "id": "d4e13c5c-ad50-4bcc-8bb9-d0dff77b81df", "companyId": "15053542", "type": "payperiod", "_rid": "NmJkAKiCbEc4rQIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc4rQIAAAAAAA==/", "_etag": "\"a300889c-0000-0100-0000-68701bae0000\"", "_attachments": "attachments/", "_ts": 1752177582}, {"payPeriodId": "1060040369728293", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-10-04T00:00:00Z", "endDate": "2025-10-17T00:00:00Z", "submitByDate": "2025-10-15T00:00:00Z", "checkDate": "2025-10-17T00:00:00Z", "checkCount": 0, "id": "7ae2d6ce-3b07-425a-b8bb-1dc53555b280", "companyId": "15053542", "type": "payperiod", "_rid": "NmJkAKiCbEc5rQIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc5rQIAAAAAAA==/", "_etag": "\"a3008c9c-0000-0100-0000-68701bae0000\"", "_attachments": "attachments/", "_ts": 1752177582}, {"payPeriodId": "1060038933324796", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2024-12-28T00:00:00Z", "endDate": "2025-01-10T00:00:00Z", "submitByDate": "2025-01-08T00:00:00Z", "checkDate": "2025-01-10T00:00:00Z", "checkCount": 2, "id": "1a3a0d35-001e-4a4b-8b3b-9bc87c785c3d", "companyId": "15053542", "type": "payperiod", "_rid": "NmJkAKiCbEc9rQIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc9rQIAAAAAAA==/", "_etag": "\"a300a09c-0000-0100-0000-68701bae0000\"", "_attachments": "attachments/", "_ts": 1752177582}, {"payPeriodId": "1060039005766995", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-01-11T00:00:00Z", "endDate": "2025-01-24T00:00:00Z", "submitByDate": "2025-01-22T00:00:00Z", "checkDate": "2025-01-24T00:00:00Z", "checkCount": 2, "id": "5f3f5d92-d0a1-46ec-bb4b-1448526266f9", "companyId": "15053542", "type": "payperiod", "_rid": "NmJkAKiCbEc+rQIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc+rQIAAAAAAA==/", "_etag": "\"a300a69c-0000-0100-0000-68701bae0000\"", "_attachments": "attachments/", "_ts": 1752177582}, {"payPeriodId": "1060039082324947", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-01-25T00:00:00Z", "endDate": "2025-02-07T00:00:00Z", "submitByDate": "2025-02-05T00:00:00Z", "checkDate": "2025-02-07T00:00:00Z", "checkCount": 2, "id": "342e5d92-9f60-4805-aa66-002cd382501b", "companyId": "15053542", "type": "payperiod", "_rid": "NmJkAKiCbEc-rQIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc-rQIAAAAAAA==/", "_etag": "\"a300aa9c-0000-0100-0000-68701bae0000\"", "_attachments": "attachments/", "_ts": 1752177582}, {"payPeriodId": "1060039155227372", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-02-08T00:00:00Z", "endDate": "2025-02-21T00:00:00Z", "submitByDate": "2025-02-19T00:00:00Z", "checkDate": "2025-02-21T00:00:00Z", "checkCount": 2, "id": "b4fca50e-3e3a-40ef-be6a-ec33febc1b2e", "companyId": "15053542", "type": "payperiod", "_rid": "NmJkAKiCbEdArQIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdArQIAAAAAAA==/", "_etag": "\"a300b19c-0000-0100-0000-68701bae0000\"", "_attachments": "attachments/", "_ts": 1752177582}, {"payPeriodId": "1060039226816095", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-02-22T00:00:00Z", "endDate": "2025-03-07T00:00:00Z", "submitByDate": "2025-03-05T00:00:00Z", "checkDate": "2025-03-07T00:00:00Z", "checkCount": 2, "id": "4fade446-a453-4f9a-b583-b03253f87420", "companyId": "15053542", "type": "payperiod", "_rid": "NmJkAKiCbEdBrQIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdBrQIAAAAAAA==/", "_etag": "\"a300b89c-0000-0100-0000-68701bae0000\"", "_attachments": "attachments/", "_ts": 1752177582}, {"payPeriodId": "1060039322698844", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-03-08T00:00:00Z", "endDate": "2025-03-21T00:00:00Z", "submitByDate": "2025-03-19T00:00:00Z", "checkDate": "2025-03-21T00:00:00Z", "checkCount": 2, "id": "65701415-8241-49a9-879a-e0583dfbbd97", "companyId": "15053542", "type": "payperiod", "_rid": "NmJkAKiCbEdCrQIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdCrQIAAAAAAA==/", "_etag": "\"a300c09c-0000-0100-0000-68701bae0000\"", "_attachments": "attachments/", "_ts": 1752177582}, {"payPeriodId": "1060039377659245", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-03-22T00:00:00Z", "endDate": "2025-04-04T00:00:00Z", "submitByDate": "2025-04-02T00:00:00Z", "checkDate": "2025-04-04T00:00:00Z", "checkCount": 2, "id": "d196a6c5-aa0e-42f6-913a-93d3404cf9c4", "companyId": "15053542", "type": "payperiod", "_rid": "NmJkAKiCbEdDrQIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdDrQIAAAAAAA==/", "_etag": "\"a300c69c-0000-0100-0000-68701bae0000\"", "_attachments": "attachments/", "_ts": 1752177582}, {"payPeriodId": "1060039468095452", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-04-05T00:00:00Z", "endDate": "2025-04-18T00:00:00Z", "submitByDate": "2025-04-16T00:00:00Z", "checkDate": "2025-04-18T00:00:00Z", "checkCount": 2, "id": "f60eaf7b-0584-4065-8dd9-acc20c5d7ba4", "companyId": "15053542", "type": "payperiod", "_rid": "NmJkAKiCbEdErQIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdErQIAAAAAAA==/", "_etag": "\"a300c89c-0000-0100-0000-68701bae0000\"", "_attachments": "attachments/", "_ts": 1752177582}, {"payPeriodId": "1060039536105789", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-04-19T00:00:00Z", "endDate": "2025-05-02T00:00:00Z", "submitByDate": "2025-04-30T00:00:00Z", "checkDate": "2025-05-02T00:00:00Z", "checkCount": 2, "id": "36a5ead3-f6d8-449b-8214-52f21237c6a3", "companyId": "15053542", "type": "payperiod", "_rid": "NmJkAKiCbEdFrQIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdFrQIAAAAAAA==/", "_etag": "\"a300d59c-0000-0100-0000-68701baf0000\"", "_attachments": "attachments/", "_ts": 1752177583}, {"payPeriodId": "1060039609017436", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-05-03T00:00:00Z", "endDate": "2025-05-16T00:00:00Z", "submitByDate": "2025-05-14T00:00:00Z", "checkDate": "2025-05-16T00:00:00Z", "checkCount": 1, "id": "f881938c-3f11-4931-a1fe-6b000ad39b56", "companyId": "15053542", "type": "payperiod", "_rid": "NmJkAKiCbEdGrQIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdGrQIAAAAAAA==/", "_etag": "\"a300d79c-0000-0100-0000-68701baf0000\"", "_attachments": "attachments/", "_ts": 1752177583}, {"payPeriodId": "1060039668461552", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-05-17T00:00:00Z", "endDate": "2025-05-30T00:00:00Z", "submitByDate": "2025-05-28T00:00:00Z", "checkDate": "2025-05-30T00:00:00Z", "checkCount": 1, "id": "bd3133e1-c3ec-421a-acd9-a6a999d6c9b2", "companyId": "15053542", "type": "payperiod", "_rid": "NmJkAKiCbEdHrQIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdHrQIAAAAAAA==/", "_etag": "\"a300e09c-0000-0100-0000-68701baf0000\"", "_attachments": "attachments/", "_ts": 1752177583}, {"payPeriodId": "1060039727884576", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-05-31T00:00:00Z", "endDate": "2025-06-13T00:00:00Z", "submitByDate": "2025-06-11T00:00:00Z", "checkDate": "2025-06-13T00:00:00Z", "checkCount": 1, "id": "6d2dd49a-626f-420d-a921-abfca0338889", "companyId": "15053542", "type": "payperiod", "_rid": "NmJkAKiCbEdIrQIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdIrQIAAAAAAA==/", "_etag": "\"a300e59c-0000-0100-0000-68701baf0000\"", "_attachments": "attachments/", "_ts": 1752177583}, {"payPeriodId": "1060039795327264", "intervalCode": "BI_WEEKLY", "status": "COMPLETED", "description": "Bi-weekly Payroll (1)", "startDate": "2025-06-14T00:00:00Z", "endDate": "2025-06-27T00:00:00Z", "submitByDate": "2025-06-25T00:00:00Z", "checkDate": "2025-06-27T00:00:00Z", "checkCount": 2, "id": "49c3091f-1aef-4e7c-9f51-7d5eb25d270e", "companyId": "15053542", "type": "payperiod", "_rid": "NmJkAKiCbEdJrQIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdJrQIAAAAAAA==/", "_etag": "\"a300ec9c-0000-0100-0000-68701baf0000\"", "_attachments": "attachments/", "_ts": 1752177583}, {"payPeriodId": "1060039864835261", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-06-28T00:00:00Z", "endDate": "2025-07-11T00:00:00Z", "submitByDate": "2025-07-09T00:00:00Z", "checkDate": "2025-07-11T00:00:00Z", "checkCount": 0, "id": "c063dbc8-8763-4bfb-bef4-ecd9036b8c90", "companyId": "15053542", "type": "payperiod", "_rid": "NmJkAKiCbEdKrQIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdKrQIAAAAAAA==/", "_etag": "\"a300f09c-0000-0100-0000-68701baf0000\"", "_attachments": "attachments/", "_ts": 1752177583}, {"payPeriodId": "1060039943334947", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-07-12T00:00:00Z", "endDate": "2025-07-25T00:00:00Z", "submitByDate": "2025-07-23T00:00:00Z", "checkDate": "2025-07-25T00:00:00Z", "checkCount": 0, "id": "ec5c68ae-4cea-4cf2-afb7-ec0f949b8a03", "companyId": "15053542", "type": "payperiod", "_rid": "NmJkAKiCbEdLrQIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdLrQIAAAAAAA==/", "_etag": "\"a300fa9c-0000-0100-0000-68701baf0000\"", "_attachments": "attachments/", "_ts": 1752177583}, {"payPeriodId": "1060040014093691", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-07-26T00:00:00Z", "endDate": "2025-08-08T00:00:00Z", "submitByDate": "2025-08-06T00:00:00Z", "checkDate": "2025-08-08T00:00:00Z", "checkCount": 0, "id": "2f38c285-a039-4454-9e44-3487ec5f8032", "companyId": "15053542", "type": "payperiod", "_rid": "NmJkAKiCbEdMrQIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdMrQIAAAAAAA==/", "_etag": "\"a300fe9c-0000-0100-0000-68701baf0000\"", "_attachments": "attachments/", "_ts": 1752177583}, {"payPeriodId": "1060040090014739", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-08-09T00:00:00Z", "endDate": "2025-08-22T00:00:00Z", "submitByDate": "2025-08-20T00:00:00Z", "checkDate": "2025-08-22T00:00:00Z", "checkCount": 0, "id": "eb70b1d2-c358-44de-87a6-9eed4e199d46", "companyId": "15053542", "type": "payperiod", "_rid": "NmJkAKiCbEdNrQIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdNrQIAAAAAAA==/", "_etag": "\"a300019d-0000-0100-0000-68701baf0000\"", "_attachments": "attachments/", "_ts": 1752177583}, {"payPeriodId": "1060040160492759", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-08-23T00:00:00Z", "endDate": "2025-09-05T00:00:00Z", "submitByDate": "2025-09-03T00:00:00Z", "checkDate": "2025-09-05T00:00:00Z", "checkCount": 0, "id": "414b6988-6760-43d0-8847-d485e2e93380", "companyId": "15053542", "type": "payperiod", "_rid": "NmJkAKiCbEdOrQIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdOrQIAAAAAAA==/", "_etag": "\"a300059d-0000-0100-0000-68701baf0000\"", "_attachments": "attachments/", "_ts": 1752177583}, {"payPeriodId": "1060040228996405", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-09-06T00:00:00Z", "endDate": "2025-09-19T00:00:00Z", "submitByDate": "2025-09-17T00:00:00Z", "checkDate": "2025-09-19T00:00:00Z", "checkCount": 0, "id": "53635d7c-515a-4c1d-abcc-c1fd0e50cbd3", "companyId": "15053542", "type": "payperiod", "_rid": "NmJkAKiCbEdPrQIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdPrQIAAAAAAA==/", "_etag": "\"a3000a9d-0000-0100-0000-68701baf0000\"", "_attachments": "attachments/", "_ts": 1752177583}, {"payPeriodId": "1060040297309714", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-09-20T00:00:00Z", "endDate": "2025-10-03T00:00:00Z", "submitByDate": "2025-10-01T00:00:00Z", "checkDate": "2025-10-03T00:00:00Z", "checkCount": 0, "id": "81a96745-64c8-4c7f-9d6d-52c1f890caac", "companyId": "15053542", "type": "payperiod", "_rid": "NmJkAKiCbEdQrQIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdQrQIAAAAAAA==/", "_etag": "\"a3000d9d-0000-0100-0000-68701baf0000\"", "_attachments": "attachments/", "_ts": 1752177583}, {"payPeriodId": "1060040369728293", "intervalCode": "BI_WEEKLY", "status": "INITIAL", "description": "Bi-weekly Payroll (1)", "startDate": "2025-10-04T00:00:00Z", "endDate": "2025-10-17T00:00:00Z", "submitByDate": "2025-10-15T00:00:00Z", "checkDate": "2025-10-17T00:00:00Z", "checkCount": 0, "id": "908dbec2-852b-4bdd-99b9-03f100903d5a", "companyId": "15053542", "type": "payperiod", "_rid": "NmJkAKiCbEdRrQIAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdRrQIAAAAAAA==/", "_etag": "\"a300109d-0000-0100-0000-68701baf0000\"", "_attachments": "attachments/", "_ts": 1752177583}], "links": [{"rel": "self", "href": "https://ca-payroll-ai-mock-sb-001-secure.nicebeach-8a7a52ab.eastus.azurecontainerapps.io/ca/companies/15053542/payperiods"}]}, "status_code": 200}