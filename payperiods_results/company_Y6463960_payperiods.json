{"success": true, "company_id": "********", "data": {"metadata": {"contentItemCount": 160}, "content": [{"payPeriodId": "1020050132589552", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2024-12-28T00:00:00Z", "endDate": "2025-01-03T00:00:00Z", "submitByDate": "2025-01-03T00:00:00Z", "checkDate": "2025-01-07T00:00:00Z", "checkCount": 3, "id": "3ee4bf31-6a66-472f-9909-1f8eef642c93", "companyId": "********", "type": "payperiod", "_rid": "NmJkAKiCbEfdMAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfdMAAAAAAAAA==/", "_etag": "\"9700ada3-0000-0100-0000-686fd2e70000\"", "_attachments": "attachments/", "_ts": 1752158951}, {"payPeriodId": "1020050191101915", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-01-04T00:00:00Z", "endDate": "2025-01-10T00:00:00Z", "submitByDate": "2025-01-10T00:00:00Z", "checkDate": "2025-01-14T00:00:00Z", "checkCount": 2, "id": "dc33e78c-b287-4011-b76c-098e3f70c6cf", "companyId": "********", "type": "payperiod", "_rid": "NmJkAKiCbEfeMAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfeMAAAAAAAAA==/", "_etag": "\"9700b1a3-0000-0100-0000-686fd2e70000\"", "_attachments": "attachments/", "_ts": 1752158951}, {"payPeriodId": "1020050255535780", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-01-11T00:00:00Z", "endDate": "2025-01-17T00:00:00Z", "submitByDate": "2025-01-17T00:00:00Z", "checkDate": "2025-01-21T00:00:00Z", "checkCount": 2, "id": "053344c1-a3ab-4e70-bf41-8f96765de088", "companyId": "********", "type": "payperiod", "_rid": "NmJkAKiCbEffMAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEffMAAAAAAAAA==/", "_etag": "\"9700b2a3-0000-0100-0000-686fd2e70000\"", "_attachments": "attachments/", "_ts": 1752158951}, {"payPeriodId": "1020050366200139", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-01-18T00:00:00Z", "endDate": "2025-01-24T00:00:00Z", "submitByDate": "2025-01-24T00:00:00Z", "checkDate": "2025-01-28T00:00:00Z", "checkCount": 2, "id": "c6a908d5-f3a1-4d93-acd4-a2a54c48bcaa", "companyId": "********", "type": "payperiod", "_rid": "NmJkAKiCbEfgMAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfgMAAAAAAAAA==/", "_etag": "\"9700b5a3-0000-0100-0000-686fd2e70000\"", "_attachments": "attachments/", "_ts": 1752158951}, {"payPeriodId": "1020050366200140", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-01-25T00:00:00Z", "endDate": "2025-01-31T00:00:00Z", "submitByDate": "2025-01-31T00:00:00Z", "checkDate": "2025-02-04T00:00:00Z", "checkCount": 4, "id": "9cc43f27-38a8-4538-8cc2-c4b35aa079a1", "companyId": "********", "type": "payperiod", "_rid": "NmJkAKiCbEfhMAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfhMAAAAAAAAA==/", "_etag": "\"9700b7a3-0000-0100-0000-686fd2e70000\"", "_attachments": "attachments/", "_ts": 1752158951}, {"payPeriodId": "1020051339964574", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-02-01T00:00:00Z", "endDate": "2025-02-07T00:00:00Z", "submitByDate": "2025-02-07T00:00:00Z", "checkDate": "2025-02-14T00:00:00Z", "checkCount": 2, "id": "443b15bb-de49-4679-aac8-e0328e9ff606", "companyId": "********", "type": "payperiod", "_rid": "NmJkAKiCbEfiMAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfiMAAAAAAAAA==/", "_etag": "\"9700baa3-0000-0100-0000-686fd2e70000\"", "_attachments": "attachments/", "_ts": 1752158951}, {"payPeriodId": "1020050455140009", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-02-08T00:00:00Z", "endDate": "2025-02-14T00:00:00Z", "submitByDate": "2025-02-14T00:00:00Z", "checkDate": "2025-02-18T00:00:00Z", "checkCount": 2, "id": "99ea12d6-61ca-42b6-8b94-15d7144bb23d", "companyId": "********", "type": "payperiod", "_rid": "NmJkAKiCbEfjMAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfjMAAAAAAAAA==/", "_etag": "\"9700bca3-0000-0100-0000-686fd2e70000\"", "_attachments": "attachments/", "_ts": 1752158951}, {"payPeriodId": "1020050564343613", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-02-15T00:00:00Z", "endDate": "2025-02-21T00:00:00Z", "submitByDate": "2025-02-21T00:00:00Z", "checkDate": "2025-02-25T00:00:00Z", "checkCount": 3, "id": "73595a70-b181-4b6b-a2db-79405f776a53", "companyId": "********", "type": "payperiod", "_rid": "NmJkAKiCbEfkMAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfkMAAAAAAAAA==/", "_etag": "\"9700bfa3-0000-0100-0000-686fd2e70000\"", "_attachments": "attachments/", "_ts": 1752158951}, {"payPeriodId": "1020050564389877", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-02-22T00:00:00Z", "endDate": "2025-02-28T00:00:00Z", "submitByDate": "2025-03-04T00:00:00Z", "checkDate": "2025-03-05T00:00:00Z", "checkCount": 3, "id": "0fc12bc8-f3dd-4dde-8766-c42a0cf33909", "companyId": "********", "type": "payperiod", "_rid": "NmJkAKiCbEflMAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEflMAAAAAAAAA==/", "_etag": "\"9700c3a3-0000-0100-0000-686fd2e70000\"", "_attachments": "attachments/", "_ts": 1752158951}, {"payPeriodId": "1020050685278132", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-03-01T00:00:00Z", "endDate": "2025-03-07T00:00:00Z", "submitByDate": "2025-03-07T00:00:00Z", "checkDate": "2025-03-11T00:00:00Z", "checkCount": 2, "id": "b5992dca-b91a-4cf8-9208-6042a4fbaf26", "companyId": "********", "type": "payperiod", "_rid": "NmJkAKiCbEfmMAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfmMAAAAAAAAA==/", "_etag": "\"9700c5a3-0000-0100-0000-686fd2e70000\"", "_attachments": "attachments/", "_ts": 1752158951}, {"payPeriodId": "1020050745549723", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-03-08T00:00:00Z", "endDate": "2025-03-14T00:00:00Z", "submitByDate": "2025-03-14T00:00:00Z", "checkDate": "2025-03-18T00:00:00Z", "checkCount": 2, "id": "383f2061-5bda-483e-9500-3b2ea97cdd0c", "companyId": "********", "type": "payperiod", "_rid": "NmJkAKiCbEfnMAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfnMAAAAAAAAA==/", "_etag": "\"9700c9a3-0000-0100-0000-686fd2e70000\"", "_attachments": "attachments/", "_ts": 1752158951}, {"payPeriodId": "1020050812229487", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-03-15T00:00:00Z", "endDate": "2025-03-21T00:00:00Z", "submitByDate": "2025-03-21T00:00:00Z", "checkDate": "2025-03-25T00:00:00Z", "checkCount": 2, "id": "6cdfacd9-82b6-444a-a8d4-689e038d46b6", "companyId": "********", "type": "payperiod", "_rid": "NmJkAKiCbEfoMAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfoMAAAAAAAAA==/", "_etag": "\"9700cba3-0000-0100-0000-686fd2e70000\"", "_attachments": "attachments/", "_ts": 1752158951}, {"payPeriodId": "1020050885374544", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-03-22T00:00:00Z", "endDate": "2025-03-28T00:00:00Z", "submitByDate": "2025-03-28T00:00:00Z", "checkDate": "2025-04-01T00:00:00Z", "checkCount": 0, "id": "34182420-ff36-4cfd-b7a6-7e75d2dfab51", "companyId": "********", "type": "payperiod", "_rid": "NmJkAKiCbEfpMAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfpMAAAAAAAAA==/", "_etag": "\"9700d2a3-0000-0100-0000-686fd2e80000\"", "_attachments": "attachments/", "_ts": 1752158952}, {"payPeriodId": "1020050956812374", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-03-29T00:00:00Z", "endDate": "2025-04-04T00:00:00Z", "submitByDate": "2025-04-04T00:00:00Z", "checkDate": "2025-04-08T00:00:00Z", "checkCount": 0, "id": "ee5422d7-a859-4bf7-bde7-2e477feb11d2", "companyId": "********", "type": "payperiod", "_rid": "NmJkAKiCbEfqMAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfqMAAAAAAAAA==/", "_etag": "\"9700d7a3-0000-0100-0000-686fd2e80000\"", "_attachments": "attachments/", "_ts": 1752158952}, {"payPeriodId": "1020051002851183", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-04-05T00:00:00Z", "endDate": "2025-04-11T00:00:00Z", "submitByDate": "2025-04-11T00:00:00Z", "checkDate": "2025-04-15T00:00:00Z", "checkCount": 0, "id": "4befe950-f8ac-47e6-9817-406fea6ccde1", "companyId": "********", "type": "payperiod", "_rid": "NmJkAKiCbEfrMAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfrMAAAAAAAAA==/", "_etag": "\"9700daa3-0000-0100-0000-686fd2e80000\"", "_attachments": "attachments/", "_ts": 1752158952}, {"payPeriodId": "1020051074914850", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-04-12T00:00:00Z", "endDate": "2025-04-18T00:00:00Z", "submitByDate": "2025-04-18T00:00:00Z", "checkDate": "2025-04-22T00:00:00Z", "checkCount": 0, "id": "fed39cff-ad67-4368-bac5-6744d5479e27", "companyId": "********", "type": "payperiod", "_rid": "NmJkAKiCbEfsMAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfsMAAAAAAAAA==/", "_etag": "\"9700dca3-0000-0100-0000-686fd2e80000\"", "_attachments": "attachments/", "_ts": 1752158952}, {"payPeriodId": "1020051120440384", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-04-19T00:00:00Z", "endDate": "2025-04-25T00:00:00Z", "submitByDate": "2025-04-25T00:00:00Z", "checkDate": "2025-04-29T00:00:00Z", "checkCount": 0, "id": "e2652655-445b-4122-a4ab-4eff231f3315", "companyId": "********", "type": "payperiod", "_rid": "NmJkAKiCbEftMAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEftMAAAAAAAAA==/", "_etag": "\"9700dea3-0000-0100-0000-686fd2e80000\"", "_attachments": "attachments/", "_ts": 1752158952}, {"payPeriodId": "1020051173298592", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-04-26T00:00:00Z", "endDate": "2025-05-02T00:00:00Z", "submitByDate": "2025-05-02T00:00:00Z", "checkDate": "2025-05-06T00:00:00Z", "checkCount": 0, "id": "5d9339c0-fee6-41e2-abff-104956c7de7b", "companyId": "********", "type": "payperiod", "_rid": "NmJkAKiCbEfuMAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfuMAAAAAAAAA==/", "_etag": "\"9700dfa3-0000-0100-0000-686fd2e80000\"", "_attachments": "attachments/", "_ts": 1752158952}, {"payPeriodId": "1020051237930936", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-05-03T00:00:00Z", "endDate": "2025-05-09T00:00:00Z", "submitByDate": "2025-05-09T00:00:00Z", "checkDate": "2025-05-13T00:00:00Z", "checkCount": 0, "id": "3d730acc-856d-4c01-968c-66e743da3198", "companyId": "********", "type": "payperiod", "_rid": "NmJkAKiCbEfvMAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfvMAAAAAAAAA==/", "_etag": "\"9700e2a3-0000-0100-0000-686fd2e80000\"", "_attachments": "attachments/", "_ts": 1752158952}, {"payPeriodId": "1020051345553714", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-05-10T00:00:00Z", "endDate": "2025-05-16T00:00:00Z", "submitByDate": "2025-05-16T00:00:00Z", "checkDate": "2025-05-20T00:00:00Z", "checkCount": 0, "id": "4ed4335a-f0f6-4d08-af6b-a599b1d164ff", "companyId": "********", "type": "payperiod", "_rid": "NmJkAKiCbEfwMAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfwMAAAAAAAAA==/", "_etag": "\"9700e3a3-0000-0100-0000-686fd2e80000\"", "_attachments": "attachments/", "_ts": 1752158952}, {"payPeriodId": "1020051349442273", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-05-17T00:00:00Z", "endDate": "2025-05-23T00:00:00Z", "submitByDate": "2025-05-22T00:00:00Z", "checkDate": "2025-05-27T00:00:00Z", "checkCount": 0, "id": "dd5b67a2-e767-4666-95a4-42577d8a409e", "companyId": "********", "type": "payperiod", "_rid": "NmJkAKiCbEfxMAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfxMAAAAAAAAA==/", "_etag": "\"9700e5a3-0000-0100-0000-686fd2e80000\"", "_attachments": "attachments/", "_ts": 1752158952}, {"payPeriodId": "1020051403401243", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-05-24T00:00:00Z", "endDate": "2025-05-30T00:00:00Z", "submitByDate": "2025-05-30T00:00:00Z", "checkDate": "2025-06-03T00:00:00Z", "checkCount": 0, "id": "7efa0937-aa82-4d29-8f38-fbf5b0976412", "companyId": "********", "type": "payperiod", "_rid": "NmJkAKiCbEfyMAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfyMAAAAAAAAA==/", "_etag": "\"9700e7a3-0000-0100-0000-686fd2e80000\"", "_attachments": "attachments/", "_ts": 1752158952}, {"payPeriodId": "1020051483575903", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-05-31T00:00:00Z", "endDate": "2025-06-06T00:00:00Z", "submitByDate": "2025-06-06T00:00:00Z", "checkDate": "2025-06-10T00:00:00Z", "checkCount": 0, "id": "54904d55-7135-4fa4-8632-acf2187c7823", "companyId": "********", "type": "payperiod", "_rid": "NmJkAKiCbEfzMAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfzMAAAAAAAAA==/", "_etag": "\"9700eca3-0000-0100-0000-686fd2e80000\"", "_attachments": "attachments/", "_ts": 1752158952}, {"payPeriodId": "1020051521882728", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-06-07T00:00:00Z", "endDate": "2025-06-13T00:00:00Z", "submitByDate": "2025-06-13T00:00:00Z", "checkDate": "2025-06-17T00:00:00Z", "checkCount": 0, "id": "dd24aa10-b040-4d0d-b9f2-c0e4c9c5b9de", "companyId": "********", "type": "payperiod", "_rid": "NmJkAKiCbEf0MAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf0MAAAAAAAAA==/", "_etag": "\"9700f1a3-0000-0100-0000-686fd2e80000\"", "_attachments": "attachments/", "_ts": 1752158952}, {"payPeriodId": "1020051588805903", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-06-14T00:00:00Z", "endDate": "2025-06-20T00:00:00Z", "submitByDate": "2025-06-20T00:00:00Z", "checkDate": "2025-06-24T00:00:00Z", "checkCount": 0, "id": "61b9778d-834b-4b6e-a976-c6a2423ddf89", "companyId": "********", "type": "payperiod", "_rid": "NmJkAKiCbEf1MAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf1MAAAAAAAAA==/", "_etag": "\"9700f5a3-0000-0100-0000-686fd2e80000\"", "_attachments": "attachments/", "_ts": 1752158952}, {"payPeriodId": "1020051644083927", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-06-21T00:00:00Z", "endDate": "2025-06-27T00:00:00Z", "submitByDate": "2025-06-27T00:00:00Z", "checkDate": "2025-07-01T00:00:00Z", "checkCount": 0, "id": "ebc63eed-8783-4459-95e8-93bd9c0f2fe7", "companyId": "********", "type": "payperiod", "_rid": "NmJkAKiCbEf2MAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf2MAAAAAAAAA==/", "_etag": "\"9700fda3-0000-0100-0000-686fd2e80000\"", "_attachments": "attachments/", "_ts": 1752158952}, {"payPeriodId": "1020051710357368", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-06-28T00:00:00Z", "endDate": "2025-07-04T00:00:00Z", "submitByDate": "2025-07-03T00:00:00Z", "checkDate": "2025-07-08T00:00:00Z", "checkCount": 0, "id": "7fed1b49-d116-42c1-bee4-6743760ad519", "companyId": "********", "type": "payperiod", "_rid": "NmJkAKiCbEf3MAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf3MAAAAAAAAA==/", "_etag": "\"9700ffa3-0000-0100-0000-686fd2e90000\"", "_attachments": "attachments/", "_ts": 1752158953}, {"payPeriodId": "1020051756265961", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-07-05T00:00:00Z", "endDate": "2025-07-11T00:00:00Z", "submitByDate": "2025-07-11T00:00:00Z", "checkDate": "2025-07-15T00:00:00Z", "checkCount": 0, "id": "1988b2cb-8e06-4660-a7ab-8c19b3780974", "companyId": "********", "type": "payperiod", "_rid": "NmJkAKiCbEf4MAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf4MAAAAAAAAA==/", "_etag": "\"970002a4-0000-0100-0000-686fd2e90000\"", "_attachments": "attachments/", "_ts": 1752158953}, {"payPeriodId": "1020051819730391", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-07-12T00:00:00Z", "endDate": "2025-07-18T00:00:00Z", "submitByDate": "2025-07-18T00:00:00Z", "checkDate": "2025-07-22T00:00:00Z", "checkCount": 0, "id": "086a7a7f-fe0e-4116-8107-5f7ba837372a", "companyId": "********", "type": "payperiod", "_rid": "NmJkAKiCbEf5MAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf5MAAAAAAAAA==/", "_etag": "\"970005a4-0000-0100-0000-686fd2e90000\"", "_attachments": "attachments/", "_ts": 1752158953}, {"payPeriodId": "1020051888295963", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-07-19T00:00:00Z", "endDate": "2025-07-25T00:00:00Z", "submitByDate": "2025-07-25T00:00:00Z", "checkDate": "2025-07-29T00:00:00Z", "checkCount": 0, "id": "50e740c7-c9ed-48ec-8563-0c4115376c37", "companyId": "********", "type": "payperiod", "_rid": "NmJkAKiCbEf6MAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf6MAAAAAAAAA==/", "_etag": "\"970007a4-0000-0100-0000-686fd2e90000\"", "_attachments": "attachments/", "_ts": 1752158953}, {"payPeriodId": "1020051942134426", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-07-26T00:00:00Z", "endDate": "2025-08-01T00:00:00Z", "submitByDate": "2025-08-01T00:00:00Z", "checkDate": "2025-08-05T00:00:00Z", "checkCount": 0, "id": "6e1b2966-0adc-4111-8e17-a2173f3fb612", "companyId": "********", "type": "payperiod", "_rid": "NmJkAKiCbEf7MAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf7MAAAAAAAAA==/", "_etag": "\"970008a4-0000-0100-0000-686fd2e90000\"", "_attachments": "attachments/", "_ts": 1752158953}, {"payPeriodId": "1020052011395069", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-08-02T00:00:00Z", "endDate": "2025-08-08T00:00:00Z", "submitByDate": "2025-08-08T00:00:00Z", "checkDate": "2025-08-12T00:00:00Z", "checkCount": 0, "id": "f23475d7-fb5c-4127-881a-30b99cf12594", "companyId": "********", "type": "payperiod", "_rid": "NmJkAKiCbEf8MAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf8MAAAAAAAAA==/", "_etag": "\"97000da4-0000-0100-0000-686fd2e90000\"", "_attachments": "attachments/", "_ts": 1752158953}, {"payPeriodId": "1020052063818754", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-08-09T00:00:00Z", "endDate": "2025-08-15T00:00:00Z", "submitByDate": "2025-08-15T00:00:00Z", "checkDate": "2025-08-19T00:00:00Z", "checkCount": 0, "id": "810b99b6-7d8c-45cf-abea-e4cb48ad61f3", "companyId": "********", "type": "payperiod", "_rid": "NmJkAKiCbEf9MAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf9MAAAAAAAAA==/", "_etag": "\"97000fa4-0000-0100-0000-686fd2e90000\"", "_attachments": "attachments/", "_ts": 1752158953}, {"payPeriodId": "1020052139654955", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-08-16T00:00:00Z", "endDate": "2025-08-22T00:00:00Z", "submitByDate": "2025-08-22T00:00:00Z", "checkDate": "2025-08-26T00:00:00Z", "checkCount": 0, "id": "981a6d22-028e-42a6-a081-e087a8c8b13f", "companyId": "********", "type": "payperiod", "_rid": "NmJkAKiCbEf+MAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf+MAAAAAAAAA==/", "_etag": "\"970013a4-0000-0100-0000-686fd2e90000\"", "_attachments": "attachments/", "_ts": 1752158953}, {"payPeriodId": "1020052186305533", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-08-23T00:00:00Z", "endDate": "2025-08-29T00:00:00Z", "submitByDate": "2025-08-28T00:00:00Z", "checkDate": "2025-09-02T00:00:00Z", "checkCount": 0, "id": "bc2ce672-d4b7-4292-aad3-8082eb2641ee", "companyId": "********", "type": "payperiod", "_rid": "NmJkAKiCbEf-MAAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf-MAAAAAAAAA==/", "_etag": "\"970016a4-0000-0100-0000-686fd2e90000\"", "_attachments": "attachments/", "_ts": 1752158953}, {"payPeriodId": "1020052260193476", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-08-30T00:00:00Z", "endDate": "2025-09-05T00:00:00Z", "submitByDate": "2025-09-05T00:00:00Z", "checkDate": "2025-09-09T00:00:00Z", "checkCount": 0, "id": "2cf4de46-cd28-4df5-b5d6-465c13bc1eca", "companyId": "********", "type": "payperiod", "_rid": "NmJkAKiCbEcAMQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcAMQAAAAAAAA==/", "_etag": "\"97001aa4-0000-0100-0000-686fd2e90000\"", "_attachments": "attachments/", "_ts": 1752158953}, {"payPeriodId": "1020052300495569", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-09-06T00:00:00Z", "endDate": "2025-09-12T00:00:00Z", "submitByDate": "2025-09-12T00:00:00Z", "checkDate": "2025-09-16T00:00:00Z", "checkCount": 0, "id": "0da8e0c5-3147-4cd6-b742-93eadc7d538a", "companyId": "********", "type": "payperiod", "_rid": "NmJkAKiCbEcBMQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcBMQAAAAAAAA==/", "_etag": "\"97001ba4-0000-0100-0000-686fd2e90000\"", "_attachments": "attachments/", "_ts": 1752158953}, {"payPeriodId": "1020052361280886", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-09-13T00:00:00Z", "endDate": "2025-09-19T00:00:00Z", "submitByDate": "2025-09-19T00:00:00Z", "checkDate": "2025-09-23T00:00:00Z", "checkCount": 0, "id": "f79d009a-0784-46cc-84e7-262e4c2fd875", "companyId": "********", "type": "payperiod", "_rid": "NmJkAKiCbEcCMQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcCMQAAAAAAAA==/", "_etag": "\"97001fa4-0000-0100-0000-686fd2e90000\"", "_attachments": "attachments/", "_ts": 1752158953}, {"payPeriodId": "1020052418058372", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-09-20T00:00:00Z", "endDate": "2025-09-26T00:00:00Z", "submitByDate": "2025-09-26T00:00:00Z", "checkDate": "2025-09-30T00:00:00Z", "checkCount": 0, "id": "0be8ea86-e75e-4eec-944c-877a1797fe13", "companyId": "********", "type": "payperiod", "_rid": "NmJkAKiCbEcDMQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcDMQAAAAAAAA==/", "_etag": "\"970023a4-0000-0100-0000-686fd2e90000\"", "_attachments": "attachments/", "_ts": 1752158953}, {"payPeriodId": "1020052484178096", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-09-27T00:00:00Z", "endDate": "2025-10-03T00:00:00Z", "submitByDate": "2025-10-03T00:00:00Z", "checkDate": "2025-10-07T00:00:00Z", "checkCount": 0, "id": "89867b27-b480-4bfa-a91c-ac2f28b22884", "companyId": "********", "type": "payperiod", "_rid": "NmJkAKiCbEcEMQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcEMQAAAAAAAA==/", "_etag": "\"970025a4-0000-0100-0000-686fd2e90000\"", "_attachments": "attachments/", "_ts": 1752158953}, {"payPeriodId": "1020050132589552", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2024-12-28T00:00:00Z", "endDate": "2025-01-03T00:00:00Z", "submitByDate": "2025-01-03T00:00:00Z", "checkDate": "2025-01-07T00:00:00Z", "checkCount": 3, "id": "20c06dcf-879d-48dd-95b2-c98fcfc9aacf", "companyId": "********", "type": "payperiod", "_rid": "NmJkAKiCbEcIMQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcIMQAAAAAAAA==/", "_etag": "\"970031a4-0000-0100-0000-686fd2ea0000\"", "_attachments": "attachments/", "_ts": 1752158954}, {"payPeriodId": "1020050191101915", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-01-04T00:00:00Z", "endDate": "2025-01-10T00:00:00Z", "submitByDate": "2025-01-10T00:00:00Z", "checkDate": "2025-01-14T00:00:00Z", "checkCount": 2, "id": "3165ec31-49c8-42fd-a02a-dbdb2bdc851c", "companyId": "********", "type": "payperiod", "_rid": "NmJkAKiCbEcJMQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcJMQAAAAAAAA==/", "_etag": "\"970033a4-0000-0100-0000-686fd2ea0000\"", "_attachments": "attachments/", "_ts": 1752158954}, {"payPeriodId": "1020050255535780", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-01-11T00:00:00Z", "endDate": "2025-01-17T00:00:00Z", "submitByDate": "2025-01-17T00:00:00Z", "checkDate": "2025-01-21T00:00:00Z", "checkCount": 2, "id": "242ff5ce-e84e-4dbb-a537-76e95c3d06ad", "companyId": "********", "type": "payperiod", "_rid": "NmJkAKiCbEcKMQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcKMQAAAAAAAA==/", "_etag": "\"970036a4-0000-0100-0000-686fd2ea0000\"", "_attachments": "attachments/", "_ts": 1752158954}, {"payPeriodId": "1020050366200139", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-01-18T00:00:00Z", "endDate": "2025-01-24T00:00:00Z", "submitByDate": "2025-01-24T00:00:00Z", "checkDate": "2025-01-28T00:00:00Z", "checkCount": 2, "id": "c02a9c23-3626-4890-a165-4fb7a295af96", "companyId": "********", "type": "payperiod", "_rid": "NmJkAKiCbEcLMQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcLMQAAAAAAAA==/", "_etag": "\"97003ca4-0000-0100-0000-686fd2ea0000\"", "_attachments": "attachments/", "_ts": 1752158954}, {"payPeriodId": "1020050366200140", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-01-25T00:00:00Z", "endDate": "2025-01-31T00:00:00Z", "submitByDate": "2025-01-31T00:00:00Z", "checkDate": "2025-02-04T00:00:00Z", "checkCount": 4, "id": "34e7f2a3-e9bb-4ef8-ac5b-21bae29c60e0", "companyId": "********", "type": "payperiod", "_rid": "NmJkAKiCbEcMMQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcMMQAAAAAAAA==/", "_etag": "\"97003fa4-0000-0100-0000-686fd2ea0000\"", "_attachments": "attachments/", "_ts": 1752158954}, {"payPeriodId": "1020051339964574", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-02-01T00:00:00Z", "endDate": "2025-02-07T00:00:00Z", "submitByDate": "2025-02-07T00:00:00Z", "checkDate": "2025-02-14T00:00:00Z", "checkCount": 2, "id": "99c7f047-cb31-4777-ba6b-de04f3cbaec7", "companyId": "********", "type": "payperiod", "_rid": "NmJkAKiCbEcNMQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcNMQAAAAAAAA==/", "_etag": "\"970042a4-0000-0100-0000-686fd2ea0000\"", "_attachments": "attachments/", "_ts": 1752158954}, {"payPeriodId": "1020050455140009", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-02-08T00:00:00Z", "endDate": "2025-02-14T00:00:00Z", "submitByDate": "2025-02-14T00:00:00Z", "checkDate": "2025-02-18T00:00:00Z", "checkCount": 2, "id": "8749d991-9c90-4a1a-ad01-4566435859d4", "companyId": "********", "type": "payperiod", "_rid": "NmJkAKiCbEcOMQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcOMQAAAAAAAA==/", "_etag": "\"970044a4-0000-0100-0000-686fd2ea0000\"", "_attachments": "attachments/", "_ts": 1752158954}, {"payPeriodId": "1020050564343613", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-02-15T00:00:00Z", "endDate": "2025-02-21T00:00:00Z", "submitByDate": "2025-02-21T00:00:00Z", "checkDate": "2025-02-25T00:00:00Z", "checkCount": 3, "id": "cb93c380-021f-4b47-90a6-0718d237d742", "companyId": "********", "type": "payperiod", "_rid": "NmJkAKiCbEcPMQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcPMQAAAAAAAA==/", "_etag": "\"970046a4-0000-0100-0000-686fd2ea0000\"", "_attachments": "attachments/", "_ts": 1752158954}, {"payPeriodId": "1020050564389877", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-02-22T00:00:00Z", "endDate": "2025-02-28T00:00:00Z", "submitByDate": "2025-03-04T00:00:00Z", "checkDate": "2025-03-05T00:00:00Z", "checkCount": 3, "id": "cd5355af-fe6d-4b19-b846-0772722dcfa1", "companyId": "********", "type": "payperiod", "_rid": "NmJkAKiCbEcQMQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcQMQAAAAAAAA==/", "_etag": "\"970049a4-0000-0100-0000-686fd2ea0000\"", "_attachments": "attachments/", "_ts": 1752158954}, {"payPeriodId": "1020050685278132", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-03-01T00:00:00Z", "endDate": "2025-03-07T00:00:00Z", "submitByDate": "2025-03-07T00:00:00Z", "checkDate": "2025-03-11T00:00:00Z", "checkCount": 2, "id": "b7750f60-cedb-441a-bf9a-c5d2d7f6abe4", "companyId": "********", "type": "payperiod", "_rid": "NmJkAKiCbEcRMQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcRMQAAAAAAAA==/", "_etag": "\"97004ca4-0000-0100-0000-686fd2ea0000\"", "_attachments": "attachments/", "_ts": 1752158954}, {"payPeriodId": "1020050745549723", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-03-08T00:00:00Z", "endDate": "2025-03-14T00:00:00Z", "submitByDate": "2025-03-14T00:00:00Z", "checkDate": "2025-03-18T00:00:00Z", "checkCount": 2, "id": "365b5ff3-9d30-48dc-80ea-7c3e24c3d9c6", "companyId": "********", "type": "payperiod", "_rid": "NmJkAKiCbEcSMQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcSMQAAAAAAAA==/", "_etag": "\"970050a4-0000-0100-0000-686fd2eb0000\"", "_attachments": "attachments/", "_ts": 1752158955}, {"payPeriodId": "1020050812229487", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-03-15T00:00:00Z", "endDate": "2025-03-21T00:00:00Z", "submitByDate": "2025-03-21T00:00:00Z", "checkDate": "2025-03-25T00:00:00Z", "checkCount": 2, "id": "5ca18084-2725-4d0d-b815-71bef2c60846", "companyId": "********", "type": "payperiod", "_rid": "NmJkAKiCbEcTMQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcTMQAAAAAAAA==/", "_etag": "\"970059a4-0000-0100-0000-686fd2eb0000\"", "_attachments": "attachments/", "_ts": 1752158955}, {"payPeriodId": "1020050885374544", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-03-22T00:00:00Z", "endDate": "2025-03-28T00:00:00Z", "submitByDate": "2025-03-28T00:00:00Z", "checkDate": "2025-04-01T00:00:00Z", "checkCount": 3, "id": "dc93232e-fc92-4754-a11d-16c236377438", "companyId": "********", "type": "payperiod", "_rid": "NmJkAKiCbEcUMQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcUMQAAAAAAAA==/", "_etag": "\"97005aa4-0000-0100-0000-686fd2eb0000\"", "_attachments": "attachments/", "_ts": 1752158955}, {"payPeriodId": "1020050956812374", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-03-29T00:00:00Z", "endDate": "2025-04-04T00:00:00Z", "submitByDate": "2025-04-04T00:00:00Z", "checkDate": "2025-04-08T00:00:00Z", "checkCount": 3, "id": "495b41e5-2da7-4e6a-b52d-e38bece99342", "companyId": "********", "type": "payperiod", "_rid": "NmJkAKiCbEcVMQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcVMQAAAAAAAA==/", "_etag": "\"97005ba4-0000-0100-0000-686fd2eb0000\"", "_attachments": "attachments/", "_ts": 1752158955}, {"payPeriodId": "1020051002851183", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-04-05T00:00:00Z", "endDate": "2025-04-11T00:00:00Z", "submitByDate": "2025-04-11T00:00:00Z", "checkDate": "2025-04-15T00:00:00Z", "checkCount": 2, "id": "1fdfcf73-c2a4-4dcb-817c-9426ffb9ef13", "companyId": "********", "type": "payperiod", "_rid": "NmJkAKiCbEcWMQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcWMQAAAAAAAA==/", "_etag": "\"97005fa4-0000-0100-0000-686fd2eb0000\"", "_attachments": "attachments/", "_ts": 1752158955}, {"payPeriodId": "1020051074914850", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-04-12T00:00:00Z", "endDate": "2025-04-18T00:00:00Z", "submitByDate": "2025-04-18T00:00:00Z", "checkDate": "2025-04-22T00:00:00Z", "checkCount": 2, "id": "5a330542-b381-43c4-9587-8cd15df993e2", "companyId": "********", "type": "payperiod", "_rid": "NmJkAKiCbEcXMQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcXMQAAAAAAAA==/", "_etag": "\"970062a4-0000-0100-0000-686fd2eb0000\"", "_attachments": "attachments/", "_ts": 1752158955}, {"payPeriodId": "1020051120440384", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-04-19T00:00:00Z", "endDate": "2025-04-25T00:00:00Z", "submitByDate": "2025-04-25T00:00:00Z", "checkDate": "2025-04-29T00:00:00Z", "checkCount": 3, "id": "7c618fca-4c7e-4bdf-803c-eee24a6f9c30", "companyId": "********", "type": "payperiod", "_rid": "NmJkAKiCbEcYMQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcYMQAAAAAAAA==/", "_etag": "\"970066a4-0000-0100-0000-686fd2eb0000\"", "_attachments": "attachments/", "_ts": 1752158955}, {"payPeriodId": "1020051173298592", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-04-26T00:00:00Z", "endDate": "2025-05-02T00:00:00Z", "submitByDate": "2025-05-02T00:00:00Z", "checkDate": "2025-05-06T00:00:00Z", "checkCount": 3, "id": "f0e5a2be-d84e-412b-9c26-5c4185db3132", "companyId": "********", "type": "payperiod", "_rid": "NmJkAKiCbEcZMQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcZMQAAAAAAAA==/", "_etag": "\"97006da4-0000-0100-0000-686fd2eb0000\"", "_attachments": "attachments/", "_ts": 1752158955}, {"payPeriodId": "1020051237930936", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-05-03T00:00:00Z", "endDate": "2025-05-09T00:00:00Z", "submitByDate": "2025-05-09T00:00:00Z", "checkDate": "2025-05-13T00:00:00Z", "checkCount": 2, "id": "7a60d349-78a7-4c4c-9196-27fee5333b98", "companyId": "********", "type": "payperiod", "_rid": "NmJkAKiCbEcaMQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcaMQAAAAAAAA==/", "_etag": "\"970073a4-0000-0100-0000-686fd2eb0000\"", "_attachments": "attachments/", "_ts": 1752158955}, {"payPeriodId": "1020051345553714", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-05-10T00:00:00Z", "endDate": "2025-05-16T00:00:00Z", "submitByDate": "2025-05-16T00:00:00Z", "checkDate": "2025-05-20T00:00:00Z", "checkCount": 2, "id": "44851a92-7d49-49a5-8a64-9147948f8248", "companyId": "********", "type": "payperiod", "_rid": "NmJkAKiCbEcbMQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcbMQAAAAAAAA==/", "_etag": "\"970076a4-0000-0100-0000-686fd2eb0000\"", "_attachments": "attachments/", "_ts": 1752158955}, {"payPeriodId": "1020051349442273", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-05-17T00:00:00Z", "endDate": "2025-05-23T00:00:00Z", "submitByDate": "2025-05-22T00:00:00Z", "checkDate": "2025-05-27T00:00:00Z", "checkCount": 2, "id": "5e3e95b0-9eb5-46cf-be65-2bb7a8f31bba", "companyId": "********", "type": "payperiod", "_rid": "NmJkAKiCbEccMQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEccMQAAAAAAAA==/", "_etag": "\"97007aa4-0000-0100-0000-686fd2eb0000\"", "_attachments": "attachments/", "_ts": 1752158955}, {"payPeriodId": "1020051403401243", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-05-24T00:00:00Z", "endDate": "2025-05-30T00:00:00Z", "submitByDate": "2025-05-30T00:00:00Z", "checkDate": "2025-06-03T00:00:00Z", "checkCount": 4, "id": "2ed94ef2-2c28-4d9d-bee9-940f7ccb722a", "companyId": "********", "type": "payperiod", "_rid": "NmJkAKiCbEcdMQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcdMQAAAAAAAA==/", "_etag": "\"97007da4-0000-0100-0000-686fd2eb0000\"", "_attachments": "attachments/", "_ts": 1752158955}, {"payPeriodId": "1020051483575903", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-05-31T00:00:00Z", "endDate": "2025-06-06T00:00:00Z", "submitByDate": "2025-06-06T00:00:00Z", "checkDate": "2025-06-10T00:00:00Z", "checkCount": 2, "id": "7568f811-268b-4ac8-a3fc-ce0703d4c594", "companyId": "********", "type": "payperiod", "_rid": "NmJkAKiCbEceMQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEceMQAAAAAAAA==/", "_etag": "\"97007fa4-0000-0100-0000-686fd2eb0000\"", "_attachments": "attachments/", "_ts": 1752158955}, {"payPeriodId": "1020051521882728", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-06-07T00:00:00Z", "endDate": "2025-06-13T00:00:00Z", "submitByDate": "2025-06-13T00:00:00Z", "checkDate": "2025-06-17T00:00:00Z", "checkCount": 2, "id": "9e6beb9d-54ad-4a47-864c-b1155d58593f", "companyId": "********", "type": "payperiod", "_rid": "NmJkAKiCbEcfMQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcfMQAAAAAAAA==/", "_etag": "\"970083a4-0000-0100-0000-686fd2eb0000\"", "_attachments": "attachments/", "_ts": 1752158955}, {"payPeriodId": "1020051588805903", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-06-14T00:00:00Z", "endDate": "2025-06-20T00:00:00Z", "submitByDate": "2025-06-20T00:00:00Z", "checkDate": "2025-06-24T00:00:00Z", "checkCount": 2, "id": "ec2ad795-0b52-4307-becd-5e2a8f5f670d", "companyId": "********", "type": "payperiod", "_rid": "NmJkAKiCbEcgMQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcgMQAAAAAAAA==/", "_etag": "\"970085a4-0000-0100-0000-686fd2ec0000\"", "_attachments": "attachments/", "_ts": 1752158956}, {"payPeriodId": "1020051644083927", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-06-21T00:00:00Z", "endDate": "2025-06-27T00:00:00Z", "submitByDate": "2025-06-27T00:00:00Z", "checkDate": "2025-07-01T00:00:00Z", "checkCount": 2, "id": "e62ef909-e3a1-4df4-9d67-f6499f52cfd3", "companyId": "********", "type": "payperiod", "_rid": "NmJkAKiCbEchMQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEchMQAAAAAAAA==/", "_etag": "\"97008aa4-0000-0100-0000-686fd2ec0000\"", "_attachments": "attachments/", "_ts": 1752158956}, {"payPeriodId": "1020051710357368", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-06-28T00:00:00Z", "endDate": "2025-07-04T00:00:00Z", "submitByDate": "2025-07-03T00:00:00Z", "checkDate": "2025-07-08T00:00:00Z", "checkCount": 0, "id": "a349150b-f0c3-46ee-9cc7-fa6a66898cba", "companyId": "********", "type": "payperiod", "_rid": "NmJkAKiCbEciMQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEciMQAAAAAAAA==/", "_etag": "\"97008da4-0000-0100-0000-686fd2ec0000\"", "_attachments": "attachments/", "_ts": 1752158956}, {"payPeriodId": "1020051756265961", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-07-05T00:00:00Z", "endDate": "2025-07-11T00:00:00Z", "submitByDate": "2025-07-11T00:00:00Z", "checkDate": "2025-07-15T00:00:00Z", "checkCount": 0, "id": "5ee892e6-f771-47aa-b03c-f121ea120163", "companyId": "********", "type": "payperiod", "_rid": "NmJkAKiCbEcjMQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcjMQAAAAAAAA==/", "_etag": "\"970090a4-0000-0100-0000-686fd2ec0000\"", "_attachments": "attachments/", "_ts": 1752158956}, {"payPeriodId": "1020051819730391", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-07-12T00:00:00Z", "endDate": "2025-07-18T00:00:00Z", "submitByDate": "2025-07-18T00:00:00Z", "checkDate": "2025-07-22T00:00:00Z", "checkCount": 0, "id": "10e642f0-e8ca-4c7b-aa6f-191fdc7efc1e", "companyId": "********", "type": "payperiod", "_rid": "NmJkAKiCbEckMQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEckMQAAAAAAAA==/", "_etag": "\"970093a4-0000-0100-0000-686fd2ec0000\"", "_attachments": "attachments/", "_ts": 1752158956}, {"payPeriodId": "1020051888295963", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-07-19T00:00:00Z", "endDate": "2025-07-25T00:00:00Z", "submitByDate": "2025-07-25T00:00:00Z", "checkDate": "2025-07-29T00:00:00Z", "checkCount": 0, "id": "944fa170-8ba4-4d5a-ae90-72f30c0d0247", "companyId": "********", "type": "payperiod", "_rid": "NmJkAKiCbEclMQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEclMQAAAAAAAA==/", "_etag": "\"970096a4-0000-0100-0000-686fd2ec0000\"", "_attachments": "attachments/", "_ts": 1752158956}, {"payPeriodId": "1020051942134426", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-07-26T00:00:00Z", "endDate": "2025-08-01T00:00:00Z", "submitByDate": "2025-08-01T00:00:00Z", "checkDate": "2025-08-05T00:00:00Z", "checkCount": 0, "id": "7a4d3569-d8fb-4390-a8a7-d87e45edf427", "companyId": "********", "type": "payperiod", "_rid": "NmJkAKiCbEcmMQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcmMQAAAAAAAA==/", "_etag": "\"970098a4-0000-0100-0000-686fd2ec0000\"", "_attachments": "attachments/", "_ts": 1752158956}, {"payPeriodId": "1020052011395069", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-08-02T00:00:00Z", "endDate": "2025-08-08T00:00:00Z", "submitByDate": "2025-08-08T00:00:00Z", "checkDate": "2025-08-12T00:00:00Z", "checkCount": 0, "id": "af42b6b5-3068-4f81-9a20-337eae5fb185", "companyId": "********", "type": "payperiod", "_rid": "NmJkAKiCbEcnMQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcnMQAAAAAAAA==/", "_etag": "\"97009aa4-0000-0100-0000-686fd2ec0000\"", "_attachments": "attachments/", "_ts": 1752158956}, {"payPeriodId": "1020052063818754", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-08-09T00:00:00Z", "endDate": "2025-08-15T00:00:00Z", "submitByDate": "2025-08-15T00:00:00Z", "checkDate": "2025-08-19T00:00:00Z", "checkCount": 0, "id": "bc690949-0aac-472e-990e-156bf21da4bc", "companyId": "********", "type": "payperiod", "_rid": "NmJkAKiCbEcoMQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcoMQAAAAAAAA==/", "_etag": "\"97009fa4-0000-0100-0000-686fd2ec0000\"", "_attachments": "attachments/", "_ts": 1752158956}, {"payPeriodId": "1020052139654955", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-08-16T00:00:00Z", "endDate": "2025-08-22T00:00:00Z", "submitByDate": "2025-08-22T00:00:00Z", "checkDate": "2025-08-26T00:00:00Z", "checkCount": 0, "id": "7abdfbef-c75a-4eae-a08c-f4772e9bdf3a", "companyId": "********", "type": "payperiod", "_rid": "NmJkAKiCbEcpMQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcpMQAAAAAAAA==/", "_etag": "\"9700a3a4-0000-0100-0000-686fd2ec0000\"", "_attachments": "attachments/", "_ts": 1752158956}, {"payPeriodId": "1020052186305533", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-08-23T00:00:00Z", "endDate": "2025-08-29T00:00:00Z", "submitByDate": "2025-08-28T00:00:00Z", "checkDate": "2025-09-02T00:00:00Z", "checkCount": 0, "id": "72111159-eb65-49cb-be26-287c132905d3", "companyId": "********", "type": "payperiod", "_rid": "NmJkAKiCbEcqMQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcqMQAAAAAAAA==/", "_etag": "\"9700a5a4-0000-0100-0000-686fd2ec0000\"", "_attachments": "attachments/", "_ts": 1752158956}, {"payPeriodId": "1020052260193476", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-08-30T00:00:00Z", "endDate": "2025-09-05T00:00:00Z", "submitByDate": "2025-09-05T00:00:00Z", "checkDate": "2025-09-09T00:00:00Z", "checkCount": 0, "id": "deb86cf3-7ff5-4034-9268-56b6f75f3070", "companyId": "********", "type": "payperiod", "_rid": "NmJkAKiCbEcrMQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcrMQAAAAAAAA==/", "_etag": "\"9700a6a4-0000-0100-0000-686fd2ec0000\"", "_attachments": "attachments/", "_ts": 1752158956}, {"payPeriodId": "1020052300495569", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-09-06T00:00:00Z", "endDate": "2025-09-12T00:00:00Z", "submitByDate": "2025-09-12T00:00:00Z", "checkDate": "2025-09-16T00:00:00Z", "checkCount": 0, "id": "8c0a6838-4c8b-40c8-b3b5-ad6f99b0b024", "companyId": "********", "type": "payperiod", "_rid": "NmJkAKiCbEcsMQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcsMQAAAAAAAA==/", "_etag": "\"9700a9a4-0000-0100-0000-686fd2ec0000\"", "_attachments": "attachments/", "_ts": 1752158956}, {"payPeriodId": "1020052361280886", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-09-13T00:00:00Z", "endDate": "2025-09-19T00:00:00Z", "submitByDate": "2025-09-19T00:00:00Z", "checkDate": "2025-09-23T00:00:00Z", "checkCount": 0, "id": "7419351a-c4a4-49a6-8b9a-4dd9c635b6af", "companyId": "********", "type": "payperiod", "_rid": "NmJkAKiCbEctMQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEctMQAAAAAAAA==/", "_etag": "\"9700ada4-0000-0100-0000-686fd2ed0000\"", "_attachments": "attachments/", "_ts": 1752158957}, {"payPeriodId": "1020052418058372", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-09-20T00:00:00Z", "endDate": "2025-09-26T00:00:00Z", "submitByDate": "2025-09-26T00:00:00Z", "checkDate": "2025-09-30T00:00:00Z", "checkCount": 0, "id": "cb9bfbbe-e90e-4d45-9491-21980cea5311", "companyId": "********", "type": "payperiod", "_rid": "NmJkAKiCbEcuMQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcuMQAAAAAAAA==/", "_etag": "\"9700afa4-0000-0100-0000-686fd2ed0000\"", "_attachments": "attachments/", "_ts": 1752158957}, {"payPeriodId": "1020052484178096", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-09-27T00:00:00Z", "endDate": "2025-10-03T00:00:00Z", "submitByDate": "2025-10-03T00:00:00Z", "checkDate": "2025-10-07T00:00:00Z", "checkCount": 0, "id": "89f350e4-fff3-4fb3-bd9c-db1ae068e5ac", "companyId": "********", "type": "payperiod", "_rid": "NmJkAKiCbEcvMQAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcvMQAAAAAAAA==/", "_etag": "\"9700b2a4-0000-0100-0000-686fd2ed0000\"", "_attachments": "attachments/", "_ts": 1752158957}, {"payPeriodId": "1020050132589552", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2024-12-28T00:00:00Z", "endDate": "2025-01-03T00:00:00Z", "submitByDate": "2025-01-03T00:00:00Z", "checkDate": "2025-01-07T00:00:00Z", "checkCount": 3, "id": "910afc94-64fa-4e83-9dc8-839018eb<PERSON>be", "companyId": "********", "type": "payperiod", "_rid": "NmJkAKiCbEcKKgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcKKgEAAAAAAA==/", "_etag": "\"9d00b7dd-0000-0100-0000-686ff95e0000\"", "_attachments": "attachments/", "_ts": 1752168798}, {"payPeriodId": "1020050191101915", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-01-04T00:00:00Z", "endDate": "2025-01-10T00:00:00Z", "submitByDate": "2025-01-10T00:00:00Z", "checkDate": "2025-01-14T00:00:00Z", "checkCount": 2, "id": "5478de9c-7382-4968-9420-461278e3a8b2", "companyId": "********", "type": "payperiod", "_rid": "NmJkAKiCbEcLKgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcLKgEAAAAAAA==/", "_etag": "\"9d00b9dd-0000-0100-0000-686ff95e0000\"", "_attachments": "attachments/", "_ts": 1752168798}, {"payPeriodId": "1020050255535780", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-01-11T00:00:00Z", "endDate": "2025-01-17T00:00:00Z", "submitByDate": "2025-01-17T00:00:00Z", "checkDate": "2025-01-21T00:00:00Z", "checkCount": 2, "id": "55bd3cf3-8105-4886-a0c5-fc9d26d29cfe", "companyId": "********", "type": "payperiod", "_rid": "NmJkAKiCbEcMKgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcMKgEAAAAAAA==/", "_etag": "\"9d00bbdd-0000-0100-0000-686ff95f0000\"", "_attachments": "attachments/", "_ts": 1752168799}, {"payPeriodId": "1020050366200139", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-01-18T00:00:00Z", "endDate": "2025-01-24T00:00:00Z", "submitByDate": "2025-01-24T00:00:00Z", "checkDate": "2025-01-28T00:00:00Z", "checkCount": 2, "id": "c0f4d036-c7d6-4827-ab3b-cd07c66fd180", "companyId": "********", "type": "payperiod", "_rid": "NmJkAKiCbEcNKgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcNKgEAAAAAAA==/", "_etag": "\"9d00c0dd-0000-0100-0000-686ff95f0000\"", "_attachments": "attachments/", "_ts": 1752168799}, {"payPeriodId": "1020050366200140", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-01-25T00:00:00Z", "endDate": "2025-01-31T00:00:00Z", "submitByDate": "2025-01-31T00:00:00Z", "checkDate": "2025-02-04T00:00:00Z", "checkCount": 4, "id": "3c3ce3cc-3bdc-4f0a-8053-fbc68fe644ea", "companyId": "********", "type": "payperiod", "_rid": "NmJkAKiCbEcOKgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcOKgEAAAAAAA==/", "_etag": "\"9d00c2dd-0000-0100-0000-686ff95f0000\"", "_attachments": "attachments/", "_ts": 1752168799}, {"payPeriodId": "1020051339964574", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-02-01T00:00:00Z", "endDate": "2025-02-07T00:00:00Z", "submitByDate": "2025-02-07T00:00:00Z", "checkDate": "2025-02-14T00:00:00Z", "checkCount": 2, "id": "a579c218-2839-4514-a82c-8d1cf6503cf2", "companyId": "********", "type": "payperiod", "_rid": "NmJkAKiCbEcPKgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcPKgEAAAAAAA==/", "_etag": "\"9d00c6dd-0000-0100-0000-686ff95f0000\"", "_attachments": "attachments/", "_ts": 1752168799}, {"payPeriodId": "1020050455140009", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-02-08T00:00:00Z", "endDate": "2025-02-14T00:00:00Z", "submitByDate": "2025-02-14T00:00:00Z", "checkDate": "2025-02-18T00:00:00Z", "checkCount": 2, "id": "9716ac07-1c58-41b1-88f2-89a1e7085e53", "companyId": "********", "type": "payperiod", "_rid": "NmJkAKiCbEcQKgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcQKgEAAAAAAA==/", "_etag": "\"9d00c8dd-0000-0100-0000-686ff95f0000\"", "_attachments": "attachments/", "_ts": 1752168799}, {"payPeriodId": "1020050564343613", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-02-15T00:00:00Z", "endDate": "2025-02-21T00:00:00Z", "submitByDate": "2025-02-21T00:00:00Z", "checkDate": "2025-02-25T00:00:00Z", "checkCount": 3, "id": "e349e8ad-4286-4767-8f89-4cba9188cedd", "companyId": "********", "type": "payperiod", "_rid": "NmJkAKiCbEcRKgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcRKgEAAAAAAA==/", "_etag": "\"9d00c9dd-0000-0100-0000-686ff95f0000\"", "_attachments": "attachments/", "_ts": 1752168799}, {"payPeriodId": "1020050564389877", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-02-22T00:00:00Z", "endDate": "2025-02-28T00:00:00Z", "submitByDate": "2025-03-04T00:00:00Z", "checkDate": "2025-03-05T00:00:00Z", "checkCount": 3, "id": "2e2ba402-1c16-4695-a152-68476bcbca36", "companyId": "********", "type": "payperiod", "_rid": "NmJkAKiCbEcSKgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcSKgEAAAAAAA==/", "_etag": "\"9d00cbdd-0000-0100-0000-686ff95f0000\"", "_attachments": "attachments/", "_ts": 1752168799}, {"payPeriodId": "1020050685278132", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-03-01T00:00:00Z", "endDate": "2025-03-07T00:00:00Z", "submitByDate": "2025-03-07T00:00:00Z", "checkDate": "2025-03-11T00:00:00Z", "checkCount": 2, "id": "737d0a70-b0e6-4080-8f2d-bd79d760bbc7", "companyId": "********", "type": "payperiod", "_rid": "NmJkAKiCbEcTKgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcTKgEAAAAAAA==/", "_etag": "\"9d00cfdd-0000-0100-0000-686ff95f0000\"", "_attachments": "attachments/", "_ts": 1752168799}, {"payPeriodId": "1020050745549723", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-03-08T00:00:00Z", "endDate": "2025-03-14T00:00:00Z", "submitByDate": "2025-03-14T00:00:00Z", "checkDate": "2025-03-18T00:00:00Z", "checkCount": 2, "id": "71decb82-dae8-4b66-80c6-392a19324911", "companyId": "********", "type": "payperiod", "_rid": "NmJkAKiCbEcUKgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcUKgEAAAAAAA==/", "_etag": "\"9d00d0dd-0000-0100-0000-686ff95f0000\"", "_attachments": "attachments/", "_ts": 1752168799}, {"payPeriodId": "1020050812229487", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-03-15T00:00:00Z", "endDate": "2025-03-21T00:00:00Z", "submitByDate": "2025-03-21T00:00:00Z", "checkDate": "2025-03-25T00:00:00Z", "checkCount": 2, "id": "abf81e10-cee8-41bc-b038-6dd0cfce1439", "companyId": "********", "type": "payperiod", "_rid": "NmJkAKiCbEcVKgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcVKgEAAAAAAA==/", "_etag": "\"9d00d2dd-0000-0100-0000-686ff95f0000\"", "_attachments": "attachments/", "_ts": 1752168799}, {"payPeriodId": "1020050885374544", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-03-22T00:00:00Z", "endDate": "2025-03-28T00:00:00Z", "submitByDate": "2025-03-28T00:00:00Z", "checkDate": "2025-04-01T00:00:00Z", "checkCount": 0, "id": "ca7f3667-008a-43d2-a7cf-8d3fb3cd4dcb", "companyId": "********", "type": "payperiod", "_rid": "NmJkAKiCbEcWKgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcWKgEAAAAAAA==/", "_etag": "\"9d00d6dd-0000-0100-0000-686ff95f0000\"", "_attachments": "attachments/", "_ts": 1752168799}, {"payPeriodId": "1020050956812374", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-03-29T00:00:00Z", "endDate": "2025-04-04T00:00:00Z", "submitByDate": "2025-04-04T00:00:00Z", "checkDate": "2025-04-08T00:00:00Z", "checkCount": 0, "id": "14180b9b-67cd-4838-86c7-35d3f73ded20", "companyId": "********", "type": "payperiod", "_rid": "NmJkAKiCbEcXKgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcXKgEAAAAAAA==/", "_etag": "\"9d00d7dd-0000-0100-0000-686ff95f0000\"", "_attachments": "attachments/", "_ts": 1752168799}, {"payPeriodId": "1020051002851183", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-04-05T00:00:00Z", "endDate": "2025-04-11T00:00:00Z", "submitByDate": "2025-04-11T00:00:00Z", "checkDate": "2025-04-15T00:00:00Z", "checkCount": 0, "id": "b4261a7a-2f51-48e6-bea8-424f337357e9", "companyId": "********", "type": "payperiod", "_rid": "NmJkAKiCbEcYKgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcYKgEAAAAAAA==/", "_etag": "\"9d00dbdd-0000-0100-0000-686ff9600000\"", "_attachments": "attachments/", "_ts": 1752168800}, {"payPeriodId": "1020051074914850", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-04-12T00:00:00Z", "endDate": "2025-04-18T00:00:00Z", "submitByDate": "2025-04-18T00:00:00Z", "checkDate": "2025-04-22T00:00:00Z", "checkCount": 0, "id": "9e095943-fe75-49c8-8d4b-df6788cb2ce5", "companyId": "********", "type": "payperiod", "_rid": "NmJkAKiCbEcZKgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcZKgEAAAAAAA==/", "_etag": "\"9d00dedd-0000-0100-0000-686ff9600000\"", "_attachments": "attachments/", "_ts": 1752168800}, {"payPeriodId": "1020051120440384", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-04-19T00:00:00Z", "endDate": "2025-04-25T00:00:00Z", "submitByDate": "2025-04-25T00:00:00Z", "checkDate": "2025-04-29T00:00:00Z", "checkCount": 0, "id": "2c37e4e7-e630-4924-a5fc-ff7c6e4e0c6f", "companyId": "********", "type": "payperiod", "_rid": "NmJkAKiCbEcaKgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcaKgEAAAAAAA==/", "_etag": "\"9d00e2dd-0000-0100-0000-686ff9600000\"", "_attachments": "attachments/", "_ts": 1752168800}, {"payPeriodId": "1020051173298592", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-04-26T00:00:00Z", "endDate": "2025-05-02T00:00:00Z", "submitByDate": "2025-05-02T00:00:00Z", "checkDate": "2025-05-06T00:00:00Z", "checkCount": 0, "id": "c28882df-9839-485c-9a9c-6bf03fd9d1d1", "companyId": "********", "type": "payperiod", "_rid": "NmJkAKiCbEcbKgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcbKgEAAAAAAA==/", "_etag": "\"9d00e3dd-0000-0100-0000-686ff9600000\"", "_attachments": "attachments/", "_ts": 1752168800}, {"payPeriodId": "1020051237930936", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-05-03T00:00:00Z", "endDate": "2025-05-09T00:00:00Z", "submitByDate": "2025-05-09T00:00:00Z", "checkDate": "2025-05-13T00:00:00Z", "checkCount": 0, "id": "61b93b05-e3a4-4c6e-8be2-29bad156b3b0", "companyId": "********", "type": "payperiod", "_rid": "NmJkAKiCbEccKgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEccKgEAAAAAAA==/", "_etag": "\"9d00e7dd-0000-0100-0000-686ff9600000\"", "_attachments": "attachments/", "_ts": 1752168800}, {"payPeriodId": "1020051345553714", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-05-10T00:00:00Z", "endDate": "2025-05-16T00:00:00Z", "submitByDate": "2025-05-16T00:00:00Z", "checkDate": "2025-05-20T00:00:00Z", "checkCount": 0, "id": "84f4122d-c67e-4fa5-9bff-8f1d4d36fd6b", "companyId": "********", "type": "payperiod", "_rid": "NmJkAKiCbEcdKgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcdKgEAAAAAAA==/", "_etag": "\"9d00ebdd-0000-0100-0000-686ff9600000\"", "_attachments": "attachments/", "_ts": 1752168800}, {"payPeriodId": "1020051349442273", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-05-17T00:00:00Z", "endDate": "2025-05-23T00:00:00Z", "submitByDate": "2025-05-22T00:00:00Z", "checkDate": "2025-05-27T00:00:00Z", "checkCount": 0, "id": "25e9c5a5-efda-4ef2-8de8-661a660a1f0c", "companyId": "********", "type": "payperiod", "_rid": "NmJkAKiCbEceKgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEceKgEAAAAAAA==/", "_etag": "\"9d00eedd-0000-0100-0000-686ff9600000\"", "_attachments": "attachments/", "_ts": 1752168800}, {"payPeriodId": "1020051403401243", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-05-24T00:00:00Z", "endDate": "2025-05-30T00:00:00Z", "submitByDate": "2025-05-30T00:00:00Z", "checkDate": "2025-06-03T00:00:00Z", "checkCount": 0, "id": "b7701da3-fa96-428f-a22e-3354c69de1dd", "companyId": "********", "type": "payperiod", "_rid": "NmJkAKiCbEcfKgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcfKgEAAAAAAA==/", "_etag": "\"9d00f3dd-0000-0100-0000-686ff9600000\"", "_attachments": "attachments/", "_ts": 1752168800}, {"payPeriodId": "1020051483575903", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-05-31T00:00:00Z", "endDate": "2025-06-06T00:00:00Z", "submitByDate": "2025-06-06T00:00:00Z", "checkDate": "2025-06-10T00:00:00Z", "checkCount": 0, "id": "6f5f963f-d0b8-4d3d-8452-a9fd570de9e2", "companyId": "********", "type": "payperiod", "_rid": "NmJkAKiCbEcgKgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcgKgEAAAAAAA==/", "_etag": "\"9d00f9dd-0000-0100-0000-686ff9600000\"", "_attachments": "attachments/", "_ts": 1752168800}, {"payPeriodId": "1020051521882728", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-06-07T00:00:00Z", "endDate": "2025-06-13T00:00:00Z", "submitByDate": "2025-06-13T00:00:00Z", "checkDate": "2025-06-17T00:00:00Z", "checkCount": 0, "id": "7b0fa51f-8c79-48ba-aaa6-822448f59bdb", "companyId": "********", "type": "payperiod", "_rid": "NmJkAKiCbEchKgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEchKgEAAAAAAA==/", "_etag": "\"9d00fddd-0000-0100-0000-686ff9600000\"", "_attachments": "attachments/", "_ts": 1752168800}, {"payPeriodId": "1020051588805903", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-06-14T00:00:00Z", "endDate": "2025-06-20T00:00:00Z", "submitByDate": "2025-06-20T00:00:00Z", "checkDate": "2025-06-24T00:00:00Z", "checkCount": 0, "id": "7657d48d-78d3-405c-a8b3-e3ae173368d1", "companyId": "********", "type": "payperiod", "_rid": "NmJkAKiCbEciKgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEciKgEAAAAAAA==/", "_etag": "\"9d00ffdd-0000-0100-0000-686ff9600000\"", "_attachments": "attachments/", "_ts": 1752168800}, {"payPeriodId": "1020051644083927", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-06-21T00:00:00Z", "endDate": "2025-06-27T00:00:00Z", "submitByDate": "2025-06-27T00:00:00Z", "checkDate": "2025-07-01T00:00:00Z", "checkCount": 0, "id": "50b65bac-6e01-4ff7-95a5-31cb04ae1168", "companyId": "********", "type": "payperiod", "_rid": "NmJkAKiCbEcjKgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcjKgEAAAAAAA==/", "_etag": "\"9d0003de-0000-0100-0000-686ff9600000\"", "_attachments": "attachments/", "_ts": 1752168800}, {"payPeriodId": "1020051710357368", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-06-28T00:00:00Z", "endDate": "2025-07-04T00:00:00Z", "submitByDate": "2025-07-03T00:00:00Z", "checkDate": "2025-07-08T00:00:00Z", "checkCount": 0, "id": "09a0f307-4409-4e1b-bd11-c413e344f2fd", "companyId": "********", "type": "payperiod", "_rid": "NmJkAKiCbEckKgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEckKgEAAAAAAA==/", "_etag": "\"9d0008de-0000-0100-0000-686ff9600000\"", "_attachments": "attachments/", "_ts": 1752168800}, {"payPeriodId": "1020051756265961", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-07-05T00:00:00Z", "endDate": "2025-07-11T00:00:00Z", "submitByDate": "2025-07-11T00:00:00Z", "checkDate": "2025-07-15T00:00:00Z", "checkCount": 0, "id": "d819e666-de44-4949-9295-24ee99e995ff", "companyId": "********", "type": "payperiod", "_rid": "NmJkAKiCbEclKgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEclKgEAAAAAAA==/", "_etag": "\"9d000bde-0000-0100-0000-686ff9610000\"", "_attachments": "attachments/", "_ts": 1752168801}, {"payPeriodId": "1020051819730391", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-07-12T00:00:00Z", "endDate": "2025-07-18T00:00:00Z", "submitByDate": "2025-07-18T00:00:00Z", "checkDate": "2025-07-22T00:00:00Z", "checkCount": 0, "id": "6f11c0c5-91ad-4df4-ad49-296d899f9619", "companyId": "********", "type": "payperiod", "_rid": "NmJkAKiCbEcmKgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcmKgEAAAAAAA==/", "_etag": "\"9d000cde-0000-0100-0000-686ff9610000\"", "_attachments": "attachments/", "_ts": 1752168801}, {"payPeriodId": "1020051888295963", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-07-19T00:00:00Z", "endDate": "2025-07-25T00:00:00Z", "submitByDate": "2025-07-25T00:00:00Z", "checkDate": "2025-07-29T00:00:00Z", "checkCount": 0, "id": "aa7a7755-8a59-4a81-b9ee-31ce6450b764", "companyId": "********", "type": "payperiod", "_rid": "NmJkAKiCbEcnKgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcnKgEAAAAAAA==/", "_etag": "\"9d000fde-0000-0100-0000-686ff9610000\"", "_attachments": "attachments/", "_ts": 1752168801}, {"payPeriodId": "1020051942134426", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-07-26T00:00:00Z", "endDate": "2025-08-01T00:00:00Z", "submitByDate": "2025-08-01T00:00:00Z", "checkDate": "2025-08-05T00:00:00Z", "checkCount": 0, "id": "67635560-5323-4990-98f6-9846e9f37420", "companyId": "********", "type": "payperiod", "_rid": "NmJkAKiCbEcoKgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcoKgEAAAAAAA==/", "_etag": "\"9d0011de-0000-0100-0000-686ff9610000\"", "_attachments": "attachments/", "_ts": 1752168801}, {"payPeriodId": "1020052011395069", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-08-02T00:00:00Z", "endDate": "2025-08-08T00:00:00Z", "submitByDate": "2025-08-08T00:00:00Z", "checkDate": "2025-08-12T00:00:00Z", "checkCount": 0, "id": "1f1344ec-32fb-4e43-aab7-c95ad5001b75", "companyId": "********", "type": "payperiod", "_rid": "NmJkAKiCbEcpKgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcpKgEAAAAAAA==/", "_etag": "\"9d0015de-0000-0100-0000-686ff9610000\"", "_attachments": "attachments/", "_ts": 1752168801}, {"payPeriodId": "1020052063818754", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-08-09T00:00:00Z", "endDate": "2025-08-15T00:00:00Z", "submitByDate": "2025-08-15T00:00:00Z", "checkDate": "2025-08-19T00:00:00Z", "checkCount": 0, "id": "a56525f9-4652-4a51-9ae2-c4e41c1b41de", "companyId": "********", "type": "payperiod", "_rid": "NmJkAKiCbEcqKgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcqKgEAAAAAAA==/", "_etag": "\"9d0017de-0000-0100-0000-686ff9610000\"", "_attachments": "attachments/", "_ts": 1752168801}, {"payPeriodId": "1020052139654955", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-08-16T00:00:00Z", "endDate": "2025-08-22T00:00:00Z", "submitByDate": "2025-08-22T00:00:00Z", "checkDate": "2025-08-26T00:00:00Z", "checkCount": 0, "id": "f6b23ff6-d57c-403f-a44a-d9bf7dea75ad", "companyId": "********", "type": "payperiod", "_rid": "NmJkAKiCbEcrKgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcrKgEAAAAAAA==/", "_etag": "\"9d001cde-0000-0100-0000-686ff9610000\"", "_attachments": "attachments/", "_ts": 1752168801}, {"payPeriodId": "1020052186305533", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-08-23T00:00:00Z", "endDate": "2025-08-29T00:00:00Z", "submitByDate": "2025-08-28T00:00:00Z", "checkDate": "2025-09-02T00:00:00Z", "checkCount": 0, "id": "5432aacd-ce77-4241-92f3-0dd577533050", "companyId": "********", "type": "payperiod", "_rid": "NmJkAKiCbEcsKgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcsKgEAAAAAAA==/", "_etag": "\"9d0021de-0000-0100-0000-686ff9610000\"", "_attachments": "attachments/", "_ts": 1752168801}, {"payPeriodId": "1020052260193476", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-08-30T00:00:00Z", "endDate": "2025-09-05T00:00:00Z", "submitByDate": "2025-09-05T00:00:00Z", "checkDate": "2025-09-09T00:00:00Z", "checkCount": 0, "id": "256d6b48-fcf6-4b88-80c3-b14742e64d41", "companyId": "********", "type": "payperiod", "_rid": "NmJkAKiCbEctKgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEctKgEAAAAAAA==/", "_etag": "\"9d0023de-0000-0100-0000-686ff9610000\"", "_attachments": "attachments/", "_ts": 1752168801}, {"payPeriodId": "1020052300495569", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-09-06T00:00:00Z", "endDate": "2025-09-12T00:00:00Z", "submitByDate": "2025-09-12T00:00:00Z", "checkDate": "2025-09-16T00:00:00Z", "checkCount": 0, "id": "22bdc770-8234-4134-9452-41cf0c7924e8", "companyId": "********", "type": "payperiod", "_rid": "NmJkAKiCbEcuKgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcuKgEAAAAAAA==/", "_etag": "\"9d0026de-0000-0100-0000-686ff9610000\"", "_attachments": "attachments/", "_ts": 1752168801}, {"payPeriodId": "1020052361280886", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-09-13T00:00:00Z", "endDate": "2025-09-19T00:00:00Z", "submitByDate": "2025-09-19T00:00:00Z", "checkDate": "2025-09-23T00:00:00Z", "checkCount": 0, "id": "63660aa1-7ac1-4edf-8721-a26ccc3ab2ef", "companyId": "********", "type": "payperiod", "_rid": "NmJkAKiCbEcvKgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcvKgEAAAAAAA==/", "_etag": "\"9d002ade-0000-0100-0000-686ff9610000\"", "_attachments": "attachments/", "_ts": 1752168801}, {"payPeriodId": "1020052418058372", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-09-20T00:00:00Z", "endDate": "2025-09-26T00:00:00Z", "submitByDate": "2025-09-26T00:00:00Z", "checkDate": "2025-09-30T00:00:00Z", "checkCount": 0, "id": "f62129a4-f244-4f42-8a27-7cd0481acb7f", "companyId": "********", "type": "payperiod", "_rid": "NmJkAKiCbEcwKgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcwKgEAAAAAAA==/", "_etag": "\"9d002dde-0000-0100-0000-686ff9610000\"", "_attachments": "attachments/", "_ts": 1752168801}, {"payPeriodId": "1020052484178096", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-09-27T00:00:00Z", "endDate": "2025-10-03T00:00:00Z", "submitByDate": "2025-10-03T00:00:00Z", "checkDate": "2025-10-07T00:00:00Z", "checkCount": 0, "id": "6694e262-188f-4b02-a565-3095e2096d20", "companyId": "********", "type": "payperiod", "_rid": "NmJkAKiCbEcxKgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcxKgEAAAAAAA==/", "_etag": "\"9d0031de-0000-0100-0000-686ff9610000\"", "_attachments": "attachments/", "_ts": 1752168801}, {"payPeriodId": "1020050132589552", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2024-12-28T00:00:00Z", "endDate": "2025-01-03T00:00:00Z", "submitByDate": "2025-01-03T00:00:00Z", "checkDate": "2025-01-07T00:00:00Z", "checkCount": 3, "id": "4025cc62-6f57-4498-a745-3a9649a72330", "companyId": "********", "type": "payperiod", "_rid": "NmJkAKiCbEc1KgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc1KgEAAAAAAA==/", "_etag": "\"9d003dde-0000-0100-0000-686ff9620000\"", "_attachments": "attachments/", "_ts": 1752168802}, {"payPeriodId": "1020050191101915", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-01-04T00:00:00Z", "endDate": "2025-01-10T00:00:00Z", "submitByDate": "2025-01-10T00:00:00Z", "checkDate": "2025-01-14T00:00:00Z", "checkCount": 2, "id": "a2afa181-6d95-4d69-b674-02d480910515", "companyId": "********", "type": "payperiod", "_rid": "NmJkAKiCbEc2KgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc2KgEAAAAAAA==/", "_etag": "\"9d0041de-0000-0100-0000-686ff9620000\"", "_attachments": "attachments/", "_ts": 1752168802}, {"payPeriodId": "1020050255535780", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-01-11T00:00:00Z", "endDate": "2025-01-17T00:00:00Z", "submitByDate": "2025-01-17T00:00:00Z", "checkDate": "2025-01-21T00:00:00Z", "checkCount": 2, "id": "328fe3ea-3d49-43b2-b4c8-52f8bd2c52a6", "companyId": "********", "type": "payperiod", "_rid": "NmJkAKiCbEc3KgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc3KgEAAAAAAA==/", "_etag": "\"9d0043de-0000-0100-0000-686ff9620000\"", "_attachments": "attachments/", "_ts": 1752168802}, {"payPeriodId": "1020050366200139", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-01-18T00:00:00Z", "endDate": "2025-01-24T00:00:00Z", "submitByDate": "2025-01-24T00:00:00Z", "checkDate": "2025-01-28T00:00:00Z", "checkCount": 2, "id": "38e332ea-6c48-4cf3-a825-ee7fc4801366", "companyId": "********", "type": "payperiod", "_rid": "NmJkAKiCbEc4KgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc4KgEAAAAAAA==/", "_etag": "\"9d0044de-0000-0100-0000-686ff9620000\"", "_attachments": "attachments/", "_ts": 1752168802}, {"payPeriodId": "1020050366200140", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-01-25T00:00:00Z", "endDate": "2025-01-31T00:00:00Z", "submitByDate": "2025-01-31T00:00:00Z", "checkDate": "2025-02-04T00:00:00Z", "checkCount": 4, "id": "e6df4497-75ce-46ab-8bfa-f0b403f6c89b", "companyId": "********", "type": "payperiod", "_rid": "NmJkAKiCbEc5KgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc5KgEAAAAAAA==/", "_etag": "\"9d0045de-0000-0100-0000-686ff9620000\"", "_attachments": "attachments/", "_ts": 1752168802}, {"payPeriodId": "1020051339964574", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-02-01T00:00:00Z", "endDate": "2025-02-07T00:00:00Z", "submitByDate": "2025-02-07T00:00:00Z", "checkDate": "2025-02-14T00:00:00Z", "checkCount": 2, "id": "e3c89bb2-f6af-45e8-9466-21345ce10bbb", "companyId": "********", "type": "payperiod", "_rid": "NmJkAKiCbEc6KgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc6KgEAAAAAAA==/", "_etag": "\"9d0049de-0000-0100-0000-686ff9620000\"", "_attachments": "attachments/", "_ts": 1752168802}, {"payPeriodId": "1020050455140009", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-02-08T00:00:00Z", "endDate": "2025-02-14T00:00:00Z", "submitByDate": "2025-02-14T00:00:00Z", "checkDate": "2025-02-18T00:00:00Z", "checkCount": 2, "id": "70c51e26-8f2f-4c99-9e7c-4d9652b90cca", "companyId": "********", "type": "payperiod", "_rid": "NmJkAKiCbEc7KgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc7KgEAAAAAAA==/", "_etag": "\"9d004bde-0000-0100-0000-686ff9620000\"", "_attachments": "attachments/", "_ts": 1752168802}, {"payPeriodId": "1020050564343613", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-02-15T00:00:00Z", "endDate": "2025-02-21T00:00:00Z", "submitByDate": "2025-02-21T00:00:00Z", "checkDate": "2025-02-25T00:00:00Z", "checkCount": 3, "id": "b08653d4-4a8c-4b3a-86b5-81c8735b31b5", "companyId": "********", "type": "payperiod", "_rid": "NmJkAKiCbEc8KgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc8KgEAAAAAAA==/", "_etag": "\"9d004fde-0000-0100-0000-686ff9620000\"", "_attachments": "attachments/", "_ts": 1752168802}, {"payPeriodId": "1020050564389877", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-02-22T00:00:00Z", "endDate": "2025-02-28T00:00:00Z", "submitByDate": "2025-03-04T00:00:00Z", "checkDate": "2025-03-05T00:00:00Z", "checkCount": 3, "id": "a593ea79-ef17-4394-9165-fe7c3314a866", "companyId": "********", "type": "payperiod", "_rid": "NmJkAKiCbEc9KgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc9KgEAAAAAAA==/", "_etag": "\"9d0053de-0000-0100-0000-686ff9620000\"", "_attachments": "attachments/", "_ts": 1752168802}, {"payPeriodId": "1020050685278132", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-03-01T00:00:00Z", "endDate": "2025-03-07T00:00:00Z", "submitByDate": "2025-03-07T00:00:00Z", "checkDate": "2025-03-11T00:00:00Z", "checkCount": 2, "id": "9d4d76bb-b540-40d5-a00d-5542814fb468", "companyId": "********", "type": "payperiod", "_rid": "NmJkAKiCbEc+KgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc+KgEAAAAAAA==/", "_etag": "\"9d0057de-0000-0100-0000-686ff9630000\"", "_attachments": "attachments/", "_ts": 1752168803}, {"payPeriodId": "1020050745549723", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-03-08T00:00:00Z", "endDate": "2025-03-14T00:00:00Z", "submitByDate": "2025-03-14T00:00:00Z", "checkDate": "2025-03-18T00:00:00Z", "checkCount": 2, "id": "8651d8f2-d19c-4834-bdf7-0d6aa1318aa7", "companyId": "********", "type": "payperiod", "_rid": "NmJkAKiCbEc-KgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEc-KgEAAAAAAA==/", "_etag": "\"9d005dde-0000-0100-0000-686ff9630000\"", "_attachments": "attachments/", "_ts": 1752168803}, {"payPeriodId": "1020050812229487", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-03-15T00:00:00Z", "endDate": "2025-03-21T00:00:00Z", "submitByDate": "2025-03-21T00:00:00Z", "checkDate": "2025-03-25T00:00:00Z", "checkCount": 2, "id": "64fb5919-6ae6-4161-a505-c81455b5e497", "companyId": "********", "type": "payperiod", "_rid": "NmJkAKiCbEdAKgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdAKgEAAAAAAA==/", "_etag": "\"9d005fde-0000-0100-0000-686ff9630000\"", "_attachments": "attachments/", "_ts": 1752168803}, {"payPeriodId": "1020050885374544", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-03-22T00:00:00Z", "endDate": "2025-03-28T00:00:00Z", "submitByDate": "2025-03-28T00:00:00Z", "checkDate": "2025-04-01T00:00:00Z", "checkCount": 3, "id": "b3915090-5f27-4f24-b79f-62f5190573cf", "companyId": "********", "type": "payperiod", "_rid": "NmJkAKiCbEdBKgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdBKgEAAAAAAA==/", "_etag": "\"9d0064de-0000-0100-0000-686ff9630000\"", "_attachments": "attachments/", "_ts": 1752168803}, {"payPeriodId": "1020050956812374", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-03-29T00:00:00Z", "endDate": "2025-04-04T00:00:00Z", "submitByDate": "2025-04-04T00:00:00Z", "checkDate": "2025-04-08T00:00:00Z", "checkCount": 3, "id": "be446c4c-08dc-4172-b0c1-8618ad72a939", "companyId": "********", "type": "payperiod", "_rid": "NmJkAKiCbEdCKgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdCKgEAAAAAAA==/", "_etag": "\"9d0068de-0000-0100-0000-686ff9630000\"", "_attachments": "attachments/", "_ts": 1752168803}, {"payPeriodId": "1020051002851183", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-04-05T00:00:00Z", "endDate": "2025-04-11T00:00:00Z", "submitByDate": "2025-04-11T00:00:00Z", "checkDate": "2025-04-15T00:00:00Z", "checkCount": 2, "id": "841e0e79-27cd-4fe6-beaf-c801d609c8ff", "companyId": "********", "type": "payperiod", "_rid": "NmJkAKiCbEdDKgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdDKgEAAAAAAA==/", "_etag": "\"9d006cde-0000-0100-0000-686ff9630000\"", "_attachments": "attachments/", "_ts": 1752168803}, {"payPeriodId": "1020051074914850", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-04-12T00:00:00Z", "endDate": "2025-04-18T00:00:00Z", "submitByDate": "2025-04-18T00:00:00Z", "checkDate": "2025-04-22T00:00:00Z", "checkCount": 2, "id": "66fb5b82-e00f-414a-a3ab-7c501746e40c", "companyId": "********", "type": "payperiod", "_rid": "NmJkAKiCbEdEKgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdEKgEAAAAAAA==/", "_etag": "\"9d006fde-0000-0100-0000-686ff9630000\"", "_attachments": "attachments/", "_ts": 1752168803}, {"payPeriodId": "1020051120440384", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-04-19T00:00:00Z", "endDate": "2025-04-25T00:00:00Z", "submitByDate": "2025-04-25T00:00:00Z", "checkDate": "2025-04-29T00:00:00Z", "checkCount": 3, "id": "89aea74f-9c69-42c9-99d2-cf7e6b1db9dd", "companyId": "********", "type": "payperiod", "_rid": "NmJkAKiCbEdFKgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdFKgEAAAAAAA==/", "_etag": "\"9d0070de-0000-0100-0000-686ff9630000\"", "_attachments": "attachments/", "_ts": 1752168803}, {"payPeriodId": "1020051173298592", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-04-26T00:00:00Z", "endDate": "2025-05-02T00:00:00Z", "submitByDate": "2025-05-02T00:00:00Z", "checkDate": "2025-05-06T00:00:00Z", "checkCount": 3, "id": "6ba7b411-4701-4136-8659-434a2a6b421b", "companyId": "********", "type": "payperiod", "_rid": "NmJkAKiCbEdGKgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdGKgEAAAAAAA==/", "_etag": "\"9d0076de-0000-0100-0000-686ff9630000\"", "_attachments": "attachments/", "_ts": 1752168803}, {"payPeriodId": "1020051237930936", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-05-03T00:00:00Z", "endDate": "2025-05-09T00:00:00Z", "submitByDate": "2025-05-09T00:00:00Z", "checkDate": "2025-05-13T00:00:00Z", "checkCount": 2, "id": "25c203ec-7769-488e-90d4-35040f3e853c", "companyId": "********", "type": "payperiod", "_rid": "NmJkAKiCbEdHKgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdHKgEAAAAAAA==/", "_etag": "\"9d0079de-0000-0100-0000-686ff9630000\"", "_attachments": "attachments/", "_ts": 1752168803}, {"payPeriodId": "1020051345553714", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-05-10T00:00:00Z", "endDate": "2025-05-16T00:00:00Z", "submitByDate": "2025-05-16T00:00:00Z", "checkDate": "2025-05-20T00:00:00Z", "checkCount": 2, "id": "955ed02b-5255-425f-9d2f-7c0d26b626a8", "companyId": "********", "type": "payperiod", "_rid": "NmJkAKiCbEdIKgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdIKgEAAAAAAA==/", "_etag": "\"9d007ede-0000-0100-0000-686ff9630000\"", "_attachments": "attachments/", "_ts": 1752168803}, {"payPeriodId": "1020051349442273", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-05-17T00:00:00Z", "endDate": "2025-05-23T00:00:00Z", "submitByDate": "2025-05-22T00:00:00Z", "checkDate": "2025-05-27T00:00:00Z", "checkCount": 2, "id": "063b4388-1d22-4049-a8db-58875b3bfb19", "companyId": "********", "type": "payperiod", "_rid": "NmJkAKiCbEdJKgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdJKgEAAAAAAA==/", "_etag": "\"9d0082de-0000-0100-0000-686ff9630000\"", "_attachments": "attachments/", "_ts": 1752168803}, {"payPeriodId": "1020051403401243", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-05-24T00:00:00Z", "endDate": "2025-05-30T00:00:00Z", "submitByDate": "2025-05-30T00:00:00Z", "checkDate": "2025-06-03T00:00:00Z", "checkCount": 4, "id": "1a328888-a7b6-4974-a3fc-e0248bb70042", "companyId": "********", "type": "payperiod", "_rid": "NmJkAKiCbEdKKgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdKKgEAAAAAAA==/", "_etag": "\"9d0084de-0000-0100-0000-686ff9630000\"", "_attachments": "attachments/", "_ts": 1752168803}, {"payPeriodId": "1020051483575903", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-05-31T00:00:00Z", "endDate": "2025-06-06T00:00:00Z", "submitByDate": "2025-06-06T00:00:00Z", "checkDate": "2025-06-10T00:00:00Z", "checkCount": 2, "id": "95b94827-586e-4343-94dd-1aee1a1dda2b", "companyId": "********", "type": "payperiod", "_rid": "NmJkAKiCbEdLKgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdLKgEAAAAAAA==/", "_etag": "\"9d0088de-0000-0100-0000-686ff9640000\"", "_attachments": "attachments/", "_ts": 1752168804}, {"payPeriodId": "1020051521882728", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-06-07T00:00:00Z", "endDate": "2025-06-13T00:00:00Z", "submitByDate": "2025-06-13T00:00:00Z", "checkDate": "2025-06-17T00:00:00Z", "checkCount": 2, "id": "39cf7512-d1b2-46f7-bdc5-97b437990578", "companyId": "********", "type": "payperiod", "_rid": "NmJkAKiCbEdMKgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdMKgEAAAAAAA==/", "_etag": "\"9d008ede-0000-0100-0000-686ff9640000\"", "_attachments": "attachments/", "_ts": 1752168804}, {"payPeriodId": "1020051588805903", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-06-14T00:00:00Z", "endDate": "2025-06-20T00:00:00Z", "submitByDate": "2025-06-20T00:00:00Z", "checkDate": "2025-06-24T00:00:00Z", "checkCount": 2, "id": "748f3f5d-bb7a-40b7-8f03-acc28d09f73a", "companyId": "********", "type": "payperiod", "_rid": "NmJkAKiCbEdNKgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdNKgEAAAAAAA==/", "_etag": "\"9d0093de-0000-0100-0000-686ff9640000\"", "_attachments": "attachments/", "_ts": 1752168804}, {"payPeriodId": "1020051644083927", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-06-21T00:00:00Z", "endDate": "2025-06-27T00:00:00Z", "submitByDate": "2025-06-27T00:00:00Z", "checkDate": "2025-07-01T00:00:00Z", "checkCount": 2, "id": "8c424d39-b4f7-4d4c-b659-1d9523c2c26c", "companyId": "********", "type": "payperiod", "_rid": "NmJkAKiCbEdOKgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdOKgEAAAAAAA==/", "_etag": "\"9d0097de-0000-0100-0000-686ff9640000\"", "_attachments": "attachments/", "_ts": 1752168804}, {"payPeriodId": "1020051710357368", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-06-28T00:00:00Z", "endDate": "2025-07-04T00:00:00Z", "submitByDate": "2025-07-03T00:00:00Z", "checkDate": "2025-07-08T00:00:00Z", "checkCount": 0, "id": "02c84ade-e1d3-4b53-b8db-4fefbc874cff", "companyId": "********", "type": "payperiod", "_rid": "NmJkAKiCbEdPKgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdPKgEAAAAAAA==/", "_etag": "\"9d009bde-0000-0100-0000-686ff9640000\"", "_attachments": "attachments/", "_ts": 1752168804}, {"payPeriodId": "1020051756265961", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-07-05T00:00:00Z", "endDate": "2025-07-11T00:00:00Z", "submitByDate": "2025-07-11T00:00:00Z", "checkDate": "2025-07-15T00:00:00Z", "checkCount": 0, "id": "e8f5da85-efdd-4141-8db3-49abb583cab1", "companyId": "********", "type": "payperiod", "_rid": "NmJkAKiCbEdQKgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdQKgEAAAAAAA==/", "_etag": "\"9d009cde-0000-0100-0000-686ff9640000\"", "_attachments": "attachments/", "_ts": 1752168804}, {"payPeriodId": "1020051819730391", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-07-12T00:00:00Z", "endDate": "2025-07-18T00:00:00Z", "submitByDate": "2025-07-18T00:00:00Z", "checkDate": "2025-07-22T00:00:00Z", "checkCount": 0, "id": "134f078e-3151-4d9d-8439-8acc366a1a9b", "companyId": "********", "type": "payperiod", "_rid": "NmJkAKiCbEdRKgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdRKgEAAAAAAA==/", "_etag": "\"9d009ede-0000-0100-0000-686ff9640000\"", "_attachments": "attachments/", "_ts": 1752168804}, {"payPeriodId": "1020051888295963", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-07-19T00:00:00Z", "endDate": "2025-07-25T00:00:00Z", "submitByDate": "2025-07-25T00:00:00Z", "checkDate": "2025-07-29T00:00:00Z", "checkCount": 0, "id": "7a7648e2-b13d-45a2-b93b-e7135691f08d", "companyId": "********", "type": "payperiod", "_rid": "NmJkAKiCbEdSKgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdSKgEAAAAAAA==/", "_etag": "\"9d00a2de-0000-0100-0000-686ff9640000\"", "_attachments": "attachments/", "_ts": 1752168804}, {"payPeriodId": "1020051942134426", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-07-26T00:00:00Z", "endDate": "2025-08-01T00:00:00Z", "submitByDate": "2025-08-01T00:00:00Z", "checkDate": "2025-08-05T00:00:00Z", "checkCount": 0, "id": "2bf54edf-2cd7-45d2-bafe-668d6af5b791", "companyId": "********", "type": "payperiod", "_rid": "NmJkAKiCbEdTKgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdTKgEAAAAAAA==/", "_etag": "\"9d00a4de-0000-0100-0000-686ff9640000\"", "_attachments": "attachments/", "_ts": 1752168804}, {"payPeriodId": "1020052011395069", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-08-02T00:00:00Z", "endDate": "2025-08-08T00:00:00Z", "submitByDate": "2025-08-08T00:00:00Z", "checkDate": "2025-08-12T00:00:00Z", "checkCount": 0, "id": "6736c08c-90ef-4136-aca2-2f9298f00e62", "companyId": "********", "type": "payperiod", "_rid": "NmJkAKiCbEdUKgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdUKgEAAAAAAA==/", "_etag": "\"9d00a7de-0000-0100-0000-686ff9640000\"", "_attachments": "attachments/", "_ts": 1752168804}, {"payPeriodId": "1020052063818754", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-08-09T00:00:00Z", "endDate": "2025-08-15T00:00:00Z", "submitByDate": "2025-08-15T00:00:00Z", "checkDate": "2025-08-19T00:00:00Z", "checkCount": 0, "id": "f2e35697-142f-4cbc-bce6-d4a23ac6b3da", "companyId": "********", "type": "payperiod", "_rid": "NmJkAKiCbEdVKgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdVKgEAAAAAAA==/", "_etag": "\"9d00a9de-0000-0100-0000-686ff9640000\"", "_attachments": "attachments/", "_ts": 1752168804}, {"payPeriodId": "1020052139654955", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-08-16T00:00:00Z", "endDate": "2025-08-22T00:00:00Z", "submitByDate": "2025-08-22T00:00:00Z", "checkDate": "2025-08-26T00:00:00Z", "checkCount": 0, "id": "08bfae69-36ae-489f-b321-578bdf255322", "companyId": "********", "type": "payperiod", "_rid": "NmJkAKiCbEdWKgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdWKgEAAAAAAA==/", "_etag": "\"9d00afde-0000-0100-0000-686ff9640000\"", "_attachments": "attachments/", "_ts": 1752168804}, {"payPeriodId": "1020052186305533", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-08-23T00:00:00Z", "endDate": "2025-08-29T00:00:00Z", "submitByDate": "2025-08-28T00:00:00Z", "checkDate": "2025-09-02T00:00:00Z", "checkCount": 0, "id": "bb9b8f23-12f5-4145-bfe7-c0d553a163cd", "companyId": "********", "type": "payperiod", "_rid": "NmJkAKiCbEdXKgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdXKgEAAAAAAA==/", "_etag": "\"9d00b2de-0000-0100-0000-686ff9640000\"", "_attachments": "attachments/", "_ts": 1752168804}, {"payPeriodId": "1020052260193476", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-08-30T00:00:00Z", "endDate": "2025-09-05T00:00:00Z", "submitByDate": "2025-09-05T00:00:00Z", "checkDate": "2025-09-09T00:00:00Z", "checkCount": 0, "id": "73aa39be-06a4-46fc-8c8d-846d7db36bd9", "companyId": "********", "type": "payperiod", "_rid": "NmJkAKiCbEdYKgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdYKgEAAAAAAA==/", "_etag": "\"9d00b5de-0000-0100-0000-686ff9650000\"", "_attachments": "attachments/", "_ts": 1752168805}, {"payPeriodId": "1020052300495569", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-09-06T00:00:00Z", "endDate": "2025-09-12T00:00:00Z", "submitByDate": "2025-09-12T00:00:00Z", "checkDate": "2025-09-16T00:00:00Z", "checkCount": 0, "id": "fd88d290-391b-43f7-ace4-6fe22e51b18d", "companyId": "********", "type": "payperiod", "_rid": "NmJkAKiCbEdZKgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdZKgEAAAAAAA==/", "_etag": "\"9d00b7de-0000-0100-0000-686ff9650000\"", "_attachments": "attachments/", "_ts": 1752168805}, {"payPeriodId": "1020052361280886", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-09-13T00:00:00Z", "endDate": "2025-09-19T00:00:00Z", "submitByDate": "2025-09-19T00:00:00Z", "checkDate": "2025-09-23T00:00:00Z", "checkCount": 0, "id": "8df0f3a3-525e-4fb9-ab18-0e51ddd99ab8", "companyId": "********", "type": "payperiod", "_rid": "NmJkAKiCbEdaKgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdaKgEAAAAAAA==/", "_etag": "\"9d00b9de-0000-0100-0000-686ff9650000\"", "_attachments": "attachments/", "_ts": 1752168805}, {"payPeriodId": "1020052418058372", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-09-20T00:00:00Z", "endDate": "2025-09-26T00:00:00Z", "submitByDate": "2025-09-26T00:00:00Z", "checkDate": "2025-09-30T00:00:00Z", "checkCount": 0, "id": "5936f4f7-262a-418d-98b2-68fa5a63a835", "companyId": "********", "type": "payperiod", "_rid": "NmJkAKiCbEdbKgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdbKgEAAAAAAA==/", "_etag": "\"9d00bbde-0000-0100-0000-686ff9650000\"", "_attachments": "attachments/", "_ts": 1752168805}, {"payPeriodId": "1020052484178096", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-09-27T00:00:00Z", "endDate": "2025-10-03T00:00:00Z", "submitByDate": "2025-10-03T00:00:00Z", "checkDate": "2025-10-07T00:00:00Z", "checkCount": 0, "id": "3f1583c9-147a-4cf3-a077-24ceaf5795b7", "companyId": "********", "type": "payperiod", "_rid": "NmJkAKiCbEdcKgEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEdcKgEAAAAAAA==/", "_etag": "\"9d00bede-0000-0100-0000-686ff9650000\"", "_attachments": "attachments/", "_ts": 1752168805}], "links": [{"rel": "self", "href": "https://ca-payroll-ai-mock-sb-001-secure.nicebeach-8a7a52ab.eastus.azurecontainerapps.io/ca/companies/********/payperiods"}]}, "status_code": 200}