{"success": true, "company_id": "70126790", "data": {"metadata": {"contentItemCount": 96}, "content": [{"payPeriodId": "1090068308878516", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-04-05T00:00:00Z", "endDate": "2025-04-11T00:00:00Z", "submitByDate": "2025-04-16T00:00:00Z", "checkDate": "2025-04-18T00:00:00Z", "checkCount": 0, "id": "24dc593f-912b-4205-804b-8e9d025c8d44", "companyId": "70126790", "type": "payperiod", "_rid": "NmJkAKiCbEfH4AAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfH4AAAAAAAAA==/", "_etag": "\"9a00b64c-0000-0100-0000-686fe11a0000\"", "_attachments": "attachments/", "_ts": 1752162586}, {"payPeriodId": "1090068472679456", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-04-12T00:00:00Z", "endDate": "2025-04-22T00:00:00Z", "submitByDate": "2025-04-23T00:00:00Z", "checkDate": "2025-04-25T00:00:00Z", "checkCount": 0, "id": "03cab3b9-48c5-4b33-a0b8-3b3dc94e0833", "companyId": "70126790", "type": "payperiod", "_rid": "NmJkAKiCbEfI4AAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfI4AAAAAAAAA==/", "_etag": "\"9a00b74c-0000-0100-0000-686fe11a0000\"", "_attachments": "attachments/", "_ts": 1752162586}, {"payPeriodId": "1090068607580206", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-04-19T00:00:00Z", "endDate": "2025-04-25T00:00:00Z", "submitByDate": "2025-04-30T00:00:00Z", "checkDate": "2025-05-02T00:00:00Z", "checkCount": 0, "id": "ac7a192f-d9c5-4ee0-801f-48f66f248d10", "companyId": "70126790", "type": "payperiod", "_rid": "NmJkAKiCbEfJ4AAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfJ4AAAAAAAAA==/", "_etag": "\"9a00bb4c-0000-0100-0000-686fe11a0000\"", "_attachments": "attachments/", "_ts": 1752162586}, {"payPeriodId": "1090068766474605", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-04-26T00:00:00Z", "endDate": "2025-05-06T00:00:00Z", "submitByDate": "2025-05-07T00:00:00Z", "checkDate": "2025-05-09T00:00:00Z", "checkCount": 0, "id": "f24e3ca8-fa28-4716-a6c2-61c83280660d", "companyId": "70126790", "type": "payperiod", "_rid": "NmJkAKiCbEfK4AAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfK4AAAAAAAAA==/", "_etag": "\"9a00c44c-0000-0100-0000-686fe11a0000\"", "_attachments": "attachments/", "_ts": 1752162586}, {"payPeriodId": "1090068910122723", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-05-03T00:00:00Z", "endDate": "2025-05-13T00:00:00Z", "submitByDate": "2025-05-14T00:00:00Z", "checkDate": "2025-05-16T00:00:00Z", "checkCount": 0, "id": "b99e7c9e-5cdf-47f7-864f-f18abe7a691b", "companyId": "70126790", "type": "payperiod", "_rid": "NmJkAKiCbEfL4AAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfL4AAAAAAAAA==/", "_etag": "\"9a00ca4c-0000-0100-0000-686fe11b0000\"", "_attachments": "attachments/", "_ts": 1752162587}, {"payPeriodId": "1090069372811176", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-05-27T00:00:00Z", "endDate": "2025-06-03T00:00:00Z", "submitByDate": "2025-06-04T00:00:00Z", "checkDate": "2025-06-06T00:00:00Z", "checkCount": 0, "id": "676d289d-7d23-4462-87eb-7c3e080895ff", "companyId": "70126790", "type": "payperiod", "_rid": "NmJkAKiCbEfM4AAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfM4AAAAAAAAA==/", "_etag": "\"9a00cc4c-0000-0100-0000-686fe11b0000\"", "_attachments": "attachments/", "_ts": 1752162587}, {"payPeriodId": "1090066261671213", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-01-04T00:00:00Z", "endDate": "2025-01-10T00:00:00Z", "submitByDate": "2025-01-15T00:00:00Z", "checkDate": "2025-01-17T00:00:00Z", "checkCount": 0, "id": "9d277d7d-bd9b-4768-9af0-4eb134575b35", "companyId": "70126790", "type": "payperiod", "_rid": "NmJkAKiCbEfN4AAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfN4AAAAAAAAA==/", "_etag": "\"9a00d14c-0000-0100-0000-686fe11b0000\"", "_attachments": "attachments/", "_ts": 1752162587}, {"payPeriodId": "1090066718312102", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-01-25T00:00:00Z", "endDate": "2025-01-31T00:00:00Z", "submitByDate": "2025-02-05T00:00:00Z", "checkDate": "2025-02-07T00:00:00Z", "checkCount": 0, "id": "e33bca87-2781-4325-89e5-d6eca2ed9cac", "companyId": "70126790", "type": "payperiod", "_rid": "NmJkAKiCbEfO4AAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfO4AAAAAAAAA==/", "_etag": "\"9a00d44c-0000-0100-0000-686fe11b0000\"", "_attachments": "attachments/", "_ts": 1752162587}, {"payPeriodId": "1090067366309235", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-02-22T00:00:00Z", "endDate": "2025-02-28T00:00:00Z", "submitByDate": "2025-03-05T00:00:00Z", "checkDate": "2025-03-07T00:00:00Z", "checkCount": 0, "id": "202283d8-b05b-4228-b90a-154356ec7424", "companyId": "70126790", "type": "payperiod", "_rid": "NmJkAKiCbEfP4AAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfP4AAAAAAAAA==/", "_etag": "\"9a00d94c-0000-0100-0000-686fe11b0000\"", "_attachments": "attachments/", "_ts": 1752162587}, {"payPeriodId": "1090069068164443", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-05-10T00:00:00Z", "endDate": "2025-05-16T00:00:00Z", "submitByDate": "2025-05-21T00:00:00Z", "checkDate": "2025-05-23T00:00:00Z", "checkCount": 0, "id": "1ab8833d-7f61-4704-8564-c98ca612831e", "companyId": "70126790", "type": "payperiod", "_rid": "NmJkAKiCbEfQ4AAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfQ4AAAAAAAAA==/", "_etag": "\"9a00dc4c-0000-0100-0000-686fe11b0000\"", "_attachments": "attachments/", "_ts": 1752162587}, {"payPeriodId": "1090069981462594", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-06-28T00:00:00Z", "endDate": "2025-07-04T00:00:00Z", "submitByDate": "2025-07-09T00:00:00Z", "checkDate": "2025-07-11T00:00:00Z", "checkCount": 0, "id": "db511352-3835-40ad-9b98-9ce6b7e69959", "companyId": "70126790", "type": "payperiod", "_rid": "NmJkAKiCbEfR4AAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfR4AAAAAAAAA==/", "_etag": "\"9a00df4c-0000-0100-0000-686fe11b0000\"", "_attachments": "attachments/", "_ts": 1752162587}, {"payPeriodId": "1090070290801900", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-07-05T00:00:00Z", "endDate": "2025-07-11T00:00:00Z", "submitByDate": "2025-07-16T00:00:00Z", "checkDate": "2025-07-18T00:00:00Z", "checkCount": 0, "id": "2fbc11fa-84c7-4da3-a0ca-008a04ebb451", "companyId": "70126790", "type": "payperiod", "_rid": "NmJkAKiCbEfS4AAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfS4AAAAAAAAA==/", "_etag": "\"9a00e44c-0000-0100-0000-686fe11b0000\"", "_attachments": "attachments/", "_ts": 1752162587}, {"payPeriodId": "1090070290801901", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-07-12T00:00:00Z", "endDate": "2025-07-18T00:00:00Z", "submitByDate": "2025-07-23T00:00:00Z", "checkDate": "2025-07-25T00:00:00Z", "checkCount": 0, "id": "b23accad-6a16-488f-9642-3a469e047759", "companyId": "70126790", "type": "payperiod", "_rid": "NmJkAKiCbEfT4AAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfT4AAAAAAAAA==/", "_etag": "\"9a00e54c-0000-0100-0000-686fe11b0000\"", "_attachments": "attachments/", "_ts": 1752162587}, {"payPeriodId": "1090070534686875", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-07-19T00:00:00Z", "endDate": "2025-07-25T00:00:00Z", "submitByDate": "2025-07-30T00:00:00Z", "checkDate": "2025-08-01T00:00:00Z", "checkCount": 0, "id": "6e09b1db-19ff-4af2-bf39-4cf768e42634", "companyId": "70126790", "type": "payperiod", "_rid": "NmJkAKiCbEfU4AAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfU4AAAAAAAAA==/", "_etag": "\"9a00ea4c-0000-0100-0000-686fe11b0000\"", "_attachments": "attachments/", "_ts": 1752162587}, {"payPeriodId": "1090070684726097", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-07-26T00:00:00Z", "endDate": "2025-08-01T00:00:00Z", "submitByDate": "2025-08-06T00:00:00Z", "checkDate": "2025-08-08T00:00:00Z", "checkCount": 0, "id": "f042c64d-67a2-4c9b-aee5-2748c4254de6", "companyId": "70126790", "type": "payperiod", "_rid": "NmJkAKiCbEfV4AAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfV4AAAAAAAAA==/", "_etag": "\"9a00ec4c-0000-0100-0000-686fe11b0000\"", "_attachments": "attachments/", "_ts": 1752162587}, {"payPeriodId": "1090070836330844", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-08-02T00:00:00Z", "endDate": "2025-08-08T00:00:00Z", "submitByDate": "2025-08-13T00:00:00Z", "checkDate": "2025-08-15T00:00:00Z", "checkCount": 0, "id": "ff38e5f5-9384-4dda-a65c-aa597add5a9e", "companyId": "70126790", "type": "payperiod", "_rid": "NmJkAKiCbEfW4AAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfW4AAAAAAAAA==/", "_etag": "\"9a00ef4c-0000-0100-0000-686fe11b0000\"", "_attachments": "attachments/", "_ts": 1752162587}, {"payPeriodId": "1090070992165330", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-08-09T00:00:00Z", "endDate": "2025-08-15T00:00:00Z", "submitByDate": "2025-08-20T00:00:00Z", "checkDate": "2025-08-22T00:00:00Z", "checkCount": 0, "id": "df371f8e-a388-43bd-99da-01153ddab3cf", "companyId": "70126790", "type": "payperiod", "_rid": "NmJkAKiCbEfX4AAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfX4AAAAAAAAA==/", "_etag": "\"9a00f34c-0000-0100-0000-686fe11b0000\"", "_attachments": "attachments/", "_ts": 1752162587}, {"payPeriodId": "1090071223750859", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-08-16T00:00:00Z", "endDate": "2025-08-22T00:00:00Z", "submitByDate": "2025-08-27T00:00:00Z", "checkDate": "2025-08-29T00:00:00Z", "checkCount": 0, "id": "7d571307-0b43-4273-9aa2-e7459cca3357", "companyId": "70126790", "type": "payperiod", "_rid": "NmJkAKiCbEfY4AAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfY4AAAAAAAAA==/", "_etag": "\"9a00f64c-0000-0100-0000-686fe11c0000\"", "_attachments": "attachments/", "_ts": 1752162588}, {"payPeriodId": "1090071223750860", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-08-23T00:00:00Z", "endDate": "2025-08-29T00:00:00Z", "submitByDate": "2025-09-03T00:00:00Z", "checkDate": "2025-09-05T00:00:00Z", "checkCount": 0, "id": "03e050bc-0a1a-40a0-a31c-73efdfd7e558", "companyId": "70126790", "type": "payperiod", "_rid": "NmJkAKiCbEfZ4AAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfZ4AAAAAAAAA==/", "_etag": "\"9a00fb4c-0000-0100-0000-686fe11c0000\"", "_attachments": "attachments/", "_ts": 1752162588}, {"payPeriodId": "1090071471643499", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-08-30T00:00:00Z", "endDate": "2025-09-05T00:00:00Z", "submitByDate": "2025-09-10T00:00:00Z", "checkDate": "2025-09-12T00:00:00Z", "checkCount": 0, "id": "9b17ee04-c4ab-4258-aa17-7b70d4f0f068", "companyId": "70126790", "type": "payperiod", "_rid": "NmJkAKiCbEfa4AAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfa4AAAAAAAAA==/", "_etag": "\"9a00fd4c-0000-0100-0000-686fe11c0000\"", "_attachments": "attachments/", "_ts": 1752162588}, {"payPeriodId": "1090071689298598", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-09-06T00:00:00Z", "endDate": "2025-09-12T00:00:00Z", "submitByDate": "2025-09-17T00:00:00Z", "checkDate": "2025-09-19T00:00:00Z", "checkCount": 0, "id": "2ef9d67a-339a-43a8-a777-1002b10e718a", "companyId": "70126790", "type": "payperiod", "_rid": "NmJkAKiCbEfb4AAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfb4AAAAAAAAA==/", "_etag": "\"9a00024d-0000-0100-0000-686fe11c0000\"", "_attachments": "attachments/", "_ts": 1752162588}, {"payPeriodId": "1090071689298599", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-09-13T00:00:00Z", "endDate": "2025-09-19T00:00:00Z", "submitByDate": "2025-09-24T00:00:00Z", "checkDate": "2025-09-26T00:00:00Z", "checkCount": 0, "id": "95e98dc5-738f-4150-97b9-b193e97b94ab", "companyId": "70126790", "type": "payperiod", "_rid": "NmJkAKiCbEfc4AAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfc4AAAAAAAAA==/", "_etag": "\"9a00094d-0000-0100-0000-686fe11c0000\"", "_attachments": "attachments/", "_ts": 1752162588}, {"payPeriodId": "1090072003628744", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-09-20T00:00:00Z", "endDate": "2025-09-26T00:00:00Z", "submitByDate": "2025-10-01T00:00:00Z", "checkDate": "2025-10-03T00:00:00Z", "checkCount": 0, "id": "c79de0db-6381-42c1-9580-50191d2b1b0d", "companyId": "70126790", "type": "payperiod", "_rid": "NmJkAKiCbEfd4AAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfd4AAAAAAAAA==/", "_etag": "\"9a000e4d-0000-0100-0000-686fe11c0000\"", "_attachments": "attachments/", "_ts": 1752162588}, {"payPeriodId": "1090072003628745", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-09-27T00:00:00Z", "endDate": "2025-10-03T00:00:00Z", "submitByDate": "2025-10-08T00:00:00Z", "checkDate": "2025-10-10T00:00:00Z", "checkCount": 0, "id": "07b483b7-ab6d-4b8a-ab72-fdc7805985a9", "companyId": "70126790", "type": "payperiod", "_rid": "NmJkAKiCbEfe4AAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfe4AAAAAAAAA==/", "_etag": "\"9a000f4d-0000-0100-0000-686fe11c0000\"", "_attachments": "attachments/", "_ts": 1752162588}, {"payPeriodId": "1090068308878516", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-04-05T00:00:00Z", "endDate": "2025-04-11T00:00:00Z", "submitByDate": "2025-04-16T00:00:00Z", "checkDate": "2025-04-18T00:00:00Z", "checkCount": 1, "id": "53c9cfb9-d6cd-4f82-8dc0-53ec855bde6d", "companyId": "70126790", "type": "payperiod", "_rid": "NmJkAKiCbEff4AAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEff4AAAAAAAAA==/", "_etag": "\"9a00124d-0000-0100-0000-686fe11c0000\"", "_attachments": "attachments/", "_ts": 1752162588}, {"payPeriodId": "1090068472679456", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-04-12T00:00:00Z", "endDate": "2025-04-22T00:00:00Z", "submitByDate": "2025-04-23T00:00:00Z", "checkDate": "2025-04-25T00:00:00Z", "checkCount": 2, "id": "466931dd-606b-486f-8d6d-1cc5e2870ddd", "companyId": "70126790", "type": "payperiod", "_rid": "NmJkAKiCbEfg4AAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfg4AAAAAAAAA==/", "_etag": "\"9a00144d-0000-0100-0000-686fe11c0000\"", "_attachments": "attachments/", "_ts": 1752162588}, {"payPeriodId": "1090068607580206", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-04-19T00:00:00Z", "endDate": "2025-04-25T00:00:00Z", "submitByDate": "2025-04-30T00:00:00Z", "checkDate": "2025-05-02T00:00:00Z", "checkCount": 2, "id": "71c6637e-52f7-46e9-b341-da7041358942", "companyId": "70126790", "type": "payperiod", "_rid": "NmJkAKiCbEfh4AAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfh4AAAAAAAAA==/", "_etag": "\"9a00174d-0000-0100-0000-686fe11c0000\"", "_attachments": "attachments/", "_ts": 1752162588}, {"payPeriodId": "1090068766474605", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-04-26T00:00:00Z", "endDate": "2025-05-06T00:00:00Z", "submitByDate": "2025-05-07T00:00:00Z", "checkDate": "2025-05-09T00:00:00Z", "checkCount": 2, "id": "8e3419b1-ce9a-43ea-9814-ec9f3535deff", "companyId": "70126790", "type": "payperiod", "_rid": "NmJkAKiCbEfi4AAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfi4AAAAAAAAA==/", "_etag": "\"9a00224d-0000-0100-0000-686fe11c0000\"", "_attachments": "attachments/", "_ts": 1752162588}, {"payPeriodId": "1090068910122723", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-05-03T00:00:00Z", "endDate": "2025-05-13T00:00:00Z", "submitByDate": "2025-05-14T00:00:00Z", "checkDate": "2025-05-16T00:00:00Z", "checkCount": 1, "id": "a96ece7c-3c29-4c21-a834-46820f89005a", "companyId": "70126790", "type": "payperiod", "_rid": "NmJkAKiCbEfj4AAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfj4AAAAAAAAA==/", "_etag": "\"9a002b4d-0000-0100-0000-686fe11c0000\"", "_attachments": "attachments/", "_ts": 1752162588}, {"payPeriodId": "1090069372811176", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-05-27T00:00:00Z", "endDate": "2025-06-03T00:00:00Z", "submitByDate": "2025-06-04T00:00:00Z", "checkDate": "2025-06-06T00:00:00Z", "checkCount": 2, "id": "0389dc42-6205-467e-ab03-137c698c1bde", "companyId": "70126790", "type": "payperiod", "_rid": "NmJkAKiCbEfk4AAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfk4AAAAAAAAA==/", "_etag": "\"9a002d4d-0000-0100-0000-686fe11d0000\"", "_attachments": "attachments/", "_ts": 1752162589}, {"payPeriodId": "1090066261671213", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-01-04T00:00:00Z", "endDate": "2025-01-10T00:00:00Z", "submitByDate": "2025-01-15T00:00:00Z", "checkDate": "2025-01-17T00:00:00Z", "checkCount": 0, "id": "f1a518f3-092a-4aca-907e-bbac15dd57ca", "companyId": "70126790", "type": "payperiod", "_rid": "NmJkAKiCbEfl4AAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfl4AAAAAAAAA==/", "_etag": "\"9a00314d-0000-0100-0000-686fe11d0000\"", "_attachments": "attachments/", "_ts": 1752162589}, {"payPeriodId": "1090066718312102", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-01-25T00:00:00Z", "endDate": "2025-01-31T00:00:00Z", "submitByDate": "2025-02-05T00:00:00Z", "checkDate": "2025-02-07T00:00:00Z", "checkCount": 0, "id": "c1e98a41-6ad9-4fbd-8de1-4d0b9f2670cf", "companyId": "70126790", "type": "payperiod", "_rid": "NmJkAKiCbEfm4AAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfm4AAAAAAAAA==/", "_etag": "\"9a00354d-0000-0100-0000-686fe11d0000\"", "_attachments": "attachments/", "_ts": 1752162589}, {"payPeriodId": "1090067366309235", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-02-22T00:00:00Z", "endDate": "2025-02-28T00:00:00Z", "submitByDate": "2025-03-05T00:00:00Z", "checkDate": "2025-03-07T00:00:00Z", "checkCount": 0, "id": "7596f747-dd6a-4e5d-ab65-e1f1d8e15ac1", "companyId": "70126790", "type": "payperiod", "_rid": "NmJkAKiCbEfn4AAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfn4AAAAAAAAA==/", "_etag": "\"9a00404d-0000-0100-0000-686fe11d0000\"", "_attachments": "attachments/", "_ts": 1752162589}, {"payPeriodId": "1090069068164443", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-05-10T00:00:00Z", "endDate": "2025-05-16T00:00:00Z", "submitByDate": "2025-05-21T00:00:00Z", "checkDate": "2025-05-23T00:00:00Z", "checkCount": 0, "id": "5fd817fa-64e6-4864-bffd-a94934c0c065", "companyId": "70126790", "type": "payperiod", "_rid": "NmJkAKiCbEfo4AAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfo4AAAAAAAAA==/", "_etag": "\"9a00444d-0000-0100-0000-686fe11d0000\"", "_attachments": "attachments/", "_ts": 1752162589}, {"payPeriodId": "1090069981462594", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-06-28T00:00:00Z", "endDate": "2025-07-04T00:00:00Z", "submitByDate": "2025-07-09T00:00:00Z", "checkDate": "2025-07-11T00:00:00Z", "checkCount": 0, "id": "a9f883fb-ca81-4766-a1a2-ce25c22e0465", "companyId": "70126790", "type": "payperiod", "_rid": "NmJkAKiCbEfp4AAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfp4AAAAAAAAA==/", "_etag": "\"9a004a4d-0000-0100-0000-686fe11d0000\"", "_attachments": "attachments/", "_ts": 1752162589}, {"payPeriodId": "1090070290801900", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-07-05T00:00:00Z", "endDate": "2025-07-11T00:00:00Z", "submitByDate": "2025-07-16T00:00:00Z", "checkDate": "2025-07-18T00:00:00Z", "checkCount": 0, "id": "68eff276-fa3f-4100-9f68-ef66f2c85191", "companyId": "70126790", "type": "payperiod", "_rid": "NmJkAKiCbEfq4AAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfq4AAAAAAAAA==/", "_etag": "\"9a004d4d-0000-0100-0000-686fe11d0000\"", "_attachments": "attachments/", "_ts": 1752162589}, {"payPeriodId": "1090070290801901", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-07-12T00:00:00Z", "endDate": "2025-07-18T00:00:00Z", "submitByDate": "2025-07-23T00:00:00Z", "checkDate": "2025-07-25T00:00:00Z", "checkCount": 0, "id": "4eefcf07-d78f-4725-8529-02aefc9996f9", "companyId": "70126790", "type": "payperiod", "_rid": "NmJkAKiCbEfr4AAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfr4AAAAAAAAA==/", "_etag": "\"9a004f4d-0000-0100-0000-686fe11d0000\"", "_attachments": "attachments/", "_ts": 1752162589}, {"payPeriodId": "1090070534686875", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-07-19T00:00:00Z", "endDate": "2025-07-25T00:00:00Z", "submitByDate": "2025-07-30T00:00:00Z", "checkDate": "2025-08-01T00:00:00Z", "checkCount": 0, "id": "de6ba565-cd97-4759-aba6-3daa3c28de41", "companyId": "70126790", "type": "payperiod", "_rid": "NmJkAKiCbEfs4AAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfs4AAAAAAAAA==/", "_etag": "\"9a00534d-0000-0100-0000-686fe11d0000\"", "_attachments": "attachments/", "_ts": 1752162589}, {"payPeriodId": "1090070684726097", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-07-26T00:00:00Z", "endDate": "2025-08-01T00:00:00Z", "submitByDate": "2025-08-06T00:00:00Z", "checkDate": "2025-08-08T00:00:00Z", "checkCount": 0, "id": "44882ec2-f4e8-40c9-a03c-34f9345070a0", "companyId": "70126790", "type": "payperiod", "_rid": "NmJkAKiCbEft4AAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEft4AAAAAAAAA==/", "_etag": "\"9a00564d-0000-0100-0000-686fe11d0000\"", "_attachments": "attachments/", "_ts": 1752162589}, {"payPeriodId": "1090070836330844", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-08-02T00:00:00Z", "endDate": "2025-08-08T00:00:00Z", "submitByDate": "2025-08-13T00:00:00Z", "checkDate": "2025-08-15T00:00:00Z", "checkCount": 0, "id": "1d5647b8-b42c-4e34-abc1-64b62aa52271", "companyId": "70126790", "type": "payperiod", "_rid": "NmJkAKiCbEfu4AAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfu4AAAAAAAAA==/", "_etag": "\"9a005e4d-0000-0100-0000-686fe11d0000\"", "_attachments": "attachments/", "_ts": 1752162589}, {"payPeriodId": "1090070992165330", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-08-09T00:00:00Z", "endDate": "2025-08-15T00:00:00Z", "submitByDate": "2025-08-20T00:00:00Z", "checkDate": "2025-08-22T00:00:00Z", "checkCount": 0, "id": "817b58de-fde6-49b2-8935-c7fdf39564eb", "companyId": "70126790", "type": "payperiod", "_rid": "NmJkAKiCbEfv4AAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfv4AAAAAAAAA==/", "_etag": "\"9a00614d-0000-0100-0000-686fe11d0000\"", "_attachments": "attachments/", "_ts": 1752162589}, {"payPeriodId": "1090071223750859", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-08-16T00:00:00Z", "endDate": "2025-08-22T00:00:00Z", "submitByDate": "2025-08-27T00:00:00Z", "checkDate": "2025-08-29T00:00:00Z", "checkCount": 0, "id": "c478a72f-549e-4e42-acf7-8e536174b0aa", "companyId": "70126790", "type": "payperiod", "_rid": "NmJkAKiCbEfw4AAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfw4AAAAAAAAA==/", "_etag": "\"9a00634d-0000-0100-0000-686fe11d0000\"", "_attachments": "attachments/", "_ts": 1752162589}, {"payPeriodId": "1090071223750860", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-08-23T00:00:00Z", "endDate": "2025-08-29T00:00:00Z", "submitByDate": "2025-09-03T00:00:00Z", "checkDate": "2025-09-05T00:00:00Z", "checkCount": 0, "id": "a6ceabc3-b2a4-46b8-bcb6-a3f403e860ec", "companyId": "70126790", "type": "payperiod", "_rid": "NmJkAKiCbEfx4AAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfx4AAAAAAAAA==/", "_etag": "\"9a00664d-0000-0100-0000-686fe11e0000\"", "_attachments": "attachments/", "_ts": 1752162590}, {"payPeriodId": "1090071471643499", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-08-30T00:00:00Z", "endDate": "2025-09-05T00:00:00Z", "submitByDate": "2025-09-10T00:00:00Z", "checkDate": "2025-09-12T00:00:00Z", "checkCount": 0, "id": "3896bee5-ed19-4987-942b-7f6f7cac168d", "companyId": "70126790", "type": "payperiod", "_rid": "NmJkAKiCbEfy4AAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfy4AAAAAAAAA==/", "_etag": "\"9a00694d-0000-0100-0000-686fe11e0000\"", "_attachments": "attachments/", "_ts": 1752162590}, {"payPeriodId": "1090071689298598", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-09-06T00:00:00Z", "endDate": "2025-09-12T00:00:00Z", "submitByDate": "2025-09-17T00:00:00Z", "checkDate": "2025-09-19T00:00:00Z", "checkCount": 0, "id": "4ed3db1e-133c-477e-b559-89a6c4ec5cde", "companyId": "70126790", "type": "payperiod", "_rid": "NmJkAKiCbEfz4AAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEfz4AAAAAAAAA==/", "_etag": "\"9a006c4d-0000-0100-0000-686fe11e0000\"", "_attachments": "attachments/", "_ts": 1752162590}, {"payPeriodId": "1090071689298599", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-09-13T00:00:00Z", "endDate": "2025-09-19T00:00:00Z", "submitByDate": "2025-09-24T00:00:00Z", "checkDate": "2025-09-26T00:00:00Z", "checkCount": 0, "id": "b9e9d1ba-6e16-422e-bce5-2a0f45c7db2a", "companyId": "70126790", "type": "payperiod", "_rid": "NmJkAKiCbEf04AAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf04AAAAAAAAA==/", "_etag": "\"9a00714d-0000-0100-0000-686fe11e0000\"", "_attachments": "attachments/", "_ts": 1752162590}, {"payPeriodId": "1090072003628744", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-09-20T00:00:00Z", "endDate": "2025-09-26T00:00:00Z", "submitByDate": "2025-10-01T00:00:00Z", "checkDate": "2025-10-03T00:00:00Z", "checkCount": 0, "id": "2518a0c4-44e6-458c-9ff8-69daf8615634", "companyId": "70126790", "type": "payperiod", "_rid": "NmJkAKiCbEf14AAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf14AAAAAAAAA==/", "_etag": "\"9a00764d-0000-0100-0000-686fe11e0000\"", "_attachments": "attachments/", "_ts": 1752162590}, {"payPeriodId": "1090072003628745", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-09-27T00:00:00Z", "endDate": "2025-10-03T00:00:00Z", "submitByDate": "2025-10-08T00:00:00Z", "checkDate": "2025-10-10T00:00:00Z", "checkCount": 0, "id": "226d172b-5d7c-444c-8d8c-515ef9605f6c", "companyId": "70126790", "type": "payperiod", "_rid": "NmJkAKiCbEf24AAAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf24AAAAAAAAA==/", "_etag": "\"9a00784d-0000-0100-0000-686fe11e0000\"", "_attachments": "attachments/", "_ts": 1752162590}, {"payPeriodId": "1090068308878516", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-04-05T00:00:00Z", "endDate": "2025-04-11T00:00:00Z", "submitByDate": "2025-04-16T00:00:00Z", "checkDate": "2025-04-18T00:00:00Z", "checkCount": 0, "id": "e7d1e0b3-2fe0-406d-9e78-59603f5c4bef", "companyId": "70126790", "type": "payperiod", "_rid": "NmJkAKiCbEf02QEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf02QEAAAAAAA==/", "_etag": "\"a000a864-0000-0100-0000-687007f40000\"", "_attachments": "attachments/", "_ts": 1752172532}, {"payPeriodId": "1090068472679456", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-04-12T00:00:00Z", "endDate": "2025-04-22T00:00:00Z", "submitByDate": "2025-04-23T00:00:00Z", "checkDate": "2025-04-25T00:00:00Z", "checkCount": 0, "id": "9deae4f1-461f-408f-b7be-355c56245fe7", "companyId": "70126790", "type": "payperiod", "_rid": "NmJkAKiCbEf12QEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf12QEAAAAAAA==/", "_etag": "\"a000a964-0000-0100-0000-687007f40000\"", "_attachments": "attachments/", "_ts": 1752172532}, {"payPeriodId": "1090068607580206", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-04-19T00:00:00Z", "endDate": "2025-04-25T00:00:00Z", "submitByDate": "2025-04-30T00:00:00Z", "checkDate": "2025-05-02T00:00:00Z", "checkCount": 0, "id": "342ab506-6232-491d-9dc5-08a55a0fd55c", "companyId": "70126790", "type": "payperiod", "_rid": "NmJkAKiCbEf22QEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf22QEAAAAAAA==/", "_etag": "\"a000ab64-0000-0100-0000-687007f40000\"", "_attachments": "attachments/", "_ts": 1752172532}, {"payPeriodId": "1090068766474605", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-04-26T00:00:00Z", "endDate": "2025-05-06T00:00:00Z", "submitByDate": "2025-05-07T00:00:00Z", "checkDate": "2025-05-09T00:00:00Z", "checkCount": 0, "id": "cdedf10e-9fed-49be-9292-0fb8075309d3", "companyId": "70126790", "type": "payperiod", "_rid": "NmJkAKiCbEf32QEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf32QEAAAAAAA==/", "_etag": "\"a000ae64-0000-0100-0000-687007f40000\"", "_attachments": "attachments/", "_ts": 1752172532}, {"payPeriodId": "1090068910122723", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-05-03T00:00:00Z", "endDate": "2025-05-13T00:00:00Z", "submitByDate": "2025-05-14T00:00:00Z", "checkDate": "2025-05-16T00:00:00Z", "checkCount": 0, "id": "3d90f539-8809-4636-bf29-af68412522d1", "companyId": "70126790", "type": "payperiod", "_rid": "NmJkAKiCbEf42QEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf42QEAAAAAAA==/", "_etag": "\"a000b364-0000-0100-0000-687007f40000\"", "_attachments": "attachments/", "_ts": 1752172532}, {"payPeriodId": "1090069372811176", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-05-27T00:00:00Z", "endDate": "2025-06-03T00:00:00Z", "submitByDate": "2025-06-04T00:00:00Z", "checkDate": "2025-06-06T00:00:00Z", "checkCount": 0, "id": "f2d05fd3-f0e3-43f3-ab32-07e8006c7549", "companyId": "70126790", "type": "payperiod", "_rid": "NmJkAKiCbEf52QEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf52QEAAAAAAA==/", "_etag": "\"a000b764-0000-0100-0000-687007f40000\"", "_attachments": "attachments/", "_ts": 1752172532}, {"payPeriodId": "1090066261671213", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-01-04T00:00:00Z", "endDate": "2025-01-10T00:00:00Z", "submitByDate": "2025-01-15T00:00:00Z", "checkDate": "2025-01-17T00:00:00Z", "checkCount": 0, "id": "6cc8c03c-013c-4223-b743-18943bf2d397", "companyId": "70126790", "type": "payperiod", "_rid": "NmJkAKiCbEf62QEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf62QEAAAAAAA==/", "_etag": "\"a000b964-0000-0100-0000-687007f40000\"", "_attachments": "attachments/", "_ts": 1752172532}, {"payPeriodId": "1090066718312102", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-01-25T00:00:00Z", "endDate": "2025-01-31T00:00:00Z", "submitByDate": "2025-02-05T00:00:00Z", "checkDate": "2025-02-07T00:00:00Z", "checkCount": 0, "id": "45453daf-2804-4d45-9a54-635a1e79bf91", "companyId": "70126790", "type": "payperiod", "_rid": "NmJkAKiCbEf72QEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf72QEAAAAAAA==/", "_etag": "\"a000bd64-0000-0100-0000-687007f40000\"", "_attachments": "attachments/", "_ts": 1752172532}, {"payPeriodId": "1090067366309235", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-02-22T00:00:00Z", "endDate": "2025-02-28T00:00:00Z", "submitByDate": "2025-03-05T00:00:00Z", "checkDate": "2025-03-07T00:00:00Z", "checkCount": 0, "id": "c6195456-679b-4531-8b3d-bbe0ed6a2232", "companyId": "70126790", "type": "payperiod", "_rid": "NmJkAKiCbEf82QEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf82QEAAAAAAA==/", "_etag": "\"a000bf64-0000-0100-0000-687007f40000\"", "_attachments": "attachments/", "_ts": 1752172532}, {"payPeriodId": "1090069068164443", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-05-10T00:00:00Z", "endDate": "2025-05-16T00:00:00Z", "submitByDate": "2025-05-21T00:00:00Z", "checkDate": "2025-05-23T00:00:00Z", "checkCount": 0, "id": "3ca0dd91-3faa-4ea3-a0e6-0ec2a2dcca51", "companyId": "70126790", "type": "payperiod", "_rid": "NmJkAKiCbEf92QEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf92QEAAAAAAA==/", "_etag": "\"a000c264-0000-0100-0000-687007f40000\"", "_attachments": "attachments/", "_ts": 1752172532}, {"payPeriodId": "1090069981462594", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-06-28T00:00:00Z", "endDate": "2025-07-04T00:00:00Z", "submitByDate": "2025-07-09T00:00:00Z", "checkDate": "2025-07-11T00:00:00Z", "checkCount": 0, "id": "49206aae-cdfa-4417-8b59-736d040b0bdc", "companyId": "70126790", "type": "payperiod", "_rid": "NmJkAKiCbEf+2QEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf+2QEAAAAAAA==/", "_etag": "\"a000c364-0000-0100-0000-687007f40000\"", "_attachments": "attachments/", "_ts": 1752172532}, {"payPeriodId": "1090070290801900", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-07-05T00:00:00Z", "endDate": "2025-07-11T00:00:00Z", "submitByDate": "2025-07-16T00:00:00Z", "checkDate": "2025-07-18T00:00:00Z", "checkCount": 0, "id": "dcce6c73-2c9f-4806-bd8e-f1c8880ef21c", "companyId": "70126790", "type": "payperiod", "_rid": "NmJkAKiCbEf-2QEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEf-2QEAAAAAAA==/", "_etag": "\"a000c664-0000-0100-0000-687007f50000\"", "_attachments": "attachments/", "_ts": 1752172533}, {"payPeriodId": "1090070290801901", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-07-12T00:00:00Z", "endDate": "2025-07-18T00:00:00Z", "submitByDate": "2025-07-23T00:00:00Z", "checkDate": "2025-07-25T00:00:00Z", "checkCount": 0, "id": "6b4bf9d9-a680-448b-8462-54afa3952a45", "companyId": "70126790", "type": "payperiod", "_rid": "NmJkAKiCbEcA2gEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcA2gEAAAAAAA==/", "_etag": "\"a000c764-0000-0100-0000-687007f50000\"", "_attachments": "attachments/", "_ts": 1752172533}, {"payPeriodId": "1090070534686875", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-07-19T00:00:00Z", "endDate": "2025-07-25T00:00:00Z", "submitByDate": "2025-07-30T00:00:00Z", "checkDate": "2025-08-01T00:00:00Z", "checkCount": 0, "id": "dce4ac7d-0961-4167-acd8-de544d92ecfe", "companyId": "70126790", "type": "payperiod", "_rid": "NmJkAKiCbEcB2gEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcB2gEAAAAAAA==/", "_etag": "\"a000c964-0000-0100-0000-687007f50000\"", "_attachments": "attachments/", "_ts": 1752172533}, {"payPeriodId": "1090070684726097", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-07-26T00:00:00Z", "endDate": "2025-08-01T00:00:00Z", "submitByDate": "2025-08-06T00:00:00Z", "checkDate": "2025-08-08T00:00:00Z", "checkCount": 0, "id": "518e7119-07ef-4808-a7d2-c8c373d9ffb3", "companyId": "70126790", "type": "payperiod", "_rid": "NmJkAKiCbEcC2gEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcC2gEAAAAAAA==/", "_etag": "\"a000ca64-0000-0100-0000-687007f50000\"", "_attachments": "attachments/", "_ts": 1752172533}, {"payPeriodId": "1090070836330844", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-08-02T00:00:00Z", "endDate": "2025-08-08T00:00:00Z", "submitByDate": "2025-08-13T00:00:00Z", "checkDate": "2025-08-15T00:00:00Z", "checkCount": 0, "id": "40854844-e81c-4dee-9c39-4c886c38c537", "companyId": "70126790", "type": "payperiod", "_rid": "NmJkAKiCbEcD2gEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcD2gEAAAAAAA==/", "_etag": "\"a000cb64-0000-0100-0000-687007f50000\"", "_attachments": "attachments/", "_ts": 1752172533}, {"payPeriodId": "1090070992165330", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-08-09T00:00:00Z", "endDate": "2025-08-15T00:00:00Z", "submitByDate": "2025-08-20T00:00:00Z", "checkDate": "2025-08-22T00:00:00Z", "checkCount": 0, "id": "b28d5fd5-4cac-4488-91c6-fc1f9512d1d0", "companyId": "70126790", "type": "payperiod", "_rid": "NmJkAKiCbEcE2gEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcE2gEAAAAAAA==/", "_etag": "\"a000cd64-0000-0100-0000-687007f50000\"", "_attachments": "attachments/", "_ts": 1752172533}, {"payPeriodId": "1090071223750859", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-08-16T00:00:00Z", "endDate": "2025-08-22T00:00:00Z", "submitByDate": "2025-08-27T00:00:00Z", "checkDate": "2025-08-29T00:00:00Z", "checkCount": 0, "id": "9434d939-250a-4425-8584-af6136dd4a33", "companyId": "70126790", "type": "payperiod", "_rid": "NmJkAKiCbEcF2gEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcF2gEAAAAAAA==/", "_etag": "\"a000d064-0000-0100-0000-687007f50000\"", "_attachments": "attachments/", "_ts": 1752172533}, {"payPeriodId": "1090071223750860", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-08-23T00:00:00Z", "endDate": "2025-08-29T00:00:00Z", "submitByDate": "2025-09-03T00:00:00Z", "checkDate": "2025-09-05T00:00:00Z", "checkCount": 0, "id": "4e81d646-56de-4c19-9603-bb69ec7add17", "companyId": "70126790", "type": "payperiod", "_rid": "NmJkAKiCbEcG2gEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcG2gEAAAAAAA==/", "_etag": "\"a000d464-0000-0100-0000-687007f50000\"", "_attachments": "attachments/", "_ts": 1752172533}, {"payPeriodId": "1090071471643499", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-08-30T00:00:00Z", "endDate": "2025-09-05T00:00:00Z", "submitByDate": "2025-09-10T00:00:00Z", "checkDate": "2025-09-12T00:00:00Z", "checkCount": 0, "id": "a45ed146-f66c-4177-8993-acc10f9a3f19", "companyId": "70126790", "type": "payperiod", "_rid": "NmJkAKiCbEcH2gEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcH2gEAAAAAAA==/", "_etag": "\"a000d764-0000-0100-0000-687007f50000\"", "_attachments": "attachments/", "_ts": 1752172533}, {"payPeriodId": "1090071689298598", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-09-06T00:00:00Z", "endDate": "2025-09-12T00:00:00Z", "submitByDate": "2025-09-17T00:00:00Z", "checkDate": "2025-09-19T00:00:00Z", "checkCount": 0, "id": "be4e7861-e7ef-4091-96ee-f54f8375f5c2", "companyId": "70126790", "type": "payperiod", "_rid": "NmJkAKiCbEcI2gEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcI2gEAAAAAAA==/", "_etag": "\"a000d964-0000-0100-0000-687007f50000\"", "_attachments": "attachments/", "_ts": 1752172533}, {"payPeriodId": "1090071689298599", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-09-13T00:00:00Z", "endDate": "2025-09-19T00:00:00Z", "submitByDate": "2025-09-24T00:00:00Z", "checkDate": "2025-09-26T00:00:00Z", "checkCount": 0, "id": "240515ea-7977-4f33-858e-66ab0fe77dde", "companyId": "70126790", "type": "payperiod", "_rid": "NmJkAKiCbEcJ2gEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcJ2gEAAAAAAA==/", "_etag": "\"a000dd64-0000-0100-0000-687007f50000\"", "_attachments": "attachments/", "_ts": 1752172533}, {"payPeriodId": "1090072003628744", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-09-20T00:00:00Z", "endDate": "2025-09-26T00:00:00Z", "submitByDate": "2025-10-01T00:00:00Z", "checkDate": "2025-10-03T00:00:00Z", "checkCount": 0, "id": "acedfa24-813d-4e09-838d-48a76a5e3c7b", "companyId": "70126790", "type": "payperiod", "_rid": "NmJkAKiCbEcK2gEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcK2gEAAAAAAA==/", "_etag": "\"a000e064-0000-0100-0000-687007f50000\"", "_attachments": "attachments/", "_ts": 1752172533}, {"payPeriodId": "1090072003628745", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-09-27T00:00:00Z", "endDate": "2025-10-03T00:00:00Z", "submitByDate": "2025-10-08T00:00:00Z", "checkDate": "2025-10-10T00:00:00Z", "checkCount": 0, "id": "e208f57b-1576-418a-88da-ecf0dd413734", "companyId": "70126790", "type": "payperiod", "_rid": "NmJkAKiCbEcL2gEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcL2gEAAAAAAA==/", "_etag": "\"a000e264-0000-0100-0000-687007f50000\"", "_attachments": "attachments/", "_ts": 1752172533}, {"payPeriodId": "1090068308878516", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-04-05T00:00:00Z", "endDate": "2025-04-11T00:00:00Z", "submitByDate": "2025-04-16T00:00:00Z", "checkDate": "2025-04-18T00:00:00Z", "checkCount": 1, "id": "ddb53738-bf64-4890-ad68-7805f07a707c", "companyId": "70126790", "type": "payperiod", "_rid": "NmJkAKiCbEcM2gEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcM2gEAAAAAAA==/", "_etag": "\"a000e364-0000-0100-0000-687007f60000\"", "_attachments": "attachments/", "_ts": 1752172534}, {"payPeriodId": "1090068472679456", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-04-12T00:00:00Z", "endDate": "2025-04-22T00:00:00Z", "submitByDate": "2025-04-23T00:00:00Z", "checkDate": "2025-04-25T00:00:00Z", "checkCount": 2, "id": "0fa33352-54ee-42cd-8cf1-1257ca1d7927", "companyId": "70126790", "type": "payperiod", "_rid": "NmJkAKiCbEcN2gEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcN2gEAAAAAAA==/", "_etag": "\"a000e564-0000-0100-0000-687007f60000\"", "_attachments": "attachments/", "_ts": 1752172534}, {"payPeriodId": "1090068607580206", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-04-19T00:00:00Z", "endDate": "2025-04-25T00:00:00Z", "submitByDate": "2025-04-30T00:00:00Z", "checkDate": "2025-05-02T00:00:00Z", "checkCount": 2, "id": "402821af-74ea-4ed3-b448-33017abf393a", "companyId": "70126790", "type": "payperiod", "_rid": "NmJkAKiCbEcO2gEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcO2gEAAAAAAA==/", "_etag": "\"a000e664-0000-0100-0000-687007f60000\"", "_attachments": "attachments/", "_ts": 1752172534}, {"payPeriodId": "1090068766474605", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-04-26T00:00:00Z", "endDate": "2025-05-06T00:00:00Z", "submitByDate": "2025-05-07T00:00:00Z", "checkDate": "2025-05-09T00:00:00Z", "checkCount": 2, "id": "49f99b47-1da2-4960-91f9-cd625d796bf3", "companyId": "70126790", "type": "payperiod", "_rid": "NmJkAKiCbEcP2gEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcP2gEAAAAAAA==/", "_etag": "\"a000e964-0000-0100-0000-687007f60000\"", "_attachments": "attachments/", "_ts": 1752172534}, {"payPeriodId": "1090068910122723", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-05-03T00:00:00Z", "endDate": "2025-05-13T00:00:00Z", "submitByDate": "2025-05-14T00:00:00Z", "checkDate": "2025-05-16T00:00:00Z", "checkCount": 1, "id": "ab301e78-930b-4361-9933-a3bfc09576b2", "companyId": "70126790", "type": "payperiod", "_rid": "NmJkAKiCbEcQ2gEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcQ2gEAAAAAAA==/", "_etag": "\"a000ec64-0000-0100-0000-687007f60000\"", "_attachments": "attachments/", "_ts": 1752172534}, {"payPeriodId": "1090069372811176", "intervalCode": "WEEKLY", "status": "COMPLETED", "description": "Weekly Payroll (1)", "startDate": "2025-05-27T00:00:00Z", "endDate": "2025-06-03T00:00:00Z", "submitByDate": "2025-06-04T00:00:00Z", "checkDate": "2025-06-06T00:00:00Z", "checkCount": 2, "id": "2d28224c-1db2-4035-964b-d4883779cebb", "companyId": "70126790", "type": "payperiod", "_rid": "NmJkAKiCbEcR2gEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcR2gEAAAAAAA==/", "_etag": "\"a000f064-0000-0100-0000-687007f60000\"", "_attachments": "attachments/", "_ts": 1752172534}, {"payPeriodId": "1090066261671213", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-01-04T00:00:00Z", "endDate": "2025-01-10T00:00:00Z", "submitByDate": "2025-01-15T00:00:00Z", "checkDate": "2025-01-17T00:00:00Z", "checkCount": 0, "id": "754bf53b-cf53-4907-97bb-f2a8b1bcdf1a", "companyId": "70126790", "type": "payperiod", "_rid": "NmJkAKiCbEcS2gEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcS2gEAAAAAAA==/", "_etag": "\"a000f364-0000-0100-0000-687007f60000\"", "_attachments": "attachments/", "_ts": 1752172534}, {"payPeriodId": "1090066718312102", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-01-25T00:00:00Z", "endDate": "2025-01-31T00:00:00Z", "submitByDate": "2025-02-05T00:00:00Z", "checkDate": "2025-02-07T00:00:00Z", "checkCount": 0, "id": "19f040e9-3ce3-42b2-bea0-8b3f757b0a7b", "companyId": "70126790", "type": "payperiod", "_rid": "NmJkAKiCbEcT2gEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcT2gEAAAAAAA==/", "_etag": "\"a000f864-0000-0100-0000-687007f60000\"", "_attachments": "attachments/", "_ts": 1752172534}, {"payPeriodId": "1090067366309235", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-02-22T00:00:00Z", "endDate": "2025-02-28T00:00:00Z", "submitByDate": "2025-03-05T00:00:00Z", "checkDate": "2025-03-07T00:00:00Z", "checkCount": 0, "id": "c1c856ea-0e40-4bdd-b1f2-7bbcf2341b4f", "companyId": "70126790", "type": "payperiod", "_rid": "NmJkAKiCbEcU2gEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcU2gEAAAAAAA==/", "_etag": "\"a000f964-0000-0100-0000-687007f60000\"", "_attachments": "attachments/", "_ts": 1752172534}, {"payPeriodId": "1090069068164443", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-05-10T00:00:00Z", "endDate": "2025-05-16T00:00:00Z", "submitByDate": "2025-05-21T00:00:00Z", "checkDate": "2025-05-23T00:00:00Z", "checkCount": 0, "id": "73e408b8-8f11-4006-8482-569e12fe3868", "companyId": "70126790", "type": "payperiod", "_rid": "NmJkAKiCbEcV2gEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcV2gEAAAAAAA==/", "_etag": "\"a000fc64-0000-0100-0000-687007f60000\"", "_attachments": "attachments/", "_ts": 1752172534}, {"payPeriodId": "1090069981462594", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-06-28T00:00:00Z", "endDate": "2025-07-04T00:00:00Z", "submitByDate": "2025-07-09T00:00:00Z", "checkDate": "2025-07-11T00:00:00Z", "checkCount": 0, "id": "e6053e79-2045-454a-aeae-543f4fc8b9e6", "companyId": "70126790", "type": "payperiod", "_rid": "NmJkAKiCbEcW2gEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcW2gEAAAAAAA==/", "_etag": "\"a000fe64-0000-0100-0000-687007f60000\"", "_attachments": "attachments/", "_ts": 1752172534}, {"payPeriodId": "1090070290801900", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-07-05T00:00:00Z", "endDate": "2025-07-11T00:00:00Z", "submitByDate": "2025-07-16T00:00:00Z", "checkDate": "2025-07-18T00:00:00Z", "checkCount": 0, "id": "dfa560b5-e046-49e1-9726-451f10a57a7e", "companyId": "70126790", "type": "payperiod", "_rid": "NmJkAKiCbEcX2gEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcX2gEAAAAAAA==/", "_etag": "\"a000ff64-0000-0100-0000-687007f60000\"", "_attachments": "attachments/", "_ts": 1752172534}, {"payPeriodId": "1090070290801901", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-07-12T00:00:00Z", "endDate": "2025-07-18T00:00:00Z", "submitByDate": "2025-07-23T00:00:00Z", "checkDate": "2025-07-25T00:00:00Z", "checkCount": 0, "id": "86928d27-c757-4832-ac95-08fbb2d2f177", "companyId": "70126790", "type": "payperiod", "_rid": "NmJkAKiCbEcY2gEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcY2gEAAAAAAA==/", "_etag": "\"a0000465-0000-0100-0000-687007f60000\"", "_attachments": "attachments/", "_ts": 1752172534}, {"payPeriodId": "1090070534686875", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-07-19T00:00:00Z", "endDate": "2025-07-25T00:00:00Z", "submitByDate": "2025-07-30T00:00:00Z", "checkDate": "2025-08-01T00:00:00Z", "checkCount": 0, "id": "1bc79ae9-fd28-4302-b4e4-de5c985a437c", "companyId": "70126790", "type": "payperiod", "_rid": "NmJkAKiCbEcZ2gEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcZ2gEAAAAAAA==/", "_etag": "\"a0000865-0000-0100-0000-687007f70000\"", "_attachments": "attachments/", "_ts": 1752172535}, {"payPeriodId": "1090070684726097", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-07-26T00:00:00Z", "endDate": "2025-08-01T00:00:00Z", "submitByDate": "2025-08-06T00:00:00Z", "checkDate": "2025-08-08T00:00:00Z", "checkCount": 0, "id": "169744a2-00f0-49e7-a38e-6be457d6b5b5", "companyId": "70126790", "type": "payperiod", "_rid": "NmJkAKiCbEca2gEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEca2gEAAAAAAA==/", "_etag": "\"a0000965-0000-0100-0000-687007f70000\"", "_attachments": "attachments/", "_ts": 1752172535}, {"payPeriodId": "1090070836330844", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-08-02T00:00:00Z", "endDate": "2025-08-08T00:00:00Z", "submitByDate": "2025-08-13T00:00:00Z", "checkDate": "2025-08-15T00:00:00Z", "checkCount": 0, "id": "3444584d-1ea5-45a4-a4dd-d57b68013f59", "companyId": "70126790", "type": "payperiod", "_rid": "NmJkAKiCbEcb2gEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcb2gEAAAAAAA==/", "_etag": "\"a0000c65-0000-0100-0000-687007f70000\"", "_attachments": "attachments/", "_ts": 1752172535}, {"payPeriodId": "1090070992165330", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-08-09T00:00:00Z", "endDate": "2025-08-15T00:00:00Z", "submitByDate": "2025-08-20T00:00:00Z", "checkDate": "2025-08-22T00:00:00Z", "checkCount": 0, "id": "e7577d30-6a19-4be3-9b1c-77eafdeafe5b", "companyId": "70126790", "type": "payperiod", "_rid": "NmJkAKiCbEcc2gEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcc2gEAAAAAAA==/", "_etag": "\"a0000e65-0000-0100-0000-687007f70000\"", "_attachments": "attachments/", "_ts": 1752172535}, {"payPeriodId": "1090071223750859", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-08-16T00:00:00Z", "endDate": "2025-08-22T00:00:00Z", "submitByDate": "2025-08-27T00:00:00Z", "checkDate": "2025-08-29T00:00:00Z", "checkCount": 0, "id": "d2a7f7a4-26fe-496c-a36a-1028f2830b9c", "companyId": "70126790", "type": "payperiod", "_rid": "NmJkAKiCbEcd2gEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcd2gEAAAAAAA==/", "_etag": "\"a0001165-0000-0100-0000-687007f70000\"", "_attachments": "attachments/", "_ts": 1752172535}, {"payPeriodId": "1090071223750860", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-08-23T00:00:00Z", "endDate": "2025-08-29T00:00:00Z", "submitByDate": "2025-09-03T00:00:00Z", "checkDate": "2025-09-05T00:00:00Z", "checkCount": 0, "id": "568e01de-9eec-4480-925d-756c18de57b0", "companyId": "70126790", "type": "payperiod", "_rid": "NmJkAKiCbEce2gEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEce2gEAAAAAAA==/", "_etag": "\"a0001365-0000-0100-0000-687007f70000\"", "_attachments": "attachments/", "_ts": 1752172535}, {"payPeriodId": "1090071471643499", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-08-30T00:00:00Z", "endDate": "2025-09-05T00:00:00Z", "submitByDate": "2025-09-10T00:00:00Z", "checkDate": "2025-09-12T00:00:00Z", "checkCount": 0, "id": "c77ba8f5-3f00-43cb-9173-ef77f20f7ae6", "companyId": "70126790", "type": "payperiod", "_rid": "NmJkAKiCbEcf2gEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcf2gEAAAAAAA==/", "_etag": "\"a0001865-0000-0100-0000-687007f70000\"", "_attachments": "attachments/", "_ts": 1752172535}, {"payPeriodId": "1090071689298598", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-09-06T00:00:00Z", "endDate": "2025-09-12T00:00:00Z", "submitByDate": "2025-09-17T00:00:00Z", "checkDate": "2025-09-19T00:00:00Z", "checkCount": 0, "id": "2c6451c2-6e8f-46ef-88f6-d001973fa660", "companyId": "70126790", "type": "payperiod", "_rid": "NmJkAKiCbEcg2gEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcg2gEAAAAAAA==/", "_etag": "\"a0001b65-0000-0100-0000-687007f70000\"", "_attachments": "attachments/", "_ts": 1752172535}, {"payPeriodId": "1090071689298599", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-09-13T00:00:00Z", "endDate": "2025-09-19T00:00:00Z", "submitByDate": "2025-09-24T00:00:00Z", "checkDate": "2025-09-26T00:00:00Z", "checkCount": 0, "id": "5056c4e0-23b3-430a-805e-eae294306e58", "companyId": "70126790", "type": "payperiod", "_rid": "NmJkAKiCbEch2gEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEch2gEAAAAAAA==/", "_etag": "\"a0001d65-0000-0100-0000-687007f70000\"", "_attachments": "attachments/", "_ts": 1752172535}, {"payPeriodId": "1090072003628744", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-09-20T00:00:00Z", "endDate": "2025-09-26T00:00:00Z", "submitByDate": "2025-10-01T00:00:00Z", "checkDate": "2025-10-03T00:00:00Z", "checkCount": 0, "id": "ac5526de-8515-4df8-907f-dc9e89bac315", "companyId": "70126790", "type": "payperiod", "_rid": "NmJkAKiCbEci2gEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEci2gEAAAAAAA==/", "_etag": "\"a0002165-0000-0100-0000-687007f70000\"", "_attachments": "attachments/", "_ts": 1752172535}, {"payPeriodId": "1090072003628745", "intervalCode": "WEEKLY", "status": "INITIAL", "description": "Weekly Payroll (1)", "startDate": "2025-09-27T00:00:00Z", "endDate": "2025-10-03T00:00:00Z", "submitByDate": "2025-10-08T00:00:00Z", "checkDate": "2025-10-10T00:00:00Z", "checkCount": 0, "id": "08321896-d4a2-415e-9ef5-adca1f7c85cd", "companyId": "70126790", "type": "payperiod", "_rid": "NmJkAKiCbEcj2gEAAAAAAA==", "_self": "dbs/NmJkAA==/colls/NmJkAKiCbEc=/docs/NmJkAKiCbEcj2gEAAAAAAA==/", "_etag": "\"a0002565-0000-0100-0000-687007f70000\"", "_attachments": "attachments/", "_ts": 1752172535}], "links": [{"rel": "self", "href": "https://ca-payroll-ai-mock-sb-001-secure.nicebeach-8a7a52ab.eastus.azurecontainerapps.io/ca/companies/70126790/payperiods"}]}, "status_code": 200}