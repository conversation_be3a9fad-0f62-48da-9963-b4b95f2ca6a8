#!/usr/bin/env python3
"""
Script to convert JSON payperiods results from Paychex API into table format.
Each row represents a payperiod, with CustomerID as the first column.
"""

import json
import pandas as pd
import os
from pathlib import Path
from typing import List, Dict, Any


def load_payperiods_json_results(results_dir: str = "payperiods_results") -> List[Dict[str, Any]]:
    """Load all JSON payperiods result files from the results directory."""
    results = []
    results_path = Path(results_dir)
    
    if not results_path.exists():
        print(f"Payperiods results directory '{results_dir}' not found")
        return results
    
    for json_file in results_path.glob("company_*_payperiods.json"):
        try:
            with open(json_file, 'r') as f:
                data = json.load(f)
                if data.get("success") and "data" in data:
                    results.append(data)
                    print(f"Loaded {json_file.name}")
        except Exception as e:
            print(f"Error loading {json_file.name}: {e}")
    
    return results


def flatten_payperiod_data(payperiod: Dict[str, Any], customer_id: str) -> Dict[str, Any]:
    """Flatten payperiod data into a single row for the table."""
    flattened = {"CustomerID": customer_id}
    
    # Basic payperiod info
    flattened.update({
        "PayPeriodId": payperiod.get("payPeriodId", ""),
        "PayPeriodNumber": payperiod.get("payPeriodNumber", ""),
        "PayFrequency": payperiod.get("payFrequency", ""),
        "StartDate": payperiod.get("startDate", ""),
        "EndDate": payperiod.get("endDate", ""),
        "CheckDate": payperiod.get("checkDate", ""),
        "Status": payperiod.get("status", ""),
        "ProcessingStatus": payperiod.get("processingStatus", ""),
        "PayrollType": payperiod.get("payrollType", ""),
        "Year": payperiod.get("year", ""),
        "CompanyId": payperiod.get("companyId", ""),
        "Type": payperiod.get("type", "")
    })
    
    # Add any additional fields that might exist
    for key, value in payperiod.items():
        if key not in flattened and not isinstance(value, (dict, list)):
            flattened[key] = value
    
    return flattened


def convert_payperiods_to_table(results: List[Dict[str, Any]]) -> pd.DataFrame:
    """Convert JSON payperiods results to a pandas DataFrame (table format)."""
    all_payperiods = []
    
    for result in results:
        customer_id = result.get("company_id", "")
        payperiods = result.get("data", {}).get("content", [])
        
        for payperiod in payperiods:
            flattened_payperiod = flatten_payperiod_data(payperiod, customer_id)
            all_payperiods.append(flattened_payperiod)
    
    df = pd.DataFrame(all_payperiods)
    return df


def save_payperiods_to_excel(df: pd.DataFrame, filename: str = "payperiods_table.xlsx"):
    """Save DataFrame to Excel file."""
    try:
        df.to_excel(filename, index=False)
        print(f"Payperiods table saved to {filename}")
        print(f"Total payperiods: {len(df)}")
        print(f"Total companies: {df['CustomerID'].nunique()}")
    except Exception as e:
        print(f"Error saving payperiods to Excel: {e}")


def save_payperiods_to_csv(df: pd.DataFrame, filename: str = "payperiods_table.csv"):
    """Save DataFrame to CSV file."""
    try:
        df.to_csv(filename, index=False)
        print(f"Payperiods table saved to {filename}")
        print(f"Total payperiods: {len(df)}")
        print(f"Total companies: {df['CustomerID'].nunique()}")
    except Exception as e:
        print(f"Error saving payperiods to CSV: {e}")


def main():
    print("Converting payperiods JSON results to table format...")
    
    # Load JSON results
    results = load_payperiods_json_results()
    
    if not results:
        print("No valid payperiods results found to process.")
        return
    
    print(f"Processing {len(results)} payperiods result files...")
    
    # Convert to table
    df = convert_payperiods_to_table(results)
    
    if df.empty:
        print("No payperiods data found to convert.")
        return
    
    # Save to both Excel and CSV
    save_payperiods_to_excel(df)
    save_payperiods_to_csv(df)
    
    print("\nColumn names in the payperiods table:")
    for i, col in enumerate(df.columns, 1):
        print(f"{i:2d}. {col}")


if __name__ == "__main__":
    main()